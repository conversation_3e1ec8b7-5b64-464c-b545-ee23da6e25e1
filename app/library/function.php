<?php

function list_to_tree($list, $pk = 'id', $pid = 'pid', $child = '_child', $root = 0)
{
    $tree = array();// 创建Tree
    if (is_array($list)) {
        // 创建基于主键的数组引用
        $refer = array();
        foreach ($list as $key => $data) {
            $refer[$data[$pk]] =& $list[$key];
        }

        foreach ($list as $key => $data) {
            // 判断是否存在parent
            $parentId = $data[$pid];
            if ($root == $parentId) {
                $tree[$data[$pk]] =& $list[$key];
            } else {
                if (isset($refer[$parentId])) {
                    $parent =& $refer[$parentId];
                    $parent[$child][] =& $list[$key];
                }
            }
        }
    }
    return $tree;
}

/**
 * 获取到零时区的时间，默认是泰国东七区转换为零时区
 * @param $date
 * @param string $format
 * @param string $time_zone_num
 * @param string $action
 * @return false|string
 */
function zero_time_zone($date, $format = 'Y-m-d H:i:s', $time_zone_num = '7', $action = '-')
{
    return date($format, strtotime($date. $action . $time_zone_num . ' hours'));
}

/**
 * 获取展示时间，默认是零时区转换为泰国时区
 * @param $date
 * @param string $format
 * @param string $time_zone_num
 * @param string $action
 * @return false|string
 */
if(!function_exists('show_time_zone')) {
    function show_time_zone($date, $format = 'Y-m-d H:i:s', $action = '+')
    {
        if (!$date) {
            return '';
        }
        $time_offset = env('add_hour', 7);
        return date($format, strtotime($date . $action . $time_offset . ' hours'));
    }
}

/**
 * @param $from
 * @param $to
 * @return string
 */
function format_duration($from, $to)
{
    if ($to > $from) {
        $period = $to - $from;
    } else {
        $period = $from - $to;
    }
    $hour = floor($period / 3600);
    $minute = floor(($period - $hour * 3600) / 60);
    $second = $period - $hour * 3600 - $minute * 60;
    $str = '';
    if ($hour > 0) {
        $str .= $hour . 'h ';
    }
    if ($minute > 0) {
        $str .= $minute . 'm ';
    }
    if ($second) {
        $str .= $second . 's ';
    }
    $str = empty($str) ? '1 m' : trim($str);
    return $str;
}

function registerStream()
{
    $existed = in_array("var", stream_get_wrappers());
    if ($existed) {
        stream_wrapper_unregister("var");
    }
    stream_wrapper_register("var", "App\Library\VarStream");
}

if(!function_exists('filter_param')) {
    function filter_param($array)
    {
        if (empty($array)) {
            return $array;
        }

        if (!is_array($array)) {
            return strip_tags(addcslashes(stripslashes($array), "'"));
        } else {
            foreach ($array as $key => $value) {
                if (!is_array($value)) {
                    $value = strip_tags(addcslashes(stripslashes($value), "'"));
                    $array[$key] = $value;
                } else {
                    filter_param($array[$key]);
                }
            }
        }

        return $array;
    }
}

// 清除多维数组元素两边的空白字符
if (!function_exists('trim_array')) {
    function trim_array ($input)
    {
        if (!is_array($input)) {
            return trim($input);
        }

        return array_map('trim_array', $input);
    }
}

// 多维数组按指定字段排序
if (!function_exists('array_sort')) {
    /**
     * 二维数组根据某个字段排序
     * @param array $array 要排序的数组
     * @param string $keys   要排序的键字段
     * @param mixed $sort  排序类型  SORT_ASC     SORT_DESC
     * @return array 排序后的数组
     */
    function array_sort(array $array, string $keys, $sort = SORT_DESC) {
        if (empty($array)) {
            return [];
        }

        $keys_value = [];
        foreach ($array as $k => $v) {
            $keys_value[$k] = $v[$keys];
        }

        array_multisort($keys_value, $sort, $array);
        return $array;
    }
}
/**
 * 打印数据
 */
if (!function_exists('dd')) {
    function dd(...$args)
    {
        if (function_exists('dump')) {
            dump(...$args);
        } else {
            var_dump(...$args);
        }
        die;
    }
}
/**
 * need molten.so
 * molten_get_traceid
 */
if(!function_exists('molten_get_traceid')) {
    function molten_get_traceid()
    {
        static $traceid = '';
        if (empty($traceid)) {
            $traceid = uniqid('oa_', true);
        }

        return $traceid;
    }
}

if(!function_exists('array_only')){
    function array_only($array,$keys){
        return array_intersect_key($array, array_flip((array) $keys));
    }
}

// 获取当前时间: 微秒
if (!function_exists('get_curr_micro_time')) {
    function get_curr_micro_time()
    {
        return microtime(true);
    }
}

// 计算代码片段执行时长
if (!function_exists('get_exec_time')) {
    function get_exec_time($start_micro_time)
    {
        return intval((microtime(true) - $start_micro_time) * 1000) . 'ms';
    }
}

// 获取系统国家码
if (!function_exists('get_country_code')) {
    function get_country_code()
    {
        static $country_code = '';
        if (empty($country_code)) {
            $country_code = strtoupper(env('country_code', 'TH'));
        }

        return $country_code;
    }
}


if(!function_exists('gmdate_customize_by_timestamp')){
    /**
     * 指定时间转换为业务系统所在时区的时间，按时间戳
     * @param $timestamp
     * @param string $format
     * @return false|string
     */
    function gmdate_customize_by_timestamp($timestamp, $format = 'Y-m-d H:i:s') {
        $time_offset = env('add_hour', 7);
        return gmdate($format, $timestamp + $time_offset * 3600);
    }
}

if(!function_exists('gmdate_customize_by_datetime')){
    /**
     * 指定时间转换为业务系统所在时区的时间，按日期时间
     * 仅支持当前时区转特定时区，如服务器+08:00,设置泰国编码可转为+07:00
     * @param $datetime
     * @param string $format
     * @return false|string
     */
    function gmdate_customize_by_datetime($datetime, $format = 'Y-m-d H:i:s') {
        $time_offset = env('add_hour', 7);
        return date($format, strtotime($datetime) + $time_offset * 3600);
    }
}

if(!function_exists('get_sys_time_offset')) {
    /**
     * 获取系统设置时区偏移量
     * @return int|mixed|string
     */
    function get_sys_time_offset()
    {
        return env('add_hour', 7);
    }
}

if (!function_exists('format_array')) {
    function format_array($array)
    {
        sort($array);
        $tem = "";
        $temarray = array();
        $j = 0;
        for ($i = 0; $i < count($array); $i++) {
            if ($array[$i] != $tem) {
                $temarray[$j] = $array[$i];
                $j++;
            }
            $tem = $array[$i];
        }
        return $temarray;
    }
}

if (!function_exists('curl_request')) {
    function curl_request($url, $data = null, $method = 'get', $header = array("content-type: application/json"), $https = true, $timeout = 10)
    {
        $method = strtoupper($method);
        $ch = curl_init();//初始化
        curl_setopt($ch, CURLOPT_URL, $url);//访问的URL
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);//只获取页面内容，但不输出
        curl_setopt($ch, CURLOPT_USERAGENT, 'Mozilla/5.0 (Windows NT 6.1) AppleWebKit/537.11 (KHTML, like Gecko) Chrome/23.0.1271.1 Safari/537.11');

        if ($https) {
            curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);//https请求 不验证证书
            curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);//https请求 不验证HOST
        }

        switch ($method) {
            case 'POST':
                curl_setopt($ch, CURLOPT_POST, true);//请求方式为post请求
                curl_setopt($ch, CURLOPT_POSTFIELDS, $data);//请求数据

                break;
            case 'PUT': case 'DELETE':
                curl_setopt($ch, CURLOPT_CUSTOMREQUEST, $method); //设置请求方式
                curl_setopt($ch, CURLOPT_POSTFIELDS, $data);//请求数据

                break;
        }

        curl_setopt($ch, CURLOPT_TIMEOUT, $timeout);
        curl_setopt($ch, CURLOPT_HTTPHEADER, $header); //模拟的header头
        //curl_setopt($ch, CURLOPT_HEADER, false);//设置不需要头信息
        $result = curl_exec($ch);//执行请求
        curl_close($ch);//关闭curl，释放资源
        return $result;
    }
}

// 获取系统环境
if (!function_exists('get_runtime_env')) {
    function get_runtime_env()
    {
        $env = 'pro';
        if (in_array(env('runtime'), ['dev','test'])) {
            $env = 'dev';
        } else if (in_array(env('runtime'), ['tra', 'training'])) {
            $env = 'training';
        }

        return $env;
    }
}

// 获取内存使用情况
if (!function_exists('memory_usage')) {
    function memory_usage() {
        return (!function_exists('memory_get_usage')) ? '0' : round(memory_get_usage()/1024/1024, 2).'MB';
    }
}

if (!function_exists('getTree')) {
    function getTree($array, $pid = 0, $level = 0,$fidname='parent_id',$type=false)
    {
        $list = [];
        if($type){
            $items = [];
            foreach($array as $value){
                $value['id'] = @(int)$value['id'];
                $value['parent_id'] = @(int)$value['parent_id'];
                $value['use_limit'] = @(int)$value['use_limit'];
                $value['status'] = @(int)$value['status'];
                $value['level'] = @(int)$value['level'];
                $value['type'] = @(int)$value['type'];
                $items[$value['id']] = $value;
            }
            //第二部 遍历数据 生成树状结构
            foreach($items as $key => $item){
                if(isset($items[$item['parent_id']])){
                    $items[$item['parent_id']]['son'][] = &$items[$key];
                }else{
                    $list[] = &$items[$key];
                }
            }
        }else{
        foreach ($array as $key => $value) {
            if (isset($value[$fidname]) && $value[$fidname] == $pid) {
                if($fidname=='attribute_id'){
                    unset($value[$fidname]);
                }else{
                 $value['level'] = $level;
                }
                $list[] = $value;
                unset($array[$key]);
                getTree($array, $value['id'], $level + 1);

            }
        }
        }
        return $list;
    }
}

if (!function_exists('arrayDepth')) {
    function arrayDepth($array)
    {
        $max_deep = 1;
        foreach ($array as $value) {
            if (isset($value['son']) && is_array($value['son'])) {
                $deep = arrayDepth($value['son']) + 1;
                if ($deep > $max_deep) {
                    $max_deep = $deep;
                }
            }
        }
        return $max_deep;
    }
}


if (!function_exists('arrayColumns')) {
    /*
     *  返回数组中指定多列
     * @param  Array  $input       需要取出数组列的多维数组
     * @param  String $column_keys 要取出的列名，逗号分隔，如不传则返回所有列
     * @param  String $index_key   作为返回数组的索引的列
     * @return Array
    */
    function arrayColumns($input, $column_keys = null, $index_key = null)
    {
        $result = array();
        $keys = isset($column_keys) ? explode(',', $column_keys) : array();
        if ($input) {
            foreach ($input as $k => $v) {

                // 指定返回列
                if (!$keys) {
                    $keys = array_keys($v);
                }
                $tmp = array();
                foreach ($keys as $key) {
                    $tmp[$key] = $v[$key];
                }
                // 指定索引列
                if (isset($index_key)) {
                    $result[$v[$index_key]] = $tmp;
                } else {
                    $result[] = $tmp;
                }

            }
        }
        return $result;
    }
}

if (!function_exists('arrayPicTool')) {
    /*
     *  返回数组中指定多列
     * @param  Array  $input       需要取出数组列的多维数组
     * @param  String $column_keys 要取出的列名，逗号分隔，如不传则返回所有列
     * @param  String $index_key   作为返回数组的索引的列
     * @return Array
    */
    function arrayPicTool($data)
    {
        $result = array();
       if(empty($data)){
           return [];
       }
       foreach ($data as $imt){
           $result[]=env('img_prefix','http://fle-staging-asset-internal.oss-ap-southeast-1.aliyuncs.com/').$imt['object_key'];
       }
        return $result;
    }
}

// 生成附件完整地址
if (!function_exists('gen_file_url')) {
    function gen_file_url($file_info) {
        return env('img_prefix', 'unsetting') . $file_info['object_key'];
    }
}

/**
 * 合并同一数据的多个附件
 * @param array $list
 * @param bool $is_attach_name
 * @param bool $is_have_http
 * @return mixed
 */
if (!function_exists('merge_attachments')) {
    function merge_attachments(array $list = [],$is_attach_name = true,$is_have_http = false) {
        $result = [];
        foreach ($list as $k => $v) {
            // 有可能数据结构没有该key，例如部分业务的附件数据存储在业务表的某字段中，并未放到附件表[sys_attachment]
            $v['oss_bucket_key'] = $v['oss_bucket_key'] ?? '';

            $_curr_attach = $result[$v['oss_bucket_key']] ?? '';
            $attach_name = $is_attach_name ? ($v['file_name'] . ': ') : '';
            if (!empty($_curr_attach)) {
                if ($is_have_http){
                    $_curr_attach .= " ; \r\n" . $attach_name . $v['object_key'];
                }else{
                    $_curr_attach .= " ; \r\n" . $attach_name . gen_file_url($v);
                }
            } else {
                if ($is_have_http){
                    $_curr_attach = $attach_name . $v['object_key'];
                }else{
                    $_curr_attach = $attach_name . gen_file_url($v);
                }
            }

            $result[$v['oss_bucket_key']] = $_curr_attach;
        }

        return $result;
    }
}

/**
 * 判断是否是闰年
 * @param year
 */
if(!function_exists('is_leap_year')){
    function is_leap_year($year)
    {
        return (($year % 4 == 0 && $year % 100 != 0) || ($year % 400 == 0));
    }
}

/**
 * 获取数据对象操作的错误信息
 * @param object $data_object
 * @return string $msg
 */
if(!function_exists('get_data_object_error_msg')){
    function get_data_object_error_msg($data_object)
    {
        $msg = '';

        if (is_object($data_object) && method_exists($data_object, 'getMessages')) {
            $messages = $data_object->getMessages();
            foreach ($messages as $message) {
                $msg .= $message->getMessage() . '; ';
            }
        }

        return $msg;
    }
}

/**
 * 提取一维数组中重复的元素
 * @param array $array
 * @return array
 */
if(!function_exists('array_repeat_element')){
    function array_repeat_element($array = [])
    {
        if (empty($array)) {
            return [];
        }

        $unique_array = array_unique($array);
        return array_unique(array_diff_assoc($array, $unique_array));
    }
}

/**
 * 高精度循环累加
 * @Date: 10/28/22 9:00 PM
 * @param array $numbers
 * @param int $scale
 * @return string
 * @author: peak pan
 **/
if (!function_exists('bc_add_batch')) {
    function bc_add_batch(array $numbers, int $scale = 2)
    {
        if ($numbers && count($numbers) >= 2) {
            $return = '0';
            foreach ($numbers as $item) {
                $return = bcadd($item, $return, $scale);
            }
            return $return;
        }
        return bcadd('0', $numbers[0] ?? '0', $scale);
    }
}

/**
 * 获取某日期所属季度
 * @param string $date Y-m-d
 * @return int
 */
if (!function_exists('get_season_by_date')) {
    function get_season_by_date($date)
    {
        if (empty($date)) {
            return 1;
        }

        $month = (int)mb_substr($date, 5, 2);
        return (int)ceil($month / 3);
    }
}

/**
 * 发送飞书文本消息
 * @param string $msg_content 消息内容
 * @param string $msg_api_key 消息key
 * @return mixed
 */
if (!function_exists('send_feishu_text_msg')) {
    function send_feishu_text_msg($msg_content, $msg_api_key = '')
    {
        if (empty($msg_api_key)) {
            return '';
        }

        // 文本消息
        $api_params = [
            'msg_type' => 'text',
            'content' => [
                'text' => $msg_content
            ]
        ];

        $curl = curl_init();
        curl_setopt_array($curl, array(
            CURLOPT_URL => 'https://open.feishu.cn/open-apis/bot/v2/hook/' . $msg_api_key,
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_ENCODING => '',
            CURLOPT_MAXREDIRS => 10,
            CURLOPT_TIMEOUT => 5,
            CURLOPT_FOLLOWLOCATION => true,
            CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
            CURLOPT_CUSTOMREQUEST => 'POST',
            CURLOPT_POSTFIELDS => json_encode($api_params),
            CURLOPT_HTTPHEADER => array(
                'Accept-Language: zh-CN',
                'Content-Type: application/json'
            ),
        ));

        $response = curl_exec($curl);
        curl_close($curl);

        return $response;
    }
}

/**
 * 获得名字和昵称拼接的字符串
 * 逐步替代BaseService->getNameAndNickName
 * @param $name
 * @param $nick_name
 * @return string
 */
if (!function_exists('get_name_and_nick_name')) {
    function get_name_and_nick_name($name, $nick_name = '')
    {
        $str = $name;
        if (!empty($nick_name)) {
            $str .= '(' . $nick_name . ')';
        }

        return $str;
    }
}

/**
 * 异步导入任务-阿里云地址解析
 */
if (!function_exists('get_oss_info_by_url')) {
    function get_oss_info_by_url($url)
    {
        $oss_info = [
            'bucket_name' => '',
            'object_key' => '',
            'file_name' => '',
        ];
        //取bucket_name 把开头的http://去掉
        $have_http = stripos($url, 'http://');
        $have_https = stripos($url, 'https://');
        $first_point = stripos($url, '.');
        if ($have_http !== false) {
            $start_index = $have_http + mb_strlen('http://');
            $end_index = $first_point - $have_http;
            $oss_info['bucket_name'] = mb_substr($url, $start_index, $end_index);
        } elseif ($have_https !== false) {
            $start_index = $have_https + mb_strlen('https://');
            $end_index = $first_point - $start_index;
            $oss_info['bucket_name'] = mb_substr($url, $start_index, $end_index);
        } else {
            $oss_info['bucket_name'] = mb_substr($url, 0, $first_point);
        }
        //取object_key, 取第一个.com/后边的所有字符
        $bucket_name_index = stripos($url, '.com/');
        $oss_info['object_key'] = mb_substr($url, $bucket_name_index + mb_strlen('.com/'));
        //拼接file_name, 使用md5后的object_key, 再拼上原来的后缀
        $file_name_str = md5($oss_info['object_key']);
        $oss_info['file_name'] = 'import_' . $file_name_str;
        $extension = strchr($oss_info['object_key'], '.');
        $oss_info['file_name'] = $oss_info['file_name'] . $extension;
        return $oss_info;
    }
}

/**
 * 过滤指定的空参数
 * @param array $params  参数
 * @param array $not_must 要过滤的参数名,一维数组
 * @return array $params  过滤后的参数
 */
if (!function_exists('params_filter')) {
    function params_filter($params, $not_must)
    {
        if (empty($not_must) || !is_array($not_must)) {
            return $params;
        }
        foreach ($not_must as $value) {
            if (strstr($value, '[*].')) {
                //过滤二维的
                $key_value = explode('[*].', $value);
                if (isset($params[$key_value[0]])) {
                    foreach ($params[$key_value[0]] as $sub => $sub_v) {
                        if (isset($sub_v[$key_value[1]]) && empty($sub_v[$key_value[1]])) {
                            unset($params[$key_value[0]][$sub][$key_value[1]]);
                        }
                    }
                }
            } else {
                //过滤一维的
                if (isset($params[$value]) && empty($params[$value])) {
                    unset($params[$value]);
                }
            }

        }
        return $params;
    }
}
/**
 * 读取oss中的excel文件
 * @param $file_url
 * @param $set_type
 * @date 2023/5/30
 * @return array $excel_data excel数据
 */
if (!function_exists('get_oss_file_excel_data')) {
    function get_oss_file_excel_data($file_url, $set_type)
    {
        $oss_info = get_oss_info_by_url($file_url);
        $file_name = $oss_info['file_name'];
        ob_start();
        readfile($file_url);
        $file_data = ob_get_contents();
        ob_end_clean();

        $tmp_dir = sys_get_temp_dir() . '/';
        $tmp_path = $tmp_dir . $file_name;
        file_put_contents($tmp_path, $file_data);

        // 2. 读取文件
        $config = ['path' => $tmp_dir];
        $excel = new \Vtiful\Kernel\Excel($config);

        // 读取上传文件数据
        $excel_data = $excel->openFile($file_name)
            ->openSheet()
            ->setType($set_type)
            ->getSheetData();
        //删除临时文件
        unlink($tmp_path);
        return $excel_data;
    }
}

/**
 * 把字符串写入到临时文件
 * @param $file_content
 * @param $file_name
 * @date 2023/7/14
 * @return array
 */
if (!function_exists('put_data_in_file')) {
    function put_data_in_file($file_content, $file_name)
    {
        $tmp_dir  = sys_get_temp_dir() . '/';
        $tmp_path = $tmp_dir . $file_name;
        file_put_contents($tmp_path, $file_content);
        return $tmp_path;
    }
}

/**
 * 获取随机流水号
 * @param int $length 随机字符长度
 * @return string
 */
if (!function_exists('get_generate_random_string')) {
    function get_generate_random_string($length = 10)
    {
        $characters = str_shuffle('0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ');
        $random_string = '';
        for ($i = 0; $i < $length; $i++) {
            $random_string .= $characters[rand(0, strlen($characters) - 1)];
        }
        return strtoupper($random_string);
    }
}

/**
 * 获取指定位数的随机数字
 * @param int $length 指定长度
 * @return string
 */
if (!function_exists('get_random_number')) {
    function get_random_number($length = 6)
    {
        $random_number = '';
        for ($i = 0; $i < $length; $i++) {
            $random_number .= mt_rand(0, 9);
        }
        return $random_number;
    }
}



/**
 * 对一个二维数组里面的多个字段同时排序
 * sort_array_by_fields($items, 'apply_no', SORT_DESC, 'barcode', SORT_ASC,'wms_no',SORT_ASC)
 * 第一个是数据,第二个是排序的字段 ,第三个是 排序规则 ....
 * @return array
 */
if (!function_exists('sort_array_by_fields')) {
    function sort_array_by_fields()
    {
        $args = func_get_args(); // 获取函数的参数的数组
        if (empty($args)) {
            return [];
        }
        $arr = array_shift($args);
        if (!is_array($arr)) {
            return [];
        }

        foreach ($args as $key => $field) {
            if (is_string($field)) {
                $temp = array();
                foreach ($arr as $index => $val) {
                    $temp[$index] = $val[$field];
                }
                $args[$key] = $temp;
            }
        }
        $args[] = &$arr;//引用值
        call_user_func_array('array_multisort', $args);
        return array_pop($args);

    }
}

/**
 * 小驼峰转下划线
 * userInfo=>user_info
 */
if (!function_exists('convert_camel_to_underscore')) {
    function convert_camel_to_underscore($str): string
    {
     return strtolower(preg_replace('/([a-z])([A-Z])/', '$1_$2', $str));
    }
}


/**
 * 多语言字段名拼接
 *
 * @param string $field_prefix 字段前缀
 * @param string $sys_lang 系统语言
 * @param string $default_lang 默认语言
 * @param string $zh_field_suffix 中文字段后缀
 *
 * @return string 字段名
 *
 */
if (!function_exists('get_lang_field_name')) {
    function get_lang_field_name($field_prefix, $sys_lang, $default_lang = 'local', $zh_field_suffix = 'zh')
    {
        // 配中英
        $lang_arr = [
            'en' => 'en',
            'zh-cn' => $zh_field_suffix,
            'zh' => $zh_field_suffix
        ];

        // 非中英, 走默认(一般是当地语言)
        $field_lang = $lang_arr[strtolower($sys_lang)] ?? $default_lang;
        return $field_prefix . $field_lang;
    }
}


/**
 * 找某部门链下的子部门
 *
 * @param string $curr_ancestry_v3 某部门当前的部门链
 * @param array $all_department_list 所有部门列表 id => v3
 * @return array
 */
if (!function_exists('get_sub_department_ids')) {
    function get_sub_department_ids(string $curr_ancestry_v3, array $all_department_list)
    {
        if (empty($curr_ancestry_v3) || empty($all_department_list)) {
            return [];
        }

        $sub_dept_ids = [];
        $curr_ancestry_v3 .= '/';
        foreach ($all_department_list as $id => $v3) {
            if (stripos($v3, $curr_ancestry_v3) !== false) {
                $sub_dept_ids[] = $id;
            }
        }

        return $sub_dept_ids;
    }
}

/**
 * 是否是日期格式: YYYY-MM-DD
 *
 * @param string $date 日期
 * @return bool
 */
if(!function_exists('is_date_format')) {
    function is_date_format(string $date)
    {
        return preg_match('/^[1-9]{1}\d{3}-\d{2}-\d{2}$/', $date) ? true : false;
    }
}

/**
 * 获取客户端真实IP
 */
if (!function_exists('get_client_real_ip')) {
    function get_client_real_ip()
    {
        if (isset($_SERVER)) {
            if (isset($_SERVER['HTTP_X_FORWARDED_FOR'])) {
                $arr = explode(',', $_SERVER['HTTP_X_FORWARDED_FOR']);
                $real_ip = 'unknown';
                foreach ($arr as $ip) {
                    $ip = trim($ip);
                    if ($ip != 'unknown') {
                        $real_ip = $ip;
                        break;
                    }
                }
            } else {
                if (isset($_SERVER['HTTP_CLIENT_IP'])) {
                    $real_ip = $_SERVER['HTTP_CLIENT_IP'];
                } else {
                    if (isset($_SERVER['REMOTE_ADDR'])) {
                        $real_ip = $_SERVER['REMOTE_ADDR'];
                    } else {
                        $real_ip = '0.0.0.0';
                    }
                }
            }
        } else {
            if (getenv('HTTP_X_FORWARDED_FOR')) {
                $real_ip = getenv('HTTP_X_FORWARDED_FOR');
            } else {
                if (getenv('HTTP_CLIENT_IP')) {
                    $real_ip = getenv('HTTP_CLIENT_IP');
                } else {
                    $real_ip = getenv('REMOTE_ADDR');
                }
            }
        }

        preg_match('/[\\d\\.]{7,15}/', $real_ip, $online_ip);
        $real_ip = (!empty($online_ip[0]) ? $online_ip[0] : '0.0.0.0');
        return $real_ip;
    }
}


//获取环境变量 小写
if (!function_exists('get_runtime')) {
    function get_runtime() {
        return strtolower(RUNTIME);
    }
}


/**
 * 参数加签
 *
 * @param array $params
 * @param string $api_key
 * @param bool $is_build_query true-返回参数拼接后的请求串; false-返回带sign参数的数组
 * @return mixed
 */
if (!function_exists('build_params_sign')) {
    function build_params_sign(array $params, string $api_key = '', bool $is_build_query = false)
    {
        $sign = '';
        ksort($params);
        foreach ($params as $k => $v) {
            if (($v != null) && ($k != 'sign')) {
                $sign .= $k . '=' . $v . '&';
            }
        }
        $sign .= 'key=' . $api_key;

        $params['sign'] = strtoupper(hash('sha256', $sign));

        if ($is_build_query) {
            $request_str = '';
            foreach ($params as $k => $v) {
                $request_str .= $k . '=' . urlencode($v) . '&';
            }

            return substr($request_str, 0, -1);
        } else {
            return $params;
        }
    }
}

/**
 * 获取图片base64数据
 *
 * @param string $image_path 图片文件路径(本地或远程)
 * @return mixed
 */
if (!function_exists('get_image_base64')) {
    function get_image_base64(string $image_path = '')
    {
        if (empty($image_path)) {
            return '';
        }

        $image_content = file_get_contents($image_path);
        if (empty($image_content)) {
            return '';
        }

        $base64 = base64_encode($image_content);
        $image_info = getimagesize($image_path);
        return "data:{$image_info['mime']};base64,{$base64}";
    }
}

/**
 * 生成uuid
 * @return string
 */
if (!function_exists('generate_uuid')) {
    function generate_uuid($prefix = '')
    {
        return md5(uniqid($prefix . '_' . mt_rand(), true));
    }
}

/**
 * 格式化Excel中的时分秒 time 列
 */
if (!function_exists('format_excel_time')) {
    function format_excel_time(string $time)
    {
        if (empty($time)) {
            return '';
        }

        if (stripos($time, ':') !== false) {
            $time_section_count = substr_count($time, ':');
            switch ($time_section_count) {
                case 1:
                    $time = $time . ':00';
                    break;
                case 2:
                    $time = $time;
                    break;
            }

        } else {
            $time = gmdate('H:i:s', $time * 86400);
        }

        return $time;
    }
}


/**
 * 替换html标签
 * @return string
 */
if (!function_exists('replace_html_tags')) {
    function replace_html_tags($html)
    {
        $html = str_replace('&nbsp;', ' ', $html);
        //其他的写在下面

        return strip_tags($html,"");
    }
}

/**
 * 计算每段日期范围的月数
 *
 * 1. 实际月数 = (首月数 + 中间月数 + 尾月数)四舍五入保留2位小数
 * 2. 中间月数: (结束年 - 开始年) * 12 + (结束月 - 开始月)  - 1
 * 3. 首月: ((首月月末日期 - 开始日期) + 1) / 首月天数 -中间过程不处理小数
 * 4. 尾月: ((结束日期 - 尾月月初日期) + 1)）/ 尾月天数-中间过程不处理小数
 *
 * @return mixed
 */
if (!function_exists('cal_total_months')) {
    function cal_total_months(string $start_date, string $end_date)
    {
        $start_date_item = explode('-', $start_date);
        $end_date_item = explode('-', $end_date);

        $start_date_year = $start_date_item[0];
        $start_date_month = (int)$start_date_item[1];
        $start_date_day = (int)$start_date_item[2];

        $end_date_year = $end_date_item[0];
        $end_date_month = (int)$end_date_item[1];
        $end_date_day = (int)$end_date_item[2];

        // 首月月数
        $days_in_start_month = date('t', strtotime($start_date));
        $start_months = (($days_in_start_month - $start_date_day) + 1) / $days_in_start_month;

        // 尾月月数
        $days_in_end_month = date('t', strtotime($end_date));
        $end_months = (($end_date_day - 1) + 1) / $days_in_end_month;

        // 中间月数
        $middle_months = ($end_date_year - $start_date_year) * 12 + ($end_date_month - $start_date_month) - 1;

        // 实际月数
        return round($start_months + $middle_months + $end_months, 2);
    }
}

/**
 * 判断是否是有效的URL
 * @return mixed
 */
if (!function_exists('is_valid_url')) {
    function is_valid_url($url) {
        return filter_var($url, FILTER_VALIDATE_URL) !== false;
    }
}

/**
 * 获取格式化的微妙时间
 *
 * @return mixed
 */
if (!function_exists('get_datetime_with_milliseconds')) {
    function get_datetime_with_milliseconds()
    {
        $microtime = microtime(true); // 获取当前时间戳的微秒数
        $milliseconds = round($microtime * 1000); // 将微秒数转换为毫秒数
        return date('Y-m-d H:i:s') . '.' . substr($milliseconds, -3); // 将毫秒数拼接在日期时间后面
    }
}

/**
 * 两个数组是否相等(一维)
 *
 * @return mixed
 */
if (!function_exists('arrays_is_equal')) {
    function arrays_is_equal($arr1, $arr2) {
        return (count(array_diff($arr1, $arr2)) == 0 && count(array_diff($arr2, $arr1)) == 0);
    }
}

/**
 * 计算两个经纬度之间的距离
 * @param float $lat1 第一个纬度
 * @param float $lng1 第一个经度
 * @param float $lat2 第二个纬度
 * @param float $lng2 第二个经度
 * @return float 距离，单位：公里
 */
if (!function_exists('get_distance')) {
    function get_distance($lat1, $lng1, $lat2, $lng2)
    {
        // 地球半径，单位：公里
        $earthRadius = 6371;

        // 将经纬度转换为弧度
        $lat1 = deg2rad($lat1);
        $lng1 = deg2rad($lng1);
        $lat2 = deg2rad($lat2);
        $lng2 = deg2rad($lng2);

        // 计算差值
        $latDiff = $lat2 - $lat1;
        $lngDiff = $lng2 - $lng1;

        // Haversine公式
        $distance = 2 * $earthRadius * asin(sqrt(
                sin($latDiff / 2) * sin($latDiff / 2) +
                cos($lat1) * cos($lat2) * sin($lngDiff / 2) * sin($lngDiff / 2)
            ));

        // 返回距离，保留2位小数
        return round($distance, 2);
    }
}

if (!function_exists('keep_zero_and_non_empty')) {
    function keep_zero_and_non_empty($value): bool
    {
        return !is_null($value)        // 排除 NULL
            && $value !== false         // 排除 FALSE
            && (is_string($value) ? $value !== '' : true)  // 排除空字符串
            && (is_array($value) ? count($value) > 0 : true); // 排除空数组
    }
}

/**
 * 判断当前系统国家代码是否匹配指定国家
 * @description: 检查当前环境的国家代码是否与指定的国家代码匹配，支持单个国家或多个国家的匹配
 * @author: AI
 * @date: 2025-07-15
 * @param string|array $country 要匹配的国家代码，可以是字符串或数组
 * @return bool 匹配返回true，不匹配返回false
 * @example: isCountry('TH') => true/false
 * @example: isCountry(['TH', 'CN', 'US']) => true/false
 */
if (!function_exists('isCountry')) {
    function isCountry($country = 'TH'): bool
    {
        // 获取当前环境的国家代码，默认为TH
        $currentCountryCode = strtolower(env('country_code', 'TH'));
        
        // 参数验证：确保参数不为空
        if (empty($country)) {
            return false;
        }
        
        // 如果传入的是数组，检查当前国家代码是否在数组中
        if (is_array($country)) {
            // 将数组中的所有国家代码转换为小写进行比较
            $lowercaseCountries = array_map('strtolower', $country);
            return in_array($currentCountryCode, $lowercaseCountries);
        }
        
        // 如果传入的是字符串，直接比较（不区分大小写）
        return $currentCountryCode === strtolower($country);
    }
}

/**
 * @description:把$class 替换为当前国家下$class 没有的话 返回当前 $class
 * @param null
 * @return     :
 * <AUTHOR> L.J
 * @time       : 2022/6/30 14:48
 */
if (!function_exists('reBuildCountryInstance')) {

    function reBuildCountryInstance($class, $args = [])
    {
        if (is_object($class)) {
            $reflect = new \ReflectionClass($class);

            if (
                strpos($reflect->getName(), ucfirst(strtolower(env('country_code', 'TH')))) === false
            ) {
                $newClass = str_replace("\\Services",
                        "\\Services\\" . ucfirst(strtolower(env('country_code', 'TH'))) . "\\",
                        $reflect->getNamespaceName())  . $reflect->getShortName();

                if (class_exists($newClass)) {
                    $newReFlection = new \ReflectionClass($newClass);
                    if ($newReFlection->inNamespace() && $newReFlection->isInstantiable()) {
                        return $newReFlection->newInstanceArgs($args);
                    }
                }
            }

            return $class;
        } else {
            throw new \Exception('不是一个实例化对象');
        }
    }
}

/**
 * 数组根据指定字段分组
 */
if (!function_exists('array_group_by_column')) {
    function array_group_by_column($array, $column = '')
    {
        if (empty($column)) {
            return [];
        }
        $grouped = [];
        foreach ($array as $item) {
            $category = $item[$column] ?? null;

            if (empty($category)) {
                continue;
            }

            // 如果分类不存在，初始化空数组
            if (!isset($grouped[$category])) {
                $grouped[$category] = [];
            }
            // 将当前元素添加到对应分类
            $grouped[$category][] = $item;
        }
        return $grouped;
    }
}

/**
 * 获取数组中指定字段指定值的元素列表
 *
 */
if (!function_exists('filter_array_by_field_value')) {
    function filter_array_by_field_value($array, $field, $value) {
        return array_filter($array, function($item) use ($field, $value) {
            return isset($item[$field]) && $item[$field] == $value;
        });
    }
}

/**
 * 验证是佛是指定格式的日期
 */
if (!function_exists('validate_date')) {
    function validate_date($date, $delimiter = '/')
    {
        // 正则匹配基础格式
        $model = '/^\d{4}\/\d{2}\/\d{2}$/';
        if ($delimiter == '-') {
            $model = '/^\d{4}-\d{2}-\d{2}$/';
        }

        if (!preg_match($model, $date)) {
            return false;
        }

        // 拆分年月日
        [$year, $month, $day] = explode($delimiter, $date);

        // 校验数值范围
        if ($year < 1000 || $year > 9999 || $month < 1 || $month > 12) {
            return false;
        }

        // 每月天数校验（包含闰年2月）
        $days_in_month = [
            1  => 31,
            2  => ($year % 4 == 0 && ($year % 100 != 0 || $year % 400 == 0)) ? 29 : 28,
            3  => 31,
            4  => 30,
            5  => 31,
            6  => 30,
            7  => 31,
            8  => 31,
            9  => 30,
            10 => 31,
            11 => 30,
            12 => 31,
        ];

        return $day >= 1 && $day <= $days_in_month[(int)$month];
    }
}


/**
 * 智能类型转换函数(数据转换与默认值处理)
 * @param mixed $input 输入值
 * @param string $type 目标类型(int/float/string/bool/array)
 * @param mixed $default 默认值
 * @return mixed 转换后的值
 */

if (!function_exists('convert_type')) {
    function convert_type($input, $type = 'int', $default = '')
    {
        if (empty($input)) {
            return $default;
        }

        switch (strtolower($type)) {
            case 'int':
                return (int)$input;
            case 'string':
                return (string)$input;
            case 'float':
                return (float)$input;
            case 'bool':
                return filter_var($input, FILTER_VALIDATE_BOOLEAN);
            case 'array':
                return is_array($input) ? $input : [$input];
            default:
                return $input;
        }
    }
}

/**
 * 解析合同编号，提取分隔符后的最后一部分
 * @description: 合同编号可能包含分隔符-，若有则返回最后一个分隔符后的内容，若最后一部分为空则返回原始编号
 * @param string $contract_number 合同编号字符串
 * @return string 解析后的结果
 * @author: AI
 * @date: 2025-07-22 17:30:00
 * @example parse_contract_number("HT-2023-001") => "001"; parse_contract_number("HT2023001") => "HT2023001"; parse_contract_number("HT-2023-") => "HT-2023-"
 */
if (!function_exists('parse_contract_number')) {
    function parse_contract_number($contract_number)
    {
        // 检查是否包含分隔符
        if (strpos($contract_number, '-') !== false) {
            // 分割字符串
            $parts = explode('-', $contract_number);
            // 获取最后一个元素并去除空白
            $last_part = end($parts);
            $last_part = trim($last_part);

            // 如果最后一部分不为空则返回，否则返回原始编号
            $result = $last_part !== '' ? $last_part : $contract_number;
        } else {
            $result = $contract_number;
        }

        // 不包含分隔符，直接返回原始编号
        return trim($result);
    }
}

if (!function_exists('is_valid_integer_range')) {
    /**
     * 验证变量是否为指定范围内的整数
     * @description: 检查变量是否为整数或字符串整数，并验证其是否在指定的min和max范围内（包含边界值）
     * @param mixed $value 需要验证的变量
     * @param int $min 最小值（包含），默认为0
     * @param int $max 最大值（包含），默认为999999999
     * @return bool 验证通过返回true，否则返回false
     * @author: AI
     * @date: 2025-07-21 19:00:00
     * @example: is_valid_integer_range(12345) => true; is_valid_integer_range("123", 0, 500) => true; is_valid_integer_range(1000000000) => false; is_valid_integer_range("123.45") => false
     */
    function is_valid_integer_range($value, int $min = 0, int $max = 999999999)
    {
        // 使用filter_var验证是否为整数，兼容数值型和字符串型整数
        $intValue = filter_var($value, FILTER_VALIDATE_INT);
        if ($intValue === false) {
            return false;
        }
        // 检查范围
        if ($intValue < $min || $intValue > $max) {
            return false;
        }
        return true;
    }
}

/**
 * 解析合同编号，提取分隔符后的最后一部分
 * @description: 合同编号可能包含分隔符-，若有则返回最后一个分隔符后的内容，若最后一部分为空则返回原始编号
 * @param string $contract_number 合同编号字符串
 * @return string 解析后的结果
 * @author: AI
 * @date: 2025-07-22 17:30:00
 * @example parse_contract_number("HT-2023-001") => "001"; parse_contract_number("HT2023001") => "HT2023001"; parse_contract_number("HT-2023-") => "HT-2023-"
 */
if (!function_exists('parse_contract_number')) {
    function parse_contract_number($contract_number)
    {
        // 检查是否包含分隔符
        if (strpos($contract_number, '-') !== false) {
            // 分割字符串
            $parts = explode('-', $contract_number);
            // 获取最后一个元素并去除空白
            $last_part = end($parts);
            $last_part = trim($last_part);

            // 如果最后一部分不为空则返回，否则返回原始编号
            $result = $last_part !== '' ? $last_part : $contract_number;
        } else {
            $result = $contract_number;
        }

        // 不包含分隔符，直接返回原始编号
        return trim($result);
    }
}

/**
 * 重置OPcache缓存
 * @description: 检查OPcache状态并提供重置功能（完全重置或文件特定失效）
 * @param string|null $filepath 可选文件路径，指定要失效的单个文件
 * @return bool 返回操作是否成功执行
 * @author: AI
 * @date: 2025-07-25
 */
if (!function_exists('safe_reset_opcache')) {
    function safe_reset_opcache($filepath = null) {
        $result = ['success' => false, 'message' => ''];

        // 检查OPcache扩展是否加载
        if (!extension_loaded('Zend OPcache')) {
            $result['message'] = 'OPcache扩展未加载, 无需重置';
            return $result;
        }

        if ($filepath) {
            // 指定文件重置
            if (function_exists('opcache_invalidate')) {
                opcache_invalidate($filepath, true);
                $result = [
                    'success' => true,
                    'message' => '已重置文件缓存: ' . realpath($filepath)
                ];
            } else {
                $result['message'] = 'opcache_invalidate()函数不可用, 无需重置';
            }
        } else {
            // 整体重置
            if (function_exists('opcache_reset')) {
                opcache_reset();
                $result = [
                    'success' => true,
                    'message' => '已重置全部OPcache缓存'
                ];
            } else {
                $result['message'] = 'opcache_reset()函数不可用, 无需重置';
            }
        }

        return $result;
    }
}
