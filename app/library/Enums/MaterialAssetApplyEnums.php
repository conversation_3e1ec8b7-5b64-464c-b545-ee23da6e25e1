<?php
namespace App\Library\Enums;
final class MaterialAssetApplyEnums
{
    //资产申请单导出时间锁
    const MATERIAL_ASSET_APPLY_EXPORT_LOCK='material_asset_apply_export_lock';
    const MATERIAL_ASSET_APPLY_EXPORT_LIMIT = 30000;

    //资产申请单编号前缀
    const MATERIAL_ASSET_APPLY_NO_PREFIX = 'ZC';

    //附件最多上传10个
    const OSS_MATERIAL_MAX = 10;

    //资产申请附件
    const OSS_MATERIAL_TYPE_ASSET_APPLY = 4;

    //申请单列表
    const LIST_TYPE_APPLY = 1;
    //审批列表
    const LIST_TYPE_APPLY_AUDIT = 2;
    //数据查询列表
    const LIST_TYPE_APPLY_DATA = 3;
    //领用出库-关联申请单
    const LIST_TYPE_OUT_STORAGE_LIST = 4;

    //是否全部关联完成，1否
    const IS_ALL_RELATED_NO = 1;

    //是否全部关联完成，2是
    const IS_ALL_RELATED_YES = 2;

    //数据来源，0:oa申请、1:by申请
    const SOURCE_TYPE_OA = 0;
    const SOURCE_TYPE_BY = 1;
    const SOURCE_TYPE_HCM = 2;//请求来源HCM

    const MATERIAL_ASSET_UPLOAD_FILE_SUFFIX = 'xlsx';

    const MATERIAL_ASSET_UPLOAD_SCRAP_MAX_DATA_NUMBER = 50000;  //上传作废的最大条数

    const MATERIAL_ASSET_UPLOAD_FINANCE_MAX_DATA_NUMBER = 100000;//上传财务修改的最大条数

    const MATERIAL_ASSET_OUT_STORAGE_IMPORT_ADD_MAX = 5000;//资产领用出库-导入新增最大条数

    const MATERIAL_ASSET_APPLY_AUDIT_EXPORT = 50000;//资产领用申请-导出审核最大条数
    const MATERIAL_ASSET_APPLY_AUDIT_MAX = 10000;//资产领用申请-批量审核条数

    //是否锁定,0 否 1是
    const MATERIAL_ASSET_IS_BATCH_UN_LOCK = 0;   //批量上传审核解锁
    const MATERIAL_ASSET_IS_BATCH_LOCK = 1;   //批量上传审核锁

    const MATERIAL_ASSET_AUDIT_APPROVAL_RESULT_AGREE  = 'Agree'; //审核结果同意
    const MATERIAL_ASSET_AUDIT_APPROVAL_RESULT_REJECT  =  'Reject';//审核结果驳回
    const MATERIAL_ASSET_AUDIT_NUM_RULE = '/^([1-9]\d{0,3}|[0])$/';

    //lnt 公司配置的 可申请的barcode
    const MATERIAL_ASSET_LNT_BARCODE = 'lnt_bar_code';

    //lnt 公司配置的指定职位受lnt公司可申请的barcode限制
    const MATERIAL_ASSET_LNT_JOB_IDS = 'lnt_job_ids';

    //个人代理可申请资产相关开关配置
    const MATERIAL_ASSET_PERSONAL_AGENT_STATUS_KEY = 'material_asset_apply_personal_agent_status';//是否开启雇佣类型只能申请固定barcode
    const MATERIAL_ASSET_PERSONAL_AGENT_BARCODE_KEY = 'material_asset_apply_personal_agent_barcodes';//只能申请固定barcode
    const MATERIAL_ASSET_PERSONAL_AGENT_COMPANY_KEY = 'material_asset_apply_personal_agent_company_id';//所属公司默认配置的公司
    const MATERIAL_ASSET_PERSONAL_AGENT_TRANSFER_STAFF_KEY = 'material_asset_personal_agent_transfer_staff';//个人代理可转移的接收人工号组

}
