<?php
namespace App\Library\Enums;


final class SysConfigEnums
{
    //系统模块key
    const SYS_MODULE_CONTRACT = 'sys_module_contract'; //合同管理
    const SYS_MODULE_OTHER_CONTRACT = 'sys_module_other_contract'; // 其他合同
    const SYS_MODULE_STORE_RENT_CONTRACT = 'sys_module_store_rent_contract'; // 网点租房合同
    const SYS_MODULE_ELECTRONIC_CONTRACT = 'sys_module_electronic_contract';//电子合同
    const SYS_MODULE_VENDOR = 'sys_module_vendor'; //供应商管理
    const SYS_MODULE_PERMISSION = 'sys_module_permission'; //权限管理
    const SYS_MODULE_MATERIAL = 'sys_module_material'; //物料/资产管理
    const SYS_MODULE_LOAN = 'sys_module_loan'; //借款管理
    const SYS_MODULE_SHARE = 'sys_module_share'; //信息共享中心
    const SYS_MODULE_TICKET = 'sys_module_ticket'; //工单
    const SYS_MODULE_PURCHASE = 'sys_module_purchase'; //采购管理
    const SYS_MODULE_PURCHASE_STORAGE_PAYABLE = 'sys_module_purchase_storage_payable'; //采购管理 -应付
    const SYS_MODULE_PURCHASE_STORAGE_ORDER = 'sys_module_purchase_storage_order'; //采购管理 -订单
    const SYS_MODULE_PURCHASE_PAYMENT = 'sys_module_purchase_payment'; //采购管理-采购付款申请单
    const SYS_MODULE_PURCHASE_APPLY = 'sys_module_purchase_apply'; //采购管理-采购申请单
    const SYS_MODULE_PURCHASE_ORDER = 'sys_module_purchase_order'; //采购管理-采购订单
    const SYS_MODULE_PURCHASE_STORAGE = 'sys_module_purchase_storage'; //采购管理-入库通知单
    const SYS_MODULE_PURCHASE_ACCEPTANCE = 'sys_module_purchase_acceptance'; //采购管理-验收单
    const SYS_MODULE_PURCHASE_SAMPLE = 'sys_module_purchase_sample'; //采购管理-样品确认
    const SYS_MODULE_REIMBURSEMENT = 'sys_module_reimbursement'; //报销管理
    const SYS_MODULE_ORGANIZATION = 'sys_module_organization'; //组织架构
    const SYS_MODULE_SALARY = 'sys_module_salary'; //薪酬管理
    const SYS_MODULE_HC = 'sys_module_hc'; //HC预算管理
    const SYS_MODULE_PAYMENT = 'sys_module_payment'; //租房付款管理
    const SYS_MODULE_BUDGET = 'sys_module_budget'; //财务预算管理
    const SYS_MODULE_ORDINARY_PAYMENT = 'sys_module_ordinary_payment'; //普通付款
    const SYS_MODULE_WAGE = 'sys_module_wage'; //薪资发放审批
    const SYS_MODULE_TRANSFER = 'sys_module_transfer'; //转岗管理
    const SYS_MODULE_ACCESS_DATA_SYS = 'sys_module_access_data_sys'; //取数工单系统
    const SYS_MODULE_TRAINING = 'sys_module_training'; //EHS管理
    const SYS_MODULE_RESERVE = 'sys_module_reserve'; //网点备用金
    const SYS_MODULE_QUOTATION = 'sys_module_quotation'; //报价管理
    const SYS_MODULE_TRAINING_SYSTEM = 'sys_module_training_system'; //FLASH学院管理后台
    const SYS_MODULE_BANK_FLOW = 'sys_module_bank_flow'; //银行流水管理
    const SYS_MODULE_ACCIDENT = 'sys_module_accident'; //异常/事故处理
    const SYS_MODULE_PAY = 'sys_module_pay'; //支付管理
    const SYS_MODULE_SHOP = 'sys_module_shop'; //员工商城
    const SYS_MODULE_WITHHOLDING_TAX = 'sys_module_withholding_tax'; //代扣税管理
    const SYS_MODULE_KPI = 'sys_module_kpi'; //KPI目标管理
    const SYS_MODULE_SCHOOL = 'sys_module_school'; //员工培训系统
    const SYS_MODULE_SAPMANAGE = 'sys_module_sapmanage'; //sap数据传输管理
    const SYS_MODULE_SETTING = 'sys_module_setting'; //配置管理
    const SYS_MODULE_TALENT_REVIEW = 'sys_module_talent_review'; //人才盘点
    const SYS_MODULE_DEPOSIT = 'sys_module_deposit'; //押金管理
    const SYS_MODULE_DEPOSIT_REIMBURSEMENT = 'sys_module_deposit_reimburse';//押金管理-报销
    const SYS_MODULE_DEPOSIT_ORDINARY_PAYMENT = 'sys_module_deposit_ordinary_payment';//押金管理-普通付款
    const SYS_MODULE_DEPOSIT_PURCHASE_PAYMENT = 'sys_module_deposit_purchase_payment';//押金管理-采购付款
    const SYS_MODULE_DEPOSIT_RENT_PAYMENT = 'sys_module_deposit_rent_payment';//押金管理-租房付款

    const SYS_MODULE_CHEQUE = 'sys_module_cheque'; //支票管理
    const SYS_MODULE_ADMINISTRATION_TICKET = 'sys_module_administration_ticket'; //行政工单
    const SYS_MODULE_ASSET_WORK_ORDER = 'sys_module_asset_work_order'; //资产工单
    const SYS_MODULE_SALARY_DEDUCT = 'sys_module_salary_deduct'; //薪资抵扣管理

    const SYS_MODULE_SHOP_WORK_SIZE = 'sys_module_shop_work_size'; //员工商城-员工工服尺码
    const SYS_MODULE_SHOP_BATCH_ORDER = 'sys_module_shop_batch_order'; //员工商城-批量下单工服
    const SYS_MODULE_AGENCY_PAYMENT = 'sys_module_agency_payment'; //代理支付

    // 业务模块的数据权限启用状态
    const DATA_PERMISSION_STATUS_NO = 0;// 未启用
    const DATA_PERMISSION_STATUS_YES = 1;// 已启用

    // 数据配置权限 应用场景
    const DATA_PERMISSION_APPLICATION_SCENES_ALL = 1; // 全部部门: 管辖范围中 未删除 + 已删除的部门
    const DATA_PERMISSION_APPLICATION_SCENES_ENABLED = 2; // 可用部门: 管辖范围中 未删除的部门

    // 是否开启:审批流驳回后给申请人发送短信提醒/给申请人/发起人发送审批通过待支付BY消息提醒
    const SEND_SUBMITTER_REJECT_SMS_YES = 1;
    const SEND_SUBMITTER_REJECT_SMS_NO = 0;
}
