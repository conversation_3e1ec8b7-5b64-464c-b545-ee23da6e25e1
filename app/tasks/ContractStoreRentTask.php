<?php

use App\Library\Enums;
use App\Library\Enums\ContractEnums;
use App\Library\Enums\GlobalEnums;
use App\Library\ErrCode;
use App\Library\Exception\BusinessException;
use App\Library\Validation\ValidationException;
use App\Models\oa\SysAttachmentModel;
use App\Models\oa\WarehouseStoreModel;
use App\Modules\Common\Services\EnumsService;
use App\Modules\Contract\Models\ContractArchive;
use App\Modules\Contract\Models\ContractStoreRentingModel;
use App\Modules\Contract\Services\ContractStoreRentingService;
use App\Repository\StoreRepository;
use App\Repository\HrStaffRepository;

class ContractStoreRentTask extends BaseTask
{

    //刷历史数据
    public function flush_wt_area_serviceAction()
    {
        $data = [];

        $rents = ContractStoreRentingModel::find()->toArray();

        foreach ($rents as $rent){
            $tmp  = [];
            $tmp['store_renting_contract_id'] = $rent['id'];
            $tmp['contract_id'] = $rent['contract_id'];

            if(!empty($rent['withholding_tax'])){
                $tmp['type'] = 'rent_tax';
                $tmp['label'] = ' ';
                $tmp['amount'] = floatval($rent['withholding_tax']);
                $data[] = $tmp;
            }

            if(!empty($rent['wt_area_service'])){
                $tmp['type'] = 'rent_service_tax';
                $tmp['label'] = ' ';
                $tmp['amount'] = floatval($rent['wt_area_service']);
                $data[] = $tmp;
            }
        }
        $model = new \App\Modules\Contract\Models\ContractStoreRentingMoneyModel();
        $flag = $model->batch_insert($data);
        echo json_encode($flag).PHP_EOL;
    }

    // BI网点租房合同记录表到OA系统归档(一次性的, 已废弃)
    public function sync_contract_store_renting_serviceAction(){
        // 获取bi中已通过的网点租房合同
        $sql = "select * from `store_renting_contract` where `state` = 3 and `pass_state` = 1;";
        $list = $this->db_rbi->fetchAll($sql);

        // 将合同数据映射到OA系统表
        $contractStoreRentingList = $contractStoreRentingDetailList = $contractList = $contractArchiveList = [];
        // 最新的合同
        $lastContract       = ContractStoreRentingModel::findFirst([
            'order'      => 'id desc',
            'columns'    => 'id,contract_no',
        ]);
        $contractId       = ($lastContract && !empty($lastContract->id)) ? $lastContract->id + 1 : 1;
        $contractNo       = ($lastContract && !empty($lastContract->contract_no)) ? $lastContract->contract_no + 1 : 1;

        foreach ($list as $contract) {
            // 合并映射到contract_store_renting表数据
            $contractStoreRentingItem = $this->mapToContractStoreRenting($contract,$contractId,$contractNo);
            // 合并映射到contract_store_renting_detail表数据
            $contractStoreRentingDetailList[] = $this->mapToContractStoreRentingDetail($contractStoreRentingItem,$contractId);
            // 合并映射到contract表数据
            $contractList[] = $this->mapToContract($contractStoreRentingItem);
            // 合并归档contract_archive表数据
            $contractArchiveList[] = $this->mapToContractArchive($contractStoreRentingItem);

            $contractStoreRentingList[] = $contractStoreRentingItem;
            $contractId++;
            $contractNo++;
        }

        try {
            $this->db_oa->begin();
            $contractStoreRentingModel = new ContractStoreRentingModel();
            $flag = $contractStoreRentingModel->batch_insert($contractStoreRentingList);
            if (!$flag) {
                $this->db_oa->rollback();
                echo '网点租房合同表映射入库失败'.PHP_EOL;
                die;
            }
            $contractStoreRentingDetailModel = new \App\Modules\Contract\Models\ContractStoreRentingDetailModel();
            $flag = $contractStoreRentingDetailModel->batch_insert($contractStoreRentingDetailList);
            if (!$flag) {
                $this->db_oa->rollback();
                echo '网点租房合同明细表映射入库失败'.PHP_EOL;
                die;
            }
            $contractModel = new \App\Modules\Contract\Models\Contract();
            $flag = $contractModel->batch_insert($contractList);
            if (!$flag) {
                $this->db_oa->rollback();
                echo '合同总表映射入库失败'.PHP_EOL;
                die;
            }
            $contractArchiveModel = new \App\Modules\Contract\Models\ContractArchive();
            $flag = $contractArchiveModel->batch_insert($contractArchiveList);
            if (!$flag) {
                $this->db_oa->rollback();
                echo '合同归档表映射入库失败'.PHP_EOL;
                die;
            }
            $this->db_oa->commit();
        } catch (Exception $e) {
            echo 'Error info: '.json_encode($e->getMessage()).PHP_EOL;
            die;
        }

        echo '同步和归档成功！'.PHP_EOL;
    }

    private function mapToContractStoreRenting($contract,$id,$contractNo){
        return [
            'id' => $id,
            'manage_id' => 10000,
            'store_id' => $contract['store_id'],
            'store_cate' => $this->mappingToStoreType($contract['store_id'],$contract['type']),
            'provinces' => $contract['provinces'],
            'lon_lat' => $contract['lon_lat'],
            'store_addr' => $contract['store_addr'],
            'house_owner_type' => $contract['contract_Initiator'],
            'house_owner_name' => $contract['house_owner_name'],
            'house_contract_area' => $contract['house_contract_area'],
            'house_actual_area' => $contract['house_actual_area'],
            'house_equip_list' => $contract['house_equip_list'],
            'contract_name' => $contract['contract_name'],
            'contract_id' => $contract['store_id'].$id,
            'contract_deadline' => $contract['contract_deadline'],
            'contract_lease_type' => $contract['contract_lease_type'],
            'contract_begin' => $contract['contract_begin'],
            'contract_end' => $contract['contract_end'],
            'rent_free_time' => $contract['rent_free_time'],
            'contract_effect_date' => $contract['contract_effect_date'],
            'contract_signer_name' => $contract['contract_signer_name'],
            'signer_phone' => $contract['signer_phone'],
            'contract_money' => $contract['contract_money'],
            'contract_benefits' => $contract['contract_benefits'],
            'paid_money' => $contract['paid_money'],
            'exempted_amount_months' => $contract['exempted_amount_months'],
            'monthly_payment_type' => $contract['monthly_payment_type'],
            'actual_monthly_payment' => $contract['actual_monthly_payment'],
            'money_symbol' => 1, // 泰币
            'exchange_rate' => 1, // 汇率默认1
            'deposit_amount' => $contract['deposit_amount'],
            'pay_amount' => $contract['pay_amount'],
            'full_refund_conditions' => $contract['full_refund_conditions'],
            'contract_Initiator' => $contract['contract_Initiator'],
            'contract_remarks' => $contract['contract_remarks'],
            'bank_collection' => $contract['bank_collection'],
            'hourse_owner_addr' => $contract['hourse_owner_addr'],
            'monthly_area_service' => $contract['monthly_area_service'],
            'vat' => $contract['vat'],
            'withholding_tax' => $contract['withholding_tax'],
            'wt_area_service' => $contract['wt_area_service'],
            'property_tax' => $contract['property_tax'],
            'payment_method' => $contract['payment_method'],
            'total_amount_monthly' => $contract['total_amount_monthly'],
            'duty_stamp' => $contract['duty_stamp'],
            'total_amount' => $contract['total_amount'],
            'signboard_tax' => $contract['signboard_tax'],
            // 已废弃, v18530 已由表字段存储 迁移到 系统附件表
            'pdf_required_name' => !empty($contract['pdf_required_name']) ? $this->addBucketName($contract['pdf_required_name']) : '',
            'pdf_noneed_name' => !empty($contract['pdf_noneed_name']) ? $this->addBucketName($contract['pdf_noneed_name']) : '',
            'state' => 2,
            'type' => $contract['type'],
            'pass_state' => $contract['pass_state'],
            'created_at' => $contract['created_at'],
            'update_at' => $contract['update_at'],
            'is_main' => 1, // 默认主合同
            'contract_no' => $contractNo,
            'contract_lang' => 1, // 默认为泰语
            'area_service_amount_no_tax' => floatval($contract['monthly_area_service']),
            'area_service_amount_vat' => 0,
            'area_amount_wht' => floatval($contract['wt_area_service']),
            'contract_total_amount' => 0, // 总金额
            'contract_deposit' => $contract['contract_benefits'], // 定金
            'ver' => 1, // 新版本
        ];
    }

    private function mapToContractStoreRentingDetail($contract,$contractId){
        return [
            'contract_store_renting_id' => $contractId,
            'cost_start_date' => $contract['contract_begin'],
            'cost_end_date' => $contract['contract_end'],
            'amount_no_tax' => $contract['contract_money'],
            'vat_rate' => 0, // 默认为空
            'amount_vat' => floatval($contract['vat']),
            'amount_has_tax' => floatval($contract['actual_monthly_payment']),
            'wht_category' => 0,
            'wht_rate' => 0,
            'amount_wht' => floatval($contract['withholding_tax'])
        ];
    }

    private function mapToContract($contract){
        return [
            'cno'          => $contract['contract_id'],
            'cname'            => $contract['contract_name'],
            'status'           => \App\Library\Enums::CONTRACT_STATUS_PENDING,
            'is_master'        => \App\Library\Enums::CONTRACT_IS_MASTER_YES, // 默认主合同
            'sub_cno'          => '',
            'payment_currency' => $contract['money_symbol'],
            'amount'           => $contract['contract_money'],
            'contract_type'    => 'store_renting_contract',
            'create_id' => $contract['manage_id']
        ];
    }

    private function mapToContractArchive($contract){
        $new_contract = [];

        $new_contract['cno']              = $contract['contract_id'];     //合同编号
        $new_contract['cname']            = $contract['contract_name'];        //合同名称
        $new_contract['status']           = 2;                            //归档状态，1:待归档，2:已归档'
        $new_contract['template_id']      = 0;            //模版ID
        $new_contract['is_master']        = $contract['is_main'] == 1 ? 1 : 0;         //是否是主合同
        $new_contract['sub_cno']          = '';               //关联的主合同编号
        $new_contract['amount']           = !empty($contract['contract_money']) ? $contract['contract_money'] * 1000 : 0;//合同金额，千分位数字
        $new_contract['payment_currency'] = $contract['money_symbol'];          //付款币种，1:泰铢，2:美元，3:人民币
        $new_contract['create_id']        = 10000;               //申请人
        $new_contract['create_name']      = 'SuperAdmin';           //申请人姓名
        $new_contract['created_at']       = date('Y-m-d H:i:s');

        return $new_contract;
    }

    private function mappingToStoreType($store_id,$store_type){
        $type = 0;
        // 1.HO总部,2.HUB,3.DC/SP,4.SHOP,5-BDC)
        if (1 == $store_type) {
            $type = -1;
        } elseif(2 == $store_type) {
            $type = 8;
        } elseif(in_array($store_type,[3,4])) {
            // 取网点类型
            $sql = "select id,category from `sys_store` where `id` = '{$store_id}' limit 1";
            $detail = $this->db_fle->fetchOne($sql,\Phalcon\Db::FETCH_ASSOC);
            $type = $detail['category'];
        } elseif(5 == $store_type) {
            $type = 10;
        }

        return $type;
    }

    private function addBucketName($pdf_json){
        $pdfArr = json_decode($pdf_json);
        // 根据环境取bucket_name
        $img_prefix = env('img_prefix');
        $bucket_name = !empty($img_prefix) ? preg_split("/[\/\.]/",$img_prefix)[2] : '';

        if (!empty($pdfArr)) {
            foreach ($pdfArr as &$pdf) {
                $pdf->bucket_name = $bucket_name;
            }
            return json_encode($pdfArr);
        }
        return '';
    }

    /**
     * 租房合同正文附件 和 其他附件数据迁移(一次性)
     *
     * 说明: 由合同主表字段存储 迁移到 系统附件表
     *
     * 若需重新跑, 须先确保合同附件数据没有跑重复, 重跑前建议:
     * 1. 先删除系统附件表指定合同范围指定类型的附件
     *  1.1 查询SQL: select * from sys_attachment where oss_bucket_type = 61 and sub_type in (1, 2);
     *  1.2 删除SQL: delete from sys_attachment where oss_bucket_type = 61 and sub_type in (1, 2) and created_at >= '2023-12-20 18:00:00';
     * 2. 执行重跑脚本
     *
     * php app/cli.php contract_store_rent sync_pdf_files
     */
    public function sync_pdf_filesAction()
    {
        $log = '开始时间: ' . date('Y-m-d H:i:s') . PHP_EOL;
        $logger_type = 'info';

        try {
            // 1. 读取所有合同的正文附件 和 其他附件
            $contract_list = ContractStoreRentingModel::find([
                'columns' => ['id', 'contract_id', 'pdf_required_name', 'pdf_noneed_name']
            ])->toArray();
            $log .= '共有 ' . count($contract_list) . ' 条租房合同' . PHP_EOL;
            if (empty($contract_list)) {
                throw new ValidationException('租房合同数据为空, 无需处理', ErrCode::$VALIDATE_ERROR);
            }

            // 2. 构建待入库的附件
            // 合同的两个附件数据均为空的统计 和 日志
            $all_file_null_contract_count = 0;
            $all_file_null_contract_log = '';

            // 合同的两个附件json_decode 后非数组格式日志
            $text_file_data_structure_error_log = '';// 正文附件
            $other_file_data_structure_error_log = '';// 其他附件

            // 批量写入附件表失败的日志
            $batch_insert_error_log = '';

            // 共需生成多少个附件
            $file_total_count = 0;

            // 成功写入附件的数量
            $success_file_total_count = 0;

            // 分段处理(每段1000)
            $contract_list_chunk = array_chunk($contract_list, 1000);
            $sys_attachment_model = new SysAttachmentModel();
            foreach ($contract_list_chunk as $contracts) {
                $init_files = [];
                foreach ($contracts as $item) {
                    // 正文合同
                    $text_files = json_decode($item['pdf_required_name'], true);

                    // 其他附件
                    $other_files = json_decode($item['pdf_noneed_name'], true);

                    // 两种附件均为空, 跳过
                    if (empty($text_files) && empty($other_files)) {
                        $all_file_null_contract_count++;
                        $all_file_null_contract_log .= $item['contract_id'] . " - {$item['pdf_required_name']} ----- {$item['pdf_noneed_name']}" . PHP_EOL;
                        continue;
                    }

                    // 正文附件 数据结构校验
                    if (!is_array($text_files)) {
                        $text_file_data_structure_error_log .= "{$item['contract_id']} - " . $item['pdf_required_name'] . PHP_EOL;
                    } else {
                        // 构建正文附件的数据结构
                        foreach ($text_files as $t_file) {
                            // oss 文件路径为空的, 不进行初始化
                            if (empty($t_file['object_key'])) {
                                $text_file_data_structure_error_log .= "{$item['contract_id']} - " . $item['pdf_required_name'] . PHP_EOL;
                                continue;
                            }

                            $file_total_count++;

                            $init_files[] = [
                                'oss_bucket_type' => ContractEnums::RENT_CONTRACT_FILE_OSS_BUCKET_TYPE,
                                'sub_type' => ContractEnums::RENT_CONTRACT_SUB_TYPE_FILE_TEXT,
                                'oss_bucket_key' => $item['id'],
                                'bucket_name' => $t_file['bucket_name'] ?? '',
                                'object_key' => $t_file['object_key'],
                                'file_name' => $t_file['file_name'] ?? '',
                                'created_at' => date('Y-m-d H:i:s'),
                            ];
                        }
                    }

                    // 其他附件 数据结构校验
                    if (!is_array($other_files)) {
                        $other_file_data_structure_error_log .= "{$item['contract_id']} - " . $item['pdf_noneed_name'] . PHP_EOL;
                    } else {
                        // 构建其他附件的数据结构
                        foreach ($other_files as $o_file) {
                            // oss 文件路径为空的, 不进行初始化
                            if (empty($o_file['object_key'])) {
                                $other_file_data_structure_error_log .= "{$item['contract_id']} - " . $item['pdf_noneed_name'] . PHP_EOL;
                                continue;
                            }

                            $file_total_count++;

                            $init_files[] = [
                                'oss_bucket_type' => ContractEnums::RENT_CONTRACT_FILE_OSS_BUCKET_TYPE,
                                'sub_type' => ContractEnums::RENT_CONTRACT_SUB_TYPE_FILE_OTHER,
                                'oss_bucket_key' => $item['id'],
                                'bucket_name' => $o_file['bucket_name'] ?? '',
                                'object_key' => $o_file['object_key'],
                                'file_name' => $o_file['file_name'] ?? '',
                                'created_at' => date('Y-m-d H:i:s'),
                            ];
                        }
                    }
                }

                // 3. 附件入库
                if (!empty($init_files)) {
                    if ($sys_attachment_model->batch_insert($init_files) === false) {
                        $batch_insert_error_log .= json_encode($init_files, JSON_UNESCAPED_UNICODE) . PHP_EOL;
                    } else {
                        $success_file_total_count += count($init_files);
                    }
                }
            }

            $log .= '共需生成 ' . $file_total_count . ' 个附件' . PHP_EOL;
            $log .= '成功生成 ' . $success_file_total_count . ' 个附件' . PHP_EOL;
            $log .= '两种附件均为空的合同数量: ' . $all_file_null_contract_count . PHP_EOL;
            $log .= '两种附件均为空的日志: ' . $all_file_null_contract_log . PHP_EOL;
            $log .= '正文附件数据结构有误的日志: ' . $text_file_data_structure_error_log . PHP_EOL;
            $log .= '其他附件数据结构有误的日志: ' . $other_file_data_structure_error_log . PHP_EOL;
            $log .= '附件批量写入失败的数据: ' . $batch_insert_error_log . PHP_EOL;

        } catch (ValidationException $e) {
            $log .= '提醒: ' . $e->getMessage() . PHP_EOL;
        } catch (BusinessException $e) {
            $logger_type = 'warning';
            $log .= '注意: ' . $e->getMessage() . PHP_EOL;
        } catch (Exception $e) {
            $logger_type = 'error';
            $log .= '异常: ' . $e->getMessage() . PHP_EOL;
        }

        $log .= '结束时间: ' . date('Y-m-d H:i:s') . PHP_EOL;
        $this->logger->$logger_type($log);
        exit($log);
    }

    /**
     * 去掉租房合同银行信息里的前后空格
     *
     * php app/cli.php contract_store_rent update_bank_collection xxxxx,yyyyyyy
     *
     * @param array $params
     */
    public function update_bank_collectionAction(array $params = [])
    {
        $log = '';
        $contract_nos = $params[0] ?? '';
        $contract_nos = explode(',', $contract_nos);
        $log .= '待处理单据 ' . count($contract_nos) . ' 条' . PHP_EOL;
        if (empty($contract_nos)) {
            exit($log);
        }

        $rents = ContractStoreRentingModel::find([
            'conditions' => 'contract_id IN ({contract_ids:array})',
            'bind' => ['contract_ids' => $contract_nos]
        ]);

        $rents_count = count($rents);
        $log .= '对应单据共 ' . $rents_count . ' 条' . PHP_EOL;
        if (empty($rents_count)) {
            exit($log);
        }

        $null_log = [];
        $success_count = 0;
        $error_log = [];
        foreach ($rents as $model) {
            if (empty($model->bank_collection)) {
                $null_log[] = $model->contract_id;
                continue;
            }

            $bank_collection = json_decode($model->bank_collection, true);
            if (empty($bank_collection)) {
                $null_log[] = $model->contract_id;
                continue;
            }

            // 去各元素空格
            $bank_collection = trim_array($bank_collection);

            $update = [
                'bank_collection' => json_encode($bank_collection, JSON_UNESCAPED_UNICODE)
            ];
            if ($model->i_update($update) === false) {
                $error_log[$model->contract_id] = get_data_object_error_msg($model);
                continue;
            }

            $success_count++;
        }

        $log .= '成功处理 ' . $success_count . ' 条' . PHP_EOL;
        $log .= '信息为空的单号: ' . implode(',', $null_log) . PHP_EOL;
        $log .= '更新失败的单号及错误原因: ' . json_encode($error_log, JSON_UNESCAPED_UNICODE) . PHP_EOL;

        exit($log);
    }

    /**
     *
     * 租房合同到期提醒
     *
     * php app/cli.php contract_store_rent expiration_reminder
     *
     */
    public function expiration_reminderAction()
    {
        $logger_type = 'info';

        $log = '网点租房合同到期提醒' . PHP_EOL;
        $log .= '开始时间: ' . get_datetime_with_milliseconds() . PHP_EOL;

        try {
            // 获取邮箱配置
            $emails = EnumsService::getInstance()->getSettingEnvValueIds('rent_contract_expiration_reminder_emails');

            $emails = array_map(function ($val) {
                return trim($val);
            }, $emails);

            if (empty($emails)) {
                throw new BusinessException('网点租房合同到期提醒邮箱未配置[rent_contract_expiration_reminder_emails], 请同步产品', ErrCode::$VALIDATE_ERROR);
            }

            $log .= '收件人邮箱:  ' . implode(',', $emails) . PHP_EOL;

            // 获取提醒日期规则配置
            $reminder_days = EnumsService::getInstance()->getSettingEnvValueIds('rent_contract_expiration_reminder_days');
            $reminder_days = array_unique($reminder_days);
            if (empty($reminder_days)) {
                throw new BusinessException('网点租房合同到期提醒天数未配置[rent_contract_expiration_reminder_days], 请同步产品', ErrCode::$VALIDATE_ERROR);
            }

            $log .= '提醒天数配置:  ' . implode(',', $reminder_days) . PHP_EOL;

            foreach ($reminder_days as $day) {
                if (!is_numeric($day)) {
                    throw new BusinessException('网点租房合同到期提醒天数不符合规则[rent_contract_expiration_reminder_days], 请同步产品', ErrCode::$VALIDATE_ERROR);
                }
            }

            // 获取各提醒规则的待提醒数据
            // 到期的结束日期
            $end_date_item = [];
            foreach ($reminder_days as $day) {
                $end_date_item[] = date('Y-m-d', strtotime("+ $day day"));
            }

            $log .= '待提醒的到期日期:  ' . implode(',', $end_date_item) . PHP_EOL;

            $today_date = date('Y-m-d');
            $columns = [
                'main.contract_name',
                'main.contract_id',
                'main.contract_begin',
                'main.contract_end',
                "DATEDIFF(main.contract_end, '{$today_date}') AS days",
                'main.warehouse_id',
                'main.warehouse_name',
                'main.warehouse_province_name',
                'main.warehouse_city_name',
                'main.warehouse_district_name',
            ];

            $builder = $this->modelsManager->createBuilder();
            $builder->from(['main' => ContractStoreRentingModel::class]);
            $builder->leftjoin(ContractArchive::class, 'achive.cno = main.contract_id', 'achive');
            $builder->where('main.contract_status = :contract_status:', ['contract_status' => Enums::CONTRACT_STATUS_APPROVAL]);
            $builder->andWhere('main.warehouse_id != :null_warehouse_id:', ['null_warehouse_id' => '']);
            $builder->andWhere('achive.contract_type = :contract_type:', ['contract_type' => ContractEnums::CONTRACT_TYPE_STORING]);
            $builder->inWhere('main.contract_end', $end_date_item);
            $builder->inWhere('main.is_main', [Enums::CONTRACT_IS_MASTER_YES, Enums::CONTRACT_IS_MASTER_NO]);
            $builder->inWhere('achive.status', [
                ContractEnums::CONTRACT_ARCHIVE_STATUS_PENDING,
                ContractEnums::CONTRACT_ARCHIVE_STATUS_APPROVAL,
                ContractEnums::CONTRACT_ARCHIVE_STATUS_NO_UPLOAD
            ]);

            $builder->columns($columns);
            $items = $builder->getQuery()->execute()->toArray();

            $log .= '待提醒合同数:  ' . count($items) . PHP_EOL;
            if (empty($items)) {
                throw new ValidationException('无待提醒的到期合同', ErrCode::$VALIDATE_ERROR);
            }

            // 获取仓库的主网点
            $warehouse_ids = array_values(array_unique(array_column($items, 'warehouse_id')));
            $main_store_item = WarehouseStoreModel::find([
                'conditions' => 'warehouse_id IN ({warehouse_ids:array}) AND store_flag = :store_flag: AND use_status = :use_status:',
                'bind' => [
                    'warehouse_ids' => $warehouse_ids,
                    'store_flag' => ContractEnums::WAREHOUSE_STORE_FLAG_MAIN,
                    'use_status' => ContractEnums::WAREHOUSE_STORE_USE_STATUS_ING,
                ],
                'columns' => ['warehouse_id', 'store_id']
            ])->toArray();

            $main_store_item = array_column($main_store_item, null, 'warehouse_id');

            // 网点名称
            $store_ids = array_column($main_store_item, 'store_id');
            $store_list = (new StoreRepository())->getStoreListByIds($store_ids, null);
            foreach ($main_store_item as &$store) {
                $store_info = $store_list[$store['store_id']] ?? [];
                if (!empty($store_info)) {
                    $store['store_name'] = $store_info['name'];
                } else {
                    $store['store_name'] = $store['store_id'] == Enums::PAYMENT_HEADER_STORE_ID ? Enums::PAYMENT_HEADER_STORE_NAME : '';
                }
            }

            // 生成Excel
            $excel_data = [];
            foreach ($items as $val) {
                $using_main_store_info = $main_store_item[$val['warehouse_id']] ?? [];

                $excel_data[] = [
                    $val['contract_name'],
                    $val['contract_id'],
                    $val['contract_begin'],
                    $val['contract_end'],
                    $val['days'],
                    $val['warehouse_id'],
                    $val['warehouse_name'],
                    $val['warehouse_province_name'] . ' ' . $val['warehouse_city_name'] . ' ' . $val['warehouse_district_name'],
                    $using_main_store_info['store_id'] ?? '',
                    $using_main_store_info['store_name'] ?? '',
                ];
            }

            $country_code = get_country_code();

            // 第一语言
            static::setLanguage('en');
            $first_lang = static::$t;

            // 第二语言
            $second_language = $country_code == GlobalEnums::TH_COUNTRY_CODE ? 'th' :'zh-CN';
            static::setLanguage($second_language);
            $second_lang = static::$t;

            $excel_header = [
                $first_lang['expiration_reminder_file_001'] . PHP_EOL . $second_lang['expiration_reminder_file_001'],
                $first_lang['expiration_reminder_file_002'] . PHP_EOL . $second_lang['expiration_reminder_file_002'],
                $first_lang['expiration_reminder_file_003'] . PHP_EOL . $second_lang['expiration_reminder_file_003'],
                $first_lang['expiration_reminder_file_004'] . PHP_EOL . $second_lang['expiration_reminder_file_004'],
                $first_lang['expiration_reminder_file_005'] . PHP_EOL . $second_lang['expiration_reminder_file_005'],
                $first_lang['expiration_reminder_file_006'] . PHP_EOL . $second_lang['expiration_reminder_file_006'],
                $first_lang['expiration_reminder_file_007'] . PHP_EOL . $second_lang['expiration_reminder_file_007'],
                $first_lang['expiration_reminder_file_008'] . PHP_EOL . $second_lang['expiration_reminder_file_008'],
                $first_lang['expiration_reminder_file_009'] . PHP_EOL . $second_lang['expiration_reminder_file_009'],
                $first_lang['expiration_reminder_file_010'] . PHP_EOL . $second_lang['expiration_reminder_file_010'],
            ];

            $file_name = 'Reminder of expiration of rental contract at the outlet ' . date('Ymd') . '.xlsx';
            $file_res = ContractStoreRentingService::getInstance()->exportExcel($excel_header, $excel_data, $file_name);

            $log .= '提醒文件:  ' . $file_res['data'] . PHP_EOL;

            if (empty($file_res['data'])) {
                throw new Exception('网点租房合同到期提醒Excel生成失败', ErrCode::$SYSTEM_ERROR);
            }

            // 发送邮件(同步)
            $email_subject = $country_code . $first_lang['expiration_reminder_email_subject'] . ' ' . $second_lang['expiration_reminder_email_subject'];

            $email_content = $first_lang->_('expiration_reminder_email_content', ['file_download_url' => $file_res['data']]);
            $email_content .= "<br />" . $second_lang->_('expiration_reminder_email_content', ['file_download_url' => $file_res['data']]);

            $log .= '邮件主题: ' . $email_subject . PHP_EOL;

            if (!$this->mailer->send($emails, $email_subject, $email_content)) {
                throw new BusinessException('邮件发送失败', ErrCode::$BUSINESS_ERROR);
            }

            $log .= '邮件发送成功' . PHP_EOL;

        } catch (ValidationException $e) {
            $log .= $e->getMessage() . PHP_EOL;

        } catch (BusinessException $e) {
            $logger_type = 'notice';
            $log .= $e->getMessage() . PHP_EOL;

        } catch (Exception $e) {
            $logger_type = 'warning';
            $log .= $e->getMessage() . PHP_EOL;
        }

        $log .= '结束时间: ' . get_datetime_with_milliseconds() . PHP_EOL;
        $this->logger->$logger_type($log);
        exit($log);
    }

    /**
     * 
     * 同步租房合同负责人所属部门的存量数据
     *
     * php app/cli.php contract_store_rent sync_leader_department
     *
     * @param array $params
     */
    public function sync_leader_departmentAction(array $params = [])
    {
        $log = '开始时间: ' . date('Y-m-d H:i:s') . PHP_EOL;
        $logger_type = 'info';

        try {
            // 1. 获取所有租房合同
            $contract_models = ContractStoreRentingModel::find();
            $contract_list = $contract_models->toArray();
            $log .= '共有 ' . count($contract_list) . ' 条租房合同' . PHP_EOL;
            if (empty($contract_list)) {
                throw new ValidationException('租房合同数据为空, 无需处理', ErrCode::$VALIDATE_ERROR);
            }

            // 2. 分批获取租房合同负责人的所属部门 并 更新到合同中
            // 获取所有合同负责人的所属部门
            $contract_leader_ids = array_filter(array_unique(array_column($contract_list, 'contract_leader_id')));
            $log .= '共涉及 ' . count($contract_leader_ids) . ' 个负责人' . PHP_EOL;
            if (empty($contract_leader_ids)) {
                throw new BusinessException('未找到负责人信息, 无需处理', ErrCode::$VALIDATE_ERROR);
            }

            $contract_leader_ids  = (new HrStaffRepository())->getStaffNodeDepartmentInfoByIds(array_values($contract_leader_ids));

            // 逐个更新合同
            $leader_id_null_list = [];
            $leader_info_null_list = [];
            $sync_success_count = 0;
            $sync_error_list = [];
            foreach ($contract_models as $model) {
                if (empty($model->contract_leader_id)) {
                    $leader_id_null_list[] = $model->contract_id;
                    continue;
                }

                // 负责人信息
                $leader_info = $contract_leader_ids[$model->contract_leader_id] ?? [];
                if (empty($leader_info)) {
                    $leader_info_null_list[] = $model->contract_id . ' - ' . $model->contract_leader_id;
                    continue;
                }

                // 更新
                $update_data = [
                    'leader_node_department_id' => $leader_info['node_department_id'] ?? 0,
                    'leader_node_department_name' => $leader_info['node_department_name'] ?? '',
                ];
                if ($model->i_update($update_data) === false) {
                    $sync_error_list[] = $model->contract_id . ' - ' . $model->contract_leader_id;
                    continue;
                }

                $sync_success_count++;

                if ($sync_success_count % 1000 == 0) {
                    sleep(5);
                }
            }

            $log .= '同步成功 ' . $sync_success_count . PHP_EOL;
            $log .= '同步失败单据 ' . json_encode($sync_error_list, JSON_UNESCAPED_UNICODE) . PHP_EOL;
            $log .= '负责人ID为空的单据 ' . json_encode($leader_id_null_list, JSON_UNESCAPED_UNICODE) . PHP_EOL;
            $log .= '负责人信息为空的单据 ' . json_encode($leader_info_null_list, JSON_UNESCAPED_UNICODE) . PHP_EOL;


        } catch (ValidationException $e) {
            $log .= '提醒: ' . $e->getMessage() . PHP_EOL;
        } catch (BusinessException $e) {
            $logger_type = 'notice';
            $log .= '注意: ' . $e->getMessage() . PHP_EOL;
        } catch (Exception $e) {
            $logger_type = 'error';
            $log .= '异常: ' . $e->getMessage() . PHP_EOL;
        }

        $log .= '结束时间: ' . date('Y-m-d H:i:s') . PHP_EOL;
        $this->logger->$logger_type($log);
        exit($log);

    }

    /**
     * 更新合同有效状态
     *
     * @description: 更新合同有效状态
     * @author: AI
     * @date: 2025-08-26 21:14:00
     * php app/cli.php contract_store_rent update_contract_effective_status
     */
    public function update_contract_effective_statusAction()
    {
        $this->checkLock(__METHOD__);
        $log = '开始时间: ' . date('Y-m-d H:i:s') . PHP_EOL;
        $logger_type = 'info';
        
        try {
            // 获取当前系统日期
            $currentDate = date('Y-m-d');
            $log .= '当前系统日期: ' . $currentDate . PHP_EOL;
            
            // 查询合同状态为审批通过且合同有效状态非失效的记录
            $contracts = ContractStoreRentingModel::find([
                'conditions' => 'contract_status = :contract_status: AND effective_status != :effective_status:',
                'bind' => [
                    'contract_status' => Enums::WF_STATE_APPROVED, // 审批通过
                    'effective_status' => ContractEnums::RENT_CONTRACT_EFFECTIVE_STATUS_INVALID  // 非失效状态
                ],
                'columns' => ['id', 'contract_id', 'contract_begin', 'contract_end', 'effective_status']
            ])->toArray();
            if (empty($contracts)) {
                throw new ValidationException('无符合条件的合同记录，无需处理', ErrCode::$VALIDATE_ERROR);
            }
            
            $totalCount = count($contracts);
            $log .= '查询到符合条件的合同数量: ' . $totalCount . PHP_EOL;

            // 统计各类更新数量
            $updateCount  = 0;
            $expiredCount = 0;   // 失效
            $activeCount  = 0;   // 生效中
            $pendingCount = 0;   // 待生效
            $errorList    = [];
            
            // 遍历处理每个合同
            foreach ($contracts as $contract) {
                $contractId    = $contract['contract_id'];
                $contractBegin = $contract['contract_begin'];
                $contractEnd   = $contract['contract_end'];
                $currentStatus = $contract['effective_status'];
                
                // 根据日期关系判断新的有效状态
                if ($currentDate > $contractEnd) {
                    //当前系统日期大于合同结束日期的，合同有效状态改为失效
                    $newStatus = ContractEnums::RENT_CONTRACT_EFFECTIVE_STATUS_INVALID; // 失效
                    $expiredCount++;
                } elseif ($currentDate < $contractBegin) {
                    //当前系统日期小于合同开始日期的，合同有效状态改为待生效
                    $newStatus = ContractEnums::RENT_CONTRACT_EFFECTIVE_STATUS_WAIT; // 待生效
                    $pendingCount++;
                } else {
                    //当前系统日期在合同开始日期和结束日期之间的（包括等于），合同有效状态改为生效中
                    $newStatus = ContractEnums::RENT_CONTRACT_EFFECTIVE_STATUS_VALID; // 生效中
                    $activeCount++;
                }

                // 如果状态发生变化，则更新（添加乐观锁和重试机制）
                if ($newStatus == $currentStatus) {
                    continue;
                }

                $updateSuccess = false;
                $retryCount = 0;
                $maxRetries = 3;

                while (!$updateSuccess && $retryCount < $maxRetries) {
                    // 重新获取最新数据
                    $latestModel = ContractStoreRentingModel::findFirst([
                        'conditions' => 'id = :id: AND effective_status = :status:',
                        'bind' => ['id' => $contract['id'], 'status' => $currentStatus]
                    ]);

                    if (!$latestModel) {
                        $errorList[] = [
                            'contract_id' => $contractId,
                            'error_msg' => '数据已被其他进程修改，重试第' . ($retryCount + 1) . '次失败'
                        ];
                        $retryCount++;
                        continue;
                    }

                    $updateData = ['effective_status' => $newStatus];
                    if ($latestModel->i_update($updateData) !== false) {
                        $updateSuccess = true;
                        $updateCount++;
                        // 更新本地模型状态，避免影响后续逻辑
                        $contract['effective_status'] = $newStatus;
                    } else {
                        $errorList[] = [
                            'contract_id' => $contractId,
                            'error_msg' => '更新失败：' . get_data_object_error_msg($latestModel)
                        ];
                        $retryCount++;
                        usleep(100000); // 100ms延迟后重试
                    }
                }

                if (!$updateSuccess) {
                    $errorList[] = [
                        'contract_id' => $contractId,
                        'error_msg' => '达到最大重试次数（' . $maxRetries . '次），更新失败'
                    ];
                }
            }
            
            $log .= '状态更新成功数量: ' . $updateCount . PHP_EOL;
            $log .= '其中：失效(' . $expiredCount . '), 生效中(' . $activeCount . '), 待生效(' . $pendingCount . ')' . PHP_EOL;
            
            if (!empty($errorList)) {
                $log .= '状态更新失败的合同数量: ' . count($errorList) . PHP_EOL;
                $log .= '失败详情: ' . json_encode($errorList, JSON_UNESCAPED_UNICODE) . PHP_EOL;
            }
            
        } catch (ValidationException $e) {
            $log .= '提醒: ' . $e->getMessage() . PHP_EOL;
        } catch (BusinessException $e) {
            $logger_type = 'notice';
            $log .= '注意: ' . $e->getMessage() . PHP_EOL;
        } catch (Exception $e) {
            $logger_type = 'error';
            $log .= '异常: ' . $e->getMessage() . PHP_EOL;
        }
        
        $log .= '结束时间: ' . date('Y-m-d H:i:s') . PHP_EOL;
        $this->logger->$logger_type($log);
        $this->clearLock(__METHOD__);
        exit($log);
    }

}
