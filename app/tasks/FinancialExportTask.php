<?php
/**
 * 财务相关模块的异步下载任务
 */

use App\Library\ErrCode;
use App\Library\Validation\ValidationException;
use App\Library\Enums\DownloadCenterEnum;
use App\Modules\Common\Services\DownloadCenterService;
use App\Modules\Payment\Services\StoreRentingDetailService;
use App\Modules\Reimbursement\Services\BaseService;
use App\Modules\Reimbursement\Services\ListService AS ReimbursementListService;
use App\Modules\Reimbursement\Services\DetailService AS ReimbursementDetailService;
use App\Modules\OrdinaryPayment\Services\OrdinaryPaymentListService;
use App\Modules\Contract\Services\ContractElectronicService;
use App\Modules\User\Services\UserService;
use App\Modules\AgencyPayment\Services\AgencyPaymentService;
use App\Modules\Warehouse\Services\WarehouseService;
use App\Modules\Budget\Services\BudgetWithholdingService;
use App\Library\OssHelper;
use App\Modules\Warehouse\Services\RequirementService;
use App\Modules\Warehouse\Services\ThreadService;
use GuzzleHttp\Exception\GuzzleException;

class FinancialExportTask extends BaseTask
{
    /**
     * 报销管理 - 数据查询 - 导出Excel
     *
     * php app/cli.php financial_export reimbursement
     */
    public function reimbursementAction()
    {
        $process_name = str_replace('Action', '', __FUNCTION__);
        $log = '任务名称: ' . $process_name . PHP_EOL;
        $log .= '开始时间: ' . date('Y-m-d H:i:s')  . PHP_EOL;
        $log .= '初始内存: ' . memory_usage() . PHP_EOL;
        $is_exception = false;

        try {
            // 1.提取待处理的下载任务(最早入队的)
            $task_model = DownloadCenterService::getInstance()->getFirstPendingTask(DownloadCenterEnum::FINANCE_REIMBURSEMENT_DATA);
            if (empty($task_model)) {
                throw new ValidationException('没有待下载的任务', ErrCode::$VALIDATE_ERROR);
            }

            // 2.1解析参数
            $params = !empty($task_model->args_json) ? json_decode(base64_decode($task_model->args_json, true), true) : [];
            $params['language'] = $params['language'] ?? DownloadCenterEnum::DETAUL_LANGUAGE;

            $log .= '任务ID: ' . $task_model->id . PHP_EOL;
            $log .= '任务参数: ' . json_encode($params, JSON_UNESCAPED_UNICODE) . PHP_EOL;

            // 2.2设置语言
            ReimbursementListService::setLanguage($params['language']);

            // 2.3分批取数 并 生成Excel文件
            $params['pageNum'] = DownloadCenterEnum::INIT_PAGE_NUM;
            $params['pageSize'] = DownloadCenterEnum::DEFAUL_PER_PAGE_SIZE;
            $params['export_file_name'] = $task_model->file_name;

            // 操作者信息: 对接通用数据权限
            $user_info = (new UserService())->getLoginUser($task_model->staff_info_id);

            // 兼容报销审核 和 数据查询的导出
            $data_type = !empty($params['data_list_type']) ? $params['data_list_type'] : BaseService::LIST_TYPE_QUERY_EXPORT;
            $excel_result = ReimbursementListService::getInstance()->export($params, $user_info, true, $data_type);
            $log .= 'Excel结果: ' . json_encode($excel_result, JSON_UNESCAPED_UNICODE) . PHP_EOL;

            // 3.更新任务
            if (isset($excel_result['code']) && $excel_result['code'] == ErrCode::$SUCCESS && !empty($excel_result['data'])) {
                $save_model_result = DownloadCenterService::getInstance()->saveTask($task_model, DownloadCenterEnum::TASK_STATUS_SUCCESS, $excel_result['data']);
                if ($save_model_result === false) {
                    $log .= '任务状态: 更新失败, 原因可能是: ' . get_data_object_error_msg($task_model) . PHP_EOL;

                } else {
                    $log .= '任务状态: 更新成功' . PHP_EOL;
                }

            } else {
                // 记录错误日志
                $log .= '任务状态: 未更新, 生成Excel失败, 待下次重新执行' . PHP_EOL;
            }

        } catch (ValidationException $e) {
            $log .= $e->getMessage() . PHP_EOL;

        } catch (Exception $e) {
            $log .= $e->getMessage() . PHP_EOL;
            $is_exception = true;
        }

        $log .= '当前内存: ' . memory_usage() . PHP_EOL;
        $log .= '结束时间: ' . date('Y-m-d H:i:s') . PHP_EOL;

        if ($is_exception) {
            $this->logger->error('报销管理-数据查询-导出Excel任务: ' . $log);
        } else {
            $this->logger->info('报销管理-数据查询-导出Excel任务: ' . $log);
        }

        exit($log);
    }

    /**
     * 报销管理-单据详情-报销实质关联的支援单详情-导出
     *
     * php app/cli.php financial_export reimbursement_detail_support_v2
     */
    public function reimbursement_detail_support_v2Action()
    {
        $process_name = str_replace('Action', '', __FUNCTION__);
        $log = '任务名称: ' . $process_name . PHP_EOL;
        $log .= '开始时间: ' . date('Y-m-d H:i:s')  . PHP_EOL;
        $log .= '初始内存: ' . memory_usage() . PHP_EOL;
        $is_exception = false;

        try {
            // 1.提取待处理的下载任务(最早入队的)
            $task_model = DownloadCenterService::getInstance()->getFirstPendingTask(DownloadCenterEnum::FINANCE_REIMBURSEMENT_DETAIL_STORE_SUPPORT_V2);
            if (empty($task_model)) {
                throw new ValidationException('没有待下载的任务', ErrCode::$VALIDATE_ERROR);
            }

            // 2.1解析参数
            $params = !empty($task_model->args_json) ? json_decode(base64_decode($task_model->args_json, true), true) : [];
            $params['language'] = $params['language'] ?? DownloadCenterEnum::DETAUL_LANGUAGE;

            $log .= '任务ID: ' . $task_model->id . PHP_EOL;
            $log .= '任务参数: ' . json_encode($params, JSON_UNESCAPED_UNICODE) . PHP_EOL;

            // 2.2设置语言
            ReimbursementDetailService::setLanguage($params['language']);

            // 2.3 取数 并 生成Excel文件
            $params['pageNum'] = DownloadCenterEnum::INIT_PAGE_NUM;
            $params['pageSize'] = DownloadCenterEnum::DEFAUL_PER_PAGE_SIZE * 10;
            $params['export_file_name'] = $task_model->file_name;

            $excel_result = ReimbursementDetailService::getInstance()->exportDetailStoreSupportListV2($params);
            $log .= 'Excel结果: ' . ($excel_result['data'] ?? 'null') . PHP_EOL;

            // 3.更新任务
            if (isset($excel_result['code']) && $excel_result['code'] == ErrCode::$SUCCESS && !empty($excel_result['data'])) {
                $save_model_result = DownloadCenterService::getInstance()->saveTask($task_model, DownloadCenterEnum::TASK_STATUS_SUCCESS, $excel_result['data']);
                if ($save_model_result === false) {
                    $log .= '任务状态: 更新失败, 原因可能是: ' . get_data_object_error_msg($task_model) . PHP_EOL;

                } else {
                    $log .= '任务状态: 更新成功' . PHP_EOL;
                }

            } else {
                // 记录错误日志
                $log .= '任务状态: 未更新, 生成Excel失败, 待下次重新执行' . PHP_EOL;
            }

        } catch (ValidationException $e) {
            $log .= $e->getMessage() . PHP_EOL;

        } catch (Exception $e) {
            $log .= $e->getMessage() . PHP_EOL;
            $is_exception = true;
        }

        $log .= '当前内存: ' . memory_usage() . PHP_EOL;
        $log .= '结束时间: ' . date('Y-m-d H:i:s') . PHP_EOL;

        if ($is_exception) {
            $this->logger->error('报销管理-单据详情-报销实质关联的支援单详情-导出-导出Excel任务: ' . $log);
        } else {
            $this->logger->info('报销管理-单据详情-报销实质关联的支援单详情-导出-导出Excel任务: ' . $log);
        }

        exit($log);
    }


    /**
     * 普通付款 - 数据查询 - 导出
     *
     * php app/cli.php financial_export ordinary_payment
     */
    public function ordinary_paymentAction()
    {
        $process_name = str_replace('Action', '', __FUNCTION__);
        $log = '任务名称: ' . $process_name . PHP_EOL;
        $log .= '开始时间: ' . date('Y-m-d H:i:s')  . PHP_EOL;
        $log .= '初始内存: ' . memory_usage() . PHP_EOL;
        $is_exception = false;

        try {
            // 1.提取待处理的下载任务(最早入队的)
            $task_model = DownloadCenterService::getInstance()->getFirstPendingTask(DownloadCenterEnum::FINANCE_ORDINARY_PAYMENT_DATA);
            if (empty($task_model)) {
                throw new ValidationException('没有待下载的任务', ErrCode::$VALIDATE_ERROR);
            }

            // 2.1解析参数
            $params = !empty($task_model->args_json) ? json_decode(base64_decode($task_model->args_json, true), true) : [];
            $params['language'] = $params['language'] ?? DownloadCenterEnum::DETAUL_LANGUAGE;

            $log .= '任务ID: ' . $task_model->id . PHP_EOL;
            $log .= '任务参数: ' . json_encode($params, JSON_UNESCAPED_UNICODE) . PHP_EOL;

            // 2.2设置系统语言
            OrdinaryPaymentListService::setLanguage($params['language']);

            // 操作者信息: 对接通用数据权限
            $user_info = (new UserService())->getLoginUser($task_model->staff_info_id);

            // 2.3获取数据总量
            $all_total = OrdinaryPaymentListService::getInstance()->getDataExportTotal($params, $user_info);
            $total_page_num = ceil($all_total / DownloadCenterEnum::DEFAUL_PER_PAGE_SIZE);
            $log .= '预计总量: ' . $all_total . PHP_EOL;
            $log .= '预计批次: ' . $total_page_num . PHP_EOL;

            // 2.4分批取数
            $excel_data = [];
            $page_num = DownloadCenterEnum::INIT_PAGE_NUM;
            for ($page_num; $page_num <= $total_page_num; $page_num++) {
                $log .= '----- 取数批次: ' . $page_num . '-----' . PHP_EOL;

                $params['pageNum'] = $page_num;
                $params['pageSize'] = DownloadCenterEnum::DEFAUL_PER_PAGE_SIZE;
                $list = OrdinaryPaymentListService::getInstance()->getExportData($params, $user_info);
                $log .= '本批数量: ' . count($list) . PHP_EOL;

                // 2.5合并数据
                $excel_data = array_merge($excel_data, $list);

                unset($list);

                $log .= '当前内存: ' . memory_usage() . PHP_EOL;
            }

            $log .= '实际总量: ' . count($excel_data) . PHP_EOL;
            $log .= '当前内存: ' . memory_usage() . PHP_EOL;

            // 2.6获取Excel表头
            $excel_header = OrdinaryPaymentListService::getInstance()->getExportExcelHeaderFields();

            // 2.7生成Excel
            $excel_result = OrdinaryPaymentListService::getInstance()->exportExcel($excel_header, $excel_data, $task_model->file_name);
            $log .= 'Excel结果: ' . json_encode($excel_result, JSON_UNESCAPED_UNICODE) . PHP_EOL;
            unset($excel_data);

            // 3.更新任务
            if (isset($excel_result['code']) && $excel_result['code'] == ErrCode::$SUCCESS && !empty($excel_result['data'])) {
                $save_model_result = DownloadCenterService::getInstance()->saveTask($task_model, DownloadCenterEnum::TASK_STATUS_SUCCESS, $excel_result['data']);
                if ($save_model_result === false) {
                    $log .= '任务状态: 更新失败, 原因可能是: ' . get_data_object_error_msg($task_model) . PHP_EOL;

                } else {
                    $log .= '任务状态: 更新成功' . PHP_EOL;
                }

            } else {
                // 记录错误日志
                $log .= '任务状态: 未更新, 生成Excel失败, 待下次重新执行' . PHP_EOL;
            }

        } catch (ValidationException $e) {
            $log .= $e->getMessage() . PHP_EOL;

        } catch (Exception $e) {
            $log .= $e->getMessage() . PHP_EOL;
            $is_exception = true;
        }

        $log .= '当前内存: ' . memory_usage() . PHP_EOL;
        $log .= '结束时间: ' . date('Y-m-d H:i:s') . PHP_EOL;

        if ($is_exception) {
            $this->logger->error('普通付款-数据查询-导出任务: ' . $log);
        } else {
            $this->logger->info('普通付款-数据查询-导出任务: ' . $log);
        }

        exit($log);
    }

    /**
     * 合同管理 - 电子合同制作/数据查询 - 导出
     *
     * php app/cli.php financial_export contract_electronic
     */
    public function contract_electronicAction()
    {
        $process_name = str_replace('Action', '', __FUNCTION__);
        $log = '任务名称: ' . $process_name . PHP_EOL;
        $log .= '开始时间: ' . date('Y-m-d H:i:s')  . PHP_EOL;
        $log .= '初始内存: ' . memory_usage() . PHP_EOL;
        $is_exception = false;

        try {
            // 1.提取待处理的下载任务(最早入队的)
            $task = DownloadCenterService::getInstance()->getMorePendingTask([DownloadCenterEnum::CONTRACT_ELECTRONIC_EXPORT, DownloadCenterEnum::CONTRACT_ELECTRONIC_EXPORT_ALL]);
            if (empty($task->toArray())) {
                throw new ValidationException('没有待下载的任务', ErrCode::$VALIDATE_ERROR);
            }
            foreach ($task as $task_model) {
                // 2.1解析参数
                $params = !empty($task_model->args_json) ? json_decode(base64_decode($task_model->args_json, true), true) : [];
                $params['language'] = $params['language'] ?? DownloadCenterEnum::DETAUL_LANGUAGE;

                $log .= '任务ID: ' . $task_model->id . PHP_EOL;
                $log .= '任务参数: ' . json_encode($params, JSON_UNESCAPED_UNICODE) . PHP_EOL;

                // 2.2设置系统语言
                ContractElectronicService::setLanguage($params['language']);

                // 操作者信息: 对接通用数据权限
                $user_info = (new UserService())->getLoginUser($task_model->staff_info_id);

                // 2.3获取数据总量
                $list_type = $task_model->type == DownloadCenterEnum::CONTRACT_ELECTRONIC_EXPORT ? ContractElectronicService::LIST_TYPE_APPLY : ContractElectronicService::LIST_TYPE_SEARCH;
                $all_total = ContractElectronicService::getInstance()->getTotal($params, $user_info, $list_type);
                $total_page_num = ceil($all_total / DownloadCenterEnum::DEFAUL_PER_PAGE_SIZE);
                $log .= '预计总量: ' . $all_total . PHP_EOL;
                $log .= '预计批次: ' . $total_page_num . PHP_EOL;

                // 2.4分批取数
                $excel_data = [];
                $page_num = DownloadCenterEnum::INIT_PAGE_NUM;
                for ($page_num; $page_num <= $total_page_num; $page_num++) {
                    $log .= '----- 取数批次: ' . $page_num . '-----' . PHP_EOL;

                    $params['pageNum'] = $page_num;
                    $params['pageSize'] = DownloadCenterEnum::DEFAUL_PER_PAGE_SIZE;
                    $list = ContractElectronicService::getInstance()->getList($params, $user_info, $list_type, true);
                    $log .= '本批数量: ' . count($list['items']) . PHP_EOL;

                    // 2.5合并数据
                    $excel_data = array_merge($excel_data, $list['items']);

                    unset($list);

                    $log .= '当前内存: ' . memory_usage() . PHP_EOL;
                }

                $log .= '实际总量: ' . count($excel_data) . PHP_EOL;
                $log .= '当前内存: ' . memory_usage() . PHP_EOL;

                // 2.6获取Excel表头
                $excel_data = ContractElectronicService::getInstance()->getExportExcelHeaderFields($list_type, $excel_data);

                // 2.7生成Excel
                $excel_result = ContractElectronicService::getInstance()->exportExcel($excel_data['header'], $excel_data['row_values'], $task_model->file_name);
                $log .= 'Excel结果: ' . json_encode($excel_result, JSON_UNESCAPED_UNICODE) . PHP_EOL;
                unset($excel_data);

                // 3.更新任务
                if (isset($excel_result['code']) && $excel_result['code'] == ErrCode::$SUCCESS && !empty($excel_result['data'])) {
                    $save_model_result = DownloadCenterService::getInstance()->saveTask($task_model, DownloadCenterEnum::TASK_STATUS_SUCCESS, $excel_result['data']);
                    if ($save_model_result === false) {
                        $log .= '任务状态: 更新失败, 原因可能是: ' . get_data_object_error_msg($task_model) . PHP_EOL;

                    } else {
                        $log .= '任务状态: 更新成功' . PHP_EOL;
                    }

                } else {
                    // 记录错误日志
                    $log .= '任务状态: 未更新, 生成Excel失败, 待下次重新执行' . PHP_EOL;
                }
            }
        } catch (ValidationException $e) {
            $log .= $e->getMessage() . PHP_EOL;

        } catch (Exception $e) {
            $log .= $e->getMessage() . PHP_EOL;
            $is_exception = true;
        }

        $log .= '当前内存: ' . memory_usage() . PHP_EOL;
        $log .= '结束时间: ' . date('Y-m-d H:i:s') . PHP_EOL;

        if ($is_exception) {
            $this->logger->error('合同管理-电子合同制作/数据查询-电子合同导出任务: ' . $log);
        } else {
            $this->logger->info('合同管理-电子合同制作/数据查询-电子合同导出任务: ' . $log);
        }

        exit($log);
    }

    /**
     * 合同管理-数据查询-其他合同-审批数据查询-列表导出
     *
     * php app/cli.php financial_export contractOtherExport
     */
    public function contractOtherExportAction()
    {
        $process_name = str_replace('Action', '', __FUNCTION__);
        $log = '任务名称: ' . $process_name . PHP_EOL;
        $log .= '开始时间: ' . date('Y-m-d H:i:s')  . PHP_EOL;
        $log .= '初始内存: ' . memory_usage() . PHP_EOL;
        $is_exception = false;

        try {
            // 1.提取待处理的下载任务(最早入队的)
            $task = DownloadCenterService::getInstance()->getMorePendingTask([DownloadCenterEnum::CONTRACT_OTHER_EXPORT]);
            if (empty($task->toArray())) {
                throw new ValidationException('没有待下载的任务', ErrCode::$VALIDATE_ERROR);
            }
            foreach ($task as $task_model) {
                // 2.1解析参数
                $params = !empty($task_model->args_json) ? json_decode(base64_decode($task_model->args_json, true), true) : [];
                $params['language'] = $params['language'] ?? DownloadCenterEnum::DETAUL_LANGUAGE;

                $log .= '任务ID: ' . $task_model->id . PHP_EOL;
                $log .= '任务参数: ' . json_encode($params, JSON_UNESCAPED_UNICODE) . PHP_EOL;

                // 2.2设置系统语言
                ContractElectronicService::setLanguage($params['language']);

                // 操作者信息: 对接通用数据权限
                $user_info = (new UserService())->getLoginUser($task_model->staff_info_id);

                $result = \App\Modules\Contract\Services\ListService::getInstance()->getDownloadList($params, $user_info);

                // 3.更新任务
                if (!empty($result['data'])) {
                    $save_model_result = DownloadCenterService::getInstance()->saveTask($task_model, DownloadCenterEnum::TASK_STATUS_SUCCESS, $result['data']);
                    if ($save_model_result === false) {
                        $log .= '任务状态: 更新失败, 原因可能是: ' . get_data_object_error_msg($task_model) . PHP_EOL;

                    } else {
                        $log .= '任务状态: 更新成功' . PHP_EOL;
                    }

                } else {
                    // 记录错误日志
                    $log .= '任务状态: 未更新, 生成Excel失败, 待下次重新执行' . PHP_EOL;
                }
            }
        } catch (ValidationException $e) {
            $log .= $e->getMessage() . PHP_EOL;

        } catch (Exception $e) {
            $log .= $e->getMessage() . PHP_EOL;
            $is_exception = true;
        }

        $log .= '当前内存: ' . memory_usage() . PHP_EOL;
        $log .= '结束时间: ' . date('Y-m-d H:i:s') . PHP_EOL;

        if ($is_exception) {
            $this->logger->error('合同管理-数据查询-其他合同-审批数据查询-列表导出: ' . $log);
        } else {
            $this->logger->info('合同管理-数据查询-其他合同-审批数据查询-列表导出: ' . $log);
        }

        exit($log);
    }

    /**
     * 代理支付 - 导出
     *
     * php app/cli.php financial_export agency_payment
     */
    public function agency_paymentAction()
    {
        $process_name = str_replace('Action', '', __FUNCTION__);
        $log = '任务名称: ' . $process_name . PHP_EOL;
        $log .= '开始时间: ' . date('Y-m-d H:i:s')  . PHP_EOL;
        $log .= '初始内存: ' . memory_usage() . PHP_EOL;
        $is_exception = false;

        try {
            // 1.提取待处理的下载任务(最早入队的)
            $export_type = [
                DownloadCenterEnum::AGENCY_PAYMENT_APPLY,
                DownloadCenterEnum::AGENCY_PAYMENT_APPLY_DETAIL_EXPORT,
                DownloadCenterEnum::AGENCY_PAYMENT_AUDIT,
                DownloadCenterEnum::AGENCY_PAYMENT_AUDIT_DETAIL_EXPORT,
                DownloadCenterEnum::AGENCY_PAYMENT_PAY,
                DownloadCenterEnum::AGENCY_PAYMENT_PAY_DETAIL_EXPORT,
                DownloadCenterEnum::AGENCY_PAYMENT_DATA,
                DownloadCenterEnum::AGENCY_PAYMENT_DATA_DETAIL_EXPORT,
            ];
            $task = DownloadCenterService::getInstance()->getMorePendingTask($export_type);
            if (empty($task->toArray())) {
                throw new ValidationException('没有待下载的任务', ErrCode::$VALIDATE_ERROR);
            }
            foreach ($task as $task_model) {
                // 2.1解析参数
                $params = !empty($task_model->args_json) ? json_decode(base64_decode($task_model->args_json, true), true) : [];
                $params['language'] = $params['language'] ?? DownloadCenterEnum::DETAUL_LANGUAGE;

                $log .= '任务ID: ' . $task_model->id . PHP_EOL;
                $log .= '任务参数: ' . json_encode($params, JSON_UNESCAPED_UNICODE) . PHP_EOL;

                // 2.2设置系统语言
                AgencyPaymentService::setLanguage($params['language']);

                // 操作者信息: 对接通用数据权限
                $user_info = (new UserService())->getLoginUser($task_model->staff_info_id);

                // 2.3获取数据总量
                $list_type = AgencyPaymentService::$list_type[$task_model->type];
                $all_total = AgencyPaymentService::getInstance()->getDataExportTotal($params, $user_info, $list_type);
                $total_page_num = ceil($all_total / DownloadCenterEnum::DEFAUL_PER_PAGE_SIZE);
                $log .= '预计总量: ' . $all_total . PHP_EOL;
                $log .= '预计批次: ' . $total_page_num . PHP_EOL;

                // 2.4分批取数
                $excel_data = [];
                $page_num = DownloadCenterEnum::INIT_PAGE_NUM;
                for ($page_num; $page_num <= $total_page_num; $page_num++) {
                    $log .= '----- 取数批次: ' . $page_num . '-----' . PHP_EOL;

                    $params['pageNum'] = $page_num;
                    $params['pageSize'] = DownloadCenterEnum::DEFAUL_PER_PAGE_SIZE;
                    $list = AgencyPaymentService::getInstance()->getExportData($params, $user_info, $list_type);
                    $log .= '本批数量: ' . count($list) . PHP_EOL;

                    // 2.5合并数据
                    $excel_data = array_merge($excel_data, $list);

                    unset($list);

                    $log .= '当前内存: ' . memory_usage() . PHP_EOL;
                }

                $log .= '实际总量: ' . count($excel_data) . PHP_EOL;
                $log .= '当前内存: ' . memory_usage() . PHP_EOL;

                // 2.6获取Excel表头
                $excel_title = AgencyPaymentService::getInstance()->getExportExcelHeaderFields();

                // 2.7生成Excel
                $excel_result = AgencyPaymentService::getInstance()->exportExcel($excel_title, $excel_data, $task_model->file_name);
                $log .= 'Excel结果: ' . json_encode($excel_result, JSON_UNESCAPED_UNICODE) . PHP_EOL;
                unset($excel_data);

                // 3.更新任务
                if (isset($excel_result['code']) && $excel_result['code'] == ErrCode::$SUCCESS && !empty($excel_result['data'])) {
                    $save_model_result = DownloadCenterService::getInstance()->saveTask($task_model, DownloadCenterEnum::TASK_STATUS_SUCCESS, $excel_result['data']);
                    if ($save_model_result === false) {
                        $log .= '任务状态: 更新失败, 原因可能是: ' . get_data_object_error_msg($task_model) . PHP_EOL;

                    } else {
                        $log .= '任务状态: 更新成功' . PHP_EOL;
                    }

                } else {
                    // 记录错误日志
                    $log .= '任务状态: 未更新, 生成Excel失败, 待下次重新执行' . PHP_EOL;
                }
            }
        } catch (ValidationException $e) {
            $log .= $e->getMessage() . PHP_EOL;

        } catch (Exception $e) {
            $log .= $e->getMessage() . PHP_EOL;
            $is_exception = true;
        }

        $log .= '当前内存: ' . memory_usage() . PHP_EOL;
        $log .= '结束时间: ' . date('Y-m-d H:i:s') . PHP_EOL;

        if ($is_exception) {
            $this->logger->error('代理支付-我的申请/我的审核/代理支付/数据查询-导出任务: ' . $log);
        } else {
            $this->logger->info('代理支付-我的申请/我的审核/代理支付/数据查询-导出任务: ' . $log);
        }

        exit($log);
    }

    /**
     * 仓库管理 - 仓库信息 - 导出
     *
     * php app/cli.php financial_export warehouse_info
     */
    public function warehouse_infoAction()
    {
        $process_name = str_replace('Action', '', __FUNCTION__);
        $log = '任务名称: ' . $process_name . PHP_EOL;
        $log .= '开始时间: ' . date('Y-m-d H:i:s')  . PHP_EOL;
        $log .= '初始内存: ' . memory_usage() . PHP_EOL;
        $is_exception = false;

        try {
            // 1.提取待处理的下载任务(最早入队的)
            $task_model = DownloadCenterService::getInstance()->getFirstPendingTask(DownloadCenterEnum::FINANCE_WAREHOUSE_MANAGEMENT_INFO);
            if (empty($task_model)) {
                throw new ValidationException('没有待下载的任务', ErrCode::$VALIDATE_ERROR);
            }

            // 2.1解析参数
            $params = !empty($task_model->args_json) ? json_decode(base64_decode($task_model->args_json, true), true) : [];
            $params['language'] = $params['language'] ?? DownloadCenterEnum::DETAUL_LANGUAGE;

            $log .= '任务ID: ' . $task_model->id . PHP_EOL;
            $log .= '任务参数: ' . json_encode($params, JSON_UNESCAPED_UNICODE) . PHP_EOL;

            // 2.2设置系统语言
            static::setLanguage($params['language']);

            // 2.3获取数据总量
            $all_total = WarehouseService::getInstance()->infoExportCount($params);
            $total_page_num = ceil($all_total / DownloadCenterEnum::DEFAUL_PER_PAGE_SIZE);
            $log .= '预计总量: ' . $all_total . PHP_EOL;
            $log .= '预计批次: ' . $total_page_num . PHP_EOL;

            // 2.4分批取数
            $excel_data = [];
            $page_num = DownloadCenterEnum::INIT_PAGE_NUM;
            for ($page_num; $page_num <= $total_page_num; $page_num++) {
                $log .= '----- 取数批次: ' . $page_num . '-----' . PHP_EOL;

                $params['pageNum'] = $page_num;
                $params['pageSize'] = DownloadCenterEnum::DEFAUL_PER_PAGE_SIZE;
                $list = WarehouseService::getInstance()->getInfoExportData($params);
                $log .= '本批数量: ' . count($list) . PHP_EOL;

                // 2.5合并数据
                $excel_data = array_merge($excel_data, $list);

                unset($list);

                $log .= '当前内存: ' . memory_usage() . PHP_EOL;
            }

            $log .= '实际总量: ' . count($excel_data) . PHP_EOL;
            $log .= '当前内存: ' . memory_usage() . PHP_EOL;

            // 2.7生成Excel
            $excel_result = WarehouseService::getInstance()->generateInfoExportFile($excel_data);
            $log .= "Excel结果: {$excel_result['data']}" . PHP_EOL;
            unset($excel_data);

            // 3.更新任务
            if (isset($excel_result['code']) && $excel_result['code'] == ErrCode::$SUCCESS && !empty($excel_result['data'])) {
                $save_model_result = DownloadCenterService::getInstance()->saveTask($task_model, DownloadCenterEnum::TASK_STATUS_SUCCESS, $excel_result['data']);
                if ($save_model_result === false) {
                    $log .= '任务状态: 更新失败, 原因可能是: ' . get_data_object_error_msg($task_model) . PHP_EOL;

                } else {
                    $log .= '任务状态: 更新成功' . PHP_EOL;
                }

            } else {
                // 记录错误日志
                $log .= '任务状态: 未更新, 生成Excel失败, 待下次重新执行' . PHP_EOL;
            }

        } catch (ValidationException $e) {
            $log .= $e->getMessage() . PHP_EOL;

        } catch (Exception $e) {
            $log .= $e->getMessage() . PHP_EOL;
            $is_exception = true;
        }

        $log .= '当前内存: ' . memory_usage() . PHP_EOL;
        $log .= '结束时间: ' . date('Y-m-d H:i:s') . PHP_EOL;

        if ($is_exception) {
            $this->logger->error('仓库管理-仓库信息-导出任务: ' . $log);
        } else {
            $this->logger->info('仓库管理-仓库信息-导出任务: ' . $log);
        }

        exit($log);
    }


    /**
     * 网点租房付款 - 数据查询-批量下载
     *
     * php app/cli.php financial_export payment_store_renting_batch_download
     */
    public function payment_store_renting_batch_downloadAction()
    {
        $process_name = str_replace('Action', '', __FUNCTION__);
        $log = '任务名称: ' . $process_name . PHP_EOL;
        $log .= '开始时间: ' . date('Y-m-d H:i:s')  . PHP_EOL;
        $log .= '初始内存: ' . memory_usage() . PHP_EOL;
        $is_exception = false;

        try {
            // 1.提取待处理的下载任务(最早入队的)
            $task_model = DownloadCenterService::getInstance()->getFirstPendingTask(DownloadCenterEnum::PAYMENT_STORE_RENTING_BATCH_DOWNLOAD);
            if (empty($task_model)) {
                throw new ValidationException('没有待下载的任务', ErrCode::$VALIDATE_ERROR);
            }

            // 2.1解析参数
            $params = !empty($task_model->args_json) ? json_decode(base64_decode($task_model->args_json, true), true) : [];
            $params['language'] = $params['language'] ?? DownloadCenterEnum::DETAUL_LANGUAGE;

            $log .= '任务ID: ' . $task_model->id . PHP_EOL;
            $log .= '任务参数: ' . json_encode($params, JSON_UNESCAPED_UNICODE) . PHP_EOL;

            // 2.2设置系统语言
            static::setLanguage($params['language']);


            // 2.3生成zip
            $tmp_dir = sys_get_temp_dir() . '/';
            $file_name = DownloadCenterEnum::$download_center_excel_setting_item[DownloadCenterEnum::PAYMENT_STORE_RENTING_BATCH_DOWNLOAD]['file_name'];
            $file_name = str_replace('{YmdHis}', date('YmdHis'), $file_name);
            $zip_file_path = $tmp_dir . $file_name;
            $zip = new ZipArchive();
            if ($zip->open($zip_file_path, ZipArchive::CREATE) !== TRUE) {
                throw new ValidationException("无法打开 <$file_name>\n", ErrCode::$VALIDATE_ERROR);
            }

            // 2.4分单据下载pdf
            $payment_store_renting_dir = $tmp_dir . 'payment_store_renting_' . date('Y-m-d');
            if (!is_dir($payment_store_renting_dir)) {
                mkdir($payment_store_renting_dir, 0755);
            }
            $has_file = false;
            foreach ($params['ids'] as $id) {
                $res = StoreRentingDetailService::getInstance()->download($id, $task_model->staff_info_id,false, true);
                if (isset($res['code']) && $res['code'] == ErrCode::$SUCCESS) {
                    $has_file = true;
                    //解析阿里云 & 放置本地
                    $file_url = $res['data']['file_url'];
                    $oss_info  = get_oss_info_by_url($file_url);
                    $file_name = $oss_info['file_name'];
                    ob_start();
                    readfile($file_url);
                    $file_data = ob_get_contents();
                    ob_end_clean();
                    $tmp_path = $payment_store_renting_dir . '/' . $file_name;
                    file_put_contents($tmp_path, $file_data);

                    //将文件放入压缩包
                    if ($zip->addFile($tmp_path, $res['data']['file_name']) === FALSE) {
                        continue;
                    }
                } else {
                    $log .= '单据id：' . $id . '无法生成pdf，原因是：' . $res['message'] . PHP_EOL;
                }
                $log .= '当前内存: ' . memory_usage() . PHP_EOL;
            }
            $zip->close();

            $log .= '当前内存: ' . memory_usage() . PHP_EOL;
            $file_download_path = '';
            if ($has_file) {
                //将压缩包上传阿里云
                $upload_result = OssHelper::uploadFile($zip_file_path);
                $file_download_path = $upload_result['object_url'];
                $log .= 'Zip结果: ' . json_encode($upload_result, JSON_UNESCAPED_UNICODE)  . PHP_EOL;

                //普通付款-pdf文件-从本地删除
                $pdf_file = scandir($payment_store_renting_dir);
                foreach ($pdf_file as $file) {
                    if ($file == '.' || $file == '..') {
                        continue;
                    }
                    unlink($payment_store_renting_dir . '/' . $file);
                }
                unlink($zip_file_path);
            }
            rmdir($payment_store_renting_dir);

            // 3.更新任务
            $save_model_result = DownloadCenterService::getInstance()->saveTask($task_model, DownloadCenterEnum::TASK_STATUS_SUCCESS, $file_download_path);
            if ($save_model_result === false) {
                $log .= '任务状态: 更新失败, 原因可能是: ' . get_data_object_error_msg($task_model) . PHP_EOL;

            } else {
                $log .= '任务状态: 更新成功' . PHP_EOL;
            }
        } catch (ValidationException $e) {
            $log .= $e->getMessage() . PHP_EOL;

        } catch (Exception $e) {
            $log .= $e->getMessage() . PHP_EOL;
            $is_exception = true;
        }

        $log .= '当前内存: ' . memory_usage() . PHP_EOL;
        $log .= '结束时间: ' . date('Y-m-d H:i:s') . PHP_EOL;

        if ($is_exception) {
            $this->logger->error('网点租房付款 - 数据查询-批量下载任务: ' . $log);
        } else {
            $this->logger->info('网点租房付款 - 数据查询-批量下载任务: ' . $log);
        }

        exit($log);
    }

    /**
     * 仓库管理 - 仓库需求管理 - 导出
     *
     * php app/cli.php financial_export warehouse_requirement
     * @param array $params 参数组
     * @throws GuzzleException
     */
    public function warehouse_requirementAction($params)
    {
        $process_name = str_replace('Action', '', __FUNCTION__);
        $log          = '任务名称: ' . $process_name . PHP_EOL;
        $log          .= '开始时间: ' . date('Y-m-d H:i:s') . PHP_EOL;
        $log          .= '初始内存: ' . memory_usage() . PHP_EOL;
        $is_exception = false;

        try {
            $task_type_index = $params[0] ?? RequirementService::REQUIREMENT_LIST;//导出索引
            $task_type = RequirementService::$task_type[$task_type_index] ?? DownloadCenterEnum::FINANCE_WAREHOUSE_REQUIREMENT_EXPORT;
            // 1.提取待处理的下载任务(最早入队的)
            $task_model = DownloadCenterService::getInstance()->getFirstPendingTask($task_type);
            if (empty($task_model)) {
                throw new ValidationException('没有待下载的任务', ErrCode::$VALIDATE_ERROR);
            }

            // 2.1解析参数
            $params             = !empty($task_model->args_json) ? json_decode(base64_decode($task_model->args_json, true), true) : [];
            $params['language'] = $params['language'] ?? DownloadCenterEnum::DETAUL_LANGUAGE;

            $log .= '任务ID: ' . $task_model->id . PHP_EOL;
            $log .= '任务参数: ' . json_encode($params, JSON_UNESCAPED_UNICODE) . PHP_EOL;

            // 2.2设置系统语言
            static::setLanguage($params['language']);

            // 2.3获取数据总量
            $all_total      = RequirementService::getInstance()->getDownloadDataTotal($params, $task_type_index);
            $total_page_num = ceil($all_total / DownloadCenterEnum::DEFAUL_PER_PAGE_SIZE);
            $log            .= '预计总量: ' . $all_total . PHP_EOL;
            $log            .= '预计批次: ' . $total_page_num . PHP_EOL;

            // 2.4分批取数
            $excel_data = [];
            $page_num   = DownloadCenterEnum::INIT_PAGE_NUM;
            for ($page_num; $page_num <= $total_page_num; $page_num++) {
                $log .= '----- 取数批次: ' . $page_num . '-----' . PHP_EOL;

                $params['pageNum']  = $page_num;
                $params['pageSize'] = DownloadCenterEnum::DEFAUL_PER_PAGE_SIZE;
                $list               = RequirementService::getInstance()->export($params, $task_type_index);
                $log                .= '本批数量: ' . count($list) . PHP_EOL;

                // 2.5合并数据
                $excel_data = array_merge($excel_data, $list);

                unset($list);

                $log .= '当前内存: ' . memory_usage() . PHP_EOL;
            }

            $log .= '实际总量: ' . count($excel_data) . PHP_EOL;
            $log .= '当前内存: ' . memory_usage() . PHP_EOL;

            // 2.7生成Excel
            $excel_result = RequirementService::getInstance()->generateInfoExportFile($excel_data, $task_type);
            $log          .= "Excel结果: {$excel_result['data']}" . PHP_EOL;
            unset($excel_data);

            // 3.更新任务
            if (isset($excel_result['code']) && $excel_result['code'] == ErrCode::$SUCCESS && !empty($excel_result['data'])) {
                $save_model_result = DownloadCenterService::getInstance()->saveTask($task_model, DownloadCenterEnum::TASK_STATUS_SUCCESS, $excel_result['data']);
                if ($save_model_result === false) {
                    $log .= '任务状态: 更新失败, 原因可能是: ' . get_data_object_error_msg($task_model) . PHP_EOL;
                } else {
                    $log .= '任务状态: 更新成功' . PHP_EOL;
                }
            } else {
                // 记录错误日志
                $log .= '任务状态: 未更新, 生成Excel失败, 待下次重新执行' . PHP_EOL;
            }
        } catch (ValidationException $e) {
            $log .= $e->getMessage() . PHP_EOL;
        } catch (Exception $e) {
            $log          .= $e->getMessage() . PHP_EOL;
            $is_exception = true;
        }

        $log .= '当前内存: ' . memory_usage() . PHP_EOL;
        $log .= '结束时间: ' . date('Y-m-d H:i:s') . PHP_EOL;

        if ($is_exception) {
            $this->logger->error('仓库管理-仓库需求管理-导出任务: ' . $log);
        } else {
            $this->logger->info('仓库管理-仓库需求管理-导出任务: ' . $log);
        }

        exit($log);
    }

    /**
     * 仓库管理 - 仓库线索管理 - 导出
     *
     * php app/cli.php financial_export warehouse_thread
     * @param array $params 参数组
     * @throws GuzzleException
     */
    public function warehouse_threadAction($params)
    {
        $process_name = str_replace('Action', '', __FUNCTION__);
        $log          = '任务名称: ' . $process_name . PHP_EOL;
        $log          .= '开始时间: ' . date('Y-m-d H:i:s') . PHP_EOL;
        $log          .= '初始内存: ' . memory_usage() . PHP_EOL;
        $is_exception = false;

        try {
            $task_type_index = $params[0] ?? ThreadService::THREAD_LIST;//导出索引
            $task_type = ThreadService::$task_type[$task_type_index] ?? DownloadCenterEnum::FINANCE_WAREHOUSE_THREAD_EXPORT;
            // 1.提取待处理的下载任务(最早入队的)
            $task_model = DownloadCenterService::getInstance()->getFirstPendingTask($task_type);
            if (empty($task_model)) {
                throw new ValidationException('没有待下载的任务', ErrCode::$VALIDATE_ERROR);
            }

            // 2.1解析参数
            $params             = !empty($task_model->args_json) ? json_decode(base64_decode($task_model->args_json, true), true) : [];
            $params['language'] = $params['language'] ?? DownloadCenterEnum::DETAUL_LANGUAGE;

            $log .= '任务ID: ' . $task_model->id . PHP_EOL;
            $log .= '任务参数: ' . json_encode($params, JSON_UNESCAPED_UNICODE) . PHP_EOL;

            // 2.2设置系统语言
            static::setLanguage($params['language']);
            // 操作者信息: 对接通用数据权限
            $user = (new UserService())->getLoginUser($task_model->staff_info_id);

            // 2.3获取数据总量
            $all_total      = ThreadService::getInstance()->getDownloadDataTotal($params, $user, $task_type_index);
            $total_page_num = ceil($all_total / DownloadCenterEnum::DEFAUL_PER_PAGE_SIZE);
            $log            .= '预计总量: ' . $all_total . PHP_EOL;
            $log            .= '预计批次: ' . $total_page_num . PHP_EOL;

            // 2.4分批取数
            $excel_data = [];
            $page_num   = DownloadCenterEnum::INIT_PAGE_NUM;
            for ($page_num; $page_num <= $total_page_num; $page_num++) {
                $log .= '----- 取数批次: ' . $page_num . '-----' . PHP_EOL;

                $params['pageNum']  = $page_num;
                $params['pageSize'] = DownloadCenterEnum::DEFAUL_PER_PAGE_SIZE;
                $list               = ThreadService::getInstance()->export($params, $user, $task_type_index);
                $log                .= '本批数量: ' . count($list) . PHP_EOL;

                // 2.5合并数据
                $excel_data = array_merge($excel_data, $list);

                unset($list);

                $log .= '当前内存: ' . memory_usage() . PHP_EOL;
            }

            $log .= '实际总量: ' . count($excel_data) . PHP_EOL;
            $log .= '当前内存: ' . memory_usage() . PHP_EOL;

            // 2.7生成Excel
            $excel_result = ThreadService::getInstance()->generateInfoExportFile($excel_data, $task_type);
            $log          .= "Excel结果: {$excel_result['data']}" . PHP_EOL;
            unset($excel_data);

            // 3.更新任务
            if (isset($excel_result['code']) && $excel_result['code'] == ErrCode::$SUCCESS && !empty($excel_result['data'])) {
                $save_model_result = DownloadCenterService::getInstance()->saveTask($task_model, DownloadCenterEnum::TASK_STATUS_SUCCESS, $excel_result['data']);
                if ($save_model_result === false) {
                    $log .= '任务状态: 更新失败, 原因可能是: ' . get_data_object_error_msg($task_model) . PHP_EOL;
                } else {
                    $log .= '任务状态: 更新成功' . PHP_EOL;
                }
            } else {
                // 记录错误日志
                $log .= '任务状态: 未更新, 生成Excel失败, 待下次重新执行' . PHP_EOL;
            }
        } catch (ValidationException $e) {
            $log .= $e->getMessage() . PHP_EOL;
        } catch (Exception $e) {
            $log          .= $e->getMessage() . PHP_EOL;
            $is_exception = true;
        }

        $log .= '当前内存: ' . memory_usage() . PHP_EOL;
        $log .= '结束时间: ' . date('Y-m-d H:i:s') . PHP_EOL;

        if ($is_exception) {
            $this->logger->error('仓库管理-仓库线索管理-导出任务: ' . $log);
        } else {
            $this->logger->info('仓库管理-仓库线索管理-导出任务: ' . $log);
        }

        exit($log);
    }

    /**
     * 预算管理-费用预提
     * php app/cli.php financial_export budget_withholding
     */
    public function budget_withholdingAction()
    {
        $process_name = str_replace('Action', '', __FUNCTION__);
        $log          = '任务名称: ' . $process_name . PHP_EOL;
        $log          .= '开始时间: ' . date('Y-m-d H:i:s') . PHP_EOL;
        $log          .= '初始内存: ' . memory_usage() . PHP_EOL;
        $is_exception = false;

        try {
            // 1.提取待处理的下载任务(最早入队的)
            $task_model = DownloadCenterService::getInstance()->getFirstPendingTask(DownloadCenterEnum::BUDGET_WITHHOLDING_EXPORT);
            if (empty($task_model)) {
                throw new ValidationException('没有待下载的任务', ErrCode::$VALIDATE_ERROR);
            }

            // 2.1解析参数
            $params = !empty($task_model->args_json) ? json_decode(base64_decode($task_model->args_json, true), true) : [];
            $params['language'] = $params['language'] ?? DownloadCenterEnum::DETAUL_LANGUAGE;

            $log .= '任务ID: ' . $task_model->id . PHP_EOL;
            $log .= '任务参数: ' . json_encode($params, JSON_UNESCAPED_UNICODE) . PHP_EOL;

            // 2.2设置系统语言
            BudgetWithholdingService::setLanguage($params['language']);

            // 操作者信息: 对接通用数据权限
            $user_info = (new UserService())->getLoginUser($task_model->staff_info_id);

            // 2.3获取数据总量
            $all_total = BudgetWithholdingService::getInstance()->getDataExportTotal($params, $user_info);
            $total_page_num = ceil($all_total / DownloadCenterEnum::DEFAUL_PER_PAGE_SIZE);
            $log .= '预计总量: ' . $all_total . PHP_EOL;
            $log .= '预计批次: ' . $total_page_num . PHP_EOL;

            // 2.4分批取数
            $excel_data = [];
            $page_num = DownloadCenterEnum::INIT_PAGE_NUM;
            for ($page_num; $page_num <= $total_page_num; $page_num++) {
                $log .= '----- 取数批次: ' . $page_num . '-----' . PHP_EOL;

                $params['pageNum'] = $page_num;
                $params['pageSize'] = DownloadCenterEnum::DEFAUL_PER_PAGE_SIZE;
                $list = BudgetWithholdingService::getInstance()->getExportData($params, $user_info);
                $log .= '本批数量: ' . count($list) . PHP_EOL;

                // 2.5合并数据
                $excel_data = array_merge($excel_data, $list);

                unset($list);

                $log .= '当前内存: ' . memory_usage() . PHP_EOL;
            }

            $log .= '实际总量: ' . count($excel_data) . PHP_EOL;
            $log .= '当前内存: ' . memory_usage() . PHP_EOL;

            // 2.6获取Excel表头
            $excel_title = BudgetWithholdingService::getInstance()->getExportExcelHeaderFields();

            // 2.7生成Excel
            $excel_result = BudgetWithholdingService::getInstance()->exportExcel($excel_title, $excel_data, $task_model->file_name);
            $log .= 'Excel结果: ' . json_encode($excel_result, JSON_UNESCAPED_UNICODE) . PHP_EOL;
            unset($excel_data);

            // 3.更新任务
            if (isset($excel_result['code']) && $excel_result['code'] == ErrCode::$SUCCESS && !empty($excel_result['data'])) {
                $save_model_result = DownloadCenterService::getInstance()->saveTask($task_model, DownloadCenterEnum::TASK_STATUS_SUCCESS, $excel_result['data']);
                if ($save_model_result === false) {
                    $log .= '任务状态: 更新失败, 原因可能是: ' . get_data_object_error_msg($task_model) . PHP_EOL;
                } else {
                    $log .= '任务状态: 更新成功' . PHP_EOL;
                }
            } else {
                // 记录错误日志
                $log .= '任务状态: 未更新, 生成Excel失败, 待下次重新执行' . PHP_EOL;
            }
        } catch (ValidationException $e) {
            $log .= $e->getMessage() . PHP_EOL;
        } catch (Exception $e) {
            $log .= $e->getMessage() . PHP_EOL;
            $is_exception = true;
        }

        $log .= '当前内存: ' . memory_usage() . PHP_EOL;
        $log .= '结束时间: ' . date('Y-m-d H:i:s') . PHP_EOL;

        if ($is_exception) {
            $this->logger->error('预算管理-费用预提-导出任务: ' . $log);
        } else {
            $this->logger->info('预算管理-费用预提-导出任务: ' . $log);
        }

        exit($log);
    }
}
