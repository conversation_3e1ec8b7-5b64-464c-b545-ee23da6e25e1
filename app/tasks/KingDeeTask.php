<?php

/**
 * Created by PhpStorm.
 * Date: 2023/8/22
 * Time: 10:26
 */

use App\Library\ErrCode;
use App\Library\Validation\ValidationException;
use App\Modules\Common\Services\EnumsService;
use App\Modules\Third\Services\KingDeeService;
use App\Library\Enums\KingDeeEnums;
use App\Library\Enums\PayEnums;
use App\Library\Enums\GlobalEnums;
use App\Modules\Organization\Models\SysDepartmentModel;
use App\Modules\Vendor\Models\Vendor;
use App\Modules\Reimbursement\Services\ListService as ReimbursementListService;
use App\Modules\OrdinaryPayment\Services\OrdinaryPaymentListService;
use App\Modules\OrdinaryPayment\Models\OrdinaryPayment;
use App\Repository\oa\AccountingSubjectsRepository;
use App\Repository\DepartmentRepository;
use App\Models\oa\PaymentModel;
use App\Modules\Common\Models\EnvModel;
use App\Library\Enums\SysConfigEnums;
use App\Modules\Purchase\Services\StorageService;
use App\Modules\Purchase\Services\OrderService;
use App\Modules\Purchase\Services\PaymentService;
use App\Library\Enums;
use App\Modules\Purchase\Models\PurchasePayment;
use App\Repository\oa\VendorRepository;

class KingDeeTask Extends BaseTask
{

    /**
     * 同步供应商数据
     * */
    public function sync_vendor_dataAction()
    {
        $this->checkLock(__METHOD__);
        echo 'begin: ' . date('Y-m-d H:i:s') . PHP_EOL;

        try {
            //获取组织编码
            $tip_1 = '未配置指定的金蝶的组织bu 配置,请联系产品配置';
            $tip_2 = '未配置的组织代码,请联系产品配置';
            $tip_3 = '组织架构中找不到公司配置的组织代码,请联系产品配置';

            $company_id = EnumsService::getInstance()->getSettingEnvValueIds('kingdee_company_ids');
            if (empty($company_id)) {
                throw new ValidationException($tip_1, ErrCode::$VALIDATE_ERROR);
            }

            $kingdee_organization_code = EnumsService::getInstance()->getSettingEnvValue('kingdee_organization_code');
            if (empty($kingdee_organization_code)) {
                throw new ValidationException($tip_2, ErrCode::$VALIDATE_ERROR);
            }

            //获取配置的bu对应的金碟组织代码信息
            $assigned_organization_code = SysDepartmentModel::find([
                'conditions' => 'id in ({ids:array})',
                'bind'       => ['ids' => $company_id]
            ])->toArray();

            $assigned_organization_codes = [];
            foreach ($assigned_organization_code as $value) {
                if (empty($value['sap_company_id'])) {
                    $tip_4 = '组织架构中找不到公司' . $value['id'] . '配置的组织代码,请联系产品配置';
                    throw new ValidationException($tip_4, ErrCode::$VALIDATE_ERROR);
                }

                //去除默认组织编码
                if ($value['sap_company_id'] == $kingdee_organization_code) {
                    continue;
                }
                $assigned_organization_codes[] = $value['sap_company_id'];
            }

            $assigned_organization_codes = array_unique($assigned_organization_codes);
            $assigned_organization_codes = implode(',', $assigned_organization_codes);
            if (empty($assigned_organization_codes)) {
                throw new ValidationException($tip_3, ErrCode::$VALIDATE_ERROR);
            }

            $i = 0;
            $z = 0;//记录成功执行数

            $res = Vendor::find([
                'conditions' => 'is_send_kingdee = :is_send_kingdee:',
                'bind'       => ['is_send_kingdee' => KingDeeEnums::IS_SEND_KING_DEE_1],
                'limit'      => 100,
                'offset'     => $i,
            ]);

            while (!empty($res->toArray())) {
                foreach ($res as $item) {
                    $send_data = [
                        'organization_code'          => $kingdee_organization_code,
                        'vendor_id'                  => empty($item->kingdee_supplier_no) ? $item->vendor_id : $item->kingdee_supplier_no,
                        'vendor_name'                => $item->vendor_name,
                        'assigned_organization_code' => $assigned_organization_codes
                    ];
                    $response  = KingDeeService::getInstance()->syncVendorData($send_data);
                    if ($response['ResponseStatus']['IsSuccess']) {
                        $item->kingdee_supplier_no = empty($item->kingdee_supplier_no) ? $item->vendor_id : $item->kingdee_supplier_no;
                        $item->is_send_kingdee     = KingDeeEnums::IS_SEND_KING_DEE_2;
                        $bool                      = $item->save();
                        if ($bool === false) {
                            $this->logger->warning('供应商同步金碟失败' . $item->vendor_id);
                        }
                        $z++;

                    } else {
                        $item->is_send_kingdee = KingDeeEnums::IS_SEND_KING_DEE_3;
                        $bool                  = $item->save();
                        if ($bool === false) {
                            $this->logger->warning('供应商同步金碟失败' . $item->vendor_id);
                        }
                    }
                }

                $i   += 100;
                $res = Vendor::find([
                    'conditions' => 'is_send_kingdee = :is_send_kingdee:',
                    'bind'       => ['is_send_kingdee' => KingDeeEnums::IS_SEND_KING_DEE_1],
                    'limit'      => 100,
                    'offset'     => $i,
                ]);
            }

            echo '成功处理' . $z . '条数';
        } catch (ValidationException $e) {
            $this->logger->notice('kingdee_task_validation:' . $e->getMessage());
            echo $e->getMessage() . PHP_EOL;
        } catch (Exception $e) {
            $this->logger->error('kingdee_task_exception:' . $e->getMessage());
            echo $e->getMessage() . PHP_EOL;
        }

        echo 'end: ' . date('Y-m-d H:i:s') . PHP_EOL;
        $this->clearLock(__METHOD__);
        exit;
    }

    /**
     * 推送日期抽象
     * @param $params
     * @return array
     * @throws Exception
     */
    protected function getPushDate($params)
    {
        // 获取推送日期配置
        $task_send_day_num = EnvModel::getEnvByCode(KingDeeEnums::TASK_SEND_DAY_NUM, '');
        if (empty($task_send_day_num) || !is_numeric($task_send_day_num) || $task_send_day_num < 1 || $task_send_day_num > 31) {
            throw new ValidationException('推送日期配置无效', ErrCode::$VALIDATE_ERROR);
        }
        //计算本月日期1-5号只执行上个月 5-31号只执行本月数据
        $start_date = date('Y-m-01');//1号
        $end_date = date('Y-m-d',strtotime(date('Y-m-'.$task_send_day_num)));//5号
        $today = $params[0] ? $params[0] : date('Y-m-d');
        if ($today >= $start_date && $today < $end_date) {
            //若今天在1-4之间，则需要获取上个月(上月1号含～本月1号不含)的数据同步至金蝶
            $date_start = date('Y-m-01 00:00:00', strtotime(date('Y-m-01') . ' -1 month'));
            $date_end = date('Y-m-01 00:00:00');//本月1号不含
        } else {
            //5号之后的日期，需要获取本月（本月1号含～下月1号不含）的数据同步至SAP
            $date_start = date('Y-m-01 00:00:00');//本月1号含
            $date_end = date('Y-m-01 00:00:00', strtotime(date('Y-m-01') . ' +1 month'));//下月1号不含
        }
        return [$date_start, $date_end];
    }

    /**
     * 获取设置的费用所属公司
     * @return array
     * @throws ValidationException
     */
    protected function getKingdeeCompanyIds()
    {
        $kingdee_company_ids = EnumsService::getInstance()->getSettingEnvValueIds(KingDeeEnums::KINGDEE_BU_SETTING_CODE);
        if (empty($kingdee_company_ids)) {
            throw new ValidationException('未配置金蝶BU公司ID配置,请联系产品配置', ErrCode::$VALIDATE_ERROR);
        }
        return $kingdee_company_ids;
    }

    /**
     * 采购-订单数据推送金蝶
     * 执行频率，每5分钟运行一次
     * php app/cli.php king_dee storage_order
     * @param $params
     */
    public function storage_orderAction($params)
    {
        $this->checkLock(__METHOD__, 10800);
        $process_name = str_replace('Action', '', __FUNCTION__);
        $this->check_process($process_name);
        $is_exception = false;
        $log = 'begin: ' . date('Y-m-d H:i:s') . PHP_EOL;
        try {
            $country_code = get_country_code();
            $alone_po_except_country_code = EnumsService::getInstance()->getSettingEnvValueIds(Enums\KingDeeEnums::ALONE_PO_EXCEPT_COUNTRY_CODE);
            if (in_array($country_code,array_map('strtoupper', $alone_po_except_country_code))){
                $log .= '当前国家' . $country_code . '不推送采购订单到金蝶' . PHP_EOL;
                $this->logger->info($log);
                echo $log;
                return;
            }
            //设置语种
            self::setLanguage($country_code);
            [$date_start, $date_end] = $this->getPushDate($params);

            $king_dee_release_date = EnvModel::getEnvByCode('king_dee_release_date', '');
            if (empty($king_dee_release_date)) {
                throw new Exception('金蝶发布日期配置无效', ErrCode::$VALIDATE_ERROR);
            }
            $log .= '数据范围：' . $date_start . '~' . $date_end . PHP_EOL;
            //获取设置的费用所属公司
            $kingdee_company_ids = $this->getKingdeeCompanyIds();
            // 金蝶公司与项目映射关系枚举配置
            $company_project_enum = EnumsService::getInstance()->getSettingEnvValueJson(KingDeeEnums::COMPANY_PROJECT_ENUM);
            //费用公司为空不可传输
            if (!empty($kingdee_company_ids)) {
                //会计科目
                $account_subjects_list = AccountingSubjectsRepository::getInstance()->getListByIds();
                //获取bu公司对应的sap公司码
                $sap_company_list = (new DepartmentRepository())->getDepartmentByIds($kingdee_company_ids, 2);
                $kingdee_purchase_storage_type = EnumsService::getInstance()->getSettingEnvValueIds(Enums\KingDeeEnums::KINGDEE_PURCHASE_STORAGE_TYPE);
                //到期日=当前日期+1095天
                $expire_date = date('Y-m-d', strtotime('+ 1095 days'));
                $purchase_type_list = \App\Modules\Purchase\Models\PurchaseType::find(['columns' => 'purchase_type as id,name_key']);
                $purchase_type_list = !empty($purchase_type_list) ? $purchase_type_list->toArray() : [];
                $purchase_type_list = array_column($purchase_type_list, 'name_key', 'id');
                $t      = \App\Modules\Training\Services\TaskService::getTranslation('en');
                foreach ($purchase_type_list as $key => $value) {
                    $purchase_type_list[$key] = $t->_($value);
                }
                //应付数据推送
                $total = 0;
                $success_count = 0;
                //应付数据推送
                $page = 1;
                $params = ['kingdee_company_ids' => $kingdee_company_ids, 'date_start' => $date_start, 'date_end' => $date_end,'king_dee_release_date' => $king_dee_release_date,'kingdee_purchase_storage_type' => $kingdee_purchase_storage_type, 'max_id' => 0];
                $deal_list = OrderService::getInstance()->getSendKingDeeList($params);
                $deal_data = $deal_list->toArray();
                while (!empty($deal_data)) {
                    $params['max_id'] = max(array_column($deal_data, 'id'));
                    $total += count($deal_data);
                    $log .= '第' . $page . '页应付数据推送(正向)处理开始' . PHP_EOL;

                    $vendor_ids = array_column($deal_data, 'vendor_id');
                    $vendor_data = Vendor::find([
                        'columns' => 'vendor_id, kingdee_supplier_no',
                        'conditions' => 'vendor_id in ({vendor_id:array})',
                        'bind' => [
                            'vendor_id' => $vendor_ids,
                        ]
                    ])->toArray();
                    $vendor_data = array_column($vendor_data, 'kingdee_supplier_no', 'vendor_id');
                    //开始处理查询到的需同步至金蝶应付数据
                    foreach ($deal_list as $item) {
                        $payable_params = OrderService::getInstance()->getPayableParams(
                            $item,
                            [
                                'is_cancel' => KingDeeEnums::IS_CANCEL_PAY_NO,
                                'expire_date' => $expire_date,
                                'sap_company_list' => $sap_company_list,
                                'account_subjects_list' => $account_subjects_list,
                                'company_project_enum' => $company_project_enum,
                                'purchase_type_list' => $purchase_type_list,
                                'vendor_data' => $vendor_data,
                            ]);
                        $response = KingDeeService::getInstance()->payable($payable_params);
                        $log_data = [
                            'type' => KingDeeEnums::PAY_TYPE_PAYABLE,
                            'order_code' => $item->pono,
                            'module_name_key' => SysConfigEnums::SYS_MODULE_PURCHASE_STORAGE_ORDER,
                            'cost_company_id' => $item->cost_company,
                            'cost_department_id' => $item->cost_department,
                            'approved_at' => $item->approve_at,
                            'pay_operate_at' => null,
                            'pay_at' => null,
                            'currency' => $item->currency,
                            'is_cancel_pay' => KingDeeEnums::IS_CANCEL_PAY_NO
                        ];
                        $item = \App\Modules\Purchase\Models\PurchaseOrder::findFirst(['conditions' => 'id=:id:', 'bind' => ['id' => $item->id]]);
                        $result = KingDeeService::getInstance()->savePayLog($response, $log_data, $item);
                        $success_count += $result ? 1 : 0;
                    }
                    $log .= '第' . $page . '页应付数据推送(正向)处理结束' . PHP_EOL;
                    sleep(1);
                    $page += 1;
                    $deal_list = OrderService::getInstance()->getSendKingDeeList($params);
                    $deal_data = $deal_list->toArray();
                }
                $log .= 'storage_payable 应付数据推送总数:' . $total . '条数' . PHP_EOL;
                $log .= 'storage_payable 应付数据推送成功:' . $success_count . '条数' . PHP_EOL;
                $log .= 'storage_payable 应付数据推送失败:' . ($total - $success_count) . '条数' . PHP_EOL;
            } else {
                $log .= '未配置金蝶BU公司ID配置,请联系产品配置' . PHP_EOL;
            }
            $log .= 'end: ' . date('Y-m-d H:i:s');
        } catch (Exception $e) {
            $is_exception = true;
            $log .= 'storage_payable_sync_kingdee-exception: ' . $e->getMessage();
        }
        if ($is_exception) {
            $this->logger->warning($log);
        } else {
            $this->logger->info($log);
        }
        $this->clearLock(__METHOD__);
        exit($log);
    }

    /**
     * 采购-应付数据推送金蝶
     * 执行频率，每天下午一点和凌晨一点分别执行，每国错开10分钟
     * php app/cli.php king_dee storage_payable
     * @param $params
     */
    public function storage_payableAction($params)
    {
        $this->checkLock(__METHOD__, 10800);
        $process_name = str_replace('Action', '', __FUNCTION__);
        $this->check_process($process_name);
        $is_exception = false;
        $log          = 'begin: ' . date('Y-m-d H:i:s') . PHP_EOL;
        try {
            $country_code = get_country_code();
            //设置语种
            self::setLanguage($country_code);
            [$date_start, $date_end] = $this->getPushDate($params);

            $king_dee_release_date = EnvModel::getEnvByCode('king_dee_release_date', '');
            if (empty($king_dee_release_date)) {
                throw new Exception('金蝶发布日期配置无效', ErrCode::$VALIDATE_ERROR);
            }
            
            $log .= '数据范围：' . $date_start . '~' . $date_end . PHP_EOL;
            //获取设置的费用所属公司
            $kingdee_company_ids = $this->getKingdeeCompanyIds();
            // 金蝶公司与项目映射关系枚举配置
            $company_project_enum = EnumsService::getInstance()->getSettingEnvValueJson(KingDeeEnums::COMPANY_PROJECT_ENUM);
            //费用公司为空不可传输
            if (!empty($kingdee_company_ids)) {
                //会计科目
                $account_subjects_list = AccountingSubjectsRepository::getInstance()->getListByIds();
                //获取bu公司对应的sap公司码
                $sap_company_list              = (new DepartmentRepository())->getDepartmentByIds($kingdee_company_ids,
                    2);
                $kingdee_purchase_storage_type = EnumsService::getInstance()->getSettingEnvValueIds(Enums\KingDeeEnums::KINGDEE_PURCHASE_STORAGE_TYPE);
                //到期日=当前日期+1095天
                $expire_date        = date('Y-m-d', strtotime('+ 1095 days'));
                $purchase_type_list = \App\Modules\Purchase\Models\PurchaseType::find(['columns' => 'purchase_type as id,name_key']);
                $purchase_type_list = !empty($purchase_type_list) ? $purchase_type_list->toArray() : [];
                $purchase_type_list = array_column($purchase_type_list, 'name_key', 'id');
                $t                  = \App\Modules\Training\Services\TaskService::getTranslation('en');
                foreach ($purchase_type_list as $key => $value) {
                    $purchase_type_list[$key] = $t->_($value);
                }
                //应付数据推送
                $total         = 0;
                $success_count = 0;
                //应付数据推送
                $page      = 1;
                $params    = [
                    'kingdee_company_ids'           => $kingdee_company_ids,
                    'date_start'                    => $date_start,
                    'date_end'                      => $date_end,
                    'king_dee_release_date'         => $king_dee_release_date,
                    'kingdee_purchase_storage_type' => $kingdee_purchase_storage_type,
                    'max_id' => 0,
                ];
                $deal_list = StorageService::getInstance()->getSendKingDeeList($params);
                $deal_data = $deal_list->toArray();
                while (!empty($deal_data)) {
                    $params['max_id'] = max(array_column($deal_data, 'id'));
                    $total += count($deal_data);
                    $log   .= '第' . $page . '页应付数据推送(正向)处理开始' . PHP_EOL;

                    $vendor_ids  = array_column($deal_data, 'vendor_id');
                    $vendor_data = Vendor::find([
                        'columns'    => 'vendor_id, kingdee_supplier_no',
                        'conditions' => 'vendor_id in ({vendor_id:array})',
                        'bind'       => [
                            'vendor_id' => $vendor_ids,
                        ]
                    ])->toArray();
                    $vendor_data = array_column($vendor_data, 'kingdee_supplier_no', 'vendor_id');
                    //开始处理查询到的需同步至金蝶应付数据
                    foreach ($deal_list as $item) {
                        $payable_params = StorageService::getInstance()->getPayableParams(
                            $item,
                            [
                                'is_cancel'             => KingDeeEnums::IS_CANCEL_PAY_NO,
                                'expire_date'           => $expire_date,
                                'sap_company_list'      => $sap_company_list,
                                'account_subjects_list' => $account_subjects_list,
                                'company_project_enum'  => $company_project_enum,
                                'purchase_type_list'    => $purchase_type_list,
                                'vendor_data'           => $vendor_data,
                            ]);
                        $response       = KingDeeService::getInstance()->payable($payable_params);
                        $log_data      = [
                            'type'               => KingDeeEnums::PAY_TYPE_PAYABLE,
                            'order_code'         => $item->psno,
                            'module_name_key'    => SysConfigEnums::SYS_MODULE_PURCHASE_STORAGE_PAYABLE,
                            'cost_company_id'    => $item->cost_company,
                            'cost_department_id' => $item->cost_department,
                            'approved_at'        => $item->approve_at,
                            'pay_operate_at'     => null,
                            'pay_at'             => null,
                            'currency'           => $item->currency,
                            'is_cancel_pay'      => KingDeeEnums::IS_CANCEL_PAY_NO
                        ];
                        $item = \App\Modules\Purchase\Models\PurchaseStorage::findFirst(['conditions' => 'id=:id:', 'bind' => ['id' => $item->id]]);
                        $result        = KingDeeService::getInstance()->savePayLog($response, $log_data, $item);
                        $success_count += $result ? 1 : 0;
                    }
                    $log .= '第' . $page . '页应付数据推送(正向)处理结束' . PHP_EOL;
                    sleep(1);
                    $page      += 1;
                    $deal_list = StorageService::getInstance()->getSendKingDeeList($params);
                    $deal_data = $deal_list->toArray();
                }
                $log .= 'storage_payable 应付数据推送总数:' . $total . '条数' . PHP_EOL;
                $log .= 'storage_payable 应付数据推送成功:' . $success_count . '条数' . PHP_EOL;
                $log .= 'storage_payable 应付数据推送失败:' . ($total - $success_count) . '条数' . PHP_EOL;
            } else {
                $log .= '未配置金蝶BU公司ID配置,请联系产品配置' . PHP_EOL;
            }
            $log .= 'end: ' . date('Y-m-d H:i:s');
        } catch (Exception $e) {
            $is_exception = true;
            $log          .= 'storage_payable_sync_kingdee-exception: ' . $e->getMessage();
        }
        if ($is_exception) {
            $this->logger->warning($log);
        } else {
            $this->logger->info($log);
        }
        $this->clearLock(__METHOD__);
        exit($log);
    }

    /**
     * 报销-应付数据推送金蝶
     * 执行频率，1号或6号到31号凌晨1点
     * @param $params
     */
    public function reimbursement_payableAction($params)
    {
        $this->checkLock(__METHOD__, 10800);
        $is_exception = false;
        $log = 'begin: ' . date('Y-m-d H:i:s') . PHP_EOL;
        try {
            $country_code = get_country_code();
            //设置语种
            self::setLanguage($country_code);

            [$date_start, $date_end] = $this->getPushDate($params);
            $log .= '数据范围：' . $date_start . '~' . $date_end . PHP_EOL;

            //获取设置的费用所属公司
            $kingdee_company_ids = $this->getKingdeeCompanyIds();
            // 金蝶公司与项目映射关系枚举配置
            $company_project_enum = EnumsService::getInstance()->getSettingEnvValueJson(KingDeeEnums::COMPANY_PROJECT_ENUM);
            $business_type_no = 'Y01';

            //会计科目
            $account_subjects_list = AccountingSubjectsRepository::getInstance()->getListByIds();
            //获取bu公司对应的sap公司码
            $sap_company_list = (new DepartmentRepository())->getDepartmentByIds($kingdee_company_ids, 2);
            //到期日=当前日期+180天
            $expire_date = date('Y-m-d', strtotime('+ 180 days'));
            //获取系统配置中的金蝶个人供应商编码
            $vendor_id = EnumsService::getInstance()->getSettingEnvValue(KingDeeEnums::KINGDEE_PERSONAL_SUPPLIER_SETTING_CODE);

            //应付数据推送
            $total = 0;
            $success_count = 0;

            //应付数据推送(正向)
            $page = 1;
            $params = ['kingdee_company_ids' => $kingdee_company_ids, 'date_start' => $date_start, 'date_end' => $date_end, 'pay_type' => KingDeeEnums::PAY_TYPE_PAYABLE, 'max_id' => 0];
            $deal_list = ReimbursementListService::getInstance()->getSendKingDeeList($params);
            $deal_data = $deal_list->toArray();
            while (!empty($deal_data)) {
                $params['max_id'] = max(array_column($deal_data, 'id'));
                $total += count($deal_data);
                $log .= '第' . $page . '页应付数据推送(正向)处理开始' . PHP_EOL;

                //开始处理查询到的需同步至金蝶应付数据
                foreach ($deal_list as $item) {
                    $payable_params = ReimbursementListService::getInstance()->getPayableParams(
                        $item,
                        [
                            'is_cancel' => KingDeeEnums::IS_CANCEL_PAY_NO,
                            'expire_date' => $expire_date,
                            'vendor_id' => $vendor_id,
                            'sap_company_list' => $sap_company_list,
                            'account_subjects_list' => $account_subjects_list,
                            'company_project_enum' => $company_project_enum,
                            'business_type_no' => $business_type_no,
                        ]);
                    $response = KingDeeService::getInstance()->payable($payable_params);
                    $log_data = [
                        'type' => KingDeeEnums::PAY_TYPE_PAYABLE,
                        'order_code' => $item->no,
                        'module_name_key' => SysConfigEnums::SYS_MODULE_REIMBURSEMENT,
                        'cost_company_id' => $item->cost_company_id,
                        'cost_department_id' => $item->cost_department,
                        'approved_at' => $item->approved_at,
                        'pay_operate_at' => $item->pay_operate_date,
                        'pay_at' => null,
                        'currency' => $item->currency,
                        'is_cancel_pay' => KingDeeEnums::IS_CANCEL_PAY_NO
                    ];
                    $result = KingDeeService::getInstance()->savePayLog($response, $log_data, $item);
                    $success_count += $result ? 1 : 0;
                }
                $log .= '第' . $page . '页应付数据推送(正向)处理结束' . PHP_EOL;
                sleep(1);
                $page += 1;
                $deal_list = ReimbursementListService::getInstance()->getSendKingDeeList($params);
                $deal_data = $deal_list->toArray();
            }

            //应付数据推送(反向)
            $page = 1;
            $params['max_id'] = 0;
            $params['is_cancel'] = KingDeeEnums::IS_CANCEL_PAY_YES;
            $deal_list = ReimbursementListService::getInstance()->getSendKingDeeList($params);
            $deal_data = $deal_list->toArray();
            while (!empty($deal_data)) {
                $params['max_id'] = max(array_column($deal_data, 'id'));
                $total += count($deal_data);
                $log .= '第' . $page . '页应付数据推送(反向)处理开始' . PHP_EOL;

                //开始处理查询到的需同步至金蝶应付数据
                foreach ($deal_list as $item) {
                    $payable_params = ReimbursementListService::getInstance()->getPayableParams(
                        $item,
                        [
                            'is_cancel' => KingDeeEnums::IS_CANCEL_PAY_YES,
                            'expire_date' => $expire_date,
                            'vendor_id' => $vendor_id,
                            'sap_company_list' => $sap_company_list,
                            'account_subjects_list' => $account_subjects_list,
                            'company_project_enum' => $company_project_enum,
                            'business_type_no' => $business_type_no,
                        ]);
                    $response = KingDeeService::getInstance()->payable($payable_params, KingDeeEnums::IS_CANCEL_PAY_YES);
                    $log_data = [
                        'type' => KingDeeEnums::PAY_TYPE_PAYABLE,
                        'order_code' => $item->no,
                        'module_name_key' => SysConfigEnums::SYS_MODULE_REIMBURSEMENT,
                        'cost_company_id' => $item->cost_company_id,
                        'cost_department_id' => $item->cost_department,
                        'approved_at' => $item->approved_at,
                        'pay_operate_at' => $item->pay_operate_date,
                        'pay_at' => null,
                        'currency' => $item->currency,
                        'is_cancel_pay' => KingDeeEnums::IS_CANCEL_PAY_YES
                    ];
                    $result = KingDeeService::getInstance()->savePayLog($response, $log_data, $item);
                    $success_count += $result ? 1 : 0;
                }

                $log .= '第' . $page . '页应付数据推送(反向)处理结束' . PHP_EOL;
                sleep(1);
                $page += 1;
                $deal_list = ReimbursementListService::getInstance()->getSendKingDeeList($params);
                $deal_data = $deal_list->toArray();
            }

            $log .= 'reimbursement_payable 应付数据推送总数:' . $total . '条数' . PHP_EOL;
            $log .= 'reimbursement_payable 应付数据推送成功:' . $success_count . '条数' . PHP_EOL;
            $log .= 'reimbursement_payable 应付数据推送失败:' . ($total - $success_count) . '条数' . PHP_EOL;

            $log .= 'end: ' . date('Y-m-d H:i:s');
        } catch (Exception $e) {
            $is_exception = true;
            $log .= 'reimbursement_payable_sync_kingdee-exception: ' . $e->getMessage();
        }
        if ($is_exception) {
            $this->logger->warning($log);
        } else {
            $this->logger->info($log);
        }
        $this->clearLock(__METHOD__);
        exit($log);
    }

    /**
     * 报销-付款数据推送金蝶
     * 执行频率，1号或6号到31号凌晨3点
     * @param $params
     */
    public function reimbursement_paybillAction($params)
    {
        $this->checkLock(__METHOD__, 10800);
        $is_exception = false;
        $log = 'begin: ' . date('Y-m-d H:i:s') . PHP_EOL;
        try {
            $country_code = get_country_code();
            //设置语种
            self::setLanguage($country_code);

            // 获取推送日期配置
            [$date_start, $date_end] = $this->getPushDate($params);
            $log .= '数据范围：' . $date_start . '~' . $date_end . PHP_EOL;

            //获取设置的费用所属公司
            $kingdee_company_ids = $this->getKingdeeCompanyIds();
            $business_type_no = 'Y01';
            //获取bu公司对应的sap公司码
            $sap_company_list = (new DepartmentRepository())->getDepartmentByIds($kingdee_company_ids, 2);

            //获取系统配置中的金蝶个人供应商编码
            $vendor_id = EnumsService::getInstance()->getSettingEnvValue(KingDeeEnums::KINGDEE_PERSONAL_SUPPLIER_SETTING_CODE);

            //付款数据推送
            $total = 0;
            $success_count = 0;
            $page = 1;
            $params = ['kingdee_company_ids' => $kingdee_company_ids, 'date_start' => $date_start, 'date_end' => $date_end, 'pay_type' => KingDeeEnums::PAY_TYPE_PAYBILL, 'max_id' => 0];
            $deal_list = ReimbursementListService::getInstance()->getSendKingDeeList($params);
            $deal_data = $deal_list->toArray();
            while (!empty($deal_data)) {
                $params['max_id'] = max(array_column($deal_data, 'id'));
                $total += count($deal_data);
                $log .= '第' . $page . '页付款数据推送处理开始' . PHP_EOL;

                //获取流转到支付模块的单据信息组
                $nos = array_column($deal_data, 'no');
                $payment_list = PaymentModel::find([
                    'columns' => 'no, pay_method',
                    'conditions' => 'no in ({nos:array})',
                    'bind' => ['nos' => $nos]
                ])->toArray();
                $payment_list = array_column($payment_list, 'pay_method', 'no');

                //开始处理查询到的需同步至金蝶应付数据
                foreach ($deal_list as $item) {
                    //如果未对接支付模块，则固定为银行转账，如果对接了支付模块，则取支付模块对应的支付方式，需要按照以下枚举传输值
                    if ($item->is_pay_module == PayEnums::BIZ_DATA_IS_PAY_MODULE_YES) {
                        $pay_method = $payment_list[$item->no] ?? 0;
                    } else {
                        $pay_method = GlobalEnums::PAYMENT_METHOD_BANK_TRANSFER;//银行转账
                    }
                    //WHT税额所有明细行总计，需要保证精确度
                    $details = $item->getDetails()->toArray();
                    $abstract = !empty($item->no) ? 'Payment_'.$item->no : '';
                    $wht_tax_amount = bcdiv(array_sum(array_column($details, 'wht_tax_amount')), 1000, 2);
                    $response = KingDeeService::getInstance()->paybill([
                        'bill_no' => $country_code . $item->no,
                        'pay_at' => date('Y-m-d' , strtotime($item->pay_at)),
                        'vendor_id' => $vendor_id,
                        'currency' => $item->currency,
                        'sap_company_id' => $sap_company_list[$item->cost_company_id]['sap_company_id'] ?? '',
                        'remark' => $item->remark,
                        'no' => $item->no,
                        'pay_method' => KingDeeEnums::$settle_type[$pay_method] ?? '',
                        'pay_bank_account' => $item->pay_bank_account,
                        'amount' => bcdiv(($item->amount - $item->loan_amount), 1000, 2),
                        'real_amount' => bcdiv($item->real_amount, 1000, 2),
                        'wht_tax_amount' => $wht_tax_amount,
                        'business_type_no' => $business_type_no,
                        'abstract' => $abstract,
                    ]);

                    $log_data = [
                        'type' => KingDeeEnums::PAY_TYPE_PAYBILL,
                        'order_code' => $item->no,
                        'module_name_key' => SysConfigEnums::SYS_MODULE_REIMBURSEMENT,
                        'cost_company_id' => $item->cost_company_id,
                        'cost_department_id' => $item->cost_department,
                        'approved_at' => $item->approved_at,
                        'pay_operate_at' => $item->pay_operate_date,
                        'pay_at' => $item->pay_at,
                        'currency' => $item->currency,
                        'is_cancel_pay' => KingDeeEnums::IS_CANCEL_PAY_NO
                    ];
                    $result = KingDeeService::getInstance()->savePayLog($response, $log_data, $item);
                    $success_count += $result ? 1 : 0;
                }

                $log .= '第' . $page . '页付款数据推送处理结束' . PHP_EOL;
                sleep(1);
                $page += 1;
                $deal_list = ReimbursementListService::getInstance()->getSendKingDeeList($params);
                $deal_data = $deal_list->toArray();
            }

            $log .= 'reimbursement_paybill 付款数据推送总数:' . $total . '条数' . PHP_EOL;
            $log .= 'reimbursement_paybill 付款数据推送成功:' . $success_count . '条数' . PHP_EOL;
            $log .= 'reimbursement_paybill 付款数据推送失败:' . ($total - $success_count) . '条数' . PHP_EOL;

            $log .= 'end: ' . date('Y-m-d H:i:s');
        } catch (Exception $e) {
            $is_exception = true;
            $log .= 'reimbursement_paybill_sync_kingdee-exception: ' . $e->getMessage();
        }
        if ($is_exception) {
            $this->logger->warning($log);
        } else {
            $this->logger->info($log);
        }
        $this->clearLock(__METHOD__);
        exit($log);
    }


    /**
     * 普通付款-应付数据推送金碟
     * 执行频率，1号或6号到31号凌晨1点5分
     * @param $params
     * */
    public function ordinary_payment_payableAction($params)
    {
        $this->checkLock(__METHOD__, 10800);
        $log = 'begin: ' . date('Y-m-d H:i:s') . PHP_EOL;
        try {
            $country_code = get_country_code();
            //设置语种
            self::setLanguage($country_code);

            // 获取推送日期配置
            [$date_start, $date_end] = $this->getPushDate($params);
            //获取设置的费用所属公司
            $kingdee_company_ids = $this->getKingdeeCompanyIds();
            // 金蝶公司与项目映射关系枚举配置
            $company_project_enum = EnumsService::getInstance()->getSettingEnvValueJson(KingDeeEnums::COMPANY_PROJECT_ENUM);
            // 业务类型编号
            $business_type_no = 'Y03';
            //获取系统配置中的金蝶个人供应商编码
            $vendor_id_gys = EnumsService::getInstance()->getSettingEnvValue(KingDeeEnums::KINGDEE_PERSONAL_SUPPLIER_SETTING_CODE);

            //会计科目
            $account_subjects_list = AccountingSubjectsRepository::getInstance()->getListByIds();
            //获取bu公司对应的公司码
            $sap_company_list = (new DepartmentRepository())->getDepartmentByIds($kingdee_company_ids, 2);
            //到期日=当前日期+180天
//                $expire_date = date('Y-m-d', strtotime('+ 180 days'));

            //应付数据推送
            $total         = 0;
            $success_count = 0;

            //应付数据推送(正向)
            $page      = 1;
            $params    = ['kingdee_company_ids' => $kingdee_company_ids, 'date_start' => $date_start, 'date_end' => $date_end, 'pay_type' => KingDeeEnums::PAY_TYPE_PAYABLE, 'max_id' => 0];
            $deal_data = OrdinaryPaymentListService::getInstance()->getSendKingDeeList($params);

            while (!empty($deal_data)) {
                $params['max_id'] = max(array_column($deal_data, 'id'));
                foreach ($deal_data as $item) {
                    if ($item['payee_type'] != Enums::PAYEE_TYPE_PERSONAL){
                        $vendor_id = $item['sap_supplier_no'];
                    }else{
                        $vendor_id = $vendor_id_gys;
                    }
                    $payable_params = OrdinaryPaymentListService::getInstance()->getPayableParams(
                        $item,
                        [
                            'is_cancel'             => KingDeeEnums::IS_CANCEL_PAY_NO,
                            'expire_date'           => date('Y-m-d',strtotime($item['should_pay_date'])),
                            'vendor_id'             => $vendor_id,
                            'sap_company_list'      => $sap_company_list,
                            'account_subjects_list' => $account_subjects_list,
                            'company_project_enum'  => $company_project_enum,
                            'business_type_no'      => $business_type_no,
                        ]);

                    $response = KingDeeService::getInstance()->payable($payable_params);

                    $log_data   = [
                        'type'               => KingDeeEnums::PAY_TYPE_PAYABLE,
                        'order_code'         => $item['apply_no'],
                        'module_name_key'    => SysConfigEnums::SYS_MODULE_ORDINARY_PAYMENT,
                        'cost_company_id'    => $item['cost_company_id'],
                        'cost_department_id' => $item['cost_department_id'],
                        'approved_at'        => $item['approved_at'],
                        'pay_operate_at'     => $item['pay_at'] ?? $item['approved_at'],
                        'currency'           => $item['currency'],
                        'is_cancel_pay'      => KingDeeEnums::IS_CANCEL_PAY_NO
                    ];
                    $main_model = (object)[];
                    if ($response) {
                        $main_model = OrdinaryPayment::findFirst([
                            'conditions' => 'id = :id:',
                            'bind'       => ['id' => $item['id']]
                        ]);
                    }
                    $result     = KingDeeService::getInstance()->savePayLog($response, $log_data, $main_model);
                    $success_count += $result ? 1 : 0;
                }
                $total += count($deal_data);
                $log   .= '第' . $page . '页应付数据推送(正向)处理开始' . PHP_EOL;
                sleep(1);
                $page      += 1;
                $deal_data = OrdinaryPaymentListService::getInstance()->getSendKingDeeList($params);

            }


            //应付数据推送(反向)
            $page                = 1;
            $params['max_id'] = 0;
            $params['is_cancel'] = KingDeeEnums::IS_CANCEL_PAY_YES;
            $deal_data           = OrdinaryPaymentListService::getInstance()->getSendKingDeeList($params);
            while (!empty($deal_data)) {
                $params['max_id'] = max(array_column($deal_data, 'id'));
                foreach ($deal_data as $item) {
                    if ($item['payee_type'] != Enums::PAYEE_TYPE_PERSONAL){
                        $vendor_id = $item['sap_supplier_no'];
                    }
                    $payable_params = OrdinaryPaymentListService::getInstance()->getPayableParams(
                        $item,
                        [
                            'is_cancel' => KingDeeEnums::IS_CANCEL_PAY_YES,
                            'expire_date' => date('Y-m-d',strtotime($item['should_pay_date'])),
                            'vendor_id' => $vendor_id,
                            'sap_company_list' => $sap_company_list,
                            'account_subjects_list' => $account_subjects_list,
                            'company_project_enum' => $company_project_enum,
                            'business_type_no'      => $business_type_no,
                        ]);

                    $response = KingDeeService::getInstance()->payable($payable_params, KingDeeEnums::IS_CANCEL_PAY_YES);

                    $log_data   = [
                        'type'               => KingDeeEnums::PAY_TYPE_PAYABLE,
                        'order_code'         => $item['apply_no'],
                        'module_name_key'    => SysConfigEnums::SYS_MODULE_ORDINARY_PAYMENT,
                        'cost_company_id'    => $item['cost_company_id'],
                        'cost_department_id' => $item['cost_department_id'],
                        'approved_at'        => $item['approved_at'],
                        'pay_operate_at'     => $item['pay_at'] ?? '',
                        'currency'           => $item['currency'],
                        'is_cancel_pay'      => KingDeeEnums::IS_CANCEL_PAY_YES
                    ];
                    $main_model = (object)[];
                    if ($response) {
                        $main_model = OrdinaryPayment::findFirst([
                                'conditions' => 'id = :id:',
                                'bind'       => ['id' => $item['id']]]
                        );
                    }
                    $result = KingDeeService::getInstance()->savePayLog($response, $log_data, $main_model);

                    $success_count += $result ? 1 : 0;
                }
                $total += count($deal_data);
                $log   .= '第' . $page . '页应付数据推送(正向)处理开始' . PHP_EOL;
                sleep(1);
                $page      += 1;
                $deal_data = OrdinaryPaymentListService::getInstance()->getSendKingDeeList($params);

            }
            $log .= 'end: ' . date('Y-m-d H:i:s');
            $this->logger->info($log);

        } catch (Exception $e) {
            $log .= 'ordinary_payment_payable_sync_kingdee-exception: ' . $e->getMessage();
            $this->logger->error($log);

        }
        $this->clearLock(__METHOD__);
        exit($log);
    }


    /**
     * 普通付款-付款数据推送金蝶
     * 执行频率，1号或6号到31号凌晨3点5分
     * @param $params
     * */
    public function ordinary_payment_paybillAction($params)
    {
        $this->checkLock(__METHOD__, 10800);
        $log = 'begin: ' . date('Y-m-d H:i:s') . PHP_EOL;
        try {
            $country_code = get_country_code();
            //设置语种
            self::setLanguage($country_code);

            // 获取推送日期配置
            [$date_start, $date_end] = $this->getPushDate($params);
            //获取设置的费用所属公司
            $kingdee_company_ids = $this->getKingdeeCompanyIds();
            // 业务类型编号
            $business_type_no = 'Y03';
            //获取系统配置中的金蝶个人供应商编码
            $vendor_id_gys = EnumsService::getInstance()->getSettingEnvValue(KingDeeEnums::KINGDEE_PERSONAL_SUPPLIER_SETTING_CODE);

            //获取bu公司对应的sap公司码
            $sap_company_list = (new DepartmentRepository())->getDepartmentByIds($kingdee_company_ids, 2);


            //付款数据推送
            $total         = 0;
            $success_count = 0;
            $page          = 1;
            $params        = ['kingdee_company_ids' => $kingdee_company_ids, 'date_start' => $date_start, 'date_end' => $date_end, 'pay_type' => KingDeeEnums::PAY_TYPE_PAYBILL, 'max_id' => 0];
            $deal_data     = OrdinaryPaymentListService::getInstance()->getSendKingDeeList($params);

            while (!empty($deal_data)) {
                $params['max_id'] = max(array_column($deal_data,'id'));
                $total += count($deal_data);
                $log   .= '第' . $page . '页付款数据推送处理开始' . PHP_EOL;

                //获取流转到支付模块的单据信息组
                $nos          = array_column($deal_data, 'apply_no');
                $payment_list = PaymentModel::find([
                    'columns'    => 'no, pay_method',
                    'conditions' => 'no in ({nos:array})',
                    'bind'       => ['nos' => $nos]
                ])->toArray();
                $payment_list = array_column($payment_list, 'pay_method', 'no');

                //开始处理查询到的需同步至金蝶应付数据
                foreach ($deal_data as $item) {
                    //如果未对接支付模块，则固定为银行转账，如果对接了支付模块，则取支付模块对应的支付方式，需要按照以下枚举传输值
                    if ($item['is_pay_module'] == PayEnums::BIZ_DATA_IS_PAY_MODULE_YES) {
                        $pay_method = $payment_list[$item['apply_no']] ?? 0;
                    } else {
                        $pay_method = GlobalEnums::PAYMENT_METHOD_BANK_TRANSFER;//银行转账
                    }
                    if ($item['payee_type'] != Enums::PAYEE_TYPE_PERSONAL){
                        $vendor_id = $item['sap_supplier_no'];
                    }else{
                        $vendor_id = $vendor_id_gys;
                    }

                    $response = KingDeeService::getInstance()->paybill([
                        'bill_no'          => $country_code . $item['apply_no'],
                        'pay_at'           => date('Y-m-d', strtotime($item['pay_bk_flow_date'])),
                        'vendor_id'        => $vendor_id,
                        'currency'         => $item['currency'],
                        'sap_company_id'   => $sap_company_list[$item['cost_company_id']]['sap_company_id'] ?? '',
                        'remark'           => $item['remark'],
                        'no'               => $item['apply_no'],
                        'pay_method'       => KingDeeEnums::$settle_type[$pay_method] ?? '',
                        'pay_bank_account' => $item['pay_bk_account'],
                        'amount'           => $item['amount_total_have_tax'],
                        'real_amount'      => $item['amount_total_actually'],
                        'wht_tax_amount'   => $item['amount_total_wht'],
                        'discount_value'   => $item['amount_discount'],
                        'business_type_no' => $business_type_no,
                        'abstract' => 'Payment_' . $item['apply_no'],
                    ]);

                    $log_data   = [
                        'type'               => KingDeeEnums::PAY_TYPE_PAYBILL,
                        'order_code'         => $item['apply_no'],
                        'module_name_key'    => SysConfigEnums::SYS_MODULE_ORDINARY_PAYMENT,
                        'cost_company_id'    => $item['cost_company_id'],
                        'cost_department_id' => $item['cost_department_id'],
                        'approved_at'        => $item['approved_at'],
                        'pay_operate_at'     => $item['pay_at'],
                        'currency'           => $item['currency'],
                        'is_cancel_pay'      => KingDeeEnums::IS_CANCEL_PAY_NO
                    ];
                    $main_model = (object)[];
                    if ($response) {
                        $main_model = OrdinaryPayment::findFirst([
                                'conditions' => 'id = :id:',
                                'bind'       => ['id' => $item['id']]]
                        );
                    }
                    $result        = KingDeeService::getInstance()->savePayLog($response, $log_data, $main_model);
                    $success_count += $result ? 1 : 0;
                }

                $log .= '第' . $page . '页付款数据推送处理结束' . PHP_EOL;
                sleep(1);
                $page      += 1;
                $deal_data = OrdinaryPaymentListService::getInstance()->getSendKingDeeList($params);
            }

            $log .= 'ordinary_payment_paybill 付款数据推送总数:' . $total . '条数' . PHP_EOL;
            $log .= 'ordinary_payment_paybill 付款数据推送成功:' . $success_count . '条数' . PHP_EOL;
            $log .= 'ordinary_payment_paybill 付款数据推送失败:' . ($total - $success_count) . '条数' . PHP_EOL;

            $log .= 'end: ' . date('Y-m-d H:i:s');
        } catch (Exception $e) {
            $log .= 'ordinary_payment_payable_sync_kingdee-exception: ' . $e->getMessage();
            $this->logger->error($log);

        }

        $this->logger->info($log);
        $this->clearLock(__METHOD__);
        exit($log);


    }

    /**
     * 采购-付款申请单应付数据推送金蝶
     * 每天凌晨1点(当地时区)定时推送审批通过的数据
     * php app/cli.php king_dee purchase_payment_payable
     * @param $params
     */
    public function purchase_payment_payableAction($params)
    {
        $this->checkLock(__METHOD__);
        $is_exception = false;
        $log          = 'begin: ' . date('Y-m-d H:i:s') . PHP_EOL;
        try {
            $country_code = get_country_code();
            //设置语种
            self::setLanguage($country_code);

            [$date_start, $date_end] = $this->getPushDate($params);
            $log .= '数据范围：' . $date_start . '~' . $date_end . PHP_EOL;

            //获取设置的费用所属公司
            $kingdee_company_ids = $this->getKingdeeCompanyIds();
            // 金蝶公司与项目映射关系枚举配置
            $company_project_enum = EnumsService::getInstance()->getSettingEnvValueJson(KingDeeEnums::COMPANY_PROJECT_ENUM);

            //会计科目
            $account_subjects_list = AccountingSubjectsRepository::getInstance()->getListByIds();

            //获取bu公司对应的sap公司码
            $sap_company_list = (new DepartmentRepository())->getDepartmentByIds($kingdee_company_ids, 2);

            //获取系统配置中的关联的采购订单的采购类型不是：固定资产类，库存类
            $kingdee_purchase_storage_type = EnumsService::getInstance()->getSettingEnvValueIds(KingDeeEnums::KINGDEE_PURCHASE_STORAGE_TYPE);

            //应付数据推送
            $total         = 0;
            $success_count = 0;

            $business_type_no = 'Y05';
            $params    = [
                'kingdee_purchase_storage_type' => $kingdee_purchase_storage_type,
                'kingdee_company_ids'           => $kingdee_company_ids,
                'date_start'                    => $date_start,
                'date_end'                      => $date_end,
                'pay_type'                      => KingDeeEnums::PAY_TYPE_PAYABLE,
                'max_id'                        => 0,
            ];

            //应付数据推送(正向)
            $page      = 1;
            $deal_data = PaymentService::getInstance()->getSendKingDeeList($params);
            while (!empty($deal_data)) {
                $vendor_list = VendorRepository::getInstance()->getVendorListByIds(array_column($deal_data, 'vendor_id'));
                $params['max_id'] = max(array_column($deal_data, 'id'));
                $total += count($deal_data);
                $log   .= '第' . $page . '页应付数据推送(正向)处理开始' . PHP_EOL;

                //开始处理查询到的需同步至金蝶应付数据
                foreach ($deal_data as $value) {
                    $item = PurchasePayment::findFirst(['conditions' => 'id=:id:', 'bind' => ['id' => $value['id']]]);
                    $payable_params = PaymentService::getInstance()->getPayableParams(
                        $item,
                        [
                            'is_cancel'             => KingDeeEnums::IS_CANCEL_PAY_NO,
                            'expire_date'           => $item->due_date,
                            'vendor_id'             => $vendor_list[$item->vendor_id]['kingdee_supplier_no'] ?? '',
                            'sap_company_list'      => $sap_company_list,
                            'account_subjects_list' => $account_subjects_list,
                            'company_project_enum'  => $company_project_enum,
                            'business_type_no'      => $business_type_no,
                        ]);
                    $response      = KingDeeService::getInstance()->payable($payable_params);
                    $log_data      = [
                        'type'               => KingDeeEnums::PAY_TYPE_PAYABLE,
                        'order_code'         => $item->ppno,
                        'module_name_key'    => SysConfigEnums::SYS_MODULE_PURCHASE_PAYMENT,
                        'cost_company_id'    => $item->cost_company_id,
                        'cost_department_id' => $item->cost_department,
                        'approved_at'        => $item->approve_at,
                        'pay_operate_at'     => $item->pay_at,
                        'pay_at'             => null,
                        'currency'           => $item->currency,
                        'is_cancel_pay'      => KingDeeEnums::IS_CANCEL_PAY_NO,
                    ];
                    $result        = KingDeeService::getInstance()->savePayLog($response, $log_data, $item);
                    $success_count += $result ? 1 : 0;
                }
                $log .= '第' . $page . '页应付数据推送(正向)处理结束' . PHP_EOL;
                sleep(1);
                $page      += 1;
                $deal_data = PaymentService::getInstance()->getSendKingDeeList($params);
            }

            //应付数据推送(反向)
            $page                = 1;
            $params['max_id']    = 0;
            $params['is_cancel'] = KingDeeEnums::IS_CANCEL_PAY_YES;
            $deal_data           = PaymentService::getInstance()->getSendKingDeeList($params);
            while (!empty($deal_data)) {
                $vendor_list = VendorRepository::getInstance()->getVendorListByIds(array_column($deal_data, 'vendor_id'));
                $params['max_id'] = max(array_column($deal_data, 'id'));
                $total += count($deal_data);
                $log   .= '第' . $page . '页应付数据推送(反向)处理开始' . PHP_EOL;

                //开始处理查询到的需同步至金蝶应付数据
                foreach ($deal_data as $value) {
                    $item = PurchasePayment::findFirst(['conditions' => 'id=:id:', 'bind' => ['id' => $value['id']]]);
                    $payable_params = PaymentService::getInstance()->getPayableParams(
                        $item,
                        [
                            'is_cancel'             => KingDeeEnums::IS_CANCEL_PAY_YES,
                            'expire_date'           => $item->due_date,
                            'vendor_id'             => $vendor_list[$item->vendor_id]['kingdee_supplier_no'] ?? '',
                            'sap_company_list'      => $sap_company_list,
                            'account_subjects_list' => $account_subjects_list,
                            'company_project_enum'  => $company_project_enum,
                            'business_type_no'      => $business_type_no,
                        ]);
                    $response       = KingDeeService::getInstance()->payable($payable_params, KingDeeEnums::IS_CANCEL_PAY_YES);
                    $log_data      = [
                        'type'               => KingDeeEnums::PAY_TYPE_PAYABLE,
                        'order_code'         => $item->ppno,
                        'module_name_key'    => SysConfigEnums::SYS_MODULE_PURCHASE_PAYMENT,
                        'cost_company_id'    => $item->cost_company_id,
                        'cost_department_id' => $item->cost_department,
                        'approved_at'        => $item->approve_at,
                        'pay_operate_at'     => $item->pay_at,
                        'pay_at'             => null,
                        'currency'           => $item->currency,
                        'is_cancel_pay'      => KingDeeEnums::IS_CANCEL_PAY_YES,
                    ];
                    $result        = KingDeeService::getInstance()->savePayLog($response, $log_data, $item);
                    $success_count += $result ? 1 : 0;
                }

                $log .= '第' . $page . '页应付数据推送(反向)处理结束' . PHP_EOL;
                sleep(1);
                $page      += 1;
                $deal_data = PaymentService::getInstance()->getSendKingDeeList($params);
            }

            $log .= 'purchase_payment_payable 应付数据推送总数:' . $total . '条数' . PHP_EOL;
            $log .= 'purchase_payment_payable 应付数据推送成功:' . $success_count . '条数' . PHP_EOL;
            $log .= 'purchase_payment_payable 应付数据推送失败:' . ($total - $success_count) . '条数' . PHP_EOL;
            $log .= 'end: ' . date('Y-m-d H:i:s');
        } catch (Exception $e) {
            $is_exception = true;
            $log          .= 'purchase_payment_payable_sync_kingdee-exception: ' . $e->getMessage() . PHP_EOL;
        }
        if ($is_exception) {
            $this->logger->warning($log);
        } else {
            $this->logger->info($log);
        }
        $this->clearLock(__METHOD__);
        exit($log);
    }

    /**
     * 采购-付款申请单付款数据推送金蝶
     * 每天凌晨1点(当地时区)定时推送审批通过的数据
     * php app/cli.php king_dee purchase_payment_paybill
     * @param $params
     */
    public function purchase_payment_paybillAction($params)
    {
        $this->checkLock(__METHOD__);
        $is_exception = false;
        $log          = 'begin: ' . date('Y-m-d H:i:s') . PHP_EOL;
        try {
            $country_code = get_country_code();
            //设置语种
            self::setLanguage($country_code);

            // 获取推送日期配置
            [$date_start, $date_end] = $this->getPushDate($params);
            $log .= '数据范围：' . $date_start . '~' . $date_end . PHP_EOL;

            //获取设置的费用所属公司
            $kingdee_company_ids = $this->getKingdeeCompanyIds();
            //获取bu公司对应的sap公司码
            $sap_company_list = (new DepartmentRepository())->getDepartmentByIds($kingdee_company_ids, 2);

            //付款数据推送
            $total         = 0;
            $success_count = 0;
            $page          = 1;
            $params        = [
                'kingdee_company_ids' => $kingdee_company_ids,
                'date_start'          => $date_start,
                'date_end'            => $date_end,
                'pay_type'            => KingDeeEnums::PAY_TYPE_PAYBILL,
                'max_id'              => 0,
            ];
            $deal_list     = PaymentService::getInstance()->getSendKingDeeList($params);
            $deal_data     = $deal_list->toArray();
            while (!empty($deal_data)) {
                $vendor_list = VendorRepository::getInstance()->getVendorListByIds(array_column($deal_data, 'vendor_id'));
                $params['max_id'] = max(array_column($deal_data, 'id'));
                $total += count($deal_data);
                $log   .= '第' . $page . '页付款数据推送处理开始' . PHP_EOL;

                //开始处理查询到的需同步至金蝶应付数据
                foreach ($deal_list as $item) {
                    $response = KingDeeService::getInstance()->paybill([
                        'bill_no'          => $country_code . $item->ppno,
                        'pay_at'           => date('Y-m-d', strtotime($item->real_pay_at)),
                        'vendor_id'        => $vendor_list[$item->vendor_id]['kingdee_supplier_no'] ?? '',
                        'currency'         => $item->currency,
                        'sap_company_id'   => $sap_company_list[$item->cost_company_id]['sap_company_id'] ?? '',
                        'remark'           => $item->remark,
                        'no'               => $country_code . $item->ponos . $item->ppno,
                        'pay_method'       => KingDeeEnums::$settle_type[$item->payment_method] ?? '',
                        'pay_bank_account' => $item->pay_bank_account,
                        'amount'           => bcdiv($item->receipt_amount, 1000, 2),
                        'real_amount'      => bcdiv($item->cur_amount, 1000, 2),
                        'wht_tax_amount'   => bcdiv($item->wht_amount, 1000, 2),
                        'abstract'         => 'Payment_' . $item->ppno, //Payment_OA采购付款单编号
                    ]);
                    $log_data      = [
                        'type'               => KingDeeEnums::PAY_TYPE_PAYBILL,
                        'order_code'         => $item->ppno,
                        'module_name_key'    => SysConfigEnums::SYS_MODULE_PURCHASE_PAYMENT,
                        'cost_company_id'    => $item->cost_company_id,
                        'cost_department_id' => $item->cost_department,
                        'approved_at'        => $item->approved_at,
                        'pay_operate_at'     => $item->pay_at,
                        'pay_at'             => $item->real_pay_at,
                        'currency'           => $item->currency,
                        'is_cancel_pay'      => KingDeeEnums::IS_CANCEL_PAY_NO,
                    ];
                    $result        = KingDeeService::getInstance()->savePayLog($response, $log_data, $item);
                    $success_count += $result ? 1 : 0;
                }

                $log .= '第' . $page . '页付款数据推送处理结束' . PHP_EOL;
                sleep(1);
                $page      += 1;
                $deal_list = PaymentService::getInstance()->getSendKingDeeList($params);
                $deal_data = $deal_list->toArray();
            }

            $log .= 'purchase_payment_paybill 付款数据推送总数:' . $total . '条数' . PHP_EOL;
            $log .= 'purchase_payment_paybill 付款数据推送成功:' . $success_count . '条数' . PHP_EOL;
            $log .= 'purchase_payment_paybill 付款数据推送失败:' . ($total - $success_count) . '条数' . PHP_EOL;

            $log .= 'end: ' . date('Y-m-d H:i:s');
        } catch (Exception $e) {
            $is_exception = true;
            $log          .= 'purchase_payment_paybill_sync_kingdee-exception: ' . $e->getMessage();
        }
        if ($is_exception) {
            $this->logger->warning($log);
        } else {
            $this->logger->info($log);
        }
        $this->clearLock(__METHOD__);
        exit($log);
    }
}