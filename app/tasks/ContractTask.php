<?php

use App\Library\Exception\BusinessException;
use App\Library\Validation\ValidationException;
use App\Library\BaseService;
use App\Models\backyard\SysDistrictModel;
use App\Models\oa\ContractElectronicModel;
use App\Models\oa\ContractElectronicSignInfoModel;
use App\Models\oa\ContractQuotationModel;
use App\Models\oa\ContractSubFileModel;
use App\Models\oa\ContractWarehouseModel;
use App\Models\oa\WarehouseStoreModel;
use App\Modules\Common\Models\ContractCompanyModel;
use App\Modules\Common\Models\EnvModel;
use App\Modules\Common\Services\EnumsService;
use App\Modules\Contract\Models\Contract;
use App\Modules\Contract\Models\ContractStoreRentingModel;
use App\Modules\Contract\Services\AddService as ContractAddService;
use App\Modules\Contract\Services\ContractElectronicFlowService;
use App\Modules\Contract\Services\ContractElectronicService;
use App\Modules\Contract\Services\ContractFlowService;
use App\Modules\Contract\Services\ContractStoreRentingService;
use App\Modules\Contract\Models\ContractArchive;
use App\Library\Enums\ContractEnums;
use App\Library\ErrCode;
use App\Modules\Contract\Services\ContractTemplateVersionService;
use App\Modules\User\Services\UserService;
use App\Modules\Warehouse\Services\WarehouseService;
use App\Repository\DepartmentRepository;
use App\Repository\HrStaffRepository;
use App\Repository\oa\ContractArchiveRepository;
use App\Repository\oa\ContractCategoryRepository;
use App\Library\Enums;
use App\Models\oa\ContractTemplateVersionModel;
use App\Models\oa\SettingEnvModel;
use App\Modules\User\Models\AttachModel;
use GuzzleHttp\Exception\GuzzleException;

class ContractTask extends BaseTask
{
    // 定期发送邮件
    public function send_to_businessAction()
    {
        $output_log = '';

        // 发送邮件
        try {
            $addressStr = (new \App\Modules\Common\Services\EnumsService())->getContractAttachmentAddress();
            $addressList = explode(",", $addressStr);
            if (empty($addressList)) {
                $output_log .= "Email address is empty\n";
                $this->logger->info($output_log);
                exit($output_log);
            }

            // 获取合同归档数据
            $archiveList = (new App\Modules\Contract\Models\ContractArchive())->getAllContractArchive();

            // 导出文件
            $filename = "附件";
            $exportData = empty($archiveList) ? [] : $archiveList;
            $obj = \App\Modules\Contract\Services\ArchiveExportService::getInstance();
            $obj->setLanguage('en');
            $ret = $obj->exportContractArchive($filename, $exportData);

            if (empty($ret) || empty($ret['data'])) {
                $output_log .= "Export contract file failed\n";
                $this->logger->info($output_log);
                exit($output_log);
            }

            $title = "Contract in OA";
            $content = "<p>Dear all,</p><p>&nbsp;&nbsp;&nbsp;&nbsp;Please kindly refer to attachment regarding the contract items in OA system(excluding branch rental contract).</p><p>Thanks!</p><p>OA system Adminstrator</p>";
            foreach ($addressList as $address) {
                $output_log .= "Send email Processing: address - $address, ";

                $result = $this->mailer->sendAsync($address, $title, $content, [$ret['data']]);
                if ($result) {
                    $output_log .= "send success\n";
                } else {
                    $output_log .= "send error\n";
                }
            }

            $this->logger->info($output_log);

        } catch (\Exception $e) {
            $output_log .= "Send email to business compliance error(" . $e->getMessage() . ")\n";
            $this->logger->warning($output_log);
        }

        exit($output_log);
    }

    //重试-同步MS失败记录
    public function retry_sync_msAction()
    {
        $this->checkLock(__METHOD__);

        //获取失败记录
        $failed_result = \App\Modules\CrmQuotation\Models\CrmQuotationSyncFailedModel::find([
            'conditions' => 'retry_result=:retry_result: ',
            'bind' => ['retry_result' => 0],
            'order' => 'sync_time',
            'limit' => 100
        ]);
        $failed_result = $failed_result->toArray();
        //没有记录直接退出
        if (empty($failed_result)) {
            $failed_result = \App\Modules\CrmQuotation\Models\CrmQuotationSyncFailedModel::find([
                'conditions' => 'retry_result=:retry_result: and retry_counter<10',
                'bind' => ['retry_result' => 2],
                'order' => 'sync_time',
                'limit' => 100
            ]);
            $failed_result = $failed_result->toArray();
        }
        //有失败未处理的,判断重试次数,多次重试失败的发送邮件
        $crm_service = new \App\Modules\CrmQuotation\Services\CrmFlowService();
        $success = $error = 0;
        foreach ($failed_result as $k => $v) {
            $failed_update = [];
            #报价单状态
            $quotation = \App\Modules\CrmQuotation\Models\CrmQuotationApplyModel::findFirst([
                'conditions' => 'quoted_price_list_sn=?1',
                'bind' => [1 => $v['quotation_no']]
            ]);
            #是否需要同步CRM
            $need_sync_crm = 0;
            $contract = null;
            if ($v['sync_source'] == 2) {
                $need_sync_crm = 1;
                //查询关联的合同
                $contract_quotation_info = ContractQuotationModel::findFirst([
                    'conditions' => 'quotation_no = :quotation_no:',
                    'bind' => ['quotation_no' => $quotation->quoted_price_list_sn]
                ]);
                if (!$contract_quotation_info) {
                    $this->logger->warning('查询报价单关联合同失败,未找到关联的合同; 报价单号=' . $quotation->quoted_price_list_sn);
                    continue;
                }
                $contract = Contract::findFirst([
                    'id = :id:',
                    'bind' => ['id' => $contract_quotation_info->contract_id]
                ]);
            }
            $sync_result = $crm_service->syncMS($quotation, $need_sync_crm, $contract->effective_date ?? '', $contract->expiry_date ?? '');
            if ($sync_result) {
                $success += 1;
                $failed_update['retry_result'] = 1;
            } else {
                $error += 1;
                $failed_update['retry_result'] = 2;
            }
            #更新失败记录
            $failed_update['retry_counter'] = '`retry_counter`+1';
            $failed_update['retry_time'] = gmdate('Y-m-d H:i:s');
            $sql = "UPDATE `crm_quotation_sync_failed` SET `retry_counter`=`retry_counter`+1,`retry_time`=?,`retry_result`=? WHERE id=? ";
            $this->db_oa->execute($sql, [$failed_update['retry_time'], $failed_update['retry_result'], $v['id']]);
        }

        $result = '执行结束,成功' . $success . '条;失败' . $error . '条';
        $this->logger->info($result);
        $this->clearLock(__METHOD__);
        exit($result);
    }

    //历史未归档合同同步
    public function contract_rechive_syncAction($params)
    {
        //上次进程是否结束
        $process_name = str_replace('Action', '', __FUNCTION__);
        $this->check_process($process_name);

        // 传递的网点编号参数
        $params = $params[0] ?? '';
        $params = !empty($params) ? explode(',', $params) : [];
        $contract_no_list = [];
        foreach ($params as $contract_no) {
            $contract_no_list[] = trim($contract_no);
        }
        $contract_no_list = array_values(array_unique(array_filter($contract_no_list)));
        if (empty($contract_no_list)) {
            exit('合同编号参数不能为空！' . PHP_EOL);
        }
        //获取网点合同
        $contract_renting_list = ContractStoreRentingModel::find([
            'conditions' => 'contract_id in ({contract_ids:array})',
            'bind' => ['contract_ids' => $contract_no_list],
        ])->toArray();
        if (empty($contract_renting_list)) {
            exit('未归档合同编号不存在！' . PHP_EOL);
        }

        $total = count($contract_renting_list);
        $success = $error = 0;
        try {
            $this->db_oa->begin();

            // 归档处理
            $instance = ContractStoreRentingService::getInstance();
            foreach ($contract_renting_list as $contract_renting) {
                $save_result = $instance->saveArchive($contract_renting['id'], []);
                if ($save_result) {
                    $success++;
                    continue;
                }

                $error++;
            }

            $this->db_oa->commit();
        } catch (Exception $e) {
            $this->db_oa->rollback();
            exit('Error info => ' . json_encode($e->getMessage(), JSON_UNESCAPED_UNICODE) . PHP_EOL);
        }

        exit('执行结束,总条数' . $total . '条; 成功' . $success . '条; 失败' . $error . '条' . PHP_EOL);
    }

    /**
     * v15322报价单结构改变刷新历史数据
     */
    public function contract_quotation_refreshAction()
    {
        //一次性处理,生产368条
        //查询关联过报价单的合同
        $contract_model = new Contract();
        $contract = $contract_model::find([
            'quotation_no != :empty_str: and quotation_no is not null',
            'bind' => ['empty_str' => '']
        ])->toArray();
        $contract_ids = array_column($contract, 'id');
        $contract_ids = array_values($contract_ids);
        $contract_ids = implode(',', $contract_ids);
        $db = $this->getDI()->get('db_oa');
        try {
            $db->begin();
            //更新是否需要关联报价单为1.是
            $update_success = $db->updateAsDict(
                $contract_model->getSource(),
                [
                    'is_quotation' => ContractEnums::CONTRACT_RELATION_QUOTATION_YES
                ],
                [
                    'conditions' => "id in ({$contract_ids})",
                ]
            );
            if (!$update_success) {
                throw new \Exception('更新是否关联报价单失败');
            }
            //历史数据写入关联表
            $now_time = date('Y-m-d H:i:s');
            foreach ($contract as $v) {
                $tmp = [];
                $tmp['contract_id'] = $v['id'];
                $tmp['cno'] = $v['cno'];
                $tmp['quotation_no'] = $v['quotation_no'];
                $tmp['created_at'] = $now_time;
                $tmp['updated_at'] = $now_time;
                $tmp['configure_consumer_id'] = $v['configure_consumer_id'] ?? '';
                $tmp['configure_consumer_time'] = $v['configure_consumer_time'] ?? null;
                $contract_quotation_data[] = $tmp;
            }
            //入库
            if (!empty($contract_quotation_data)) {
                $contract_quotation_model = new ContractQuotationModel();
                $contract_quotation_bool = $contract_quotation_model->batch_insert($contract_quotation_data);
                if ($contract_quotation_bool === false) {
                    throw new \Exception('合同关联报价单创建失败, 数据=' . json_encode($contract_quotation_data, JSON_UNESCAPED_UNICODE) . ' ;可能的原因是: ' . get_data_object_error_msg($contract_quotation_model));
                }
            }

            $db->commit();
        } catch (\Exception $e) {
            $db->rollback();
            $this->logger->error('执行失败,原因是' . $e->getMessage() . ';trace=' . $e->getTraceAsString());
            exit('执行失败,原因是' . $e->getMessage());
        }
        $result = '执行结束,共' . count($contract) . '条合同被更新';
        $this->logger->info($result . '处理的合同id:' . $contract_ids);
        exit($result);
    }

    /**
     * 合同归档表 contract_archive
     * 区分租房合同数据
     * */
    public function deal_contract_archiveAction()
    {
        $db = $this->getDI()->get('db_oa');
        $db->begin();

        try {
            $builder = $this->modelsManager->createBuilder();
            $column_str = "c.id,c.cno";
            $builder->columns($column_str);
            $builder->from(['c' => ContractArchive::class])
                ->leftJoin(ContractStoreRentingModel::class, 'c.cno = s.contract_id', 's')
                ->where('s.contract_id > :contract_id:', ['contract_id' => 0]);
            $contract_archive_data = $builder->getQuery()->execute()->toArray();
            $contract_archive_ids = array_column($contract_archive_data, 'id');
            $contract_archive_ids = array_values($contract_archive_ids);
            $contract_archive_ids = implode(',', $contract_archive_ids);

            if (!empty($contract_archive_ids)) {
                $update_success = $db->updateAsDict(
                    (new ContractArchive())->getSource(),
                    [
                        'contract_type' => ContractEnums::CONTRACT_TYPE_STORING,
                    ],
                    [
                        'conditions' => "id in ({$contract_archive_ids})",
                    ]
                );
                if (!$update_success) {
                    throw new \Exception('更新合同归档失败');
                }
            }


            $db->commit();


        } catch (\Exception $e) {
            $db->rollback();
            $this->logger->error('执行失败,原因是' . $e->getMessage() . ';trace=' . $e->getTraceAsString());
            exit('执行失败,原因是' . $e->getMessage());
        }
        $this->logger->info('处理的合同id:' . $contract_archive_ids);

        exit('处理归档数据' . count($contract_archive_data) . '条');

    }

    /**
     * 同步合同相关表创建人的直属部门ID字段
     * 一次性脚本
     *
     * php app/cli.php contract sync_create_dept_id
     *
     * 注: 三张表 总数据量 1w+, 总容量 30M 内, 集中取出, 单条更新, 暂无性能 和 资源不够问题, 支持脚本重复执行
     *
     * 处理 2000 条数据 脚本约需 80s
     */
    public function sync_create_dept_idAction()
    {
        $log = '脚本名称: sync_create_dept_id' . PHP_EOL;
        $log .= '开始时间: ' . date('Y-m-d H:i:s') . PHP_EOL;
        try {
            // 其他合同表 申请人
            $contract_models = Contract::find([
                'conditions' => 'create_department_id = :create_department_id:',
                'bind' => ['create_department_id' => 0]
            ]);
            $contract_unprocessed_count = count($contract_models);
            $log .= '其他合同表 create_department_id 待处理数据 共 ' . $contract_unprocessed_count . ' 条;' . PHP_EOL;


            // 网点租房表 申请人
            $rent_contract_models = ContractStoreRentingModel::find([
                'conditions' => 'create_department_id = :create_department_id:',
                'bind' => ['create_department_id' => 0]
            ]);
            $rent_contract_unprocessed_count = count($rent_contract_models);
            $log .= '租房合同表 create_department_id 待处理数据 共 ' . $rent_contract_unprocessed_count . ' 条;' . PHP_EOL;


            // 合同归档表 申请人
            $archive_contract_models = ContractArchive::find([
                'conditions' => 'create_department_id = :create_department_id:',
                'bind' => ['create_department_id' => 0]
            ]);
            $archive_contract_unprocessed_count = count($archive_contract_models);
            $log .= '归档合同表 create_department_id 待处理数据 共 ' . $archive_contract_unprocessed_count . ' 条;' . PHP_EOL;

            $unprocessed_count_total = $contract_unprocessed_count + $rent_contract_unprocessed_count + $archive_contract_unprocessed_count;
            if ($unprocessed_count_total <= 0) {
                throw new ValidationException('无待处理数据, 请勿反复执行脚本', ErrCode::$VALIDATE_ERROR);
            }

            // 获取所有创建人信息
            $contract_create_ids = array_column($contract_models->toArray(), 'create_id');
            $rent_contract_create_ids = array_column($rent_contract_models->toArray(), 'contract_leader_id');
            $archive_contract_create_ids = array_column($archive_contract_models->toArray(), 'create_id');
            $all_create_ids = array_merge($contract_create_ids, $rent_contract_create_ids, $archive_contract_create_ids);
            $all_create_ids = array_values(array_unique(array_filter($all_create_ids)));

            $log .= '共 ' . count($all_create_ids) . ' 创建人;(已去重)' . PHP_EOL;

            $all_create_item = (new HrStaffRepository())->getStaffListByStaffIds($all_create_ids);
            $all_create_item = array_column($all_create_item, 'node_department_id', 'staff_info_id');

            $log .= '共 ' . count($all_create_item) . ' 创建人在HR员工表;' . PHP_EOL;

            // 合同相关表逐条记录更新
            // 其他合同
            $_node_dept_id_null_count = 0;
            $_save_fail_count = 0;
            $_success_count = 0;
            foreach ($contract_models as $c_models) {
                $_node_dept_id = $all_create_item[$c_models->create_id] ?? null;
                if (is_null($_node_dept_id)) {
                    $_node_dept_id_null_count++;
                    continue;
                }

                if ($c_models->i_update(['create_department_id' => $_node_dept_id]) === false) {
                    $_save_fail_count++;
                    continue;
                }

                $_success_count++;
            }

            $log .= PHP_EOL . '其他合同更新结果' . PHP_EOL;
            $log .= '创建人不在HR员工表的记录数 ' . $_node_dept_id_null_count . PHP_EOL;
            $log .= '更新失败的记录数 ' . $_save_fail_count . PHP_EOL;
            $log .= '更新成功的记录数 ' . $_success_count . PHP_EOL;

            sleep(10);

            // 租房合同
            $_rend_node_dept_id_null_count = 0;
            $_rend_save_fail_count = 0;
            $_rend_success_count = 0;
            foreach ($rent_contract_models as $r_models) {
                $_node_dept_id = $all_create_item[$r_models->contract_leader_id] ?? null;
                if (is_null($_node_dept_id)) {
                    $_rend_node_dept_id_null_count++;
                    continue;
                }

                if ($r_models->i_update(['create_department_id' => $_node_dept_id]) === false) {
                    $_rend_save_fail_count++;
                    continue;
                }

                $_rend_success_count++;
            }

            $log .= PHP_EOL . '租房合同更新结果' . PHP_EOL;
            $log .= '创建人不在HR员工表的记录数 ' . $_rend_node_dept_id_null_count . PHP_EOL;
            $log .= '更新失败的记录数 ' . $_rend_save_fail_count . PHP_EOL;
            $log .= '更新成功的记录数 ' . $_rend_success_count . PHP_EOL;

            sleep(10);

            // 归档合同
            $_archive_node_dept_id_null_count = 0;
            $_archive_save_fail_count = 0;
            $_archive_success_count = 0;
            foreach ($archive_contract_models as $a_models) {
                $_node_dept_id = $all_create_item[$a_models->create_id] ?? null;
                if (is_null($_node_dept_id)) {
                    $_archive_node_dept_id_null_count++;
                    continue;
                }

                if ($a_models->i_update(['create_department_id' => $_node_dept_id]) === false) {
                    $_archive_save_fail_count++;
                    continue;
                }

                $_archive_success_count++;
            }

            $log .= PHP_EOL . '归档合同更新结果' . PHP_EOL;
            $log .= '创建人不在HR员工表的记录数 ' . $_archive_node_dept_id_null_count . PHP_EOL;
            $log .= '更新失败的记录数 ' . $_archive_save_fail_count . PHP_EOL;
            $log .= '更新成功的记录数 ' . $_archive_success_count . PHP_EOL;

        } catch (ValidationException $e) {
            $log .= $e->getMessage() . PHP_EOL;

        } catch (Exception $e) {
            $log .= $e->getMessage() . PHP_EOL;
        }

        $log .= '结束时间: ' . date('Y-m-d H:i:s') . PHP_EOL;

        $this->logger->info($log);
        exit($log);
    }

    /**
     * 合同未归档提醒-法务
     * 说明: 每周一当地时间9点,给法务发送邮件提醒所有未归档的合同
     * 未归档合同取值范围:
     * 1.归档表中 其他合同,租房合同,GPMD合同 未归档数据
     * 2.合同表中 审批通过且未进入归档表中的数据(其他合同需要点击下载时进入归档表,租房合同和GPMD合同终审通过就会进入归档表,所有只有其他合同会存在这种数据)
     * php cli.php contract not_achieve_remind_email
     */
    public function not_achieve_remind_emailAction()
    {
        //上次进程是否结束
        $process_name = str_replace('Action', '', __FUNCTION__);
        $this->check_process($process_name);
        try {
            //查询超过30天的未归档数据 ContractEnums::CONTRACT_ARCHIVE_STATUS_PENDING
            $now_time = time();
            $archive_time = strtotime('-30 days', $now_time);
            $archive_pending_other = ContractArchiveRepository::getInstance()->getNoArchiveContractArchive();
            $archive_pending_renting = ContractArchiveRepository::getInstance()->getNoArchiveContractArchiveRenting();
            $archive_pending_data = array_merge($archive_pending_other, $archive_pending_renting);
            //取其他合同超过30天没有进入归档表的
            $archive_not_data = ContractArchiveRepository::getInstance()->getNoArchiveContract();
            //所有符合条件的合同
            $archive_all_data = array_merge($archive_pending_data, $archive_not_data);
            $time_out_data = [];
            //设置翻译
            self::setLanguage('en');
            //统计数据
            //合同分类map
            $contract_category_map = EnumsService::getInstance()->getContractCategorysMap();
            //查部门名称
            $node_department_ids = array_column($archive_all_data, 'node_department_id');
            $node_department_ids = array_values(array_unique($node_department_ids));
            $department_data = (new DepartmentRepository())->getDepartmentByIds($node_department_ids, 2);
            $excel_data = [];
            foreach ($archive_all_data as $k => &$archive_value) {
                //其他合同的合同类型
                $archive_value['template_title'] = '';
                if (!empty($archive_value['template_id'])) {
                    //直属分类信息
                    $contract_category_info = $contract_category_map[$archive_value['template_id']] ?? [];
                    $archive_value['template_title'] = $contract_category_info['label'] ?? '';
                    //若直属分类是二级分类, 则需拼接一级分类
                    if (isset($contract_category_info['level']) && $contract_category_info['level'] > 1) {
                        $archive_value['template_title'] = $contract_category_map[$contract_category_info['ancestry_id']]['label'] . '/' . $archive_value['template_title'];
                    }
                }
                //合同负责人所在部门
                $archive_value['create_department_text'] = $department_data[$archive_value['node_department_id']]['name'] ?? '';
                if (strtotime($archive_value['approved_at']) <= $archive_time) {
                    $time_out_data[] = $archive_value;
                }
                //合同分类
                $archive_value['contract_type_text'] = self::$t->_(ContractEnums::$contract_type_items[$archive_value['contract_type']]);
                $excel_data[] = [
                    $k,
                    $archive_value['cno'],
                    $archive_value['contract_type_text'],
                    $archive_value['template_title'],
                    $archive_value['create_department_text'],
                    $archive_value['approved_at']
                ];
            }
            //导出excel
            $header = [
                static::$t->_('not_achieve_remind_email_excel_no'),
                static::$t->_('not_achieve_remind_email_excel_contract_no'),
                static::$t->_('not_achieve_remind_email_excel_contract_type'),
                static::$t->_('not_achieve_remind_email_excel_contract_template'),
                static::$t->_('not_achieve_remind_email_excel_contract_department'),
                static::$t->_('not_achieve_remind_email_excel_contract_approved_at'),
            ];
            $file_name = 'not_achieve_data_' . date('Ymd') . '.xlsx';
            $export_excel = (new BaseService())->exportExcel($header, $excel_data, $file_name);
            if (!$export_excel || !isset($export_excel['data'])) {
                throw new Exception('生成excel失败 返回结果:' . json_encode($export_excel, JSON_UNESCAPED_UNICODE));
            }
            //所有未归档
            $all_count = count($archive_all_data);
            //超过90天的未归档
            $time_out_count = count($time_out_data);

            //发邮件
            $str_emails = EnvModel::getEnvByCode('not_achieve_data_legal_email');
            if (empty($str_emails)) {
                throw new Exception('法务部邮箱为空');
            }
            $emails = explode(',', $str_emails);
            $title = get_country_code() . static::$t->_('not_achieve_remind_email_title');
            $content = static::$t->_('not_achieve_remind_email_content', ['datetime' => date('Y-m-d H:i:s', $now_time), 'all_count' => $all_count, 'time_out_count' => $time_out_count]);
            $html = '<p>' . $content . '</p><br/>';
            $html .= '<p>' . $export_excel['data'] . '</p>';
            $send_result = $this->mailer->sendAsync($emails, $title, $html);
            if (!$send_result) {
                throw new Exception('邮件发送失败 result:' . json_encode($send_result) . '; emails:' . json_encode($emails) . '; title:' . $title . '; content:' . $content);
            }
            $log = '邮件发送成功 emails:' . json_encode($emails) . '; title:' . $title . '; content:' . $content;
        } catch (Exception $e) {
            $log = '合同未归档提醒发邮件-失败: message=' . $e->getMessage() . '; trace=' . $e->getTraceAsString() . ', 执行时间:' . date('Y-m-d H:i:s');
            $this->logger->error($log);
            echo $log . PHP_EOL;
            exit();
        }
        if (empty($error_data)) {
            $this->logger->info($log);
        } else {
            $this->logger->warning($log);
        }
        exit($log);
    }

    /**
     * 合同未归档提醒-给合同负责人
     * 说明: 每周一当地时间9点,给合同负责人发送超30天未归档的合同数量
     * 未归档合同取值范围:
     * 1.归档表中 其他合同,租房合同,GPMD合同 超过30天未归档数据 以合同负责人维度统计数量
     * 2.合同表中 审批通过且未进入归档表中 超过30天的未归档数据  以合同负责人维度统计数量
     * 合同负责人取值说明:
     * 1.其他合同,GPMD合同 用合同的创建人 create_id
     * 2.租房合同 用合同负责人字段 contract_leader_id
     * 合同负责人上级取值说明:
     * 合同负责人所属部门(node_department_id) 在sys_department表中的manager_id
     * php cli.php contract not_achieve_remind_staff_email
     */
    public function not_achieve_remind_staff_emailAction()
    {
        //上次进程是否结束
        $process_name = str_replace('Action', '', __FUNCTION__);
        $this->check_process($process_name);
        try {
            //查询超过30天的未归档数据 ContractEnums::CONTRACT_ARCHIVE_STATUS_PENDING
            $now_time = time();
            $archive_time = strtotime('-30 days', $now_time);
            $time_out_date = date('Y-m-d H:i:s', $archive_time);
            $archive_pending_data_other = ContractArchiveRepository::getInstance()->getTimeOutContractArchive($time_out_date);
            $archive_pending_data_renting = ContractArchiveRepository::getInstance()->getTimeOutContractArchiveRenting($time_out_date);
            //取其他合同超过30天没有进入归档表的
            $archive_not_data = ContractArchiveRepository::getInstance()->getTimeOutContract($time_out_date);
            $archive_pending_data_all = array_merge($archive_pending_data_other, $archive_pending_data_renting, $archive_not_data);
            //合并员工统计
            $all_staff_count = [];
            foreach ($archive_pending_data_all as $renting_value) {
                if (key_exists($renting_value['create_id'], $all_staff_count)) {
                    $all_staff_count[$renting_value['create_id']] = (string)($all_staff_count[$renting_value['create_id']] + $renting_value['count']);
                } else {
                    $all_staff_count[$renting_value['create_id']] = $renting_value['count'];
                }
            }
            //获取员工email
            $staff_ids = array_keys($all_staff_count);
            $staff_list = (new HrStaffRepository())->getStaffListByStaffIds($staff_ids);
            $staff_email_list = array_column($staff_list, 'email', 'staff_info_id');
            //获取员工部门负责人email
            $department_ids = array_values(array_unique(array_column($staff_list, 'node_department_id')));
            $department_list = (new DepartmentRepository())->getDepartmentByIds($department_ids);
            $department_manager = array_values(array_unique(array_column($department_list, 'manager_id')));
            $manager_list = (new HrStaffRepository())->getStaffListByStaffIds($department_manager);
            //部门负责人->邮箱地址
            $manager_email_list = array_column($manager_list, 'email', 'staff_info_id');
            //合同负责人->部门
            $staff_department_list = array_column($staff_list, 'node_department_id', 'staff_info_id');
            //部门id->部门负责人
            $department_manager_kv = array_column($department_list, 'manager_id', 'id');
            //最终得到合同负责人->部门负责人邮箱地址$staff_manager_email
            $staff_manager_email = [];
            foreach ($staff_department_list as $staff => $department_id) {
                if (key_exists($department_id, $department_manager_kv)) {
                    $manager_id = $department_manager_kv[$department_id];
                    $manager_email = $manager_email_list[$manager_id] ?? '';
                    $staff_manager_email[$staff] = $manager_email;
                }
            }
            //员工对应的部门负责人邮箱
            //设置翻译
            self::setLanguage('en');
            //发送邮件
            $email_title = '[' . get_country_code() . ']' . static::$t->_('not_achieve_remind_staff_email_title');
            $error_data = $success_data = [];
            foreach ($all_staff_count as $staff_id => $count) {
                if (key_exists($staff_id, $staff_email_list) && !empty($staff_email_list[$staff_id])) {
                    $email_content = static::$t->_('not_achieve_remind_staff_email_content', ['datetime' => $time_out_date, 'count' => $count]);
                    $email_html = '<p>' . $email_content . '</p>';
                    $cc = [];
                    if (isset($staff_manager_email[$staff_id]) && !empty($staff_manager_email[$staff_id])) {
                        $cc = [$staff_manager_email[$staff_id]];
                    }
                    $send_result = $this->mailer->sendAsync($staff_email_list[$staff_id], $email_title, $email_html, [], $cc);
                    if (!$send_result) {
                        $error_data[] = '邮件发送失败 result:' . json_encode($send_result) . '; emails:' . $staff_email_list[$staff_id] . 'cc:' . json_encode($cc) . '; title:' . $email_title . '; content:' . $email_html;
                    }
                    $success_data[] = '邮件发送成功 result:' . json_encode($send_result) . 'emails:' . $staff_email_list[$staff_id] . 'cc:' . json_encode($cc) . '; title:' . $email_title . '; content:' . $email_html;
                } else {
                    $success_data[] = '员工邮箱为空 staff_info_id=' . $staff_id;
                }
            }
            $log = '合同未归档提醒-给合同负责人发邮件-成功: 共' . count($all_staff_count) . '个员工; 成功发送' . count($success_data) . '个, 发送失败' . count($error_data) . '个, 执行时间:' . date('Y-m-d H:i:s');
        } catch (Exception $e) {
            $log = '合同未归档提醒-给合同负责人发邮件-失败: message=' . $e->getMessage() . '; trace=' . $e->getTraceAsString() . ', 执行时间:' . date('Y-m-d H:i:s');
            $this->logger->error($log);
            echo $log . PHP_EOL;
            exit();
        }
        if (empty($error_data)) {
            $this->logger->info('合同未归档提醒-给合同负责人-发送成功' . json_encode($success_data, JSON_UNESCAPED_UNICODE));
        } else {
            $this->logger->warning('合同未归档提醒-给合同负责人-发送失败' . json_encode($error_data, JSON_UNESCAPED_UNICODE));
        }
        exit($log);
    }


    /**
     * 电子合同
     * 子文件状态维护
     * */
    public function deal_subfile_stateAction()
    {
        //上次进程是否结束
        $process_name = str_replace('Action', '', __FUNCTION__);
        $this->check_process($process_name);
        echo 'begin' . date('Y-m-d H:i:s') . PHP_EOL;

        try {
            $file_lists = ContractSubFileModel::Find([
                'conditions' => 'state =:state:',
                'bind' => ['state' => ContractEnums::SUB_FILE_STATUS_1],
            ]);

            $i = 0;
            foreach ($file_lists as $item) {
                $state = ContractEnums::SUB_FILE_STATUS_1;
                if (date('Y-m-d H:i:s') > $item->outage_time) {
                    $state = ContractEnums::SUB_FILE_STATUS_2;
                }

                if (date('Y-m-d H:i:s') < $item->activation_time) {
                    $state = ContractEnums::SUB_FILE_STATUS_3;
                }

                if (date('Y-m-d H:i:s') < $item->outage_time && date('Y-m-d H:i:s') > $item->activation_time) {
                    $state = ContractEnums::SUB_FILE_STATUS_1;
                }

                if ($item->state != $state) {
                    $i++;

                    $bool = $item->i_update(['state' => $state, 'updated_at' => date('Y-m-d H:i:s')]);
                    if ($bool === false) {
                        $this->logger->warning('电子合同状态维护失败原因：' . get_data_object_error_msg($item) . '数据:id' . $item->id);
                    }
                }
            }
            echo '本次处理成功' . $i . '条';
        } catch (Exception $e) {
            $log = '电子合同子文件使用状态维护失败: message=' . $e->getMessage() . '; trace=' . $e->getTraceAsString() . ', 执行时间:' . date('Y-m-d H:i:s');
            $this->logger->warning($log);
            echo $log . PHP_EOL;
        }

        echo 'end' . date('Y-m-d H:i:s') . PHP_EOL;
        exit;

    }

    /**
     * 电子合同
     * 合同模版版本状态维护
     * */
    public function deal_contract_template_versionAction()
    {
        //上次进程是否结束
        $process_name = str_replace('Action', '', __FUNCTION__);
        $this->check_process($process_name);
        echo 'begin' . date('Y-m-d H:i:s') . PHP_EOL;

        try {
            $contract_template_lists = ContractTemplateVersionModel::Find([
                'conditions' => 'state =:state:',
                'bind' => ['state' => ContractEnums::CONTRACT_TEMPLATE_STATE_2],
            ]);
            $i = 0;
            foreach ($contract_template_lists as $item) {

                $state = ContractEnums::CONTRACT_TEMPLATE_STATE_1;
                if (date('Y-m-d H:i:s') > $item->template_downtime) {
                    $state = ContractEnums::CONTRACT_TEMPLATE_STATE_3;
                }

                if (date('Y-m-d H:i:s') < $item->template_activation_time) {
                    $state = ContractEnums::CONTRACT_TEMPLATE_STATE_1;
                }

                if (date('Y-m-d H:i:s') < $item->template_downtime && date('Y-m-d H:i:s') > $item->template_activation_time) {
                    $state = ContractEnums::CONTRACT_TEMPLATE_STATE_2;
                    $i++;
                }

                if ($item->state != $state) {
                    $i++;
                    $bool = $item->i_update(['state' => $state, 'updated_at' => date('Y-m-d H:i:s')]);
                    if ($bool === false) {
                        $this->logger->warning('电子合同模板版本状态维护失败原因：' . get_data_object_error_msg($item) . '数据:id' . $item->id);
                    }
                }
            }


            echo '本次处理成功' . $i . '条';
        } catch (Exception $e) {
            $log = '电子合同模版版本使用状态维护失败: message=' . $e->getMessage() . '; trace=' . $e->getTraceAsString() . ', 执行时间:' . date('Y-m-d H:i:s');
            $this->logger->warning($log);
            echo $log . PHP_EOL;
        }

        echo 'end' . date('Y-m-d H:i:s') . PHP_EOL;
        exit;
    }

    /**
     * FH 电子合同创建
     *
     * @param array $params 参数1: 合同制作人   参数2: 处理Excel的第几行数据(为空则处理所有)
     * php app/cli.php contract add_fh_electronic 27186 2
     */
    public function add_fh_electronicAction($params = [])
    {
        $log = '开始时间: ' . date('Y-m-d H:i:s') . PHP_EOL;
        $log .= '初始内存: ' . memory_usage() . PHP_EOL;

        $logger_type = 'info';

        try {
            $uid = $params[0] ?? 0;
            $make_excel_data_row = $params[1] ?? '';
            $log .= "处理的Excel行号: {$make_excel_data_row}" . PHP_EOL;

            $log .= '制作人: ' . $uid . PHP_EOL;
            if (empty($uid)) {
                throw new ValidationException("制作人参数有误, uid={$uid}", ErrCode::$VALIDATE_ERROR);
            }

            $excel_url = EnumsService::getInstance()->getSettingEnvValue('fh_e_contract_auto_add_init_data_file');
            $log .= 'Excel Url: ' . $excel_url . PHP_EOL;
            if (empty($excel_url)) {
                throw new ValidationException("Excel参数有误, url={$excel_url}", ErrCode::$VALIDATE_ERROR);
            }

            // 1. 获取制作人信息
            $user_info = (new UserService())->getLoginUser($uid);
            if (empty($user_info)) {
                throw new ValidationException("制作人员工信息不存在, uid={$uid}", ErrCode::$VALIDATE_ERROR);
            }

            // 2. 获取电子合同Excel原数据
            $tmp_dir = sys_get_temp_dir() . '/';
            $file_name = 'init_e_data_' . date('YmdHis') . '_' . mt_rand() . '.xlsx';

            // 2.1 生成Excel本地文件
            $file_path = $this->getExcelFilePath($excel_url, $tmp_dir, $file_name);

            // 2.2 读取文件
            $config = ['path' => $tmp_dir];
            $excel = new \Vtiful\Kernel\Excel($config);

            $excel_data = $excel->openFile($file_name)
                ->openSheet()
                ->setSkipRows(1)
                ->setType([
                    7 => \Vtiful\Kernel\Excel::TYPE_TIMESTAMP,
                    13 => \Vtiful\Kernel\Excel::TYPE_TIMESTAMP,
                    14 => \Vtiful\Kernel\Excel::TYPE_TIMESTAMP,
                    21 => \Vtiful\Kernel\Excel::TYPE_TIMESTAMP,
                ])
                ->getSheetData();
            unlink($file_path);
            if (empty($excel_data)) {
                throw new Exception("file is empty, file_path={$file_path}", ErrCode::$BUSINESS_ERROR);
            }

            $log .= 'Excel 总行数: ' . count($excel_data) . PHP_EOL;
            $log .= '当前内存: ' . memory_usage() . PHP_EOL;

            // 3. 构造入库数据
            $add_success_count = 0;
            $add_fail_count = 0;
            $add_fail_row_line_log = '';

            $data_row_num = 1;

            // 根据模板编号 获取 模板ID
            $template_nos = array_values(array_unique(array_column($excel_data, '3')));
            $tpl_list = ContractTemplateVersionModel::find([
                'conditions' => 'template_no IN ({template_nos:array})',
                'bind' => ['template_nos' => $template_nos],
                'columns' => ['id', 'template_no']
            ])->toArray();
            if (empty($tpl_list)) {
                throw new ValidationException('模板信息不存在, ' . json_encode($template_nos, JSON_UNESCAPED_UNICODE), ErrCode::$VALIDATE_ERROR);
            }

            $log .= '模板编号与ID Map: ' . json_encode($tpl_list, JSON_UNESCAPED_UNICODE) . PHP_EOL;

            // 初始化系统语言环境
            static::setLanguage('th');

            // 获取模板信息
            $tpl_no_info_map = [];
            foreach ($tpl_list as $tpl_value) {
                $tpl_info = ContractTemplateVersionService::getInstance()->getDetail(['id' => $tpl_value['id']]);
                $tpl_no_info_map[$tpl_value['template_no']] = $tpl_info['data'];
            }

            // 规则表单项字段 与 Excel索引 的映射关系(填表单取值用)
            $rule_field_index_map = [
                'contract_name' => [
                    'index' => 4,
                    'attr' => [
                        'clearable' => true
                    ]
                ],

                'franchisee_name' => [
                    'index' => 5,
                    'attr' => [
                        'clearable' => true
                    ]
                ],

                'franchisee_type' => [
                    'index' => 6,
                    'attr' => [
                        'clearable' => false,
                        'multiple' => false
                    ]
                ],

                'sign_account_opening_date' => [
                    'index' => 7,
                    'attr' => [
                        'clearable' => true
                    ]
                ],

                'sign_franchisee_name' => [
                    'index' => 8,
                    'attr' => [
                        'clearable' => true
                    ]
                ],

                'sign_franchisee_id_number' => [
                    'index' => 9,
                    'attr' => [
                        'clearable' => true
                    ]
                ],

                'sign_address' => [
                    'index' => 10,
                    'attr' => [
                        'clearable' => true
                    ]
                ],

                'sign_email_address' => [
                    'index' => 11,
                    'attr' => [
                        'clearable' => true
                    ]
                ],

                'sign_contract_period' => [
                    'index' => 12,
                    'attr' => [
                        'clearable' => true
                    ]
                ],

                'sign_contract_start_date' => [
                    'index' => 13,
                    'attr' => [
                        'clearable' => true
                    ]
                ],

                'sign_contract_end_date' => [
                    'index' => 14,
                    'attr' => [
                        'clearable' => true
                    ]
                ],

                'sign_branch_address' => [
                    'index' => 15,
                    'attr' => [
                        'clearable' => true
                    ]
                ],

                'sign_deposit' => [
                    'index' => 16,
                    'attr' => [
                        'clearable' => true
                    ]
                ],

                'sign_dot_radius' => [
                    'index' => 17,
                    'attr' => [
                        'clearable' => true,
                        'multiple' => false
                    ]
                ],

                'sign_superior_address' => [
                    'index' => 18,
                    'attr' => [
                        'clearable' => true
                    ]
                ],

                'sign_service_charge' => [
                    'index' => 19,
                    'attr' => [
                        'clearable' => true
                    ]
                ],

                'sign_franchise_fee' => [
                    'index' => 20,
                    'attr' => [
                        'clearable' => true
                    ]
                ],

                'sign_account_opening_date1' => [
                    'index' => 21,
                    'attr' => [
                        'clearable' => true
                    ]
                ],

                'delivery_fh_principal' => [
                    'index' => 22,
                    'attr' => [
                        'clearable' => true,
                        'multiple' => false
                    ]
                ],

                'delivery_company_name' => [
                    'index' => 23,
                    'attr' => [
                        'clearable' => true
                    ]
                ],

                'franchisee_name1' => [
                    'index' => 24,
                    'attr' => [
                        'clearable' => true
                    ]
                ],

                'sign_branch_address1' => [
                    'index' => 25,
                    'attr' => [
                        'clearable' => true
                    ]
                ],

                'delivery_company_dbd' => [
                    'index' => 26,
                    'attr' => [
                        'clearable' => true
                    ]
                ]
            ];

            // 相关枚举映射
            $franchisee_type_map = [
                '个人' => 1,
                '企业' => 2
            ];

            // 签约信息半径映射
            $enums_data = EnumsService::getInstance()->getSettingEnvValueMap('pmd_form_rule_select_enums');
            $sign_dot_radius_map = $enums_data['sign_dot_radius'];
            $sign_dot_radius_map = array_column($sign_dot_radius_map, 'id', 'name');

            // 遍历待初始化数据, 逐条处理
            foreach ($excel_data as $row) {
                $data_row_num++;

                // 测试和验收用, 生成指定行号的电子合同
                if (!empty($make_excel_data_row) && $make_excel_data_row != $data_row_num) {
                    continue;
                }

                // 各字段去掉前后空格
                $row = trim_array($row);

                // 日期字段的转换
                // 签约信息: 开账号日期
                $row[7] = date('Y-m-d', $row[7]);

                // 合同开始日期
                $row[13] = date('Y-m-d', $row[13]);

                // 合同结束日期
                $row[14] = date('Y-m-d', $row[14]);

                // 送货约定信息: 开账户日期
                $row[21] = date('Y-m-d', $row[21]);

                // 加盟商类型
                $row[6] = $franchisee_type_map[$row[6]];

                // 网点半径
                $row[17] = $sign_dot_radius_map[$row[17]];

                // 获取电子合同对应的模板版本
                $tpl_info = $tpl_no_info_map[$row[3]] ?? [];
                if (empty($tpl_info)) {
                    $add_fail_row_line_log .= "Excel 第 {$data_row_num} 行 的 模板编号未找到对应的模板信息[no = {$row[3]}]" . PHP_EOL;
                    continue;
                }

                // 构造电子合同规则表单
                $form_rule = $tpl_info['form_rule'];
                foreach ($form_rule as $info_key => &$info_rules) {
                    foreach ($info_rules as &$rule) {
                        $field_info = $rule_field_index_map[$rule['field']] ?? [];
                        $rule['value'] = $row[$field_info['index']];
                        $rule = array_merge($rule, $field_info['attr']);
                    }
                }

                // 构造请求参数
                $add_params = [
                    'template_no' => $tpl_info['template_no'],
                    'contract_type' => $tpl_info['contract_type'],
                    'lang' => $tpl_info['lang'],
                    'business_review' => $tpl_info['business_review'],
                    'ver' => $tpl_info['ver'],
                    'form_rule' => $form_rule,
                    'file_url' => $tpl_info['file_url'],
                    'template_name' => $tpl_info['contract_name'],
                    'department_id' => $tpl_info['department_id'],
                    'contract_name' => $row[4],
                    'start_date' => $row[13],
                    'end_date' => $row[14],
                    'customer_name' => $row[8],
                    'is_completed' => ContractEnums::CONTRACT_IS_COMPLETED,
                ];

                // 创建电子合同
                $add_res = ContractElectronicService::getInstance()->add($add_params, $user_info);

                // 4. 记录入库后每个电子合同的关联数据
                if ($add_res['code'] == ErrCode::$SUCCESS) {
                    $add_success_count++;
                    $log_data = [
                        'excel_line' => $data_row_num,
                        'franchisee_no' => $row[0],
                        'franchisee_store_id' => $row[1],
                        'template_no' => $row[3],
                        'e_contract_no' => $add_res['data']['no'],
                        'e_contract_id' => $add_res['data']['id'],
                    ];

                    $this->logger->info('成功的电子合同日志: ' . json_encode($log_data, JSON_UNESCAPED_UNICODE));
                } else {
                    $add_fail_count++;
                    $add_fail_row_line_log .= "Excel 第 {$data_row_num} 行 生成合同失败, data = " . json_encode($row, JSON_UNESCAPED_UNICODE) . PHP_EOL;
                }
            }

            unset($excel_data);

            // 失败日志入表一份
            $fail_setting_model = SettingEnvModel::findFirst([
                'conditions' => 'code = :code:',
                'bind' => ['code' => 'fh_e_contract_auto_add_fail_log']
            ]);
            $fail_setting_model->save(['val' => $add_fail_row_line_log]);
            $log .= '当前内存: ' . memory_usage() . PHP_EOL;

            $log .= "制作成功: {$add_success_count} 个" . PHP_EOL;
            $log .= "制作失败: {$add_fail_count} 个" . PHP_EOL;
            $log .= "失败的Excel数据日志: {$add_fail_row_line_log}" . PHP_EOL;

        } catch (ValidationException $e) {
            $log .= '提醒: ' . $e->getMessage() . PHP_EOL;

        } catch (Exception $e) {
            $log .= '异常: ' . $e->getMessage() . PHP_EOL;
            $logger_type = 'notice';
        }

        $log .= '结束时间: ' . date('Y-m-d H:i:s') . PHP_EOL;
        $log .= '当前内存: ' . memory_usage() . PHP_EOL;
        $this->logger->$logger_type($log);
        exit($log);
    }

    /**
     * FH 合同申请 及 审批
     *
     * @param array $params 参数1: 合同申请人; 参数2: 创建Excel中指定行的合同数据; 参数3: 审批人
     *
     * php app/cli.php contract apply_fh_contract 27186 1 22568
     *
     */
    public function apply_fh_contractAction($params = [])
    {
        $log = '开始时间: ' . date('Y-m-d H:i:s') . PHP_EOL;
        $log .= '初始内存: ' . memory_usage() . PHP_EOL;

        $logger_type = 'info';

        try {
            $uid = $params[0] ?? 0;
            $make_excel_data_row = $params[1] ?? '';
            $auditor_id = $params[2] ?? 0;

            $log .= '申请人: ' . $uid . PHP_EOL;
            if (empty($uid)) {
                throw new ValidationException("申请人参数有误, uid={$uid}", ErrCode::$VALIDATE_ERROR);
            }

            $log .= '审批人: ' . $auditor_id . PHP_EOL;
            if (empty($auditor_id)) {
                throw new ValidationException("审批人参数有误, uid={$auditor_id}", ErrCode::$VALIDATE_ERROR);
            }

            $log .= "处理的Excel行号: {$make_excel_data_row}" . PHP_EOL;
            $excel_url = EnumsService::getInstance()->getSettingEnvValue('fh_e_contract_auto_apply_data_file');
            $log .= 'Excel Url: ' . $excel_url . PHP_EOL;
            if (empty($excel_url)) {
                throw new ValidationException("Excel参数有误, url={$excel_url}", ErrCode::$VALIDATE_ERROR);
            }

            // 1. 获取申请人信息
            $user_info = (new UserService())->getLoginUser($uid);
            if (empty($user_info)) {
                throw new ValidationException("申请人信息不存在, uid={$uid}", ErrCode::$VALIDATE_ERROR);
            }

            // 获取审批人信息
            $auditor_info = (new UserService())->getLoginUser($auditor_id);
            if (empty($auditor_info)) {
                throw new ValidationException("审批人信息不存在, uid={$auditor_id}", ErrCode::$VALIDATE_ERROR);
            }

            // 2. 获取电子合同Excel原数据
            $tmp_dir = sys_get_temp_dir() . '/';
            $file_name = 'e_apply_data_' . date('YmdHis') . '_' . mt_rand() . '.xlsx';

            // 2.1 生成Excel本地文件
            $file_path = $this->getExcelFilePath($excel_url, $tmp_dir, $file_name);

            // 2.2 读取文件
            $config = ['path' => $tmp_dir];
            $excel = new \Vtiful\Kernel\Excel($config);

            $excel_data = $excel->openFile($file_name)
                ->openSheet()
                ->setSkipRows(1)
                ->setType([
                    18 => \Vtiful\Kernel\Excel::TYPE_TIMESTAMP,
                    19 => \Vtiful\Kernel\Excel::TYPE_TIMESTAMP,
                ])
                ->getSheetData();
            unlink($file_path);
            if (empty($excel_data)) {
                throw new Exception("file is empty, file_path={$file_path}", ErrCode::$BUSINESS_ERROR);
            }

            $log .= 'Excel 总行数: ' . count($excel_data) . PHP_EOL;
            $log .= '当前内存: ' . memory_usage() . PHP_EOL;

            // 初始化系统语言环境
            static::setLanguage('th');

            // 是否为集团间合同枚举
            $is_group_company_map = [
                '是' => 1,
                '否' => 0
            ];

            // 是否为供应商枚举
            $is_vendor_map = [
                '是' => 1,
                '否' => 0
            ];

            // 加盟商类型枚举
            $franchisee_type_map = [
                '个人' => 1,
                '企业' => 2
            ];

            // 合同语言枚举
            $contract_lang_map = [
                '泰文' => 'th',
                '英文' => 'en',
                '中文' => 'zh-CN',
            ];

            // 付款币种枚举
            $payment_currency_map = [
                'THB' => 1,
                'USD' => 2,
                'CNY' => 3,
                'PHP' => 4,
                'LAK' => 5,
                'MYR' => 6,
                'IDR' => 7,
                'VND' => 8,
                'EUR' => 9
            ];

            // 合同主从属性枚举
            $is_master_map = [
                '主合同' => 1,
                '附属合同' => 2
            ];

            // 合同间所属公司
            $contract_company_map = ContractCompanyModel::getCompanyByCode();
            $contract_company_map = array_column($contract_company_map, 'company_code', 'company_name');

            // 合同分类
            $contract_template_map = [
                '运营合同' => 7,
                '市场合同' => 3,
                '其他合同' => 9
            ];

            // 处理Excel数据
            $data_row_num = 1;
            $apply_success_count = 0;
            $apply_fail_count = 0;

            $approvel_success_count = 0;
            $approvel_fail_count = 0;

            $apply_fail_log = '';
            $approvel_fail_log = '';

            $contract_flow_service = new ContractFlowService();

            // 遍历待初始化数据, 逐条处理
            foreach ($excel_data as $row) {
                $data_row_num++;

                // 测试和验收用, 生成指定行号的电子合同
                if (!empty($make_excel_data_row) && $make_excel_data_row != $data_row_num) {
                    continue;
                }

                // 各字段去掉前后空格
                $row = trim_array($row);

                // 合同生效日期
                $row[18] = date('Y-m-d', $row[18]);

                // 合同到期日期
                $row[19] = date('Y-m-d', $row[19]);

                // 3. 构造入库数据
                // 3.1 获取电子合同默认值
                $get_e_contract_default = ContractElectronicService::getInstance()->contractDefault(['id' => $row[1]]);

                // 3.2
                $apply_params = [
                    // Excel数据
                    'cname' => $row[3],// 合同名称
                    'cno' => $row[4],// 合同编号
                    'company_code' => $contract_company_map[$row[5]], // 5 合同所属公司: Flash Home Operation => 15
                    'is_group_contract' => $is_group_company_map[$row[6]],// 是否集团公司间合同: 0-否; 1-是
                    'is_vendor' => $is_vendor_map[$row[7]],// 是否为供应商: 0-否; 1-是
                    'customer_company_name' => $row[8],// 客户(加盟商)公司名称
                    'template_id' => $contract_template_map[$row[9]],// 合同分类ID 运营合同 => 7
                    'franchisee_type' => $franchisee_type_map[$row[10]],// 加盟商类型
                    'franchisee_id' => $row[11],// 加盟商ID
                    'store_id' => $row[12],// 网点CODE
                    'franchisee_name' => $row[13],// 加盟商负责人姓名
                    'lang' => $contract_lang_map[$row[14]],// 合同语言
                    'payment_currency' => $payment_currency_map[$row[15]],// 付款币种
                    'amount' => $row[16], // 合同金额
                    'is_master' => $is_master_map[$row[17]], // 合同主从属性 1-主合同
                    'effective_date' => $row[18],// 合同生效日期
                    'expiry_date' => $row[19],// 合同到期日期
                    'contract_desc' => $row[20], // 合同说明
                    'contract_file_arr' => $get_e_contract_default['contract_file'], // 合同正文附件
                    'attachment_arr' => [],// 其他附件, 默认空

                    // 其他数据
                    'is_agreement' => 1,// 固定值: 特殊合同是否约定: 1-否; 2-是
                    'apply_staff_department' => $get_e_contract_default['apply_staff_department'],// 固定值: 1-Flash Home
                    'contract_storage_type' => ContractEnums::CONTRACT_STORAGE_TYPE_2,// 固定值: 1-纸质合同; 2-电子合同
                    'electronic_id' => $get_e_contract_default['electronic_id']// 关联的电子合同ID
                ];

                // 3.3 申请数据入库
                $res = ContractAddService::getInstance()->one($apply_params, $user_info);
                if ($res['code'] != ErrCode::$SUCCESS || empty($res['data']['id'])) {
                    // 合同申请 处理失败
                    $_apply_fail_log = "合同申请失败: Excel 行号={$data_row_num}, 合同申请参数=" . json_encode($apply_params, JSON_UNESCAPED_UNICODE);
                    $this->logger->info($_apply_fail_log);
                    $apply_fail_log .= $_apply_fail_log . PHP_EOL;
                    $apply_fail_count++;
                    continue;
                }

                $apply_success_count++;
                $this->logger->info("合同申请成功: {$apply_params['cno']} - {$res['data']['id']} - {$apply_params['franchisee_id']} - {$apply_params['store_id']}");

                // 3.4 申请单据审批 + 统计申请 + 审批结果
                $flow_approve_res = $contract_flow_service->approve($res['data']['id'], '', $auditor_info);
                if ($flow_approve_res['code'] == ErrCode::$SUCCESS) {
                    $this->logger->info("合同审批成功: {$apply_params['cno']} - {$res['data']['id']} - {$auditor_info['id']}");
                    $approvel_success_count++;
                } else {
                    // 审批失败
                    $_approvel_fail_log = "合同审批失败: {$apply_params['cno']} - {$res['data']['id']} - {$auditor_info['id']}";
                    $this->logger->info($_approvel_fail_log);
                    $approvel_fail_log .= $_approvel_fail_log . PHP_EOL;
                    $approvel_fail_count++;
                }
            }

            unset($excel_data);
            $log .= '当前内存: ' . memory_usage() . PHP_EOL;

            $log .= "申请成功: {$apply_success_count} 个" . PHP_EOL;
            $log .= "申请失败: {$apply_fail_count} 个" . PHP_EOL;
            $log .= "审批成功: {$approvel_success_count} 个" . PHP_EOL;
            $log .= "审批失败: {$approvel_fail_count} 个" . PHP_EOL;
            $log .= "申请失败的日志: {$apply_fail_log}" . PHP_EOL;
            $log .= "审批失败的日志: {$approvel_fail_log}" . PHP_EOL;

        } catch (ValidationException $e) {
            $log .= '提醒: ' . $e->getMessage() . PHP_EOL;

        } catch (Exception $e) {
            $log .= '异常: ' . $e->getMessage() . PHP_EOL;
            $logger_type = 'notice';
        }

        $log .= '结束时间: ' . date('Y-m-d H:i:s') . PHP_EOL;
        $log .= '当前内存: ' . memory_usage() . PHP_EOL;
        $this->logger->$logger_type($log);
        exit($log);
    }

    /**
     * FH 合同发起签约
     *
     * @param array $params 参数1: 电子合同ID
     * php app/cli.php contract sign_fh_contract 6
     */
    public function sign_fh_contractAction($params = [])
    {
        $log = '开始时间: ' . date('Y-m-d H:i:s') . PHP_EOL;
        $log .= '初始内存: ' . memory_usage() . PHP_EOL;

        $logger_type = 'info';

        try {
            $e_id = $params[0] ?? 0;

            $log .= '电子合同ID: ' . $e_id . PHP_EOL;

            // 1. 获取电子合同Excel原数据
            $excel_url = EnumsService::getInstance()->getSettingEnvValue('fh_e_contract_auto_sign_data_file');
            $log .= 'Excel Url: ' . $excel_url . PHP_EOL;
            if (empty($excel_url)) {
                throw new ValidationException("Excel参数有误, url={$excel_url}", ErrCode::$VALIDATE_ERROR);
            }

            // 2. 获取电子合同Excel原数据
            $tmp_dir = sys_get_temp_dir() . '/';
            $file_name = 'e_sign_data_' . date('YmdHis') . '_' . mt_rand() . '.xlsx';

            // 2.1 生成Excel本地文件
            $file_path = $this->getExcelFilePath($excel_url, $tmp_dir, $file_name);

            // 2.2 读取文件
            $config = ['path' => $tmp_dir];
            $excel = new \Vtiful\Kernel\Excel($config);

            $excel_data = $excel->openFile($file_name)
                ->openSheet()
                ->setSkipRows(1)
                ->getSheetData();
            unlink($file_path);
            if (empty($excel_data)) {
                throw new Exception("file is empty, file_path={$file_path}", ErrCode::$BUSINESS_ERROR);
            }

            $log .= 'Excel 总行数: ' . count($excel_data) . PHP_EOL;
            $log .= '当前内存: ' . memory_usage() . PHP_EOL;


            // 初始化系统语言环境
            static::setLanguage('th');

            // 2. 签字下发
            $data_row_num = 1;
            $success_count = 0;
            $fail_count = 0;
            $fail_log = '';
            foreach ($excel_data as $row) {
                $data_row_num++;

                // 各字段去掉前后空格
                $row = trim_array($row);

                // 测试和验收用, 生成指定行号的电子合同
                if (!empty($e_id) && $e_id != $row[4]) {
                    continue;
                }

                $sign_params = [
                    'customer_type' => ContractEnums::SIGN_CUSTOMER_TYPE_3,
                    'store_id' => $row[1],
                    'id' => $row[4]
                ];
                $this->logger->info("签字下发参数: Excel {$data_row_num} 行, 签字下发参数: " . json_encode($sign_params, JSON_UNESCAPED_UNICODE));
                $res = ContractElectronicService::getInstance()->signInitiate($sign_params);
                $this->logger->info("签字下发结果: " . json_encode($res, JSON_UNESCAPED_UNICODE));

                if ($res['code'] == ErrCode::$SUCCESS) {
                    $success_count++;
                } else {
                    $fail_count++;
                    $fail_log .= "Excel 行号: {$data_row_num}, 签字下发参数: " . json_encode($sign_params, JSON_UNESCAPED_UNICODE) . PHP_EOL;
                }
            }

            unset($excel_data);
            $log .= '当前内存: ' . memory_usage() . PHP_EOL;

            $log .= "签字下发成功: {$success_count} 个" . PHP_EOL;
            $log .= "签字下发失败: {$fail_count} 个" . PHP_EOL;
            $log .= "失败的日志: {$fail_log}" . PHP_EOL;

        } catch (ValidationException $e) {
            $log .= '提醒: ' . $e->getMessage() . PHP_EOL;

        } catch (Exception $e) {
            $log .= '异常: ' . $e->getMessage() . PHP_EOL;
            $logger_type = 'notice';
        }

        $log .= '结束时间: ' . date('Y-m-d H:i:s') . PHP_EOL;
        $log .= '当前内存: ' . memory_usage() . PHP_EOL;
        $this->logger->$logger_type($log);
        exit($log);
    }

    /**
     * 下载文件
     *
     * @param string $file_url
     * @param string $tmp_dir
     * @param string $file_name
     * @return mixed
     */
    protected function getExcelFilePath(string $file_url, string $tmp_dir = '', string $file_name = '')
    {
        // 1. 下载文件
        $tmp_dir = $tmp_dir ? $tmp_dir : sys_get_temp_dir() . '/';
        $file_name = $file_name ? $file_name : date('YmdHis') . '_' . mt_rand() . '.xlsx';
        ob_start();
        readfile($file_url);
        $file_data = ob_get_contents();
        ob_end_clean();
        $tmp_path = $tmp_dir . $file_name;
        if (file_put_contents($tmp_path, $file_data)) {
            return $tmp_path;
        } else {
            return '';
        }
    }

    /**
     * 提取电子合同的合同编号(针对一次性存量电子合同)
     *
     * php app/cli.php contract sync_e_content_no
     *
     */
    public function sync_e_content_noAction()
    {
        $log = '开始时间: ' . date('Y-m-d H:i:s') . PHP_EOL;
        $log .= '初始内存: ' . memory_usage() . PHP_EOL;

        $logger_type = 'info';

        try {
            // 1. 获取指定的电子合同配置
            $e_contract_ids = EnumsService::getInstance()->getSettingEnvValueIds('pending_sync_e_contract_ids');

            // 2. 获取电子合同主数据
            $i = 0;
            $length = 5;
            $conditions = 'contract_no = :contract_no:';
            $bind = ['contract_no' => ''];
            if (!empty($e_contract_ids)) {
                $conditions .= ' AND id IN ({ids:array})';
                $bind['ids'] = $e_contract_ids;
            }

            $e_contract_models = ContractElectronicModel::find([
                'conditions' => $conditions,
                'bind' => $bind,
                'offset' => $i,
                'limit' => $length,
                'order' => 'id DESC'
            ]);

            $batch = 1;
            $total_count = 0;
            $fail_count = 0;
            $max_limit_total = ContractElectronicModel::count([
                'conditions' => $conditions,
                'bind' => $bind
            ]);
            while ($total_count < $max_limit_total && !empty(count($e_contract_models))) {
                echo "每批处理 $length 条, 当前第 $batch 批" . PHP_EOL;

                // 逐条处理
                $_i = 0;
                foreach ($e_contract_models as $model) {
                    $_i++;

                    $_log = "第 $_i 条, id={$model->id}, no={$model->no}";

                    $total_count++;

                    $form_data = json_decode($model->contract_content, true);
                    if (empty($form_data)) {
                        $_log .= ', 表单数据为空, 略过' . PHP_EOL;
                        echo $_log;
                        continue;
                    }

                    $contract_basic = $form_data['contractBasic'] ?? [];
                    if (empty($contract_basic)) {
                        $_log .= ', 无合同基础信息, 略过' . PHP_EOL;
                        echo $_log;
                        continue;
                    }

                    $contract_no = '';
                    foreach ($contract_basic as $contract_item) {
                        if ($contract_item['field'] == 'contract_no') {
                            $contract_no = $contract_item['value'];
                            break;
                        }
                    }

                    $_log .= ", contract_no = $contract_no";
                    if (!empty($contract_no) && $model->i_update(['contract_no' => trim($contract_no)]) === false) {
                        $fail_count++;
                        $_log .= ', 同步失败, 原因可能是' . get_data_object_error_msg($model);
                    } else {
                        $_log .= ', 同步成功';
                    }

                    echo $_log . PHP_EOL;
                }

                $batch++;
                $e_contract_models = ContractElectronicModel::find([
                    'conditions' => $conditions,
                    'bind' => $bind,
                    'offset' => $i + $length,
                    'limit' => $length,
                    'order' => 'id DESC'
                ]);
            }

            $log .= "contract_no 待同步的 共 {$max_limit_total} 条, 已处理完毕, 失败 {$fail_count} 条" . PHP_EOL;
            $log .= '当前内存: ' . memory_usage() . PHP_EOL;

        } catch (ValidationException $e) {
            $log .= '提醒: ' . $e->getMessage() . PHP_EOL;

        } catch (Exception $e) {
            $log .= '异常: ' . $e->getMessage() . PHP_EOL;
            $logger_type = 'notice';
        }

        $log .= '结束时间: ' . date('Y-m-d H:i:s') . PHP_EOL;
        $log .= '当前内存: ' . memory_usage() . PHP_EOL;
        $this->logger->$logger_type($log);
        exit($log);
    }

    /**
     * 采购合同到期提醒
     * 执行时间：当地时间10:00am
     * php app/cli.php contract procure_expiration_reminder
     *
     * @return void
     * @throws GuzzleException
     */
    public function procure_expiration_reminderAction()
    {
        $log = '任务名称: ' . __FUNCTION__ . PHP_EOL;
        $log .= '开始时间: ' . date('Y-m-d H:i:s') . PHP_EOL;
        $logger_type = 'info';
        try {
            $procure_expiration_reminder_day = EnumsService::getInstance()->getSettingEnvValueIds('procure_expiration_reminder_day');
            $procure_expiration_reminder_email = EnumsService::getInstance()->getSettingEnvValueIds('procure_expiration_reminder_email');

            if (empty($procure_expiration_reminder_day) || empty($procure_expiration_reminder_email)) {
                throw new ValidationException('无配置，不执行', ErrCode::$VALIDATE_ERROR);
            }
            $purchase_contract_category = ContractCategoryRepository::getInstance()->getSubCategoryIdsByAncestryId(Enums::CONTRACT_TEMPLATE_PURCHASE);
            if (empty($purchase_contract_category)) {
                throw new Exception('没找到对应分类数据');
            }
            $list = Contract::find([
                'conditions' => 'status = :status: AND template_id IN ({template_ids:array}) and expiry_date is not null',
                'bind' => [
                    'status' => Enums::CONTRACT_STATUS_APPROVAL,
                    'template_ids' => $purchase_contract_category,
                ],
            ])->toArray();
            if (empty($list)) {
                throw new ValidationException('获取数据列表为空', ErrCode::$VALIDATE_ERROR);
            }
            $expiration_reminder_day = [];
            foreach ($procure_expiration_reminder_day as $v) {
                $expiration_reminder_day[date('Y-m-d', strtotime('+' . $v . ' day'))] = $v;
            }
            //合同所属公司
            $contract_company = EnumsService::getInstance()->getContractCompanyItem();
            $excel_data = [];

            $contract_nos = array_column($list, 'cno');
            $archive_item = ContractArchive::find([
                'conditions' => 'cno IN ({nos:array})',
                'bind' => ['nos' => $contract_nos],
                'columns' => ['id', 'cno']
            ])->toArray();
            $archive_item_ids = array_column($archive_item, 'id');
            $archive_item_nos = array_column($archive_item, 'cno', 'id');

            // 盖章合同附件
            $archive_item_attachment = AttachModel::find([
                'columns' => ['id','oss_bucket_type', 'oss_bucket_key', 'object_key'],
                'conditions' => 'oss_bucket_type in({oss_bucket_type:array}) AND oss_bucket_key IN ({keys:array}) AND deleted = :deleted:',
                'bind' => [
                    'oss_bucket_type' => [Enums::OSS_BUCKET_TYPE_ARCHIVE_CONTRACT_FILE],
                    'keys' => $archive_item_ids,
                    'deleted' => Enums\GlobalEnums::IS_NO_DELETED
                ]
            ])->toArray();
            $_archive_item_attachment = [];
            foreach ($archive_item_attachment as $v) {
                $path = !empty($v['object_key']) ? \App\Library\OssHelper::downloadFileHcm($v['object_key'],86400*7) : '';
                $_archive_item_attachment[$archive_item_nos[$v['oss_bucket_key']]] = $path['file_url'] ?? '';
            }
            unset($archive_item_attachment);

            // 合同附件和合同正文
            $contract_ids = array_column($list, 'id');
            $detail_attachment = AttachModel::find([
                'columns' => ['id','oss_bucket_type', 'oss_bucket_key', 'object_key'],
                'conditions' => 'oss_bucket_type in({oss_bucket_type:array}) AND oss_bucket_key IN ({keys:array}) AND deleted = :deleted:',
                'bind' => [
                    'oss_bucket_type' => [Enums::OSS_BUCKET_TYPE_CONTRACT_FILE, Enums::OSS_BUCKET_TYPE_CONTRACT_ATTACHMENT],
                    'keys' => $contract_ids,
                    'deleted' => Enums\GlobalEnums::IS_NO_DELETED
                ]
            ])->toArray();
            $_detail_attachment = [];
            foreach ($detail_attachment as $v) {
                if (get_runtime_env() == 'dev') {
                    $path = !empty($v['object_key']) ? \App\Library\OssHelper::downloadFileHcm($v['object_key'],86400) : '';
                }else{
                    $path = !empty($v['object_key']) ? \App\Library\OssHelper::downloadFileHcm($v['object_key'],86400*7) : '';
                }
                $_detail_attachment[$v['oss_bucket_key'] . '_' . $v['oss_bucket_type']][] = $path['file_url'] ?? '';
            }
            unset($detail_attachment);

            foreach ($list as $v) {
                if (!isset($expiration_reminder_day[$v['expiry_date']])) {
                    continue;
                }
                $excel_data[] = [
                    // 合同名称
                    $v['cname'] ?? '',
                    // 合同编号
                    $v['cno'] ?? '',
                    // 合同所属公司
                    $contract_company[$v['company_code']] ?? '',
                    // 供应商名称
                    $v['vendor_name'] ?? '',
                    // 合同说明
                    $v['contract_desc'] ?? '',
                    // 合同生效日期
                    $v['effective_date'] ?? '',
                    // 合同结束日期
                    $v['expiry_date'] ?? '',
                    // 距离合同结束天数
                    $expiration_reminder_day[$v['expiry_date']],
                    // 合同正文
                    !empty($_detail_attachment[$v['id'] . '_' . Enums::OSS_BUCKET_TYPE_CONTRACT_FILE]) ? implode(PHP_EOL, $_detail_attachment[$v['id'] . '_' . Enums::OSS_BUCKET_TYPE_CONTRACT_FILE]) : '',
                    // 合同附件
                    !empty($_detail_attachment[$v['id'] . '_' . Enums::OSS_BUCKET_TYPE_CONTRACT_ATTACHMENT]) ? implode(PHP_EOL, $_detail_attachment[$v['id'] . '_' . Enums::OSS_BUCKET_TYPE_CONTRACT_ATTACHMENT]) : '',
                    // 盖章合同
                    $_archive_item_attachment[$v['cno']] ?? '',
                ];
            }

            unset($list);

            if (empty($excel_data)) {
                throw new ValidationException('无需执行的数据', ErrCode::$VALIDATE_ERROR);
            }

            $header = [
                '合同名称 Contract name',
                '合同编号 Contract ID',
                '合同所属公司 Company of Contract',
                '供应商名称 Supplier name',
                '合同说明 Contract Explanation',
                '合同生效日期 Effective Date',
                '合同结束日期 End Date',
                '距离合同结束天数 Days until contract end',
                '合同正文 Contract proper',
                '合同附件 Appendices of a contract',
                '盖章合同 Sealed contract',
            ];
            $file_name = 'contract_data' . date('Ymd') . '.xlsx';
            $export_excel = (new BaseService())->exportExcel($header, $excel_data, $file_name);
            if (!$export_excel || !isset($export_excel['data'])) {
                throw new Exception('生成excel失败 返回结果:' . json_encode($export_excel, JSON_UNESCAPED_UNICODE));
            }
            $email_title = '[' . get_country_code() . ']合同到期提醒Contract expiration reminder';
            $email_content = sprintf("
            附件中的合同即将到期，请及时处理！<br/>
            <a href='%s'>点击下载附件</a><br/>
            The contract in the attachment is about to expire. Please deal with it in time!<br/>
            <a href='%s'>Click to download attachment</a>
            ", $export_excel['data'], $export_excel['data']);
            $send_result = $this->mailer->sendAsync($procure_expiration_reminder_email, $email_title, $email_content);
            if (!$send_result) {
                throw new Exception('邮件发送失败 result:' . json_encode($send_result) . '; emails:' . json_encode($procure_expiration_reminder_email) . '; title:' . $email_title . '; content:' . $email_content);
            }
            $log .= '邮件发送成功 emails:' . json_encode($procure_expiration_reminder_email) . '; title:' . $email_title . PHP_EOL;
        } catch (ValidationException $e) {
            $log .= $e->getMessage() . PHP_EOL;
        } catch (Exception $e) {
            $logger_type = 'error';
            $log .= $e->getMessage() . PHP_EOL;
        }
        $this->logger->$logger_type($log);
        $log .= '结束时间: ' . date('Y-m-d H:i:s') . PHP_EOL;
        exit($log);
    }

    /**
     * 电子合同 - 处理超时未签约的(PMD)
     *
     * php app/cli.php contract deal_timeout_unsigned
     *
     */
    public function deal_timeout_unsignedAction()
    {
        $log = '开始时间: ' . date('Y-m-d H:i:s') . PHP_EOL;
        $log .= '初始内存: ' . memory_usage() . PHP_EOL;

        $logger_type = 'info';

        try {
            // 1. 获取超出指定天数，仍未签约的电子合同
            $sign_valid_days = (int)EnumsService::getInstance()->getSettingEnvValue('pmd_electronic_contract_sign_valid_days', 0);

            if (empty($sign_valid_days)) {
                throw new BusinessException("有效天数配置异常, 请核查, pmd_electronic_contract_sign_valid_days={$sign_valid_days}", ErrCode::$BUSINESS_ERROR);
            }

            // 取PMD/Retail/FFM 的
            $department_ids = [];
            $department_list = ContractElectronicService::getInstance()->getContractApplicableDepartmentByName();
            $department_ids = array_merge($department_ids, $department_list['Group Project Management'] ?? []);
            $department_ids = array_merge($department_ids, $department_list['Retail Management'] ?? []);
            $department_ids = array_merge($department_ids, $department_list['FFM'] ?? []);
            $department_ids = array_filter($department_ids);
            if (empty($department_ids)) {
                throw new BusinessException('部门ID配置获取异常, 请核查, contract_applicable_departments=' . json_encode($department_list, JSON_UNESCAPED_UNICODE), ErrCode::$BUSINESS_ERROR);
            }

            $initiate_sign_date = date('Y-m-d', strtotime("-$sign_valid_days day"));

            // 2. 获取超时未签约的电子合同
            $models = ContractElectronicModel::find([
                'conditions' => 'department_id IN ({department_ids:array}) AND initiate_sign_date < :initiate_sign_date: AND approve_status = :approve_status: AND sign_status IN ({sign_status:array})',
                'bind' => [
                    'department_ids' => $department_ids,
                    'initiate_sign_date' => $initiate_sign_date,
                    'approve_status' => Enums::WF_STATE_APPROVED,
                    'sign_status' => [ContractEnums::CONTRACT_SIGN_STATUS_2, ContractEnums::CONTRACT_SIGN_STATUS_6],
                ],
            ]);

            $contract_count = count($models);
            if (empty($contract_count)) {
                throw new ValidationException("无符合条件的数据, 略", ErrCode::$VALIDATE_ERROR);
            }

            $log .= "超时未签约的电子合同, 共 $contract_count 条" . PHP_EOL;

            // 获取创建人企业邮箱
            $created_ids = array_values(array_unique(array_column($models->toArray(), 'created_id')));
            $created_ids = (new HrStaffRepository())->getStaffListByStaffIds($created_ids);

            $handle_log = '';
            $handle_count = 0;
            foreach ($models as $model) {
                $handle_count++;

                $handle_log .= PHP_EOL . '待更新电子合同签约状态, no=' . $model->no;

                // 更新状态为超时未签约
                $update = [
                    'sign_status' => ContractEnums::CONTRACT_SIGN_STATUS_9,
                    'updated_at' => date('Y-m-d H:i:s')
                ];
                if ($model->save($update) === false) {
                    $handle_log .= '; DB更新失败; 给BD的邮件通知未发送';
                    continue;
                }

                $created_email = $created_ids[$model->created_id]['email'] ?? '';
                $handle_log .= "; 更新成功; 邮箱地址-{$created_email}";

                if (empty($created_email)) {
                    $handle_log .= '; 邮件未发送, 邮箱为空';
                    continue;
                }

                try {
                    // 给BD发送超时未签约提醒
                    $email_val = [
                        'emails' => [$created_email],
                        'electronic_no' => $model->no,
                        'contract_lang' => $model->lang,
                        'department_id' => $model->department_id,
                    ];

                    ContractElectronicService::getInstance()->sendElectronicContractEmailNotice(ContractEnums::ELECTRONIC_CONTRACT_EMIAL_SCENCE_SIGN_TIMEOUT, $email_val, true);

                    $handle_log .= '; 邮件发送成功';
                } catch (Exception $e) {
                    $handle_log .= "; 邮件发送失败,原因可能是={$e->getMessage()}";
                }
            }

            $log .= "处理条数: {$handle_count}" . PHP_EOL;
            $log .= "处理结果: {$handle_log}" . PHP_EOL;

        } catch (ValidationException $e) {
            $log .= '提醒: ' . $e->getMessage() . PHP_EOL;

        } catch (BusinessException $e) {
            $log .= '异常: ' . $e->getMessage() . PHP_EOL;
            $logger_type = 'notice';
        } catch (Exception $e) {
            $log .= '错误: ' . $e->getMessage() . PHP_EOL;
            $logger_type = 'warning';
        }

        $log .= '结束时间: ' . date('Y-m-d H:i:s') . PHP_EOL;
        $log .= '当前内存: ' . memory_usage() . PHP_EOL;
        $this->logger->$logger_type($log);
        exit($log);
    }

    /**
     * 同步电子合同关联的模板版本的ftl地址
     *
     * php app/cli.php contract sync_ftl_file_path
     */
    public function sync_ftl_file_pathAction()
    {
        $log = '开始时间: ' . date('Y-m-d H:i:s') . PHP_EOL;
        $log .= '初始内存: ' . memory_usage() . PHP_EOL;

        // 获取电子合同
        $all_models = ContractElectronicModel::find();

        $nos = array_values(array_values(array_column($all_models->toArray(), 'relate_template_no')));
        echo '共 ' . count($nos) . ' 条电子合同待同步ftl_file_url' . PHP_EOL;

        // 获取电子合同关联的模板
        $all_relate_template_list = ContractTemplateVersionModel::find([
            'conditions' => 'template_no IN ({nos:array})',
            'bind' => ['nos' => $nos],
            'columns' => ['template_no', 'file_url']
        ])->toArray();
        $all_relate_template_list = array_column($all_relate_template_list, 'file_url', 'template_no');
        echo '共涉及 ' . count($all_relate_template_list) . ' 条模板版本' . PHP_EOL;

        // 更新电子合同
        $ftl_file_null_count = 0;
        $ftl_file_fail_count = 0;
        $ftl_file_success_count = 0;
        $ftl_file_error_log = '';
        foreach ($all_models as $model) {
            $relate_template_file_url = $all_relate_template_list[$model->relate_template_no] ?? '';
            if (empty($relate_template_file_url)) {
                $ftl_file_null_count++;
                $ftl_file_error_log .= $model->no . ',';
                continue;
            }

            if ($model->save(['ftl_file_url' => $relate_template_file_url]) === false) {
                $ftl_file_fail_count++;
                $ftl_file_error_log .= $model->no . ',';
                continue;
            }

            $ftl_file_success_count++;
        }

        $log .= "ftl文件地址为空的数量: $ftl_file_null_count" . PHP_EOL;
        $log .= "ftl文件地址同步失败的数量: $ftl_file_fail_count" . PHP_EOL;
        $log .= "ftl文件地址同步成功的数量: $ftl_file_success_count" . PHP_EOL;
        $log .= "ftl文件地址同步失败的电子合同编号: $ftl_file_error_log" . PHP_EOL;

        $log .= '结束时间: ' . date('Y-m-d H:i:s') . PHP_EOL;
        $log .= '当前内存: ' . memory_usage() . PHP_EOL;
        $this->logger->info($log);
        exit($log);
    }

    /**
     * 仓库信息补全(dev / tra)
     *
     * php app/cli.php contract completion_warehouse_info
     *
     */
    public function completion_warehouse_infoAction()
    {
        // 获取待补全信息的仓库
        $warehouse_models = ContractWarehouseModel::find([
            'conditions' => 'warehouse_name = :warehouse_name:',
            'bind' => ['warehouse_name' => '']
        ]);
        $warehouse_models_count = count($warehouse_models);
        echo "待处理 仓库信息 {$warehouse_models_count} 个" . PHP_EOL;
        if ($warehouse_models_count == 0) {
            exit();
        }

        // 取若干可用的省市区code
        $sys_district_list = SysDistrictModel::find([
            'conditions' => 'deleted = 0',
            'columns' => ['code', 'city_code', 'province_code'],
            'order' => 'RAND()',
            'limit' => 30
        ])->toArray();

        // 逐条补全仓库信息
        $success_count = 0;
        $error_count = 0;
        foreach ($warehouse_models as $model) {
            shuffle($sys_district_list);
            $sys_district = array_shift($sys_district_list);

            $update_data = [
                'warehouse_name' => '仓库名-' . date('Ymd') . '-' . get_generate_random_string(10),
                'real_area' => mt_rand(1, 10000),
                'province_code' => $sys_district['province_code'],
                'city_code' => $sys_district['city_code'],
                'district_code' => $sys_district['code'],
                'updated_at' => date('Y-m-d H:i:s')
            ];

            if ($model->i_update($update_data) === false) {
                $error_count++;
                continue;
            }

            $success_count++;
        }

        echo "处理成功 {$success_count} 个" . PHP_EOL;
        echo "处理失败 {$error_count} 个" . PHP_EOL;
        exit();
    }

    /**
     * 补全租房合同所关联的仓库信息
     *
     * php app/cli.php contract completion_contract_warehouse_info
     *
     */
    public function completion_contract_warehouse_infoAction()
    {
        $log = '开始时间: ' . date('Y-m-d H:i:s') . PHP_EOL;
        $log .= '初始内存: ' . memory_usage() . PHP_EOL;

        $logger_type = 'info';

        try {
            // 获取需补全仓库信息的租房合同
            $contract_models = ContractStoreRentingModel::find([
                'conditions' => 'warehouse_id != :warehouse_id: AND (warehouse_name = :warehouse_name: OR warehouse_real_area = :warehouse_real_area:)',
                'bind' => ['warehouse_id' => '', 'warehouse_name' => '', 'warehouse_real_area' => '']
            ]);
            $contract_models_count = count($contract_models);
            $log .= "待补全仓库信息的合同数: {$contract_models_count}" . PHP_EOL;
            if ($contract_models_count == 0) {
                throw new ValidationException('无待处理的合同数据', ErrCode::$VALIDATE_ERROR);
            }

            // 取仓库信息
            $warehouse_ids = array_unique(array_column($contract_models->toArray(), 'warehouse_id'));
            $warehouse_list = ContractWarehouseModel::find([
                'conditions' => 'warehouse_id IN ({warehouse_ids:array})',
                'bind' => ['warehouse_ids' => array_values($warehouse_ids)],
                'columns' => ['warehouse_name', 'real_area', 'province_code', 'city_code', 'district_code', 'warehouse_id'],

            ])->toArray();
            $warehouse_list = WarehouseService::getInstance()->getAreaInfo($warehouse_list);
            $warehouse_list = array_column($warehouse_list, null, 'warehouse_id');

            // 同步
            $success_count = 0;
            $error_count = 0;
            $null_warehouse_count = 0;
            $error_log = '';
            $null_warehouse_log = '';
            foreach ($contract_models as $model) {
                $warehouse_info = $warehouse_list[$model->warehouse_id] ?? [];
                if (empty($warehouse_info)) {
                    $null_warehouse_count++;
                    $null_warehouse_log .= $model->warehouse_id . ';';
                    continue;
                }

                $update_data = [
                    'warehouse_name' => $warehouse_info['warehouse_name'],
                    'warehouse_province_name' => $warehouse_info['province_name'],
                    'warehouse_city_name' => $warehouse_info['city_name'],
                    'warehouse_district_name' => $warehouse_info['district_name'],
                    'warehouse_real_area' => $warehouse_info['real_area'],
                ];

                if ($model->i_update($update_data) === false) {
                    $error_count++;
                    $error_log .= $model->warehouse_id . ', 同步失败原因=' . get_data_object_error_msg($model) . ' ;';
                    continue;
                }

                $success_count++;
            }

            $log .= "成功条数: {$success_count}" . PHP_EOL;
            $log .= "失败条数: {$error_count}" . PHP_EOL;
            $log .= "找不到仓库信息的条数: {$null_warehouse_count}, 仓库ID={$null_warehouse_log}" . PHP_EOL;
            $log .= "失败日志: {$error_log}" . PHP_EOL;

        } catch (ValidationException $e) {
            $log .= '提醒: ' . $e->getMessage() . PHP_EOL;

        } catch (Exception $e) {
            $log .= '错误: ' . $e->getMessage() . PHP_EOL;
            $logger_type = 'warning';
        }

        $log .= '结束时间: ' . date('Y-m-d H:i:s') . PHP_EOL;
        $log .= '当前内存: ' . memory_usage() . PHP_EOL;
        $this->logger->$logger_type($log);
        exit($log);
    }

    /**
     * 补全仓库的主网点信息
     *
     * php app/cli.php contract completion_warehouse_store
     *
     */
    public function completion_warehouse_storeAction()
    {
        $log = '开始时间: ' . date('Y-m-d H:i:s') . PHP_EOL;
        $log .= '初始内存: ' . memory_usage() . PHP_EOL;

        $logger_type = 'info';

        try {
            // 关联了仓库的租房合同(审批通过 且 生效日期最晚的)
            $contract_list = ContractStoreRentingModel::find([
                'conditions' => 'warehouse_id != :warehouse_id: AND contract_status = :contract_status:',
                'bind' => ['warehouse_id' => '', 'contract_status' => Enums::WF_STATE_APPROVED],
                'columns' => ['contract_effect_date', 'store_id', 'store_cate', 'contract_id', 'warehouse_id', 'approved_at'],
                'order' => 'warehouse_id ASC, contract_effect_date DESC',
            ])->toArray();

            $log .= '共有 ' . count($contract_list) . ' 个合同关联了仓库;' . PHP_EOL;
            if (empty($contract_list)) {
                throw new ValidationException('不存在关联了仓库的租房合同', ErrCode::$VALIDATE_ERROR);
            }

            // 找每个仓库关联的生效日期最晚的合同的网点信息
            $warehouse_store_list = [];
            foreach ($contract_list as $contract) {
                $warehouse_store = $warehouse_store_list[$contract['warehouse_id']] ?? [];
                if (empty($warehouse_store)) {
                    $warehouse_store_list[$contract['warehouse_id']] = $contract;
                    continue;
                }

                if ($contract['contract_effect_date'] > $warehouse_store['contract_effect_date']) {
                    $warehouse_store_list[$contract['warehouse_id']] = $contract;
                }
            }

            unset($contract_list);
            $log .= '共关联了 ' . count($warehouse_store_list) . ' 个仓库;' . PHP_EOL;
            $log .= '当前内存: ' . memory_usage() . PHP_EOL;

            // 取仓库信息
            $warehouse_list = ContractWarehouseModel::find([
                'conditions' => 'warehouse_id IN ({warehouse_ids:array})',
                'bind' => ['warehouse_ids' => array_keys($warehouse_store_list)],
                'columns' => ['id', 'warehouse_id', 'warehouse_status', 'deactivate_date'],
            ])->toArray();

            $log .= '共找到 ' . count($warehouse_list) . ' 个仓库信息;' . PHP_EOL;
            if (empty($warehouse_list)) {
                throw new ValidationException('未找到仓库ID对应的仓库信息', ErrCode::$VALIDATE_ERROR);
            }

            $warehouse_list = array_column($warehouse_list, null, 'warehouse_id');

            // 获取已有使用中的主网点的仓库
            $exist_main_store_warehouse = WarehouseStoreModel::find([
                'conditions' => 'warehouse_id IN ({warehouse_ids:array}) AND store_flag = :store_flag: AND use_status = :use_status:',
                'bind' => [
                    'warehouse_ids' => array_keys($warehouse_list),
                    'store_flag' => ContractEnums::WAREHOUSE_STORE_FLAG_MAIN,
                    'use_status' => ContractEnums::WAREHOUSE_STORE_USE_STATUS_ING,
                ],
                'columns' => ['warehouse_id'],
            ])->toArray();
            $exist_main_store_warehouse = array_column($exist_main_store_warehouse, 'warehouse_id');

            // 构建待初始化的仓库网点
            $init_warehouse_store = [];
            $null_warehouse_log = [];
            $no_need_sync_warehouse_ids = [];
            foreach ($warehouse_store_list as $contract_store) {
                // 若该仓库已有使用中的主网点, 则跳过
                if (in_array($contract_store['warehouse_id'], $exist_main_store_warehouse)) {
                    $no_need_sync_warehouse_ids[] = $contract_store['warehouse_id'];
                    continue;
                }

                $warehouse_info = $warehouse_list[$contract_store['warehouse_id']] ?? [];
                if (empty($warehouse_info)) {
                    $null_warehouse_log[] = $contract_store['warehouse_id'];
                    continue;
                }

                $main_store_info = [
                    'warehouse_main_id' => $warehouse_info['id'],
                    'warehouse_id' => $warehouse_info['warehouse_id'],
                    'store_id' => $contract_store['store_id'],
                    'store_category' => $contract_store['store_cate'],
                    'store_flag' => ContractEnums::WAREHOUSE_STORE_FLAG_MAIN,
                    'begin_date' => mb_substr($contract_store['approved_at'], 0, 10),
                    'end_date' => null,
                    'use_status' => ContractEnums::WAREHOUSE_STORE_USE_STATUS_ING,
                    'created_at' => date('Y-m-d H:i:s'),
                    'created_id' => 10000,
                    'updated_at' => date('Y-m-d H:i:s'),
                    'updated_id' => 10000,
                ];

                // 仓库已停用, 将网点置为结束使用
                if ($warehouse_info['warehouse_status'] == ContractEnums::WAREHOUSE_STATUS_DEACTIVATED) {
                    $main_store_info['use_status'] = ContractEnums::WAREHOUSE_STORE_USE_STATUS_END;
                    $main_store_info['end_date'] = $warehouse_info['deactivate_date'];
                }

                $init_warehouse_store[] = $main_store_info;
            }

            $log .= '待初始 ' . count($init_warehouse_store) . ' 条仓库主网点' . PHP_EOL;
            if (empty($init_warehouse_store)) {
                throw new ValidationException('无待初始化的仓库主网点数据', ErrCode::$VALIDATE_ERROR);
            }

            if ((new WarehouseStoreModel())->batch_insert($init_warehouse_store) === false) {
                throw new BusinessException('仓库主网点数据初始化失败, data=' . json_encode($init_warehouse_store, JSON_UNESCAPED_UNICODE), ErrCode::$BUSINESS_ERROR);
            }

            $log .= '仓库主网点初始化成功' . PHP_EOL;
            $log .= '没找到仓库信息的仓库ID: ' . implode(',', $null_warehouse_log) . PHP_EOL;
            $log .= '已有使用中的主网点的仓库个数(无需同步仓库的主网点): ' . count($no_need_sync_warehouse_ids) . PHP_EOL;
            $log .= '已有使用中的主网点的仓库清单(无需同步仓库的主网点): ' . implode(',', $no_need_sync_warehouse_ids) . PHP_EOL;

        } catch (ValidationException $e) {
            $log .= '提醒: ' . $e->getMessage() . PHP_EOL;
        } catch (BusinessException $e) {
            $log .= '异常: ' . $e->getMessage() . PHP_EOL;
            $logger_type = 'notice';
        } catch (Exception $e) {
            $log .= '错误: ' . $e->getMessage() . PHP_EOL;
            $logger_type = 'warning';
        }

        $log .= '结束时间: ' . date('Y-m-d H:i:s') . PHP_EOL;
        $log .= '当前内存: ' . memory_usage() . PHP_EOL;
        $this->logger->$logger_type($log);
        exit($log);
    }

    /**
     * 电子合同老数据的OTP文件生成 并 写入关联合同的其他附件中
     *
     * php app/cli.php contract init_otp_file
     *
     */
    public function init_otp_fileAction()
    {
        $log = '开始时间: ' . date('Y-m-d H:i:s') . PHP_EOL;
        $log .= '初始内存: ' . memory_usage() . PHP_EOL;

        // 获取完成签约的电子合同[签字审批完毕]
        $all_models = ContractElectronicSignInfoModel::find([
            'conditions' => 'business_audit_status = :business_audit_status:',
            'bind' => ['business_audit_status' => Enums::WF_STATE_APPROVED],
        ]);

        $sign_info_item = $all_models->toArray();
        $log .= '共有 ' . count($sign_info_item) . ' 个电子合同签字审批通过, 待追加OTP附件' . PHP_EOL;
        if (empty($sign_info_item)) {
            exit($log);
        }

        // 获取电子合同关联的其他合同的ID
        $electronic_nos = array_column($sign_info_item, 'electronic_no');
        $electronic_contract_list = ContractElectronicModel::find([
            'conditions' => 'no IN ({no:array}) AND sign_status = :sign_status:',
            'bind' => ['no' => $electronic_nos, 'sign_status' => ContractEnums::CONTRACT_SIGN_STATUS_3],
            'columns' => ['no', 'relate_id']
        ])->toArray();
        $electronic_contract_list = array_column($electronic_contract_list, 'relate_id', 'no');


        // 附件
        $attach_arr = [];
        $error_log = '';
        foreach ($all_models as $models) {
            // 提取OTP日志 + 日志文件
            $relate_contract_id = $electronic_contract_list[$models->electronic_no] ?? 0;
            $file_info = ContractElectronicFlowService::getInstance()->generatePOAOtpLogFile($models, $relate_contract_id);
            if (empty($file_info)) {
                $error_log .= "electronic_no={$models->electronic_no}, contract_id={$relate_contract_id}; ";
                continue;
            }

            // 追加到关联合同的其他附件中
            $attach_arr[] = [
                'oss_bucket_type' => Enums::OSS_BUCKET_TYPE_CONTRACT_ATTACHMENT,
                'sub_type' => 0,
                'oss_bucket_key' => $relate_contract_id,
                'bucket_name' => $file_info['bucket_name'],
                'object_key' => $file_info['object_key'],
                'file_name' => $file_info['file_name']
            ];
        }

        $attach_count = count($attach_arr);
        $log .= '共需导入 ' . $attach_count . '个附件, 数据如下: ' . json_encode($attach_arr, JSON_UNESCAPED_UNICODE) . PHP_EOL;

        $attach = new AttachModel();
        if ($attach->batch_insert($attach_arr) === false) {
            $log .= '其他附件批量写入失败, 请检查' . PHP_EOL;
        } else {
            $log .= '导入成功' . PHP_EOL;
        }

        $log .= '异常提示: OTP文件生成失败的相关信息, ' . $error_log . PHP_EOL;
        $log .= '结束时间: ' . date('Y-m-d H:i:s') . PHP_EOL;
        $log .= '当前内存: ' . memory_usage() . PHP_EOL;
        $this->logger->info($log);
        exit($log);
    }

}
