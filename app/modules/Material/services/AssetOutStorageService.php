<?php
namespace App\Modules\Material\Services;

use App\Library\Enums;
use App\Library\Enums\MaterialAssetApplyEnums;
use App\Library\Enums\MaterialAssetOutStorageEnums;
use App\Library\Enums\MaterialClassifyEnums;
use App\Library\Enums\MaterialEnums;
use App\Library\Enums\ImportCenterEnums;
use App\Library\Enums\GlobalEnums;
use App\Library\Enums\StaffInfoEnums;
use App\Library\Validation\Validation;
use App\Models\backyard\HrStaffInfoModel;
use App\Models\backyard\SettingEnvModel;
use App\Models\oa\MiniHrStaffInfoModel;
use App\Modules\Common\Services\EnumsService;
use App\Modules\Common\Services\ImportCenterService;
use App\Modules\Material\Models\MaterialAssetApplyModel;
use App\Modules\Material\Models\MaterialAssetApplyProductModel;
use App\Modules\Material\Models\MaterialAssetOutStorageModel;
use App\Modules\Material\Models\MaterialAssetOutStorageProductModel;
use App\Modules\Material\Models\MaterialAssetsModel;
use App\Modules\Material\Models\MaterialAssetTransferBatchModel;
use App\Modules\Material\Models\MaterialAssetTransferLogModel;
use App\Modules\Material\Models\MaterialAssetUpdateLogModel;
use App\Modules\Material\Models\MaterialAttachmentModel;
use App\Modules\Material\Models\MaterialCategoryModel;
use App\Modules\Material\Models\MaterialFinanceCategoryModel;
use App\Modules\Material\Models\MaterialSauModel;
use App\Modules\Material\Models\MaterialSauSkuModel;
use App\Modules\Material\Models\MaterialScmAssetUnmatchedModel;
use App\Modules\Payment\Services\StoreRentingAddService;
use App\Modules\Purchase\Services\BaseService as PurchaseService;
use App\Modules\Purchase\Services\validSignService;
use App\Modules\Organization\Services\DepartmentService;
use App\Library\ErrCode;
use App\Library\Validation\ValidationException;
use App\Library\Exception\BusinessException;
use App\Modules\User\Services\StaffService;
use App\Modules\User\Services\UserService;
use App\Repository\backyard\SysCityRepository;
use App\Repository\backyard\SysDistrictRepository;
use App\Repository\backyard\SysProvinceRepository;
use App\Repository\HrStaffRepository;
use App\Repository\StoreRepository;
use App\Util\RedisKey;
use App\Models\oa\MaterialSauStorageModel;

class AssetOutStorageService extends BaseService
{
    public static $validate_material_add = [
        'no'=>'Required|StrLenGeLe:10,20',
        'apply_date'=>'Required|Date',
        'staff_id'=>'Required|IntGt:0',//使用人工号,
        'staff_name'=>'Required|StrLenGeLe:1,50',//员工姓名
        'company_id'=>'Required|IntGt:0',//所属公司ID
        'company_name'=>'Required|StrLenGeLe:1,50',//所属公司名称
        'node_department_id'=>'Required|IntGt:0',//所属部门ID
        'node_department_name'=>'Required|StrLenGeLe:1,50',//所属部门名称
        'sys_store_id'=>'Required|StrLenGeLe:1,10',//所属网点
        'store_name'=>'Required|StrLenGeLe:1,50',//所属网点名称
        'job_id'=>'Required|IntGt:0',//职位ID
        'job_name'=>'Required|StrLenGeLe:1,255',//职位名称
        //'pc_code'=>'Required|StrLenGeLe:1,255',//成本中心
        'receive_store_id'=>'Required|StrLenGeLe:1,10',//收件人信息地址ID
        'receive_name'=>'Required|StrLenGeLe:1,100',//收件人信息名称
        'province_code' => 'Required|StrLenGeLe:1,4',//省编码
        'city_code' => 'Required|StrLenGeLe:1,6',//市编码
        'district_code' => 'Required|StrLenGeLe:1,8',//区编码
        'postal_code' => 'Required|StrLenGeLe:1,2000',//邮编
        'address' => 'Required|StrLenGeLe:1,255',//详细地址
        'mark' => 'StrLenGeLe:0,500',//备注
        'update_to_scm' => 'Required|IntIn:'.MaterialClassifyEnums::MATERIAL_CATEGORY_OFF.','.MaterialClassifyEnums::MATERIAL_CATEGORY_NO,//是否通过SCM出库，1否，2是
        'mach_code'=>'IfIntEq:update_to_scm,'.MaterialClassifyEnums::MATERIAL_CATEGORY_NO.'|Required|StrLenGeLe:1,128',//货主code
        'mach_name'=>'IfIntEq:update_to_scm,'.MaterialClassifyEnums::MATERIAL_CATEGORY_NO.'|Required|StrLenGeLe:1,128',//货主名称
        'stock_id' =>'IfIntEq:update_to_scm,'.MaterialClassifyEnums::MATERIAL_CATEGORY_NO.'|Required|StrLenGeLe:1,10',//仓库ID
        'delivery_way' => 'Required|IntIn:' . MaterialClassifyEnums::DELIVERY_WAY_EXPRESS . ',' . MaterialClassifyEnums::DELIVERY_WAY_SELF,//配送方式

        'products' => 'Required|Arr|ArrLenGeLe:1,50',
        'products[*]' => 'Required|Obj',
        'products[*].barcode' => 'Required|StrLenGeLe:1,30',//barcode
        'products[*].name_zh' => 'Required|StrLenGeLe:1,100',//中文名称
        'products[*].name_en' => 'Required|StrLenGeLe:1,100',//英文名称
        'products[*].name_local' => 'StrLenGeLe:0,100',//当地语言名称
        'products[*].category_id' => 'Required|IntGt:0',//物料分类id
        'products[*].category_name' => 'Required|StrLenGeLe:1,100',//物料分类名称
        'products[*].category_code' => 'Required|StrLenGeLe:1,30',//物料分类编码
        'products[*].finance_category_id' => 'Required|IntGt:0',//财务分类id
        'products[*].finance_category_name' => 'Required|StrLenGeLe:1,100',//财务分类名称
        'products[*].finance_category_code' => 'Required|StrLenGeLe:1,30',//财务分类编码
        'products[*].unit_zh' => 'StrLenGeLe:0,20',//基本单位-中文
        'products[*].unit_en' => 'StrLenGeLe:0,20',//基本单位-英文
        'products[*].model' => 'StrLenGeLe:0,100',//规格型号
        'products[*].use' => 'Required|IntIn:'.MaterialEnums::USE_VALIDATE,//使用方向
    ];

    /**
     * 非必需的筛选条件
     * @var array
     */
    public static $not_must_params = [
        'staff_id',
        'status',
        'node_department_id',
        'company_id',
        'use',
        'delivery_way',
        'create_id',
        'create_date_begin',
        'create_date_end',
        'pageSize',
        'pageNum'
    ];

    //搜索标准型号
    public static $validate_search_barcode = [
        'pageSize' => 'IntGt:0',//每页条数
        'pageNum' => 'IntGt:0', //页码
        'name' => 'StrLenGeLe:0,30', //物料名称
        'barcode' => 'StrLenGeLe:0,30', //标准型号的唯一标识
        'model' => 'StrLenGeLe:0,100', //规格型号
        'mach_code' => 'Required|StrLenGeLe:1,128',//货主code
        'stock_id' => 'Required|StrLenGeLe:1,10'//仓库id
    ];

    //搜索资产台账
    public static $validate_search_asset = [
        'pageSize' => 'IntGt:0',//每页条数
        'pageNum' => 'IntGt:0', //页码
        'name' => 'StrLenGeLe:0,30', //物料名称
        'barcode' => 'StrLenGeLe:0,30', //标准型号的唯一标识
        'model' => 'StrLenGeLe:0,100', //规格型号
        'asset_code' => 'StrLenGeLe:0,100',//资产编码
        'sn_code' => 'StrLenGeLe:0,50'//sn编码
    ];

    /**
     * 列表搜索校验
     */
    public static $validate_list_search = [
        'no'=>'StrLenGeLe:0,20',//领用单号
        'staff_id'=>'IntGt:0',//使用人工号
        'status' =>'IntGt:0',//单据状态
        'sys_store_id'=>'StrLenGeLe:0,10',//所属网点
        'receive_store_id[*]' => 'StrLenGeLe:0,10',//使用部门/网点
        'node_department_id'=>'IntGt:0',//部门ID
        'company_id' => 'IntGt:0',//所属公司
        'barcode' => 'StrLenGeLe:0,30', //标准型号的唯一标识
        'name' => 'StrLenGeLe:0,100', //物料名称
        'model' => 'StrLenGeLe:0,100',//规格型号
        'use' =>'IntGt:0',//使用方向
        'delivery_way' => 'IntGt:0',//配送方式
        'create_id'=>'IntGt:0',//处理人工号
        'create_date_begin' => 'Date',//创建日期-起始
        'create_date_end' => 'Date',//创建日期-截止
        'pageSize' => 'IntGt:0',//每页条数
        'pageNum' => 'IntGt:0', //页码
        'apply_no'=> 'StrLenGeLe:0,50',//关联申请单号
        'scm_no' => 'StrLenGeLe:0,255',//scm出库单号
    ];

    /**
     * 修改、详情、取消校验验证
     */
    public static $validate_Id = [
        'id' => 'Required|IntGt:0',
    ];

    /**
     * 名下资产
     * @var array
     */
    public static $validate_owner_asset = [
        'staff_id' => 'Required|IntGt:0',
        'pageSize' => 'IntGt:0',//每页条数
        'pageNum' => 'IntGt:0', //页码
    ];

    /**
     * OA回调接口-SCM出库成功 资产
     * @var array
     */
    public static $validate_recall_param = [
        'sign'  => 'Required|StrLenGeLe:30,33',
        'orderSn'   => 'Required|StrLenGeLe:1,255',
        'timestamp' => 'Required',
        'goods' => 'Required|Arr|ArrLenGe:1',
        'goods[*]' => 'Required|Obj',
        'goods[*].barCode' => 'Required|StrLenGe:1',
        'goods[*].num' => 'Required|IntGt:0',
        'goods[*].unit'=> 'Required|StrLenGe:0',
        'goods[*].asset'=>'Required|Arr',
        'goods[*].asset[*]' => 'Obj',
        'goods[*].asset[*].snCode'=>'StrLenGeLe:0,50',
        'goods[*].asset[*].assetCode'=>'StrLenGeLe:0,100',
        'goods[*].asset[*].outBoundDate'=>'DateTime',
    ];

    /**
     * OA回调接口-SCM出库成功 耗材
     * @var array
     */
    public static $validate_recall_param_wms = [
        'sign'  => 'Required|StrLenGeLe:30,33',
        'orderSn'   => 'Required|StrLenGeLe:1,255',
        'timestamp' => 'Required',
        'bizExt' => 'Required',
        'goods' => 'Required|Arr|ArrLenGe:1',
        'goods[*]' => 'Required|Obj',
        'goods[*].barCode' => 'Required|StrLenGe:1',
        'goods[*].num' => 'Required|IntGt:0',
        'goods[*].unit'=> 'Required|StrLenGe:0'
    ];

    /**
     * 关联申请单-筛选
     * @var array
     */
    public static $validate_apply_param = [
        'id' => 'Required|IntGt:0',//关联的申请单ID
        'update_to_scm' => 'Required|IntIn:'.MaterialClassifyEnums::MATERIAL_CATEGORY_OFF.','.MaterialClassifyEnums::MATERIAL_CATEGORY_NO,//是否通过SCM出库，1否，2是
        'mach_code'=>'IfIntEq:update_to_scm,'.MaterialClassifyEnums::MATERIAL_CATEGORY_NO.'|Required|StrLenGeLe:1,128',//货主code
        'stock_id' => 'IfIntEq:update_to_scm,'.MaterialClassifyEnums::MATERIAL_CATEGORY_NO.'|Required|StrLenGeLe:1,10',//仓库ID
        'barcode_exact' => 'StrLenGeLe:0,20', //标准型号的唯一标识
        'asset_code' => 'StrLenGeLe:0,100',//资产编码
        'sn_code' => 'StrLenGeLe:0,50'//sn编码
    ];

    /**
     * 关联申请单-提交
     * @var array
     */
    public static $validate_related_apply_param = [
        'apply_id' => 'Required|IntGt:0',//关联的申请单ID
        'apply_no' => 'Required|StrLenGeLe:1,50',//关联申请单号
    ];

    /**
     * 领用发放-资产明细-查看
     * @var array
     */
    public static $info_detail_list_params = [
        'aor_no'=>'StrLenGeLe:1,80',
        'bar_code'=>'Required|StrLenGe:1',
        'pageSize' => 'IntGt:0',//每页条数
        'pageNum' => 'IntGt:0', //页码
    ];




    private static $instance;
    private function __construct()
    {
    }
    /**
     * @return AssetOutStorageService
     */
    public static function getInstance()
    {
        if (!isset(self::$instance) || !(self::$instance instanceof self)) {
            self::$instance = new self;
        }
        return self::$instance;
    }

    /**
     * 资产领用-资产领用默认配置项
     * @return array
     */
    public function getOptionsDefault()
    {
        $code = ErrCode::$SUCCESS;
        $message = 'success';
        $data = [];
        try {
            //状态、使用方向、配送方式
            $data = $this->getEnums();
            //费用所属公司
            $data['cost_company'] = (new PurchaseService())->getCooCostCompany();

            //获取货主列表
            $data['cargo_owner'] = $this->getScmCargoOwner();

            //雇佣类型
            $data['hire_type_item'] = EnumsService::getInstance()->getHireTypeEnum(true);
        } catch (\Exception $e) {
            $code = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $this->logger->warning('material-asset-out-storage_options_default-failed:'.$e->getMessage());
        }
        return [
            'code' => $code,
            'message' => $message,
            'data' => $data
        ];
    }

    /**
     * 获取状态、使用方向、配送方式
     * @return array
     */
    public function getEnums()
    {
        $data = [];
        $enums_arr = [
            'status'=>MaterialAssetOutStorageEnums::$asset_out_storage_status,
            'use'=>MaterialEnums::$use,
            'delivery_way_list' => MaterialClassifyEnums::$delivery_way_arr
        ];
        foreach ($enums_arr as $key=>$value) {
            foreach ($value as $k=>$v) {
                $data[$key][] = [
                    'value'=>$k,
                    'label'=>static::$t->_($v)
                ];
            }
        }
        return $data;
    }

    /**
     * 领用出库-添加默认配置项
     * @param array $user 当前登陆者信息组
     * @return array
     */
    public function getAddDefault($user)
    {
        $code = ErrCode::$SUCCESS;
        $message = 'success';
        $data = [];
        try {
            $now_date = date("Ymd");
            $data['no'] = static::genSerialNo(MaterialAssetOutStorageEnums::MATERIAL_ASSET_OUT_STORAGE_NO_PREFIX, RedisKey::MATERIAL_ASSET_OUT_STORAGE_COUNTER);
            $data['apply_date'] = date("Y-m-d", strtotime($now_date));
            $data['create_id'] = $user['id'];
            $data['create_name'] = $user['name'];
        } catch (\Exception $e) {
            $code = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $this->logger->warning('material-asset-out-storage_add_default-failed:'.$e->getMessage());
        }
        return [
            'code' => $code,
            'message' => $message,
            'data' => $data
        ];
    }

    /**
     * 获取额外的参数组信息
     * @param array $params 请求参数
     * @param array $validate_param 验证参数组
     * @return mixed
     */
    public function getExtendValidation($params, $validate_param)
    {
        //是否是关联了申请单号
        if (!empty($params['apply_id'])) {
            $validate_param['products[*].apply_product_id'] = 'Required|IntGt:0';//资产申请单-资产明细表ID
        }
        $update_to_scm = $params['update_to_scm'];
        if ($update_to_scm == MaterialClassifyEnums::MATERIAL_CATEGORY_OFF) {
            //非更新至SCM
            $validate_param['products[*].asset_code'] = 'Required|StrLenGeLe:1,100';//资产编码
            $validate_param['products[*].sn_code'] = 'StrLenGeLe:0,50';//sn编码
        } else if ($update_to_scm == MaterialClassifyEnums::MATERIAL_CATEGORY_NO) {
            //是更新至SCM
            $validate_param['products[*].this_time_num'] = 'Required|IntGeLe:1,1000|>>>:'.static::$t->_('material_asset_out_storage_number_error');//本次发放数量
            $validate_param['products[*].stock_id'] = 'Required|StrLenGeLe:1,10';//仓库ID
            $validate_param['products[*].stock_name'] = 'Required|StrLenGeLe:1,255';//仓库名称
        }
        //根据配置判断成本中心是否必填
        $validate_param['pc_code'] = 'StrLenGeLe:0,255';//成本中心
        if (isset($params['staff_id'])){
            $is_must = MaterialSettingService::getInstance()->getPcCodeRequired($params['staff_id']);
            if ($is_must){
                $validate_param['pc_code'] = 'Required|StrLenGeLe:1,255';//成本中心
            }
        }
        return $validate_param;
    }

    /**
     * 14828需求，增加去SCM实时查询available Inventory
     * 若库存小于本次发放数量，后端增加限制，不可提交，页面增加toast提示：“存在库存不足的资产，请删除该行后再提交！”
     * @param array $params 请求参数
     * @return bool
     * @throws ValidationException
     */
    public function checkAvailableInventory($params)
    {
        //是通过SCM出库，必须传递货主和仓库
        if (!empty($params['update_to_scm']) && $params['update_to_scm'] == MaterialClassifyEnums::MATERIAL_CATEGORY_NO && !empty($params['mach_code']) && !empty($params['stock_id'])) {
            $barcode_arr = array_column($params['products'], 'barcode');
            //去SCM查询查询各Barcode可用库存
            $barcode_available_inventory = $this->handleUpdateToScmBarcode($barcode_arr, $params['mach_code'], $params['stock_id'], static::$language);
            foreach ($params['products'] as $product) {
                if (empty($barcode_available_inventory[$product['barcode']]) || !is_numeric($barcode_available_inventory[$product['barcode']]) || $barcode_available_inventory[$product['barcode']] < $product['this_time_num']) {
                    throw new ValidationException(static::$t->_('scm_barcode_available_inventory_not_enough'), ErrCode::$VALIDATE_ERROR);
                }
            }
        }
        return true;
    }

    /**
     * 领用出库-添加
     * @param array $params 请求参数组
     * @param array $user 当前登陆者信息组
     * @return array
     */
    public function add($params, $user)
    {
        $code = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $data = [];
        $db = $this->getDI()->get('db_oa');
        try {
            $db->begin();
            //操作时间
            $now_time = date('Y-m-d H:i:s', time());
            $params['now_time'] = $now_time;
            //验证参数
            [$data, $asset_update_data, $update_log_data, $transfer_data] = $this->validationSubmit($params, $user);
            //保存出库单等信息
            $this->saveOutStorage($data, $asset_update_data, $update_log_data, $transfer_data, $user, $db);
            $db->commit();
        } catch (ValidationException $e) {
            $code = $e->getCode();
            $message = $e->getMessage();
        } catch (BusinessException $e) {
            // 业务错误（数据错误等），不可对外抛出
            $code = $e->getCode();
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        } catch (\Exception $e) {
            // 系统错误，不可对外抛出
            $code = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        }
        if (!empty($message)) {
            $db->rollback();
        }
        if (!empty($real_message)) {
            $this->logger->error('material-asset-out-storage-add failed:' . $real_message . json_encode($data));
        }
        return [
            'code' => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data' => []
        ];
    }

    /**
     * 验证数据提交合法性以及组装数据
     * @param array $params 请求参数组
     * @param array $user 当前登陆者信息组
     * @param array|object $asset_apply_info 关联申请单信息
     * @param array|object $asset_apply_product_list 关联申请单-资产明细
     * @return array
     * @throws ValidationException
     * @throws BusinessException
     */
    private function validationSubmit($params, $user, $asset_apply_info = [], $asset_apply_product_list = [])
    {
        //判断领用出库编码是否已存在
        $no = $params['no'];
        $asset_out_storage = MaterialAssetOutStorageModel::findFirst([
            'conditions'=>'no=:no: and is_deleted=:is_deleted:',
            'bind'=>['no'=>$no, 'is_deleted'=>MaterialClassifyEnums::IS_DELETED_NO]
        ]);
        if (!empty($asset_out_storage)) {
            throw new ValidationException(static::$t->_('material_asset_out_storage_no_existed'), ErrCode::$VALIDATE_ERROR);
        }
        //检测个人代理是否可以转移
        $to_user_info = (new HrStaffRepository())->getStaffById($params['staff_id']);
        if (empty($to_user_info)) {
            throw new ValidationException(static::$t->_('re_staff_info_id_not_exist'), ErrCode::$VALIDATE_ERROR);
        }
        //19888 领用员工只能是在职，待离职的员工
        if ($to_user_info['state'] != StaffInfoEnums::STAFF_STATE_IN) {
            throw new ValidationException(static::$t->_('material_asset_out_storage_staff_id_error'), ErrCode::$VALIDATE_ERROR);
        }
        //19311 限制出库的员工雇佣类型
        $asset_out_storage_staff_hire_type_limit = EnumsService::getInstance()->getSettingEnvValueIds('material_asset_out_storage_staff_hire_type');
        if (in_array($to_user_info['hire_type'], $asset_out_storage_staff_hire_type_limit)) {
            throw new ValidationException(static::$t->_('material_asset_out_storage_staff_hire_type_limit'), ErrCode::$VALIDATE_ERROR);
        }
        //如果是 lnt 公司 验证 barcode 是否在配置内
        $lntNotice = AssetApplyService::getInstance()->checkLntBarcode($params['products'], $to_user_info);
        if(!empty($lntNotice)){
            throw new ValidationException(static::$t->_('lnt_barcode_notice', ['code_str' => $lntNotice]), ErrCode::$VALIDATE_ERROR);
        }

        //组装更新资产台账信息组
        $asset_update_data = [];
        //组装资产台账更新操作日志
        $update_log_data = [];
        //组装转移数据
        $transfer_data = [];
        //是否更新至scm
        $update_to_scm = $params['update_to_scm'];
        //出库明细行清单
        $products = $params['products'];
        //scm出库单号
        $scm_no = "";
        //传递过来的barcode组
        $barcode_arr = array_values(array_filter(array_unique(array_column($products, 'barcode'))));
        if ($update_to_scm == MaterialClassifyEnums::MATERIAL_CATEGORY_NO) {
            //更新至SCM；第一步判断是否存在相同barcode
            if (count($barcode_arr) < count($products)) {
                throw new ValidationException(static::$t->_('material_asset_out_storage_barcode_unique_error'), ErrCode::$VALIDATE_ERROR);
            }
            //查询barcode信息
            $barcode_list = MaterialSauModel::find([
                'conditions' => 'is_deleted=:is_deleted: and barcode in ({barcode:array})',
                'bind'=>['is_deleted'=>MaterialClassifyEnums::IS_DELETED_NO, 'barcode'=>$barcode_arr]
            ])->toArray();
            //第二步判断存在删除barcode
            $diff_barcode = array_diff($barcode_arr, array_column($barcode_list, 'barcode'));
            if (!empty($diff_barcode)) {
                throw new ValidationException(static::$t->_('material_asset_barcode_enable_or_delete', ['barcode'=>implode(";", $diff_barcode)]), ErrCode::$VALIDATE_ERROR);
            }
            //未启用barcode
            $enable_barcode = [];
            //非资产类的barcode
            $not_asset_barcode = [];
            //非更新至SCM的barcode
            $not_update_to_scm_barcode = [];
            foreach ($barcode_list as $item) {
                if ($item['status'] == MaterialClassifyEnums::MATERIAL_PROHIBITED_USE) {
                    $enable_barcode[] = $item['barcode'];
                } elseif ($item['category_type'] != MaterialClassifyEnums::MATERIAL_CATEGORY_TYPE_ASSET) {
                    $not_asset_barcode[] = $item['barcode'];
                } elseif ($item['update_to_scm'] != MaterialClassifyEnums::MATERIAL_CATEGORY_NO) {
                    $not_update_to_scm_barcode[] = $item['barcode'];
                }
            }
            //第三步判断存在非启用barcode
            if (!empty($enable_barcode)) {
                throw new ValidationException(static::$t->_('material_asset_barcode_enable_or_delete', ['barcode'=>implode(";", $enable_barcode)]), ErrCode::$VALIDATE_ERROR);
            }
            //第四步判断存在非资产barcode
            if (!empty($not_asset_barcode)) {
                throw new ValidationException(static::$t->_('material_asset_barcode_not_asset', ['barcode'=>implode(";", $not_asset_barcode)]), ErrCode::$VALIDATE_ERROR);
            }
            //第五步判断存在不更新至scm的barcode
            if (!empty($not_update_to_scm_barcode)) {
                throw new ValidationException(static::$t->_('material_asset_barcode_not_to_scm', ['barcode'=>implode(";", $not_update_to_scm_barcode)]), ErrCode::$VALIDATE_ERROR);
            }
            //第六步判断若关联申请单，每行资产是否超过资产申请数量
            $this->validationRelatedApplySubmit(MaterialClassifyEnums::MATERIAL_CATEGORY_NO, $params, $asset_apply_info, $asset_apply_product_list);
            //第七步判断个人代理是否可出库
            AssetTransferService::getInstance()->checkPersonalAgentCanTransfer([], $to_user_info, $barcode_arr, 2);
            //第八步通过验证后，调取SCM出库单接口
            $scm = new ScmService();
            //19888需求 使用地是other时也需要传递领用人所属部门成本中心
            $scm_params = $params;
            $scm_params['nodeSn'] = is_numeric($scm_params['receive_store_id']) ? $params['pc_code'] : $scm_params['receive_store_id'];
            $scm_params['deliveryWay'] = ScmService::$delivery_way_list[$params['delivery_way']];
            $scm_no = $scm->warehouseAdd($params['mach_code'], $params['stock_id'], $scm_params);
        } elseif ($update_to_scm == MaterialClassifyEnums::MATERIAL_CATEGORY_OFF) {
            //非更新至SCM;第一步判断个人代理出的资产每样只能是一个
            if ($to_user_info['hire_type'] == StaffInfoEnums::HIRE_TYPE_PERSONAL_AGENCY && count($barcode_arr) < count($products)) {
                throw new ValidationException(static::$t->_('material_asset_barcode_permission_error'), ErrCode::$VALIDATE_ERROR);
            }
            //第二步判断存在非闲置状态资产
            $asset_code_arr = array_filter(array_unique(array_column($products, 'asset_code')));
            $asset_list = MaterialAssetsModel::find([
               'conditions'=>'asset_code in({code:array}) and is_deleted=:is_deleted: and bar_code in({barcode:array})',
               'bind'=>['code'=>$asset_code_arr, 'is_deleted'=>MaterialClassifyEnums::IS_DELETED_NO, 'barcode' => $barcode_arr]
            ])->toArray();
            if (empty($asset_list) || count($asset_code_arr) != count($asset_list)) {
                throw new ValidationException(static::$t->_('material_asset_out_storage_include_non_idle_asset'), ErrCode::$VALIDATE_ERROR);
            }
            //第三步状态值超过2个以上或者状态值里不存在闲置状态
            $status_arr = array_unique(array_column($asset_list, 'status'));
            if (count($status_arr) > 1 || !in_array(MaterialEnums::ASSET_STATUS_UNUSED, $status_arr)) {
                throw new ValidationException(static::$t->_('material_asset_out_storage_include_non_idle_asset'), ErrCode::$VALIDATE_ERROR);
            }
            //第四步判断若关联申请单，每行资产是否超过资产申请数量
            $this->validationRelatedApplySubmit(MaterialClassifyEnums::MATERIAL_CATEGORY_OFF, $params, $asset_apply_info, $asset_apply_product_list);

            //第五步个人代理是否可出库
            AssetTransferService::getInstance()->checkPersonalAgentCanTransfer([], $to_user_info, $barcode_arr, 2);

            //第六步同步验证后，组装资产台账信息组
            $asset_update_info = [
                'aor_date' => $params['now_time'],//出库日期
                'aor_no' => $params['no'],//OA出库单号
                'status' => MaterialEnums::ASSET_STATUS_OUT_STORAGE,//出库中
                'updated_at' => $params['now_time']
            ];

            //资产转移 记录协议公司
            $fromStaff = empty($asset_list) ? [] : array_column($asset_list, 'staff_id');
            $toStaff   = [$params['staff_id']];
            $allStaff  = array_values(array_unique(array_merge($fromStaff, $toStaff)));
            //查询员工协议公司
            $staffData = HrStaffInfoModel::find([
                'columns'    => 'staff_info_id, contract_company_id',
                'conditions' => 'staff_info_id in ({ids:array})',
                'bind'       => ['ids' => $allStaff],
            ])->toArray();
            $staffData = empty($staffData) ? [] : array_column($staffData, 'contract_company_id', 'staff_info_id');
            //获取公司名称
            $companyList = EnumsService::getInstance()->getPayrollCompanyInfo();

            //转移数据
            $transfer_info = [
                'to_staff_id' => $params['staff_id'],
                'to_staff_name' => $params['staff_name'],
                'to_node_department_id' => $params['node_department_id'],
                'to_node_department_name' => $params['node_department_name'],
                'to_sys_store_id' => $params['sys_store_id'],
                'to_store_name' => $params['store_name'],
                'to_company_id' => $params['company_id'],
                'to_company_name' => $params['company_name'],
                'to_contract_company_id' => $staffData[$params['staff_id']] ?? 0,
                'to_contract_company_name' => $companyList[$staffData[$params['staff_id']] ?? 0] ?? '',
                'to_pc_code' => $params['pc_code'],
                'to_use_land' => $params['receive_name'] ?? '',
                'transfer_remark' => $params['mark'] ?? '',
                'transfer_operator_id' => $user['id'],
                'transfer_operator_name' => $user['name'],
                'transfer_type' => MaterialEnums::TRANSFER_TYPE_OUT_STORAGE,
                'status' => MaterialEnums::TRANSFER_LOG_STATUS_UNRECEIVED,
                'transfer_at' => $params['now_time'],
                'aor_date' => $params['now_time'],//出库日期
                'aor_no' => $params['no'],//OA出库单号
                'created_at' => $params['now_time'],
                'updated_at' => $params['now_time']
            ];
            //V19094出库转移使用网点取值逻辑变更
            if (!is_numeric($params['receive_store_id'])) {
                //网点
                $transfer_info['to_sys_store_id'] = $params['receive_store_id'];
                $transfer_info['to_store_name'] = $transfer_info['to_use_land'];
            } else {
                //总部地址
                $transfer_info['to_sys_store_id'] = Enums::HEAD_OFFICE_STORE_FLAG;
                $transfer_info['to_store_name'] = Enums::PAYMENT_HEADER_STORE_NAME;
            }

            //需要将出库单明细行上的使用方向反更新到资产台账表中
            $post_asset_data = array_column($products, null, 'asset_code');
            foreach ($asset_list as $asset) {
                //根据使用方向将资产台账进行分组
                $use = !empty($post_asset_data[$asset['asset_code']]) ? $post_asset_data[$asset['asset_code']]['use'] : MaterialEnums::USE_PERSONAL;
                if (!empty($asset_update_data[$use])) {
                    array_push($asset_update_data[$use]['update_ids'], $asset['id']);
                } else {
                    $asset_update_info['use'] = $use;
                    $asset_update_data[$use] = [
                        'update_data' => $asset_update_info,
                        'update_ids' => [$asset['id']]
                    ];
                }
                //组装更新资产台账日志记录
                $log_data = [];
                foreach ($asset_update_info as $k=>$item) {
                    if ($asset[$k] != $item) {
                        $log_data[] = [
                            'before' => $asset[$k],
                            'after' => $item,
                            'field_name' => $k
                        ];
                    }
                }
                if (!empty($log_data)) {
                    $update_log_data[] = [
                        'asset_code' => $asset['asset_code'],
                        'staff_id' => $user['id'],
                        'staff_name' => $user['name'],
                        'content' => json_encode($log_data, JSON_UNESCAPED_UNICODE),
                        'type' => MaterialEnums::OPERATE_TYPE_OUT,
                        'created_at' => $params['now_time'],
                        'updated_at' => $params['now_time'],
                    ];
                }
                //组装转移数据
                $transfer_info['asset_id'] = $asset['id'];
                $transfer_info['barcode'] = $asset['bar_code'];
                $transfer_info['asset_code'] = $asset['asset_code'];
                $transfer_info['from_staff_id'] = $asset['staff_id'];
                $transfer_info['from_node_department_id'] = $asset['node_department_id'];
                $transfer_info['from_node_department_name'] = $asset['node_department_name'];
                $transfer_info['from_sys_store_id'] = $asset['sys_store_id'];
                $transfer_info['from_store_name'] = $asset['store_name'];
                $transfer_info['from_company_id'] = $asset['company_id'];
                $transfer_info['from_company_name'] = $asset['company_name'];
                $transfer_info['from_contract_company_id'] = $staffData[$asset['staff_id']] ?? 0;
                $transfer_info['from_contract_company_name'] = $companyList[$staffData[$asset['staff_id']] ?? 0] ?? '';
                $transfer_info['from_pc_code'] = $asset['pc_code'];
                $transfer_info['from_use_land'] = $asset['use_land'];
                $transfer_data[] = $transfer_info;
            }
        }
        $params['scm_no'] = $scm_no;
        return [$params, $asset_update_data, $update_log_data, $transfer_data];
    }

    /**
     * 验证申请出库的申请数据合法性以及增加每行资产发放数量、申请单完全关联完打标记
     * @param integer $update_to_scm 是否更新至SCM
     * @param array $params 申请出库请求参数
     * @param object $asset_apply_info 资产申请单信息
     * @param object $asset_apply_product_list 资产申请单-资产明细
     * @throws BusinessException
     * @throws ValidationException
     */
    private function validationRelatedApplySubmit($update_to_scm, $params, $asset_apply_info, $asset_apply_product_list)
    {
        if (!empty($asset_apply_info) && !empty($asset_apply_product_list)) {
            //17900已被批量导入新增-锁定的关申请单-单个关联时不可提交
            if ($asset_apply_info->is_batch_related_lock == MaterialAssetApplyEnums::MATERIAL_ASSET_IS_BATCH_LOCK && empty($params['is_batch_related_lock'])) {
                throw new ValidationException(static::$t->_('material_asset_out_storage_apply_no_locked'), ErrCode::$VALIDATE_ERROR);
            }
            //非更新至SCM
            $apply_product_nums = [];
            $params_product_list = $params['products'];
            if ($update_to_scm == MaterialClassifyEnums::MATERIAL_CATEGORY_OFF) {
                foreach ($params_product_list as $product) {
                    if (empty($apply_product_nums[$product['apply_product_id']])) {
                        $apply_product_nums[$product['apply_product_id']]['apply_product_id'] = $product['apply_product_id'];
                        $apply_product_nums[$product['apply_product_id']]['this_time_num'] = 1;
                    } else {
                        $apply_product_nums[$product['apply_product_id']]['this_time_num'] += 1;
                    }
                }
                $params_product_list = $apply_product_nums;
            }

            $asset_apply_product_list_arr = $asset_apply_product_list->toArray();
            $asset_apply_product_list_arr = array_column($asset_apply_product_list_arr, null, 'id');
            //每行申请资产上的上一级审核人确定数量或未发放数量与每行出库资产上的申请数量作比较，避免超卖
            foreach ($params_product_list as $item) {
                foreach ($asset_apply_product_list as $product) {
                    if ($item['apply_product_id'] == $product->id) {
                        //申请单上未发放的数量 = 上一级审核人确定数量 - 累计发放数量
                        $last_unused_num = $product->last_time_num - $product->total_issued_num;
                        //申请出库的申请数据 >（申请单的上一级审核人确定数量或申请单上未发放的数量）
                        if ($product->last_time_num < $item['this_time_num'] || $last_unused_num < $item['this_time_num']) {
                            throw new ValidationException(static::$t->_('material_asset_out_storage_num_pass'), ErrCode::$VALIDATE_ERROR);
                        }
                        $product->total_issued_num += $item['this_time_num'];
                        $product->updated_at = $params['now_time'];
                        $bool = $product->save();
                        if ($bool === false) {
                            //更新资产申请单上资产明细行
                            throw new BusinessException('关联申请单[申请单资产明细行累计数量更新失败]: 待处理数据: ' . json_encode($product->toArray(), JSON_UNESCAPED_UNICODE) . '; 可能存在的问题: ' . get_data_object_error_msg($product), ErrCode::$MATERIAL_ASSET_OUT_STORAGE_RELATED_ERROR);
                        }
                        $asset_apply_product_list_arr[$product->id]['total_issued_num']+= $item['this_time_num'];
                    }
                }
            }

            //该申请单总的一级审核人确定数量
            $asset_apply_total_apply_num = array_sum(array_column($asset_apply_product_list_arr, 'last_time_num'));
            //该申请单总的累计发放数量
            $asset_apply_total_issued_num = array_sum(array_column($asset_apply_product_list_arr, 'total_issued_num'));
            //判断该申请单所有的上一级审核人确定数量和是否跟所有的累计发放数量和相等
            if ($asset_apply_total_apply_num == $asset_apply_total_issued_num) {
                $asset_apply_info->is_all_related = MaterialAssetApplyEnums::IS_ALL_RELATED_YES;
                $asset_apply_info->updated_at = $params['now_time'];
                $bool = $asset_apply_info->save();
                if ($bool === false) {
                    //更新资产申请单上是否完全关联状态
                    throw new BusinessException('关联申请单[申请单完全关联状态更新失败]: 待处理数据: ' . json_encode($asset_apply_info->toArray(), JSON_UNESCAPED_UNICODE) . '; 可能存在的问题: ' . get_data_object_error_msg($asset_apply_info), ErrCode::$MATERIAL_ASSET_OUT_STORAGE_RELATED_ERROR);
                }
            }
        }
    }

    /**
     * 保存出库单、出库单资产明细；更新资产台账信息；保存资产台账更新日志
     * @param array $params 请求参数组
     * @param array $asset_update_data 资产台账信息组
     * @param array $update_log_data 资产台账更新日志组
     * @param array $transfer_data 资产转移数据组
     * @param array $user 当前登陆者信息组
     * @param object $db 数据库
     * @throws BusinessException
     */
    private function saveOutStorage($params, $asset_update_data, $update_log_data, $transfer_data, $user, $db)
    {
        $now_time = $params['now_time'];
        //领用出库单信息组
        $order_data = [
            'no' => $params['no'],
            'scm_no' => $params['scm_no'],
            'staff_id' => $params['staff_id'],
            'staff_name' => $params['staff_name'],
            'company_id' => $params['company_id'],
            'company_name' => $params['company_name'],
            'node_department_id' => $params['node_department_id'],
            'node_department_name' => $params['node_department_name'],
            'sys_store_id' => $params['sys_store_id'],
            'store_name' => $params['store_name'],
            'job_id' => $params['job_id'],
            'job_name' => $params['job_name'],
            'pc_code' => $params['pc_code'],
            'receive_store_id' => $params['receive_store_id'],
            'receive_name' => $params['receive_name'],
            'province_code' => $params['province_code'],
            'city_code' => $params['city_code'],
            'district_code' => $params['district_code'],
            'postal_code' => $params['postal_code'],
            'address' => $params['address'],
            'create_id' => $user['id'],
            'create_name' => $user['name'],
            'apply_date' => $params['apply_date'],
            'mark' => $params['mark'],
            'status' => $params['update_to_scm'] == MaterialClassifyEnums::MATERIAL_CATEGORY_OFF ? MaterialAssetOutStorageEnums::STATUS_OUT : MaterialAssetOutStorageEnums::STATUS_WAIT_APPROVE,
            'update_to_scm' => $params['update_to_scm'],
            'mach_code' => $params['mach_code'] ?? '',
            'mach_name' => $params['mach_name'] ?? '',
            'stock_id' => $params['stock_id'] ?? '',
            'apply_id' => $params['apply_id'] ?? 0,
            'apply_no' => $params['apply_no'] ?? '',
            'delivery_way' => $params['delivery_way'],
            'is_deleted' => MaterialClassifyEnums::IS_DELETED_NO,
            'created_at' => $now_time,
            'updated_at' => $now_time
        ];
       //资产领用出库单信息入库
        $asset_out_order = new MaterialAssetOutStorageModel();
        $bool = $asset_out_order->i_create($order_data);
        if ($bool === false) {
            throw new BusinessException('领用出库单[出库单信息添加失败]: 待处理数据: '. json_encode($order_data, JSON_UNESCAPED_UNICODE) . '; 可能存在的问题: ' . get_data_object_error_msg($asset_out_order), ErrCode::$MATERIAL_ASSET_OUT_STORAGE_ADD_ERROR);
        }

        //领用出库明细信息组
        $products = [];
        foreach ($params['products'] as $product) {
            $product_info = [
                'aor_id' => $asset_out_order->id,
                'aor_no' => $order_data['no'],
                'barcode' => $product['barcode'],
                'name_zh' => $product['name_zh'],
                'name_en' => $product['name_en'],
                'name_local' => $product['name_local'],
                'category_id' => $product['category_id'],
                'category_name' => $product['category_name'],
                'category_code' => $product['category_code'],
                'finance_category_id' => $product['finance_category_id'],
                'finance_category_name' => $product['finance_category_name'],
                'finance_category_code' => $product['finance_category_code'],
                'asset_code' => $product['asset_code'] ?? '',
                'sn_code' => $product['sn_code'] ?? '',
                'unit_zh' => $product['unit_zh'],
                'unit_en' => $product['unit_en'],
                'model' => $product['model'],
                'use' => $product['use'],
                'stock_id' => $product['stock_id'] ?? '',
                'stock_name' => $product['stock_name'] ?? '',
                'this_time_num' => ($order_data['update_to_scm'] == MaterialClassifyEnums::MATERIAL_CATEGORY_OFF) ? 1 : $product['this_time_num'],
                'real_quantity_received' => ($order_data['update_to_scm'] == MaterialClassifyEnums::MATERIAL_CATEGORY_OFF) ? 1 : 0,
                'real_unit' => ($order_data['update_to_scm'] == MaterialClassifyEnums::MATERIAL_CATEGORY_OFF) ? $product['unit_en']: '',
                'apply_product_id' => $product['apply_product_id'] ?? 0,
                'is_deleted' => MaterialClassifyEnums::IS_DELETED_NO,
                'created_at' => $now_time,
                'updated_at' => $now_time
            ];
            $products[] = $product_info;
        }
        //出库单明细入库
        $out_storage_product = new MaterialAssetOutStorageProductModel();
        if (!$out_storage_product->batch_insert($products)) {
            throw new BusinessException('领用出库单[出库单信息明细添加失败]: 待处理数据: '. json_encode($products, JSON_UNESCAPED_UNICODE) . '; 可能存在的问题: ' . get_data_object_error_msg($out_storage_product), ErrCode::$MATERIAL_ASSET_OUT_STORAGE_ADD_ERROR);
        }

        //存在要更新的资产台账信息
        if (!empty($asset_update_data)) {
            $material_asset_table_name =  (new MaterialAssetsModel())->getSource();
            foreach ($asset_update_data as $item) {
                $bool = $db->updateAsDict(
                    $material_asset_table_name,
                    $item['update_data'],
                    ['conditions' => "id IN (".implode(',', $item['update_ids']).") and is_deleted=".MaterialClassifyEnums::IS_DELETED_NO]
                );
                if (!$bool) {
                    throw new BusinessException('领用出库单[更新资产台账信息失败]: 待处理数据: '. json_encode($item, JSON_UNESCAPED_UNICODE), ErrCode::$MATERIAL_ASSET_OUT_STORAGE_ADD_ERROR);
                }
            }
        }
        if (!empty($transfer_data)) {
            //转移记录入库
            $transfer_batch_model = new MaterialAssetTransferBatchModel();
            $transfer_batch_data = [
                'staff_id' => $user['id'],
                'staff_name' => $this->getNameAndNickName($user['name'], $user['nick_name']),
                'type' => MaterialEnums::TRANSFER_TYPE_BATCH,
                'status' => MaterialEnums::TRANSFER_BATCH_STATUS_UNRECEIVED,
                'created_at' => $now_time,
                'updated_at' => $now_time
            ];
            $bool = $transfer_batch_model->i_create($transfer_batch_data);
            if ($bool === false) {
                throw new BusinessException('领用出库单[资产转移头信息添加失败]: 待处理数据: ' . json_encode($transfer_batch_data, JSON_UNESCAPED_UNICODE) . '; 可能的原因是: ' . get_data_object_error_msg($transfer_batch_model), ErrCode::$MATERIAL_ASSET_OUT_STORAGE_TRANSFER_BATCH_ADD_ERROR);
            }
            $transfer_log_model = new MaterialAssetTransferLogModel();
            foreach ($transfer_data as &$transfer_data_v) {
                $transfer_data_v['batch_id'] = $transfer_batch_model->id;
            }
            if (!$transfer_log_model->batch_insert($transfer_data)) {
                throw new BusinessException('领用出库单[资产转移明细添加失败]: 待处理数据: ' . json_encode($transfer_data, JSON_UNESCAPED_UNICODE) . '; 可能存在的问题: ' . get_data_object_error_msg($transfer_log_model), ErrCode::$MATERIAL_ASSET_OUT_STORAGE_TRANSFER_LOG_ADD_ERROR);
            }
        }
        //存在要记录的资产台账更新日志
        if (!empty($update_log_data)) {
            $update_log_model = new MaterialAssetUpdateLogModel();
            $log_bool = $update_log_model->batch_insert($update_log_data);
            if ($log_bool === false) {
                throw new BusinessException('领用出库单[记录台账更新日志失败]: 待处理数据: '. json_encode($update_log_data, JSON_UNESCAPED_UNICODE) . '; 可能存在的问题: ' . get_data_object_error_msg($update_log_model), ErrCode::$MATERIAL_ASSET_OUT_STORAGE_ADD_ERROR);
            }
        }
    }

    /**
     * 获取barcode筛选条数
     * @param array $params 请求参数组
     * @return int
     */
    public function getSearchBarcodeCount($params)
    {
        $category_type = $params['category_type'];
        $builder = $this->modelsManager->createBuilder();
        $builder->from(['ms'=>MaterialSauModel::class]);
        if ($category_type == MaterialClassifyEnums::MATERIAL_CATEGORY_TYPE_WMS) {
            $builder->leftjoin(MaterialSauSkuModel::class, 'ms.id=mss.sau_id  and mss.is_deleted = ' . MaterialClassifyEnums::IS_DELETED_NO, 'mss');
        }
        $builder->leftjoin(MaterialCategoryModel::class, 'ms.category_id=mc.id', 'mc');
        $builder->leftjoin(MaterialFinanceCategoryModel::class, 'ms.finance_category_id=mfc.id', 'mfc');
        $builder->columns('count(ms.id) as total, ms.is_contain_job');
        $builder = StandardService::getInstance()->getListCondition(static::$language, $builder, $params);
        //由于资产类barcode对应多个职位的关系表，要筛选职位需要分组过滤未设置职位、设置包含筛选职位、设置不包含筛选职位才可搜索到barcode
        if (!empty($params['job_id']) && $category_type == MaterialClassifyEnums::MATERIAL_CATEGORY_TYPE_ASSET) {
            $count = $builder->getQuery()->execute()->count();
        } else {
            $total_info = $builder->getQuery()->getSingleResult();
            $count = intval($total_info->total);
        }
        return $count;
    }

    /**
     * 搜索标准型号
     * @param string $locale 当前语种
     * @param array $params 搜索条件
     * @return array
     */
    public function searchBarcode($locale, $params)
    {
        $code = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $data = [];
        $items = [];
        try {
            $page_size = empty($params['pageSize']) ? 20 : $params['pageSize'];
            $page_num = empty($params['pageNum']) ? 1 : $params['pageNum'];
            $offset = $page_size * ($page_num - 1);
            $use_val = '';
            $has_base_barcode = $params['has_base_barcode'] ?? false;//是否设定barcode筛选范围
            $base_barcode = $params['base_barcode'] ?? [];//设定的barcode组
            //未设定barcode筛选范围 或 设定了范围也必须有值才能查询
            $count = 0;
            if ($has_base_barcode === false || ($has_base_barcode && $base_barcode)) {
                $params['category_type'] = isset($params['category_type']) ? $params['category_type'] : MaterialClassifyEnums::MATERIAL_CATEGORY_TYPE_ASSET;
                $count = $this->getSearchBarcodeCount($params);
                if ($count > 0) {
                    $builder = $this->modelsManager->createBuilder();
                    $builder->from(['ms' => MaterialSauModel::class]);
                    if ($params['category_type'] == MaterialClassifyEnums::MATERIAL_CATEGORY_TYPE_WMS) {
                        $builder->leftjoin(MaterialSauSkuModel::class, 'ms.id=mss.sau_id  and mss.is_deleted = ' . MaterialClassifyEnums::IS_DELETED_NO, 'mss');
                        $use_val = ', mss.use_val';
                    }
                    $builder->leftjoin(MaterialCategoryModel::class, 'ms.category_id=mc.id', 'mc');
                    $builder->leftjoin(MaterialFinanceCategoryModel::class, 'ms.finance_category_id=mfc.id', 'mfc');
                    $builder = StandardService::getInstance()->getListCondition($locale, $builder, $params);
                    $builder->columns('ms.id,barcode,name_zh,name_en,name_local,unit_zh,unit_en,model,ms.update_to_scm,ms.is_contain_job,mc.id category_id, mc.name category_name, mc.code category_code, mfc.id finance_category_id, mfc.name finance_category_name, mfc.code finance_category_code' . $use_val);
                    $builder->limit($page_size, $offset);
                    $items = $builder->getQuery()->execute()->toArray();
                    $items = $this->handleSauList($items, $locale, $params['mach_code'] ?? '', $params['stock_id'] ?? '', $params);
                }
            }

            $data = [
                'items' => $items,
                'pagination' => [
                    'current_page' => $page_num,
                    'per_page' => $page_size,
                    'total_count' => $count,
                ]
            ];
        } catch (ValidationException $e) {
            // 校验错误，可对外抛出
            $code = $e->getCode();
            $message = $e->getMessage();
        } catch (\Exception $e) {
            $code = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage();
            $this->logger->warning('material-asset-out-storage-search-barcode-failed:' . $real_message . ' select :' . json_encode($params));
        }
        return [
            'code' => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data' => $data
        ];
    }

    /**
     * 格式化标准型号列表
     * @param array $items 标准型号列表
     * @param string $locale 当前语种
     * @param string $mach_code 货主编码
     * @param string $warehouse_id 仓库ID
     * @param array $params 传入的参数数据
     * @return array
     * @throws ValidationException
     */
    private function handleSauList($items, $locale, $mach_code, $warehouse_id, $params)
    {
        if (!empty($items)) {
            //获取barcode图片信息
            $materialAttachment = new  MaterialAttachmentModel();
            $pic_arr_key_arr = $materialAttachment->getColumnArr($items);
            //更新至scm的barcode组
            $update_to_scm_barcode = [];
            //非更新至scm的barcode组
            $not_update_to_scm_barcode = [];
            foreach ($items as $item) {
                if ($item['update_to_scm'] == MaterialClassifyEnums::MATERIAL_CATEGORY_NO) {
                    $update_to_scm_barcode[] = $item['barcode'];
                } else {
                    $not_update_to_scm_barcode[] = $item['barcode'];
                }
            }
            if ($params['category_type'] == MaterialClassifyEnums::MATERIAL_CATEGORY_TYPE_WMS) {
                //17404【ALL|OA/BY 耗材管理】耗材出库拆分以及库存查询逻辑优化
                $store_storage_rules_list = WarehouseDivisionRuleService::getInstance()->getStoreStorageRulesList($params);
                if (!empty($store_storage_rules_list)) {
                    //一个设置下同一个公司的货主是唯一的
                    $mach_code = $store_storage_rules_list[0]['mach_code'];
                    $warehouse_id = implode(',', array_column($store_storage_rules_list, 'stock_id'));
                    //开始处理更新至scm的各个barcode的可用库存
                    $barcode_available_inventory = $this->handleUpdateToScmBarcode($update_to_scm_barcode, $mach_code, $warehouse_id);
                } else {
                    //耗材分组查询可用库存
                    $barcode_available_inventory = $this->handleGroupMerge($update_to_scm_barcode, $mach_code, $warehouse_id, $params['company_id'], $locale);
                }
            } else {
                //开始处理更新至scm的各个barcode的可用库存
                $barcode_available_inventory = $this->handleUpdateToScmBarcode($update_to_scm_barcode, $mach_code, $warehouse_id, $locale);
            }

            //开始处理非更新至scm的各个barcode的可用库存
            $asset_barcode_available_inventory = $this->handNotUpdateToScmBarcode($not_update_to_scm_barcode);
            foreach ($items as &$item) {
                if ($item['update_to_scm'] == MaterialClassifyEnums::MATERIAL_CATEGORY_NO) {
                    $item['available_inventory'] = $barcode_available_inventory[$item['barcode']] ?? 0;
                } else {
                    $item['available_inventory'] = $asset_barcode_available_inventory[$item['barcode']] ?? 0;
                }
                $item['show_name'] = $item['name_'.(MaterialClassifyEnums::$language_fields[$locale] ?? 'local')];
                $item['show_unit'] = $item['unit_'.(MaterialClassifyEnums::$language_fields[$locale] ?? 'en')];
                $item['pic'] = $pic_arr_key_arr[$item['id']] ?? [];
            }
        }
        return $items ?? [];
    }

    /**
     * 分组处理不同的货主 去scm查可用库存
     * @param array $update_to_scm_barcode 数据
     * @param string $default_mach_code 货主
     * @param string $warehouse_id 仓库
     * @param string $company_id 所属公司
     * @param string $locale
     * @return array
     * @throws ValidationException
     */
    public function handleGroupMerge(array $update_to_scm_barcode, $default_mach_code, $warehouse_id, $company_id, $locale)
    {
        $available_inventory = [];
        $sau_storage_arr     = MaterialSauStorageModel::find([
            'conditions' => 'is_deleted = :is_deleted: and company_id = :company_id: and barcode in ({barcode:array})',
            'bind'       => ['is_deleted' => MaterialClassifyEnums::IS_DELETED_NO, 'company_id' => $company_id, 'barcode' => array_values($update_to_scm_barcode)]
        ])->toArray();

        $mach_barcode_arr = array_unique(array_filter(array_column($sau_storage_arr, 'barcode')));
        //对barcode没有货主的数据 做默认货主去查询后合并
        $barcode_array_diff = array_diff($update_to_scm_barcode, $mach_barcode_arr);
        if (!empty($sau_storage_arr)) {

            //对查询出来的数据按照货主分组
            $mach_code_arr = array_unique(array_filter(array_column($sau_storage_arr, 'mach_code')));
            foreach ($mach_code_arr as $mach_code) {
                //处理当前公司下的 货主对应的barcode，一个货主会有多个barcode
                $to_scm_barcode = $this->handleSauScmBarcode($sau_storage_arr, $mach_code);
                //开始处理更新至scm的不同的货主对应的barcode的可用库存
                $available_inventory = $available_inventory + $this->handleUpdateToScmBarcode($to_scm_barcode, $mach_code, $warehouse_id, $locale);
            }
        }
        if (!empty($barcode_array_diff)) {
            $barcode_available_inventory_diff = $this->handleUpdateToScmBarcode($barcode_array_diff, $default_mach_code, $warehouse_id, $locale);
            $available_inventory              = $available_inventory + $barcode_available_inventory_diff;
        }

        return $available_inventory;
    }


    /**
     * 处理货主分组数据
     * @param array $sau_storage_arr 数据
     * @param string $mach_code 货主
     * @return array
     */
    public function handleSauScmBarcode(array $sau_storage_arr, string $mach_code)
    {
        $value = [];
        foreach ($sau_storage_arr as $item_storage) {
            if ($item_storage['mach_code'] == $mach_code) {
                $value[] = $item_storage['barcode'];
            }
        }
        return $value;

    }


    /**
     * 从scm端查询各个barcode的可用库存
     * @param array $update_to_scm_barcode 更新至scm的barcode组
     * @param string $mach_code 货主
     * @param string $warehouse_id 仓库
     * @param string $locale 当前语种
     * @return array
     * @throws ValidationException
     */
    public function handleUpdateToScmBarcode($update_to_scm_barcode, $mach_code, $warehouse_id = '', $locale = '')
    {
        //查询到库存的barcode组
        $has_stock_barcode = [];
        //查询不到的barcode组
        $err_barcode = [];
        //每个barcode的可用库存组
        $barcode_available_inventory = [];
        if ($update_to_scm_barcode) {
            //获取barcode在SCM可售库存
            $scm = new ScmService();
            $scm_result = $scm->goodsStock($mach_code, $warehouse_id, implode(",", $update_to_scm_barcode), $locale);
            $scm_result_data = $scm_result['data'];
            if (!empty($scm_result_data)) {
                $has_stock_barcode = !empty($scm_result_data['stock']) ? $scm_result_data['stock'] : [];
                $err_barcode = !empty($scm_result_data['errBarcode']) ? $scm_result_data['errBarcode'] : [];
            }
            foreach ($update_to_scm_barcode as $barcode) {
                if (!empty($has_stock_barcode) && !empty($has_stock_barcode[$barcode]) && isset($has_stock_barcode[$barcode]['availableInventory'])) {
                    $barcode_available_inventory[$barcode] = $has_stock_barcode[$barcode]['availableInventory'];
                } else {
                    if (!empty($err_barcode) && in_array($barcode, $err_barcode)) {
                        $barcode_available_inventory[$barcode] = static::$t->_('material_sau_not_existed');
                    } else {
                        $barcode_available_inventory[$barcode] = $scm_result['message'];
                    }
                }
            }
        }
        return $barcode_available_inventory ?? [];
    }

    /**
     * 从资产台账中查找到各barcode下面的资产数量
     * @param array $not_update_to_scm_barcode 非更新至scm的barcode组
     * @return array
     */
    public function handNotUpdateToScmBarcode($not_update_to_scm_barcode)
    {
        $asset_barcode_available_inventory = [];
        if (!empty($not_update_to_scm_barcode)) {
            $available_inventory_arr = AssetAccountService::getInstance()->getBarcodeAssetCount([
                'barcode' => $not_update_to_scm_barcode,
                'status' => MaterialEnums::ASSET_STATUS_UNUSED
            ]);
            foreach ($not_update_to_scm_barcode as $barcode) {
                if (!empty($available_inventory_arr[$barcode])) {
                    $asset_barcode_available_inventory[$barcode] = $available_inventory_arr[$barcode];
                } else {
                    $asset_barcode_available_inventory[$barcode] = 0;
                }
            }
        }
        return $asset_barcode_available_inventory;
    }

    /**
     * 搜索资产台账列表
     * @param string $locale 当前语种
     * @param array $params 搜索条件
     * @return array
     */
    public function searchAsset($locale, $params)
    {
        $code = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $data = [];
        $items = [];
        try {
            $page_size = empty($params['pageSize']) ? 20 : $params['pageSize'];
            $page_num = empty($params['pageNum']) ? 1 : $params['pageNum'];
            $offset = $page_size * ($page_num - 1);
            $builder = $this->modelsManager->createBuilder();
            $builder->from(MaterialAssetsModel::class);
            $builder->where(
                'is_deleted = :is_deleted: and status =:status:',
                [
                    'is_deleted'=>MaterialClassifyEnums::IS_DELETED_NO,
                    'status'=>MaterialEnums::ASSET_STATUS_UNUSED,
                ]
            );
            //资产编码
            if (isset($params['asset_code']) && !empty($params['asset_code'])) {
                $builder->andWhere('asset_code like :asset_code:', ['asset_code' => '%'.trim($params['asset_code']).'%']);
            }
            //sn编码
            if (isset($params['sn_code']) && !empty($params['sn_code'])) {
                $builder->andWhere('sn_code like :sn_code:', ['sn_code' => '%'.trim($params['sn_code']).'%']);
            }
            //barcode搜索
            if (isset($params['barcode']) && !empty($params['barcode'])) {
                if (is_array($params['barcode'])) {
                    $builder->inWhere('bar_code', $params['barcode']);
                } else {
                    $builder->andWhere('bar_code like :barcode:', ['barcode' => '%'.trim($params['barcode']).'%']);
                }
            }
            //barcode精确查找
            if (isset($params['barcode_exact']) && !empty($params['barcode_exact'])) {
                $builder->andWhere('bar_code = :barcode:', ['barcode' => $params['barcode_exact']]);
            }
            //名称搜索
            if (isset($params['name']) && !empty($params['name'])) {
                $builder->andWhere('name_'.(MaterialClassifyEnums::$language_fields[$locale] ?? 'local').' like :name:', ['name' => '%' . trim($params['name']) . '%']);
            }
            //规格
            if (isset($params['model']) && !empty($params['model'])) {
                $builder->andWhere('model like :model:', ['model' => '%' . trim($params['model']) . '%']);
            }
            $builder->columns('count(id) as total');
            $total_info = $builder->getQuery()->getSingleResult();
            $count = intval($total_info->total);
            if ($count > 0) {
                $builder->columns('id,bar_code barcode,name_zh,name_en,name_local,unit_zh,unit_en,model,status,asset_code,sn_code,category_id, category_name, category_code, finance_category_id, finance_category_name, finance_category_code');
                $builder->limit($page_size, $offset);
                $items = $builder->getQuery()->execute()->toArray();
                $items = $this->handleAssetList($items, $locale);
            }
            $data = [
                'items' => $items,
                'pagination' => [
                    'current_page' => $page_num,
                    'per_page' => $page_size,
                    'total_count' => $count,
                ]
            ];
        } catch (\Exception $e) {
            $code = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage();
            $this->logger->warning('material-asset-out-storage-search-asset-failed:' . $real_message . ' select :' . json_encode($params));
        }
        return [
            'code' => $code,
            'message' => $message,
            'data' => $data
        ];
    }

    /**
     * 格式化资产台账列表
     * @param array $items 标准型号列表
     * @param string $locale 当前语种
     * @return array
     */
    private function handleAssetList($items, $locale)
    {
        if (!empty($items)) {
            [$barcode_key_list, $pic_arr_key_arr] = $this->handleBarcode($items);
            foreach ($items as &$item) {
                $item['show_name'] = $item['name_'.(MaterialClassifyEnums::$language_fields[$locale] ?? 'local')];
                $item['show_unit'] = $item['unit_'.(MaterialClassifyEnums::$language_fields[$locale] ?? 'en')];
                $item['status_text'] = static::$t[MaterialEnums::$asset_status[$item['status']]];
                if (!empty($barcode_key_list[$item['barcode']])) {
                    $item['pic'] = $pic_arr_key_arr[$barcode_key_list[$item['barcode']]['id']] ?? [];
                } else {
                    $item['pic'] = [];
                }
            }
        }
        return $items ?? [];
    }

    /**
     * 领用出库-列表
     * @param array $user 当前登录着信息组
     * @param array $condition 设置参数
     * @param bool $export 是否导出，true导出、false非导出
     * @param integer $count 导出记录数
     * @return array
     */
    public function getList(array $user, array $condition, $export = false, $count = 0)
    {
        $page_size = empty($condition['pageSize']) ? MaterialEnums::PAGE_SIZE : $condition['pageSize'];
        $page_num  = empty($condition['pageNum']) ? MaterialEnums::PAGE_NUM : $condition['pageNum'];
        $offset    = $page_size * ($page_num - 1);
        $code    = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $data = [
            'items'      => [],
            'pagination' => [
                'current_page' => $page_num,
                'per_page'     => $page_size,
                'total_count'  => 0,
            ],
        ];

        try {
            if ($export === false) {
                //列表查询总数，导出无需查询，在验证总导出数限制时已查询到，传递过来了
                $count = $this->getListCount($user, $condition);
            }
            if ($count) {
                $builder = $this->modelsManager->createBuilder();
                if ($export === false) {
                    //非导出
                    $columns = 'main.id, no, apply_no, status, staff_id, staff_name, job_name, store_name, node_department_name, company_name, create_id, create_name, apply_date, scm_no, main.delivery_way';
                } else {
                    $columns = "main.no, main.apply_no,main.scm_no,main.status,main.create_id,main.create_name,main.apply_date,main.staff_id,main.staff_name,main.company_name,main.node_department_name,main.store_name,main.pc_code,main.receive_name, main.delivery_way, ";
                    $columns .= "main.province_code,main.city_code,main.district_code,main.postal_code,main.address,main.mark,main.update_to_scm,main.mach_name,product.stock_id,product.stock_name, ";
                    $columns .= "product.barcode,product.name_zh,product.name_en,product.name_local,product.unit_zh,product.unit_en,product.model,ma.asset_code,ma.sn_code,product.asset_code as product_asset_code,product.sn_code as product_sn_code,product.use,product.category_name,product.category_code,product.finance_category_name,product.finance_category_code,product.this_time_num,";
                    $columns .= 'mini.hire_type';
                }
                $builder->columns($columns);
                $builder->from(['main'=>MaterialAssetOutStorageModel::class]);

                if ($export === true) {
                    $builder->leftjoin(MaterialAssetOutStorageProductModel::class, 'main.id=product.aor_id ', 'product');
                    $builder->leftjoin(MaterialAssetTransferLogModel::class, 'log.aor_no=main.no and log.barcode=product.barcode and product.asset_code IN ("", log.asset_code) and log.transfer_type = ' . MaterialEnums::TRANSFER_TYPE_OUT_STORAGE . ' and log.is_deleted = ' . Enums\GlobalEnums::IS_NO_DELETED, 'log');
                    $builder->leftjoin(MaterialAssetsModel::class, 'ma.id=log.asset_id', 'ma');
                    $builder->leftjoin(MiniHrStaffInfoModel::class, 'mini.staff_info_id = main.staff_id', 'mini');
                } else {
                    $builder->leftjoin(MaterialAssetOutStorageProductModel::class, 'main.id=product.aor_id', 'product');
                }
                //组合搜索条件
                $builder = $this->getCondition($user, $builder, $condition);
                $builder->limit($page_size, $offset);
                $builder->orderby('main.id desc');
                if ($export === false) {
                    //非导出是出库单信息组
                    $builder->groupBy('main.id');
                }
                $items_obj = $builder->getQuery()->execute();
                $items = $items_obj ? $items_obj->toArray() : [];
                $items = $this->handleListItems($items, $export);
            }
            $data['items']                     = $items ?? [];
            $data['pagination']['total_count'] = $count;
        } catch (ValidationException $e) {
            $code = $e->getCode();
            $message = $e->getMessage();
        }  catch (\Exception $e) {
            $code         = ErrCode::$SYSTEM_ERROR;
            $message      = static::$t->_('retry_later');
            $real_message = $e->getMessage() . $e->getTraceAsString();
        }
        if (!empty($real_message)) {
            $this->logger->warning('get-material-asset-account-list-failed:' . $real_message);
        }
        return [
            'code'    => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data'    => $data,
        ];
    }

    /**
     * 获取特定条件下的总数
     *
     * @param array $user 当前登陆者信息组
     * @param array $condition 筛选条件组
     * @return int
     * @throws ValidationException
     */
    public function getListCount(array $user, array $condition)
    {
        $builder = $this->modelsManager->createBuilder();
        $builder->from(['main'=>MaterialAssetOutStorageModel::class]);
        $builder->leftjoin(MaterialAssetOutStorageProductModel::class, 'main.id=product.aor_id', 'product');
        $builder->columns("main.id");
        //组合搜索条件
        $builder = $this->getCondition($user, $builder, $condition);
        $builder->groupBy('main.id');
        return (int) $builder->getQuery()->execute()->count();
    }

    /**
     * 组装查询条件
     *
     * @param array $user 当前登录者信息组
     * @param object $builder 查询器
     * @param array $condition 查询条件
     * @return mixed
     * @throws ValidationException
     */
    public function getCondition(array $user, object $builder, array $condition)
    {
        //资产数据管控组-可看数据权限范围
        $data_permission = MaterialSettingService::getInstance()->getStaffDataPermissionGroup($user['id'], MaterialSettingService::DATA_PERMISSION_ASSET_OUT);

        //创建时间起始与结束校验
        $create_start = !empty($condition['create_date_begin']) ? strtotime($condition['create_date_begin']) : '';
        $create_end = !empty($condition['create_date_end']) ? strtotime($condition['create_date_end']) : '';
        if ($create_start > $create_end) {
            throw new ValidationException(self::$t->_('material_asset_list_date_error'), ErrCode::$VALIDATE_ERROR);
        }
        $no = !empty($condition['no']) ? $condition['no'] : ''; //领用单号
        $staff_id = !empty($condition['staff_id']) ? $condition['staff_id'] : 0;//负责人工号
        $create_id = !empty($condition['create_id']) ? $condition['create_id'] : 0;//处理人工号
        $status = !empty($condition['status']) ? $condition['status'] : 0;//状态
        $sys_store_id = !empty($condition['sys_store_id']) ? $condition['sys_store_id'] : '';//使用网点
        $receive_store_id = !empty($condition['receive_store_id']) ? $condition['receive_store_id'] : [];//使用部门/网点
        $node_department_id = !empty($condition['node_department_id']) ? $condition['node_department_id'] : 0;//使用部门
        $company_id = !empty($condition['company_id']) ? $condition['company_id'] : 0;//所属公司

        $barcode = !empty($condition['barcode']) ? $condition['barcode'] : '';//barcode
        $name = !empty($condition['name']) ? $condition['name'] : '';//资产名称
        $model = !empty($condition['model']) ? $condition['model'] : '';//规格型号
        $use = !empty($condition['use']) ? $condition['use'] : 0;//使用方向
        $delivery_way = !empty($condition['delivery_way']) ? $condition['delivery_way'] : 0;//配送方式
        $apply_no = !empty($condition['apply_no']) ? $condition['apply_no'] : '';//关联申请单号
        $scm_no = !empty($condition['scm_no']) ? $condition['scm_no'] : '';//scm出库单号

        $locale = static::$language;
        $builder->where('main.is_deleted = :is_deleted:', ['is_deleted'=>MaterialClassifyEnums::IS_DELETED_NO]);

        //资产数据管控组-可看数据权限范围
        if (!empty($data_permission['node_department_id'])) {
            $builder->inWhere('main.node_department_id', $data_permission['node_department_id']);
        }

        if (!empty($no)) {
            //领用单号
            $builder->andWhere('main.no = :no:', ['no' => $no]);
        }
        if (!empty($staff_id)) {
            //使用人员工工号
            $builder->andWhere('main.staff_id =:staff_id:', ['staff_id'=>$staff_id]);
        }
        if (!empty($create_id)) {
            //处理人工号
            $builder->andWhere('main.create_id =:create_id:', ['create_id'=>$create_id]);
        }
        if (!empty($status)) {
            //使用状态搜索
            $builder->andWhere('main.status =:status:', ['status'=>$status]);
        }
        if (!empty($sys_store_id)) {
            //按照使用网点搜索
            $builder->andWhere('main.sys_store_id =:sys_store_id:', ['sys_store_id' => $sys_store_id]);
        }
        if (!empty($receive_store_id)) {
            //使用部门/网点
            $builder->inWhere('main.receive_store_id', $receive_store_id);
        }
        if (!empty($node_department_id)) {
            //按照部门查找,需要找该部门以及下属子部门的所有信息
            $department_service = new DepartmentService();
            $department_ids = $department_service->getChildrenListByDepartmentIdV2($node_department_id,true);
            array_push($department_ids,$node_department_id);
            $builder->andWhere('main.node_department_id in ({department_ids:array})', ['department_ids' => $department_ids]);
        }
        if (!empty($company_id)) {
            //所属公司
            $builder->andWhere('main.company_id = :company_id:', ['company_id'=>$company_id]);
        }
        if (!empty($create_start)) {
            //创建日期-起始
            $builder->andWhere('main.created_at >= :created_start:', ['created_start'=>date('Y-m-d 00:00:00',$create_start)]);
        }
        if (!empty($create_end)) {
            //创建日期-截止
            $builder->andWhere('main.created_at <= :created_end:', ['created_end'=>date('Y-m-d 23:59:59',$create_end)]);
        }
        if (!empty($apply_no)) {
            //关联申请单号
            $builder->andWhere('main.apply_no = :apply_no:', ['apply_no'=>$apply_no]);
        }

        if (!empty($barcode)) {
            //资产编码
            $builder->andWhere('product.barcode = :barcode:', ['barcode' => $barcode]);
        }
        if (!empty($name)) {
            //资产名称搜索
            $builder->andWhere('product.name_'.(MaterialClassifyEnums::$language_fields[$locale] ?? 'local').' like :name:', ['name' => '%' . $name . '%']);
        }
        if (!empty($model)) {
            //规格型号搜索
            $builder->andWhere('product.model like :model:', ['model' => '%' . $model . '%']);
        }
        if (!empty($use)) {
            //使用方向
            $builder->andWhere('product.use = :use:', ['use'=>$use]);
        }
        if (!empty($delivery_way)) {
            //配送方式
            $builder->andWhere('main.delivery_way = :delivery_way:', ['delivery_way'=>$delivery_way]);
        }
        if (!empty($scm_no)) {
            //scm出库单号
            $builder->andWhere('main.scm_no like :scm_no:', ['scm_no' => $scm_no . '%']);
        }
        return $builder;
    }

    /**
     * 格式化出库单列表
     * @param array $items 台账列表
     * @param bool $export 导出
     * @return array
     */
    private function handleListItems($items, $export)
    {
        if (empty($items)) {
            return [];
        }
        $locale = static::$language;
        if ($export === true) {
            //导出
            $row_value = [];
            //获取省信息
            $province_code = array_values(array_filter(array_unique(array_column($items, 'province_code'))));
            $province_list = SysProvinceRepository::getInstance()->getListByCodes($province_code);
            //获取市信息
            $city_code = array_values(array_filter(array_unique(array_column($items, 'city_code'))));
            $city_list = SysCityRepository::getInstance()->getListByCodes($city_code);
            //获取区信息
            $district_code = array_values(array_filter(array_unique(array_column($items, 'district_code'))));
            $district_list = SysDistrictRepository::getInstance()->getListByCodes($district_code);
            //获取雇佣类型枚举
            $hire_type_enum = EnumsService::getInstance()->getHireTypeEnum();
            //组装导出需要的数据
            foreach ($items as $item) {
                $name = $item['name_'.(MaterialClassifyEnums::$language_fields[$locale] ?? 'local')];
                $name = empty($name) ? $item['name_en'] : $name;
                $row_value[] = [
                    'no' => $item['no'],
                    'apply_no' => $item['apply_no'],
                    'scm_no'=>$item['scm_no'],
                    'status_text' => static::$t[MaterialAssetOutStorageEnums::$asset_out_storage_status[$item['status']]],
                    'create_id'=>$item['create_id'],
                    'create_name'=>$item['create_name'],
                    'apply_date'=>$item['apply_date'],
                    'delivery_way_text' => static::$t[MaterialClassifyEnums::$delivery_way_arr[$item['delivery_way']]],
                    'staff_id' => $item['staff_id'],
                    'staff_name' => $item['staff_name'],
                    'hire_type_text' => $hire_type_enum[$item['hire_type']] ?? '',//领用员工雇佣类型
                    'company_name'=>$item['company_name'],
                    'node_department_name' => $item['node_department_name'],
                    'store_name' => $item['store_name'],
                    'pc_code' => $item['pc_code'],
                    'receive_name'=>$item['receive_name'],
                    'province_name'=> !empty($province_list[$item['province_code']]) ? $province_list[$item['province_code']]['name'] : '',
                    'city_name'=>!empty($city_list[$item['city_code']]) ? $city_list[$item['city_code']]['name'] : '',
                    'district_name'=>!empty($district_list[$item['district_code']]) ? $district_list[$item['district_code']]['name'] : '',
                    'postal_code'=>$item['postal_code'],
                    'address' => $item['address'],
                    'mark'=>$item['mark'],
                    'update_to_scm'=>($item['update_to_scm'] == MaterialClassifyEnums::MATERIAL_CATEGORY_NO) ? 'Y' : 'N',
                    'mach_name' => $item['mach_name'],
                    'stock_name'=>$item['stock_name'],
                    'barcode'=>$item['barcode'],
                    'name' => $name,
                    'model' => $item['model'],
                    'unit' => $item['unit_'.(MaterialClassifyEnums::$language_fields[$locale] ?? 'en')],
                    'this_time_num' => $item['status'] == MaterialAssetOutStorageEnums::STATUS_OUT ? MaterialAssetOutStorageEnums::THIS_TIME_NUM : $item['this_time_num'],
                    'asset_code' => $item['status'] == MaterialAssetOutStorageEnums::STATUS_OUT ? $item['asset_code'] : $item['product_asset_code'],
                    'sn_code'    => $item['status'] == MaterialAssetOutStorageEnums::STATUS_OUT ? $item['sn_code'] : $item['product_sn_code'],
                    'use' => static::$t[MaterialEnums::$use[$item['use']]],
                    'finance_category_code' => $item['finance_category_code'],
                    'finance_category_name' => $item['finance_category_name'],
                    'category_code' => $item['category_code'],
                    'category_name' => $item['category_name'],
                    'scm_no' => $item['scm_no']
                ];
            }
            $items = $row_value;
        } else {
            //非导出
            foreach ($items as &$item) {
                $item['status_text'] = static::$t[MaterialAssetOutStorageEnums::$asset_out_storage_status[$item['status']]];
                $item['delivery_way_text'] = static::$t[MaterialClassifyEnums::$delivery_way_arr[$item['delivery_way']]];
            }
        }
        return $items;
    }

    /**
     * 获取资产领用出库导出的表头
     */
    public function getExportExcelHeaderFields()
    {
        return [
            static::$t->_('material_asset_out.no'),//OA资产出库单号
            static::$t->_('material_asset_out.apply_no'),//关联申请单号
            static::$t->_('material_asset_out.scm_no'),//SCM出库单号
            static::$t->_('material_asset_out.status_text'),//单据状态
            static::$t->_('material_wms_staff_info_id'),//创建人工号
            static::$t->_('material_wms_staff_info_name'),//创建人姓名
            static::$t->_('supplier.016'),//创建时间
            static::$t->_('delivery_way'),//配送方式
            static::$t->_('material_asset_out.staff_id'),//领用员工工号
            static::$t->_('material_asset_out.staff_name'),//领用员工姓名
            static::$t->_('material_asset_out.hire_type'),//领用员工雇佣类型
            static::$t->_('acceptance_cost_company'),//费用所属公司
            static::$t->_('acceptance_cost_department'),//费用所属部门
            static::$t->_('bank_flow_export_field_create_store_name'),//费用所属网点
            static::$t->_('sap_cost_center'),//成本中心
            static::$t->_('material_asset_out.receive_name'),//收件人信息
            static::$t->_('material_asset_out.province'),//省
            static::$t->_('material_asset_out.city'),//市
            static::$t->_('material_asset_out.district'),//区
            static::$t->_('material_asset_out.postal'),//邮编
            static::$t->_('material_asset_out.address'),//详细地址
            static::$t->_('bank_flow_export_field_remark'),//备注
            static::$t->_('material_asset_out.update_to_scm'),//是否通过SCM出库
            static::$t->_('material_asset_out.mach_name'),//货主
            static::$t->_('material_asset_out.stock_id'),//仓库
            static::$t->_('material_asset.barcode'),//barcode
            static::$t->_('material_asset.name'),//资产名称
            static::$t->_('material_asset.model'),//规格型号
            static::$t->_('material_asset.unit'),//单位
            static::$t->_('material_asset.this_time_num'),//本次方法数量
            static::$t->_('material_asset.asset_code'),//资产编码
            static::$t->_('material_asset.sn_code'),//sn码
            static::$t->_('material_asset.use'),//使用方向
            static::$t->_('material_asset.finance_category_code'),//财务分类编码
            static::$t->_('material_asset.finance_category_name'),//财务分类名称
            static::$t->_('material_asset.category_code'),//物料分类编码
            static::$t->_('material_asset.category_name'),//物料分类名称
        ];
    }

    /**
     * 领用出库-导出svc文件 - 废弃
     * @param string $locale 当前语种
     * @param array $condition 查询参数
     * @param array $user 当前登陆者信息组
     * @return array
     */
    public function exportSvc($locale, $condition, $user)
    {
        try {
            $count = $this->getExportListCount($locale, $condition);
            if ($count > MaterialAssetOutStorageEnums::MATERIAL_ASSET_OUT_EXPORT_LIMIT) {
                throw new ValidationException(static::$t->_('material_asset_data_limit_error', ['max'=>MaterialAssetOutStorageEnums::MATERIAL_ASSET_OUT_EXPORT_LIMIT]), ErrCode::$VALIDATE_ERROR);
            }
            $file_name = "material_asset_out_" . date("YmdHis");
            //设置好告诉浏览器要下载excel文件的headers
            header('Content-Encoding:UTF-8');
            header('Content-Type: application/vnd.ms-excel;charset=UTF-8');
            header('Content-Disposition: attachment; filename="'. $file_name .'.csv"');
            //跨域设置
            header('Access-Control-Allow-Origin:*' );
            // 响应类型
            header('Access-Control-Allow-Methods:POST,GET');
            // 带 cookie 的跨域访问
            header('Access-Control-Allow-Credentials: true');
            // 响应头设置
            header('Access-Control-Allow-Headers:x-requested-with,Content-Type,Authorization,Content-Disposition');
            //打开php标准输出流
            $fp = fopen('php://output', 'a');
            //添加BOM头，以UTF8编码导出CSV文件，如果文件头未添加BOM头，打开会出现乱码。
            fwrite($fp, chr(0xEF).chr(0xBB).chr(0xBF));

            $header = $this->getExportExcelHeaderFields();

            fputcsv($fp,$header);
            if ($count > 0) {
                $page_size = 2000;
                $step = ceil($count/$page_size);
                for ($i=1; $i<=$step; $i++) {
                    $condition['pageNum'] = $i;
                    $condition['pageSize'] = $page_size;
                    $list = $this->getList($locale, $condition,true, $count);
                    $row_values = $list['data']['items'];
                    foreach ($row_values as $item) {
                        fputcsv($fp, $item);
                    }
                    if (ob_get_level() > 0) {
                        ob_flush();
                    }
                    flush();//必须同时使用 ob_flush() 和flush() 函数来刷新输出缓冲。
                }
            }
            fclose($fp);
            $this->unLock( md5(MaterialAssetOutStorageEnums::MATERIAL_ASSET_OUT_STORAGE_EXPORT_LOCK . '_' . $user['id']));
            exit();
        } catch (ValidationException $e) {
            // 校验错误，可对外抛出
            $code = $e->getCode();
            $message = $e->getMessage();
        }  catch (\Exception $e) {
            $code    = ErrCode::$SYSTEM_ERROR;
            $message = $e->getMessage() . $e->getTraceAsString();
            $this->logger->warning('download-material-asset-out=storage-failed:' . $message . $e->getTraceAsString());
        }
        return [
            'code'    => $code ,
            'message' => $message,
            'data'    => '',
        ];
    }

    /**
     * 领用出库-作废
     * @param array $params 请求入参
     * @return array
     */
    public function cancel($params)
    {
        $code = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $result = false;
        $db = $this->getDI()->get('db_oa');
        try {
            $db->begin();
            $asset_out_storage = $this->getMaterialAssetOutStorageInfoById($params['id']);
            if ($asset_out_storage->status != MaterialAssetOutStorageEnums::STATUS_WAIT_APPROVE) {
                //仅待审核的出库单才可做作废操作
                throw new ValidationException(self::$t['material_asset_out_cancel_error'], ErrCode::$VALIDATE_ERROR);
            }
            $scm = new ScmService();
            $result = $scm->cancelOutbound($asset_out_storage->mach_code, $asset_out_storage->scm_no);
            if ($result) {
                //scm出库成功
                $now_time = date('Y-m-d H:i:s');
                $asset_out_storage->status = MaterialAssetOutStorageEnums::STATUS_CANCEL;
                $asset_out_storage->updated_at = $now_time;
                $bool = $asset_out_storage->save();
                if ($bool === false) {
                    throw new BusinessException('领用出库单[作废失败]: 待处理数据: '. json_encode($asset_out_storage->toArray(), JSON_UNESCAPED_UNICODE) . '; 可能存在的问题: ' . get_data_object_error_msg($asset_out_storage), ErrCode::$MATERIAL_ASSET_OUT_STORAGE_CANCEL_ERROR);
                }
                //如果是关联申请单创建的出库单，需要归还申请单上的累计发放数量，若申请单已完全发放，则要置为未完全发放
                if (!empty($asset_out_storage->apply_id)) {
                    $apply_info = MaterialAssetApplyModel::findFirst([
                        'conditions'=>'id=:id: and is_deleted = :is_deleted:',
                        'bind'=>['id'=>$asset_out_storage->apply_id, 'is_deleted'=>MaterialClassifyEnums::IS_DELETED_NO]
                    ]);
                    if (!empty($apply_info)) {
                        //若申请单已完全发放，则要置为未完全发放
                        if ($apply_info->is_all_related == MaterialAssetApplyEnums::IS_ALL_RELATED_YES) {
                            $apply_info->is_all_related =  MaterialAssetApplyEnums::IS_ALL_RELATED_NO;
                            $apply_info->updated_at = $now_time;
                            $bool = $apply_info->save();
                            if ($bool === false) {
                                throw new BusinessException('领用出库单作废[更新申请单是否全部关联状态失败]: 待处理数据: '. json_encode($apply_info->toArray(), JSON_UNESCAPED_UNICODE) . '; 可能存在的问题: ' . get_data_object_error_msg($apply_info), ErrCode::$MATERIAL_ASSET_OUT_STORAGE_CANCEL_ERROR);
                            }
                        }
                        //查询出库单-明细行
                        $products = $asset_out_storage->getProducts()->toArray();
                        foreach ($products as $item) {
                            $asset_apply_product_info = MaterialAssetApplyProductModel::findFirst([
                                'conditions'=>'id=:id: and apply_id = :apply_id: and is_deleted = :is_deleted:',
                                'bind'=>['id'=>$item['apply_product_id'], 'apply_id'=>$asset_out_storage->apply_id, 'is_deleted'=>MaterialClassifyEnums::IS_DELETED_NO]
                            ]);
                            if (!empty($asset_apply_product_info)) {
                                $asset_apply_product_info->total_issued_num -= $item['this_time_num'];
                                $asset_apply_product_info->updated_at = $now_time;
                                $bool = $asset_apply_product_info->save();
                                if ($bool === false) {
                                    throw new BusinessException('领用出库单作废[更新申请单资产明细累计发放数量失败]: 待处理数据: '. json_encode($asset_apply_product_info->toArray(), JSON_UNESCAPED_UNICODE) . '; 可能存在的问题: ' . get_data_object_error_msg($asset_apply_product_info), ErrCode::$MATERIAL_ASSET_OUT_STORAGE_CANCEL_ERROR);
                                }
                            }
                        }
                    }
                }
                $db->commit();
            }
        } catch (ValidationException $e) {
            // 校验错误，可对外抛出
            $code = $e->getCode();
            $message = $e->getMessage();
        } catch (BusinessException $e) {
            // 业务错误（数据错误等），不可对外抛出
            $code = $e->getCode();
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        } catch (\Exception $e) {
            // 系统错误，不可对外抛出
            $code = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        }
        if (!empty($message)) {
            $db->rollback();
        }
        if (!empty($real_message)) {
            $this->logger->error('material-asset-out-storage-cancel failed:' . $real_message);
        }
        return [
            'code' => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data' => $result
        ];
    }

    /**
     * 根据出库单ID获取资产资产出库单信息
     * @param int $id 资产出库单ID
     * @return mixed
     * @throws ValidationException
     */
    public function getMaterialAssetOutStorageInfoById($id)
    {
        $asset_out_storage = MaterialAssetOutStorageModel::findFirst([
            'conditions'=>'id=:id: and is_deleted=:is_deleted:',
            'bind'=>['id'=>$id, 'is_deleted'=>MaterialClassifyEnums::IS_DELETED_NO]
        ]);
        if (empty($asset_out_storage)) {
            throw new ValidationException(self::$t['material_asset_out_not_found'], ErrCode::$VALIDATE_ERROR);
        }
        return $asset_out_storage;
    }

    /**
     * 领用出库-详情
     * @param array $params 请求入参
     * @return array
     */
    public function detail($params)
    {
        $code = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $detail = [];
        try {
            $asset_out_storage = $this->getMaterialAssetOutStorageInfoById($params['id']);
            $detail = $asset_out_storage->toArray();

            //雇佣类型枚举
            $detail['hire_type'] = '';
            $detail['hire_type_text'] = '';
            if ($detail['staff_id']) {
                $staff_info = (new HrStaffRepository())->getStaffById($detail['staff_id']);
                $hire_type_enum = EnumsService::getInstance()->getHireTypeEnum();
                $detail['hire_type'] = $staff_info['hire_type'];
                $detail['hire_type_text'] = $hire_type_enum[$staff_info['hire_type']] ?? '';
            }

            if ($detail['update_to_scm']== MaterialClassifyEnums::MATERIAL_CATEGORY_OFF) {
                $products = MaterialAssetsModel::find([
                    'columns' => 'id as aor_id ,aor_no,asset_code,bar_code as barcode,name_en,name_local,name_zh,model,sn_code, unit_en,unit_zh,use',
                    'conditions' => 'aor_no =:aor_no: and is_deleted = :is_deleted:',
                    'bind' => ['aor_no'=>$detail['no'], 'is_deleted' => MaterialClassifyEnums::IS_DELETED_NO]
                ])->toArray();
            } else {
                $products = $asset_out_storage->getProducts()->toArray();
            }

            $detail['products'] = $products;
            $detail['products'] = $products ? $this->handleDetail($detail['products']) : [];
        } catch (ValidationException $e) {
            // 校验错误，可对外抛出
            $code = $e->getCode();
            $message = $e->getMessage();
        } catch (\Exception $e) {
            // 系统错误，不可对外抛出
            $code = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage();
            $this->logger->error('material-asset-out-storage-detail failed:' . $real_message);
        }
        return [
            'code' => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data' => $detail
        ];
    }

    /**
     * 根据含有barcode的数据中找到barcode并获取barcode的图片附件信息
     * @param array $list 含有barcode的数据
     * @return array
     */
    private function handleBarcode($list)
    {
        $barcode_key_list = [];
        $pic_arr_key_arr = [];
        if (!empty($list)) {
            $barcode_arr = array_values(array_filter(array_unique(array_column($list, "barcode"))));
            if (empty($barcode_arr)) {
                return [$barcode_key_list, $pic_arr_key_arr];
            }
            $barcode_list = MaterialSauModel::find([
                'columns' => 'id, barcode',
                'conditions' => 'barcode in ({barcode:array}) and is_deleted = :is_deleted:',
                'bind' => ['barcode'=>$barcode_arr, 'is_deleted' => MaterialClassifyEnums::IS_DELETED_NO]
            ])->toArray();
            if (!empty($barcode_list)) {
                $barcode_key_list = array_column($barcode_list, null, 'barcode');
                //获取barcode图片信息
                $materialAttachment = new  MaterialAttachmentModel();
                $pic_arr_key_arr = $materialAttachment->getColumnArr($barcode_list);
            }
        }
        return [$barcode_key_list, $pic_arr_key_arr];
    }

    /**
     * 格式化详情信息
     * @param array $product_list 出库单明细列表
     * @return array
     */
    private function handleDetail($product_list)
    {
        if (!empty($product_list)) {
            [$barcode_key_list, $pic_arr_key_arr] = $this->handleBarcode($product_list);
            foreach ($product_list as &$product) {
                if (!empty($barcode_key_list[$product['barcode']])) {
                    $product['pic'] = $pic_arr_key_arr[$barcode_key_list[$product['barcode']]['id']] ?? [];
                } else {
                    $product['pic'] = [];
                }
            }
        }
        return $product_list ?? [];
    }

    /**
     * 领用出库-名下资产
     * @param string $locale 当前语种
     * @param array $params 查询参数
     * @return array
     */
    public function ownerAsset($locale,$params)
    {
        $code = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $data = [];
        $items = [];
        try {
            $page_size = empty($params['pageSize']) ? 20 : $params['pageSize'];
            $page_num = empty($params['pageNum']) ? 1 : $params['pageNum'];
            $offset = $page_size * ($page_num - 1);
            $builder = $this->modelsManager->createBuilder();
            $builder->from(MaterialAssetsModel::class);
            $builder->where(
                'is_deleted = :is_deleted: and staff_id =:staff_id:',
                [
                    'is_deleted'=>MaterialClassifyEnums::IS_DELETED_NO,
                    'staff_id'=>$params['staff_id'],
                ]
            );
            $builder->columns('count(id) as total');
            $total_info = $builder->getQuery()->getSingleResult();
            $count = intval($total_info->total);
            if ($count > 0) {
                $builder->columns('id,bar_code barcode,name_zh,name_en,name_local,unit_zh,unit_en,model,asset_code,sn_code,use,use_land,status');
                $builder->limit($page_size, $offset);
                $items = $builder->getQuery()->execute()->toArray();
                $items = $this->handleOwnerList($items, $locale);
            }
            $data = [
                'items' => $items,
                'pagination' => [
                    'current_page' => $page_num,
                    'per_page' => $page_size,
                    'total_count' => $count,
                ]
            ];
        } catch (\Exception $e) {
            $code = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage();
            $this->logger->warning('material-asset-out-storage-owner-asset-failed:' . $real_message . ' select :' . json_encode($params));
        }
        return [
            'code' => $code,
            'message' => $message,
            'data' => $data
        ];
    }

    /**
     * 格式化明细资产列表
     * @param array $items 标准型号列表
     * @param string $locale 当前语种
     * @return array
     */
    private function handleOwnerList($items, $locale)
    {
        if (!empty($items)) {
            foreach ($items as &$item) {
                $item['show_name'] = $item['name_'.(MaterialClassifyEnums::$language_fields[$locale] ?? 'local')];
                $item['show_unit'] = $item['unit_'.(MaterialClassifyEnums::$language_fields[$locale] ?? 'en')];
                $item['status_text'] = static::$t[MaterialEnums::$asset_status[$item['status']]];
                $item['use_text'] = static::$t[MaterialEnums::$use[$item['use']]];
            }
        }
        return $items ?? [];
    }

    /**
     * OA回调接口-SCM出库成功
     * @param array $params scm回调请求次参数
     * @api https://yapi.flashexpress.pub/project/133/interface/api/61948
     * @return array
     */
    public function dealScmStatus($params)
    {
        $code    = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $data    = [];
        try {
            $this->logger->info('scm_callback_asset_out_storage_log' . json_encode($params));
            if ((new validSignService())->validator($params)) {
                //处理业务
                $response = $this->updateStorageOrder($params);
                $code     = $response['code'];
                $message  = $response['message'];
            }
        } catch (ValidationException $e) {
            $code    = ErrCode::$VALIDATE_ERROR;
            $message = $e->getMessage();
        } catch (\Exception $e) {
            $code    = $e->getCode();
            $message = $e->getMessage();
            $real_message = $e->getMessage();
        }
        if (!empty($real_message)) {
            $this->logger->warning('asset-out_storage-deal-failed:' . $real_message);
        }
        $this->logger->info('asset-out_storage-deal-return: code=' .$code.";message=".$message.";data=".json_encode($data));
        return [
            'code'    => $code,
            'message' => $message,
            'data'    => $data
        ];
    }

    /**
     * OA回调接口-SCM出库成功-变更出库单信息
     * @param array $params scm回调请求次参数
     * @return array
     */
    public function updateStorageOrder($params)
    {
        $code    = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $db      = $this->getDI()->get('db_oa');
        try {
            $db->begin();
            $now_time = date('Y-m-d H:i:s');
            //检查出库单
            $out_storage_data = MaterialAssetOutStorageModel::findFirst([
                'conditions' => 'scm_no = :orderSn: and status in ({status:array}) and is_deleted=:is_deleted:',
                'bind' => [
                    'orderSn' => $params['orderSn'],
                    'status'  => [MaterialAssetOutStorageEnums::STATUS_WAIT_APPROVE, MaterialAssetOutStorageEnums::STATUS_APPROVED_WAIT_OUT], 'is_deleted' => MaterialClassifyEnums::IS_DELETED_NO
                ]
            ]);
            if (empty($out_storage_data)) {
                throw new ValidationException('scm单号不存在或状态不对' . $params['orderSn'], ErrCode::$VALIDATE_ERROR);
            }

            //查询当前出库单商品信息
            $products = $out_storage_data->getProducts();
            $storage_products = $products->toArray();

            //比较出库单商品是否一致
            $product_barcode_arr = array_column($storage_products, 'barcode');
            $params_barcode_arr  = array_column($params['goods'], 'barCode');
            //产品要求回传与出库barcode不一致告警但不影响出库
            if (array_diff($product_barcode_arr, $params_barcode_arr) || array_diff($params_barcode_arr, $product_barcode_arr)) {
                $this->logger->notice('material-asset-out-storage-scm-recall-failed: scm实际出库商品不符, scm编号：[ ' . $params['orderSn'] . ' ], 出库申请单号：[ ' . $out_storage_data->no . ' ]');
            }

            //出库行明细依barcode为key组装数组
            $storage_products = array_column($storage_products, null, 'barcode');

            //传递过来的sn编码组
            $params_sn_code_arr = [];
            //首先根据传递过来的资产编码与资产台账上的资产编码进行匹配
            $params_asset_code_arr = [];
            //传递过来的barcode下的数量不一致的barcode组
            $barcode_num_error = [];
            //传递过来的barcode下无资产情况的数目
            $barcode_no_asset = [];
            foreach ($params['goods'] as $good) {
                //比较每一个barcode的出库数量
                if (!empty($storage_products[$good['barCode']]) && $good['num'] != $storage_products[$good['barCode']]['this_time_num']) {
                    $barcode_num_error[] = $good['barCode'];
                }
                if (!$good['asset']) {
                    //存在传递的barcode下无资产的情况
                    $barcode_no_asset[] = $good['barCode'];
                }
                foreach ($good['asset'] as $asset) {
                    $params_asset_code_arr[] = $asset['assetCode'];
                    if (!empty($asset['snCode'])) {
                        $params_sn_code_arr[] = $asset['snCode'];
                    }
                }
            }
            if (empty($params_asset_code_arr)) {
                throw new BusinessException('scm缺少资产编码传参, scm编号：[ ' . $params['orderSn'] . ' ], 出库申请单号：[ ' . $out_storage_data->no .' ]', ErrCode::$BUSINESS_ERROR);
            }
            //回调里存在barcode传递出库数量与申请数量不一致的barcode组需要告警；但不能影响正常barcode出库
            if ($barcode_num_error) {
                $this->logger->notice('material-asset-out-storage-scm-recall-failed: scm实际出库商品数量不符, 不符商品：[ ' . implode(',', $barcode_num_error) .' ], scm编号：[ ' . $params['orderSn'] . ' ], 出库申请单号：[ ' . $out_storage_data->no .' ]');
            }

            //回调既有barcode下有资产也有barcode下无资产情况也要告警;但不能影响有资产的barcode正常出库
            if ($barcode_no_asset && count($params['goods']) != count($barcode_no_asset)) {
                $this->logger->notice('material-asset-out-storage-scm-recall-failed: scm回调里存在barcode下缺少资产编码传参数据, scm编号：[ ' . $params['orderSn'] . ' ], 出库申请单号：[ ' . $out_storage_data->no .' ], barcode组：[' . implode(',', $barcode_no_asset) . ']');
            }

            $params_asset_code_arr = array_values(array_unique($params_asset_code_arr));
            $asset_list = MaterialAssetsModel::find([
                'conditions' => 'asset_code in ({codes:array}) and status=:status: and is_deleted=:is_deleted:',
                'bind' => ['codes' => $params_asset_code_arr, 'status' => MaterialEnums::ASSET_STATUS_UNUSED, 'is_deleted' => MaterialClassifyEnums::IS_DELETED_NO]
            ])->toArray();
            $asset_list = !empty($asset_list) ? array_column($asset_list, null, 'asset_code') : [];
            //其次如果存在根据资产编码未找到资产台账信息，则根据旧资产编码进行查找
            $old_asset_code_arr = array_diff($params_asset_code_arr,array_column($asset_list, 'asset_code'));
            $old_asset_list = [];
            if (!empty($old_asset_code_arr)) {
                $old_asset_list = MaterialAssetsModel::find([
                    'conditions' => 'old_asset_code in ({codes:array}) and status=:status: and is_deleted=:is_deleted:',
                    'bind' => ['codes' => array_values($old_asset_code_arr), 'status' => MaterialEnums::ASSET_STATUS_UNUSED, 'is_deleted' => MaterialClassifyEnums::IS_DELETED_NO]
                ])->toArray();
                $old_asset_list = !empty($old_asset_list) ? array_column($old_asset_list, null, 'old_asset_code') : [];
            }
            //防止数组索引从0开始保留原来数组索引结构
            $asset_list = $asset_list + $old_asset_list;
            //根据SCM传递过来的snCode查找资产台账库里是否已存在sn编码
            $sn_list = [];
            if (!empty($params_sn_code_arr)) {
                $params_sn_code_arr = array_unique($params_sn_code_arr);
                $sn_list = MaterialAssetsModel::find([
                    'columns' => 'sn_code',
                    'conditions' => 'sn_code in ({codes:array}) and is_deleted=:is_deleted:',
                    'bind' => ['codes' => $params_sn_code_arr, 'is_deleted' => MaterialClassifyEnums::IS_DELETED_NO]
                ])->toArray();
                $sn_list = !empty($sn_list) ? array_column($sn_list, 'sn_code') : [];
            }
            //协议签署公司
            $allStaff   = array_column($asset_list, 'staff_id');
            $allStaff[] = $out_storage_data->staff_id;
            $staffData = [];
            if(!empty($allStaff)){
                //查询员工协议公司
                $staffData = HrStaffInfoModel::find([
                    'columns'    => 'staff_info_id, contract_company_id',
                    'conditions' => 'staff_info_id in ({ids:array})',
                    'bind'       => ['ids' => $allStaff],
                ])->toArray();
                $staffData = empty($staffData) ? [] : array_column($staffData, 'contract_company_id', 'staff_info_id');
            }
            //获取公司名称
            $companyList = EnumsService::getInstance()->getPayrollCompanyInfo();

            //组装出库单明细更新信息组
            $material_asset_out_storage_product_data = [];
            //出库时资产编码匹配失败记录组
            $material_asset_out_storage_error_data = [];
            //组装资产台账更新信息组
            $material_asset_update_data = [];
            //组装资产台账更新操作日志
            $material_asset_update_log_data = [];
            //组装资产转移数据
            $material_transfer_data = [];
            foreach ($params['goods'] as $good) {
                //组装出库单行信息更新数据组
                $material_asset_out_storage_product_data[$storage_products[$good['barCode']]['id']] = [
                    'real_quantity_received' => $good['num'],
                    'real_unit' => $good['unit']
                ];
                //组装资产台账信息匹配失败组、更新资产台账信息组、资产台账更新操作日志
                foreach ($good['asset'] as $param_asset) {
                    //传递的资产编码是否能在资产台账中找到
                    if (!empty($asset_list[$param_asset['assetCode']])) {
                        //查找到的资产信息
                        $asset_info = $asset_list[$param_asset['assetCode']];
                        //找到后，判断该资产上的barcode与传递回来的barcode是否一致
                        if ($asset_info['bar_code'] == $good['barCode']) {
                            //一致，判断回传sn是否是空的
                            if (!empty($param_asset['snCode'])) {
                                if (!empty($asset_info['sn_code']) && $asset_info['sn_code'] != $param_asset['snCode']) {
                                    //资产台账上的sn编码不为空，跟回传回来的sn编码不一致，更新资产台账信息，但不更新sn编码，需要记录出库日志表
                                    $material_asset_out_storage_error_data[] = [
                                        'scm_return_json'=>json_encode($params),
                                        'order_sn'=>$params['orderSn'],
                                        'psno'=>$out_storage_data->no,
                                        'bar_code'=>$asset_info['bar_code'],
                                        'asset_code'=>$asset_info['asset_code'],
                                        'sn_code' => $asset_info['sn_code'],
                                        'reason' => 'SCM回调的sn编码与OA在台账中查到的资产对应的sn编码不一致',
                                        'source_type' =>2,
                                        'created_at'=>$now_time,
                                        'call_date'=>$now_time,
                                    ];
                                } else if (empty($asset_info['sn_code']) && !empty($sn_list) && in_array($param_asset['snCode'], $sn_list)) {
                                    //资产台账上的sn编码为空，回传回来的sn编码在资产台账中已存在，更新资产台账信息，但不更新sn编码，需要记录出库日志表
                                    $material_asset_out_storage_error_data[] = [
                                        'scm_return_json'=>json_encode($params),
                                        'order_sn'=>$params['orderSn'],
                                        'psno'=>$out_storage_data->no,
                                        'bar_code'=>$asset_info['bar_code'],
                                        'asset_code'=>$asset_info['asset_code'],
                                        'sn_code' => $param_asset['snCode'],
                                        'reason' => 'SCM回调的sn编码在资产台账中已存在',
                                        'source_type' =>2,
                                        'created_at'=>$now_time,
                                        'call_date'=>$now_time,
                                    ];
                                } else {
                                    //更新资产台账信息，更新sn编码
                                    $asset_info['sn_code'] = $param_asset['snCode'];
                                }
                            }
                            //担心回传回来的sn编码存在重复，故而验证通过后放入sn编码组里
                            array_push($sn_list, $param_asset['snCode']);
                            //组装更新的资产台账信息
                            //资产转移需求 取消了直接修改staff_id,staff_name,node_department_id,node_department_name,sys_store_id,store_name,job_id,job_name,company_id,company_name,pc_code,use_land
                            //资产转移接收时不更新use, 所以这里依然直接变更
                            $asset_update_info = [
                                'id' => $asset_info['id'],
                                'sn_code' => $asset_info['sn_code'],//sn编码
                                'use' => $storage_products[$good['barCode']]['use'],
                                'aor_date' => $param_asset['outBoundDate'],//出库日期
                                'aor_no' => $out_storage_data->no,//OA出库单号
                                'aor_scm_no' => $params['orderSn'],//scm出库单号
                                'status' => MaterialEnums::ASSET_STATUS_OUT_STORAGE,//出库中
                                'updated_at' => $now_time
                            ];
                            $material_asset_update_data[] = $asset_update_info;
                            //组装更新资产台账日志记录
                            $log_data = [];
                            foreach ($asset_update_info as $k=>$item) {
                                if ($asset_info[$k] != $item) {
                                    $log_data[] = [
                                        'before' => $asset_info[$k],
                                        'after' => $item,
                                        'field_name' => $k
                                    ];
                                }
                            }
                            if (!empty($log_data)) {
                                $material_asset_update_log_data[] = [
                                    'asset_code' => $asset_info['asset_code'],
                                    'staff_id' => '10000',
                                    'staff_name' => 'SuperAdmin',
                                    'content' => json_encode($log_data, JSON_UNESCAPED_UNICODE),
                                    'type' => MaterialEnums::OPERATE_TYPE_OUT,
                                    'created_at' => $now_time,
                                    'updated_at' => $now_time
                                ];
                            }
                            //组装资产转移数据
                            $material_transfer_info = [
                                'asset_id' => $asset_info['id'],
                                'barcode' => $asset_info['bar_code'],
                                'asset_code' => $asset_info['asset_code'],
                                'from_staff_id' => $asset_info['staff_id'],
                                'from_staff_name' => $asset_info['staff_name'],
                                'from_node_department_id' => $asset_info['node_department_id'],
                                'from_node_department_name' => $asset_info['node_department_name'],
                                'from_sys_store_id' => $asset_info['sys_store_id'],
                                'from_store_name' => $asset_info['store_name'],
                                'from_company_id' => $asset_info['company_id'],
                                'from_company_name' => $asset_info['company_name'],
                                'from_contract_company_id' => $staffData[$asset_info['staff_id']] ?? 0,
                                'from_contract_company_name' => $companyList[$staffData[$asset_info['staff_id']] ?? 0] ?? '',
                                'from_pc_code' => $asset_info['pc_code'],
                                'from_use_land' => $asset_info['use_land'],
                                'to_staff_id' => $out_storage_data->staff_id,
                                'to_staff_name' => $out_storage_data->staff_name,
                                'to_node_department_id' => $out_storage_data->node_department_id,
                                'to_node_department_name' => $out_storage_data->node_department_name,
                                'to_sys_store_id' => $out_storage_data->sys_store_id,
                                'to_store_name' => $out_storage_data->store_name,
                                'to_company_id' => $out_storage_data->company_id,
                                'to_company_name' => $out_storage_data->company_name,
                                'to_contract_company_id' => $staffData[$out_storage_data->staff_id] ?? 0,
                                'to_contract_company_name' => $companyList[$staffData[$out_storage_data->staff_id] ?? 0] ?? '',
                                'to_pc_code' => $out_storage_data->pc_code,
                                'to_use_land' => $out_storage_data->receive_name,
                                'transfer_operator_id' => $out_storage_data->create_id,
                                'transfer_operator_name' => $out_storage_data->create_name,
                                'transfer_type' => MaterialEnums::TRANSFER_TYPE_OUT_STORAGE,
                                'status' => MaterialEnums::TRANSFER_LOG_STATUS_UNRECEIVED,
                                'transfer_at' => $param_asset['outBoundDate'],
                                'aor_date' => $param_asset['outBoundDate'],//出库日期
                                'aor_no' => $out_storage_data->no,//OA出库单号
                                'aor_scm_no' => $params['orderSn'],//scm出库单号
                                'transfer_remark' => $out_storage_data->no . ',' .$out_storage_data->mark,//转移原因
                                'created_at' => $now_time,
                                'updated_at' => $now_time
                            ];
                            //V19094出库转移使用网点取值逻辑变更
                            if (!is_numeric($out_storage_data->receive_store_id)) {
                                //网点
                                $material_transfer_info['to_sys_store_id'] = $out_storage_data->receive_store_id;
                                $material_transfer_info['to_store_name'] = $out_storage_data->receive_name;
                            } else if ($out_storage_data->receive_store_id != 0) {
                                //非网点、非Other，总部地址
                                $material_transfer_info['to_sys_store_id'] = Enums::HEAD_OFFICE_STORE_FLAG;
                                $material_transfer_info['to_store_name'] = Enums::PAYMENT_HEADER_STORE_NAME;
                            }
                            $material_transfer_data[] = $material_transfer_info;
                        } else {
                            $material_asset_out_storage_error_data[] = [
                                'scm_return_json'=>json_encode($params),
                                'order_sn'=>$params['orderSn'],
                                'psno'=>$out_storage_data->no,
                                'bar_code'=>$asset_info['bar_code'],
                                'asset_code'=>$asset_info['asset_code'],
                                'sn_code' => $asset_info['sn_code'],
                                'reason' => 'SCM回调的barcode与OA在台账中查到的资产对应的barcode不一致',
                                'source_type' =>2,
                                'created_at'=>$now_time,
                                'call_date'=>$now_time,
                            ];
                        }
                    } else {
                        $material_asset_out_storage_error_data[] = [
                            'scm_return_json'=>json_encode($params),
                            'order_sn'=>$params['orderSn'],
                            'psno'=>$out_storage_data->no,
                            'bar_code'=>$good['barCode'],
                            'asset_code'=>$param_asset['assetCode'],
                            'sn_code' => $param_asset['snCode'],
                            'reason' => '在闲置状态下的资产编码和旧资产编码都未找到该资产编码对应的资产台账信息',
                            'source_type' =>2,
                            'created_at'=>$now_time,
                            'call_date'=>$now_time,
                        ];
                    }
                }
            }

            //--------------------------------验证都通过了，开始数据库操作-----------------------
            //更新出库单信息
            $out_storage_data->status = MaterialAssetOutStorageEnums::STATUS_OUT;
            $out_storage_data->updated_at = $now_time;
            $bool = $out_storage_data->save();
            if ($bool === false) {
                //更新出库单信息失败
                throw new BusinessException('SCM出库成功回调[出库单信息更新失败]: 待处理数据: ' . json_encode($out_storage_data->toArray(), JSON_UNESCAPED_UNICODE) . '; 可能存在的问题: ' . get_data_object_error_msg($out_storage_data), ErrCode::$MATERIAL_ASSET_OUT_STORAGE_SCM_RECALL_ERROR);
            }
            //更新出库单明细行
            foreach ($products as $product) {
                $update_product_info = $material_asset_out_storage_product_data[$product->id];
                $product->real_quantity_received = $update_product_info['real_quantity_received'];
                $product->real_unit = $update_product_info['real_unit'];
                $product->updated_at = $now_time;
                $bool = $product->save();
                if ($bool === false) {
                    //更新出库单明细行失败
                    throw new BusinessException('SCM出库成功回调[出库单行信息更新失败]: 待处理数据: ' . json_encode($product->toArray(), JSON_UNESCAPED_UNICODE) . '; 可能存在的问题: ' . get_data_object_error_msg($product), ErrCode::$MATERIAL_ASSET_OUT_STORAGE_SCM_RECALL_ERROR);
                }
            }
            //更新资产台账信息
            if (!empty($material_asset_update_data)) {
                foreach ($material_asset_update_data as $asset) {
                    $material_asset = MaterialAssetsModel::findFirst([
                        'conditions' => 'id = :id: and is_deleted = :is_deleted:',
                        'bind' => ['id' => $asset['id'], 'is_deleted' => MaterialClassifyEnums::IS_DELETED_NO]
                    ]);
                    $bool = $material_asset->i_update($asset);
                    if ($bool === false) {
                        throw new BusinessException('SCM出库成功回调[资产台账信息更新失败]: 待处理数据: ' . json_encode($asset, JSON_UNESCAPED_UNICODE) . '; 可能存在的问题: ' . get_data_object_error_msg($material_asset), ErrCode::$MATERIAL_ASSET_OUT_STORAGE_SCM_RECALL_ERROR);
                    }
                }
            }
            //转移记录入库
            if (!empty($material_transfer_data)) {
                $transfer_batch_model = new MaterialAssetTransferBatchModel();
                //获取入库单创建人信息
                $us = new UserService();
                $user = $us->getUserById($out_storage_data->create_id);
                $transfer_batch_data = [
                    'staff_id' => $out_storage_data->create_id,
                    'staff_name' => $this->getNameAndNickName($user->name ?? '', $user->nick_name ?? ''),
                    'type' => MaterialEnums::TRANSFER_TYPE_BATCH,
                    'status' => MaterialEnums::TRANSFER_BATCH_STATUS_UNRECEIVED,
                    'created_at' => $now_time,
                    'updated_at' => $now_time
                ];
                $bool = $transfer_batch_model->i_create($transfer_batch_data);
                if ($bool === false) {
                    throw new BusinessException('SCM出库成功回调[资产转移头信息添加失败]: 待处理数据: ' . json_encode($transfer_batch_data, JSON_UNESCAPED_UNICODE) . '; 可能的原因是: ' . get_data_object_error_msg($transfer_batch_model), ErrCode::$MATERIAL_ASSET_OUT_STORAGE_TRANSFER_BATCH_ADD_ERROR);
                }
                $transfer_log_model = new MaterialAssetTransferLogModel();
                foreach ($material_transfer_data as &$transfer_data_v) {
                    $transfer_data_v['batch_id'] = $transfer_batch_model->id;
                }
                if (!$transfer_log_model->batch_insert($material_transfer_data)) {
                    throw new BusinessException('SCM出库成功回调[资产转移明细添加失败]: 待处理数据: '. json_encode($material_transfer_data, JSON_UNESCAPED_UNICODE) . '; 可能存在的问题: ' . get_data_object_error_msg($transfer_log_model), ErrCode::$MATERIAL_ASSET_OUT_STORAGE_TRANSFER_LOG_ADD_ERROR);
                }
            }
            //插入资产台账更新操作日志
            if (!empty($material_asset_update_log_data)) {
                $update_log_model = new MaterialAssetUpdateLogModel();
                $log_bool = $update_log_model->batch_insert($material_asset_update_log_data);
                if ($log_bool === false) {
                    throw new BusinessException('SCM出库成功回调[插入资产台账更新操作日志失败]: 待处理数据: '. json_encode($material_asset_update_log_data, JSON_UNESCAPED_UNICODE) . '; 可能存在的问题: ' . get_data_object_error_msg($update_log_model), ErrCode::$MATERIAL_ASSET_OUT_STORAGE_SCM_RECALL_ERROR);
                }
            }
            //插入出库时资产编码匹配失败记录
            if (!empty($material_asset_out_storage_error_data)) {
                $scm_asset_unmatched_log = new MaterialScmAssetUnmatchedModel();
                $log_bool = $scm_asset_unmatched_log->batch_insert($material_asset_out_storage_error_data);
                if ($log_bool === false) {
                    throw new BusinessException('SCM出库成功回调[插入匹配失败资产日志失败]: 待处理数据: '. json_encode($material_asset_out_storage_error_data, JSON_UNESCAPED_UNICODE) . '; 可能存在的问题: ' . get_data_object_error_msg($scm_asset_unmatched_log), ErrCode::$MATERIAL_ASSET_OUT_STORAGE_SCM_RECALL_ERROR);
                }
            }
            $db->commit();
        } catch (ValidationException $e) {      //校验错误，可对外抛出
            $code    = $e->getCode();
            $message = $e->getMessage();
        } catch (BusinessException $e) {
            $code         = $code = $e->getCode();
            $message      = static::$t->_('retry_later');
            $real_message = $e->getMessage();
            $this->logger->notice('material-asset-out-storage-scm-recall-failed:' . $real_message);
        } catch (\Exception $e) {
            $code         = ErrCode::$SYSTEM_ERROR;
            $message      = static::$t->_('retry_later');
            $real_message = $e->getMessage();
            $this->logger->warning('material-asset-out-storage-scm-recall-failed:' . $real_message);
        }
        if (!empty($message)) {
            $db->rollback();
        }
        return [
            'code'    => $code,
            'message' => $message
        ];
    }

    /**
     * 获取关联申请单列表
     * @param array $params 搜索参数组
     * @param array $user 当前登陆者信息组
     * @return array
     */
    public function getApplyList($params, $user)
    {
        //已通过
        $params['status'] = Enums::CONTRACT_STATUS_APPROVAL;
        //未全部关联完成
        $params['is_all_related'] = MaterialAssetApplyEnums::IS_ALL_RELATED_NO;
        //是否批量关联锁定 = 0否
        $params['is_batch_related_lock'] = MaterialAssetApplyEnums::MATERIAL_ASSET_IS_BATCH_UN_LOCK;
        //排序
        $params['sort'] = 'asc';
        return AssetApplyService::getInstance()->getList($params, MaterialAssetApplyEnums::LIST_TYPE_OUT_STORAGE_LIST, $user);
    }

    /**
     * 获取关联申请单详情
     * @param array $params 搜索参数组
     * @return array
     */
    public function getApplyInfo($params)
    {
        return AssetApplyService::getInstance()->getInfo($params);
    }

    /**
     * 获取关联申请单-资产明细列表
     * @param array $params 搜索参数组
     * @return array
     */
    public function getApplyProductList($params)
    {
        $list = [
            'code' => ErrCode::$SUCCESS,
            'message' => '',
            'data' => []
        ];
        try {
            //资产申请单-基本信息
            $apply = MaterialAssetApplyModel::findFirst([
                'conditions' => 'id=:id: and is_deleted = :is_deleted:',
                'bind' => ['id' => $params['id'], 'is_deleted' => MaterialClassifyEnums::IS_DELETED_NO]
            ]);
            if (!empty($apply)) {
                //资产申请单-资产明细
                $product_list = $apply->getProducts()->toArray();
                if (!empty($product_list)) {
                    $apply_barcode = array_values(array_filter(array_unique(array_column($product_list, "barcode"))));
                    $update_to_scm = $params['update_to_scm'];
                    if ($update_to_scm == MaterialClassifyEnums::MATERIAL_CATEGORY_NO) {
                        //更新至SCM,直接搜索符合条件的barcode列表返回
                        $params['barcode'] = $apply_barcode;
                        $params['pageSize'] = count($params['barcode']);
                        $list = $this->searchBarcode(static::$language, $params);
                    } else {
                        //拿到申请的barcode到sau表中查找barcode物料类型=“资产”&&更新至SCM=“否”&&未删除列表
                        $search_barcode = MaterialSauModel::find([
                            'columns' => 'barcode',
                            'conditions' => 'barcode in ({barcode:array}) and category_type=:category_type: and update_to_scm = :update_to_scm: and is_deleted=:is_deleted:',
                            'bind' => [
                                'barcode' => $apply_barcode,
                                'category_type' => MaterialClassifyEnums::MATERIAL_CATEGORY_TYPE_ASSET,
                                'update_to_scm' => $update_to_scm,
                                'is_deleted' => MaterialClassifyEnums::IS_DELETED_NO
                            ]
                        ])->toArray();
                        if (!empty($search_barcode)) {
                            $barcode_arr = array_values(array_column($search_barcode, 'barcode'));
                            //barcode的精确查找
                            if (!empty($params['barcode_exact'])) {
                                if (in_array($params['barcode_exact'], $barcode_arr)) {
                                    $list = $this->searchAsset(static::$language, $params);
                                }
                            } else {
                                $params['barcode'] = $barcode_arr;
                                $list = $this->searchAsset(static::$language, $params);
                            }
                        }
                    }
                    if (!empty($list) && !empty($list['data']['items'])) {
                        $list['data']['pagination']['current_page'] = intval($list['data']['pagination']['current_page']);
                        $list['data']['pagination']['per_page'] = intval($list['data']['pagination']['per_page']);
                        $product_list = array_column($product_list,null,'barcode');
                        foreach ($list['data']['items'] as &$item) {
                            $item['use'] = $product_list[$item['barcode']]['use'];//资产领用申请单明细表-使用方向
                            $item['apply_product_id'] = $product_list[$item['barcode']]['id'];//资产领用申请单明细表-ID
                            $item['this_time_num'] = $product_list[$item['barcode']]['this_time_num'];//资产领用申请单明细表-申请数量
                            $item['last_time_num'] = $product_list[$item['barcode']]['last_time_num'];//资产领用申请单明细表-上一级审核人确定数量
                            $item['total_issued_num'] = $product_list[$item['barcode']]['total_issued_num'];//资产领用申请单明细表-累计发放数量
                        }
                    }
                }
            }
        } catch (\Exception $e) {
            // 系统错误，不可对外抛出
            $this->logger->error('material-asset-out-storage-getApplyProductList failed:' . $e->getMessage());
        }
        return $list;
    }

    /**
     * 关联申请单
     * @param array $params 请求参数组
     * @param array $user 当前登录者信息组
     * @return array
     */
    public function relatedApply($params, $user)
    {
        $code = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $data = [];
        $db = $this->getDI()->get('db_oa');
        $apply_no = $params['apply_no'];
        $save_lock = RedisKey::MATERIAL_ASSET_OUT_STORAGE_SAVE_LOCK. $apply_no;
        try {
            $db->begin();
            //对要关联的申请单进行加锁
            if($this->checkLock($save_lock)) {
                throw new ValidationException(static::$t->_('material_asset_related_apply_lock'), ErrCode::$VALIDATE_ERROR);
            }else{
                $this->setLock($save_lock,1,10);
            }
            //第一步判断申请单是否存在
            $asset_apply = MaterialAssetApplyModel::findFirst([
                'conditions'=>'id=:id: and apply_no=:apply_no: and is_deleted=:is_deleted: and status=:status:',
                'bind'=>['id'=>$params['apply_id'], 'apply_no'=>$apply_no, 'is_deleted'=>MaterialClassifyEnums::IS_DELETED_NO, 'status'=>Enums::CONTRACT_STATUS_APPROVAL]
            ]);
            if (empty($asset_apply)) {
                throw new ValidationException(static::$t->_('material_asset_apply_not_found'), ErrCode::$VALIDATE_ERROR);
            }
            //第二步判断申请单是否被全部关联完成
            if ($asset_apply->is_all_related != MaterialAssetApplyEnums::IS_ALL_RELATED_NO) {
                throw new ValidationException(static::$t->_('material_asset_apply_all_related'), ErrCode::$VALIDATE_ERROR);
            }
            //第三步判断提交过来的barcode是否是申请单上的barcode
            $asset_apply_product_list = $asset_apply->getProducts();
            $asset_apply_product_barcode = array_column($asset_apply_product_list->toArray(), 'barcode');
            $not_in_asset_apply_product = [];
            foreach ($params['products'] as $param_product) {
                if (!in_array($param_product['barcode'], $asset_apply_product_barcode)) {
                    $not_in_asset_apply_product[] = $param_product['barcode'];
                }
            }
            if (!empty($not_in_asset_apply_product)) {
                throw new ValidationException(static::$t->_('material_asset_apply_has_not', ['barcode'=>implode(";", $not_in_asset_apply_product)]), ErrCode::$VALIDATE_ERROR);
            }
            //操作时间
            $now_time = date('Y-m-d H:i:s', time());
            $params['now_time'] = $now_time;
            //第四步验证提交过来的参数合法性
            [$data, $asset_update_data, $update_log_data, $transfer_data] = $this->validationSubmit($params, $user, $asset_apply, $asset_apply_product_list);
            //保存出库单等信息
            $this->saveOutStorage($data, $asset_update_data, $update_log_data, $transfer_data, $user, $db);
            $db->commit();
        } catch (ValidationException $e) {
            $code = $e->getCode();
            $message = $e->getMessage();
        } catch (BusinessException $e) {
            // 业务错误（数据错误等），不可对外抛出
            $code = $e->getCode();
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        } catch (\Exception $e) {
            // 系统错误，不可对外抛出
            $code = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        }
        if (!empty($message)) {
            $db->rollback();
        }
        if (!empty($real_message)) {
            $this->logger->error('material-asset-out-storage-relatedApply failed:' . $real_message . json_encode($data));
        }
        //释放锁
        $this->unLock($save_lock);
        return [
            'code' => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data' => []
        ];
    }

    /**
     * 处理类
     * @Date: 8/31/22 11:03 AM
     * @param array $condition 条件参数
     * @return array
     */
    public function getInfoDetailList($condition)
    {
        $page_size = empty($condition['pageSize']) ? MaterialEnums::PAGE_SIZE : $condition['pageSize'];
        $page_num  = empty($condition['pageNum']) ? MaterialEnums::PAGE_NUM : $condition['pageNum'];
        $offset    = $page_size * ($page_num - 1);
        $code      = ErrCode::$SUCCESS;
        $message   = $real_message = '';
        $data      = [
            'items'      => [],
            'pagination' => [
                'current_page' => $page_num,
                'per_page'     => $page_size,
                'total_count'  => 0,
            ],
        ];
        if (!empty($condition['aor_no'])) {
            $material_asset_out_storage = MaterialAssetOutStorageModel::findFirst([
                'conditions' => 'no = :no:',
                'bind'       => ['no' => $condition['aor_no']],
            ]);
            if (!empty($material_asset_out_storage) && $material_asset_out_storage->status == MaterialAssetOutStorageEnums::STATUS_OUT) {
                try {
                    $builder = $this->modelsManager->createBuilder();
                    $builder->from(['main' => MaterialAssetsModel::class]);
                    $builder->leftjoin(MaterialAssetTransferLogModel::class, 'log.asset_id = main.id', 'log');
                    $builder->andWhere('log.aor_no =:aor_no: and bar_code=:bar_code: ', ['aor_no' => $condition['aor_no'], 'bar_code' => $condition['bar_code']]);
                    $builder->andWhere('log.transfer_type = :transfer_type: and log.is_deleted = :is_deleted:', ['transfer_type' => MaterialEnums::TRANSFER_TYPE_OUT_STORAGE, 'is_deleted' => Enums\GlobalEnums::IS_NO_DELETED]);
                    $count = (int)$builder->columns('COUNT(main.id ) as total')->getQuery()->getSingleResult()->total;
                    if ($count) {
                        $builder->columns("main.asset_code,main.sn_code");
                        $builder->limit($page_size, $offset);
                        $builder->orderby('main.id desc');
                        $items_obj = $builder->getQuery()->execute();
                        $items     = $items_obj ? $items_obj->toArray() : [];
                    }

                    $data['items']                     = $items ?? [];
                    $data['pagination']['total_count'] = $count;
                } catch (\Exception $e) {
                    $code         = ErrCode::$SYSTEM_ERROR;
                    $message      = static::$t->_('retry_later');
                    $real_message = $e->getMessage() . $e->getTraceAsString();
                    $this->logger->warning('get_info_detail_list:' . $real_message);
                }

            }
        }

        return [
            'code'    => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data'    => $data,
        ];
    }

    /**
     * 获取特定条件下的总数 主要用于导出数据
     *
     * @param array $user 当前登陆者信息组
     * @param array $condition 条件
     * @return int
     * @throws ValidationException
     */
    public function getExportListCount(array $user, array $condition)
    {
        $builder = $this->modelsManager->createBuilder();
        $builder->from(['main' => MaterialAssetOutStorageModel::class]);
        $builder->leftjoin(MaterialAssetOutStorageProductModel::class, 'main.id=product.aor_id ', 'product');
        $builder->leftjoin(MaterialAssetTransferLogModel::class, 'log.aor_no=main.no and log.barcode=product.barcode and product.asset_code IN ("", log.asset_code) and log.transfer_type = ' . MaterialEnums::TRANSFER_TYPE_OUT_STORAGE . ' and log.is_deleted = ' . Enums\GlobalEnums::IS_NO_DELETED, 'log');
        $builder->leftjoin(MaterialAssetsModel::class, 'ma.id=log.asset_id', 'ma');
        //组合搜索条件
        $builder = $this->getCondition($user, $builder, $condition);
        return (int)$builder->columns('COUNT(main.id ) as total')->getQuery()->getSingleResult()->total;
    }

    /**
     * 领用出库-导入新增
     * @param string $excel_file 文件
     * @param array $user 当前员工信息组
     * @return array
     */
    public function importAddTask($excel_file, $user)
    {
        $real_message = '';
        try {
            //是否上传了文件
            if (empty($excel_file)) {
                throw new ValidationException(static::$t->_('bank_flow_not_found_file'), ErrCode::$VALIDATE_ERROR);
            }

            $file = $excel_file[0];
            //仅支持.xlsx格式的文件
            $extension = $file->getExtension();
            if (!in_array($extension, ['xlsx'])) {
                throw new ValidationException(static::$t->_('file_format_error'), ErrCode::$VALIDATE_ERROR);
            }
            //解析文件内容
            $config = ['path' => ''];
            $excel = new \Vtiful\Kernel\Excel($config);

            //读取上传文件数据
            $config = ['path' => ''];
            $excel = new \Vtiful\Kernel\Excel($config);
            $excel_data = $excel->openFile($file->getTempName())
                ->openSheet()
                ->setType([
                    \Vtiful\Kernel\Excel::TYPE_STRING,
                ])
                ->getSheetData();
            //弹出excel标题一行信息
            array_shift($excel_data);
            array_shift($excel_data);
            if (empty($excel_data)) {
                throw new ValidationException(static::$t->_('data_empty_or_read_data_failed'), ErrCode::$VALIDATE_ERROR);
            }

            //验证条数
            $import_limit = MaterialAssetApplyEnums::MATERIAL_ASSET_OUT_STORAGE_IMPORT_ADD_MAX;
            if (count($excel_data) > $import_limit) {
                throw new ValidationException(static::$t->_('material_asset_out_storage_import_error', ['num' => $import_limit]), ErrCode::$VALIDATE_ERROR);
            }

            //是否关联资产申请单
            $clear_excel_data = [];
            $excel_asset_code_data = [];
            foreach ($excel_data as $key => $row) {
                $clear_excel_data[] = trim($row[0]);
                $asset_code = trim($row[14]);
                if (!empty($asset_code)) {
                    $excel_asset_code_data[] = $asset_code;
                }
            }
            $is_relate_apply = array_unique($clear_excel_data);
            //必填，下拉框，Y 和N。注意：一个文件里，是否关联资产申请单，不可以不同。即导入文件里，都是关联资产申请单，或者都是不关联资产申请单
            if (count($is_relate_apply) > 1) {
                throw new ValidationException(static::$t->_('material_asset_out_storage_is_relate_apply_error'), ErrCode::$VALIDATE_ERROR);
            } elseif (!in_array($is_relate_apply[0], ['Y', 'N'])) {
                throw new ValidationException(static::$t->_('material_asset_out_storage_is_relate_apply_val_error'), ErrCode::$VALIDATE_ERROR);
            }

            //填写的资产码，在整个excel中不可重复
            if (count(array_unique($excel_asset_code_data)) != count($excel_asset_code_data)) {
                throw new ValidationException(static::$t->_('material_asset_out_storage_asset_code_repeat'), ErrCode::$VALIDATE_ERROR);
            }

            // 导入中心
            return ImportCenterService::getInstance()->addImportTask($file, $user, ImportCenterEnums::TYPE_MATERIAL_ASSET_OUT_STORAGE_ADD);
        } catch (ValidationException $e) {
            // 校验错误，可对外抛出
            $code = $e->getCode();
            $message = $e->getMessage();
        } catch (\Exception $e) {
            // 系统错误，不可对外抛出
            $code = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        }
        if (!empty($real_message)) {
            $this->logger->error('importAddTask-资产领用出库-导入新增-任务新增失败-' . $real_message);
        }
        return [
            'code' => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data' => $code == ErrCode::$SUCCESS ? true : false
        ];
    }

    /**
     * 导入新增-查询最后一次导入成功的结果
     * @return array
     */
    public function getImportResult()
    {
        return ImportCenterService::getInstance()->getBarcodeImportResult(ImportCenterEnums::TYPE_MATERIAL_ASSET_OUT_STORAGE_ADD);
    }

    /**
     * 资产领用出库-导入新增-数据处理
     * @param array $excel_data 批量数据
     * @param int $user_id 批量操作人id
     * @param int $update_result_column 备注写入列
     * @return array
     * @throws BusinessException
     */
    public function batchAddAssetOutStorage($excel_data, $user_id, $update_result_column)
    {
        $code = ErrCode::$SUCCESS;
        $message = $real_message = $real_trace = '';
        $correct_and_error = $correct_data = $error_data = $apply_no_lock = [];
        $db = $this->getDI()->get('db_oa');
        try {
            $result_data = $excel_data;
            //将excel数据数字索引转换为关联索引
            $data = $this->excelToDataEdit($excel_data);
            $is_relate_apply = $data[0]['is_relate_apply'];
            if ($is_relate_apply == 'Y') {
                //是否关联资产申请单 = 是
                $correct_and_error = $this->validationRelatedApply($data, $user_id, $db);
            } elseif ($is_relate_apply == 'N') {
                //是否关联资产申请单 = 否
                $correct_and_error = $this->validationAdd($data, $user_id);
            }
            $this->logger->info('资产领用出库-导入新增 校验之后的解析结果为' . json_encode($correct_and_error, JSON_UNESCAPED_UNICODE));

            //将校验结果反写到excel文件中
            $error_data = $correct_and_error['error_data'];
            $correct_data = $correct_and_error['correct_data'];
            if ($correct_data || $error_data) {
                foreach ($correct_data as $index => $cv) {
                    $result_data[$index][$update_result_column] = $cv['error_message'];
                }
                foreach ($error_data as $err_index => $ev) {
                    $result_data[$err_index][$update_result_column] = $ev['error_message'];
                }
            }
        } catch (ValidationException $e) {
            $code    = $e->getCode();
            $message = $e->getMessage();
        } catch (BusinessException $e) {
            $code         = $e->getCode();
            $message      = static::$t->_('retry_later');
            $real_message = $e->getMessage();
            $real_trace   = $e->getTraceAsString();
        } catch (\Exception $e) {
            $code         = ErrCode::$SYSTEM_ERROR;
            $message      = static::$t->_('retry_later');
            $real_message = $e->getMessage();
            $real_trace   = $e->getTraceAsString();
        }

        if (!empty($real_message)) {
            $this->logger->error('资产领用出库-导入新增 失败 ' . $real_message . '; trace=' . $real_trace);
            $correct_data = [];
            $error_data = $excel_data;
        }

        $apply_no_lock = $correct_and_error['apply_no_lock'] ?? [];
        if (!empty($apply_no_lock) && !empty($message)) {
            //批量解锁【由于异常行为退出脚本的也需要对加锁的单据解锁】
            $this->logger->info('资产领用出库-导入新增-关联申请单逻辑 解锁数据为' . json_encode($apply_no_lock, JSON_UNESCAPED_UNICODE));
            AssetApplyService::getInstance()->unLockBatchApply($apply_no_lock, $db, 'is_batch_related_lock');
        }

        return [
            'code' => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data' => [
                'excel_data' => $result_data,
                'all_num' => count($excel_data),
                'success_num' => count($correct_data),
                'failed_sum' => count($error_data),
            ],
        ];
    }


    /**
     * 处理数据
     * @param $excel_data
     * @return array
     */
    public function excelToDataEdit($excel_data)
    {
        //excel转字段
        $data_key = [
            'is_relate_apply',//是否关联资产申请单
            'apply_no',//申请单号
            'staff_id',//领用员工工号
            'company_id',//费用所属公司
            'receive_store_id',//使用网点ID
            'address',//详细地址
            'mark',//备注
            'delivery_way',//配送方式
            'update_to_scm',//是否通过scm出库
            'mach_code',//SCM货主
            'stock_id',//SCM仓库
            'use',//使用方向
            'barcode',//barcode
            'this_time_num',//本次发放数量
            'asset_code',//资产码
        ];
        $data = [];
        foreach ($excel_data as $k => $v) {
            foreach ($data_key as $index => $key) {
                if (in_array($key, ['use', 'delivery_way'])) {
                    //例 1-个人使用 | 1-快递
                    $end = stripos($v[$index], '-');
                    if ($end) {
                        $v[$index] = substr($v[$index], 0, $end);
                    } else {
                        $v[$index] = '';
                    }
                }

                //资产编码
                if ($key == 'asset_code' && !empty($v[$index])) {
                    $asset_code_arr = array_filter(explode(',', trim(trim(str_replace('，', ',', $v[$index]), ','))));
                    foreach ($asset_code_arr as &$item) {
                        $item = trim($item);
                    }
                    $v[$index] = implode(',', $asset_code_arr);
                }
                $data[$k][$key] = trim($v[$index]);
            }
        }
        return $data;
    }

    /**
     * 获取验证规则所需要的信息组
     * @param array $data excel数据
     * @param integer $user_id 员工ID
     * @return array
     */
    public function getValidationBaseData($data, $user_id)
    {
        $user = (new UserService())->getUserById($user_id);
        $create_user = $this->format_user($user);
        //获取excel中所有领用人信息
        $staff_info_arr = [];
        $staff_id_arr = array_values(array_unique(array_filter(array_column($data, 'staff_id'))));
        if (!empty($staff_id_arr)) {
            $staff_list = StaffService::getInstance()->searchStaff(['staff_id' => $staff_id_arr, 'limit' => count($staff_id_arr)]);
            if (!empty($staff_list['data'])) {
                $staff_info_arr = array_column($staff_list['data'], null, 'staff_id');
            }
        }

        //BU级别公司列表
        $bu_company_list = (new PurchaseService())->getCooCostCompany();
        $cost_company_kv = array_column($bu_company_list, 'cost_company_name', 'cost_company_id');

        //使用网点ID
        $receive_store_ids = array_values(array_unique(array_filter(array_column($data, 'receive_store_id'))));
        $headquarters_ids = [];//总部地址
        $sys_store_ids = [];//网点地址
        foreach ($receive_store_ids as $id) {
            if (is_numeric($id)) {
                $headquarters_ids[] = $id;
            } else {
                $sys_store_ids[] = $id;
            }
        }
        $store_repository = new StoreRepository();
        $headquarters_list = $store_repository->getHeaderListByIds($headquarters_ids);
        $sys_store_list = $store_repository->getStoreListByIds($sys_store_ids);
        $store_list = array_column(array_merge($headquarters_list, $sys_store_list), null, 'id');

        //获取省信息
        $excel_province_code_arr = array_column($data, 'province_code');
        $store_province_code_arr = array_column($store_list, 'province_code');
        $province_code_arr = array_merge($excel_province_code_arr, $store_province_code_arr);
        $province_code = array_values(array_filter(array_unique($province_code_arr)));
        $province_list = SysProvinceRepository::getInstance()->getListByCodes($province_code);

        //获取市信息
        $excel_city_code_arr = array_column($data, 'city_code');
        $store_city_code_arr = array_column($store_list, 'city_code');
        $city_code_arr = array_merge($excel_city_code_arr, $store_city_code_arr);
        $city_code = array_values(array_filter(array_unique($city_code_arr)));
        $city_list = SysCityRepository::getInstance()->getListByCodes($city_code);

        //获取区信息
        $excel_district_code_arr = array_column($data, 'district_code');
        $store_district_code_arr = array_column($store_list, 'district_code');
        $district_code_arr = array_merge($excel_district_code_arr, $store_district_code_arr);
        $district_code = array_values(array_filter(array_unique($district_code_arr)));
        $district_list = SysDistrictRepository::getInstance()->getListByCodes($district_code);

        //货主信息
        $scm_service = new ScmService();
        $mach_code_list = $scm_service->scmCargoOwner();
        $mach_code_kv = array_column($mach_code_list, 'name', 'mach_code');

        //获取仓库信息
        $stock_list = $scm_service->getStockList();

        //以下做代码迁移 && lnt目前只有马来有这个逻辑，其他国家没必要获取
        $lnt_setting_barcode = $lnt_company_ids = $lnt_job_ids = [];
        if (get_country_code() == GlobalEnums::MY_COUNTRY_CODE) {
            //lnt 公司 能申请的barcode 配置
            $lnt_setting_barcode = EnumsService::getInstance()->getSettingEnvValueIds(MaterialAssetApplyEnums::MATERIAL_ASSET_LNT_BARCODE);
            //lnt 公司 id
            $lnt_company_ids = Enumsservice::getInstance()->getSettingEnvValueIds('lnt_company_ids');
            //LNT配置的职位只可申请lnt可以申请的barcode
            $lnt_job_ids = EnumsService::getInstance()->getSettingEnvValueIds(MaterialAssetApplyEnums::MATERIAL_ASSET_LNT_JOB_IDS);
        }

        return [
            'create_user'         => $create_user,
            'staff_info_list'     => $staff_info_arr,
            'company_list'        => $cost_company_kv,
            'store_list'          => $store_list,
            'province_list'       => $province_list,
            'city_list'           => $city_list,
            'district_list'       => $district_list,
            'mach_code_list'      => $mach_code_kv,
            'stock_list'          => $stock_list,
            'lnt_setting_barcode' => $lnt_setting_barcode,
            'lnt_company_ids'     => $lnt_company_ids,
            'lnt_job_ids'          => $lnt_job_ids,
        ];
    }

    /**
     * 资产领用出库-导入新增-关联申请单逻辑
     * @param array $data excel数据
     * @param int $user_id 批量操作人id
     * @param object $db 事务
     * @return array
     * @throws BusinessException
     */
    public function validationRelatedApply($data, $user_id, $db)
    {
        $error_data = $correct_data = $out_correct_data = [];
        //第一步将excel数据按照关联的申请单进行分组
        $apply_group_data = [];
        foreach ($data as $line => $item) {
            $apply_group_data[$item['apply_no']]['product'][$line] = $item;
            $apply_group_data[$item['apply_no']][$item['staff_id']]['update_to_scm'][] = $item['update_to_scm'];
            $apply_group_data[$item['apply_no']][$item['staff_id']]['mach_code'][] = $item['mach_code'];
            $apply_group_data[$item['apply_no']][$item['staff_id']]['stock_id'][] = $item['stock_id'];
            $apply_group_data[$item['apply_no']][$item['staff_id']]['barcode'][] = $item['barcode'];
            $apply_group_data[$item['apply_no']][$item['staff_id']]['company_id'][] = $item['company_id'];
            $apply_group_data[$item['apply_no']][$item['staff_id']]['receive_store_id'][] = $item['receive_store_id'];
            $apply_group_data[$item['apply_no']][$item['staff_id']]['address'][] = $item['address'];
            $apply_group_data[$item['apply_no']][$item['staff_id']]['mark'][] = $item['mark'];
            $apply_group_data[$item['apply_no']][$item['staff_id']]['delivery_way'][] = $item['delivery_way'];
            $apply_group_data[$item['apply_no']][$item['staff_id']]['product'][$line] = $item;
        }
        //第二步获取关联的申请单列表[审核通过 && 未被完全关联完毕]
        $apply_list_data = [];
        $apply_no_arr = array_values(array_filter(array_keys($apply_group_data)));
        if (!empty($apply_no_arr)) {
            $apply_list_data = MaterialAssetApplyModel::find([
                'conditions' => 'apply_no in ({array_nos:array}) and is_deleted = :is_deleted: and status = :status: and is_all_related = :is_all_related:',
                'bind' => ['array_nos' => $apply_no_arr, 'is_deleted' => GlobalEnums::IS_NO_DELETED, 'status' => Enums::WF_STATE_APPROVED, 'is_all_related' => MaterialAssetApplyEnums::IS_ALL_RELATED_NO]
            ])->toArray();
            $apply_list_data = array_column($apply_list_data, null, 'apply_no');
            $this->logger->info('资产领用出库-导入新增-关联申请单逻辑 查询到的申请单数据:' . json_encode($apply_list_data, JSON_UNESCAPED_UNICODE));
        }

        //第三步取到的申请单记录锁定关联
        [$apply_no_lock, $apply_no_un_lock] = AssetApplyService::getInstance()->lockBatchApply($apply_list_data, $db, 'is_batch_related_lock');
        $this->logger->info('资产领用出库-导入新增-关联申请单逻辑 批量加锁数据:' . json_encode($apply_no_lock, JSON_UNESCAPED_UNICODE) . ', 本次被其他增加过锁的数据是:' . json_encode($apply_no_un_lock, JSON_UNESCAPED_UNICODE));

        //第四步获取验证所需基础数据
        $base_data = $this->getValidationBaseData($data, $user_id);
        $create_user = $base_data['create_user'];
        $staff_info_list = $base_data['staff_info_list'];
        $company_list = $base_data['company_list'];
        $store_list = $base_data['store_list'];
        $province_list = $base_data['province_list'];
        $city_list = $base_data['city_list'];
        $district_list = $base_data['district_list'];
        $mach_code_list = $base_data['mach_code_list'];
        $stock_list = $base_data['stock_list'];

        //lnt 公司 能申请的barcode 配置
        $settingBarcode = $base_data['lnt_setting_barcode'];
        //lnt 公司 id
        $lntIds = $base_data['lnt_company_ids'];
        //LNT配置的职位只可申请lnt可以申请的barcode
        $lntJobIds = $base_data['lnt_job_ids'];

        //第五步验证每个申请单下关联领用人已经出库明细合法性
        foreach ($apply_group_data as $apply_no => $item) {
            //5.1 - 验证关联的申请单号合法性，不合法，那所有关联这个申请单的excel行就不去验证其它逻辑了
            $main_error_message = '';
            $one_apply_info = $apply_list_data[$apply_no] ?? [];
            if (empty($apply_no) || mb_strlen($apply_no) > 20 || empty($one_apply_info)) {
                $main_error_message = static::$t->_('material_asset_out_storage_apply_no_error');
            } elseif ($one_apply_info['is_batch_related_lock'] == MaterialAssetApplyEnums::MATERIAL_ASSET_IS_BATCH_LOCK) {
                $main_error_message = static::$t->_('material_asset_out_storage_apply_no_locked');
            }
            if ($main_error_message) {
                foreach ($item['product'] as $line => $v) {
                    $v['error_message'] = $main_error_message;
                    $error_data[$line] = $v;
                }
                continue;
            }
            //5.2 - 验证每一个领用人信息合法性，不合法，那所有该领用人信息下的excel行就不去验证其它逻辑了
            unset($item['product']);//以下逻辑是按照领用人去走，故而得移除
            foreach ($item as $staff_id => $value) {
                $main_staff_error_message = $relate_apply_product_params = [];
                $update_to_scm = $mach_code = $stock_id = '';
                //必填，校验输入的员工必须是在职的非待离职的正式员工或者实习员工。
                $one_staff_info = $staff_info_list[$staff_id] ?? [];
                $state = $one_staff_info['state'] ?? 0;
                $is_lnt = false;
                if (empty($staff_id) || empty($one_staff_info) || $state != StaffInfoEnums::STAFF_STATE_IN) {
                    $main_staff_error_message[] = static::$t->_('material_asset_out_storage_staff_id_error');
                } else {
                    //是否lnt 公司
                    $is_lnt = !empty($one_staff_info['contract_company_id']) && in_array($one_staff_info['contract_company_id'], $lntIds) && in_array($one_apply_info['job_title'], $lntJobIds);
                    //费用所属公司
                    $company_id_arr = array_unique($value['company_id']);
                    $company_id = $company_id_arr[0] ?? 0;
                    $one_company_info = $company_list[$company_id] ?? [];
                    if (count($company_id_arr) > 1) {
                        $main_staff_error_message[] = static::$t->_('material_asset_out_storage_company_id_error');
                    } elseif (empty($one_company_info)) {
                        $main_staff_error_message[] = static::$t->_('material_asset_out_storage_company_id_empty');
                    }
                    //使用网点ID
                    $receive_store_id_arr = array_unique($value['receive_store_id']);
                    $receive_store_id = $receive_store_id_arr[0] ?? '';
                    $one_store_info = $store_list[$receive_store_id] ?? [];
                    if (count($receive_store_id_arr) > 1) {
                        $main_staff_error_message[] = static::$t->_('material_asset_out_storage_receive_store_id_error');
                    } elseif (empty($receive_store_id)) {
                        $main_staff_error_message[] = static::$t->_('material_asset_out_storage_receive_store_id_empty');
                    } else if (empty($one_store_info)) {
                        $main_staff_error_message[] = static::$t->_('material_asset_out_storage_receive_store_id_invalid');
                    } else {
                        //总部地址或者网点
                        $province_code = $one_store_info['province_code'] ?? '';
                        $city_code = $one_store_info['city_code'] ?? '';
                        $district_code = $one_store_info['district_code'] ?? '';
                        $one_province_info = $province_list[$province_code] ?? [];
                        if (empty($province_code) || empty($one_province_info)) {
                            $main_staff_error_message[] = static::$t->_('material_asset_out_storage_province_code_error');
                        }
                        $one_city_info = $city_list[$city_code] ?? [];
                        if (empty($city_code) || empty($one_city_info)) {
                            $main_staff_error_message[] = static::$t->_('material_asset_out_storage_city_code_error');
                        }
                        $one_district_info = $district_list[$district_code] ?? [];
                        if (empty($district_code) || empty($one_district_info)) {
                            $main_staff_error_message[] = static::$t->_('material_asset_out_storage_district_code_error');
                        }
                    }
                    //详细地址
                    /*$address_arr = array_unique($value['address']);
                    $address = $address_arr[0] ?? '';
                    if (empty($address) || mb_strlen($address) > 500 || count($address_arr) > 1) {
                        $main_staff_error_message[] = static::$t->_('material_asset_out_storage_address_error');
                    }*/
                    //备注
                    $mark_arr = array_unique($value['mark']);
                    $mark = $mark_arr[0] ?? '';
                    if (count($mark_arr) > 1) {
                        $main_staff_error_message[] = static::$t->_('material_asset_out_storage_mark_error');
                    }

                    //配送方式
                    $delivery_way_arr = array_unique($value['delivery_way']);
                    $delivery_way = $delivery_way_arr[0] ? $delivery_way_arr[0] : MaterialClassifyEnums::DELIVERY_WAY_EXPRESS;
                    if (count($delivery_way_arr) > 1) {
                        $main_staff_error_message[] = static::$t->_('material_asset_out_storage_delivery_way_error');
                    }

                    //是否通过scm出库
                    $update_to_scm_arr = array_unique($value['update_to_scm']);
                    $update_to_scm = $update_to_scm_arr[0] ?? '';
                    if (empty($update_to_scm) || !in_array($update_to_scm, ['Y', 'N']) || count($update_to_scm_arr) > 1) {
                        $main_staff_error_message[] = static::$t->_('material_asset_out_storage_update_to_scm_error');
                    } elseif ($update_to_scm == 'Y') {
                        //货主
                        $mach_code_arr = array_unique($value['mach_code']);
                        $mach_code = $mach_code_arr[0] ?? '';
                        $one_mach_code_info = $mach_code_list[$mach_code] ?? [];
                        if (empty($mach_code) || count($mach_code_arr) > 1) {
                            //如果是否通过SCM出库为Y，则该字段必填
                            $main_staff_error_message[] = static::$t->_('material_asset_out_storage_mach_code_error');
                        } elseif (empty($one_mach_code_info)) {
                            //去命中SCM的货主，如果不存在，则提示：货主无效，请重新输入。
                            $main_staff_error_message[] = static::$t->_('material_asset_out_storage_mach_code_invalid');
                        }

                        //仓库
                        $stock_id_arr = array_unique($value['stock_id']);
                        $stock_id = $stock_id_arr[0] ?? 0;
                        $one_stock_info = $stock_list[$mach_code][$stock_id] ?? [];
                        if (empty($stock_id) || count($stock_id_arr) > 1) {
                            //一个文件里的同一笔资产申请单行的SCM仓库必须是一致
                            $main_staff_error_message[] = static::$t->_('material_asset_out_storage_stock_id_error');
                        } elseif (empty($one_stock_info)) {
                            //命中所填写SCM货主底下的仓库，仓库不是属于上述输入SCM货主底下的仓库,则提示：仓库无效，请重新输入
                            $main_staff_error_message[] = static::$t->_('material_asset_out_storage_stock_id_invalid');
                        }
                    }

                    //如果是关联了资产申请单，一个资产申请单里barcode必须唯一，否则提示：一个资产申请单里barcode重复，请检查！
                    $all_barcode = array_filter(array_column($value['product'], 'barcode'));
                    $unique_barcode = array_unique($all_barcode);
                    if (count($all_barcode) != count($unique_barcode)) {
                        $main_staff_error_message[] = static::$t->_('material_asset_out_storage_barcode_repeat');
                    }
                }
                if ($main_staff_error_message ) {
                    foreach ($value['product'] as $line => $v) {
                        $v['error_message'] = implode(';', $main_staff_error_message);
                        $error_data[$line] = $v;
                    }
                    continue;
                }

                //5.3 - 每一个领用人信息合法后-验证出库明细行
                $has_error = false;
                $apply_barcode_product_list = $apply_asset_product_list = [];
                if ($update_to_scm === 'Y' && $mach_code && $stock_id) {
                    //同步至scm
                    $apply_scm_product = $this->getApplyProductList(['id' => $one_apply_info['id'], 'update_to_scm' => MaterialClassifyEnums::MATERIAL_CATEGORY_NO, 'mach_code' => $mach_code, 'stock_id' => $stock_id, 'status' => MaterialClassifyEnums::MATERIAL_START_USING]);
                    $apply_barcode_product_list = $apply_scm_product['data']['items'] ?? [];
                    $apply_barcode_product_list = array_column($apply_barcode_product_list, null, 'barcode');
                }
                foreach ($value['product'] as $line => &$v) {
                    $main_staff_product_message = [];
                    //barcode必填
                    if (empty($v['barcode']) || mb_strlen($v['barcode']) > 30) {
                        $main_staff_product_message[] = static::$t->_('material_asset_out_storage_barcode_error');
                    } elseif ($update_to_scm === 'Y') {
                        $one_barcode_info = $apply_barcode_product_list[$v['barcode']] ?? [];
                        if (empty($one_barcode_info)) {
                            //同步至scm,但不存在申请单里
                            $main_staff_product_message[] = static::$t->_('material_asset_out_storage_barcode_error');
                        } elseif ($v['this_time_num'] > !is_numeric($one_barcode_info['available_inventory']) ? 0 : $one_barcode_info['available_inventory']) {
                            $main_staff_product_message[] = static::$t->_('scm_barcode_available_inventory_not_enough');
                        } else {
                            $relate_apply_product_params['products'][] = [
                                'barcode' => $v['barcode'],
                                'name_zh' => $one_barcode_info['name_zh'] ?? '',
                                'name_en' => $one_barcode_info['name_en'] ?? '',
                                'name_local' => $one_barcode_info['name_local'] ?? '',
                                'category_id' => $one_barcode_info['category_id'] ?? '',
                                'category_name' => $one_barcode_info['category_name'] ?? '',
                                'category_code' => $one_barcode_info['category_code'] ?? '',
                                'finance_category_id' => $one_barcode_info['finance_category_id'] ?? '',
                                'finance_category_name' => $one_barcode_info['finance_category_name'] ?? '',
                                'finance_category_code' => $one_barcode_info['finance_category_code'] ?? '',
                                'unit_zh' => $one_barcode_info['unit_zh'] ?? '',
                                'unit_en' => $one_barcode_info['unit_en'] ?? '',
                                'model' => $one_barcode_info['model'] ?? '',
                                'use' => $v['use'],
                                'apply_product_id' => $one_barcode_info['apply_product_id'] ?? 0,
                                'stock_id' => $stock_id,
                                'stock_name' => $one_stock_info['name'] ?? '',
                                'this_time_num' => $v['this_time_num'],
                            ];
                        }
                    }

                    //使用方向
                    if (empty($v['use']) || !in_array($v['use'], [MaterialEnums::USE_PERSONAL, MaterialEnums::USE_PUBLIC])) {
                        $main_staff_product_message[] = static::$t->_('material_asset_out_storage_use_error');
                    }

                    //发放数量
                    if (empty($v['this_time_num']) || !preg_match(MaterialAssetOutStorageEnums::THIS_TIME_NUMBER_RULE, $v['this_time_num'])) {
                        $main_staff_product_message[] = static::$t->_('material_asset_out_storage_this_time_num_error');
                    }

                    //非同步至SCM
                    if ($update_to_scm === 'N') {
                        //资产编码必填
                        if (empty($v['asset_code'])) {
                            $main_staff_product_message[] = static::$t->_('material_asset_out_storage_asset_code_empty');
                        } else {
                            //输入了发放数量，则资产编码和数量要一致
                            $asset_code_data = array_unique(array_filter(explode(',', $v['asset_code'])));
                            if (preg_match(MaterialAssetOutStorageEnums::THIS_TIME_NUMBER_RULE, $v['this_time_num']) && $v['this_time_num'] > 1 && count($asset_code_data) != $v['this_time_num']) {
                                $main_staff_product_message[] = static::$t->_('material_asset_out_storage_asset_code_num_error');
                            } else {
                                foreach ($asset_code_data as $asset_code) {
                                    $apply_no_scm_product = $this->getApplyProductList(['id' => $one_apply_info['id'], 'update_to_scm' => MaterialClassifyEnums::MATERIAL_CATEGORY_OFF, 'asset_code' => $asset_code]);
                                    $apply_asset_product_list = $apply_no_scm_product['data']['items'] ?? [];
                                    $apply_asset_product_list = array_column($apply_asset_product_list, null, 'asset_code');
                                    $one_asset_info = $apply_asset_product_list[$asset_code] ?? [];
                                    if (empty($one_asset_info)) {
                                        $main_staff_product_message[] = static::$t->_('material_asset_out_storage_asset_code_error');
                                    } elseif ($one_asset_info['barcode'] != $v['barcode']) {
                                        $main_staff_product_message[] = static::$t->_('material_asset_out_storage_barcode_invalid');
                                    } else {
                                        $relate_apply_product_params['products'][] = [
                                            'barcode' => $v['barcode'],
                                            'name_zh' => $one_asset_info['name_zh'] ?? '',
                                            'name_en' => $one_asset_info['name_en'] ?? '',
                                            'name_local' => $one_asset_info['name_local'] ?? '',
                                            'category_id' => $one_asset_info['category_id'] ?? '',
                                            'category_name' => $one_asset_info['category_name'] ?? '',
                                            'category_code' => $one_asset_info['category_code'] ?? '',
                                            'finance_category_id' => $one_asset_info['finance_category_id'] ?? '',
                                            'finance_category_name' => $one_asset_info['finance_category_name'] ?? '',
                                            'finance_category_code' => $one_asset_info['finance_category_code'] ?? '',
                                            'asset_code' => $asset_code,
                                            'sn_code' => $one_asset_info['sn_code'] ?? '',
                                            'use' => $v['use'],
                                            'unit_zh' => $one_asset_info['unit_zh'] ?? '',
                                            'unit_en' => $one_asset_info['unit_en'] ?? '',
                                            'model' => $one_asset_info['model'] ?? '',
                                            'apply_product_id' => $one_asset_info['apply_product_id'] ?? 0
                                        ];
                                    }
                                }
                            }
                        }
                    }
                    //是lnt 员工 非 lnt 商品
                    if($is_lnt && !empty($settingBarcode) && !in_array($v['barcode'], $settingBarcode)){
                        $main_staff_product_message[] = static::$t->_('lnt_barcode_notice', ['code_str' => $v['barcode']]);
                    }

                    if ($main_staff_product_message) {
                        $has_error = true;
                        $v['error_message'] = implode(';', $main_staff_product_message);
                        $error_data[$line] = $v;
                    } else {
                        $correct_data[$line] = $v;
                    }
                }

                //每一个领用人信息合法后-验证出库明细行,明细行但凡存在不合法数据，则该用户无法出库
                if ($has_error === true) {
                    //若出库单有多行，其中有的校验成功，有的校验失败，校验成功的也无法出库需要要写失败原因
                    if (count($value['product']) > 1) {
                        foreach ($value['product'] as $line => $v) {
                            if (empty($v['error_message'])) {
                                $v['error_message'] = static::$t->_('material_asset_out_storage_fail');
                                $error_data[$line] = $v;
                                unset($correct_data[$line]);
                            }
                        }
                    }
                    continue;
                }

                //5.4 - 每一个领用人信息合法后-验证出库明细行,若明细行无误，组装出库数据 && 出库
                $out_storage_error = [];
                try {
                    //19888需求资产使用网点为总部地址 按照部门成本中心，网点仍按照网点中心
                    if (is_numeric($receive_store_id)) {
                        $type = 1;
                        $pc_id = $one_staff_info['node_department_id'];
                    } else {
                        $type = 2;
                        $pc_id = $receive_store_id;
                    }
                    $pc_code_data = StoreRentingAddService::getInstance()->getPcCode($pc_id, $type);
                    $out_storage_no = static::genSerialNo(MaterialAssetOutStorageEnums::MATERIAL_ASSET_OUT_STORAGE_NO_PREFIX, RedisKey::MATERIAL_ASSET_OUT_STORAGE_COUNTER);
                    $relate_apply_data = [
                        'no' => $out_storage_no,
                        'apply_date' => date('Y-m-d'),
                        'staff_id' => $staff_id,
                        'staff_name' => $one_staff_info['staff_name'],
                        'apply_id' => $one_apply_info['id'],
                        'apply_no' => $apply_no,
                        'company_id' => $company_id,
                        'company_name'=>$one_company_info,
                        'node_department_id' => $one_staff_info['node_department_id'],
                        'node_department_name' => $one_staff_info['node_department_name'],
                        'sys_store_id' => $one_staff_info['sys_store_id'],
                        'store_name' => $one_staff_info['store_name'],
                        'job_id' => $one_staff_info['job_id'],
                        'job_name' => $one_staff_info['job_name'],
                        'pc_code' => $pc_code_data['data']['pc_code'] ?? '',
                        'receive_store_id' => $receive_store_id,
                        'receive_name' => $one_store_info['name'] ?? '',
                        'province_code' => $province_code,
                        'city_code' => $city_code,
                        'district_code' => $district_code,
                        'postal_code' => $one_district_info['postal_code'],
                        'address' => $one_store_info['detail_address'] ?? '0',//产品要求没有地址的默认为0
                        'mark' => mb_substr($one_apply_info['reason'] . $mark, 0, 500),
                        'delivery_way' => $delivery_way,
                        'update_to_scm' => $update_to_scm == 'Y' ? MaterialClassifyEnums::MATERIAL_CATEGORY_NO : MaterialClassifyEnums::MATERIAL_CATEGORY_OFF,
                        'stock_id' => $stock_id ?? '',
                        'stock_name' => $one_stock_info['name'] ?? '',
                        'mach_code' => $mach_code ?? '',
                        'mach_name' => $one_mach_code_info ?? '',
                        'is_batch_related_lock' => MaterialAssetApplyEnums::MATERIAL_ASSET_IS_BATCH_LOCK
                    ];
                    $relate_apply_params = array_merge($relate_apply_data, $relate_apply_product_params);
                    $this->logger->info('资产领用出库-导入新增-关联申请单 员工：【' . $staff_id . '】 出库请求参数' . json_encode($relate_apply_params, JSON_UNESCAPED_UNICODE));

                    //验证出库参数合法性
                    $validate_param = array_merge(self::$validate_related_apply_param, self::$validate_material_add);
                    $validate_param = $this->getExtendValidation($relate_apply_params, $validate_param);
                    Validation::validate($relate_apply_params, $validate_param);
                    //出库
                    $result = $this->relatedApply($relate_apply_params, $create_user);
                    if ($result['code'] == ErrCode::$SUCCESS) {
                        foreach ($value['product'] as $line => $item) {
                            $item['error_message'] = static::$t->_('material_asset_out_storage_success') . ',' . $out_storage_no;
                            $correct_data[$line] = $item;
                        }
                    } else {
                        throw new \Exception($result['message'], $result['code']);
                    }
                } catch (ValidationException $e) {
                    $out_storage_error[] = $e->getMessage();
                } catch (\Exception $e) {
                    $out_storage_error[] = $e->getMessage();
                }
                //5.5 - 每一个领用人信息合法后-验证出库明细行,若明细行无误，出库失败
                if ($out_storage_error) {
                    foreach ($value['product'] as $line => $v) {
                        $v['error_message'] = implode(';', $out_storage_error);
                        $error_data[$line] = $v;
                        unset($correct_data[$line]);
                    }
                }
                sleep(1);
            }
        }

        //第六步解除申请单锁
        AssetApplyService::getInstance()->unLockBatchApply($apply_no_lock, $db, 'is_batch_related_lock');
        $this->logger->info('资产领用出库-导入新增-关联申请单逻辑 解锁数据为' . json_encode($apply_no_lock, JSON_UNESCAPED_UNICODE));

        return ['correct_data' => $correct_data, 'error_data' => $error_data, 'apply_no_lock' => $apply_no_lock];
    }

    /**
     * 资产领用出库-导入新增-直接出库
     * @param array $data excel数据
     * @param int $user_id 批量操作人id
     * @return array
     * @throws BusinessException
     */
    public function validationAdd($data, $user_id)
    {
        //第一步获取验证所需基础数据
        $base_data = $this->getValidationBaseData($data, $user_id);
        $create_user = $base_data['create_user'];
        $staff_info_list = $base_data['staff_info_list'];
        $company_list = $base_data['company_list'];
        $store_list = $base_data['store_list'];
        $province_list = $base_data['province_list'];
        $city_list = $base_data['city_list'];
        $district_list = $base_data['district_list'];
        $mach_code_list = $base_data['mach_code_list'];
        $stock_list = $base_data['stock_list'];

        //lnt 公司 能申请的barcode 配置
        $settingBarcode = $base_data['lnt_setting_barcode'];
        //lnt 公司 id
        $lntIds = $base_data['lnt_company_ids'];
        //LNT配置的职位只可申请lnt可以申请的barcode
        $lntJobIds = $base_data['lnt_job_ids'];

        //第二步遍历excel行 && 验证工号、网点合法性 && 按领用人+ 网点分组
        $error_data = $correct_data = $apply_group_data = [];
        foreach ($data as $line => $item) {
            $error_message = [];
            //输入的员工必须是在职的非待离职的正式员工或者实习员工
            $staff_id = $item['staff_id'];
            $one_staff_info = $staff_info_list[$staff_id] ?? [];
            $state = $one_staff_info['state'] ?? 0;
            if (empty($staff_id) || empty($one_staff_info) || $state != StaffInfoEnums::STAFF_STATE_IN) {
                $error_message[] = static::$t->_('material_asset_out_storage_staff_id_error');
            }

            //使用网点ID必填，网点填写网点编号，总部填写总部地址ID
            $receive_store_id = $item['receive_store_id'];
            $one_store_info = $store_list[$receive_store_id] ?? [];
            if (empty($receive_store_id)) {
                $error_message[] = static::$t->_('material_asset_out_storage_receive_store_id_empty');
            } elseif (empty($one_store_info)) {
                $error_message[] = static::$t->_('material_asset_out_storage_receive_store_id_invalid');
            } else {
                //总部地址或者网点
                $province_code = $one_store_info['province_code'] ?? '';
                $city_code = $one_store_info['city_code'] ?? '';
                $district_code = $one_store_info['district_code'] ?? '';
                $one_province_info = $province_list[$province_code] ?? [];
                if (empty($province_code) || empty($one_province_info)) {
                    $main_staff_error_message[] = static::$t->_('material_asset_out_storage_province_code_error');
                }
                $one_city_info = $city_list[$city_code] ?? [];
                if (empty($city_code) || empty($one_city_info)) {
                    $main_staff_error_message[] = static::$t->_('material_asset_out_storage_city_code_error');
                }
                $one_district_info = $district_list[$district_code] ?? [];
                if (empty($district_code) || empty($one_district_info)) {
                    $main_staff_error_message[] = static::$t->_('material_asset_out_storage_district_code_error');
                }
            }

            //若工号或网点不合法不需要分组，也不需要出库
            if (!empty($error_message)) {
                $item['error_message'] = implode(';', $error_message);
                $error_data[$line] = $item;
            } else {
                //按领用人+试用网点分组
                $staff_store_key = $staff_id . '_' . $receive_store_id;
                $apply_group_data[$staff_store_key]['receive_store_id'] = $receive_store_id;
                $apply_group_data[$staff_store_key]['staff_info'] = $one_staff_info;
                $apply_group_data[$staff_store_key]['store_info'] = $one_store_info;
                $apply_group_data[$staff_store_key]['product'][$line] = $item;
                $apply_group_data[$staff_store_key]['update_to_scm'][] = $item['update_to_scm'];
                $apply_group_data[$staff_store_key]['mach_code'][] = $item['mach_code'];
                $apply_group_data[$staff_store_key]['stock_id'][] = $item['stock_id'];
                $apply_group_data[$staff_store_key]['barcode'][] = $item['barcode'];
                $apply_group_data[$staff_store_key]['company_id'][] = $item['company_id'];
                $apply_group_data[$staff_store_key]['mark'][] = $item['mark'];
                $apply_group_data[$staff_store_key]['delivery_way'][] = $item['delivery_way'];
            }
        }

        //第三步遍历分组 && 验证每一个领用人信息合法性
        foreach ($apply_group_data as $value) {
            $main_staff_error_message = $relate_apply_product_params = [];
            $mach_code = $stock_id = '';
            //费用所属公司必须是BU级，同网点、同领用人公司必须一致
            $company_id_arr = array_unique($value['company_id']);
            $company_id = $company_id_arr[0] ?? 0;
            $one_company_info = $company_list[$company_id] ?? [];
            if (count($company_id_arr) > 1) {
                $main_staff_error_message[] = static::$t->_('material_asset_out_storage_company_id_diff');
            } elseif (empty($one_company_info)) {
                $main_staff_error_message[] = static::$t->_('material_asset_out_storage_company_id_empty');
            }

            //是否通过scm出库，同网点、同领用人必须一致
            $update_to_scm_arr = array_unique($value['update_to_scm']);
            $update_to_scm = $update_to_scm_arr[0] ?? '';
            if (empty($update_to_scm) || !in_array($update_to_scm, ['Y', 'N']) || count($update_to_scm_arr) > 1) {
                $main_staff_error_message[] = static::$t->_('material_asset_out_storage_update_to_scm_diff');
            } elseif ($update_to_scm == 'Y') {
                //货主，同网点、同领用人必须一致
                $mach_code_arr = array_unique($value['mach_code']);
                $mach_code = $mach_code_arr[0] ?? '';
                $one_mach_code_info = $mach_code_list[$mach_code] ?? [];
                if (empty($mach_code) || count($mach_code_arr) > 1) {
                    //如果是否通过SCM出库为Y，则该字段必填
                    $main_staff_error_message[] = static::$t->_('material_asset_out_storage_mach_code_diff');
                } elseif (empty($one_mach_code_info)) {
                    //去命中SCM的货主，如果不存在，则提示：货主无效，请重新输入。
                    $main_staff_error_message[] = static::$t->_('material_asset_out_storage_mach_code_invalid');
                }

                //仓库，同网点、同领用人必须一致
                $stock_id_arr = array_unique($value['stock_id']);
                $stock_id = $stock_id_arr[0] ?? 0;
                $one_stock_info = $stock_list[$mach_code][$stock_id] ?? [];
                if (empty($stock_id) || count($stock_id_arr) > 1) {
                    $main_staff_error_message[] = static::$t->_('material_asset_out_storage_stock_id_diff');
                } elseif (empty($one_stock_info)) {
                    //命中所填写SCM货主底下的仓库，仓库不是属于上述输入SCM货主底下的仓库,则提示：仓库无效，请重新输入
                    $main_staff_error_message[] = static::$t->_('material_asset_out_storage_stock_id_invalid');
                }
            }

            //barcode,同网点、同领用人不可重复
            $all_barcode = array_filter($value['barcode']);
            $unique_barcode = array_unique($all_barcode);
            if (count($all_barcode) != count($unique_barcode)) {
                $main_staff_error_message[] = static::$t->_('material_asset_out_storage_barcode_diff');
            }

            //备注，同网点、同领用人必须一致
            $mark_arr = array_unique($value['mark']);
            $mark = $mark_arr[0] ?? '';
            if (count($mark_arr) > 1) {
                $main_staff_error_message[] = static::$t->_('material_asset_out_storage_mark_diff');
            }

            //配送方式，同网点、同领用人，一个相同组的配送方式，必须一致。
            $delivery_way_arr = array_unique($value['delivery_way']);
            $delivery_way = $delivery_way_arr[0] ? $delivery_way_arr[0] : MaterialClassifyEnums::DELIVERY_WAY_EXPRESS;
            if (count($delivery_way_arr) > 1) {
                $main_staff_error_message[] = static::$t->_('material_asset_out_storage_delivery_way_diff');
            }

            if ($main_staff_error_message) {
                foreach ($value['product'] as $line => $v) {
                    $v['error_message'] = implode(';', $main_staff_error_message);
                    $error_data[$line] = $v;
                }
                continue;
            }

            //第四步每一个领用人信息合法后-验证出库明细行
            $has_error = false;
            $apply_barcode_product_list = $apply_asset_product_list = [];
            if ($update_to_scm === 'Y' && $mach_code && $stock_id) {
                //同步至SCM
                $list = $this->searchBarcode(static::$language, ['barcode' => $unique_barcode, 'mach_code' => $mach_code, 'stock_id' => $stock_id, 'status' => MaterialClassifyEnums::MATERIAL_START_USING, 'pageSize' => count($unique_barcode)]);
                if (!empty($list) && !empty($list['data']['items'])) {
                    $apply_barcode_product_list = array_column($list['data']['items'], null, 'barcode');
                }
            }

            //是否lnt 公司
            $is_lnt = !empty($value['staff_info']['contract_company_id']) && in_array($value['staff_info']['contract_company_id'], $lntIds) && in_array($value['staff_info']['job_id'], $lntJobIds);
            foreach ($value['product'] as $line => &$item) {
                $main_staff_product_message = [];
                //该字段不可填写，否则提示：选了不关联资产申请单，该字段不可输入值
                if ($item['apply_no']) {
                    $main_staff_product_message[] = static::$t->_('material_asset_out_storage_apply_no_has_value');
                }

                /*//详细地址必填1-500字符
                $address = $item['address'];
                if (empty($address) || mb_strlen($address) > 500) {
                    $main_staff_product_message[] = static::$t->_('material_asset_out_storage_address_empty');
                }*/

                //发放数量
                $this_time_num = $item['this_time_num'];
                if (empty($this_time_num) || !preg_match(MaterialAssetOutStorageEnums::THIS_TIME_NUMBER_RULE, $this_time_num)) {
                    $main_staff_product_message[] = static::$t->_('material_asset_out_storage_this_time_num_error');
                }

                //使用方向
                $use = $item['use'];
                if (empty($use) || !in_array($use, [MaterialEnums::USE_PERSONAL, MaterialEnums::USE_PUBLIC])) {
                    $main_staff_product_message[] = static::$t->_('material_asset_out_storage_use_error');
                }

                //barcode必填
                $barcode = $item['barcode'];
                if (empty($barcode) || mb_strlen($barcode) > 30) {
                    $main_staff_product_message[] = static::$t->_('material_asset_out_storage_barcode_error');
                } else if ($update_to_scm === 'Y') {
                    $one_barcode_info = $apply_barcode_product_list[$barcode] ?? [];
                    if (empty($one_barcode_info)) {
                        $main_staff_product_message[] = static::$t->_('excel_result_barcode_not_exist');
                    } else if (preg_match(MaterialAssetOutStorageEnums::THIS_TIME_NUMBER_RULE, $this_time_num) && ($this_time_num > (!is_numeric($one_barcode_info['available_inventory']) ? 0 : $one_barcode_info['available_inventory']))) {
                        $main_staff_product_message[] = static::$t->_('scm_barcode_available_inventory_not_enough');
                    } else {
                        $relate_apply_product_params['products'][] = [
                            'barcode' => $barcode,
                            'name_zh' => $one_barcode_info['name_zh'] ?? '',
                            'name_en' => $one_barcode_info['name_en'] ?? '',
                            'name_local' => $one_barcode_info['name_local'] ?? '',
                            'category_id' => $one_barcode_info['category_id'] ?? '',
                            'category_name' => $one_barcode_info['category_name'] ?? '',
                            'category_code' => $one_barcode_info['category_code'] ?? '',
                            'finance_category_id' => $one_barcode_info['finance_category_id'] ?? '',
                            'finance_category_name' => $one_barcode_info['finance_category_name'] ?? '',
                            'finance_category_code' => $one_barcode_info['finance_category_code'] ?? '',
                            'unit_zh' => $one_barcode_info['unit_zh'] ?? '',
                            'unit_en' => $one_barcode_info['unit_en'] ?? '',
                            'model' => $one_barcode_info['model'] ?? '',
                            'use' => $use,
                            'stock_id' => $stock_id,
                            'stock_name' => $one_stock_info['name'] ?? '',
                            'this_time_num' => $this_time_num,
                        ];
                    }
                }

                //非同步至SCM
                if ($update_to_scm == 'N') {
                    $asset_code_str = $item['asset_code'];
                    //资产编码必填
                    if (empty($asset_code_str)) {
                        $main_staff_product_message[] = static::$t->_('material_asset_out_storage_asset_code_empty');
                    } else {
                        $asset_code_data = array_unique(array_filter(explode(',', $asset_code_str)));
                        //输入了发放数量，则资产编码和数量要一致
                        if (preg_match(MaterialAssetOutStorageEnums::THIS_TIME_NUMBER_RULE, $this_time_num) && $this_time_num > 1 && count($asset_code_data) != $this_time_num) {
                            $main_staff_product_message[] = static::$t->_('material_asset_out_storage_asset_code_num_error');
                        } else {
                            foreach ($asset_code_data as $asset_code) {
                                $list = $this->searchAsset(static::$language, ['asset_code' => $asset_code]);
                                $apply_asset_product_list = array_column($list['data']['items'] ?? [], null, 'asset_code');
                                $one_asset_info = $apply_asset_product_list[$asset_code] ?? [];
                                if (empty($one_asset_info)) {
                                    $main_staff_product_message[] = static::$t->_('material_asset_out_storage_include_non_idle_asset');
                                } else if ($one_asset_info['barcode'] != $barcode) {
                                    $main_staff_product_message[] = static::$t->_('material_asset_out_storage_asset_code_invalid');
                                } else {
                                    $relate_apply_product_params['products'][] = [
                                        'barcode' => $barcode,
                                        'name_zh' => $one_asset_info['name_zh'] ?? '',
                                        'name_en' => $one_asset_info['name_en'] ?? '',
                                        'name_local' => $one_asset_info['name_local'] ?? '',
                                        'category_id' => $one_asset_info['category_id'] ?? '',
                                        'category_name' => $one_asset_info['category_name'] ?? '',
                                        'category_code' => $one_asset_info['category_code'] ?? '',
                                        'finance_category_id' => $one_asset_info['finance_category_id'] ?? '',
                                        'finance_category_name' => $one_asset_info['finance_category_name'] ?? '',
                                        'finance_category_code' => $one_asset_info['finance_category_code'] ?? '',
                                        'asset_code' => $asset_code,
                                        'sn_code' => $one_asset_info['sn_code'] ?? '',
                                        'use' => $use,
                                        'unit_zh' => $one_asset_info['unit_zh'] ?? '',
                                        'unit_en' => $one_asset_info['unit_en'] ?? '',
                                        'model' => $one_asset_info['model'] ?? ''
                                    ];
                                }
                            }
                        }
                    }
                }
                //是lnt 员工 非 lnt 商品
                if($is_lnt && !empty($settingBarcode) && !in_array($barcode, $settingBarcode)){
                    $main_staff_product_message[] = static::$t->_('lnt_barcode_notice', ['code_str' => $barcode]);
                }

                if ($main_staff_product_message) {
                    $has_error = true;
                    $item['error_message'] = implode(';', $main_staff_product_message);
                    $error_data[$line] = $item;
                } else {
                    $correct_data[$line] = $item;
                }
            }

            //每一个领用人信息合法后-验证出库明细行,明细行但凡存在不合法数据，则该用户无法出库
            if ($has_error === true) {
                //若出库单有多行，其中有的校验成功，有的校验失败，校验成功的也无法出库需要要写失败原因
                if (count($value['product']) > 1) {
                    foreach ($value['product'] as $line => $v) {
                        if (empty($v['error_message'])) {
                            $v['error_message'] = static::$t->_('material_asset_out_storage_fail');
                            $error_data[$line] = $v;
                            unset($correct_data[$line]);
                        }
                    }
                }
                continue;
            }

            //第五步，每一个领用人信息合法后-验证出库明细行,若明细行无误，组装出库数据 && 出库
            $out_storage_error = [];
            try {
                $one_staff_info = $value['staff_info'];
                $one_store_info = $value['store_info'];
                $receive_store_id = $value['receive_store_id'];
                if (is_numeric($receive_store_id)) {
                    $type = 1;
                    $pc_id = $one_staff_info['node_department_id'];
                } else {
                    $type = 2;
                    $pc_id = $receive_store_id;
                }
                $pc_code_data = StoreRentingAddService::getInstance()->getPcCode($pc_id, $type);
                $relate_apply_params = [
                    'staff_id' => $one_staff_info['staff_id'],
                    'staff_name' => $one_staff_info['staff_name'],
                    'company_id' => $company_id,
                    'company_name'=>$one_company_info,
                    'node_department_id' => $one_staff_info['node_department_id'],
                    'node_department_name' => $one_staff_info['node_department_name'],
                    'sys_store_id' => $one_staff_info['sys_store_id'],
                    'store_name' => $one_staff_info['store_name'],
                    'job_id' => $one_staff_info['job_id'],
                    'job_name' => $one_staff_info['job_name'],
                    'pc_code' => $pc_code_data['data']['pc_code'] ?? '',
                    'receive_store_id' => $one_store_info['id'] ?? 0,
                    'receive_name' => $one_store_info['name'] ?? 'Others',
                    'province_code' => $one_store_info['province_code'],
                    'city_code' => $one_store_info['city_code'],
                    'district_code' => $one_store_info['district_code'],
                    'postal_code' => $district_list[$one_store_info['district_code']]['postal_code'],
                    'address' => $one_store_info['detail_address'] ?? '0',//产品要求没有地址的默认为0
                    'mark' => mb_substr($mark, 0, 500),
                    'delivery_way' => $delivery_way,
                    'update_to_scm' => $update_to_scm == 'Y' ? MaterialClassifyEnums::MATERIAL_CATEGORY_NO : MaterialClassifyEnums::MATERIAL_CATEGORY_OFF,
                    'stock_id' => $stock_id ?? '',
                    'stock_name' => $one_stock_info['name'] ?? '',
                    'mach_code' => $mach_code ?? '',
                    'mach_name' => $one_mach_code_info ?? '',
                ];

                //若明细行无误，组装出库数据
                $out_storage_no = static::genSerialNo(MaterialAssetOutStorageEnums::MATERIAL_ASSET_OUT_STORAGE_NO_PREFIX, RedisKey::MATERIAL_ASSET_OUT_STORAGE_COUNTER);
                $relate_apply_params['no'] = $out_storage_no;
                $relate_apply_params['apply_date'] = date('Y-m-d');
                $relate_apply_params['create_id'] = $create_user['id'];
                $relate_apply_params['create_name'] = $create_user['name'];
                $relate_apply_params = array_merge($relate_apply_params, $relate_apply_product_params);
                $this->logger->info('资产领用出库-导入新增-直接出库 员工：【' . $one_staff_info['staff_id'] . '】 出库请求参数' . json_encode($relate_apply_params, JSON_UNESCAPED_UNICODE));

                //验证出库参数合法性
                $validate_param = $this->getExtendValidation($relate_apply_params, self::$validate_material_add);
                Validation::validate($relate_apply_params, $validate_param);
                //出库
                $result = $this->add($relate_apply_params, $create_user);
                if ($result['code'] == ErrCode::$SUCCESS) {
                    foreach ($value['product'] as $line => $item) {
                        $item['error_message'] = static::$t->_('material_asset_out_storage_success') . ',' . $out_storage_no;
                        $correct_data[$line] = $item;
                    }
                } else {
                    throw new \Exception($result['message'], $result['code']);
                }
            } catch (ValidationException $e) {
                $out_storage_error[] = $e->getMessage();
            } catch (\Exception $e) {
                $out_storage_error[] = $e->getMessage();
            }

            //每一个领用人信息合法后-验证出库明细行,若明细行无误，出库失败
            if ($out_storage_error) {
                foreach ($value['product'] as $line => $v) {
                    $v['error_message'] = implode(';', $out_storage_error);
                    $error_data[$line] = $v;
                    unset($correct_data[$line]);
                }
            }
            sleep(1);
        }

        return ['correct_data' => $correct_data, 'error_data' => $error_data];
    }

    /**
     * 获取资产审批详情-网点资产数据（出库）
     * @param array $condition 请求参数组
     * @return mixed
     */
    public function getAssetApplyAuditAssetList(array $condition)
    {
        $builder = $this->modelsManager->createBuilder();
        $columns = 'main.staff_id,main.staff_name, main.job_name, main.status, product.barcode, product.name_zh, product.name_en, product.name_local, product.this_time_num';
        $builder->columns($columns);
        $builder->from(['main' => MaterialAssetOutStorageModel::class]);
        $builder->leftjoin(MaterialAssetOutStorageProductModel::class, 'main.id = product.aor_id ', 'product');
        $builder->where('main.is_deleted = :is_deleted: and product.is_deleted = :is_deleted:', ['is_deleted' => GlobalEnums::IS_NO_DELETED]);
        $builder->andWhere('main.receive_store_id = :receive_store_id:', ['receive_store_id' => $condition['use_land_id']]);
        $builder->inWhere('main.status', [MaterialAssetOutStorageEnums::STATUS_WAIT_APPROVE, MaterialAssetOutStorageEnums::STATUS_APPROVED_WAIT_OUT]);
        return $builder->getQuery()->execute()->toArray();
    }

    /**
     * 获取某申请单关联的出库单列表
     * @param integer $apply 资产申请单
     * @return mixed
     */
    public function getOutStorageListByApply($apply)
    {
        if ($apply->status != Enums::WF_STATE_APPROVED) {
            return [];
        }

        $data = MaterialAssetOutStorageModel::find([
            'columns' => 'no, mach_code, stock_id, status, update_to_scm',
            'conditions'=>'apply_id = :apply_id: and status != :status: and is_deleted = :is_deleted:',
            'bind'=>[
                'apply_id' => $apply->id,
                'status' => MaterialAssetOutStorageEnums::STATUS_CANCEL,
                'is_deleted' => MaterialClassifyEnums::IS_DELETED_NO
            ],
            'order' => 'id DESC'
        ])->toArray();

        if (empty($data)) {
            return [];
        }

        if ($apply->delivery_way == MaterialClassifyEnums::DELIVERY_WAY_SELF) {
            //出库单配送方式为自提的时候
            $stock_data = (new ScmService())->getStockList();
            foreach ($data as &$item) {
                $stock_info = $stock_data[$item['mach_code']][$item['stock_id']] ?? [];
                $item['stock_name'] = $stock_info['name'] ?? '';//仓库名称
                $item['address'] = ($stock_info['province'] ?? '') . ($stock_info['city'] ?? '') . ($stock_info['district'] ?? '') . ($stock_info['address'] ?? '');//自提地址:省+市+区+详细地址
                $item['contact'] = $stock_info['contact'] ?? '';//联系人
                $item['telephone'] = $stock_info['telephone'] ?? '';//联系方式
                //当状态为待审核、已审核待出库的时候，显示未出库;已出库显示已出库
                $item['status_text'] = in_array($item['status'], [MaterialAssetOutStorageEnums::STATUS_WAIT_APPROVE, MaterialAssetOutStorageEnums::STATUS_APPROVED_WAIT_OUT]) ? static::$t->_('material_wms_out_storage_status_null') : static::$t->_(MaterialAssetOutStorageEnums::$asset_out_storage_status[$item['status']]);
            }
        } else {
            //出库单配送方式为快递的时候
            foreach ($data as &$item) {
                $item['status_text'] = static::$t->_(MaterialAssetOutStorageEnums::$asset_out_storage_status[$item['status']]);
            }
        }
        return $data;
    }
}
