<?php
namespace App\Modules\Material\Services;
use App\Library\Enums;
use App\Library\Enums\MaterialClassifyEnums;
use App\Library\Enums\MaterialEnums;
use App\Library\Enums\GlobalEnums;
use App\Library\Enums\MaterialAssetApplyEnums;
use App\Library\Enums\ImportCenterEnums;
use App\Library\Enums\ByWorkflowEnums;
use App\Library\Enums\MaterialAssetOutStorageEnums;
use App\Library\Enums\MaterialWmsEnums;
use App\Library\Enums\StaffInfoEnums;
use App\Library\ErrCode;
use App\Library\Validation\Validation;
use App\Models\backyard\HrStaffInfoModel;
use App\Models\oa\ByWorkflowAuditLogModel;
use App\Models\oa\ByWorkflowBusinessRelModel;
use App\Models\oa\MaterialSauPermissionModel;
use App\Modules\Common\Services\EnumsService;
use App\Modules\Common\Services\ImportCenterService;
use App\Modules\Material\Models\MaterialAssetApplyModel;
use App\Modules\Material\Models\MaterialAssetApplyProductModel;
use App\Modules\Material\Models\MaterialAssetOutStorageModel;
use App\Modules\Material\Models\MaterialAttachmentModel;
use App\Modules\Material\Models\MaterialSauModel;
use App\Modules\Organization\Services\DepartmentService;
use App\Modules\Purchase\Services\BaseService as PurchaseService;
use App\Library\Validation\ValidationException;
use App\Library\Exception\BusinessException;
use App\Modules\Third\Services\ByWorkflowService;
use App\Modules\User\Services\StaffService;
use App\Repository\DepartmentRepository;
use App\Repository\HrStaffRepository;
use App\Repository\oa\MaterialAssetsRepository;
use App\Util\RedisKey;

class AssetApplyService extends BaseService
{

    /**
     * 非必需的筛选条件
     * @var array
     */
    public static $not_must_params = [
        'staff_id',
        'status',
        'node_department_id',
        'company_id',
        'apply_date_start',
        'apply_date_end',
        'approve_at_start',
        'approve_at_end',
        'type',
        'pageSize',
        'pageNum'
    ];

    //列表搜索
    public static $validate_list_search = [
        'staff_id'=>'IntGt:0',//申请人工号,
        'apply_no'=>'StrLenGeLe:0,50',//领用单号-精确
        'like_apply_no'=>'StrLenGeLe:0,50',//领用单号-模糊
        'status[*]' => 'IntIn:' . Enums::CONTRACT_STATUS_PENDING . ',' . Enums::CONTRACT_STATUS_REJECTED . ',' . Enums::CONTRACT_STATUS_APPROVAL . ',' . Enums::CONTRACT_STATUS_CANCEL,
        'sys_store_id[*]'=>'StrLenGeLe:0,10',//申请人所属网点
        'node_department_id'=>'IntGt:0',//申请人所属部门ID
        'company_id' => 'IntGt:0',//申请人所属公司
        'use_land_id[*]' => 'StrLenGeLe:0,10', //标准型号的唯一标识
        'apply_date_start' => 'Date',//申请日期-起始
        'apply_date_end' => 'Date',//申请日期-截止
        'approve_at_start'=>'Date',//审批通过日期-起始
        'approve_at_end' =>'Date',//审批通过日期-截止
        'type' => 'IntIn:0,1',//审批列表（0待审核，1已处理）
        'barcode' =>'StrLenGeLe:0,30',//barcode
        'name' => 'StrLenGeLe:0,100',//资产名称
        'pageSize' => 'IntGt:0',//每页条数
        'pageNum' => 'IntGt:0', //页码
    ];

    //搜索标准型号
    public static $validate_search_barcode = [
        'job_id' => 'Required|IntGt:0',//职位id
        'pageSize' => 'IntGt:0',//每页条数
        'pageNum' => 'IntGt:0', //页码
        'name' => 'StrLenGeLe:0,30', //物料名称
        'barcode' => 'StrLenGeLe:0,30', //标准型号的唯一标识
        'model' => 'StrLenGeLe:0,100', //规格型号
    ];

    //新增或者重新提交
    public static $validate_add = [
        'apply_no'=>'Required|StrLenGeLe:15,50',
        'apply_date'=>'Required|Date',
        'staff_id'=>'Required|IntGt:0',//使用人工号,
        'staff_name'=>'Required|StrLenGeLe:1,50',//员工姓名
        'company_id'=>'Required|IntGt:0',//所属公司ID
        'company_name'=>'Required|StrLenGeLe:1,50',//所属公司名称
        'node_department_id'=>'Required|IntGt:0',//所属部门ID
        'node_department_name'=>'Required|StrLenGeLe:1,50',//所属部门名称
        'sys_store_id'=>'Required|StrLenGeLe:1,10',//所属网点
        'store_name'=>'Required|StrLenGeLe:1,50',//所属网点名称
        'use_land_id'=>'Required|StrLenGeLe:1,10',//使用地ID
        'use_land_name'=>'Required|StrLenGeLe:1,100',//使用地名称
        'job_id' => 'Required|IntGt:0',//职位id
        'reason' => 'Required|StrLenGeLe:1,200',//申请理由
        'attachments' => 'Required|Arr',//附件信息
        'consignee_id' => 'Required|IntGt:0',//收货人工号
        'consignee_name' => 'Required|StrLenGeLe:1,50',//收货人姓名
        'delivery_way' => 'Required|IntIn:' . MaterialClassifyEnums::DELIVERY_WAY_EXPRESS . ',' . MaterialClassifyEnums::DELIVERY_WAY_SELF,//配送方式

        'products' => 'Required|Arr|ArrLenGeLe:1,50',
        'products[*]' => 'Required|Obj',
        'products[*].barcode' => 'Required|StrLenGeLe:1,30',//barcode
        'products[*].name_zh' => 'Required|StrLenGeLe:1,100',//中文名称
        'products[*].name_en' => 'Required|StrLenGeLe:1,100',//英文名称
        'products[*].name_local' => 'StrLenGeLe:0,100',//当地语言名称
        'products[*].unit_zh' => 'StrLenGeLe:0,20',//基本单位-中文
        'products[*].unit_en' => 'StrLenGeLe:0,20',//基本单位-英文
        'products[*].model' => 'StrLenGeLe:0,100',//规格型号
        'products[*].use' => 'Required|IntIn:'.MaterialEnums::USE_VALIDATE,//使用方向
        'products[*].this_time_num' => 'Required|IntGeLe:1,1000',//申请数量
        'products[*].available_inventory'=>'Required|IntGe:0',//可用库存
    ];

    //详情、重新提交、撤回、驳回、通过校验验证
    public static $validate_update = [
        'id' => 'Required|IntGt:0',
    ];

    // by审批流水号
    public static $validate_workflow_no = [
        'workflow_no' => 'Required|StrLenGeLe:1,50',
    ];

    //撤回
    public static $validate_cancel = [
        'reason'=>'Required|StrLenGeLe:1,100'
    ];

    //驳回
    public static $validate_reject = [
        'reason'=>'Required|StrLenGeLe:1,1000'
    ];

    //审批-产品明细
    public static $validate_products = [
        'products' => 'Required|Arr|ArrLenGeLe:1,50',
        'products[*]' => 'Required|Obj',
        'products[*].id'=>'Required|IntGt:0',//资产明细表ID
        'products[*].available_inventory'=>'Required|Int',//可用库存
        'products[*].last_time_num' => 'Required|IntGeLe:0,9999',//上一级审批人确认数量
    ];

    //审批-网点资产数据
    public static $validate_audit_asset_list = [
        'use_land_id'=>'Required|StrLenGeLe:1,10',//使用地ID
    ];

    //查询收货人
    public static $validate_consignee = [
        'use_land_id'  => 'Required|StrLenGeLe:1,10|>>>:use_land_id error',
        'headquarters' => 'Required|StrLenGeLe:1,10|>>>:headquarters error',
        'sys_store_id' => 'Required|StrLenGeLe:1,10|>>>:sys_store_id error',
        'name'         => 'StrLenGeLe:0,200|>>>:name error',
    ];

    private static $instance;
    /**
     * 单例
     * @return AssetApplyService
     */
    public static function getInstance()
    {
        if (!isset(self::$instance) || !(self::$instance instanceof self)) {
            self::$instance = new self;
        }
        return self::$instance;
    }

    /**
     * 资产台账默认配置项
     * @return array
     */
    public function getOptionsDefault()
    {
        $code = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $data = [];
        try {
            //申请状态、使用方向、配送方式
            $data = $this->getEnums();
            //费用所属公司
            $data['cost_company'] = (new PurchaseService())->getCooCostCompany();
        } catch (\Exception $e) {
            $code = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        }
        if (!empty($real_message)) {
            $this->logger->warning('获取资产台账枚举异常信息: ' . $real_message);
        }
        return [
            'code' => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data' => $data,
        ];
    }

    /**
     * 获取使用状态、使用方向、配送方式
     * @return array
     */
    public function getEnums()
    {
        $data = [];
        $enums_arr = [
            'status' => Enums::$contract_status,
            'use'=>MaterialEnums::$use,
            'delivery_way_list' => MaterialClassifyEnums::$delivery_way_arr
        ];
        foreach ($enums_arr as $key => $value) {
            foreach ($value as $k => $v) {
                $data[$key][] = [
                    'value'=>$k,
                    'label'=>static::$t->_($v)
                ];
            }
        }
        return $data;
    }

    /**
     * 获取资产申请单列表
     * @param array $condition 查询条件组
     * @param integer $type 1:申请单列表，2审批列表，3数据查询列表
     * @param array $user 当前登陆者信息组
     * @param bool $export 是否导出true是，false否
     * @param int $count 总记录数
     * @return array
     */
    public function getList($condition, $type, $user=[], $export = false, $count = 0)
    {
        $page_size = empty($condition['pageSize']) ? MaterialEnums::PAGE_SIZE : $condition['pageSize'];
        $page_num  = empty($condition['pageNum']) ? MaterialEnums::PAGE_NUM : $condition['pageNum'];
        $offset    = $page_size * ($page_num - 1);
        $code    = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $data = [
            'items'      => [],
            'pagination' => [
                'current_page' => $page_num,
                'per_page'     => $page_size,
                'total_count'  => 0,
            ],
        ];

        try {
            if ($export === false) {
                //列表查询总数，导出无需查询，在验证总导出数限制时已查询到，传递过来了
                $count = $this->getListCount($condition, $type, $user, $export);
            }
            if ($count > 0) {
                $builder = $this->modelsManager->createBuilder();
                if ($export === false) {
                    //非导出
                    $columns = 'main.id, main.apply_no, status, staff_id, staff_name,company_name, store_name, node_department_name, apply_date, use_land_name, reason, operation_remark,approve_at';
                } else {
                    //导出
                    $columns = 'main.apply_no, status, apply_date, approve_at, staff_id, staff_name, company_name, node_department_name, store_name, use_land_name, reason, operation_remark, ';
                    $columns.= 'barcode,this_time_num,last_time_num,name_zh,name_en,name_local,unit_zh,unit_en,model,use';
                }
                $builder->columns($columns);
                $builder->from(['main'=>MaterialAssetApplyModel::class]);
                if ($export === true || !empty($condition['barcode']) || !empty($condition['name'])) {
                    //导出或者涉及到资产明细表搜索项，则关联资产明细表
                    $builder->leftjoin(MaterialAssetApplyProductModel::class, 'main.id=product.apply_id', 'product');
                }
                //组合搜索条件
                $builder = $this->getCondition($builder, $condition, $type, $user);
                $builder->limit($page_size, $offset);
                $builder->orderby('main.id '.($condition['sort'] ?? 'DESC'));
                $items_obj = $builder->getQuery()->execute();
                $items = $items_obj ? $items_obj->toArray() : [];
                $items = $this->handleListItems($items, $export);
            }
            $data['items']                     = $items ?? [];
            $data['pagination']['total_count'] = $count;
        } catch (ValidationException $e) {
            $code = $e->getCode();
            $message = $e->getMessage();
        }  catch (\Exception $e) {
            $code         = ErrCode::$SYSTEM_ERROR;
            $message      = static::$t->_('retry_later');
            $real_message = $e->getMessage() . $e->getTraceAsString();
        }
        if (!empty($real_message)) {
            $this->logger->warning('get-material-apply-list-failed:' . $real_message);
        }
        return [
            'code'    => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data'    => $data,
        ];
    }

    /**
     * 获取特定条件下的总数
     * @param array $condition 筛选条件组
     * @param integer $type 1:申请单列表，2审批列表，3数据查询列表
     * @param array $user 当前登陆者信息组
     * @param bool $export 是否导出，true导出、false非导出
     * @return int
     * @throws ValidationException
     */
    public function getListCount($condition, $type, $user=[], $export=false)
    {
        $builder = $this->modelsManager->createBuilder();
        $builder->columns("count('main.id') as count");
        $builder->from(['main'=>MaterialAssetApplyModel::class]);
        if ($export === true || !empty($condition['barcode']) || !empty($condition['name'])) {
            //导出或者涉及到资产明细表搜索项，则关联资产明细表
            $builder->leftjoin(MaterialAssetApplyProductModel::class, 'main.id=product.apply_id', 'product');
        }
        //组合搜索条件
        $builder = $this->getCondition($builder, $condition, $type, $user);
        $count_info = $builder->getQuery()->getSingleResult();
        return intval($count_info->count) ?? 0;
    }

    /**
     * 组装查询条件
     * @param object $builder 查询器
     * @param array $condition 查询条件
     * @param integer $type 1:申请单列表，2审批列表，3数据查询列表
     * @param array $user 当前登陆者信息组
     * @return mixed
     * @throws ValidationException
     */
    public function getCondition($builder, $condition, $type, $user=[])
    {
        //申请时间起始与结束校验
        $apply_date_start = !empty($condition['apply_date_start']) ? strtotime($condition['apply_date_start']) : '';
        $apply_date_end = !empty($condition['apply_date_end']) ? strtotime($condition['apply_date_end']) : '';
        if ($apply_date_start > $apply_date_end) {
            throw new ValidationException(self::$t->_('material_asset_list_date_error'), ErrCode::$VALIDATE_ERROR);
        }
        //审批时间起始与结束校验
        $approve_at_start = !empty($condition['approve_at_start']) ? strtotime($condition['approve_at_start']) : '';
        $approve_at_end = !empty($condition['approve_at_end']) ? strtotime($condition['approve_at_end']) : '';
        if ($approve_at_start > $approve_at_end) {
            throw new ValidationException(self::$t->_('material_asset_list_date_error'), ErrCode::$VALIDATE_ERROR);
        }
        $apply_no = !empty($condition['apply_no']) ? $condition['apply_no'] : ''; //申请单号-精确搜索
        $staff_id = !empty($condition['staff_id']) ? trim($condition['staff_id']) : 0;//申请人工号
        $status = !empty($condition['status']) ? $condition['status'] : [];//状态组
        $sys_store_id = !empty($condition['sys_store_id']) ? $condition['sys_store_id'] : [];//所属网点
        $node_department_id = !empty($condition['node_department_id']) ? $condition['node_department_id'] : 0;//所属部门
        $company_id = !empty($condition['company_id']) ? $condition['company_id'] : 0;//所属公司
        $use_land_id =  !empty($condition['use_land_id']) ? $condition['use_land_id'] : [];//使用地ID组
        $audit_type = $condition['type'] ?? 0;//审批列表类型，0待审核，1已处理
        $workflow_no = $condition['workflow_no'] ?? [];//按照by的审批流水号搜索
        $like_apply_no = !empty($condition['like_apply_no']) ? $condition['like_apply_no'] : ''; //申请单号-模糊搜索
        $barcode = !empty($condition['barcode']) ? $condition['barcode'] : '';//barcode搜索
        $name = !empty($condition['name']) ? $condition['name'] : '';//资产名称搜索
        $is_all_related = !empty($condition['is_all_related']) ? $condition['is_all_related'] : 0;//是否全部关联完成，1否，2是
        $is_batch_related_lock = isset($condition['is_batch_related_lock']) ? $condition['is_batch_related_lock'] : '';//是否批量关联锁定 0否，1是

        $builder->where('main.is_deleted = :is_deleted:', ['is_deleted'=>MaterialClassifyEnums::IS_DELETED_NO]);
        if ($type == Enums\MaterialAssetApplyEnums::LIST_TYPE_APPLY) {
            //申请列表
            $builder->andWhere('main.staff_id =:staff_id:', ['staff_id'=>$user['id']]);
        } else if ($type == Enums\MaterialAssetApplyEnums::LIST_TYPE_APPLY_AUDIT) {
            if ($audit_type == 0) {
                //待审核
                $builder->andWhere('main.status =:status:', ['status'=>Enums::CONTRACT_STATUS_PENDING]);
            } else {
                //已处理（驳回、通过）
                $builder->andWhere('audit.approval_id = :approval_id:', ['approval_id' => $user['id']]);
                $builder->andWhere('audit.biz_type = :biz_type:', ['biz_type' => Enums::WF_MATERIAL_ASSET]);
                $builder->inWhere('audit.status', [Enums::CONTRACT_STATUS_REJECTED, Enums::CONTRACT_STATUS_APPROVAL]);
            }
        } else if ($type == MaterialAssetApplyEnums::LIST_TYPE_APPLY_DATA) {
            //资产数据管控组-可看数据权限范围
            $data_permission = MaterialSettingService::getInstance()->getStaffDataPermissionGroup($user['id'], MaterialSettingService::DATA_PERMISSION_ASSET_APPLY);

            //资产数据管控组-可看数据权限范围
            if (!empty($data_permission['node_department_id'])) {
                $builder->inWhere('main.node_department_id', $data_permission['node_department_id']);
            }
        }

        if (!empty($apply_no)) {
            //申请单号
            $builder->andWhere('main.apply_no = :apply_no:', ['apply_no' => $apply_no]);
        }
        if (!empty($staff_id)) {
            //申请人工号
            $builder->andWhere('main.staff_id =:staff_id:', ['staff_id'=>$staff_id]);
        }
        if (!empty($status)) {
            //申请状态
            if (is_array($status)) {
                $builder->inWhere('main.status ', $status);
            } else {
                $builder->andWhere('main.status =:status:', ['status'=>$status]);
            }
        }
        if (!empty($sys_store_id)) {
            //按照所属网点搜索
            if (is_array($sys_store_id)) {
                $builder->inWhere('main.sys_store_id ', $sys_store_id);
            } else {
                $builder->andWhere('main.sys_store_id =:sys_store_id:', ['sys_store_id' => $sys_store_id]);
            }
        }
        if (!empty($node_department_id)) {
            //按照部门查找,需要找该部门以及下属子部门的所有信息
            $department_service = new DepartmentService();
            $department_ids = $department_service->getChildrenListByDepartmentIdV2($node_department_id, true);
            array_push($department_ids, $node_department_id);
            $builder->inWhere('main.node_department_id', $department_ids);
        }
        if (!empty($company_id)) {
            //所属公司
            $builder->andWhere('main.company_id = :company_id:', ['company_id'=>$company_id]);
        }
        if (!empty($use_land_id)) {
            $builder->inWhere('main.use_land_id ', $use_land_id);
        }
        if (!empty($apply_date_start)) {
            //申请日期-起始
            $builder->andWhere('main.apply_date >= :apply_date_start:', ['apply_date_start'=>date('Y-m-d 00:00:00', $apply_date_start)]);
        }
        if (!empty($apply_date_end)) {
            //申请日期-截止
            $builder->andWhere('main.apply_date <= :apply_date_end:', ['apply_date_end'=>date('Y-m-d 23:59:59', $apply_date_end)]);
        }
        if (!empty($approve_at_start)) {
            //审批通过日期-起始
            $builder->andWhere('main.approve_at >= :approve_at_start:', ['approve_at_start'=>date('Y-m-d 00:00:00', $approve_at_start)]);
        }
        if (!empty($approve_at_end)) {
            //审批通过日期-截止
            $builder->andWhere('main.approve_at <= :approve_at_end:', ['approve_at_end'=>date('Y-m-d 23:59:59', $approve_at_end)]);
        }
        if (!empty($workflow_no)) {
            //按照by审批流水号搜索
            $builder->inWhere('main.workflow_no', $workflow_no);
        }
        if (!empty($like_apply_no)) {
            //申请单号模糊搜索
            $builder->andwhere('main.apply_no like :like_apply_no: ', ['like_apply_no'=>'%'.$like_apply_no.'%']);
        }
        if (!empty($is_all_related)) {
            //是否全部关联完成，1否，2是
            $builder->andwhere('main.is_all_related = :is_all_related:', ['is_all_related' => $is_all_related]);
        }
        if ($is_batch_related_lock !== '') {
            //是否批量关联锁定 0否，1是
            $builder->andwhere('main.is_batch_related_lock = :is_batch_related_lock:', ['is_batch_related_lock' => $is_batch_related_lock]);
        }

        if (!empty($barcode)) {
            //barcode搜索
            $builder->andwhere('product.barcode like :barcode: ', ['barcode'=>'%'.$barcode.'%']);
        }
        if (!empty($name)) {
            //资产名称搜索
            $builder->andWhere('product.name_'.(MaterialClassifyEnums::$language_fields[static::$language] ?? 'local').' like :name:', ['name' => '%' . $name . '%']);
        }

        return $builder;
    }

    /**
     * 格式化申请单列表
     * @param array $items 申请单列表
     * @param bool $export 导出
     * @return array
     */
    private function handleListItems($items, $export = false)
    {
        if (empty($items)) {
            return [];
        }
        if ($export === true) {
            //导出
            $row_value = [];
            //组装导出需要的数据
            $staff_ids = array_values(array_unique(array_column($items, 'staff_id')));
            $staff_list = (new HrStaffRepository())->getStaffListByStaffIds($staff_ids);
            $hire_type_enum = EnumsService::getInstance()->getHireTypeEnum();
            foreach ($items as $item) {
                $one_staff_info = $staff_list[$item['staff_id']] ?? [];
                $row_value[] = [
                    'apply_no' => $item['apply_no'],
                    'status' => static::$t[Enums::$contract_status[$item['status']]],
                    'apply_date' => $item['apply_date'],
                    'approve_at' => $item['approve_at'],
                    'staff_id' => $item['staff_id'],
                    'staff_name' => $item['staff_name'],
                    $one_staff_info ? ($hire_type_enum[$one_staff_info['hire_type']] ?? '') : '',//雇佣类型
                    'company_name'=>$item['company_name'],
                    'node_department_name' => $item['node_department_name'],
                    'store_name' => $item['store_name'],
                    'use_land_name' => $item['use_land_name'],
                    'reason' => $item['reason'],
                    'operation_remark' => $item['operation_remark'],
                    'barcode'=>$item['barcode'],
                    'this_time_num' => $item['this_time_num'],
                    'last_time_num' => $item['status'] == Enums::WF_STATE_APPROVED ? $item['last_time_num'] : '',
                    'name_zh' => $item['name_zh'],
                    'name_en' => $item['name_en'],
                    'name_local' => $item['name_local'],
                    'model' => $item['model'],
                    'unit_zh' => $item['unit_zh'],
                    'unit_en' => $item['unit_en'],
                    'use' => static::$t[MaterialEnums::$use[$item['use']]]
                ];
            }
            $items = $row_value;
        } else {
            //非导出
            foreach ($items as &$item) {
                $item['approve_at'] = $item['approve_at'] ? date('Y-m-d', strtotime($item['approve_at'])) : '';
                $item['status_text'] = static::$t[Enums::$contract_status[$item['status']]];
            }
        }
        return $items;
    }

    /**
     * 获取资产申请审批列表
     * @param array $condition 查询条件组
     * @param array $user 当前登陆者信息组
     * @return array
     */
    public function getAuditList($condition, $user)
    {
        $page_size = empty($condition['pageSize']) ? MaterialEnums::PAGE_SIZE : $condition['pageSize'];
        $page_num  = empty($condition['pageNum']) ? MaterialEnums::PAGE_NUM : $condition['pageNum'];
        $offset    = $page_size * ($page_num - 1);
        $code    = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $data = [
            'items'      => [],
            'pagination' => [
                'current_page' => $page_num,
                'per_page'     => $page_size,
                'total_count'  => 0,
            ],
        ];

        try {
            //审批列表类型，0待审核，1已处理
            $audit_type = $condition['type'] ?? 0;

            //待审核列表
            if ($audit_type == 0) {
                //第一步在oa库查特定条件下的workflow_no号列
                $builder = $this->modelsManager->createBuilder();
                $builder->columns('main.workflow_no');
                $builder->from(['main'=>MaterialAssetApplyModel::class]);
                $builder = $this->getCondition($builder, $condition, Enums\MaterialAssetApplyEnums::LIST_TYPE_APPLY_AUDIT, $user);
                $search_list = $builder->getQuery()->execute()->toArray();
                if (!empty($search_list)) {
                    // 第二步调取by接口获取当前登陆人要审批的审批流水号组
                    $workflow_no = array_column($search_list, 'workflow_no');
                    $by_list_params = [
                        'serial_no'=>$workflow_no,
                        'biz_type'=>[ByWorkflowEnums::BY_BIZ_TYPE_ASSET],
                        'approval_id' => $user['id'],
                        'state'=>[ByWorkflowEnums::BY_OPERATE_WAIT_AUDIT],
                        'page_num'=>$page_num,
                        'page_size' => $page_size
                    ];
                    $result = (new ByWorkflowService())->getList($by_list_params);
                    $data['pagination']['total_count'] = !empty($result['total_count']) ? $result['total_count'] : 0;
                    if (!empty($result['list'])) {
                        //第三步查询到该审批人明细存在要审批的数据，关联上业务单据然后展示
                        $condition['workflow_no'] = array_values(array_column($result['list'], 'serial_no'));
                        if ($condition['workflow_no']) {
                            $builder = $this->modelsManager->createBuilder();
                            $columns = "main.id, apply_no, status, staff_id, staff_name,company_name, store_name, node_department_name, apply_date, use_land_name, reason, operation_remark, approve_at";
                            $builder->columns($columns);
                            $builder->from(['main'=>MaterialAssetApplyModel::class]);
                            $builder = $this->getCondition($builder, $condition, Enums\MaterialAssetApplyEnums::LIST_TYPE_APPLY_AUDIT, $user);
                            $builder->orderby('main.id asc');
                            $items = $builder->getQuery()->execute()->toArray();
                            $items = $this->handleListItems($items);
                        }
                    }
                }
            } else if ($audit_type == 1) {
                //已处理列表
                $builder = $this->modelsManager->createBuilder();
                $builder->columns('count(distinct audit.biz_value) as total');
                $builder->from(['audit'=>ByWorkflowAuditLogModel::class]);
                $builder->leftjoin(MaterialAssetApplyModel::class, 'main.id = audit.biz_value', 'main');
                $builder = $this->getCondition($builder, $condition, Enums\MaterialAssetApplyEnums::LIST_TYPE_APPLY_AUDIT, $user);
                $total_info = $builder->getQuery()->getSingleResult();
                $count = intval($total_info->total);
                if ($count > 0) {
                    $columns = "main.id, apply_no, main.status, main.staff_id, staff_name,company_name, store_name, node_department_name, apply_date, use_land_name, reason, operation_remark,approve_at";
                    $builder->columns($columns);
                    $builder->limit($page_size, $offset);
                    //由于有驳回、撤回可重新提交，审批人反复对一个申请单审批的可能所以列表需要过滤下重复审批的单据只展示一个即可
                    $builder->groupBy('main.id');
                    $builder->orderby('main.id desc');
                    $items = $builder->getQuery()->execute()->toArray();
                    $items = $this->handleListItems($items);
                }
                $data['pagination']['total_count'] = $count;
            }
            $data['items'] = $items ?? [];
        } catch (ValidationException $e) {
            $code = $e->getCode();
            $message = $e->getMessage();
        }  catch (\Exception $e) {
            $code         = ErrCode::$SYSTEM_ERROR;
            $message      = static::$t->_('retry_later');
            $real_message = $e->getMessage() . $e->getTraceAsString();
        }
        if (!empty($real_message)) {
            $this->logger->warning('get-material-audit-list-failed:' . $real_message);
        }
        return [
            'code'    => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data'    => $data,
        ];
    }

    /**
     * 资产申请-导出svc文件
     * @param array $condition 查询参数
     * @param integer $type 1:申请单列表，2审批列表，3数据查询列表
     * @param array $user 当前登陆者信息组
     * @return array
     */
    public function exportSvc($condition, $type, $user)
    {
        try {
            $count = $this->getListCount($condition, $type, $user, true);
            if ($count > Enums\MaterialAssetApplyEnums::MATERIAL_ASSET_APPLY_EXPORT_LIMIT) {
                throw new ValidationException(static::$t->_('material_asset_data_limit_error', ['max'=>Enums\MaterialAssetApplyEnums::MATERIAL_ASSET_APPLY_EXPORT_LIMIT]), ErrCode::$VALIDATE_ERROR);
            }
            $file_name = "material_asset_apply_" . date("YmdHis");
            //设置好告诉浏览器要下载excel文件的headers
            header('Content-Encoding:UTF-8');
            header('Content-Type: application/vnd.ms-excel;charset=UTF-8');
            header('Content-Disposition: attachment; filename="'. $file_name .'.csv"');
            //跨域设置
            header('Access-Control-Allow-Origin:*' );
            // 响应类型
            header('Access-Control-Allow-Methods:POST,GET');
            // 带 cookie 的跨域访问
            header('Access-Control-Allow-Credentials: true');
            // 响应头设置
            header('Access-Control-Allow-Headers:x-requested-with,Content-Type,Authorization,Content-Disposition');
            //打开php标准输出流
            $fp = fopen('php://output', 'a');
            //添加BOM头，以UTF8编码导出CSV文件，如果文件头未添加BOM头，打开会出现乱码。
            fwrite($fp, chr(0xEF).chr(0xBB).chr(0xBF));
            $header = [
                static::$t->_('material_asset_apply.no'),//资产申请单号
                static::$t->_('material_asset_apply.status'),//单据状态
                static::$t->_('storage_apply_date'),//申请时间
                static::$t->_('re_field_approve_at'),//审批通过时间
                static::$t->_('re_field_apply_id'),//申请人工号
                static::$t->_('re_field_apply_name'),//申请人姓名
                static::$t->_('material_asset.hire_type'),//雇佣类型
                static::$t->_('payment_store_renting_apply_company'),//申请人所属公司
                static::$t->_('re_field_apply_department_name'),//申请人所属部门
                static::$t->_('re_field_apply_store_name'),//申请人所属网点
                static::$t->_('material_asset_apply.use_land_name'),//资产使用地点
                static::$t->_('global.equipment_reason'),//申请理由
                static::$t->_('global.reject_reason'),//驳回原因
                static::$t->_('material_asset.barcode'),//barcode
                static::$t->_('material_asset_apply.this_time_num'),//申请数量
                static::$t->_('material_asset_apply.audit_pass_num'),//审批通过数量
                static::$t->_('material_asset_apply.name_zh'),//资产中文名称
                static::$t->_('material_asset_apply.name_en'),//资产英文名称
                static::$t->_('material_asset_apply.name_local'),//资产当地语言名称
                static::$t->_('material_asset.model'),//规格型号
                static::$t->_('material_asset_apply.unit_zh'),//基本单位-中文
                static::$t->_('material_asset_apply.unit_en'),//基本单位-英文
                static::$t->_('material_asset.use'),//使用方向
            ];
            fputcsv($fp,$header);
            if ($count > 0) {
                $page_size = 2000;
                $step = ceil($count/$page_size);
                for ($i=1; $i<=$step; $i++) {
                    $condition['pageNum'] = $i;
                    $condition['pageSize'] = $page_size;
                    $list = $this->getList($condition, $type, $user,true, $count);
                    $row_values = $list['data']['items'];
                    foreach ($row_values as $item) {
                        fputcsv($fp, $item);
                    }
                    if (ob_get_level() > 0) {
                        ob_flush();
                    }
                    flush();//必须同时使用 ob_flush() 和flush() 函数来刷新输出缓冲。
                }
            }
            fclose($fp);
            $this->unLock( md5(Enums\MaterialAssetApplyEnums::MATERIAL_ASSET_APPLY_EXPORT_LOCK . '_' . $user['id']));
            exit();
        } catch (ValidationException $e) {
            // 校验错误，可对外抛出
            $code = $e->getCode();
            $message = $e->getMessage();
        }  catch (\Exception $e) {
            $code    = ErrCode::$SYSTEM_ERROR;
            $message = $e->getMessage() . $e->getTraceAsString();
            $this->logger->warning('download-material-asset-apply-failed:' . $message);
        }
        return [
            'code'    => $code ,
            'message' => $message,
            'data'    => '',
        ];
    }

    /**
     * 领用申请-添加默认配置项
     * @param array $user 当前登陆者信息组
     * @return array
     */
    public function getAddDefault($user)
    {
        $code = ErrCode::$SUCCESS;
        $message = 'success';
        $data = [];
        try {
            $now_date = date("Ymd");
            $data['apply_no'] = static::genSerialNo(Enums\MaterialAssetApplyEnums::MATERIAL_ASSET_APPLY_NO_PREFIX, RedisKey::MATERIAL_ASSET_APPLY_COUNTER, 5);
            $data['apply_date'] = date("Y-m-d", strtotime($now_date));
            $data['staff_id'] = $user['id'];
            $data['staff_name'] = $user['name'];
            $staff_list = StaffService::getInstance()->searchStaff(['staff_id' => $user['id'], 'limit' => 1]);
            $user_other_info = !empty($staff_list['data']) ? $staff_list['data'][0] : [];
            $data['node_department_id'] = $user_other_info['node_department_id'] ?? 0;
            $data['node_department_name'] = $user_other_info['node_department_name'] ?? '';
            $data['sys_store_id'] = $user_other_info['sys_store_id'] ?? '';
            $data['store_name'] = $user_other_info['store_name'] ?? '';
            $data['job_id'] = $user_other_info['job_id'] ?? 0;
        } catch (\Exception $e) {
            $code = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $this->logger->warning('material-asset-apply_add_default-failed:'.$e->getMessage());
        }
        return [
            'code' => $code,
            'message' => $message,
            'data' => $data
        ];
    }

    /**
     * 搜索标准型号
     * @param array $params 搜索条件
     * @param integer $user_id 用户id
     * @return array
     */
    public function searchBarcode($params, $user_id)
    {
        $default_scm_cargo_stock_ids = $this->getScmMachCodeAndStockIds();
        $params = array_merge($params, $default_scm_cargo_stock_ids);

        //个人代理的资产清单-需要是设置里的barcode数据，未设置或设置空，均空
        $user_info = (new HrStaffRepository())->getStaffById($user_id);
        if ($user_info['hire_type'] == StaffInfoEnums::HIRE_TYPE_PERSONAL_AGENCY && $this->getPersonalAgentStatus()) {
            $base_barcode = EnumsService::getInstance()->getSettingEnvValueIds(MaterialAssetApplyEnums::MATERIAL_ASSET_PERSONAL_AGENT_BARCODE_KEY);
            $params['has_base_barcode'] = true;
            $params['base_barcode'] = $base_barcode;
        }
        //申请只要启用的
        $params['status'] = MaterialClassifyEnums::MATERIAL_START_USING;
        //16850需求增加包含可申请条件的筛选
        $params['use_scene'] = [MaterialClassifyEnums::MATERIAL_USE_SCENE_APPLY, MaterialClassifyEnums::MATERIAL_USE_SCENE_APPLY_AND_BUY];
        return AssetOutStorageService::getInstance()->searchBarcode(static::$language, $params);
    }

    /**
     * 获取scm货主和仓库ID组
     * @return mixed
     */
    public function getScmMachCodeAndStockIds()
    {
        $scm_service = new ScmService();
        $default_scm_cargo_owner = $scm_service->getDefaultScmCargoOwner();
        $params['mach_code'] = !empty($default_scm_cargo_owner) ? $default_scm_cargo_owner['mach_code'] : '';
        //18392需求查询scm库存按照配置的仓库ID组查询
        $params['stock_id'] = EnumsService::getInstance()->getSettingEnvValue('material_asset_apply_scm_stock_ids');
        return $params;
    }

    /**
     * 获取更新至scm的各个barcode的可用库存
     * @param array $update_to_scm_barcode 更新至scm的barcode组
     * @return array
     * @throws ValidationException
     */
    public function getBarcodeAvailableInventory($update_to_scm_barcode)
    {
        $default_scm_cargo_stock_ids = $this->getScmMachCodeAndStockIds();
        return AssetOutStorageService::getInstance()->handleUpdateToScmBarcode($update_to_scm_barcode, $default_scm_cargo_stock_ids['mach_code'], $default_scm_cargo_stock_ids['stock_id']);
    }

    /**
     * 领用申请-添加
     * @param array $params 请求参数组
     * @param array $user 当前登陆者信息组
     * @return array
     */
    public function add($params, $user)
    {
        $code = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $data = [];
        $db = $this->getDI()->get('db_oa');
        try {
            $db->begin();
            //仅可对本人的单据做提交操作
            if ($params['staff_id'] != $user['id']) {
                throw new ValidationException(static::$t->_("material_asset_apply_user_error"), ErrCode::$VALIDATE_ERROR);
            }
            //验证参数
            $params = $this->validationSubmit($params, $user['id']);

            //判断申请单号是否已存在
            $apply_no= $params['apply_no'];
            $asset_apply = MaterialAssetApplyModel::findFirst([
                'conditions'=>'apply_no=:apply_no: and is_deleted=:is_deleted:',
                'bind'=>['apply_no'=>$apply_no, 'is_deleted'=>MaterialClassifyEnums::IS_DELETED_NO]
            ]);
            if (!empty($asset_apply)) {
                throw new ValidationException(static::$t->_('material_asset_apply_no_existed'), ErrCode::$VALIDATE_ERROR);
            }
            //操作时间
            $params['now_time'] = date('Y-m-d H:i:s', time());
            //资产申请单信息入库
            $order_data = $this->getApplyMainData($params);
            $main_model = new MaterialAssetApplyModel();
            $bool = $main_model->i_create($order_data);
            if ($bool === false) {
                throw new BusinessException('资产申请单[资产申请单添加失败]: 待处理数据: '. json_encode($order_data, JSON_UNESCAPED_UNICODE) . '; 可能存在的问题: ' . get_data_object_error_msg($main_model), ErrCode::$MATERIAL_ASSET_APPLY_ADD_ERROR);
            }

            //添加申请单附属信息（资产明细行、附件信息）到表
            $this->saveApplyAttachedToDb($params, $main_model);
            $db->commit();
        } catch (ValidationException $e) {
            $code = $e->getCode();
            $message = $e->getMessage();
        } catch (BusinessException $e) {
            // 业务错误（数据错误等），不可对外抛出
            $code = $e->getCode();
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        } catch (\Exception $e) {
            // 系统错误，不可对外抛出
            $code = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        }
        if (!empty($message)) {
            $db->rollback();
        }
        if (!empty($real_message)) {
            $this->logger->error('material-asset-apply-add failed:' . $real_message . json_encode($data));
        }
        return [
            'code' => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data' => []
        ];
    }

    /**
     * 验证数据提交合法性以及组装数据
     * @param array $params 请求参数组
     * @param integer $user_id 员工工号
     * @throws ValidationException
     * @return mixed
     * @throws ValidationException
     */
    private function validationSubmit($params, $user_id)
    {
        //验证附件信息是否超过最大附件数限制
        if (!empty($data['attachments'])) {
            if (count($data['attachments']) > Enums\MaterialAssetApplyEnums::OSS_MATERIAL_MAX) {
                throw new ValidationException(static::$t->_('material_asset_apply_attachments_error'), ErrCode::$VALIDATE_ERROR);
            }
        }

        //验证明细行barcode是否重复
        $barcode_arr = array_filter(array_unique(array_column($params['products'], 'barcode')));
        if (count($barcode_arr) < count($params['products'])) {
            throw new ValidationException(static::$t->_('material_asset_barcode_unique_error'), ErrCode::$VALIDATE_ERROR);
        }

        //检测用户是否存在
        $user_info = (new HrStaffRepository())->getStaffById($user_id);
        if (empty($user_info)) {
            throw new ValidationException(static::$t->_('re_staff_info_id_not_exist'), ErrCode::$VALIDATE_ERROR);
        }
        $consignee_info = HrStaffInfoModel::findFirst([
            'conditions' => 'staff_info_id = :staff_id:',
            'bind' => ['staff_id' => $params['consignee_id']]
        ]);
        if(empty($consignee_info)){
            throw new ValidationException('consignee_id error');
        }
        //如果是 lnt 公司 验证 barcode 是否在配置内
        $lntNotice = $this->checkLntBarcode($params['products'], $consignee_info->toArray());
        if(!empty($lntNotice)){
            throw new ValidationException(static::$t->_('lnt_barcode_notice', ['code_str' => $lntNotice]), ErrCode::$VALIDATE_ERROR);
        }

        //个人代理 - 系统开启雇佣类型只能申请固定barcode
        $personal_agent = ($user_info['hire_type'] == StaffInfoEnums::HIRE_TYPE_PERSONAL_AGENCY && $this->getPersonalAgentStatus()) ? true : false;
        $base_barcode = $personal_agent ? EnumsService::getInstance()->getSettingEnvValueIds(MaterialAssetApplyEnums::MATERIAL_ASSET_PERSONAL_AGENT_BARCODE_KEY) : [];

        //验证明细行每行barcode申请数量是否超过库存数量
        foreach ($params['products'] as $product) {
            if ($product['this_time_num'] > $product['available_inventory']) {
                throw new ValidationException(static::$t->_('material_asset_apply_num_error'), ErrCode::$VALIDATE_ERROR);
            } elseif ($personal_agent && ($product['this_time_num'] > 1 || empty($base_barcode) || !in_array($product['barcode'], $base_barcode))) {
                //个人代理 只可申请1个,配置barcode不存在，或不在配置的barcode组里
                throw new ValidationException(static::$t->_('material_asset_barcode_permission_error'), ErrCode::$VALIDATE_ERROR);
            }
        }

        //验证提交过来的barcode
        $barcode_list = MaterialSauModel::find([
            'conditions' => 'is_deleted=:is_deleted: and barcode in ({barcode:array})',
            'bind'=>['is_deleted'=>MaterialClassifyEnums::IS_DELETED_NO, 'barcode'=>$barcode_arr]
        ])->toArray();
        $select_barcode = array_column($barcode_list, 'barcode');
        //验证是否存在已删除的barcode
        $diff_barcode = array_diff($barcode_arr, $select_barcode);
        if (!empty($diff_barcode)) {
            throw new ValidationException(static::$t->_('material_asset_barcode_enable_or_delete', ['barcode'=>implode(";", $diff_barcode)]), ErrCode::$VALIDATE_ERROR);
        }

        //未启用barcode
        $enable_barcode = [];
        //非资产类的barcode
        $not_asset_barcode = [];
        //非包含可申请类的barcode
        $not_use_scene_apply_barcode = [];
        //包含职位的barcode组
        $contain_job_barcode_ids = [];
        //不包含职位的barcode组
        $not_contain_job_barcode_ids = [];
        foreach ($barcode_list as $item) {
            if ($item['status'] == MaterialClassifyEnums::MATERIAL_PROHIBITED_USE) {
                $enable_barcode[] = $item['barcode'];
            } elseif ($item['category_type'] != MaterialClassifyEnums::MATERIAL_CATEGORY_TYPE_ASSET) {
                $not_asset_barcode[] = $item['barcode'];
            } elseif (!in_array($item['use_scene'], [MaterialClassifyEnums::MATERIAL_USE_SCENE_APPLY, MaterialClassifyEnums::MATERIAL_USE_SCENE_APPLY_AND_BUY])) {
                $not_use_scene_apply_barcode[] = $item['barcode'];
            } elseif($item['is_contain_job'] == MaterialClassifyEnums::IS_CONTAIN_JOB_YES) {
                $contain_job_barcode_ids[] = $item['id'];
            } elseif ($item['is_contain_job'] == MaterialClassifyEnums::IS_CONTAIN_JOB_NO) {
                $not_contain_job_barcode_ids[] = $item['id'];
            }

        }
        //验证是否存在非启用的barcode
        if (!empty($enable_barcode)) {
            throw new ValidationException(static::$t->_('material_asset_barcode_enable_or_delete', ['barcode' => implode(';', $enable_barcode)]), ErrCode::$VALIDATE_ERROR);
        }
        //验证是否存在非资产类的barcode
        if (!empty($not_asset_barcode)) {
            throw new ValidationException(static::$t->_('material_asset_barcode_not_asset', ['barcode' => implode(';', $not_asset_barcode)]), ErrCode::$VALIDATE_ERROR);
        }
        //非包含可申请类的barcode
        if (!empty($not_use_scene_apply_barcode)) {
            throw new ValidationException(static::$t->_('material_asset_barcode_not_use_scene_apply', ['barcode' => implode(';', $not_use_scene_apply_barcode)]), ErrCode::$VALIDATE_ERROR);
        }

        //职位id
        $job_id = $params['job_id'];
        $all_apply_barcode = array_column($barcode_list, 'barcode', 'id');
        $not_barcode_job_permission = [];
        //当前添加的barcode中设置了包含申请人的职位的barcode
        if ($contain_job_barcode_ids) {
            $sau_permission = MaterialSauPermissionModel::find([
                'columns' => 'sau_id,GROUP_CONCAT(job_id) as job_id',
                'conditions' => 'sau_id in ({sau_ids:array})',
                'bind' => ['sau_ids' => $contain_job_barcode_ids],
                'group' => 'sau_id'
            ])->toArray();
            if (!empty($sau_permission)) {
                foreach ($sau_permission as $sau) {
                    //不在里面要拦截
                    if (!in_array($job_id, explode(',', $sau['job_id']))) {
                        $not_barcode_job_permission[] = $all_apply_barcode[$sau['sau_id']];
                    }
                }
            }
        }
        //当前添加的barcode中设置了不包含申请人的职位的barcode组
        if ($not_contain_job_barcode_ids) {
            $sau_permission = MaterialSauPermissionModel::find([
                'columns' => 'sau_id,GROUP_CONCAT(job_id) as job_id',
                'conditions' => 'sau_id in ({sau_ids:array})',
                'bind' => ['sau_ids' => $not_contain_job_barcode_ids],
                'group' => 'sau_id'
            ])->toArray();
            if (!empty($sau_permission)) {
                foreach ($sau_permission as $sau) {
                    //在里面要拦截
                    if (in_array($job_id, explode(',', $sau['job_id']))) {
                        $not_barcode_job_permission[] = $all_apply_barcode[$sau['sau_id']];
                    }
                }
            }
        }
        if (!empty($not_barcode_job_permission)) {
            throw new ValidationException(static::$t->_('material_asset_barcode_cannot_apply', ['barcode' => implode(';', $not_barcode_job_permission)]), ErrCode::$VALIDATE_ERROR);
        }


        //个人代理 - 系统开启雇佣类型只能申请固定barcode
        if ($personal_agent) {
            //已审批未出库单的资产申请单该barcode的申请数量，存在则拦截不能再申请
            $this->checkPersonalAgentCanApply($user_id, $barcode_arr);
            //个人代理，需要存储特定的公司信息
            $personal_agent_company_id = EnumsService::getInstance()->getSettingEnvValue(MaterialAssetApplyEnums::MATERIAL_ASSET_PERSONAL_AGENT_COMPANY_KEY, 0);
            $personal_agent_company_info = [];
            if ($personal_agent_company_id) {
                $personal_agent_company_info = (new DepartmentRepository())->getDepartmentDetail($personal_agent_company_id);
            }
            $params['company_id'] = $personal_agent_company_id;
            $params['company_name'] = $personal_agent_company_info ? $personal_agent_company_info['name'] : '';
        }
        return $params;
    }

    /**
     * 检测个人代理名下本次申请的barcode是否已存在审批通过未出库申请单/名下存在使用中的资产，存在则拦截不能再申请
     * @param integer $user_id 员工工号
     * @param array $apply_barcode 本次申请的barcode组
     * @return bool
     * @throws ValidationException
     */
    private function checkPersonalAgentCanApply($user_id, $apply_barcode)
    {
        $builder = $this->modelsManager->createBuilder();
        $builder->columns('product.barcode');
        $builder->from(['main' => MaterialAssetApplyModel::class]);
        $builder->leftjoin(MaterialAssetApplyProductModel::class, 'main.id=product.apply_id', 'product');
        $builder->where('main.is_deleted = :is_deleted: and product.is_deleted = :is_deleted: and main.staff_id = :staff_id: and main.status = :status: and product.total_issued_num = 0', ['is_deleted' => GlobalEnums::IS_NO_DELETED, 'staff_id' => $user_id, 'status' => Enums::WF_STATE_APPROVED]);
        $builder->inWhere('product.barcode', $apply_barcode);
        $list = $builder->getQuery()->execute()->toArray();
        if ($list) {
            throw new ValidationException(static::$t->_('material_asset_barcode_had_apply', ['barcode' => implode(',', array_column($list, 'barcode'))]), ErrCode::$VALIDATE_ERROR);
        }

        //个人代理申请人名下&使用中&资产&配置里的barcode，存在资产则拦截不能再申请
        $has_asset_list = MaterialAssetsRepository::getInstance()->searchAsset(['staff_id' => $user_id, 'status' => MaterialEnums::ASSET_STATUS_USING, 'category_type' => MaterialClassifyEnums::MATERIAL_CATEGORY_TYPE_ASSET, 'bar_code' => $apply_barcode]);
        if ($has_asset_list) {
            throw new ValidationException(static::$t->_('material_asset_barcode_has_asset', ['barcode' => implode(',', array_column($has_asset_list, 'bar_code'))]), ErrCode::$VALIDATE_ERROR);
        }

        return true;
    }


    public function checkLntBarcode($products, $user_info){
        $country_code = get_country_code();
        //只有马来
        if($country_code != GlobalEnums::MY_COUNTRY_CODE){
            return '';
        }
        $isLnt = HrStaffRepository::isLntCompanyByInfo($user_info);
        if(!$isLnt){
            return '';
        }

        //V22411 - 职位=Bike Courier[13]/Van Courier[110]/Car Courier[1199]，提交时校验资产申请里所有的barcode是否在OA-系统配置-lnt_bar_code
        $jobIds = EnumsService::getInstance()->getSettingEnvValueIds(MaterialAssetApplyEnums::MATERIAL_ASSET_LNT_JOB_IDS);
        if(empty($jobIds) || !in_array($user_info['job_title'], $jobIds)) {
            return '';
        }

        $settingBarcode = EnumsService::getInstance()->getSettingEnvValueIds(MaterialAssetApplyEnums::MATERIAL_ASSET_LNT_BARCODE);
        if(empty($settingBarcode)){
            $this->logger->info('checkLntBarcode 没有配置');
            return '';
        }
        $return = [];//不在 配置里的商品信息
        foreach ($products as $p){
            if(in_array($p['barcode'], $settingBarcode)){
                continue;
            }
            $return[] = $p['barcode'];
        }
        $str = empty($return) ? '' : implode(',', array_unique($return));
        $this->logger->info('checkLntBarcode ' . $str);
        return $str;
    }

    /**
     * 获取申请单主信息组
     * @param array $params 请求参数组
     * @param string  $type add，recommit
     * @return array
     * @throws ValidationException
     */
    private function getApplyMainData($params, $type = 'add')
    {
        //当前操作时间
        $now_time = $params['now_time'];
        //调取by创建审批
        $by_workflow = new ByWorkflowService();
        $by_add_result = $by_workflow->add([
            'submitter_id'=>$params['staff_id'],
            'summary_data'=>[['key'=>'using_department', 'value'=>$params['use_land_name']]],
            'biz_type' => Enums\ByWorkflowEnums::BY_BIZ_TYPE_ASSET,
            'audit_params' => [
                'company_id' => $params['company_id']
            ]
        ]);
        $apply_data = [
            'apply_no' => $params['apply_no'],
            'workflow_no' => $by_add_result['serial_no'],
            'apply_date' => $params['apply_date'],
            'staff_id' => $params['staff_id'],
            'staff_name' => $params['staff_name'],
            'company_id' => $params['company_id'],
            'company_name' => $params['company_name'],
            'node_department_id' => $params['node_department_id'],
            'node_department_name' => $params['node_department_name'],
            'sys_store_id' => $params['sys_store_id'],
            'store_name' => $params['store_name'],
            'job_id' => $params['job_id'],
            'use_land_id' => $params['use_land_id'],
            'use_land_name' => $params['use_land_name'],
            'reason' => $params['reason'],
            'status' => Enums::CONTRACT_STATUS_PENDING,
            'source_type' => $params['source_type'] ?? 0,
            'updated_at' => $now_time,
            'consignee_id' => $params['consignee_id'],
            'consignee_name' => $params['consignee_name'],
            'delivery_way' => $params['delivery_way'],
            'is_instead_apply' => ($params['staff_id'] == $params['consignee_id']) ? MaterialWmsEnums::IS_INSTEAD_APPLY_NO : MaterialWmsEnums::IS_INSTEAD_APPLY_YES
        ];
        if ($type == 'add') {
            $apply_data ['created_at'] = $now_time;
        } elseif ($type == 'recommit') {
            $apply_data ['operation_remark'] = '';
        }
        return $apply_data;
    }

    /**
     * 添加或重新提交申请单附属信息（资产明细行、附件信息）到表
     * @param array $params 请求参数组
     * @param object $apply_model 申请单对象信息
     * @throws BusinessException
     */
    private function saveApplyAttachedToDb($params, $apply_model)
    {
        //当前操作时间
        $now_time = $params['now_time'];
        //申请明细信息入库
        $products = [];
        foreach ($params['products'] as $product) {
            $products[] = [
                'apply_id' => $apply_model->id,
                'apply_no' => $apply_model->apply_no,
                'barcode' => $product['barcode'],
                'name_zh' => $product['name_zh'],
                'name_en' => $product['name_en'],
                'name_local' => $product['name_local'],
                'unit_zh' => $product['unit_zh'],
                'unit_en' => $product['unit_en'],
                'model' => $product['model'],
                'use' => $product['use'],
                'this_time_num' => $product['this_time_num'],
                'last_time_num' => $product['this_time_num'],
                'created_at' => $now_time,
                'updated_at' => $now_time
            ];
        }
        $apply_product = new MaterialAssetApplyProductModel();
        if (!empty($products) && !$apply_product->batch_insert($products)) {
            throw new BusinessException('资产申请单[资产申请单明细添加失败]: 待处理数据: '. json_encode($products, JSON_UNESCAPED_UNICODE) . '; 可能存在的问题: ' . get_data_object_error_msg($apply_product), ErrCode::$MATERIAL_ASSET_APPLY_ADD_ERROR);
        }
        //申请单附件信息入库
        if (!empty($params['attachments'])) {
            $material_attachment = new MaterialAttachmentModel();
            $attachArr = [];
            foreach ($params['attachments'] as $attachment) {
                $tmp = [];
                $tmp['oss_bucket_type'] = Enums\MaterialAssetApplyEnums::OSS_MATERIAL_TYPE_ASSET_APPLY;
                $tmp['oss_bucket_key'] = $apply_model->id;
                $tmp['sub_type'] = 0;
                $tmp['bucket_name'] = $attachment['bucket_name'];
                $tmp['object_key'] = $attachment['object_key'];
                $tmp['file_name'] = $attachment['file_name'];
                $attachArr[] = $tmp;
            }
            if (!$material_attachment->batch_insert($attachArr)) {
                throw new BusinessException('资产申请单[资产申请附件添加失败]: 待处理数据: '. json_encode($attachArr, JSON_UNESCAPED_UNICODE) . '; 可能存在的问题: ' . get_data_object_error_msg($material_attachment), ErrCode::$MATERIAL_ASSET_APPLY_ADD_ERROR);
            }
        }
    }

    /**
     * 领用申请-重新提交
     * @param array $params 请求参数组
     * @param array $user 当前登陆者信息组
     * @return array
     */
    public function recommit($params, $user)
    {
        $code = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $data = [];
        $db = $this->getDI()->get('db_oa');
        try {
            $db->begin();
            //验证参数
            $params = $this->validationSubmit($params, $user['id']);
            //必须是提交人，和已拒绝、已撤回，加锁，防止重复点击
            $main_model = MaterialAssetApplyModel::findFirst([
                'conditions'=>'id=:id: and staff_id=:staff_id: and status in({status:array}) and is_deleted = :is_deleted:',
                'bind'=>['id'=>$params['id'], 'staff_id'=>$user['id'], 'status'=> [Enums::CONTRACT_STATUS_REJECTED, Enums::CONTRACT_STATUS_CANCEL], 'is_deleted'=>MaterialClassifyEnums::IS_DELETED_NO],
                'for_update'=>true
            ]);
            if(empty($main_model)) {
                throw new ValidationException(static::$t->_("material_asset_apply_has_submit"), ErrCode::$CONTRACT_GET_INFO_ERROR);
            }
            // 非同一个申请单的重新提交
            if ($main_model->apply_no != $params['apply_no']) {
                throw new ValidationException(static::$t->_("material_asset_apply_prohibit_resubmission"), ErrCode::$VALIDATE_ERROR);
            }
            $apply_product = $main_model->getProducts();
            $apply_product_data = $apply_product->toArray();

            //由于历史单据重新提交会直接覆盖，以防后期查看单据，所以这里记录下之前的数据
            $this->logger->info('重新提交之前的数据：'.json_encode(['apply'=>$main_model->toArray(), 'apply_product'=>$apply_product_data]));

            $now_time = date('Y-m-d H:i:s', time());
            //当前操作时间
            $params['now_time'] = $now_time;
            //记录下来旧的审批流编号
            $old_workflow_no = $main_model->workflow_no;
            //资产申请单信息更新入库
            $order_data = $this->getApplyMainData($params, 'recommit');
            $bool = $main_model->i_update($order_data);
            if ($bool === false) {
                throw new BusinessException('资产申请单[资产申请单重新提交失败]: 待处理数据: '. json_encode($order_data, JSON_UNESCAPED_UNICODE) . '; 可能存在的问题: ' . get_data_object_error_msg($main_model), ErrCode::$MATERIAL_ASSET_APPLY_ADD_ERROR);
            }
            //审批流归档到历史审批日志表中
            $by_workflow_model = new ByWorkflowBusinessRelModel();
            $by_workflow_data = [
                'biz_value' => $main_model->id,
                'biz_type' => Enums::WF_MATERIAL_ASSET,
                'workflow_no' => $old_workflow_no,
                'created_at' => $now_time,
                'updated_at' => $now_time
            ];
            $bool = $by_workflow_model->i_create($by_workflow_data);
            if ($bool === false) {
                throw new BusinessException('资产申请单[资产申请单重新提交归总by审批日志失败]: 待处理数据: ' . json_encode($by_workflow_data,
                        JSON_UNESCAPED_UNICODE) . '; 可能存在的问题: ' . get_data_object_error_msg($by_workflow_model),
                    ErrCode::$MATERIAL_ASSET_APPLY_ADD_ERROR);
            }

            //删除掉原来的资产明细行
            $delete_apply_product = $apply_product->delete();
            if ($delete_apply_product === false) {
                throw new BusinessException('资产申请单[资产申请单重新提交删除资产失败]: 待处理数据: '. json_encode($apply_product_data, JSON_UNESCAPED_UNICODE) . '; 可能存在的问题: ' . get_data_object_error_msg($apply_product), ErrCode::$MATERIAL_ASSET_APPLY_ADD_ERROR);
            }

            //重新提交时先删除原来的附件信息
            $delete_attachment = $db->updateAsDict(
                'material_attachment',
                ['deleted' => MaterialClassifyEnums::IS_DELETED_YES],
                'oss_bucket_key = '. $main_model->id.' and oss_bucket_type = '.Enums\MaterialAssetApplyEnums::OSS_MATERIAL_TYPE_ASSET_APPLY
            );
            if (!$delete_attachment) {
                throw new BusinessException('资产申请单[资产申请单重新提交删除失败]: 待处理数据: '. json_encode(['oss_bucket_key'=>$main_model->id, 'oss_bucket_type' => Enums\MaterialAssetApplyEnums::OSS_MATERIAL_TYPE_ASSET_APPLY], JSON_UNESCAPED_UNICODE) . '; 可能存在的问题: ' . get_data_object_error_msg($delete_attachment), ErrCode::$MATERIAL_ASSET_APPLY_ADD_ERROR);
            }

            //重新提交申请单新的附属信息（资产明细行、附件信息）到表
            $this->saveApplyAttachedToDb($params, $main_model);
            $db->commit();
        } catch (ValidationException $e) {
            $code = $e->getCode();
            $message = $e->getMessage();
        } catch (BusinessException $e) {
            // 业务错误（数据错误等），不可对外抛出
            $code = $e->getCode();
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        } catch (\Exception $e) {
            // 系统错误，不可对外抛出
            $code = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        }
        if (!empty($message)) {
            $db->rollback();
        }
        if (!empty($real_message)) {
            $this->logger->error('material-asset-apply-recommit failed:' . $real_message . json_encode($data));
        }
        return [
            'code' => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data' => []
        ];
    }

    /**
     * 领用申请-撤回
     * @param array $params 请求参数组
     * @param array $user 当前登陆者信息组
     * @return array
     */
    public function cancel($params, $user)
    {
        $code = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $data = [];
        $db = $this->getDI()->get('db_oa');
        try {
            $db->begin();

            //检测该申请单是否是待审核状态
            $main_model = MaterialAssetApplyModel::findFirst([
                'conditions'=>'id=:id: and staff_id=:staff_id: and status = :status: and is_deleted = :is_deleted:',
                'bind'=>['id'=>$params['id'], 'staff_id'=>$user['id'], 'status'=> Enums::CONTRACT_STATUS_PENDING, 'is_deleted'=>MaterialClassifyEnums::IS_DELETED_NO],
                'for_update'=>true
            ]);
            if(empty($main_model)) {
                throw new ValidationException(static::$t->_("material_asset_apply_cancel_error"), ErrCode::$VALIDATE_ERROR);
            }

            //调取by接口，by撤销成功，记录撤回信息
            $by_workflow = new ByWorkflowService();
            $by_workflow->audit(['serial_no'=>$main_model->workflow_no, 'biz_type' => Enums\ByWorkflowEnums::BY_BIZ_TYPE_ASSET, 'reason' => $params['reason'], 'status'=>Enums\ByWorkflowEnums::BY_OPERATE_CANCEL, 'operator_id'=>$user['id']]);

            //单据撤回
            $now_time = date('Y-m-d H:i:s');
            $cancel_order_data = [
                'operation_remark' => $params['reason'],
                'status' => Enums::CONTRACT_STATUS_CANCEL,
                'updated_at' => date('Y-m-d H:i:s')
            ];
            $bool = $main_model->i_update($cancel_order_data);
            if ($bool === false) {
                throw new BusinessException('资产申请单[资产申请单撤回失败]: 待处理数据: '. json_encode($cancel_order_data, JSON_UNESCAPED_UNICODE) . '; 可能存在的问题: ' . get_data_object_error_msg($main_model), ErrCode::$MATERIAL_ASSET_APPLY_CANCEL_ERROR);
            }

            //撤回时需要将当前可用库存记录到明细行上
            $products = $main_model->getProducts();
            $barcode_arr = array_values(array_filter(array_unique(array_column($products->toArray(), "barcode"))));
            $barcode_list = MaterialSauModel::find([
                'columns' => 'id, barcode, update_to_scm',
                'conditions' => 'barcode in ({barcode:array}) and is_deleted = :is_deleted:',
                'bind' => ['barcode'=>$barcode_arr, 'is_deleted' => MaterialClassifyEnums::IS_DELETED_NO]
            ])->toArray();
            if (!empty($barcode_list)) {
                //更新至scm的barcode组
                $update_to_scm_barcode = [];
                //非更新至scm的barcode组
                $not_update_to_scm_barcode = [];
                foreach ($barcode_list as $item) {
                    if ($item['update_to_scm'] == MaterialClassifyEnums::MATERIAL_CATEGORY_NO) {
                        $update_to_scm_barcode[] = $item['barcode'];
                    } else {
                        $not_update_to_scm_barcode[] = $item['barcode'];
                    }
                }
                //开始处理更新至scm的各个barcode的可用库存
                $barcode_available_inventory = [];
                if ($update_to_scm_barcode) {
                    $barcode_available_inventory = $this->getBarcodeAvailableInventory($update_to_scm_barcode);
                }
                //开始处理非更新至scm的各个barcode的可用库存
                $asset_barcode_available_inventory = AssetOutStorageService::getInstance()->handNotUpdateToScmBarcode($not_update_to_scm_barcode);
                $barcode_key_list = array_column($barcode_list, null, 'barcode');
                foreach ($products as $product) {
                    $barcode = $product->barcode;
                    if (!empty($barcode_key_list[$barcode])) {
                        $update_product['updated_at'] = $now_time;
                        if ($barcode_key_list[$barcode]['update_to_scm'] == MaterialClassifyEnums::MATERIAL_CATEGORY_NO) {
                            $update_product['available_inventory'] = $barcode_available_inventory[$barcode] && is_numeric($barcode_available_inventory[$barcode]) ?$barcode_available_inventory[$barcode]: 0;
                        } else {
                            $update_product['available_inventory'] = $asset_barcode_available_inventory[$barcode] ?? 0;
                        }
                        $bool = $product->i_update($update_product);
                        if ($bool === false) {
                            throw new BusinessException('资产申请单[资产申请单审核撤回失败]: 待处理数据: '. json_encode($update_product, JSON_UNESCAPED_UNICODE) . '; 可能存在的问题: ' . get_data_object_error_msg($product), ErrCode::$MATERIAL_ASSET_APPLY_CANCEL_ERROR);
                        }
                    }
                }
            }
            $db->commit();
        } catch (ValidationException $e) {
            $code = $e->getCode();
            $message = $e->getMessage();
        } catch (BusinessException $e) {
            // 业务错误（数据错误等），不可对外抛出
            $code = $e->getCode();
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        } catch (\Exception $e) {
            // 系统错误，不可对外抛出
            $code = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        }
        if (!empty($message)) {
            $db->rollback();
        }
        if (!empty($real_message)) {
            $this->logger->error('material-asset-apply-cancel failed:' . $real_message . json_encode($data));
        }
        return [
            'code' => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data' => []
        ];
    }

    /**
     * 领用申请-驳回
     * @param array $params 请求参数组
     * @param array $user 当前登陆者信息组
     * @return array
     */
    public function reject($params, $user)
    {
        $code = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $data = [];
        $db = $this->getDI()->get('db_oa');
        try {
            $db->begin();
            $this->logger->info('领用申请-驳回，审批人信息：【 ' . json_encode($user, JSON_UNESCAPED_UNICODE) . ' 】，参数组：【 ' . json_encode($params, JSON_UNESCAPED_UNICODE)) . '】';
            [$main_model, $products, $params] = $this->validationAudit($params);
            //调取by驳回接口，by驳回成功，记录驳回信息
            $by_workflow = new ByWorkflowService();
            $by_workflow->audit(['serial_no'=>$main_model->workflow_no, 'biz_type' => Enums\ByWorkflowEnums::BY_BIZ_TYPE_ASSET, 'reason' => $params['reason'], 'status'=>Enums\ByWorkflowEnums::BY_OPERATE_REJECT, 'operator_id'=>$user['id']]);
            //by审批成功，驳回单据
            $now_time = date('Y-m-d H:i:s');
            $reject_order_data = [
                'is_batch_lock' => MaterialAssetApplyEnums::MATERIAL_ASSET_IS_BATCH_UN_LOCK,
                'operation_remark' => $params['reason'],
                'status' => Enums::CONTRACT_STATUS_REJECTED,
                'updated_at' => $now_time
            ];
            $bool = $main_model->i_update($reject_order_data);
            if ($bool === false) {
                throw new BusinessException('资产申请单[资产申请单审核驳回失败]: 待处理数据: '. json_encode($reject_order_data, JSON_UNESCAPED_UNICODE) . '; 可能存在的问题: ' . get_data_object_error_msg($main_model), ErrCode::$MATERIAL_ASSET_APPLY_REJECT_ERROR);
            }
            //驳回时需要将当前可用库存记录到明细行上
            $params_products = array_column($params['products'], null, 'id');
            foreach ($products as $product) {
                $update_product = [
                    'last_time_num' => $params_products[$product->id]['last_time_num'],
                    'available_inventory' => $params_products[$product->id]['available_inventory'],
                    'updated_at' => $now_time
                ];
                $bool = $product->i_update($update_product);
                if ($bool === false) {
                    throw new BusinessException('资产申请单[资产申请单审核驳回失败]: 待处理数据: '. json_encode($update_product, JSON_UNESCAPED_UNICODE) . '; 可能存在的问题: ' . get_data_object_error_msg($product), ErrCode::$MATERIAL_ASSET_APPLY_REJECT_ERROR);
                }
            }
            //记录审批操作记录
            $by_workflow_audit_log = new ByWorkflowAuditLogModel();
            $reject_log = [
                'biz_type' => Enums::WF_MATERIAL_ASSET,
                'biz_value' => $main_model->id,
                'staff_id' => $main_model->staff_id,
                'approval_id' => $user['id'],
                'status' => Enums::CONTRACT_STATUS_REJECTED,
                'approval_time' => $now_time,
                'created_at' => $now_time,
                'updated_at' => $now_time
            ];
            $bool = $by_workflow_audit_log->i_create($reject_log);
            if ($bool === false) {
                throw new BusinessException('资产申请单[资产申请单审核驳回失败]: 待处理数据: '. json_encode($reject_log, JSON_UNESCAPED_UNICODE) . '; 可能存在的问题: ' . get_data_object_error_msg($by_workflow_audit_log), ErrCode::$MATERIAL_ASSET_APPLY_REJECT_ERROR);
            }
            $db->commit();
        } catch (ValidationException $e) {
            $code = $e->getCode();
            $message = $e->getMessage();
        } catch (BusinessException $e) {
            // 业务错误（数据错误等），不可对外抛出
            $code = $e->getCode();
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        } catch (\Exception $e) {
            // 系统错误，不可对外抛出
            $code = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        }
        if (!empty($message)) {
            $db->rollback();
        }
        if (!empty($real_message)) {
            $this->logger->error('material-asset-apply-reject failed:' . $real_message . json_encode($data));
        }
        return [
            'code' => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data' => []
        ];
    }

    /**
     * 可审批规则验证
     * @param array $params 请求参数组
     * @param integer $audit_type 审批操作类型，2驳回，3同意
     * @return array
     * @throws ValidationException
     */
    private function validationAudit($params, $audit_type = Enums::WF_STATE_REJECTED)
    {
        //同意操作若明细行的审核数量均为0，需要提示'该单据所有资产审批数量均为0，请点击驳回'
        if ($audit_type == Enums::WF_STATE_APPROVED && !array_sum(array_column($params['products'], 'last_time_num'))) {
            throw new ValidationException(static::$t->_('material_asset_apply_product_audit_num_zero'), ErrCode::$VALIDATE_ERROR);
        }
        //检测该申请单是否是待审核状态
        $main_model = MaterialAssetApplyModel::findFirst([
            'conditions'=>'id=:id: and status = :status: and is_deleted = :is_deleted:',
            'bind' => ['id' => $params['id'], 'status' => Enums::CONTRACT_STATUS_PENDING, 'is_deleted' => MaterialClassifyEnums::IS_DELETED_NO],
            'for_update'=>true
        ]);
        if(empty($main_model)) {
            throw new ValidationException(static::$t->_('material_asset_apply_audit_error'), ErrCode::$VALIDATE_ERROR);
        }
        //批量审核锁定的单据，单个审核不可提交，否则提示"该单据正在审批，请勿重复审批"
        if ($main_model->is_batch_lock == MaterialAssetApplyEnums::MATERIAL_ASSET_IS_BATCH_LOCK && empty($params['is_batch'])) {
            throw new ValidationException(static::$t->_('material_wms_apply_ing_audit'), ErrCode::$VALIDATE_ERROR);
        }
        //判断提交过来的资产明细与库里的资产明细数目是否一致
        $products = $main_model->getProducts();
        $product_list = $products->toArray();
        $db_products_ids = array_column($product_list, 'id');
        $params_products_ids = array_column($params['products'], 'id');
        if (array_diff($db_products_ids, $params_products_ids) || array_diff($params_products_ids, $db_products_ids)) {
            throw new ValidationException(static::$t->_('material_asset_apply_product_count_error'), ErrCode::$VALIDATE_ERROR);
        }
        //检测每行资产的可用库存[单个审核走这里，批量审核已经单独走过了]
        if (empty($params['is_batch']) && $audit_type == Enums::WF_STATE_APPROVED ) {
            $params['products'] = $this->checkBarcodeAvailableInventory($main_model->toArray(), $product_list, $params['products']);
        }
        return [$main_model, $products, $params];
    }

    /**
     * 检测每行资产的可用库存
     * @param array $apply_info 资产申请单信息组
     * @param array $apply_product_list 资产申请单-资产清单
     * @param array $params_product_list 审批参数组
     * @return mixed
     * @throws ValidationException
     */
    private function checkBarcodeAvailableInventory($apply_info, $apply_product_list, $params_product_list)
    {
        //获取各barcode最新库存
        $handel_product_list = $this->handleDetail($apply_info, $apply_product_list, MaterialAssetApplyEnums::LIST_TYPE_APPLY_AUDIT);
        $handel_product_list = array_column($handel_product_list, null, 'id');
        foreach ($params_product_list as &$item) {
            $one_product_info = $handel_product_list[$item['id']];
            $available_inventory = ($one_product_info['available_inventory'] && is_numeric($one_product_info['available_inventory'])) ? $one_product_info['available_inventory'] : 0;
            if ($item['last_time_num'] > $available_inventory) {
                throw new ValidationException(static::$t->_('material_asset_apply_product_audit_num_error'), ErrCode::$VALIDATE_ERROR);
            }
            $item['available_inventory'] = $available_inventory;
        }
        return $params_product_list;
    }

    /**
     * 领用申请-通过
     * @param array $params 请求参数组
     * @param array $user 当前登陆者信息组
     * @return array
     */
    public function pass($params, $user)
    {
        $code = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $data = [];
        $db = $this->getDI()->get('db_oa');
        try {
            $db->begin();
            $this->logger->info('领用申请-通过，审批人信息：【 ' . json_encode($user, JSON_UNESCAPED_UNICODE) . ' 】，参数组：【 ' . json_encode($params, JSON_UNESCAPED_UNICODE)) . '】';
            [$main_model, $products, $params] = $this->validationAudit($params, Enums::WF_STATE_APPROVED);
            //调取by审批接口，by审核通过成功，记录通过信息
            $by_workflow = new ByWorkflowService();
            $result = $by_workflow->audit(['serial_no'=>$main_model->workflow_no, 'biz_type' => Enums\ByWorkflowEnums::BY_BIZ_TYPE_ASSET, 'reason' => $params['reason'] ?? '', 'status'=>Enums\ByWorkflowEnums::BY_OPERATE_PASS, 'operator_id'=>$user['id']]);
            //by审批通过且最终节点审批才需要做下面的逻辑
            $now_time = date('Y-m-d H:i:s');
            if (!empty($result) && !empty($result['is_final']) && $result['is_final'] == 1) {
                $pass_order_data = [
                    'is_batch_lock' => MaterialAssetApplyEnums::MATERIAL_ASSET_IS_BATCH_UN_LOCK,
                    'status' => Enums::CONTRACT_STATUS_APPROVAL,
                    'approve_at' => $now_time,
                    'updated_at' => $now_time
                ];
                $bool = $main_model->i_update($pass_order_data);
                if ($bool === false) {
                    throw new BusinessException('资产申请单[资产申请单审核通过失败]: 待处理数据: '. json_encode($pass_order_data, JSON_UNESCAPED_UNICODE) . '; 可能存在的问题: ' . get_data_object_error_msg($main_model), ErrCode::$MATERIAL_ASSET_APPLY_PASS_ERROR);
                }
            }
            //将当前可用库存记录到明细行上
            $params_products = array_column($params['products'], null, 'id');
            foreach ($products as $product) {
                $update_product = [
                    'last_time_num' => $params_products[$product->id]['last_time_num'],
                    'available_inventory' => $params_products[$product->id]['available_inventory'],
                    'updated_at' => $now_time
                ];
                $bool = $product->i_update($update_product);
                if ($bool === false) {
                    throw new BusinessException('资产申请单[资产申请单审核通过失败]: 待处理数据: '. json_encode($update_product, JSON_UNESCAPED_UNICODE) . '; 可能存在的问题: ' . get_data_object_error_msg($product), ErrCode::$MATERIAL_ASSET_APPLY_PASS_ERROR);
                }
            }
            //记录审批操作记录
            $by_workflow_audit_log = new ByWorkflowAuditLogModel();
            $pass_log = [
                'biz_type' => Enums::WF_MATERIAL_ASSET,
                'biz_value' => $main_model->id,
                'staff_id' => $main_model->staff_id,
                'approval_id' => $user['id'],
                'status' => Enums::CONTRACT_STATUS_APPROVAL,
                'approval_time' => $now_time,
                'created_at' => $now_time,
                'updated_at' => $now_time
            ];
            $bool = $by_workflow_audit_log->i_create($pass_log);
            if ($bool === false) {
                throw new BusinessException('资产申请单[资产申请单审核通过失败]: 待处理数据: '. json_encode($pass_log, JSON_UNESCAPED_UNICODE) . '; 可能存在的问题: ' . get_data_object_error_msg($by_workflow_audit_log), ErrCode::$MATERIAL_ASSET_APPLY_PASS_ERROR);
            }
            $db->commit();
        } catch (ValidationException $e) {
            $code = $e->getCode();
            $message = $e->getMessage();
        } catch (BusinessException $e) {
            // 业务错误（数据错误等），不可对外抛出
            $code = $e->getCode();
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        } catch (\Exception $e) {
            // 系统错误，不可对外抛出
            $code = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        }
        if (!empty($message)) {
            $db->rollback();
        }
        if (!empty($real_message)) {
            $this->logger->error('material-asset-apply-pass failed:' . $real_message . json_encode($data));
        }
        return [
            'code' => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data' => []
        ];
    }

    /**
     * 查看申请-详情
     * @param array $params 参数组
     * @param integer $type 1:申请单详情，2审批详情，3数据查询详情
     * @param array $user 当前登陆者信息组
     * @return array
     */
    public function detail($params, $type, $user = [])
    {
        $code = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $detail = [];
        try {
            $apply = MaterialAssetApplyModel::findFirst([
                'conditions'=>'id=:id: and is_deleted = :is_deleted:',
                'bind'=>['id'=>$params['id'], 'is_deleted'=>MaterialClassifyEnums::IS_DELETED_NO]
            ]);
            if (empty($apply)) {
                throw new ValidationException(static::$t->_("material_asset_apply_not_found"), ErrCode::$VALIDATE_ERROR);
            }

            if ($type == Enums\MaterialAssetApplyEnums::LIST_TYPE_APPLY && $apply->staff_id != $user['id']) {
                //针对申请单详情，申请人只可查看自己的
                throw new ValidationException(static::$t->_("material_asset_apply_no_auth"), ErrCode::$VALIDATE_ERROR);
            }
            $detail = $apply->toArray();
            //获取申请人雇佣类型
            $hire_type_enum = EnumsService::getInstance()->getHireTypeEnum();//雇佣类型枚举
            $apply_user_info = (new HrStaffRepository())->getStaffById($detail['staff_id']);
            $detail['hire_type'] = $apply_user_info['hire_type'];
            $detail['hire_type_text'] = $hire_type_enum[$apply_user_info['hire_type']] ?? '';
            $detail['consignee_id'] = $detail['consignee_id'] ? $detail['consignee_id'] : '';
            $detail['delivery_way_text'] = static::$t->_(MaterialClassifyEnums::$delivery_way_arr[$detail['delivery_way']]);
            //附件明细
            $detail['attachments'] = $apply->getPicAttachment()->toArray();
            //资产明细
            $detail['products'] = $apply->getProducts()->toArray();
            $detail['products'] = $this->handleDetail($detail, $detail['products'], $type);
            //审批日志
            $detail['auth_logs'] = (new ByWorkflowService())->log(['serial_no' => $detail['workflow_no'] , 'operator_id' => $user['id'], 'biz_type' => Enums\ByWorkflowEnums::BY_BIZ_TYPE_ASSET]);
            //历史审批记录
            $detail['history_auth_logs'] = $this->getApplyHistoryAuthLog($apply->id);
            //获取关联的出库单列表、只有申请人名下审核已通过的申请单才需要去查询关联的出库单信息
            $detail['storage_list'] = AssetOutStorageService::getInstance()->getOutStorageListByApply($apply);
        } catch (ValidationException $e) {
            // 校验错误，可对外抛出
            $code = $e->getCode();
            $message = $e->getMessage();
        } catch (\Exception $e) {
            // 系统错误，不可对外抛出
            $code = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage();
            $this->logger->error('material-asset-apply-detail failed:' . $real_message);
        }
        return [
            'code' => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data' => $detail
        ];
    }

    /**
     * 查看申请-详情-针对by端
     * @param array $params 参数组
     * @param integer $type 1:申请单详情，2审批详情，3数据查询详情
     * @param array $user 当前登陆者信息组
     * @return array
     */
    public function detailForBy($params, $type, $user = [])
    {
        $code = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $detail = [];
        try {
            $workflow_no = $params['workflow_no'];
            //优先根据审批编号查询申请单数据
            $apply = MaterialAssetApplyModel::findFirst([
                'conditions'=>'workflow_no = :workflow_no: and is_deleted = :is_deleted:',
                'bind'=>['workflow_no' => $workflow_no, 'is_deleted' => MaterialClassifyEnums::IS_DELETED_NO]
            ]);
            if (empty($apply)) {
                //若查询不到，则查询该申请单的历史审批数据
                $rel = ByWorkflowBusinessRelModel::findFirst([
                    'columns' => 'biz_value',
                    'conditions' => 'workflow_no = :workflow_no: and biz_type = :biz_type:',
                    'bind' => ['workflow_no' => $workflow_no, 'biz_type' => Enums::WF_MATERIAL_ASSET]
                ]);
                if (!empty($rel)) {
                    //历史查到了，那么根据申请单ID查询下申请表数据
                    $apply = MaterialAssetApplyModel::findFirst([
                        'conditions'=>'id = :id: and is_deleted = :is_deleted:',
                        'bind'=>['id' => $rel->biz_value, 'is_deleted' => MaterialClassifyEnums::IS_DELETED_NO]
                    ]);
                    if (empty($apply)) {
                        //根据历史反查申请单局也没有找到，那就说明没有
                        throw new ValidationException(static::$t->_("material_asset_apply_not_found"), ErrCode::$VALIDATE_ERROR);
                    }
                }
            }

            if ($type == Enums\MaterialAssetApplyEnums::LIST_TYPE_APPLY) {
                if ($apply->staff_id != $user['id']) {
                    //针对申请单详情，申请人只可查看自己的
                    throw new ValidationException(static::$t->_("material_asset_apply_no_auth"), ErrCode::$VALIDATE_ERROR);
                }
            }
            $detail = $apply->toArray();
            $detail['consignee_id'] = $detail['consignee_id'] ? $detail['consignee_id'] : '';
            $detail['delivery_way_text'] = static::$t->_(MaterialClassifyEnums::$delivery_way_arr[$detail['delivery_way']]);
            //资产明细
            $detail['products'] = $apply->getProducts()->toArray();
            $detail['products'] = $this->handleDetail($detail, $detail['products'], $type);
            //附件明细
            $detail['attachments'] = $apply->getPicAttachment()->toArray();
            //by端对于资产图片、附件需要返回链接地址
            foreach ($detail['products'] as &$product) {
                if (!empty($product['pic'])) {
                    $barcode_pic = [];
                    foreach ($product['pic'] as $pic) {
                        $barcode_pic[] = gen_file_url($pic);
                    }
                    $product['pic'] = $barcode_pic;
                }
            }
            $attachment_pic = [];
            if (!empty($detail['attachments'])) {
                foreach ($detail['attachments']  as $attachment) {
                    $attachment_pic[] = gen_file_url($attachment);
                }
                $detail['attachments'] = $attachment_pic;
            }
        } catch (ValidationException $e) {
            // 校验错误，可对外抛出
            $code = $e->getCode();
            $message = $e->getMessage();
        } catch (\Exception $e) {
            // 系统错误，不可对外抛出
            $code = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage();
            $this->logger->error('material-asset-apply-detail-for-by failed:' . $real_message);
        }
        return [
            'code' => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data' => $detail
        ];
    }

    /**
     * 获取申请单的历史审批日志
     * @param integer $id 申请单ID
     * @return mixed
     */
    public function getApplyHistoryAuthLog($id)
    {
        $history = ByWorkflowBusinessRelModel::find([
            'conditions' => 'biz_value = :biz_value: and biz_type = :biz_type:',
            'bind' => ['biz_value' => $id, 'biz_type' => Enums::WF_MATERIAL_ASSET],
            'columns' => 'workflow_no',
            'order'=>'id desc',
            'limit' => 10
        ])->toArray();
        if (!empty($history)) {
            $info = static::$t->_('material_asset_apply.history_workflow');
            foreach ($history as &$item) {
                $item['workflow_no_text'] = $item['workflow_no'].$info;
            }
        }
        return $history;
    }

    /**
     * 格式化详情信息
     * @param array $apply_info 申请单信息组
     * @param array $product_list 申请单明细列表
     * @param integer $type 1:申请单详情，2审批详情，3数据查询详情
     * @return array
     * @throws ValidationException
     */
    private function handleDetail($apply_info, $product_list, $type)
    {
        if (!empty($product_list)) {
            $barcode_arr = array_values(array_filter(array_unique(array_column($product_list, "barcode"))));
            if (!empty($barcode_arr)) {
                $barcode_list = MaterialSauModel::find([
                    'columns' => 'id, barcode, update_to_scm',
                    'conditions' => 'barcode in ({barcode:array}) and is_deleted = :is_deleted:',
                    'bind' => ['barcode'=>$barcode_arr, 'is_deleted' => MaterialClassifyEnums::IS_DELETED_NO]
                ])->toArray();
                if (!empty($barcode_list)) {
                    //更新至scm的barcode组
                    $update_to_scm_barcode = [];
                    //非更新至scm的barcode组
                    $not_update_to_scm_barcode = [];

                    //申请人查看详情未通过审批的、审核人查看详情&&待审核、已撤回的、数据查询查看详情&&待审核的都需要实时查询各资产明细可用库存，其他展示表中记录的库存
                    if (($type == Enums\MaterialAssetApplyEnums::LIST_TYPE_APPLY && $apply_info['status'] != Enums::CONTRACT_STATUS_APPROVAL)
                        || ($type == Enums\MaterialAssetApplyEnums::LIST_TYPE_APPLY_AUDIT && in_array($apply_info['status'], [Enums::CONTRACT_STATUS_PENDING, Enums::CONTRACT_STATUS_CANCEL]))
                        || ($type == Enums\MaterialAssetApplyEnums::LIST_TYPE_APPLY_DATA && $apply_info['status'] == Enums::CONTRACT_STATUS_PENDING)
                    ) {
                        foreach ($barcode_list as $item) {
                            if ($item['update_to_scm'] == MaterialClassifyEnums::MATERIAL_CATEGORY_NO) {
                                $update_to_scm_barcode[] = $item['barcode'];
                            } else {
                                $not_update_to_scm_barcode[] = $item['barcode'];
                            }
                        }
                        //开始处理更新至scm的各个barcode的可用库存
                        if ($update_to_scm_barcode) {
                            $barcode_available_inventory = $this->getBarcodeAvailableInventory($update_to_scm_barcode);
                        }
                        //开始处理非更新至scm的各个barcode的可用库存
                        $asset_barcode_available_inventory = AssetOutStorageService::getInstance()->handNotUpdateToScmBarcode($not_update_to_scm_barcode);
                    }

                    $barcode_key_list = array_column($barcode_list, null, 'barcode');
                    //获取barcode图片信息
                    $materialAttachment = new  MaterialAttachmentModel();
                    $pic_arr_key_arr = $materialAttachment->getColumnArr($barcode_list);
                    //组装barcode数据信息
                    foreach ($product_list as &$product) {
                        $product['now_time_num'] = $product['last_time_num'];
                        $product['use_text'] = static::$t[MaterialEnums::$use[$product['use']]];
                        if (!empty($barcode_key_list[$product['barcode']])) {
                            if ($barcode_key_list[$product['barcode']]['update_to_scm'] == MaterialClassifyEnums::MATERIAL_CATEGORY_NO) {
                                $product['available_inventory'] = $barcode_available_inventory[$product['barcode']] ?? $product['available_inventory'];
                            } else {
                                $product['available_inventory'] = $asset_barcode_available_inventory[$product['barcode']] ?? $product['available_inventory'];
                            }
                            $product['pic'] = $pic_arr_key_arr[$barcode_key_list[$product['barcode']]['id']] ?? [];
                        } else {
                            $product['pic'] = [];
                        }
                    }
                }
            }
        }
        return $product_list ?? [];
    }

    /**
     * 资产领用申请-审批日志
     * @param array $params 请求参数组
     * @param array $user 当前登陆者信息组
     * @return array
     */
    public function getAuthLog($params, $user)
    {
        $code = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $data = [];
        try {
            $by_workflow = new ByWorkflowService();
            $data = $by_workflow->log(['serial_no' => $params['workflow_no'] , 'operator_id' => $user['id'], 'biz_type' => Enums\ByWorkflowEnums::BY_BIZ_TYPE_ASSET]);
        } catch (\Exception $e) {
            // 系统错误，不可对外抛出
            $code = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage();
            $this->logger->error('material-asset-apply-get_auth_log failed:' . $real_message . json_encode($data));
        }
        return [
            'code' => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data' => $data
        ];
    }

    /**
     * 获取申请单详情
     * @param array $params 搜索参数组
     * @return array
     */
    public function getInfo($params)
    {
        $code = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $detail = [];
        try {
            $apply = MaterialAssetApplyModel::findFirst([
                'conditions'=>'id=:id: and is_deleted = :is_deleted:',
                'bind'=>['id'=>$params['id'], 'is_deleted'=>MaterialClassifyEnums::IS_DELETED_NO]
            ]);
            if (!empty($apply)) {
                $detail = $apply->toArray();
                $detail['consignee_id'] = $detail['consignee_id'] ? $detail['consignee_id'] : '';
                $detail['delivery_way_text'] = static::$t->_(MaterialClassifyEnums::$delivery_way_arr[$detail['delivery_way']]);
                //附件明细
                $detail['attachments'] = $apply->getPicAttachment()->toArray();
                //资产明细
                $product_list = $apply->getProducts()->toArray();
                if (!empty($product_list)) {
                    $barcode_arr = array_values(array_filter(array_unique(array_column($product_list, "barcode"))));
                    $barcode_list = MaterialSauModel::find([
                        'columns' => 'id, barcode, update_to_scm, category_type',
                        'conditions' => 'barcode in ({barcode:array}) and is_deleted = :is_deleted:',
                        'bind' => ['barcode'=>$barcode_arr, 'is_deleted' => MaterialClassifyEnums::IS_DELETED_NO]
                    ])->toArray();
                    if (!empty($barcode_list)) {
                        $barcode_key_list = array_column($barcode_list, null, 'barcode');
                        //获取barcode图片信息
                        $materialAttachment = new  MaterialAttachmentModel();
                        $pic_arr_key_arr = $materialAttachment->getColumnArr($barcode_list);
                        //组装barcode数据信息
                        foreach ($product_list as &$product) {
                            if (!empty($barcode_key_list[$product['barcode']])) {
                                $product['category_type'] = $barcode_key_list[$product['barcode']]['category_type'];
                                $product['update_to_scm'] = $barcode_key_list[$product['barcode']]['update_to_scm'];
                                $product['category_type_text'] = self::$t[MaterialClassifyEnums::$material_category_arr_type[$product['category_type']]];
                                $product['update_to_scm_text'] = self::$t[MaterialClassifyEnums::$is_off[$product['update_to_scm']]];
                                $product['pic'] = $pic_arr_key_arr[$barcode_key_list[$product['barcode']]['id']] ?? [];
                            } else {
                                $product['pic'] = [];
                            }
                        }
                    }
                }
                $detail['products'] = $product_list;
            }
        } catch (\Exception $e) {
            // 系统错误，不可对外抛出
            $code = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage();
            $this->logger->error('material-asset-apply-get_info failed:' . $real_message);
        }
        return [
            'code' => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data' => $detail
        ];
    }

    /**
     * 资产申请-查看出库信息
     * @param array $params 请求参数组
     * @param array $user 当前登陆者信息组
     * @return array
     */
    public function getAssetOutStorageList($params, $user)
    {
        $code = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $data = [];
        try {
            $apply = MaterialAssetApplyModel::findFirst([
                'conditions'=>'workflow_no = :workflow_no: and staff_id = :staff_id: and is_deleted = :is_deleted:',
                'bind'=>[
                    'workflow_no' => $params['workflow_no'],
                    'staff_id' => $user['id'],
                    'is_deleted' => MaterialClassifyEnums::IS_DELETED_NO
                ]
            ]);
            if (empty($apply)) {
                throw new ValidationException(static::$t->_("material_asset_apply_not_found"), ErrCode::$VALIDATE_ERROR);
            }
            $data['delivery_way'] = $apply->delivery_way;
            //只有申请人名下审核已通过的申请单才需要去查询关联的出库单信息
            $data['storage_list'] = AssetOutStorageService::getInstance()->getOutStorageListByApply($apply);
        } catch (ValidationException $e) {
            // 校验错误，可对外抛出
            $code = $e->getCode();
            $message = $e->getMessage();
        } catch (\Exception $e) {
            // 系统错误，不可对外抛出
            $code = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage();
            $this->logger->error('material-asset-apply-getAssetOutStorageList failed:' . $real_message);
        }
        return [
            'code' => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data' => $data
        ];
    }

    /**
     * 资产申请-查看路由
     * @param string $locale 当前语种
     * @param array $params 请求参数组
     * @return array
     */
    public function getOutboundTrackingInfo($locale, $params)
    {
        $code = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $data = [];
        try {
            Validation::validate($params, ['no'=>'Required|StrLenGeLe:10,20', 'mach_code' => 'Required|StrLenGeLe:1,128']);
            $scm = new ScmService();
            $data = $scm->getOutboundTrackingInfo($params['mach_code'], $params['no'], $locale);
            if (!empty($data['trackingInfo'])) {
                foreach ($data['trackingInfo'] as &$item) {
                    if (!empty($item['routes'])) {
                        foreach ($item['routes'] as &$route)  {
                            $route['routeTime'] = date('Y-m-d H:i:s', $route['routeTime']);
                        }
                    }
                }
            }
        } catch (ValidationException $e) {
            // 校验错误，可对外抛出
            $code = $e->getCode();
            $message = $e->getMessage();
        } catch (\Exception $e) {
            // 系统错误，不可对外抛出
            $code = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage();
            $this->logger->error('material-asset-apply-getOutboundTrackingInfo failed:' . $real_message);
        }
        return [
            'code' => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data' => $data
        ];
    }

    /**
     * 获取导出任务人名下待审核下数据
     * @param array $condition 筛选条件组
     * @return array
     * @throws ValidationException
     */
    public function getAuditListCount($condition)
    {
        $page_size = empty($condition['pageSize']) ? MaterialAssetApplyEnums::MATERIAL_ASSET_APPLY_AUDIT_EXPORT : $condition['pageSize'];
        $page_num  = empty($condition['pageNum']) ? GlobalEnums::DEFAULT_PAGE_NUM : $condition['pageNum'];
        $builder   = $this->modelsManager->createBuilder();
        $builder->columns('main.workflow_no');
        $builder->from(['main' => MaterialAssetApplyModel::class]);
        $builder = $this->getCondition($builder, $condition, MaterialAssetApplyEnums::LIST_TYPE_APPLY_AUDIT);
        $search_list = $builder->getQuery()->execute()->toArray();
        $data = [];
        if (!empty($search_list)) {
            $workflow_no = array_column($search_list, 'workflow_no');
            $by_list_params = [
                'serial_no' => $workflow_no,
                'biz_type' => [ByWorkflowEnums::BY_BIZ_TYPE_ASSET],
                'approval_id' => $condition['user_id'],
                'state' => [ByWorkflowEnums::BY_OPERATE_WAIT_AUDIT],
                'page_num' => $page_num,
                'page_size' => $page_size,
            ];
            $result = (new ByWorkflowService())->getList($by_list_params);
            $data['total_count'] = !empty($result['total_count']) ? $result['total_count'] : 0;
            $data['serial_no'] = $result['list'] ? array_values(array_column($result['list'], 'serial_no')) : [];
        }
        return $data;
    }

    /**
     * 导出列表
     * @param array $condition 筛选条件
     * @param integer $type 导出的数据范围
     * @param integer $count 总记录数
     * @return array
     */
    public function getExportList($condition, $type, $count)
    {
        $page_size = empty($condition['pageSize']) ? GlobalEnums::DEFAULT_PAGE_SIZE : $condition['pageSize'];
        $page_num = empty($condition['pageNum']) ? GlobalEnums::DEFAULT_PAGE_NUM : $condition['pageNum'];
        $offset = $page_size * ($page_num - 1);
        $code = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $data = [
            'items' => [],
            'pagination' => [
                'current_page' => $page_num,
                'per_page' => $page_size,
                'total_count' => 0,
            ],
        ];
        try {
            if ($count > 0) {
                $builder = $this->modelsManager->createBuilder();
                $columns = 'main.apply_no, main.apply_date, main.staff_id, main.staff_name, main.company_name, main.node_department_name, main.store_name, main.use_land_name, main.reason, main.status, main.operation_remark, main.consignee_id, main.consignee_name, ';
                $columns .= 'product.barcode, product.name_' . (MaterialClassifyEnums::$language_fields[static::$language] ?? 'local') . ' as asset_name, product.model, product.use, product.this_time_num, product.last_time_num';
                $builder->columns($columns);
                $builder->from(['main' => MaterialAssetApplyModel::class]);
                $builder->leftjoin(MaterialAssetApplyProductModel::class, 'main.id = product.apply_id', 'product');
                //组合搜索条件
                $builder = $this->getCondition($builder, $condition, $type);
                $builder->limit($page_size, $offset);
                $builder->orderby('main.id ' . ($condition['sort'] ?? 'DESC'));
                $items = $builder->getQuery()->execute()->toArray();
                $items = $this->handleExportListItems($items, $type);
            }
            $data['items'] = $items ?? [];
            $data['pagination']['total_count'] = $count;
        } catch (ValidationException $e) {
            $code = $e->getCode();
            $message = $e->getMessage();
        } catch (\Exception $e) {
            $code = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $this->logger->warning('get_material_asset_apply_audit_export_list failed:' . $real_message);
        }
        return [
            'code' => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data' => $data,
        ];
    }

    /**
     * 格式化导出列表明细
     * @param array $items 列表
     * @param integer $type 导出的数据范围
     * @return array
     */
    public function handleExportListItems($items, $type)
    {
        $row_list = [];
        if ($type == MaterialAssetApplyEnums::LIST_TYPE_APPLY_AUDIT) {
            $staff_ids = array_values(array_unique(array_column($items, 'staff_id')));
            $staff_list = (new HrStaffRepository())->getStaffListByStaffIds($staff_ids);
            $hire_type_enum = EnumsService::getInstance()->getHireTypeEnum();
            foreach ($items as $item) {
                $one_staff_info = $staff_list[$item['staff_id']] ?? [];
                $row_list[] =[
                    $item['apply_no'],//资产申请单号
                    $item['barcode'],//Barcode
                    $item['apply_date'],//申请时间
                    $item['staff_id'],//申请人工号
                    $item['staff_name'],//申请人姓名
                    $one_staff_info ? ($hire_type_enum[$one_staff_info['hire_type']] ?? '') : '',//雇佣类型
                    $item['asset_name'] . ' ' . $item['model'],//资产名称/规格型号
                    static::$t[MaterialEnums::$use[$item['use']]],//使用方向
                    $item['this_time_num'],//申请数量
                    $item['company_name'],//申请人所属公司
                    $item['node_department_name'],//申请人所属部门
                    $item['store_name'],//申请人所属网点
                    $item['use_land_name'],//使用网点
                    $item['consignee_id'] ? $item['consignee_id'] : '',//资产使用人（收货人工号）
                    $item['consignee_name'],//资产使用人（收货人姓名）
                    $item['reason'],//申请理由
                    '',//审批结果
                    $item['last_time_num'],//审核数量
                    $item['operation_remark'],//驳回原因
                ];
            }
        }
        return $row_list;
    }

    /**
     * 资产审核导出表头
     * @return array
     **/
    public function getAuditExportHeader()
    {
        return [
            static::$t->_('material_asset_apply.no'),//资产申请单号
            static::$t->_('material_barcode'),//Barcode
            static::$t->_('storage_apply_date'),//申请时间
            static::$t->_('re_field_apply_id'),//申请人工号
            static::$t->_('re_field_apply_name'),//申请人姓名
            static::$t->_('material_asset.hire_type'),//雇佣类型
            static::$t->_('material_asset_apply.asset_name_model'),//资产名称/规格型号
            static::$t->_('material_asset.use'),//使用方向
            static::$t->_('material_asset_apply.this_time_num'),//申请数量
            static::$t->_('re_field_apply_company_name'),//申请人所属公司
            static::$t->_('re_field_apply_department_name'),//申请人所属部门
            static::$t->_('re_field_apply_store_name'),//申请人所属网点
            static::$t->_('material_asset.store_name'),//使用网点
            static::$t->_('material_asset_return.use_staff_id'),//资产使用人工号
            static::$t->_('material_asset_return.use_staff_name'),//资产使用人姓名
            static::$t->_('global.equipment_reason'),//申请理由
            static::$t->_('material_asset_apply.audit_status'),//审批结果
            static::$t->_('material_asset_apply.last_time_num'),//审核数量
            static::$t->_('global.reject_reason'),//驳回原因
        ];
    }

    /**
     * 领用申请审核-批量审核
     * @param string $excel_file 文件
     * @param array $user 当前员工信息组
     * @return array
     */
    public function importAddTask($excel_file, $user)
    {
        $real_message = '';
        try {
            //是否上传了文件
            if (empty($excel_file)) {
                throw new ValidationException(static::$t->_('bank_flow_not_found_file'), ErrCode::$VALIDATE_ERROR);
            }

            $file = $excel_file[0];
            //仅支持.xlsx格式的文件
            $extension = $file->getExtension();
            if (!in_array($extension, ['xlsx'])) {
                throw new ValidationException(static::$t->_('file_format_error'), ErrCode::$VALIDATE_ERROR);
            }
            //解析文件内容
            $config = ['path' => ''];
            $excel = new \Vtiful\Kernel\Excel($config);
            // 读取上传文件数据
            $excel_data = $excel->openFile($file->getTempName())
                ->openSheet()
                ->setType([
                    0 => \Vtiful\Kernel\Excel::TYPE_STRING,//资产申请单
                    1 => \Vtiful\Kernel\Excel::TYPE_STRING,//barcode
                    13 => \Vtiful\Kernel\Excel::TYPE_STRING,//审批结果
                ])
                ->getSheetData();
            //弹出excel标题一行信息
            array_shift($excel_data);
            //资产申请单号 - 必填 - 所有行不可都为空
            foreach ($excel_data as $line => $row) {
                if (empty($row[0] ?? '')) {
                    unset($excel_data[$line]);
                }
            }
            if (empty($excel_data)) {
                throw new ValidationException(static::$t->_('data_empty_or_read_data_failed'), ErrCode::$VALIDATE_ERROR);
            }

            //验证条数
            $import_limit = MaterialAssetApplyEnums::MATERIAL_ASSET_APPLY_AUDIT_MAX;
            if (count($excel_data) > $import_limit) {
                throw new ValidationException(static::$t->_('material_asset_apply_audit_import_error', ['num' => $import_limit]), ErrCode::$VALIDATE_ERROR);
            }

            // 导入中心
            return ImportCenterService::getInstance()->addImportTask($file, $user, ImportCenterEnums::TYPE_MATERIAL_ASSET_AUDIT);
        } catch (ValidationException $e) {
            // 校验错误，可对外抛出
            $code = $e->getCode();
            $message = $e->getMessage();
        } catch (\Exception $e) {
            // 系统错误，不可对外抛出
            $code = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        }
        if (!empty($real_message)) {
            $this->logger->error('importAddTask-资产领用申请-批量审核-任务新增失败-' . $real_message);
        }
        return [
            'code' => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data' => $code == ErrCode::$SUCCESS ? true : false
        ];
    }


    /**
     * 资产批量审核 上传结果查询
     * @param integer $staff_info_id 员工工号
     * @return array
     */
    public function getImportAuditResult($staff_info_id)
    {
        return ImportCenterService::getInstance()->getBarcodeImportResult(ImportCenterEnums::TYPE_MATERIAL_ASSET_AUDIT, $staff_info_id);
    }

    /**
     * 批量审核数据处理
     * @param array $excel_data 批量数据
     * @param int $user_id 批量操作人id
     * @param int $update_result_column 备注写入列
     * @return array
     * @throws BusinessException
     * @throws ValidationException
     */
    public function batchAudit(array $excel_data, int $user_id, int $update_result_column)
    {
        $code = ErrCode::$SUCCESS;
        $message = $real_message = $real_trace = '';
        $clear_excel_data = $correct_data = $error_data = [];
        $excel_apply_barcode = [];
        $apply_no_lock = [];//加锁单据号组

        $this->logger->info('资产批量审核 开始处理数据 上传的数据为' . json_encode($excel_data, JSON_UNESCAPED_UNICODE));
        foreach ($excel_data as $key => $row) {
            //只有申请单号和barcode同时为空视为空跳过
            if (empty($row[0]) && empty($row[1])) {
                continue;
            }
            $excel_apply_barcode[] = $row[1];
            $clear_excel_data[$key] = $row;
        }
        $excel_apply_barcode = array_values(array_unique(array_filter($excel_apply_barcode)));
        $db = $this->getDI()->get('db_oa');
        try {
            $result_data = $clear_excel_data;
            //第一步将excel中数据，按照申请单号 分组
            $apply_group_data = $this->excelToDataEdit($clear_excel_data);

            //第二步根据申请单号组，从数据库获取申请单记录
            $apply_no_arr = array_values(array_filter(array_keys($apply_group_data)));
            $apply_list_obj = MaterialAssetApplyModel::find([
                'conditions' => 'apply_no in ({array_nos:array}) and is_deleted = :is_deleted:',
                'bind' => ['array_nos' => $apply_no_arr, 'is_deleted' => GlobalEnums::IS_NO_DELETED]
            ]);
            $apply_list_data = $apply_list_obj->toArray();

            //第三步取到的申请单记录锁定审核
            [$apply_no_lock, $apply_no_un_lock] = $this->lockBatchApply($apply_list_data, $db);
            $this->logger->info('资产批量审核 批量加锁数据:' . json_encode($apply_no_lock, JSON_UNESCAPED_UNICODE) . ', 本次被其他增加过锁的数据是:' . json_encode($apply_no_un_lock, JSON_UNESCAPED_UNICODE));

            //第四步根据查询到的申请单绑定的审批流编号查询当前导入任务用户名下的待审核单据清单
            $by_workflow_no = array_column($apply_list_data, 'workflow_no');
            $by_workflow_data = $by_workflow_no ? ((new ByWorkflowService())->pending(['approval_id' => $user_id, 'audit_type' => ByWorkflowEnums::BY_BIZ_TYPE_ASSET, 'serial_no' => $by_workflow_no])) : [];
            $this->logger->info('资产批量审核 查询by数据为:' . json_encode($by_workflow_data, JSON_UNESCAPED_UNICODE));

            //第五步遍历每笔申请单据，获取申请单下产品明细
            $apply_product_list = [];
            foreach ($apply_list_obj as $apply) {
                $apply_product_list[$apply->apply_no] = $apply->getProducts()->toArray();
            }

            //第六步excel参数合法性验证
            $correct_and_error = $this->validationBatchEdit($excel_apply_barcode, $apply_group_data, $apply_list_data, $apply_product_list, $by_workflow_data);
            $this->logger->info('资产批量审核 校验之后的解析结果为' . json_encode($correct_and_error, JSON_UNESCAPED_UNICODE));

            //第七步将校验结果反写到excel文件中
            $error_data = $correct_and_error['error_data'];
            $correct_data = $correct_and_error['correct_data'];
            if ($correct_data || $error_data) {
                foreach ($correct_data as $index => $cv) {
                    $result_data[$index][$update_result_column] = self::$t['excel_result_validation_pass'];
                }
                foreach ($error_data as $err_index => $ev) {
                    $result_data[$err_index][$update_result_column] = self::$t['error_message_title'] . ' : ' . $ev['error_message'];
                }
            }

            //第八步处理验证通过可审核单据
            $audit_data = $correct_and_error['audit_data'];
            if (!empty($audit_data)) {
                $user['id'] = $user_id;
                foreach ($audit_data as $item) {
                    $audit_product_params = [];
                    foreach ($item['apply_product_list'] as $product) {
                        $audit_product_params[] = [
                            'id' => $product['id'],
                            'available_inventory' => $product['available_inventory'],
                            'last_time_num' => $product['last_time_num']
                        ];
                    }
                    $audit_params = [
                        'id' => $item['apply_info']['id'],
                        'is_batch' => 1,
                        'products' => $audit_product_params,
                        'reason' => $item['operation_remark']
                    ];
                    if ($item['status'] == MaterialAssetApplyEnums::MATERIAL_ASSET_AUDIT_APPROVAL_RESULT_AGREE) {
                        //审批通过
                        $pass_info = $this->pass($audit_params, $user);
                        $this->logger->info('资产批量审核 审核数据' . json_encode($audit_params, JSON_UNESCAPED_UNICODE) . '审核返回数据' . json_encode($pass_info, JSON_UNESCAPED_UNICODE));
                    } else if ($item['status'] == MaterialAssetApplyEnums::MATERIAL_ASSET_AUDIT_APPROVAL_RESULT_REJECT) {
                        //审批驳回
                        $reject_info = $this->reject($audit_params, $user);
                        $this->logger->info('资产批量审核 驳回数据' . json_encode($audit_params, JSON_UNESCAPED_UNICODE) . '驳回返回数据' . json_encode($reject_info, JSON_UNESCAPED_UNICODE));
                    }
                }
            }

            //第九步正常情况批量解锁
            $this->logger->info('资产批量审核 开始解锁  数据为' . json_encode($apply_no_lock, JSON_UNESCAPED_UNICODE));
            $this->unLockBatchApply($apply_no_lock, $db);
        } catch (ValidationException $e) {
            $code    = $e->getCode();
            $message = $e->getMessage();
        } catch (BusinessException $e) {
            $code         = $e->getCode();
            $message      = static::$t->_('retry_later');
            $real_message = $e->getMessage();
            $real_trace   = $e->getTraceAsString();
        } catch (\Exception $e) {
            $code         = ErrCode::$SYSTEM_ERROR;
            $message      = static::$t->_('retry_later');
            $real_message = $e->getMessage();
            $real_trace   = $e->getTraceAsString();
        }
        if (!empty($message)) {
            //第十步批量解锁【由于异常行为退出脚本的也需要对加锁的单据解锁】
            $this->logger->info('资产批量审核 开始解锁  数据为' . json_encode($apply_no_lock, JSON_UNESCAPED_UNICODE));
            $this->unLockBatchApply($apply_no_lock, $db);
        }

        if (!empty($real_message)) {
            $this->logger->error('资产批量审核更新失败 ' . $real_message . '; trace=' . $real_trace);
            $correct_data = [];
            $error_data   = $clear_excel_data;
        }
        return [
            'code' => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data' => [
                'excel_data' => $result_data,
                'all_num' => count($clear_excel_data),
                'success_num' => count($correct_data),
                'failed_sum' => count($error_data),
            ],
        ];
    }

    /**
     * 处理数据
     * @param $excel_data
     * @return array
     */
    public function excelToDataEdit($excel_data)
    {
        //excel转字段
        $data_key = [
            'apply_no',//资产申请单号
            'barcode',//Barcode
            'created_at',//申请时间
            'staff_id',//申请人工号
            'staff_name',//申请人姓名
            'hire_type',//雇佣类型
            'asset_name_model',//资产名称/规格型号
            'use',//使用方向
            'this_time_num',//申请数量
            'company_name',//申请人所属公司
            'node_department_name',//申请人所属部门
            'store_name',//申请人所属网点
            'use_land_name',//资产使用网点
            'consignee_id',//资产使用人（收货人工号）
            'consignee_name',//资产使用人（收货人姓名）
            'reason',//申请理由
            'status',//审批结果
            'last_time_num',//审核数量
            'operation_remark',//驳回原因/通过原因
        ];
        $data     = [];
        foreach ($excel_data as $k => $v) {
            foreach ($data_key as $index => $key) {
                $data[$k][$key] = trim($v[$index]);
            }
        }

        //将上传的审批明细按照申请单号分组
        $apply_group_data = [];
        foreach ($data as $line => $item) {
            $apply_group_data[$item['apply_no']]['barcode'][] = $item['barcode'];
            $apply_group_data[$item['apply_no']]['status'][] = $item['status'];
            $apply_group_data[$item['apply_no']]['operation_remark'][] = $item['operation_remark'];
            $apply_group_data[$item['apply_no']]['product'][$line] = $item;
        }
        return $apply_group_data;
    }

    /**
     * 批量审核/领用出库导入新增-锁定单据
     * @param array $apply_list 申请列表
     * @param objec $db 事务
     * @param string $field 字段名
     * @return array
     * @throws BusinessException
     */
    public function lockBatchApply($apply_list, $db, $field = 'is_batch_lock')
    {
        $apply_lock_no = $apply_un_lock_no = [];
        foreach ($apply_list as $apply) {
            //未锁定的锁定，已经锁定的不再被锁定
            if ($apply[$field] == MaterialAssetApplyEnums::MATERIAL_ASSET_IS_BATCH_UN_LOCK) {
                $apply_lock_no[] = $apply['apply_no'];
            } else {
                $apply_un_lock_no[] = $apply['apply_no'];
            }
        }
        $apply_no_string = "'" . implode("','", array_unique($apply_lock_no)) . "'";
        if (!empty($apply_no_string)) {
            $update_success = $db->updateAsDict(
                (new MaterialAssetApplyModel())->getSource(),
                [
                    $field => MaterialAssetApplyEnums::MATERIAL_ASSET_IS_BATCH_LOCK,
                    'updated_at'    => date('Y-m-d H:i:s', time())
                ],
                [
                    'conditions' => " apply_no IN ($apply_no_string)",
                ]
            );
            if (!$update_success) {
                $where = ($field == 'is_batch_lock') ? '资产批量审核' : '领用出库导入新增';
                throw new BusinessException($where . '修改锁数据失败  锁定的数据是' . $apply_no_string, ErrCode::$BUSINESS_ERROR);
            }
        }
        return [$apply_lock_no, $apply_un_lock_no];
    }

    /**
     * 批量审核/领用出库导入新增-锁定单据
     * @param array $apply_no_lock 锁定的数据
     * @param objec $db 事务
     * @param string $field 字段名
     * @return array
     * @throws BusinessException
     */
    public function unLockBatchApply($apply_no_lock, $db, $field = 'is_batch_lock')
    {
        if (empty($apply_no_lock)) {
            return [];
        }
        $apply_no_string = "'" . implode("','", array_values(array_unique($apply_no_lock))) . "'";
        $update_success  = $db->updateAsDict(
            (new MaterialAssetApplyModel())->getSource(),
            [
                $field => MaterialAssetApplyEnums::MATERIAL_ASSET_IS_BATCH_UN_LOCK,
                'updated_at' => date('Y-m-d H:i:s', time()),
            ],
            [
                'conditions' => " apply_no IN ($apply_no_string)",
            ]
        );
        if (!$update_success) {
            $where = ($field == 'is_batch_lock') ? '资产批量审核' : '领用出库导入新增';
            throw new BusinessException($where . '修改锁数据失败 解锁的数据是 ' . json_encode($apply_no_lock, JSON_UNESCAPED_UNICODE), ErrCode::$BUSINESS_ERROR);
        }
        return $update_success;
    }

    /**
     * 导入修改
     * 验证数据, 错误信息放到error_message
     * @param array $excel_apply_barcode excel中的所有barcode组
     * @param array $excel_apply_data excel文件上传的审批明细按照申请单号分组
     * @param array $apply_list_data 按照excel中的申请单号数据到数据库查询的数据
     * @param array $apply_product_list 每笔申请单下申请明细
     * @param array $by_workflow_data 本次审批数据
     * @return mixed
     * @throws ValidationException
     */
    public function validationBatchEdit(array $excel_apply_barcode, array $excel_apply_data, array $apply_list_data, array $apply_product_list, array $by_workflow_data)
    {
        $correct_data = $error_data = $audit_data = [];
        $sau_sku = StandardService::getInstance()->getBarcodeList(['barcode' => array_values(array_unique($excel_apply_barcode))]);
        $sau_sku =  array_column($sau_sku, null, 'barcode');
        $sau_sku_barcode = array_column($sau_sku, 'barcode');
        $apply_handle_data = array_column($apply_list_data, null, 'apply_no');
        //待审批数据组
        $by_workflow_data_pending = $by_workflow_data['pending'] ?? [];
        foreach ($excel_apply_data as $apply_no => $item) {
            //主单据检测
            $main_error_message = [];
            //单据明细行检测
            $main_product_error_message = [];
            $status_arr = [];
            $operation_remark_arr = [];
            //主单据检测
            if (empty($apply_no)) {
                //申请单号不可为空
                $main_error_message[] = self::$t['material_asset_apply_no_not_null'];
            } else {
                //申请单号在库里不存在
                if (!in_array($apply_no, array_keys($apply_handle_data))) {
                    $main_error_message[] = self::$t['material_asset_apply_no_error'];
                } else {
                    //存在但审批状态非待审核
                    if ($apply_handle_data[$apply_no]['status'] != Enums::WF_STATE_PENDING) {
                        $main_error_message[] = self::$t['material_asset_apply_status_error'];
                    }
                    //被锁定的单据，文档中包含正在审批的单据，不可重复审批，请删除后重新导入
                    if ($apply_handle_data[$apply_no]['is_batch_lock'] == MaterialAssetApplyEnums::MATERIAL_ASSET_IS_BATCH_LOCK) {
                        $main_error_message[] = self::$t['material_asset_apply_locked'];
                    }
                    //申请单非导出审核人名下单据
                    if (!in_array($apply_handle_data[$apply_no]['workflow_no'], $by_workflow_data_pending)) {
                        $main_error_message[] = self::$t['material_asset_apply_user_permission_error'];
                    }
                    //查询Excel内同一个资产申请单号中的barcode是否与系统中该申请单中的barcode相同【barcode和原数据是否相等】
                    $one_db_apply_barcode = array_column($apply_product_list[$apply_no], 'barcode');
                    if (!empty(array_diff($item['barcode'], $one_db_apply_barcode)) || !empty(array_diff($one_db_apply_barcode, $item['barcode']))) {
                        $main_error_message[] = self::$t['material_asset_apply_barcode_error'];
                    }
                    //审批结果不一致
                    $status_arr = array_unique($item['status']);
                    //驳回原因/通过原因必须相同
                    $operation_remark_arr = array_unique($item['operation_remark']);
                    if (count($status_arr) > 1) {
                        $main_error_message[] = self::$t['material_asset_apply_result_not_consistent'];
                    } else if ($status_arr[0] == MaterialAssetApplyEnums::MATERIAL_ASSET_AUDIT_APPROVAL_RESULT_AGREE && !array_sum(array_column($item['product'], 'last_time_num'))) {
                        //同一个审批单,通过时 单据里所有barcode的审核为0，只能审核拒绝
                        $main_error_message[] = self::$t['material_asset_apply_product_audit_num_zero'];
                    } else if (count($operation_remark_arr) > 1) {
                        //同一个审批单,驳回原因/通过原因必须相同
                        $main_error_message[] = self::$t['material_asset_apply_operation_remark_not_consistent'];
                    }
                }
            }

            //明细行检测
            foreach ($item['product'] as $line => &$v) {
                $product_error_message = [];
                // barcode校验 1 检测barcode不能为空  2 检测barcode是不是有效
                if (empty($v['barcode'])) {
                    //检测barcode不能为空
                    $product_error_message[] = self::$t['material_asset_apply_barcode_not_null'];
                } else {
                    //Barcode已删除 && 审核数量>0
                    if (!in_array($v['barcode'], $sau_sku_barcode) && preg_match(MaterialAssetApplyEnums::MATERIAL_ASSET_AUDIT_NUM_RULE, $v['last_time_num']) && $v['last_time_num'] > 0) {
                        $product_error_message[] = $v['barcode'] . self::$t['material_asset_apply_barcode_deleted'];
                    }
                    //审批通过操作 && 未删除 && 非资产类 && 审核数量>0
                    if ($v['status'] == MaterialAssetApplyEnums::MATERIAL_ASSET_AUDIT_APPROVAL_RESULT_AGREE && in_array($v['barcode'], $sau_sku_barcode) && $sau_sku[$v['barcode']]['category_type'] != MaterialClassifyEnums::MATERIAL_CATEGORY_TYPE_ASSET && preg_match(MaterialAssetApplyEnums::MATERIAL_ASSET_AUDIT_NUM_RULE, $v['last_time_num']) && $v['last_time_num'] > 0) {
                        $product_error_message[] = $v['barcode'] . self::$t['material_not_asset_barcode'];
                    }
                }

                // 审批结果校验 1审批意见必须是 Agree、Reject
                if (empty($v['status'])) {
                    //审批结果不能为空
                    $product_error_message[] = self::$t['material_asset_apply_approval_result_not_null'];
                } else if (!in_array($v['status'], [MaterialAssetApplyEnums::MATERIAL_ASSET_AUDIT_APPROVAL_RESULT_AGREE, MaterialAssetApplyEnums::MATERIAL_ASSET_AUDIT_APPROVAL_RESULT_REJECT])) {
                    $product_error_message[] = self::$t['material_asset_apply_approval_result_error'];
                }

                // 审核数量请填写大于等于0，小于等于9999的整数
                if (!preg_match(MaterialAssetApplyEnums::MATERIAL_ASSET_AUDIT_NUM_RULE, $v['last_time_num'])) {
                    $product_error_message[] = self::$t['material_asset_apply_product_last_time_num_error'];
                }

                // 驳回原因校验 1 当审批驳回时，驳回原因必填   2. 驳回原因限制1000字符
                if ($v['status'] == MaterialAssetApplyEnums::MATERIAL_ASSET_AUDIT_APPROVAL_RESULT_REJECT && empty($v['operation_remark'])) {
                    $product_error_message[] = self::$t['material_asset_apply_operation_remark_not_null'];
                }

                //驳回原因/通过原因长度限制
                if (mb_strlen($v['operation_remark']) > 1000) {
                    $product_error_message[] = self::$t['material_asset_apply_operation_remark_too_long'];
                }

                $error_message = implode(';', array_merge($main_error_message, $product_error_message));
                if ($error_message !== '') {
                    $main_product_error_message[] = $product_error_message;
                    $v['error_message'] = $error_message;
                    $error_data[$line] = $v;
                } else {
                    $v['error_message'] = '';
                    $correct_data[$line] = $v;
                }
            }

            //主数据 && 明细行数据均验证通过，才认为可以进行单据审批同意/驳回
            if (empty($main_error_message) && empty($main_product_error_message)) {
                //获取各barcode最新库存
                $handle_apply_product_list = $this->handleDetail($apply_handle_data[$apply_no], $apply_product_list[$apply_no], MaterialAssetApplyEnums::LIST_TYPE_APPLY_AUDIT);
                $handle_apply_product_list = array_column($handle_apply_product_list, null, 'barcode');
                $has_error_products_num = 0;//错误明细行数
                foreach ($item['product'] as $line => &$v) {
                    $one_db_apply_product_info = $handle_apply_product_list[$v['barcode']];
                    $available_inventory = ($one_db_apply_product_info['available_inventory'] && is_numeric($one_db_apply_product_info['available_inventory'])) ? $one_db_apply_product_info['available_inventory'] : 0;
                    if ($v['last_time_num'] > $available_inventory) {
                        $has_error_products_num ++;
                        $v['error_message'] = self::$t['material_asset_apply_product_audit_num_error'];
                        $error_data[$line] = $v;
                        unset($correct_data[$line]);
                    }
                    $v['id'] = $one_db_apply_product_info['id'];
                    $v['available_inventory'] = $available_inventory;
                }
                if ($has_error_products_num == 0) {
                    $audit_data[] = [
                        'apply_info' => $apply_handle_data[$apply_no],
                        'apply_product_list' => $item['product'],
                        'status' => $status_arr[0],
                        'operation_remark' => $operation_remark_arr[0] ?? ''
                    ];
                }
            }
        }
        return ['correct_data' => $correct_data, 'error_data' => $error_data, 'audit_data' => $audit_data];
    }

    /**
     * 资产领用申请审核-查看-网点资产数据
     * @param array $params 请求参数
     * @return array
     */
    public function auditAssetList($params)
    {
        $code = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $data = [];
        try {
            //资产使用地点
            $use_land_id = $params['use_land_id'];
            //非总部地址（headqarters_address）表ID
            if (!is_numeric($use_land_id)) {
                ini_set('memory_limit', '512M');

                //资产状态的值配置了才需要获取改网点在台账数据
                $asset_status = EnumsService::getInstance()->getSettingEnvValueIds('material_asset_apply_assets_status');
                if (!empty($asset_status)) {
                    $material_asset_list = MaterialAssetsRepository::getInstance()->searchAsset([
                        'is_staff_empty' => 1,
                        'status' => $asset_status,
                        'sys_store_id' => $use_land_id,
                        'category_type' => MaterialClassifyEnums::MATERIAL_CATEGORY_TYPE_ASSET
                    ]);
                    foreach ($material_asset_list as $asset) {
                        $asset_key = $asset['bar_code'] . '_' . $asset['staff_id'] . '_' . $asset['status'] . '_asset';
                        if (isset($data[$asset_key])) {
                            $data[$asset_key]['num'] += 1;
                        } else {
                            $data[$asset_key] = [
                                'barcode' => $asset['bar_code'],
                                'name_zh' => $asset['name_zh'],
                                'name_en' => $asset['name_en'],
                                'name_local' => $asset['name_local'],
                                'staff_id' => $asset['staff_id'],
                                'staff_name' => $asset['staff_name'],
                                'job_name' => $asset['job_name'],
                                'status' => $asset['status'],
                                'status_text' => static::$t->_(MaterialEnums::$asset_status[$asset['status']]),
                                'num' => 1
                            ];
                        }
                    }
                }

                //获取该网点下出库单数据
                $asset_out_storage_list = AssetOutStorageService::getInstance()->getAssetApplyAuditAssetList($params);
                foreach ($asset_out_storage_list as $out) {
                    $asset_out_key = $out['barcode'] . '_' . $out['staff_id'] . '_' . $out['status'] . '_out';
                    if (isset($data[$asset_out_key])) {
                        $data[$asset_out_key]['num'] += $out['this_time_num'];
                    } else {
                        $data[$asset_out_key] = [
                            'barcode' => $out['barcode'],
                            'name_zh' => $out['name_zh'],
                            'name_en' => $out['name_en'],
                            'name_local' => $out['name_local'],
                            'staff_id' => $out['staff_id'],
                            'staff_name' => $out['staff_name'],
                            'job_name' => $out['job_name'],
                            'status' => $out['status'],
                            'status_text' => static::$t->_(MaterialAssetOutStorageEnums::$asset_out_storage_status[$out['status']]),
                            'num' => $out['this_time_num']
                        ];
                    }
                }
                if (!empty($data)) {
                    $data = array_values($data);
                    $staff_ids = array_values(array_unique(array_column($data, 'staff_id')));
                    $staff_list = (new HrStaffRepository())->getStaffListByStaffIds($staff_ids);
                    foreach ($data as &$item) {
                        $staff_info = $staff_list[$item['staff_id']] ?? [];
                        $item['state'] = $staff_info['state'] ?? 0;
                        $staff_wait_leave_state = $staff_info['wait_leave_state'] ?? 0;
                        $state = ($item['state'] == StaffInfoEnums::STAFF_STATE_IN && $staff_wait_leave_state == StaffInfoEnums::STAFF_WAIT_LEAVE_STATE_YES) ? StaffInfoEnums::STAFF_STAFF_WAIT_LEAVE : $item['state'];
                        $item['state_text'] = $state ? static::$t->_(StaffInfoEnums::$staff_state[$state]) : '';
                    }
                    $data = sort_array_by_fields($data, 'barcode', SORT_ASC, 'staff_id', SORT_ASC);
                }
            }
        } catch (\Exception $e) {
            // 系统错误，不可对外抛出
            $code = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage();
            $this->logger->error('material-asset-apply-detail-auditAssetList failed:' . $real_message);
        }
        return [
            'code' => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data' => $data
        ];
    }

    /**
     * 资产申请-获取收货人
     * @param array $params 条件
     * @param array $user 用户数据
     * @return array
     */
    public function getConsigneeList(array $params, array $user)
    {
        return WmsApplyService::getInstance()->consigneeList($params, $user);
    }
}
