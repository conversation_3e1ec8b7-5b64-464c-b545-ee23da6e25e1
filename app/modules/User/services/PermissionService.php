<?php

namespace App\Modules\User\Services;

use App\Library\BaseService;
use App\Library\Enums\PermissionMenuEnums;
use App\Modules\User\Models\PermissionModel;
use Phalcon\Mvc\Model\ResultsetInterface;

class PermissionService extends BaseService
{

    /**
     * 获取所有已启用的权限菜单
     *
     * @return ResultsetInterface
     */
    public function getAllPermissions()
    {
        return PermissionModel::find([
            'conditions' => 'is_open = :is_open:',
            'bind'       => ['is_open' => PermissionMenuEnums::PERMISSION_MENU_STATUS_OPENED],
            'order'      => 'sort ASC, id ASC',
        ]);
    }

    /**
     * 获取已启用的
     *
     * @param $ids
     * @return ResultsetInterface
     */
    public function getPermissionsByIds($ids)
    {
        if (empty($ids)) {
            return null;
        }

        return PermissionModel::find([
            'conditions' => 'id IN ({ids:array}) AND is_open = :is_open:',
            'bind' => ['ids' => $ids, 'is_open' => PermissionMenuEnums::PERMISSION_MENU_STATUS_OPENED]
        ]);
    }

}
