<?php

namespace App\Modules\Payment\Services;

use App\Library\Enums\ContractEnums;
use App\Library\Enums\GlobalEnums;
use App\Library\Enums\PayEnums;
use App\Library\Enums\ImportCenterEnums;
use App\Models\oa\ContractStoreRentingRemindModel;
use App\Modules\Budget\Services\LedgerAccountService;
use App\Modules\Common\Services\EnumsService;
use App\Modules\Common\Services\ImportCenterService;
use App\Modules\Contract\Models\ContractArchive;
use App\Modules\Contract\Models\ContractStoreRentingArea;
use App\Modules\Contract\Models\ContractStoreRentingDetailModel;
use App\Modules\Contract\Models\ContractStoreRentingModel;
use App\Modules\Organization\Models\SysStoreModel;
use App\Modules\Payment\Models\PaymentCostLedgeRel;
use App\Modules\User\Models\DepartmentModel;
use App\Modules\User\Services\UserService;
use App\Repository\DepartmentRepository;
use App\Repository\StoreRepository;
use App\Util\RedisKey;
use App\Library\Enums;
use App\Library\ErrCode;
use App\Library\Validation\Validation;
use App\Library\Exception\BusinessException;
use App\Library\Validation\ValidationException;
use App\Modules\Payment\Models\PaymentStoreRenting;
use App\Modules\Payment\Models\PaymentStoreRentingDetail;
use App\Modules\User\Models\AttachModel;
use App\Modules\Common\Models\EnvModel;
use App\Modules\Reimbursement\Models\Pccode;
use App\Modules\Purchase\Services\BaseService as PurchaseService;
use App\Modules\Contract\Services\ContractStoreRentingService;
use Exception;

class StoreRentingAddService extends BaseService
{
    public static $result_column = 31;//批量导入数据-结果列-索引
    public static $not_must_params = [
        '_url',
        'PHPSESSID'
    ];


    public static $get_relate_contract = [
        'contract_id'                 => 'Required|StrLenGeLe:0,50', //合同编号
    ];


    private static $instance;

    private function __construct()
    {
    }

    /**
     * @return StoreRentingAddService
     */
    public static function getInstance()
    {
        if (!isset(self::$instance) || !(self::$instance instanceof self)) {
            self::$instance = new self;
        }
        return self::$instance;
    }

    /**
     * 提交规则验证
     * @param array $data 请求参数组
     * @return array
     * @throws ValidationException
     */
    public function extendValidation(array $data)
    {
        $validate = BaseService::$validate_apply_param;
        $validate_amount_detail = self::getValidateAmountDetail($data);
        Validation::validate($data, array_merge($validate, $validate_amount_detail));

        // 付款方式为银行转账时,境内境外支付为必填
        if ($data['payment_method'] == Enums::PAYMENT_METHOD_BANK_TRANSFER && empty($data['border_payment_type'])) {
            throw new ValidationException('payment type param error', ErrCode::$VALIDATE_ERROR);
        }

        // 校验金额详情的json格式
        if (!is_array($data['amount_detail'])) {
            $data['amount_detail'] = json_decode($data['amount_detail'], true);
            if (json_last_error() != JSON_ERROR_NONE) {
                throw new ValidationException('amount_detail param error', ErrCode::$VALIDATE_ERROR);
            }
        }

        // 金额详情: vat/sst、wht 严格校验
        $vat_config = EnumsService::getInstance()->getVatRateValueItem();
        $wht_config = EnumsService::getInstance()->getWhtRateMap();
        $vat_sst_name = EnumsService::getInstance()->getVatSStRateName();

        // 获取已归档的网点租房合同基本信息
        $archive_store_renting_contract = $this->getArchiveStoreRentingContract();
        $archive_store_renting_contract = array_column($archive_store_renting_contract, null, 'contract_no');

        // 获取已申请的网点费用类型列表
        $condition = [
            'approval_status' => [1, 3],
            'is_contract' => 1
        ];
        $store_contract_available_date_item = $this->getAllStoreApplyCostByConditions($condition);
        $store_contract_available_date_item = $this->extractAvailableStoreContractDate($store_contract_available_date_item, $archive_store_renting_contract);

        // 查询被作废的时间段
        $contract_nos = array_values(array_unique(array_column($data['amount_detail'], 'contract_no')));
        $contract_no_arr = ContractArchive::find([
            'columns' => 'cno, invalid_replace_cno, invalid_replace_begin, invalid_replace_end',
            'conditions' => 'invalid_replace_cno in ({invalid_replace_cno:array}) and status in ({status:array}) and invalid_reason_type = :invalid_reason_type:',
            'bind' => [
                'invalid_replace_cno' => $contract_nos,
                'status' => [ContractEnums::CONTRACT_ARCHIVE_STATUS_INVALID, ContractEnums::CONTRACT_ARCHIVE_STATUS_INVALID_ING],
                'invalid_reason_type' => ContractEnums::INVALID_REASON_TYPE_RE_SIGN
            ],
        ])->toArray();
        $contract_no_replace = [];
        foreach ($contract_no_arr as $contract_replace) {
            $contract_no_replace[$contract_replace['invalid_replace_cno']][] = $contract_replace;
        }

        //附件配置组
        $attachment_config_list = $this->getAttachmentAllConfig();

        //验证明细行
        $store_arr = $required_file_fields = [];

        //获取税额允差值
        $finance_tax_allowance_wht_lines = [];
        $finance_tax_allowance_vat_lines = [];
        $store_renting_finance_tax_allowance = EnumsService::getInstance()->getSettingEnvValueMap('store_renting_finance_tax_allowance');
        foreach ($data['amount_detail'] as $key => &$item) {
            // 付款方式为银行转账且境内/境外支付为境外时,收款人银行地址/Swift Code为必填
            if ($data['payment_method'] == Enums::PAYMENT_METHOD_BANK_TRANSFER && PayEnums::PAY_WHERE_OUT == $data['border_payment_type'] && (empty($item['bank_address']) || empty($item['swift_code']))) {
                throw new ValidationException('bank address or swift code param error', ErrCode::$VALIDATE_ERROR);
            }
            //vat\sst
            if (!in_array($item['vat_rate'], $vat_config)) {
                throw new ValidationException(self::$t->_('vat_sst_rate_error_hint', ['VAT_SST' => $vat_sst_name]), ErrCode::$VALIDATE_ERROR);
            }

            // 费用开始日期 不可 大于等于 结束日期
            if ($item['cost_start_date'] >= $item['cost_end_date']) {
                throw new ValidationException(self::$t['payment_upload_error_004'], ErrCode::$VALIDATE_ERROR);
            }

            // 提取WHT类别
            $_wht_info = $wht_config[$item['wht_category_id']] ?? [];
            if (empty($_wht_info)) {
                throw new ValidationException(self::$t['payment_upload_error_007'], ErrCode::$VALIDATE_ERROR);
            }
            // 提取WHT税率
            if (empty($_wht_info['rate_list'][$item['wht_tax_rate_id']])) {
                throw new ValidationException(self::$t['payment_upload_error_008'], ErrCode::$VALIDATE_ERROR);
            }

            //在支付类型不是支票的情况下需要判断银行名称、银行账户名称、银行账户号必填项
            //需求9888 1. 当【付款方式】=【支票】时，【银行名称】、【银行账户名称】、【银行账户号】，改为非必填
            if ($data["payment_method"] != Enums::PAYMENT_METHOD_CHECK) {
                if (strlen($item["bank_name"]) <= 0) {
                    throw new ValidationException(self::$t['payment_upload_error_009'], ErrCode::$VALIDATE_ERROR);
                } else if (strlen($item["bank_account_name"]) <= 0) {
                    throw new ValidationException(self::$t['payment_upload_error_010'], ErrCode::$VALIDATE_ERROR);
                } else if (strlen($item['bank_account_no']) <= 0) {
                    throw new ValidationException(self::$t['payment_upload_error_011'], ErrCode::$VALIDATE_ERROR);
                }
            }

            // 菲律宾国家的话房东税务号必填
            if (get_country_code() == GlobalEnums::PH_COUNTRY_CODE && empty($item['landlord_tax_no'])) {
                throw new ValidationException(self::$t['payment_upload_error_018'], ErrCode::$VALIDATE_ERROR);
            }

            //19334需求超过允差值需要拦截提交
            if ($store_renting_finance_tax_allowance) {
                //填写的WHT金额和系统自动计算的WHT金额差异较大，请检查！ |WHT金额-（系统自动计算：不含税金额*WHT税率）|的绝对值 > 配置的WHT允差
                $set_wht_val = $store_renting_finance_tax_allowance['wht'] ?? 0;
                $wht_diff = abs(bcsub(round($item['amount'] * $item['wht_tax_rate_id'] / 100, 2), $item['wht_amount'], 2));
                if ($set_wht_val && bccomp($wht_diff, $set_wht_val, 2) === 1) {
                    $finance_tax_allowance_wht_lines[] = ($key + 1);
                }

                //填写的VAT金额和系统自动计算的VAT金额差异较大，请检查！ |VAT金额-（系统自动计算：不含税金额*VAT税率）|的绝对值 > 配置的VAT允差
                $set_vat_val = $store_renting_finance_tax_allowance['vat'] ?? 0;
                $vat_diff = abs(bcsub(round($item['amount'] * $item['vat_rate'] / 100, 2), $item['vat_amount'], 2));
                if ($set_vat_val && bccomp($vat_diff, $set_vat_val, 2) === 1) {
                    $finance_tax_allowance_vat_lines[] = ($key + 1);
                }
            }

            // 如果合同存在, 则网点须为合同网点
            if ($item['is_contract']) {
                $contract_info = $archive_store_renting_contract[$item['contract_no']] ?? [];

                // 合同信息验证
                if (empty($contract_info)) {
                    throw new ValidationException(self::$t['payment_upload_error_000'], ErrCode::$VALIDATE_ERROR);
                }

                // 合同网点验证 V22269总部的不校验与合同中网点的一致性
                if ($contract_info['store_id'] != $item['store_id'] && $data['cost_store_type'] == Enums::PAYMENT_COST_STORE_TYPE_02) {
                    throw new ValidationException(self::$t['payment_upload_error_001'], ErrCode::$VALIDATE_ERROR);
                }

                // 校验费用日期 与 合同日期
                if ($item['cost_start_date'] < $contract_info['contract_begin_date'] || $item['cost_start_date'] > $contract_info['contract_end_date']) {
                    throw new ValidationException(self::$t['payment_upload_error_004'], ErrCode::$VALIDATE_ERROR);
                }

                if ($item['cost_end_date'] > $contract_info['contract_end_date'] || $item['cost_end_date'] < $contract_info['contract_begin_date']) {
                    throw new ValidationException(self::$t['payment_upload_error_005'], ErrCode::$VALIDATE_ERROR);
                }
                // 校验费用日期与作废日期
                if (isset($contract_no_replace[$item['contract_no']])) {
                    //一个合同可能有多个作废信息
                    foreach ($contract_no_replace[$item['contract_no']] as $replace_info) {
                        $transfer_params = ['contract_no' => $replace_info['invalid_replace_cno'], 'invalid_data' => $replace_info['invalid_replace_begin'] . ' - ' . $replace_info['invalid_replace_end']];
                        //开始时间或结束时间在禁用时间内
                        if ($item['cost_start_date'] >= $replace_info['invalid_replace_begin'] && $item['cost_start_date'] <= $replace_info['invalid_replace_end']) {
                            throw new ValidationException(self::$t->_('payment_upload_error_0060', $transfer_params), ErrCode::$VALIDATE_ERROR);
                        }
                        if ($item['cost_end_date'] >= $replace_info['invalid_replace_begin'] && $item['cost_end_date'] <= $replace_info['invalid_replace_end']) {
                            throw new ValidationException(self::$t->_('payment_upload_error_0060', $transfer_params), ErrCode::$VALIDATE_ERROR);
                        }
                        //开始时间大于禁用时间开始,结束时间大于禁用时间结束
                        if ($item['cost_start_date'] <= $replace_info['invalid_replace_begin'] && $item['cost_end_date'] >= $replace_info['invalid_replace_end']) {
                            throw new ValidationException(self::$t->_('payment_upload_error_0060', $transfer_params), ErrCode::$VALIDATE_ERROR);
                        }
                    }
                }
                // 选择的日期在同一个申请单中同一网点相同费用类型的日期也不可重复
                // 是否与本次提交的其他日期段有交叉: false: 有交叉，不可放行
                $item['key'] = $key;
                $compare_upload_cost_date_res = $this->compareUploadCostDate($item, $data['amount_detail']);
                if ($compare_upload_cost_date_res['result'] == false) {
                    $hint_key = $compare_upload_cost_date_res['code'] == 1 ? 'payment_upload_error_004' : 'payment_upload_error_005';
                    throw new ValidationException(self::$t[$hint_key], ErrCode::$VALIDATE_ERROR);
                }

                // 选择的日期不可在同一网点该费用类型已申请过付款的日期
                // 是否在空闲日期段内, false: 不在空闲日期段内
                if ($this->compareUploadCostDateAndExistStoreCostDate($item, $store_contract_available_date_item) == false) {
                    throw new ValidationException(self::$t['payment_upload_error_0050'], ErrCode::$VALIDATE_ERROR);
                }
                $attachment_config_key = $item['is_contract'] . '_' . $contract_info['is_main'] . '_' . $contract_info['house_owner_type'];

                $store_arr[] = $item['store_id'];
            } else {
                //关联合同 = 否
                $attachment_config_key = $item['is_contract'];
            }
            $required_file_fields = array_merge($required_file_fields, $attachment_config_list['attachment_validation'][$attachment_config_key]['required_file'] ?? []);

            // WHT类别
            $item['wht_category'] = $_wht_info['label'];
            // wht税率
            $item['wht_tax_rate'] = $item['wht_tax_rate_id'];
        }

        //19334需求超过允差值需要拦截提交
        if (!empty($finance_tax_allowance_wht_lines)) {
            throw new ValidationException(static::$t->_('payment_upload_error_0061', ['LINES' => implode('、', $finance_tax_allowance_wht_lines)]), ErrCode::$VALIDATE_ERROR);
        }
        if (!empty($finance_tax_allowance_vat_lines)) {
            throw new ValidationException(static::$t->_('payment_upload_error_0062', ['LINES' => implode('、', $finance_tax_allowance_vat_lines), 'VAT_SST' => $vat_sst_name]), ErrCode::$VALIDATE_ERROR);
        }
        
        //是否可以多网点同时提交【配置为否0就会被拦截，配置为是或者没有配置或设置的值空都不会被拦截】
        $multiple_branches_can_submit = EnumsService::getInstance()->getSettingEnvValue('payment_store_renting_multiple_stores_status');
        $multiple_branches_can_submit = $multiple_branches_can_submit !== '' ? intval($multiple_branches_can_submit) : '';
        if ($multiple_branches_can_submit === 0 && count(array_unique(array_filter(array_column($data['amount_detail'], 'store_id')))) > 1) {
            throw new ValidationException(self::$t['contract_store_multiple_cannot_submit'], ErrCode::$VALIDATE_ERROR);
        }

        //有合同的网点类型必须是一致的
        if (!empty($store_arr)) {
            $store_category_arr = (new StoreRepository())->getStoreListByIds(array_values(array_unique($store_arr)));
            if (count(array_unique(array_column($store_category_arr, 'category'))) > 1) {
                throw new ValidationException(self::$t['contract_store_category_mast_consistent'], ErrCode::$VALIDATE_ERROR);
            }
        }

        // 存在必填附件则需要拦截附件
        if (!empty($required_file_fields)) {
            $required_file_fields = array_unique($required_file_fields);
            Validation::validate($data, ['attachment_list' => 'Required|Obj']);

            // 验证必填的附件是否都有上传
            $submit_attachment_keys = [];
            $file_validate = [];
            foreach ($data['attachment_list'] as $file_field => $v) {
                if (!empty($v)) {
                    $submit_attachment_keys[] = $file_field;
                    $is_required = in_array($file_field, $required_file_fields) ? 1 : 0;
                    $one_attachment_info = $attachment_config_list['attachment_list'][$file_field] ?? [];
                    $file_validate[$file_field] = "Required|ArrLenGeLe:{$is_required},{$one_attachment_info['max_upload_num']}|>>>:params error[attachment_list.{$file_field}]";
                    $file_validate[$file_field . '[*].file_name'] = ($is_required ? 'Required|' : '') . "StrLenGeLe:{$is_required},200|>>>:params error[attachment_list.{$file_field}.file_name]";
                    $file_validate[$file_field . '[*].bucket_name'] = ($is_required ? 'Required|' : '') . "StrLenGeLe:{$is_required},60|>>>:params error[attachment_list.{$file_field}.bucket_name]";
                    $file_validate[$file_field . '[*].object_key'] = ($is_required ? 'Required|' : '') . "StrLenGeLe:{$is_required},100|>>>:params error[attachment_list.{$file_field}.object_key]";
                }
            }

            // 必填项未填写的拦截
            $loss_required_file_fields = array_diff($required_file_fields, $submit_attachment_keys);
            $file_name = '';
            foreach ($loss_required_file_fields as $filed) {
                $file_name .= $attachment_config_list['attachment_list'][$filed]['file_name_label'] .',';
            }
            if (!empty($file_name)) {
                throw new ValidationException(self::$t->_('payment_store_renting.attachment_loss', ['file_name' => trim($file_name, ',')]), ErrCode::$VALIDATE_ERROR);
            }

            // 附件信息验证拦截[数量等]
            Validation::validate($data['attachment_list'], $file_validate);
        }
        return $data;
    }

    /**
     * 网点租房付款 - 申请创建
     * @param array $data
     * @param array $user
     * @return array
     */
    public function add(array $data, array $user)
    {
        $code = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $item_error = [];
        try {
            $db = $this->getDI()->get('db_oa');
            $db->begin();

            // 该申请编号是否存在
            $exists = PaymentStoreRenting::getFirst([
                'conditions' => 'approval_status in(1,3) and apply_no = ?1',
                'columns' => 'id',
                'bind' => [1 => $data['apply_no']]
            ]);
            if (!empty($exists)) {
                throw new ValidationException('该编号已经存在，不能重复添加 = ' . $data['apply_no'], ErrCode::$VALIDATE_ERROR);
            }

            $total_amount = 0;
            $wht_total_amount = 0;
            $actually_total_amount = 0;
            $vat_total_amount = 0;//VAT金额总计
            $total_amount_has_tax = 0;//含税金额总计
            $deposit_amount_list = [];

            // 押金总金额
            $cost_type_ids = EnvModel::getEnvByCode('deposit_amount_cost_type_ids','');
            $cost_type_ids = !empty($cost_type_ids) ? explode(',',$cost_type_ids) : [];

            $amount_detail_item = [];
            //V22269 - 金额详情需存储费用一级部门ID
            $data['amount_detail'] = $this->getAmountDetailSysDepartmentId($data['amount_detail']);
            foreach ($data['amount_detail'] as $key => $item) {
                // 金额
                $item['amount'] = round($item['amount'], 2);
                // 计算WHT金额
                $item['wht_amount'] = round($item['wht_amount'], 2);
                // 计算金额总和
                $total_amount += $item['amount'];
                // 计算WHT金额总计
                $wht_total_amount += $item['wht_amount'];
                //计算vat 金额
                if ($item['vat_amount'] >= 10000000) {
                    throw new ValidationException('vat amount is overflow', ErrCode::$VALIDATE_ERROR);
                }
                $item['vat_amount'] = round($item['vat_amount'], 2);
                $item['amount_has_tax'] = round(($item['amount'] + $item['vat_amount']),2);
                // 计算实付金额  含税金额-wht金额
                $item['actually_amount'] = round($item['amount_has_tax'] - $item['wht_amount'], 2);
                $vat_total_amount +=$item['vat_amount'];
                $total_amount_has_tax +=$item['amount_has_tax'];
                // 计算实付金额总计
                $actually_total_amount += $item['actually_amount'];

                // 押金金额
                if (in_array($item['cost_type_id'],$cost_type_ids)) {
                    $deposit_amount_list[$item['contract_no']][] = $item['amount_has_tax'];
                }

                $amount_detail_item[] = [
                    'is_contract'                 => $item['is_contract'],
                    'contract_no'                 => $item['contract_no'],
                    'store_id'                    => $item['store_id'] ?? '',
                    'store_name'                  => $item['store_name'] ?? '',
                    'cost_type'                   => $item['cost_type_id'],
                    'due_date'                    => $item['due_date'],
                    'cost_start_date'             => $item['cost_start_date'],
                    'cost_end_date'               => $item['cost_end_date'],
                    'amount'                      => $item['amount'],
                    'wht_category'                => $item['wht_category'],
                    'wht_tax_rate'                => $item['wht_tax_rate'],
                    'wht_amount'                  => $item['wht_amount'],
                    'actually_amount'             => $item['actually_amount'],
                    'bank_name'                   => $item['bank_name'],
                    'bank_account_name'           => $item['bank_account_name'],
                    'bank_account_no'             => $item['bank_account_no'],
                    'contact_phone'               => $item['contact_phone'],
                    'contact_email'               => $item['contact_email'],
                    'remark'                      => $item['remark'],
                    'vat_rate'                    => $item['vat_rate'],
                    'vat_amount'                  => $item['vat_amount'],
                    'amount_has_tax'              => $item['amount_has_tax'],
                    'cost_center_code'            => $item['cost_center_code'],
                    'ledger_account_id'           => $item['ledger_account_id'],
                    'landlord_tax_no'             => $item['landlord_tax_no'] ?? '',
                    'bank_address'                => $item['bank_address'] ?? '',
                    'swift_code'                  => $item['swift_code'] ?? '',
                    'sap_supplier_no'             => $item['sap_supplier_no'] ?? '',
                    'certificate_desc'            => $item['certificate_desc'] ?? '',
                    'index_no'                    => $item['index'] ?? 0,
                    'cost_department_id'          => $item['cost_department_id'] ?? 0,
                    'cost_center_department_name' => $item['cost_center_department_name'] ?? '',
                    'cost_sys_department'         => $item['cost_sys_department'] ?? 0
                ];
            }

            // 押金详情金额是否超过
            if (!empty($deposit_amount_list)) {
                $contract_nos = array_keys($deposit_amount_list);
                // 含税金额总计
                $valid_deposit_amount = $this->getValidDepositAmount($contract_nos,$cost_type_ids);
                // 合同押金金额
                $contract_deposit_amount = $this->getContractDepositAmount($contract_nos);
                foreach ($deposit_amount_list as $cno => $amount_item) {
                    $sum = array_sum($amount_item);
                    $sum_deposit_amount = $valid_deposit_amount[$cno] ?? 0;

                    $sum_deposit_amount_total = number_format($sum_deposit_amount + $sum, 2, '.', '');
                    if (isset($contract_deposit_amount[$cno]) && bccomp($sum_deposit_amount_total, $contract_deposit_amount[$cno], 2) > 0) {
                        throw new ValidationException(self::$t['amount_with_tax_over_deposit_amount'], ErrCode::$VALIDATE_ERROR);
                    }
                }
            }

            // 金额详情列表校验有误
            if (empty($amount_detail_item)) {
                throw new ValidationException('amount details data is null', ErrCode::$VALIDATE_ERROR);
            }

            // [2] 构建网点租房付款创建数据
            $data['total_amount'] = round($total_amount, 2);
            $data['wht_total_amount'] = round($wht_total_amount, 2);
            $data['actually_total_amount'] = round($actually_total_amount, 2);
            $data['vat_total_amount'] = round($vat_total_amount, 2);
            $data['tax_total_amount'] = round($total_amount_has_tax,2);

            $apply_data = $this->buildData($data, $user);

            // [3] 入库
            // [3.1] 主表
            $store_renting_model = new PaymentStoreRenting();
            $bool = $store_renting_model->i_create($apply_data);
            if ($bool === false) {
                throw new BusinessException('网点租房付款申请创建失败 = ' . json_encode($apply_data,JSON_UNESCAPED_UNICODE), ErrCode::$CONTRACT_CREATE_ERROR);
            }

            // [3.2] 金额详情
            $remind_data_all = [];
            foreach ($amount_detail_item as $item_detail) {
                $item_detail['store_renting_id'] = $store_renting_model->id;
                $item_detail['create_id'] = $user['id'];
                $item_detail['last_edit_id'] = $user['id'];
                $item_detail['updated_at'] = $item_detail['created_at'] = date('Y-m-d H:i:s');

                $detail_model = new PaymentStoreRentingDetail();
                $bool = $detail_model->i_create($item_detail);
                if ($bool === false) {
                    throw new BusinessException('网点租房付款申请-金额详情创建失败 = ' . json_encode($item_detail,JSON_UNESCAPED_UNICODE), ErrCode::$CONTRACT_CREATE_ERROR);
                }
                //需求15258 : 新增时把[网点合同签署提醒]相关待处理任务置为已处理
                if (get_country_code() == GlobalEnums::PH_COUNTRY_CODE) {
                    if ($item_detail['is_contract'] && $item_detail['cost_type'] == ContractEnums::COST_TYPE_CONTRACT_DEPOSIT) {
                        //查询此网点的所有-定金支付提醒的待处理数据
                        $remind_data = ContractStoreRentingRemindModel::find([
                            'columns' => 'id',
                            'conditions' => 'store_id = :store_id: and task_type = :task_type: and task_status = :task_status:',
                            'bind' => [
                                'store_id' => $item_detail['store_id'],
                                'task_type' => ContractEnums::TASK_TYPE_DEPOSIT_PAY,
                                'task_status' => ContractEnums::TASK_STATUS_TODO
                            ]
                        ])->toArray();
                        if (!empty($remind_data)) {
                            $remind_data_all = array_merge($remind_data, $remind_data_all);
                        }
                    }
                }
            }
            //需求15258 : 新增时把[网点合同签署提醒]相关待处理任务置为已处理
            if (!empty($remind_data_all)) {
                $update = [
                    'task_status' => ContractEnums::TASK_STATUS_DONE,
                    'updated_at' => date('Y-m-d H:i:s'),
                    'finished_at' => date('Y-m-d H:i:s'),
                ];
                //批量更新
                $ids = array_column($remind_data_all, 'id');
                $ids = implode(',', array_values($ids));
                $update_result = $this->getDI()->get('db_oa')->updateAsDict(
                    (new ContractStoreRentingRemindModel())->getSource(),
                    $update,
                    ['conditions' => "id IN ($ids) and task_status = ?", 'bind' => [ContractEnums::TASK_STATUS_TODO]]
                );
                if (!$update_result) {
                    $this->logger->warning('网点合同签署提醒-定金提醒-更新状态失败, ids=' . $ids . ' ; store_renting_id=' . $store_renting_model->id);
                    throw new BusinessException('网点合同签署提醒-定金提醒-更新状态失败', ErrCode::$BUSINESS_ERROR);
                }
            }

            // [3.3] 附件
            $attachArr = [];
            // 上传附件（BIR 2303）等
            if(!empty($data['event_file'])) {
                foreach ($data['event_file'] as $k => $file){
                    $tmp = [];
                    $tmp['oss_bucket_type'] =  Enums::OSS_BUCKET_TYPE_STORE_RENTING_PAYMENT;
                    $tmp['oss_bucket_key'] =  $store_renting_model->id;
                    $tmp['sub_type'] = 0;
                    $tmp['bucket_name'] = $file['bucket_name'];
                    $tmp['object_key'] = $file['object_key'];
                    $tmp['file_name'] = $file['file_name'];
                    $attachArr[] = $tmp;
                }
            }
            // 租房付款附件栏附件
            $attachment_config_list = $this->getAttachmentAllConfig();
            if (!empty($attachment_config_list['attachment_list']) && !empty($data['attachment_list'])) {
                foreach ($data['attachment_list'] as $file_field => $item) {
                    $one_config_attachment_info = $attachment_config_list['attachment_list'][$file_field] ?? [];
                    foreach ($item as $file) {
                        $attachArr[] = [
                            'oss_bucket_type' => Enums::OSS_BUCKET_TYPE_STORE_RENTING_PAYMENT,
                            'oss_bucket_key' => $store_renting_model->id,
                            'sub_type' => $one_config_attachment_info['file_sub_type'] ?? 0,
                            'bucket_name' => $file['bucket_name'],
                            'object_key' => $file['object_key'],
                            'file_name' => $file['file_name']
                        ];
                    }
                }
            }
            if (!empty($attachArr)) {
                $attach = new AttachModel();
                $attach_bool = $attach->batchInsert($attachArr);
                if ($attach_bool === false) {
                    throw new BusinessException('网点租房付款申请 - 附件创建失败 = ' . json_encode($attachArr,JSON_UNESCAPED_UNICODE), ErrCode::$CONTRACT_CREATE_ERROR);
                }
            }
            $info = ['data' => $data];
            // [3.4] 注入审批流
            $flow_bool = (new StoreRentingPaymentFlowService())->createRequest($store_renting_model->id, $user, $info);
            if ($flow_bool === false) {
                throw new BusinessException(static::$t->_('payment_store_renting_create_work_flow_failed'), ErrCode::$STORE_RENTING_PAYMENT_CREATE_WORK_FLOW_ERROR);
            }

            $db->commit();
        } catch (ValidationException $e) {
            // 校验错误，可对外抛出
            $code = $e->getCode();
            $message = $e->getMessage();

        } catch (BusinessException $e) {
            // 业务错误（数据错误等），不可对外抛出
            $code = $e->getCode();
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage();

        } catch (Exception $e) {
            // 系统错误，不可对外抛出
            $code = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        }

        if (!empty($message)) {
            $db->rollback();
        }

        if (!empty($real_message)) {
            $this->logger->warning('payment-store-renting-create-failed:' . $real_message);
        }

        return [
            'code' => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data' => [
                'item_error' => $item_error
            ]
        ];
    }

    /**
     * 组装网点租房付款的申请入库数据
     * @param array $contract_nos
     * @return array
     */
    private function getValidDepositAmount($contract_nos = [],$cost_type_ids = []){
        if (empty($contract_nos)) {
            return [];
        }

        $builder    = $this->modelsManager->createBuilder();
        $column_str = 'distinct pd.contract_no,sum(pd.amount_has_tax) amount';
        $builder->columns($column_str);
        $builder->from(['pd' => PaymentStoreRentingDetail::class]);
        $builder->leftjoin(PaymentStoreRenting::class, 'pd.store_renting_id=p.id', 'p');
        $builder->andWhere('pd.cost_type in({cost_type:array})',['cost_type' => $cost_type_ids]);
        $builder->inWhere('pd.contract_no',$contract_nos);
        $builder->inWhere('p.approval_status',[Enums::CONTRACT_STATUS_PENDING,Enums::CONTRACT_STATUS_APPROVAL]);
        $builder->inWhere('p.pay_status',[Enums::LOAN_PAY_STATUS_PENDING,Enums::LOAN_PAY_STATUS_PAY]);
        $builder->groupBy('pd.contract_no');
        $deposit_amount_list = $builder->getQuery()->execute()->toArray();

        return !empty($deposit_amount_list) ? array_column($deposit_amount_list,'amount','contract_no') : [];
    }

    /**
     * 组装网点租房付款的申请入库数据
     * @param array $contract_nos
     * @return array
     */
    private function getContractDepositAmount($contract_nos = []){
        if (empty($contract_nos)) {
            return [];
        }

        $builder    = $this->modelsManager->createBuilder();
        $column_str = 'distinct c.contract_id,c.deposit_amount';
        $builder->columns($column_str);
        $builder->from(['c' => ContractStoreRentingModel::class]);
        $builder->inWhere('c.contract_id',$contract_nos);
        $deposit_amount_list = $builder->getQuery()->execute()->toArray();

        return !empty($deposit_amount_list) ? array_column($deposit_amount_list,'deposit_amount','contract_id') : [];
    }

    /**
     * 组装网点租房付款的申请入库数据
     * @param array $data
     * @param array $user
     * @return array
     */
    private function buildData(array $data, array $user)
    {
        if (empty($data)) {
            return [];
        }

        $apply_data = [];

        $apply_data['apply_no'] = $data['apply_no'] ?? '';
        $apply_data['total_amount'] = $data['total_amount'] ?? 0;
        $apply_data['wht_total_amount'] = $data['wht_total_amount'] ?? 0;
        $apply_data['actually_total_amount'] = $data['actually_total_amount'] ?? 0;
        $apply_data['payment_method'] = $data['payment_method'] ?? 0;
        $apply_data['cost_store_type'] = $data['cost_store_type'] ?? 0;
        $apply_data['remark'] = $data['remark'] ?? '';
        $apply_data['create_date'] = date('Y-m-d');
        $apply_data['created_at'] = date('Y-m-d H:i:s');
        $apply_data['updated_at'] = $apply_data['created_at'];
        $apply_data['create_id'] = $user['id'] ?? 0;
        $apply_data['create_name'] = $user['name'];
        $apply_data['create_nickname'] = $user['nick_name'];
        $apply_data['source_type'] = $data['source_type'] ?? 1;//来源方式1:手动新增,2:Excel导入创建

        // 获取币种与系统默认币种的汇率
        $apply_data['currency'] = $data['currency'];
        $exchange_rate = EnumsService::getInstance()->getCurrencyExchangeRate($data['currency']);
        $apply_data['exchange_rate'] = $exchange_rate ? $exchange_rate : 1;

        // 获取用户基本信息
        $user_base_info = $this->getUserMetaFromBi($user['id']);
        $apply_data['create_department_id'] = $user_base_info['create_department_id'];
        $apply_data['create_department_name'] = $user_base_info['create_department_name'];
        $apply_data['create_node_department_id'] = $user_base_info['create_node_department_id'];
        $apply_data['create_node_department_name'] = $user_base_info['create_node_department_name'];
        $apply_data['create_company_id'] = $user_base_info['create_company_id'];
        $apply_data['create_company_name'] = $user_base_info['create_company_name'];
        $apply_data['create_job_title_id'] = $user_base_info['create_job_title_id'];
        $apply_data['create_job_title_name'] = $user_base_info['create_job_title_name'];
        $apply_data['create_job_title_level'] = $user_base_info['create_job_title_level'];
        $apply_data['cost_center_id'] = $user_base_info['create_center_id'];
        $apply_data['cost_center_name'] = $user_base_info['create_center_name'];
        $apply_data['cost_center_department_name'] = $user_base_info['create_center_department_name'];
        $apply_data['create_email'] = $user_base_info['create_email'] ?? '';


        $apply_data['last_update_id'] = $user['id'];
        $apply_data['last_update_name'] = $this->getNameAndNickName($user['name'] ?? '', $user['nick_name']);
        $apply_data['last_update_department'] = $apply_data['create_node_department_name'];
        $apply_data['last_update_job_title'] = $user_base_info['create_job_title_name'];
        $apply_data['last_update_at'] = $apply_data['updated_at'];
        $apply_data['ver'] = $data['ver'];//修复线上问题，之前有该字短的存储，被后期删除掉了故而修复
        $apply_data['tax_total_amount'] = $data['tax_total_amount'];
        $apply_data['vat_total_amount'] = $data['vat_total_amount'];
        $apply_data['cost_department_id'] = $data['cost_department_id'];
        $apply_data['cost_company_id'] = $data['cost_company_id'];
        $apply_data['border_payment_type'] = !empty($data['border_payment_type']) ? $data['border_payment_type'] : 0;

        return $apply_data;
    }

    /**
     * 获取申请创建页 - 基本信息 - 默认数据
     * @param array $user
     * @return array
     */
    public function getCreatePageBaseInfoDefaultData(array $user)
    {
        $code = ErrCode::$SUCCESS;
        $message = 'success';
        $real_message = '';

        $data = [];

        try {
            if (empty($user)) {
                throw new ValidationException("The employee information is null [{$user['id']}]", ErrCode::$VALIDATE_ERROR);
            }

            // 提交申请的员工基本信息
            $user_base_info = $this->getUserMetaFromBi($user['id']);
            if (empty($user_base_info)) {
                throw new ValidationException(static::$t->_("re_staff_info_id_not_exist"), ErrCode::$VALIDATE_ERROR);
            }

            $data['apply_no'] = static::genSerialNo(Enums::PAYMENT_STORE_RENTING_APPLY_NO_PREFIX,RedisKey::PAYMENT_STORE_RENTING_APPLY_COUNTER);
            $data['create_id'] = $user['id'];
            $data['create_name'] = $this->getNameAndNickName($user['name'], $user['nick_name']);
            $data['create_department_name'] = $user_base_info['create_display_department_name'];
            $data['create_company_name'] = $user_base_info['create_display_company_name'];
            $data['create_email'] = $user_base_info['create_email'];
            // 费用所属部门/中心: 本期获取申请人的所属部门/中心
            $data['cost_center_name'] = $user_base_info['create_center_name'];
            $data['cost_department_name'] = $user_base_info['create_node_department_name'];
            $data['cost_department_id'] = $user_base_info['create_node_department_id'];
            // 费用所属公司
            $data['cost_company_id'] = '';
            $data['cost_company_name'] = '';
            $dept_info = DepartmentModel::findFirst($data['cost_department_id']);
            if (!empty($dept_info) && !empty($dept_info->company_id)) {
                $coo_company_list = (new PurchaseService())->getCooCostCompany();
                if (!empty($coo_company_list)) {
                    foreach ($coo_company_list as $company) {
                        if ($company['cost_company_id'] == $dept_info->company_id) {
                            $data['cost_company_id'] = $company['cost_company_id'];
                            $data['cost_company_name'] = $company['cost_company_name'];
                            break;
                        }
                    }
                }
            }

        } catch (ValidationException $e) {
            $code = $e->getCode();
            $message = $e->getMessage();

        } catch (Exception $e) {
            $code = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage();

            $data = [];
        }

        if (!empty($real_message)) {
            $logger = $this->getDI()->get('logger');
            $logger->warning('付款管理 - 网点租房付款申请创建页 - 基本信息 - 默认值获取异常: ' . $real_message);
        }

        return [
            'code' => $code,
            'message' => $message,
            'data' => $data
        ];
    }

    /**
     * 获取申请创建页 - 下拉选项 - 默认数据
     * @param array $user
     * @return array
     */
    public function getCreatePageOptionsData(array $user)
    {
        $code = ErrCode::$SUCCESS;
        $message = $real_message = '';

        $data = [];

        try {
            if (empty($user)) {
                throw new Exception("not found user");
            }

            // 费用所属网点列表
            $data['cost_store_type_item'] = $this->getCostStoreTypeItem();

            // 付款方式列表
            $data['payment_method_item'] = $this->getPaymentMethodItem();

            // 币种
            $data['payment_currency_item'] = $this->getPaymentCurrencyItem();

            // 是否有合同
            $data['contract_status_item'] = $this->getContractStatusItem();

            // 费用类型
            $data['cost_type_item'] = $this->getCostTypeItem();

            // 核算科目
            $data['ledger_item'] = $this->getLedgerItem();

            // WHT 类别及对应WHT税率
            $data['wht_category'] = EnumsService::getInstance()->getFormatWhtRateConfig();
            $data['vat7_rate_list']  = EnumsService::getInstance()->getFormatVatRateConfig();

            // 付款银行和账号
            $data['payee_bank_list'] = json_decode(EnvModel::getEnvByCode('reimbursement_bank'),true);

            // 费用所属部门
            $umodel = (new UserService())->getUserByIdInRbi($user['id']);
            if (empty($umodel)) {
                throw new ValidationException(static::$t->_("re_staff_info_id_not_exist"), ErrCode::$VALIDATE_ERROR);
            }
            $user['department_id'] = empty($umodel->node_department_id) ? $umodel->sys_department_id : $umodel->node_department_id;

            $department_all_config   = json_decode(EnvModel::getEnvByCode('payment_store_renting_cost_department_all_config'), true) ?? [];
            $user_department_list    = $this->getMyDeptList($user) ?? [];
            $data['department_list'] = array_unique(array_merge($user_department_list, $department_all_config), SORT_REGULAR);

            // 费用所属公司
            $data['cost_company_list'] =  (new PurchaseService())->getCooCostCompany();

            // 境内境外
            $data['border_type_list'] = $this->getBorderTypeList();

            // 租房付款附件
            $data['attachment_list'] = $this->getAttachmentAllConfig();

            //租房付款可拆分费用部门工号，支持配置多个工号
            $data['payment_store_renting_split_department_staff_ids'] = EnumsService::getInstance()->getSettingEnvValueIds('payment_store_renting_split_department_staff_ids');
        } catch (ValidationException $e) {
            //校验错误，可对外抛出
            $code = $e->getCode();
            $message = $e->getMessage();
        }  catch (Exception $e) {
            $code = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage();

            $data = [];
        }

        if (!empty($real_message)) {
            $this->logger->warning('付款管理 - 网点租房付款申请创建页 - 下拉选项 获取异常: ' . $real_message);
        }

        return [
            'code' => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data' => $data
        ];
    }

    /**
     * 获取申请创建页 - 合同列表
     * @param array $params
     * @param array $user
     * @return array
     */
    public function getContractList(array $params, array $user)
    {
        $code = ErrCode::$SUCCESS;
        $message = $real_message = '';

        $data = [];

        try {
            if (empty($user)) {
                throw new Exception("not found user");
            }

            $country_code = get_country_code();
            $data = $this->getArchiveStoreRentingContract($params['contract_no']);
            if (!empty($data)) {
                // 获取网点名称
                $sys_store = $this->getSysStoreList([], [], true);
                $sys_store = array_column($sys_store, 'name', 'id');
                $contract_ids = array_values(array_column($data,'id'));
                $areaList = $this->getAreaInfoList($contract_ids);
                $contract_amount_detail = $this->getStoreRentingDetail($contract_ids);
                foreach ($data as $key => $value) {
                    $data[$key]['contract_tax_no'] = 'PH' == $country_code ? $value['contract_tax_no'] : ''; // 菲律宾默认房屋信息的房东税号TIN
                    $data[$key]['store_name'] = $sys_store[$value['store_id']] ?? '';
                    $data[$key]['area_list'] = isset($areaList[$value['id']]) ? $areaList[$value['id']] : [];
                    $data[$key]['amount_detail'] = isset($contract_amount_detail[$value['id']]) ? $contract_amount_detail[$value['id']] : [];
                }

                $data = $data ? $data : [];
            }

        } catch (Exception $e) {
            $code = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        }

        if (!empty($real_message)) {
            $logger = $this->getDI()->get('logger');
            $logger->warning('付款管理 - 网点租房付款申请创建页 - 合同搜索列表 获取异常: ' . $real_message);
        }

        return [
            'code' => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data' => $data
        ];
    }

    /**
     * 获取申请创建页 - 费用所属中心列表
     * @param array $params
     * @param array $user
     * @return array
     */
    public function getCostCenterList(array $params, array $user)
    {
        $code = ErrCode::$SUCCESS;
        $message = $real_message = '';

        $data = [];

        try {
            if (empty($user)) {
                throw new Exception("not found user");
            }

            $data = $this->getAllCostCenterList($params);
        } catch (Exception $e) {
            $code = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        }

        if (!empty($real_message)) {
            $logger = $this->getDI()->get('logger');
            $logger->warning('付款管理 - 网点租房付款申请创建页 - 费用所属中心搜索列表 获取异常: ' . $real_message);
        }

        return [
            'code' => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data' => $data
        ];
    }

    public function getStoreRentingDetail($contractIds = [])
    {
        $data = ContractStoreRentingDetailModel::find([
            'conditions' => 'contract_store_renting_id in({contract_id:array})',
            'bind'       => [
                'contract_id' => $contractIds,
            ],
        ])->toArray();

        $result = [];
        foreach ($data as $item) {
            $result[$item['contract_store_renting_id']][] = $item;
        }
        return $result;
    }

    /**
     * 获取申请创建页 - 系统网点列表
     * @param array $params
     * @param array $user
     * @return array
     */
    public function getStoreList(array $params, array $user)
    {
        $code = ErrCode::$SUCCESS;
        $message = $real_message = '';

        $data = [];

        try {
            if (empty($user)) {
                throw new Exception("not found user");
            }

            // 获取网点
            $is_append_header_office = false;
            if(isset($params['is_append_header_office'])&&$params['is_append_header_office']==1){
                $is_append_header_office = true;
            }
            $data = $this->getSysStoreList($params, $user, $is_append_header_office);

        } catch (Exception $e) {
            $code = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        }

        if (!empty($real_message)) {
            $logger = $this->getDI()->get('logger');
            $logger->warning('付款管理 - 网点租房付款申请创建页 - 系统网点列表 获取异常: ' . $real_message);
        }

        return [
            'code' => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data' => $data
        ];
    }

    /**
     * 获取申请创建页 - 系统网点列表
     * @param array $contractIds
     * @return array
     */
    public function getAreaInfoList($contractIds){
        $contractInfoList = ContractStoreRentingArea::find([
            'conditions' => 'contract_store_renting_id in ({ids:array})',
            'bind'       => [
                'ids' => $contractIds
            ]
        ])->toArray();

        $result_list = [];
        if(!empty($contractInfoList)){
            $result_list = [];
            foreach ($contractInfoList as $k => $area){
                if (empty($area['area_service_amount_no_tax'])) {
                    continue;
                }
                $area['area_start_time'] = empty($area['start_time']) ? '' : date('Y-m-d',strtotime($area['start_time']));
                $area['area_end_time'] = empty($area['end_time']) ? '' : date('Y-m-d',strtotime($area['end_time']));
                $result_list[$area['contract_store_renting_id']][] = $area;
            }
        }

        return $result_list;
    }

    /**
     * 获取 金额详情上传模板
     * @return bool
     */
    public function getAmountDetailUploadTemplate()
    {
        $code = ErrCode::$SUCCESS;
        $message = 'success';

        try {
            $template_key = Enums::PAYMENT_STORE_RENTING_DETAIL_UPLOAD_TEMPLATE_KEY . self::$language;
            $template_url = EnvModel::getEnvByCode($template_key, 'val');
        } catch (Exception $e) {
            $code = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $template_url = '';

            $logger = $this->getDI()->get('logger');
            $logger->warning('付款管理 - 网点租房付款申请 - 金额详情上传模板下载异常: ' . $e->getMessage());
        }

        return [
            'code' => $code,
            'message' => $message,
            'data' => $template_url
        ];
    }

    /**
     * 金额详情批量上传
     * @param array $excel_data
     * @param array $param
     * @param array $user 当前登录着信息组
     * @return array
     */
    public function amountDetailBatchUpload(array $excel_data, array $param, array $user)
    {
        $code = ErrCode::$SUCCESS;
        $message = 'success';
        $data = [];

        try {
            if (empty($excel_data)) {
                throw new ValidationException(static::$t->_('excel_file_empty'), ErrCode::$VALIDATE_ERROR);
            }

            // 校验导入的总数量
            if (count($excel_data) > 700) {
                throw new ValidationException('Excel Data exceeds maximum limit[700]', ErrCode::$VALIDATE_ERROR);
            }

            //V22269 excel第二列 - 区分总部-部门名称、网点-网点名称
            $staff_can_select_department_list = $sys_store_list = [];
            $cost_store_type = $param['cost_store_type'];
            if ($cost_store_type == Enums::PAYMENT_COST_STORE_TYPE_01) {
                $staff_can_select_department_list = $this->getStaffCanSelectDepartmentList($user, $param['cost_department_id']);
            } elseif ($cost_store_type == Enums::PAYMENT_COST_STORE_TYPE_02) {
                // 获取系统网点
                $sys_store_list = $this->getSysStoreList([], [], true);
                $sys_store_list = array_column($sys_store_list, 'name', 'id');
            }

            // 获取已归档的网点租房合同基本信息
            $archive_store_renting_contract = $this->getArchiveStoreRentingContract();
            $archive_store_renting_contract = array_column($archive_store_renting_contract, null, 'contract_no');

            // 费用类型
            $cost_type_item = $this->getCostItem();

            // WHT类别
            $wht_category_item = EnumsService::getInstance()->getWhtRateCategoryMap(1, true);

            // WHT类别对应的WHT税率
            $wht_tax_rate_item = EnumsService::getInstance()->getWhtRateMap();

            // vat 税率
            $vat_rate_item      = EnumsService::getInstance()->getVatRateValueItem();

            // 获取已申请的网点合同可用日期列表
            $condition = [
                'approval_status' => [1, 3],
                'is_contract' => 1
            ];
            $store_contract_available_date_item = $this->getAllStoreApplyCostByConditions($condition);
            $store_contract_available_date_item = $this->extractAvailableStoreContractDate($store_contract_available_date_item, $archive_store_renting_contract);
            $amount_detail_data = [];
            $total_amount = 0;
            $wht_total_amount = 0;
            $actually_total_amount = 0;
            $upload_total = 0;
            $vat_total_amount = 0;
            $total_amount_has_tax =0;

            // 提取待校验的列表数据 提前批量做格式转换,
            $upload_contract_date_item = [];

            foreach ($excel_data as $_tmp_key => $_tmp_item) {
                // 空白行: 跳过
                $_tmp_item = trim_array(array_slice($_tmp_item,0, 15));
                if (empty(array_filter($_tmp_item))) {
                    continue;
                }

                // 费用发生开始日期
                $_tmp_item[4] = $this->handleUploadFileDate($_tmp_item[4]);
                if (!preg_match('/^\d{4}-\d{2}-\d{2}$/', $_tmp_item[4])) {
                    continue;
                }

                // 费用发生结束日期
                $_tmp_item[5] = $this->handleUploadFileDate($_tmp_item[5]);
                if (!preg_match('/^\d{4}-\d{2}-\d{2}$/', $_tmp_item[5])) {
                    continue;
                }

                // 合同信息取值
                $contract_info = $archive_store_renting_contract[$_tmp_item[0]] ?? [];
                if (empty($contract_info)) {
                    continue;
                }

                //银行信息取值
                $bank_info=  json_decode($contract_info['bank_collection'],true);
                if (empty($bank_info)) {
                    continue;
                }

                //V22269 区分总部、网点
                $contract_store_name = '';
                if ($cost_store_type == Enums::PAYMENT_COST_STORE_TYPE_01) {
                    //总部时做此判断
                    if (empty($_tmp_item[1]) || empty($staff_can_select_department_list[$_tmp_item[1]])) {
                        continue;
                    }
                } elseif ($cost_store_type == Enums::PAYMENT_COST_STORE_TYPE_02) {
                    //网点时做此判断
                    $contract_store_name = $sys_store_list[$contract_info['store_id']] ?? '';
                    if (empty($_tmp_item[1]) || (strtolower($_tmp_item[1]) != strtolower($contract_store_name))) {
                        continue;
                    }
                }

                // 费用类型验证
                $upload_contract_date_item[$_tmp_key] = [
                    'store_id' => $contract_info['store_id'] ?? '',
                    'store_name' => $contract_store_name,
                    'contract_no' => $_tmp_item[0],
                    'cost_type_id' => $cost_type_item[$_tmp_item[2]] ?? '',
                    'cost_start_date' => $_tmp_item[4],
                    'cost_end_date' => $_tmp_item[5],
                ];
            }
            // 查询被作废的时间段
            $contract_nos = array_values(array_unique(array_column($upload_contract_date_item, 'contract_no')));
            $contract_no_replace = [];
            if (!empty($contract_nos)) {
                $contract_no_arr = ContractArchive::find([
                    'columns' => 'cno, invalid_replace_cno, invalid_replace_begin, invalid_replace_end',
                    'conditions' => 'invalid_replace_cno in ({invalid_replace_cno:array}) and status in ({status:array}) and invalid_reason_type = :invalid_reason_type:',
                    'bind' => [
                        'invalid_replace_cno' => $contract_nos,
                        'status' => [ContractEnums::CONTRACT_ARCHIVE_STATUS_INVALID, ContractEnums::CONTRACT_ARCHIVE_STATUS_INVALID_ING],
                        'invalid_reason_type' => ContractEnums::INVALID_REASON_TYPE_RE_SIGN
                    ],
                ])->toArray();
                foreach ($contract_no_arr as $contract_replace) {
                    $contract_no_replace[$contract_replace['invalid_replace_cno']][] = $contract_replace;
                }
            }

            $is_bank = $is_sap_supplier = true;
            foreach ($excel_data as $key => $item) {
                // 合同信息取值
                $contract_info_ = isset($item[0]) ? $archive_store_renting_contract[$item[0]] ?? [] : [];

                if (!empty($contract_info_) && !empty($contract_info_['bank_collection'])) {
                    $bank_info_=  json_decode($contract_info_['bank_collection'],true);
                }
                // 空白行: 跳过
                $item = trim_array(array_slice($item,0, 24));
                if (empty(array_filter($item))) {
                    continue;
                }

                $upload_total++;

                // 待转换格式字段
                // WHT类别税率(兼容含有%字符串的问题)
                if (!empty($item[11]) && strpos($item[11],'%')) {
                    $item[11] = str_replace('%','',$item[11]);
                    $item[11] = is_numeric($item[11]) ? (float) sprintf("%.6f", $item[11] / 100) : 0;
                }

                $wht_rate = is_numeric($item[11]) ? (float) sprintf("%.3f", $item[11] * 100) : '';
                $wht_rate = (string) $wht_rate;
                $item[11] = $wht_rate . '%';

                // VAT类别税率(兼容含有%字符串的问题)
                if (!empty($item[7]) && strpos($item[7],'%')) {
                    $item[7] = str_replace('%','',$item[7]);
                    $item[7] = is_numeric($item[7]) ? (float) sprintf("%.6f", $item[7] / 100) : 0;
                }

                $vat_rate = is_numeric($item[7]) ? (float) sprintf("%.3f", $item[7] * 100) : '';
                $vat_rate = (string) $vat_rate;
                $item[7] = $vat_rate.'%';

                // 应付日期
                $due_date = $item[3] = $this->handleUploadFileDate($item[3]);

                // 费用发生开始日期
                $cost_start_date = $item[4] = $this->handleUploadFileDate($item[4]);

                // 费用发生结束日期
                $cost_end_date = $item[5] = $this->handleUploadFileDate($item[5]);
                $rentingInfo = $this->rentingInfo($item[0]);
                if (isset($rentingInfo['is_main']) && $rentingInfo['is_main'] != 3) {
                    if (is_array($bank_info_)) {
                        foreach ($bank_info_ as $bank) {
                            $bank['bank_name']         = $bank['bank_name_1'] ?? $bank['bank_name'];
                            $bank['bank_account_name'] = $bank['bank_account_title_1'] ?? $bank['bank_account_name'];
                            $bank['bank_account']      = $bank['bank_book_no_1'] ?? $bank['bank_account'];

                            $old_str = trim($bank['bank_name']) . trim($bank['bank_account_name']) . trim($bank['bank_account']);
                            $new_str = trim($item[14]) . trim($item[15]) . trim($item[16]);
                            if ($old_str == $new_str) {
                                $is_bank = false;
                            }
                            // sap供应商编号
                            $bank['sap_supplier_no'] = $bank['sap_supplier_no'] ?? '';
                            if (isset($bank['sap_supplier_no']) && trim($item[19] ?? '') == $bank['sap_supplier_no']) {
                                $is_sap_supplier = false;
                            }
                        }
                        if ($is_bank) {
                            // 银行名称账户名称银行账户有误
                            $item[] = self::$t['payment_upload_error'];
                            $item[] = self::$t['payment_upload_error_021'];
                            $excel_data[$key] = $item;
                            continue;
                        }
                        if ($is_sap_supplier) {
                            // 银行名称账户名称银行账户有误
                            $item[] = self::$t['payment_upload_error'];
                            $item[] = self::$t['payment_upload_error_031'];
                            $excel_data[$key] = $item;
                            continue;
                        }
                    }
                }
                // 日期类型字段格式验证
                // 应付日期
                if (!preg_match('/^\d{4}-\d{2}-\d{2}$/', $due_date)) {
                    // 应付日期错误
                    $item[] = self::$t['payment_upload_error'];
                    $item[] = self::$t['payment_upload_error_003'];

                    $excel_data[$key] = $item;
                    continue;
                }

                // 费用发生开始日期
                if (!preg_match('/^\d{4}-\d{2}-\d{2}$/', $cost_start_date)) {
                    // 费用开始日期错误，请按照规则填写
                    $item[] = self::$t['payment_upload_error'];
                    $item[] = self::$t['payment_upload_error_004'];

                    $excel_data[$key] = $item;
                    continue;
                }

                // 费用发生结束日期
                if (!preg_match('/^\d{4}-\d{2}-\d{2}$/', $cost_end_date)) {
                    // 费用结束日期错误，请按照规则填写
                    $item[] = self::$t['payment_upload_error'];
                    $item[] = self::$t['payment_upload_error_005'];

                    $excel_data[$key] = $item;
                    continue;
                }

                // 合同编号: 必填
                $contract_no = $item[0];

                if (empty($contract_no)) {
                    // 合同错误
                    $item[] = self::$t['payment_upload_error'];
                    $item[] = self::$t['payment_upload_error_000'];

                    $excel_data[$key] = $item;
                    continue;
                }

                // 合同信息
                $contract_info = $archive_store_renting_contract[$contract_no] ?? [];

                if (empty($contract_info)) {
                    // 合同错误
                    $item[] = self::$t['payment_upload_error'];
                    $item[] = self::$t['payment_upload_error_000'];

                    $excel_data[$key] = $item;
                    continue;
                }

                //V22269 区分总部、网点
                $store_id = '';
                if ($cost_store_type == Enums::PAYMENT_COST_STORE_TYPE_01) {
                    $one_department_info = $staff_can_select_department_list[$item[1]] ?? [];
                    if (empty($item[1]) || empty($one_department_info)) {
                        // 网点名称错误
                        $item[] = self::$t['payment_upload_error'];
                        $item[] = self::$t['payment_upload_error_049'];

                        $excel_data[$key] = $item;
                        continue;
                    }
                    $cost_department_id          = $one_department_info['id'];
                    $cost_center_department_name = $item[1];
                    //根据部门ID获取对应的pc_code
                    $pcCodeRet = $this->getPcCode($cost_department_id, BaseService::COST_TYPE_HEAD_OFFICE);
                    $cost_center_code = !empty($pcCodeRet['data']) ? $pcCodeRet['data']['pc_code'] : "";
                } else {
                    // 合同网点验证
                    $store_name = $item[1];
                    $store_id = $contract_info['store_id'];
                    $contract_store_name = $sys_store_list[$store_id] ?? '';
                    if (empty($store_name) || (strtolower($store_name) != strtolower($contract_store_name))) {
                        // 网点名称错误
                        $item[] = self::$t['payment_upload_error'];
                        $item[] = self::$t['payment_upload_error_001'];

                        $excel_data[$key] = $item;
                        continue;
                    }

                    // 9888【OA|网点租房付款】功能优化 批量导入增加pc_code（费用所属中心
                    if (strtolower($store_name) == strtolower(Enums::PAYMENT_HEADER_STORE_NAME)) {
                        //如果网点名称是总部的话，因为不知道申请人所属部门，产品说不处理
                        $cost_center_code = "";//费用所属中心
                    } else {
                        //根据网点ID获取对应的pc_code
                        $pcCodeRet = $this->getPcCode($store_id, BaseService::COST_TYPE_SYS_STORE);
                        $cost_center_code = !empty($pcCodeRet['data']) ? $pcCodeRet['data']['pc_code'] : "";
                    }
                }

                // 增加合同结束日期的返回,用于合同是否即将到期提示
                $contract_end_date = $contract_info['contract_end_date'];
                $contract_effect_date = $contract_info['contract_effect_date'];
                $rent_due_date = $contract_info['rent_due_date'];
                // 费用类型验证
                $cost_type = $item[2];
                if (!array_key_exists($cost_type, $cost_type_item)) {
                    // 费用类型错误，请输入：房租或押金或区域服务费或房产税
                    $item[] = self::$t['payment_upload_error'];
                    $item[] = self::$t['payment_upload_error_002'];

                    $excel_data[$key] = $item;
                    continue;
                }
                //9888【OA|网点租房付款】功能优化 批量导入增加核算科目返回
                $cost_type_id =  $cost_type_item[$cost_type];
                $ledger_ret = $this->getLedgerInfoByCostTypeId($cost_type_id);
                if (!empty($ledger_ret['data'])) {
                    //产品说费用类型与核算科目是一对一关系
                    $ledger_account = $ledger_ret['data'][0]['name_en'];
                    $ledger_account_id = $ledger_ret['data'][0]['id'];
                } else {
                    $ledger_account = "";//核算科目名称
                    $ledger_account_id = 0;//核算科目ID
                }

                if ($cost_start_date >= $cost_end_date) {
                    // 费用开始日期 不可 大于等于 结束日期
                    $item[] = self::$t['payment_upload_error'];
                    $item[] = self::$t['payment_upload_error_004'];

                    $excel_data[$key] = $item;
                    continue;
                }

                if ($cost_start_date < $contract_info['contract_begin_date'] || $cost_start_date > $contract_info['contract_end_date']) {
                    // 费用开始日期错误，需在合同生效日期与合同结束日期之间
                    $item[] = self::$t['payment_upload_error'];
                    $item[] = self::$t['payment_upload_error_004'];

                    $excel_data[$key] = $item;
                    continue;
                }

                if ($cost_end_date > $contract_info['contract_end_date'] || $cost_end_date < $contract_info['contract_begin_date']) {
                    // 费用结束日期错误，需在合同生效日期与合同结束日期之间
                    $item[] = self::$t['payment_upload_error'];
                    $item[] = self::$t['payment_upload_error_005'];

                    $excel_data[$key] = $item;
                    continue;
                }

                // 选择的日期不可在同一网点该费用类型已申请过付款的日期
                $tmp_store_contract_data = [
                    'key' => $key,
                    'contract_no' => $contract_no,
                    'store_id' => $store_id,
                    'cost_type_id' => $cost_type_item[$cost_type],
                    'cost_start_date' => $cost_start_date,
                    'cost_end_date' => $cost_end_date,
                ];

                // 选择的日期在同一个申请单中同一网点相同费用类型的日期也不可重复
                // 是否与本次上传的日期端有交叉: false: 有交叉, 不可放行
                $compare_upload_cost_date_res = $this->compareUploadCostDate($tmp_store_contract_data, $upload_contract_date_item);
                if ($compare_upload_cost_date_res['result'] == false) {
                    $hint_key = $compare_upload_cost_date_res['code'] == 1 ? 'payment_upload_error_004' : 'payment_upload_error_005';

                    // 费用日期错误，请按照规则填写
                    $item[] = self::$t['payment_upload_error'];
                    $item[] = self::$t[$hint_key];

                    $excel_data[$key] = $item;
                    continue;
                }

                // 是否在空闲日期段内: false: 不在空闲日期段内
                if ($this->compareUploadCostDateAndExistStoreCostDate($tmp_store_contract_data, $store_contract_available_date_item) == false) {
                    // 费用开始日期错误，请按照规则填写
                    $item[] = self::$t['payment_upload_error'];
                    $item[] = self::$t['payment_upload_error_004'];

                    $excel_data[$key] = $item;
                    continue;
                }
                // 校验费用日期与作废日期
                if (isset($contract_no_replace[$contract_no])) {
                    $invalid_validate = true;
                    //一个合同可能有多个作废信息
                    $transfer_params = [];
                    foreach ($contract_no_replace[$contract_no] as $replace_info) {
                        $transfer_params = ['contract_no' => $replace_info['invalid_replace_cno'], 'invalid_data' => $replace_info['invalid_replace_begin'] . ' - ' . $replace_info['invalid_replace_end']];
                        //开始时间或结束时间在禁用时间内
                        if ($cost_start_date >= $replace_info['invalid_replace_begin'] && $cost_start_date <= $replace_info['invalid_replace_end']) {
                            $invalid_validate = false;
                            break;
                        }
                        if ($cost_end_date >= $replace_info['invalid_replace_begin'] && $cost_end_date <= $replace_info['invalid_replace_end']) {
                            $invalid_validate = false;
                            break;
                        }
                        //开始时间大于禁用时间开始,结束时间大于禁用时间结束
                        if ($cost_start_date <= $replace_info['invalid_replace_begin'] && $cost_end_date >= $replace_info['invalid_replace_end']) {
                            $invalid_validate = false;
                            break;
                        }
                    }
                    if ($invalid_validate === false) {
                        $item[] = self::$t['payment_upload_error'];
                        $item[] = self::$t->_('payment_upload_error_0060', $transfer_params);
                        $excel_data[$key] = $item;
                        continue;
                    }
                }
                // 不含税金额
                if (!preg_match('/^(([1-9]{1}[0-9]*)|([0]{1}))(\.([0-9]){0,2})?$/', $item[6])) {
                    // 金额错误，请填写大于等于0的数字
                    $item[] = self::$t['payment_upload_error'];
                    $item[] = self::$t['ordinary_payment_amount_no_tax_error'];

                    $excel_data[$key] = $item;
                    continue;
                }
                // vat税额
                if (!preg_match('/^(([1-9]{1}[0-9]{0,11})|([0]{1}))(\.([0-9]){0,2})?$/', $item[8])) {
                    // 金额错误，请填写大于等于0的数字
                    $item[] = self::$t['payment_upload_error'];
                    $item[] = self::$t['payment_upload_error_043'];

                    $excel_data[$key] = $item;
                    continue;
                }
                // wht税额
                if (!preg_match('/^(([1-9]{1}[0-9]{0,11})|([0]{1}))(\.([0-9]){0,2})?$/', $item[12])) {
                    // 金额错误，请填写大于等于0的数字
                    $item[] = self::$t['payment_upload_error'];
                    $item[] = self::$t['payment_upload_error_044'];

                    $excel_data[$key] = $item;
                    continue;
                }
                $amount = round($item[6], 2);
                $vat_amount = round($item[8], 2);
                $amount_has_tax = round($item[9], 2);
                $wht_amount = round($item[12], 2);
                $actually_amount = round($item[13], 2);

                if (!in_array($vat_rate, $vat_rate_item)) {
                    // WHT税率错误，类别为PND3，请填写：2%或3%或5%；类别为PND53，请填写：1%或2%或3%或5%；类别为/，请填写：0%
                    $item[] = self::$t['payment_upload_error'];
                    $item[] = self::$t['payment_upload_error_015'];

                    $excel_data[$key] = $item;
                    continue;
                }

                // 含税金额校验
                if (bccomp($amount_has_tax,$amount + $vat_amount,2)) {
                    // 金额错误，请填写大于等于0的数字
                    $item[] = self::$t['payment_upload_error'];
                    $item[] = self::$t['payment_upload_error_041'];

                    $excel_data[$key] = $item;
                    continue;
                }

                // 实际金额校验
                if (bccomp($actually_amount,$amount_has_tax - $wht_amount,2)) {
                    // 金额错误，请填写大于等于0的数字
                    $item[] = self::$t['payment_upload_error'];
                    $item[] = self::$t['payment_upload_error_042'];

                    $excel_data[$key] = $item;
                    continue;
                }

                // WHT类别
                $wht_category = $item[10];
                if (!array_key_exists($wht_category, $wht_category_item)) {
                    // WHT类别错误，请填写：PND3或PND53或/
                    $item[] = self::$t['payment_upload_error'];
                    $item[] = self::$t['payment_upload_error_007'];

                    $excel_data[$key] = $item;
                    continue;
                }

                // WHT类别税率
                if (empty($wht_tax_rate_item[$wht_category_item[$wht_category]]['rate_list'][$wht_rate])) {
                    // WHT税率错误，类别为PND3，请填写：2%或3%或5%；类别为PND53，请填写：1%或2%或3%或5%；类别为/，请填写：0%
                    $item[] = self::$t['payment_upload_error'];
                    $item[] = self::$t['payment_upload_error_008'];

                    $excel_data[$key] = $item;
                    continue;
                }

                $bank_name = $item[14];
                $bank_account_name = $item[15];
                $bank_account_no = $item[16];
                if ($param['payment_method'] == Enums::PAYMENT_METHOD_BANK_TRANSFER) {
                    if (mb_strlen($bank_name) <= 0 || mb_strlen($bank_name) > 100) {
                        // 银行名称错误
                        $item[] = self::$t['payment_upload_error'];
                        $item[] = self::$t['payment_upload_error_009'];

                        $excel_data[$key] = $item;
                        continue;
                    }

                    if (mb_strlen($bank_account_name) <= 0 || mb_strlen($bank_account_name) > 300) {
                        // 银行账户名称错误
                        $item[] = self::$t['payment_upload_error'];
                        $item[] = self::$t['payment_upload_error_010'];

                        $excel_data[$key] = $item;
                        continue;
                    }

                    if (mb_strlen($bank_account_no) <= 0 || mb_strlen($bank_account_no) > 100) {
                        // 银行账户号错误
                        $item[] = self::$t['payment_upload_error'];
                        $item[] = self::$t['payment_upload_error_011'];

                        $excel_data[$key] = $item;
                        continue;
                    }
                }

                $contact_phone = $item[17];
                if (mb_strlen($contact_phone) <= 0 || mb_strlen($contact_phone) > 100) {
                    // 联系人电话错误
                    $item[] = self::$t['payment_upload_error'];
                    $item[] = self::$t['payment_upload_error_012'];

                    $excel_data[$key] = $item;
                    continue;
                }

                $contact_email = $item[18];
                if (!preg_match('/^([a-zA-Z0-9\_\-\.]+)\@([a-zA-Z0-9\_\-]+)\.([a-zA-Z0-9\_\-]+)$/', $contact_email)) {

                    // 邮箱地址错误
                    $item[] = self::$t['payment_upload_error'];
                    $item[] = self::$t['payment_upload_error_013'];

                    $excel_data[$key] = $item;
                    continue;
                }

                // sap供应商编号
                $sap_supplier_no = trim($item[19] ?? '');

                //房东税务号
                $landlord_tax_no = $item[20] ?? '';
                if (get_country_code() == GlobalEnums::PH_COUNTRY_CODE && (mb_strlen($landlord_tax_no) <= 0 || mb_strlen($landlord_tax_no) > 30)) {
                    // 菲律宾国家房东税务号必填
                    $item[] = self::$t['payment_upload_error'];
                    $item[] = self::$t['payment_upload_error_018'];
                    $excel_data[$key] = $item;
                    continue;
                } else if (!empty($landlord_tax_no) && mb_strlen($landlord_tax_no) > 30) {
                    //其他国家限制长度
                    $item[] = self::$t['payment_upload_error'];
                    $item[] = self::$t['payment_amount_landlord_tax_no'];
                    $excel_data[$key] = $item;
                    continue;
                }

                // 收款人银行地址
                $bank_address = $item[21] ?? '';
                // 长度限制
                if (!empty($bank_address) && mb_strlen($bank_address) > 500) {
                    $item[] = self::$t['payment_upload_error'];
                    $item[] = self::$t['payment_upload_error_019'];

                    $excel_data[$key] = $item;
                    continue;
                }
                if ($param['payment_method'] == Enums::PAYMENT_METHOD_BANK_TRANSFER && $param['border_payment_type'] == PayEnums::PAY_WHERE_OUT && empty($bank_address)) {
                    $item[] = self::$t['payment_upload_error'];
                    $item[] = self::$t['payment_upload_error_019'];

                    $excel_data[$key] = $item;
                    continue;
                }

                // Swift Code
                $swift_code = $item[22] ?? '';
                // 长度限制
                if (!empty($swift_code) && mb_strlen($swift_code) > 30) {
                    $item[] = self::$t['payment_upload_error'];
                    $item[] = self::$t['payment_upload_error_020'];

                    $excel_data[$key] = $item;
                    continue;
                }
                if ($param['payment_method'] == Enums::PAYMENT_METHOD_BANK_TRANSFER && $param['border_payment_type'] == PayEnums::PAY_WHERE_OUT && empty($swift_code)) {
                    $item[] = self::$t['payment_upload_error'];
                    $item[] = self::$t['payment_upload_error_020'];

                    $excel_data[$key] = $item;
                    continue;
                }


                // 备注
                $remark = $item[23] ?? '';
                if (!empty($remark) && (mb_strlen($remark) > 5000)) {
                    // 备注字符长度应在0~5000之间
                    $item[] = self::$t['payment_upload_error'];
                    $item[] = self::$t['payment_upload_error_014'];

                    $excel_data[$key] = $item;
                    continue;
                }

                // 原Excel文件数据
                $item[] = self::$t['payment_upload_success'];
                $excel_data[$key] = $item;

                // 无错,计算总额, WHT总额, 实付总额
                $total_amount += $amount;
                $wht_total_amount += $wht_amount;
                $actually_total_amount += $actually_amount;
                $vat_total_amount +=$vat_amount;

                $total_amount_has_tax +=$amount_has_tax;

                // 界面展示
                $amount_detail_data[] = [
                    'is_contract'                 => 1,
                    'contract_no'                 => $contract_no,
                    'contract_end_date'           => $contract_end_date,
                    'contract_effect_date'        => $contract_effect_date,
                    'rent_due_date'               => $rent_due_date,
                    'contract_id'                 => $contract_info['contract_id'] ?? '',
                    'store_id'                    => $store_id,
                    'store_name'                  => $store_name ?? '',
                    'cost_center_code'            => $cost_center_code,
                    'ledger_account_id'           => $ledger_account_id,
                    'ledger_account_name'         => $ledger_account,
                    'cost_type_id'                => $cost_type_item[$cost_type],
                    'due_date'                    => $due_date,
                    'cost_start_date'             => $cost_start_date,
                    'cost_end_date'               => $cost_end_date,
                    'amount'                      => $amount,
                    'vat_rate'                    => $vat_rate,
                    'vat_amount'                  => $vat_amount,
                    'amount_has_tax'              => $amount_has_tax,
                    'wht_category_id'             => $wht_category_item[$wht_category],
                    'wht_tax_rate_id'             => $wht_rate,
                    'wht_tax_rate_label'          => $item[11],
                    'wht_amount'                  => $wht_amount,
                    'actually_amount'             => $actually_amount,
                    'bank_name'                   => $bank_name,
                    'bank_account_name'           => $bank_account_name,
                    'bank_account_no'             => $bank_account_no,
                    'contact_phone'               => $contact_phone,
                    'contact_email'               => $contact_email,
                    'sap_supplier_no'             => $sap_supplier_no,
                    'landlord_tax_no'             => $landlord_tax_no,
                    'bank_address'                => $bank_address,
                    'swift_code'                  => $swift_code,
                    'remark'                      => $remark,
                    'is_main'                     => $contract_info['is_main'],
                    'house_owner_type'            => $contract_info['house_owner_type'],
                    'cost_department_id'          => $cost_department_id ?? 0,
                    'cost_center_department_name' => $cost_center_department_name ?? '',
                ];
            }

            $success_total = count($amount_detail_data);
            $fail_total = $upload_total - $success_total;
            $data = [
                // 上传结果
                'upload_result' => [
                    'upload_total' => $upload_total,
                    'success_total' => $success_total,
                    'fail_total' => $fail_total > 0 ? $fail_total : 0,
                ],
                // 上传结果反馈文件
                'upload_result_file' => [
                    'file_name' => '',
                    'file_url' => ''
                ],
                // 上传后的金额详情
                'amount_detail_data' => $amount_detail_data,
                // 上传后的金额总计
                'amount_total_data' => [
                    'total_amount' => round($total_amount, 2),
                    'wht_total_amount' => round($wht_total_amount, 2),
                    'actually_total_amount' => round($actually_total_amount, 2),
                    'vat_total_amount'  => round($vat_total_amount, 2),
                    'tax_total_amount' => round($total_amount_has_tax,2),
                ],

                // 原Excel数据
                'excel_data' => $excel_data
            ];

        } catch (ValidationException $e) {
            $code = $e->getCode();
            $message = $e->getMessage();

        } catch (Exception $e) {
            $code = ErrCode::$SYSTEM_ERROR;
            $message = $e->getMessage();

            $this->logger->warning('付款管理 - 网点租房付款申请 - 金额详情批量上传异常: ' . $message);
        }

        return [
            'code' => $code,
            'message' => $message,
            'data' => $data
        ];
    }

    /**
     * 上传日期格式的兼容处理
     * @param $file_src_date
     * @return mixed
     */
    protected function handleUploadFileDate($file_src_date)
    {
        $date = '';
        // 时间戳
        if (preg_match('/\d{10}/', $file_src_date)) {
            $date = date('Y-m-d', $file_src_date);
        } else if (stripos($file_src_date, '/') > 0) {
            $date = date('Y-m-d', strtotime($file_src_date));
        } else if (stripos($file_src_date, '-') > 0) {
            $date = date('Y-m-d', strtotime($file_src_date));
        }

        return $date;
    }

    /**
     * 获取工号基本信息
     * @param $id 工号
     * @param $type 1:根据部门ID获取，2：根据网点ID获取
     * @return array
     */
    /**
     * 获取成本中心
     * 总部需要根据部门ID获取成本中心；网点需要根据网点ID获取成本中心
     * @param integer $id 部门ID或网点ID
     * @param integer $type 1总部，2网点
     * @return array
     */
    public function getPcCode($id, $type)
    {
        $code    = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $data = [];
        try {
            if ($type == self::COST_TYPE_HEAD_OFFICE) {
                $data['pc_code'] = $this->getPcCodeByDepartmentId($id);
            } else {
                $data['pc_code'] = $this->getPcCodeByStoreId($id);
            }

        } catch (Exception $e) {
            $code         = ErrCode::$SYSTEM_ERROR;
            $message      = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        }

        if (!empty($real_message)) {
            $logger = $this->getDI()->get('logger');
            $logger->warning('网点租房付款申请创建页 - 基本信息 - 获取pc code 信息异常: ' . $real_message);
        }
        return [
            'code'    => $code,
            'message' => $message,
            'data'    => $data,
        ];
    }

    /**
     * 总部需要根据部门ID获取成本中心pc_code值
     * @param interger $department_id 部门ID
     * @return string
     */
    public function getPcCodeByDepartmentId($department_id)
    {
        $data = Pccode::findFirst(["conditions" => "department_id = :department_id:", "bind" => ["department_id" => $department_id]]);
        if (!empty($data)) {
            return $data->pc_code;
        }
        return "";
    }

    /**
     * 网点需要根据网点ID获取成本中心sap_pc_code值
     * @param string $store_id 网点ID
     * @return string
     */
    public function getPcCodeByStoreId($store_id)
    {
        $data = SysStoreModel::findFirst(["conditions" => "id = :id:", "bind" => ["id" => $store_id]]);
        if (!empty($data)) {
            return $data->sap_pc_code;
        }
        return "";
    }

    /**
     * 根据费用类型获取核算科目列表
     * @param integer $cost_type_id 费用类型
     * @return array
     */
    public function getLedgerInfoByCostTypeId($cost_type_id = 0)
    {
        $code    = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $data = [];
        $conditions = $cost_type_id > 0 ? "cost_type_id = :cost_type_id: AND is_deleted = :is_deleted:" : "is_deleted = :is_deleted:";
        $bind = $cost_type_id >0 ? ['cost_type_id' => $cost_type_id, 'is_deleted' => BaseService::IS_DELETED] : ['is_deleted' => BaseService::IS_DELETED];
        $relList = PaymentCostLedgeRel::find([
            'conditions' => $conditions,
            'bind' => $bind,
            'columns' => ["ledger_account"]
        ])->toArray();
        if (count($relList) > 0) {
            $accountArr = array_column($relList, 'ledger_account');
            if (count($accountArr) > 0) {
                return LedgerAccountService::getInstance()->getLedgerListByAccounts($accountArr);
            }
        }

        return [
            'code'    => $code,
            'message' => $message,
            'data'    => $data,
        ];
    }

    /**
     * 根据用户信息获取费用所属部门列表
     * @param array $user 用户信息
     * @return array
     */
    public function getMyDeptList($user)
    {
        return DepartmentModel::find([
            'columns' => 'id, name',
            'conditions' => 'level = 1 and type in (2, 3) and deleted = 0 and manager_id = ?1 or id = ?2',
            'bind'       => [
                1 => $user['id'],
                2 => $user['department_id']
            ]
        ])->toArray();
    }

    /**
     * 根据境内境外选项
     * @return array
     */
    public function getBorderTypeList()
    {
        $borderList = PayEnums::$pay_where_id_to_lang_key;
        $options = [];

        foreach ($borderList as $key => $border) {
            if (0 == $key) {
                continue;
            }
            $options[$key] = trim(static::$t->_($border));
        }

        return $options;
    }


    /**
     * 根据loi合同编号查出合同的具体支付信息
     * @Token
     * @Date: 2021-11-24 20:28
     * @return:
     **@author: peak pan
     */
    public function rentingInfo($contract_id)
    {
        $data = ContractStoreRentingModel::findFirst([
            'columns' => '*',
            'conditions' => ' contract_id =:contract_id:',
            'bind' => [
                'contract_id' => trim($contract_id)
            ],
        ]);

        if (!empty($data)) {
            $main_one = $data->toArray();
        } else {
            $main_one = [];
        }
        return $main_one;
    }


    /**
     * 根据loi合同编号查出合同的具体支付信息
     *
     * @Token
     * @Date: 2021-11-16 20:40
     * @param $params
     * @return array
     * @author: peak pan
     */
    public function rentingBank($params)
    {
        $data = ContractStoreRentingModel::findFirst([
            'columns'=>'*',
            'conditions' => ' contract_id =:contract_id: and is_main!=:is_main:',
            'bind'       => [
                'contract_id' => trim($params['contract_id']),
                'is_main'  =>  Enums::CONTRACT_IS_LOI_YES
            ],
        ]);
        if(!empty($data)){
            $bank_collection_list = json_decode($data->toArray()['bank_collection'],true);

            if(empty($bank_collection_list)){
                return $main_list = [];
            }

            foreach ($bank_collection_list as &$value){
                $value['bank_name']         = $value['bank_name_1'] ?? $value['bank_name'] ?? '';
                $value['bank_account_name'] = $value['bank_account_title_1'] ?? $value['bank_account_name'] ?? '';
                $value['bank_account']      = $value['bank_book_no_1'] ?? $value['bank_account'] ?? '';
                $value['contact_mobile']    = $value['contact_mobile_1'] ?? $value['contact_mobile'] ?? '';
                $value['bank_dot_id']    = $value['bank_dot_id_1'] ?? $value['bank_dot_id'] ?? '';
                $value['contact_type'] = ContractStoreRentingService::getInstance()->getHouseOwnerTypeTxt($value['contact_type'] ?? '');   //联系人类型 ,1=公司，2=个人，3=中介
                $value['sap_supplier_no']   = $value['sap_supplier_no'] ?? '';
            }

            $main_list['bank_collection'] = $bank_collection_list;

        }else{
            $main_list = [];
        }
        return $main_list;
    }

    /**
     * 添加文件附件
     * @param $data
     * @param array $user 当前登陆者
     * @return array
     */
    public function addFile($data, $user = [])
    {
        $code         = ErrCode::$SUCCESS;
        $message      = $real_message = '';

        $db           = $this->getDI()->get('db_oa');
        $db->begin();
        try {
            // 是否存在
            $payment = PaymentStoreRenting::findFirst(
                [
                    'conditions' => 'id =:id:',
                    'bind'       => ['id' => $data['id']]
                ]
            );
            if (empty($payment)) {
                throw new ValidationException(static::$t->_('payment_store_renting_get_fail'), ErrCode::$VALIDATE_ERROR);
            }

            //不传递默认数据查询补充附件操作，2是申请人自己补充附件操作
            $type = $data['type'] ?? 1;
            if ($type == 2 && ($payment->create_id != $user['id'] || !in_array($payment->approval_status, [Enums::WF_STATE_PENDING, Enums::WF_STATE_APPROVED]) || !in_array($payment->pay_status, [PayEnums::PAYMENT_MODULE_PAY_STATUS_PENDING, PayEnums::PAYMENT_MODULE_PAY_STATUS_PAY]))) {
                throw new ValidationException(static::$t->_('payment_store_renting_apply_add_file'), ErrCode::$VALIDATE_ERROR);
            }
            if (!empty($data['required_supplement_file'])) {
                $attach_arr = [];
                foreach ($data['required_supplement_file'] as $k => $file) {
                    $tmp                    = [];
                    $tmp['oss_bucket_type'] = Enums::OSS_BUCKET_TYPE_STORE_RENTING_PAYMENT_SUPPLEMENT;
                    $tmp['oss_bucket_key']  = $data['id'];
                    $tmp['sub_type']        = 0;
                    $tmp['bucket_name']     = $file['bucket_name'];
                    $tmp['object_key']      = $file['object_key'];
                    $tmp['file_name']       = $file['file_name'];
                    $tmp['created_at']      = date('Y-m-d H:i:s');
                    $attach_arr[]           = $tmp;
                }
                if (!empty($attach_arr)) {
                    // 删除历史关联附件
                    $old_model = AttachModel::find([
                        'conditions' => 'oss_bucket_type = :oss_bucket_type: and oss_bucket_key = :oss_bucket_key:',
                        'bind'       => ['oss_bucket_type' => Enums::OSS_BUCKET_TYPE_STORE_RENTING_PAYMENT_SUPPLEMENT,
                                         'oss_bucket_key'  => $data['id']
                        ]
                    ]);
                    if (!empty($old_model)) {
                        $del_bool = $old_model->delete();
                        if ($del_bool === false) {
                            throw new BusinessException('租房付款-附件删除失败，attachment data => ' .
                                json_encode(['required_supplement_file' => $data['required_supplement_file'], 'message' => get_data_object_error_msg($old_model)], JSON_UNESCAPED_UNICODE), ErrCode::$BUSINESS_ERROR);
                        }
                    }
                    // 新增附件
                    $attach      = new AttachModel();
                    $attach_bool = $attach->batchInsert($attach_arr);
                    if ($attach_bool === false) {
                        throw new BusinessException('付款申请-附件添加失败，attachment data => ' . json_encode($data['required_supplement_file'], JSON_UNESCAPED_UNICODE), ErrCode::$BUSINESS_ERROR);
                    }


                }
            }
            $db->commit();

        } catch (ValidationException $e) {
            $code         = $e->getCode();
            $message      = $e->getMessage();
        } catch (BusinessException $e) {
            $code         = $e->getCode();
            $message      = $e->getMessage();
            $real_message = $e->getMessage() . '---' . $e->getLine() . '---' . $e->getTraceAsString();
        } catch (Exception $e) {
            //系统错误，不可对外抛出
            $code         = ErrCode::$SYSTEM_ERROR;
            $message      = static::$t->_('retry_later');
            $real_message = $e->getMessage() . '---' . $e->getLine() . '---' . $e->getTraceAsString();
        }
        if (!empty($message)) {
            $db->rollback();
        }

        if (!empty($real_message)) {
            $this->logger->warning('租房付款补充附件:' . $real_message);
        }

        return [
            'code'    => $code,
            'message' => $message,
            'data'    => $result ?? []
        ];
    }

    /**
     * 网点租房付款 - 我的申请 - 创建 - 批量导入数据
     * @param string $excel_file 文件
     * @param array $user 当前员工信息组
     * @return array
     */
    public function importAdd($excel_file, $user)
    {
        try {
            //是否上传了文件
            if (empty($excel_file)) {
                throw new ValidationException(static::$t->_('bank_flow_not_found_file'), ErrCode::$VALIDATE_ERROR);
            }

            $file = $excel_file[0];
            //仅支持.xlsx格式的文件
            $extension = $file->getExtension();
            if (!in_array($extension, ['xlsx'])) {
                throw new ValidationException(static::$t->_('file_format_error'), ErrCode::$VALIDATE_ERROR);
            }

            //读取上传文件数据
            $config = ['path' => ''];
            $excel = new \Vtiful\Kernel\Excel($config);
            $excel_data = $excel->openFile($file->getTempName())
                ->openSheet()
                ->setType([
                    \Vtiful\Kernel\Excel::TYPE_STRING,
                ])
                ->getSheetData();
            //弹出excel标题三行信息
            array_shift($excel_data);
            array_shift($excel_data);
            $third_header = array_shift($excel_data);

            //验证模板-头：费用所属部门	费用所属公司	费用所属总部/网点	付款方式	币种	境内/境外支付	头备注
            $third_header_key = ['cost_center_department_name', 'cost_company_name', 'cost_store_type', 'payment_method', 'currency', 'border_payment_type', 'remark'];
            foreach ($third_header_key as $key => $value) {
                if (trim($third_header[$key]) != static::$t->_('payment_store_renting_import_template_header.' . $value)) {
                    throw new ValidationException(static::$t->_('payment_store_renting_import_template_invalid'), ErrCode::$VALIDATE_ERROR);
                }
            }

            //跳过空行
            foreach ($excel_data as $line => $row) {
                if (empty(implode(',', array_filter($row)))) {
                    //空行
                    unset($excel_data[$line]);
                }
            }
            //验证条数
            if (empty($excel_data) || count($excel_data) > 5000) {
                throw new ValidationException(static::$t->_('data_empty_or_pass_limit'), ErrCode::$VALIDATE_ERROR);
            }

            // 导入中心
            return ImportCenterService::getInstance()->addImportTask($file, $user, ImportCenterEnums::TYPE_PAYMENT_STORE_RENTING_IMPORT);
        } catch (ValidationException $e) {
            // 校验错误，可对外抛出
            $code = $e->getCode();
            $message = $e->getMessage();
        } catch (Exception $e) {
            // 系统错误，不可对外抛出
            $code = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $this->logger->error('importAdd-网点租房付款 - 我的申请 - 创建 - 批量导入数据失败-' . $e->getMessage());
        }
        return [
            'code' => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data' => $code == ErrCode::$SUCCESS ? true : false
        ];
    }

    /**
     * 网点租房付款 - 我的申请 - 创建 - 批量导入数据 - 查询最后一次导入成功的结果
     * @param integer $staff_info_id 员工工号
     * @return array
     */
    public function getImportResult($staff_info_id)
    {
        return ImportCenterService::getInstance()->getBarcodeImportResult(ImportCenterEnums::TYPE_PAYMENT_STORE_RENTING_IMPORT, $staff_info_id);
    }

    /**
     * 验证批量导入数据合法性
     * @param array $excel_data excel数据组
     * @param array $user_info 操作人信息组
     * @return array
     */
    public function handleImportAddTask(array $excel_data, array $user_info): array
    {
        $code = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $total_num = 0;//总记录数
        $failed_num = 0;//失败记录数
        //获取excel表头，并追加导入结果列
        $first_header = array_shift($excel_data);
        $second_header = array_shift($excel_data);
        $third_header = array_shift($excel_data);

        $result_data = $excel_data;
        try {
            //1. 费用所属部门：必填，根据填写的部门名称去校验其是否是组织结构树里非删除的部门，如果是，则校验通过，如果不是，则提示：请填写组织结构树里存在的费用所属部门。
            $department_list = (new DepartmentRepository())->getDepartmentList();
            $department_list = array_column($department_list, 'id', 'name');

            //2. 费用所属公司：必填，根据填写的名称，去校验其是否是COO&CEO底下的BU，如果是，则校验通过；如果不是，则提示：请填写COO&CEO底下的BU
            $bu_company_list = (new PurchaseService())->getCooCostCompany();
            $cost_company_kv = array_column($bu_company_list, 'cost_company_id', 'cost_company_name');

            //5. 币种：必填，只能填写THB,USD,PHP,LAK,CNY,MYR,IDR,VND,EUR任意一个
            $currency_list = [];
            foreach (GlobalEnums::$currency_item as $currency => $value) {
                $currency_list[static::$t->_($value)] = $currency;
            }

            // 获取已归档的网点租房合同基本信息
            $archive_store_renting_contract = $this->getArchiveStoreRentingContract();
            $archive_store_renting_contract = array_column($archive_store_renting_contract, null, 'contract_no');

            // 获取系统网点
            $sys_store_list = $this->getSysStoreList([], [], true);
            $sys_store_list = array_column($sys_store_list, 'name', 'id');

            //V22269 总部时-获取当前用户是否可筛选未删除的全部部门
            $staff_can_select_department = $this->checkStaffCanSelectDepartmentList($user_info);

            // 费用类型
            $cost_type_item = $this->getCostItem();

            // WHT类别
            $wht_category_item = EnumsService::getInstance()->getWhtRateCategoryMap(1, true);

            // WHT类别对应的WHT税率
            $wht_tax_rate_item = EnumsService::getInstance()->getWhtRateMap();

            // vat 税率
            $vat_rate_item      = EnumsService::getInstance()->getVatRateValueItem();

            //开始遍历excel每一行数据并验证处理更新入库
            $excel_data = $this->excelToData($excel_data);

            //按照相关合同进行汇总，一个合同号默认为一个单据。头信息必须一致
            //如果一个表格里存在多个不关联合同的明细行，则生成一个单据。头信息必须一致
            $group_apply_data = [];
            foreach ($excel_data as $line => $item) {
                if (empty(implode(',', array_filter($item)))) {
                    //空行
                    unset($result_data[$line]);
                    continue;
                }
                if (empty($item['contract_no'])) {
                    $failed_num += 1;
                    $result_data[$line][self::$result_column] = static::$t->_('error_message_title') . '，' . static::$t->_('payment_store_renting_import_000');
                    continue;
                }
                $group_apply_data[$item['contract_no']]['detail'][$line] = $item;
            }
            //总数据记录数
            $total_num = count($result_data);

            //遍历汇总后的单据信息组
            $i = 0;
            foreach ($group_apply_data as $contract_no => $data_list) {
                $i ++;
                $group_apply_message = '';
                //单据信息组
                $create_data = [];
                try {
                    //基础校验枚举值
                    $is_validate_true = true;
                    //校验主表字段一致性
                    $validate_main_data = [];
                    foreach ($data_list['detail'] as $line => &$item) {
                        //错误信息组
                        $error_msg = [];

                        // 同一个单据。头信息必须一致
                        if (empty($validate_main_data)) {
                            //1. 费用所属部门：必填，根据填写的部门名称去校验其是否是组织结构树里非删除的部门，如果是，则校验通过，如果不是，则提示：请填写组织结构树里存在的费用所属部门。
                            if (empty($department_list[$item['cost_center_department_name']])) {
                                $error_msg[] = static::$t->_('payment_store_renting_import_003');
                            }

                            //2. 费用所属公司：必填，根据填写的名称，去校验其是否是COO&CEO底下的BU，如果是，则校验通过；如果不是，则提示：请填写COO&CEO底下的BU
                            if (empty($cost_company_kv[$item['cost_company_name']])) {
                                $error_msg[] = static::$t->_('payment_store_renting_import_004');
                            }

                            //3. 费用所属网点/总部：必填，填写1-总部或者 2-网点，填写总部对应单据上的总部，填写网点对应单据上的网点。
                            if (!in_array($item['cost_store_type'], [Enums::PAYMENT_COST_STORE_TYPE_01, Enums::PAYMENT_COST_STORE_TYPE_02])) {
                                $error_msg[] = static::$t->_('payment_store_renting_import_005');
                            }

                            //4. 付款方式：必填，填写2-银行转账或者3-支票
                            if (!in_array($item['payment_method'], [GlobalEnums::PAYMENT_METHOD_BANK_TRANSFER, GlobalEnums::PAYMENT_METHOD_CHECK])) {
                                $error_msg[] = static::$t->_('payment_store_renting_import_006');
                            }

                            //5. 币种：必填，只能填写THB,USD,PHP,LAK,CNY,MYR,IDR,VND,EUR任意一个
                            if (empty($currency_list[$item['currency']])) {
                                $error_msg[] = static::$t->_('payment_store_renting_import_007');
                            }

                            //6. 境内支付/境外支付：必填，只能选择1-境内，2-境外
                            if (!in_array($item['border_payment_type'], [PayEnums::PAY_WHERE_IN, PayEnums::PAY_WHERE_OUT])) {
                                $error_msg[] = static::$t->_('payment_store_renting_import_008');
                            }

                            //7. 备注：如有需要，可填写备注，最多5000字符
                            if ($item['remark'] && mb_strlen($item['remark']) > 5000) {
                                $error_msg[] = static::$t->_('payment_store_renting_import_009');
                            }

                            $validate_main_data['cost_center_department_name'] = $item['cost_center_department_name']; //费用所属部门
                            $validate_main_data['cost_company_name'] = $item['cost_company_name']; //费用所属公司
                            $validate_main_data['cost_store_type'] = $item['cost_store_type']; //费用所属总部/网点
                            $validate_main_data['payment_method'] = $item['payment_method']; //付款方式
                            $validate_main_data['currency'] = $item['currency']; //币种
                            $validate_main_data['border_payment_type'] = $item['border_payment_type'];//境内/境外支付
                            $validate_main_data['header_remark'] = $item['header_remark'];//头备注
                        } else {
                            $this_validate_data = [];
                            $this_validate_data['cost_center_department_name'] = $item['cost_center_department_name'];
                            $this_validate_data['cost_company_name'] = $item['cost_company_name'];
                            $this_validate_data['cost_store_type'] = $item['cost_store_type'];
                            $this_validate_data['payment_method'] = $item['payment_method'];
                            $this_validate_data['currency'] = $item['currency'];
                            $this_validate_data['border_payment_type'] = $item['border_payment_type'];
                            $this_validate_data['header_remark'] = $item['header_remark'];
                            //非首次进入和上一次的对比
                            if (implode('||', $validate_main_data) != implode('||', $this_validate_data)) {
                                $error_msg[] = static::$t->_('payment_store_renting_import_001');
                            }
                        }

                        // 相关合同验证；合同信息取值
                        $contract_info = $archive_store_renting_contract[$item['contract_no']] ?? [];
                        if (empty($contract_info)) {
                            //合同归档状态错误
                            $error_msg[] = static::$t->_('payment_upload_error_000');
                        } elseif (!empty($contract_info['bank_collection'])) {
                            $bank_info = json_decode($contract_info['bank_collection'],true);
                            $renting_info = $this->rentingInfo($item['contract_no']);
                            if (isset($renting_info['is_main']) && $renting_info['is_main'] != Enums::CONTRACT_IS_LOI_YES && is_array($bank_info)) {
                                $is_bank = $is_sap_supplier = true;
                                foreach ($bank_info as $bank) {
                                    $bank['bank_name'] = $bank['bank_name_1'] ?? $bank['bank_name'];
                                    $bank['bank_account_name'] = $bank['bank_account_title_1'] ?? $bank['bank_account_name'];
                                    $bank['bank_account'] = $bank['bank_book_no_1'] ?? $bank['bank_account'];
                                    $old_str = trim($bank['bank_name']) . trim($bank['bank_account_name']) . trim($bank['bank_account']);
                                    $new_str = trim($item['bank_name']) . trim($item['bank_account_name']) . trim($item['bank_account_no']);
                                    if ($old_str == $new_str) {
                                        $is_bank = false;
                                    }
                                    // sap供应商编号
                                    $bank['sap_supplier_no'] = $bank['sap_supplier_no'] ?? '';
                                    if (isset($bank['sap_supplier_no']) && trim($item['sap_supplier_no'] ?? '') == $bank['sap_supplier_no']) {
                                        $is_sap_supplier = false;
                                    }
                                }
                                if ($is_bank) {
                                    // 银行名称账户名称银行账户有误；付款信息与合同不一致，请检查!
                                    $error_msg[] = static::$t->_('payment_upload_error_021');
                                }
                                if ($is_sap_supplier) {
                                    // SAP供应商号码与原合同不一致，请检查
                                    $error_msg[] = static::$t->_('payment_upload_error_031');
                                }
                            }
                        }

                        // V22269 区分总部、网点拦截逻辑 - 费用所属网点：1-总部； 2-网点
                        if ($item['cost_store_type'] == Enums::PAYMENT_COST_STORE_TYPE_01) {
                            if ($staff_can_select_department) {
                                //当前用户属于配置的租房付款拆分工号时，导入的部门/网点名称必须属于组织架构中未删除的部门名称
                                $cost_department_id  = $department_list[$item['store_name']] ?? 0;
                                if (empty($item['store_name']) || empty($cost_department_id)) {
                                    $error_msg[] = static::$t->_('payment_upload_error_049');
                                }
                            } else {
                                //当前用户不属于配置的租房付款拆分费用部门工号时，导入的部门/网点名称必须等于表头填写的费用部门
                                $cost_department_id = $department_list[$item['cost_center_department_name']] ?? 0;
                                if (empty($item['store_name']) || $item['store_name'] != $item['cost_center_department_name'] || empty($cost_department_id)) {
                                    $error_msg[] = static::$t->_('payment_upload_error_049');
                                }
                            }
                            $cost_center_department_name = $item['store_name'];
                        } elseif ($item['cost_store_type'] == Enums::PAYMENT_COST_STORE_TYPE_02) {
                            $store_id            = $contract_info['store_id'] ?? '';
                            $contract_store_name = $sys_store_list[$store_id] ?? '';
                            if (empty($item['store_name']) || empty($store_id) || strtolower($item['store_name']) != strtolower($contract_store_name)) {
                                //网点名称错误
                                $error_msg[] = static::$t->_('payment_upload_error_001');
                            }
                        }

                        // 费用类型验证
                        if (!array_key_exists($item['cost_type'], $cost_type_item)) {
                            // 费用类型错误，请输入：房租或押金或区域服务费或房产税；费用类型错误，请填写系统中可选的费用类型
                            $error_msg[] = static::$t->_('payment_upload_error_002');
                        }

                        //应付日期
                        $item['due_date'] = $this->handleUploadFileDate($item['due_date']);
                        if (!preg_match('/^\d{4}-\d{2}-\d{2}$/', $item['due_date'])) {
                            $error_msg[] = static::$t->_('payment_upload_error_003');
                        }

                        //费用开始日期
                        $item['cost_start_date'] = $this->handleUploadFileDate($item['cost_start_date']);
                        if (!preg_match('/^\d{4}-\d{2}-\d{2}$/', $item['cost_start_date'])) {
                            $error_msg[] = static::$t->_('payment_upload_error_004');
                        }

                        //费用结束日期
                        $item['cost_end_date'] = $this->handleUploadFileDate($item['cost_end_date']);
                        if (!preg_match('/^\d{4}-\d{2}-\d{2}$/', $item['cost_end_date'])) {
                            $error_msg[] = static::$t->_('payment_upload_error_005');
                        }

                        // 不含税金额（含WHT）
                        if (!preg_match('/^(([1-9]{1}[0-9]*)|([0]{1}))(\.([0-9]){0,2})?$/', $item['amount'])) {
                            $error_msg[] = static::$t->_('ordinary_payment_amount_no_tax_error');
                        }

                        // VAT税率(兼容含有%字符串的问题)
                        if (!empty($item['vat_rate']) && strpos($item['vat_rate'],'%')) {
                            $item['vat_rate'] = str_replace('%','',$item['vat_rate']);
                            $item['vat_rate'] = is_numeric($item['vat_rate']) ? (float) sprintf('%.6f', $item['vat_rate'] / 100) : 0;
                        }
                        $vat_rate = is_numeric($item['vat_rate']) ? (float) sprintf('%.3f', $item['vat_rate'] * 100) : '';
                        $vat_rate = (string) $vat_rate;
                        $item['vat_rate'] = $vat_rate.'%';
                        if (!in_array($vat_rate, $vat_rate_item)) {
                            $error_msg[] = static::$t->_('payment_upload_error_015');
                        }

                        // vat税额
                        if (!preg_match('/^(([1-9]{1}[0-9]{0,11})|([0]{1}))(\.([0-9]){0,2})?$/', $item['vat_amount'])) {
                            $error_msg[] = static::$t->_('payment_upload_error_043');
                        }

                        // 含税金额（含WHT）
                        if (bccomp($item['amount_has_tax'],$item['amount'] + $item['vat_amount'],2)) {
                            $error_msg[] = static::$t->_('payment_upload_error_041');
                        }

                        //WHT类别，请填写：PND3或PND53或/
                        if (!array_key_exists($item['wht_category'], $wht_category_item)) {
                            $error_msg[] = static::$t->_('payment_upload_error_007');
                        }

                        // WHT类别税率(兼容含有%字符串的问题)
                        if (!empty($item['wht_tax_rate']) && strpos($item['wht_tax_rate'],'%')) {
                            $item['wht_tax_rate'] = str_replace('%','',$item['wht_tax_rate']);
                            $item['wht_tax_rate'] = is_numeric($item['wht_tax_rate']) ? (float) sprintf('%.6f', $item['wht_tax_rate'] / 100) : 0;
                        }
                        $wht_rate = is_numeric($item['wht_tax_rate']) ? (float) sprintf('%.3f', $item['wht_tax_rate'] * 100) : '';
                        $wht_rate = (string) $wht_rate;
                        $item['wht_tax_rate'] = $wht_rate . '%';
                        if (empty($wht_tax_rate_item[$wht_category_item[$item['wht_category']]]['rate_list'][$wht_rate])) {
                            // WHT税率错误，类别为PND3，请填写：2%或3%或5%；类别为PND53，请填写：1%或2%或3%或5%；类别为/，请填写：0%
                            $error_msg[] = static::$t->_('payment_upload_error_008');
                        }

                        // WHT金额
                        if (!preg_match('/^(([1-9]{1}[0-9]{0,11})|([0]{1}))(\.([0-9]){0,2})?$/', $item['wht_amount'])) {
                            $error_msg[] = static::$t->_('payment_upload_error_044');
                        }

                        // 实付金额
                        if (bccomp($item['actually_amount'],$item['amount_has_tax'] - $item['wht_amount'],2)) {
                            $error_msg[] = static::$t->_('payment_upload_error_042');
                        }

                        $new_item = array_values($item);
                        foreach ($new_item as $k => $v) {
                            //费用所属总部/网点、付款方式、境内/境外支付
                            if (in_array($k, [2,3,5])) {
                                continue;
                            }
                            $result_data[$line][$k] = $v;
                        }
                        if (!empty($error_msg)) {
                            $failed_num += 1;
                            $is_validate_true = false;
                            $result_data[$line][self::$result_column] = static::$t->_('error_message_title') . '，' . implode(';', $error_msg);
                        }

                        //所有行都初步验证通过组装参数
                        if ($is_validate_true) {
                            $index = 1;
                            //基础校验通过，组装参数
                            $cost_type_id = $cost_type_item[$item['cost_type']];
                            $ledger_ret = $this->getLedgerInfoByCostTypeId($cost_type_id);
                            //核算科目ID
                            $ledger_account_id = !empty($ledger_ret['data']) ? $ledger_ret['data'][0]['id'] : 0;
                            //费用所属中心
                            if ($item['cost_store_type'] == Enums::PAYMENT_COST_STORE_TYPE_01) {
                                //总部，则获取部门在组织架构中维护的SAP成本中心
                                $pc_code_id   = $cost_department_id;
                                $pc_code_type = BaseService::COST_TYPE_HEAD_OFFICE;

                            } else {
                                //根据网点ID获取对应的pc_code
                                $pc_code_id   = $store_id;
                                $pc_code_type = BaseService::COST_TYPE_SYS_STORE;
                            }
                            $pc_code_ret = $this->getPcCode($pc_code_id, $pc_code_type);
                            $cost_center_code = !empty($pc_code_ret['data']) ? $pc_code_ret['data']['pc_code'] : '';

                            if (isset($create_data['amount_detail'])) {
                                $index++;
                            } else {
                                $create_data = [
                                    'apply_no'             => static::genSerialNo(Enums::PAYMENT_STORE_RENTING_APPLY_NO_PREFIX, RedisKey::PAYMENT_STORE_RENTING_APPLY_COUNTER),
                                    'cost_department_id'   => $department_list[$item['cost_center_department_name']],
                                    'cost_department_name' => $item['cost_center_department_name'],
                                    'cost_company_id'      => $cost_company_kv[$item['cost_company_name']],
                                    'cost_company_name'    => $item['cost_company_name'],
                                    'cost_store_type'      => $item['cost_store_type'],
                                    'payment_method'       => $item['payment_method'],
                                    'currency'             => $currency_list[$item['currency']],
                                    'border_payment_type'  => $item['border_payment_type'],
                                    'remark'               => $item['header_remark'],
                                    'source_type'          => 2,//来源方式-2:Excel导入创建
                                    'event_file'           => [],
                                    'attachment_list'      => (object)[],
                                    'ver'                  => 1,
                                ];
                            }
                            $create_data['amount_detail'][] = [
                                'is_contract'                 => $item['contract_no'] ? 1 : 0,      //是否有合同：0-无;1-有
                                'contract_no'                 => $item['contract_no'],              //相关合同
                                'store_id'                    => $store_id ?? '',                   //网点id
                                'store_name'                  => $contract_store_name ?? '',        //网点名称
                                'cost_department_id'          => $cost_department_id ?? 0,          //费用所属部门ID
                                'cost_center_department_name' => $cost_center_department_name ?? '',//费用所属部门名称
                                'cost_type_id'                => $cost_type_id,                     //费用类型
                                'due_date'                    => $item['due_date'],                 //应付日期
                                'cost_start_date'             => $item['cost_start_date'],          //费用开始日期
                                'cost_end_date'               => $item['cost_end_date'],            //费用结束日期
                                'amount'                      => $item['amount'],                   //不含税金额（含WHT）
                                'vat_rate'                    => $vat_rate,                         //VAT税率
                                'vat_amount'                  => $item['vat_amount'],               //VAT税额
                                'amount_has_tax'              => $item['amount_has_tax'],           //含税金额（含WHT）
                                'wht_category'                => $item['wht_category'],
                                'wht_category_id'             => $wht_category_item[$item['wht_category']],//WHT类别
                                'wht_tax_rate_id'             => $wht_rate,                                //WHT税率
                                'wht_amount'                  => $item['wht_amount'],                      //WHT金额
                                'actually_amount'             => $item['actually_amount'],                 //实付金额
                                'bank_name'                   => $item['bank_name'],                       //银行名称
                                'bank_account_name'           => $item['bank_account_name'],               //银行账户名称
                                'bank_account_no'             => $item['bank_account_no'],                 //银行账户号
                                'contact_phone'               => $item['contact_phone'],                   //联系人电话
                                'contact_email'               => $item['contact_email'],                   //联系人邮箱
                                'sap_supplier_no'             => $item['sap_supplier_no'],                 //SAP供应商号码
                                'landlord_tax_no'             => $item['landlord_tax_no'],                 //房东税务号
                                'bank_address'                => $item['bank_address'],                    //收款人银行地址,
                                'swift_code'                  => $item['swift_code'],                      //Swift Code
                                'remark'                      => $item['remark'],                          //备注
                                'cost_center_code'            => $cost_center_code,
                                'ledger_account_id'           => $ledger_account_id,
                                'certificate_desc'            => $item['contract_no'] . '#' . date('m.d', strtotime($item['cost_start_date'])) . '-' . date('m.d', strtotime($item['cost_end_date'])),
                                'index'                       => $index,
                            ];
                        }
                    }

                    //它行有误
                    if ($is_validate_true === false) {
                        throw new ValidationException(static::$t->_('payment_store_renting_import_002'), ErrCode::$VALIDATE_ERROR);
                    }

                    //其他校验逻辑
                    self::$memory_limit = false;
                    $create_data = $this->extendValidation($create_data);

                    //所有明细行，均验证通过，开始入库
                    $create_res = $this->add($create_data, $user_info);
                    if (!isset($create_res['code']) || $create_res['code'] != ErrCode::$SUCCESS) {
                        throw new Exception($create_res['message'], $create_res['code']);
                    }

                } catch (Exception $e) {
                    $group_apply_message = $e->getMessage();
                }

                if ($group_apply_message) {
                    foreach ($data_list['detail'] as $line => $item) {
                        if (empty($result_data[$line][self::$result_column])) {
                            $result_data[$line][self::$result_column] = static::$t->_('error_message_title') . '，' . $group_apply_message;
                            $failed_num += 1;
                        }
                    }
                } else {
                    foreach ($data_list['detail'] as $line => $item) {
                        $result_data[$line][self::$result_column] = static::$t->_('success') . '，' . $create_data['apply_no'];
                    }
                }
                //每处理100条休息10s防止cpu飙升内存不足
                if ($i % 100 == 0) {
                    sleep(10);
                }
            }
            array_unshift($result_data, $first_header, $second_header, $third_header);
        } catch (Exception $e) {
            //系统错误，不可对外抛出
            $code = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $this->logger->warning('StoreRentingAdd - handleImportAddTask-failed: ' . $e->getMessage());
        }

        return [
            'code' => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data' => [
                'excel_data' => $result_data,
                'success_num' => $total_num - $failed_num,
                'failed_sum' => $failed_num
            ]
        ];
    }

    /**
     * 转excel索引
     * @param array $excel_data excel数据
     * @return array
     */
    public function excelToData($excel_data)
    {
        //excel转字段
        $data_key = [
            'cost_center_department_name',//费用所属部门
            'cost_company_name',//费用所属公司
            'cost_store_type',//费用所属总部/网点；填写1-总部或者 2-网点
            'payment_method',//付款方式；填写2-银行转账或者3-支票
            'currency',//币种只能填写THB,USD,PHP,LAK,CNY,MYR,IDR,VND,EUR任意一个
            'border_payment_type',//境内/境外支付；只能选择1-境内，2-境外
            'header_remark',//头备注
            'contract_no',//相关合同
            'store_name',//网点名称
            'cost_type',//费用类型
            'due_date',//应付日期
            'cost_start_date',//费用开始日期
            'cost_end_date',//费用结束日期
            'amount',//不含税金额（含WHT）
            'vat_rate',//VAT税率
            'vat_amount',//VAT税额
            'amount_has_tax',//含税金额（含WHT）
            'wht_category',//WHT类别
            'wht_tax_rate',//WHT税率
            'wht_amount',//WHT金额
            'actually_amount',//实付金额
            'bank_name',//银行名称
            'bank_account_name',//银行账户名称
            'bank_account_no',//银行账户号
            'contact_phone',//联系人电话
            'contact_email',//联系人邮箱
            'sap_supplier_no',//SAP供应商号码
            'landlord_tax_no',//房东税务号
            'bank_address',//收款人银行地址
            'swift_code',//Swift Code
            'remark',//备注
        ];
        $data = [];
        foreach ($excel_data as $line => $info) {
            foreach ($data_key as $index => $key) {
                //费用所属总部/网点、付款方式、境内/境外支付
                if (in_array($key, ['cost_store_type', 'payment_method', 'border_payment_type'])) {
                    $end = stripos($info[$index], '-');
                    if ($end) {
                        $info[$index] = substr($info[$index], 0, $end);
                    } else {
                        $info[$index] = '';
                    }
                }

                $data[$line][$key] = trim($info[$index]);
            }
        }
        return $data;
    }

    /**
     * V22269 - 金额详情需存储费用一级部门ID
     * @param array $amount_detail 金额详情明细行
     * @return array
     */
    private function getAmountDetailSysDepartmentId(array $amount_detail): array
    {
        $department_repository = new DepartmentRepository();
        $department_list = $department_repository->getDepartmentByIds(array_column($amount_detail, 'cost_department_id'));
        foreach ($amount_detail as &$item) {
            if (empty($item['cost_department_id']) || empty($department_list[$item['cost_department_id']])) {
                continue;
            }
            $one_department_info = $department_list[$item['cost_department_id']];
            $first_department_info = $department_repository->getFirstLevelDepartmentByAncestryV3($one_department_info['ancestry_v3']);
            $item['cost_sys_department'] = $first_department_info['id'] ?? 0;
        }
        return $amount_detail;
    }

    /**
     * V22269 总部时-根据申请人可选择范围为总部或者前端用户自己选择的总部-获取申请人权限范围下的部门清单
     * @param array $user 申请人信息组
     * @param int $cost_department_id 单据头上用户选择的费用所属部门ID
     * @return array
     */
    private function getStaffCanSelectDepartmentList(array $user, int $cost_department_id): array
    {
        $staff_can_select_department = $this->checkStaffCanSelectDepartmentList($user);
        if ($staff_can_select_department) {
            //当前用户属于配置的租房付款拆分工号时，必须属于组织架构中未删除的部门
            $staff_can_select_department_list = (new DepartmentRepository())->getDepartmentList();
        } else {
            //当前用户不属于配置的租房付款拆分费用部门工号时，必须等于表头中的费用部门
            $staff_can_select_department_list = (new DepartmentRepository())->getDepartmentDetail($cost_department_id);
            $staff_can_select_department_list = $staff_can_select_department_list ? [$staff_can_select_department_list] : $staff_can_select_department_list;
        }
        return $staff_can_select_department_list ? array_column($staff_can_select_department_list, null, 'name') : $staff_can_select_department_list;
    }

    /**
     * V22269 总部时-获取当前用户是否可筛选未删除的全部部门
     * @param array $user 申请人信息组
     * @return bool
     */
    private function checkStaffCanSelectDepartmentList(array $user): bool
    {
        //租房付款模块-可拆分费用部门工号，支持配置多个工号
        $payment_store_renting_split_department_staff_ids = EnumsService::getInstance()->getSettingEnvValueIds('payment_store_renting_split_department_staff_ids');
        return in_array($user['id'], $payment_store_renting_split_department_staff_ids);
    }
}
