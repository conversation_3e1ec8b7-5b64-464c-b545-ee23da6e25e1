<?php

namespace App\Modules\Payment\Services;

use App\Library\Enums;
use App\Library\Enums\ContractEnums;
use App\Library\Enums\DepositEnums;
use App\Library\Enums\GlobalEnums;
use App\Library\Enums\PayEnums;
use App\Library\Enums\SettingEnums;
use App\Library\Enums\SysConfigEnums;
use App\Library\ErrCode;
use App\Library\Exception\BusinessException;
use App\Library\Validation\ValidationException;
use App\Models\oa\ChequeAccountBusinessRelModel;
use App\Models\oa\ChequeAccountModel;
use App\Models\oa\PaymentStoreRentingModel;
use App\Modules\Budget\Services\LedgerAccountService;
use App\Modules\Common\Services\EnumsService;
use App\Modules\Contract\Models\Contract;
use App\Modules\Contract\Models\ContractArchive;
use App\Modules\Contract\Models\ContractStoreRentingDetailModel;
use App\Modules\Contract\Models\ContractStoreRentingModel;
use App\Modules\Contract\Services\ContractStoreRentingService;
use App\Modules\Deposit\Models\DepositLossModel;
use App\Modules\Deposit\Models\DepositModel;
use App\Modules\Deposit\Models\DepositReturnModel;
use App\Modules\Deposit\Services\DepositService;
use App\Modules\Hc\Models\SysDepartmentModel;
use App\Modules\OrdinaryPayment\Models\OrdinaryPayment;
use App\Modules\OrdinaryPayment\Models\OrdinaryPaymentDetail;
use App\Modules\Organization\Services\DepartmentService;
use App\Modules\Pay\Models\Payment;
use App\Modules\Pay\Models\PaymentCheck;
use App\Modules\Payment\Models\PaymentStoreRenting;
use App\Modules\Payment\Models\PaymentStoreRentingDetail;
use App\Modules\Payment\Models\PaymentStoreRentingPay;
use App\Modules\Purchase\Models\PurchasePayment;
use App\Modules\Purchase\Services\BaseService as PurchaseService;
use App\Modules\Setting\Services\CommonDataPermissionService;
use App\Modules\Workflow\Services\FYRService;
use App\Modules\Workflow\Services\WorkflowServiceV2;
use App\Modules\User\Models\DepartmentModel;
use GuzzleHttp\Exception\GuzzleException;


class StoreRentingListService extends BaseService
{

    // 列表页 - 搜索条件
    public static $validate_list_search = [
        'create_id' => 'StrLenGeLe:3,100|>>>:create_id param error',
        'apply_start_date'=>'date|>>>:apply_start_date param error',
        'apply_end_date' => 'date|>>>:apply_end_date param error',
        'apply_no' => 'StrLenGeLe:6,32|>>>:apply_no param error',
        'apply_status' => 'IntIn:1,2,3,4|>>>:apply_status param error',
        'pay_status' => 'IntIn:'. Enums::PAYMENT_PAY_STATUS_PENDING . ',' . Enums::PAYMENT_PAY_STATUS_PAY . ',' . Enums::PAYMENT_PAY_STATUS_NOTPAY . ','. Enums::PAYMENT_PAY_STATUS_ING . ','. Enums::PAYMENT_PAY_STATUS_PART_PAY .'|>>>:pay_status param error',
        'page' => 'IntGe:1|>>>:page param error',
        'page_num' => 'IntGe:1|>>>:page_num param error',
        'contract_no' => 'StrLenGeLe:1,20|>>>:contract_no param error',
        'pay_date_start'=>'date|>>>:pay_date_start param error',
        'pay_date_end' => 'date|>>>:pay_date_end param error'
    ];

    // 列表页 - 数据导出
    public static $validate_list_export_params = [
        'create_id' => 'StrLenGeLe:3,100|>>>:create_id param error',
        'apply_start_date'=>'date|>>>:apply_start_date param error',
        'apply_end_date' => 'date|>>>:apply_end_date param error',
        'approve_at_start_date'=>'DateTime|>>>:approve_at_start_date param error',
        'approve_at_end_date' => 'DateTime|>>>:approve_at_end_date param error',
        'apply_no' => 'StrLenGeLe:6,32|>>>:apply_no param error',
        'apply_status' => 'IntIn:1,2,3,4|>>>:apply_status param error',
        'pay_status' => 'IntIn:' . Enums::PAYMENT_PAY_STATUS_PENDING . ',' . Enums::PAYMENT_PAY_STATUS_PAY . ',' . Enums::PAYMENT_PAY_STATUS_NOTPAY . ','. Enums::PAYMENT_PAY_STATUS_ING . ','. Enums::PAYMENT_PAY_STATUS_PART_PAY . '|>>>:pay_status param error',
        'contract_no' => 'StrLenGeLe:1,20|>>>:contract_no param error',
        'pay_date_start'=>'date|>>>:pay_date_start param error',
        'pay_date_end' => 'date|>>>:pay_date_end param error'
    ];
    public static $validate_reply_list = [
        'is_reply'            => 'Required|IntIn:0,1|>>>:is_reply is_reply error'
    ];
    private static $instance;

    private function __construct()
    {
    }

    /**
     * @return StoreRentingListService
     */
    public static function getInstance()
    {
        if (!isset(self::$instance) || !(self::$instance instanceof self)) {
            self::$instance = new self;
        }
        return self::$instance;
    }

    /**
     * 获取列表页搜索条件的支付状态 和 申请状态列表
     * @param integer $user_id 当前登陆者用户id
     * @return array
     */
    public static function getSearchDefault($user_id)
    {
        //只有租房付款才有部分支付状态
        $pay_status_item = array_replace(Enums::$payment_pay_status,[Enums::PAYMENT_PAY_STATUS_PART_PAY => 'payment_pay_status.' . Enums::PAYMENT_PAY_STATUS_PART_PAY]);

        $apply_status_item = Enums::$payment_apply_status;

        $pay_status_item_tmp = [];
        foreach ($pay_status_item as $index => $t_key) {
            $pay_status_item_tmp[] = [
                'id' => $index,
                'label' => self::$t[$t_key]
            ];
        }

        $apply_status_item_tmp = [];
        foreach ($apply_status_item as $index => $t_key) {
            $apply_status_item_tmp[] = [
                'id' => $index,
                'label' => self::$t[$t_key]
            ];
        }

        return [
            'apply_status_item' => $apply_status_item_tmp,
            'pay_status_item' => $pay_status_item_tmp,
            'cost_company_list'=>(new PurchaseService())->getCooCostCompany(),
            'can_batch_audit' => in_array($user_id, EnumsService::getInstance()->getSettingEnvValueIds('payment_store_renting_batch_audit_staff_ids')) ? true :false
        ];
    }

    /**
     * 获取网点租房付款列表
     *
     * @param array $condition
     * @param array $user
     * @param int $type
     * @param bool $if_download
     * @return array
     */
    public function getList(array $condition, array $user = [], int $type = 0, bool $if_download = false)
    {
        $uid = $user['id'] ?? 0;
        $condition['uid'] = $uid;

        $page_size = empty($condition['page_size']) ? GlobalEnums::DEFAULT_PAGE_SIZE : $condition['page_size'];
        $page_num = empty($condition['page']) ? GlobalEnums::DEFAULT_PAGE_NUM : $condition['page'];
        $offset = $page_size * ($page_num - 1);

        $code = ErrCode::$SUCCESS;
        $message = $real_message = '';

        $data = [
            'items' => [],
            'pagination' => [
                'current_page' => $page_num,
                'per_page' => $page_size,
                'total_count' => 0,
            ]
        ];

        try {
            // 如果是付款支付列表, 且当前用户不在支付权限工号列表, 则返回空
            if ($type == self::LIST_TYPE_PAY) {
                $pay_staff_id = $this->getPayAuthStaffIdItem();
                if (!in_array($uid, $pay_staff_id)) {
                    throw new ValidationException(static::$t->_('no_pay_auth_access_msg'), ErrCode::$STORE_RENTING_PAYMENT_PAY_AUTH_FAIL_ERROR);
                }

                $condition['pay_staff_id_item'] = $pay_staff_id;
            }

            $builder = $this->modelsManager->createBuilder();
            $builder->from(['main' => PaymentStoreRenting::class]);

            //9888【OA|网点租房付款】功能优化 需要返回网点名称
            $builder->leftjoin(PaymentStoreRentingDetail::class, 'pay_srd.store_renting_id = main.id', 'pay_srd');
            $builder = $this->getCondition($builder, $condition, $type, $user, $if_download);

            $count = (int) $builder->columns('COUNT(DISTINCT main.id) AS total')->getQuery()->getSingleResult()->total;

            $items = [];
            if ($count) {
                $columns = [
                    'main.id',
                    'main.apply_no',
                    'main.create_id',
                    'main.create_name',
                    'main.create_nickname',
                    'main.create_department_name',
                    'main.create_node_department_name',
                    'main.cost_center_name',
                    'main.create_company_name',
                    'main.payment_method',
                    'main.currency',
                    'main.remark',
                    'main.total_amount',
                    'main.wht_total_amount',
                    'main.actually_total_amount',
                    'main.vat_total_amount',
                    'main.tax_total_amount',
                    'main.create_date',
                    'main.created_at',
                    'main.approval_status',
                    'main.pay_status',
                    'main.is_after_ap_th',
                    'main.ver',
                    'main.cost_store_type',
                    'main.cost_center_department_name',
                    'pay_srd.store_name',
                    'main.cost_company_id',
                    'main.border_payment_type',
                    'main.create_email',
                    'main.approved_at',
                    'main.cost_department_id'
                ];
                if ($type == self::LIST_TYPE_DATA) {
                    $columns = array_merge($columns, ['store.house_owner_name']);
                }

                // 审核模块的已处理列表, 展示处理时间
                if ($type == self::LIST_TYPE_AUDIT && isset($condition['flag']) && $condition['flag'] == GlobalEnums::AUDIT_TAB_PROCESSED) {
                    $columns[] = 'log.audit_at';
                }

                $builder->columns($columns);

                if (!in_array($type, [self::LIST_TYPE_AUDIT, self::LIST_TYPE_ASK])) {
                    $builder->orderBy('main.id DESC');
                }

                if (!$if_download) {
                    $builder->limit($page_size, $offset);
                }

                $builder->groupBy('main.id');
                $items = $builder->getQuery()->execute()->toArray();
                $items = $this->handleItems($items, $uid, false);
            }

            $data['items'] = $items;
            $data['pagination']['total_count'] = $count;

        } catch (ValidationException $e) {
            $code         = $e->getCode();
            $message      = $e->getMessage();

        } catch (BusinessException $e) {
            // 业务错误（数据错误等），不可对外抛出
            $code = $e->getCode();
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage();

        } catch (\Exception $e){
            $code = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        }

        if (!empty($real_message)) {
            $this->logger->warning('付款管理 - 网点租房付款列表:' . $real_message);
        }

        // 无权限的情况,返回正常结构的空数据,按成功处理
        if ($code == ErrCode::$STORE_RENTING_PAYMENT_PAY_AUTH_FAIL_ERROR) {
            $code = ErrCode::$SUCCESS;
            $message = 'success';
        }

        return [
            'code' => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data' => $data
        ];
    }

    /**
     * 列表复合搜索条件
     *
     * @param $builder
     * @param $condition
     * @param $type
     * @param $user
     * @param $if_download
     * @return mixed
     * @throws BusinessException
     */
    private function getCondition($builder, $condition, $type, $user, $if_download)
    {
        $apply_no = $condition['apply_no'] ?? '';
        $apply_status = $condition['apply_status'] ?? '';
        $pay_status = $condition['pay_status'] ?? '';
        $create_id = $condition['create_id'] ?? '';
        $apply_start_date = $condition['apply_start_date'] ?? '';
        $apply_end_date = $condition['apply_end_date'] ?? '';

        // 审批通过时间
        $approve_at_start_date = $condition['approve_at_start_date'] ?? '';
        $approve_at_end_date   = $condition['approve_at_end_date'] ?? '';
        $store_name            = $condition['store_name'] ?? '';
        $contract_no           = $condition['contract_no'] ?? '';
        $cost_company_id       = $condition['cost_company_id'] ?? [];
        $house_owner_name      = $condition['house_owner_name'] ?? '';

        // 征询回复进度
        $is_reply = $condition['is_reply'] ?? GlobalEnums::CONSULTED_REPLY_STATE_PENDING;
        $is_reply = in_array($is_reply, GlobalEnums::CONSULTED_REPLY_STATE_ITEM) ? $is_reply : GlobalEnums::CONSULTED_REPLY_STATE_PENDING;

        // 审批处理进度
        $flag = $condition['flag'] ?? GlobalEnums::AUDIT_TAB_PENDING;
        $flag = in_array($flag, GlobalEnums::AUDIT_TAB_STATE_ITEM) ? $flag : GlobalEnums::AUDIT_TAB_PENDING;

        //付款审核&付款支付&数据查询 添加网点名称模糊查询
        if (!empty($store_name) && ($type == self::LIST_TYPE_PAY || $type == self::LIST_TYPE_AUDIT || $type == self::LIST_TYPE_DATA)) {
            $builder->andWhere('( pay_srd.store_name LIKE :create_word:)', ['create_word' => "%$store_name%"]);
        }

        if ($type == self::LIST_TYPE_APPLY) {
            // 申请列表: 当前用户申请的
            $builder->andWhere('main.create_id = :uid:', ['uid' => $condition['uid']]);

            // 申请列表: 申请人搜索选项是多余的, 因此置空, 无需参与模糊查询
            $create_id = '';

        } else if ($type == self::LIST_TYPE_AUDIT) {
            $builder = (new WorkflowServiceV2())->getBizWorkflowOrderListByProcessState($builder, $flag, [Enums::WF_PAYMENT_STORE_RENTING_TYPE], $condition['uid'], 'main');

        } else if ($type == self::LIST_TYPE_PAY) {
            // 支付列表: 当前用户在支付人列表的, 且付款申请已审核通过的
            $builder->andWhere('main.approval_status = :approval_status:', ['approval_status' => Enums::PAYMENT_APPLY_STATUS_APPROVAL]);
            if ($flag == 1) {
                // 待处理
                $pay_status_item = [
                    Enums::PAYMENT_PAY_STATUS_PENDING,
                ];

                $builder->andWhere('main.is_pay_module = :is_pay_module:', ['is_pay_module' => PayEnums::BIZ_DATA_IS_PAY_MODULE_NO]);
                $builder->inWhere('main.pay_status', $pay_status_item);

            } else if ($flag == 2) {
                // 已处理
                $pay_status_item = [
                    Enums::PAYMENT_PAY_STATUS_PAY,
                    Enums::PAYMENT_PAY_STATUS_NOTPAY,
                ];

                $builder->inWhere('main.pay_status', $pay_status_item);
            }
        } else if ($type == self::LIST_TYPE_ASK) {
            // 意见征询回复列表 v18276: 待回复的单据无需取终审通过 且 待支付的
            $biz_table_info = ['table_alias' => 'main', 'pay_status_field_name' => ''];
            $builder = (new FYRService())->getBizOrderConsultationReplyListByProcessState($builder, $is_reply, [Enums::WF_PAYMENT_STORE_RENTING_TYPE], $condition['uid'], $biz_table_info);

        } else if ($type == self::LIST_TYPE_DATA) {
            // 数据查询模块
            $builder->leftjoin(PaymentStoreRentingPay::class, 'payment.store_renting_id = main.id', 'payment');
            $builder->leftjoin(ContractStoreRentingModel::class, 'pay_srd.contract_no = store.contract_id', 'store');

            if (!empty($condition['pay_date_start'])) {
                $builder->andWhere('payment.pay_date >= :pay_date_start:', ['pay_date_start' => $condition['pay_date_start']]);
            }

            if (!empty($condition['pay_date_end'])) {
                $builder->andWhere('payment.pay_date <= :pay_date_end:', ['pay_date_end' => $condition['pay_date_end']]);
            }

            if(!empty($house_owner_name)){
                $builder->andWhere('store.house_owner_name LIKE :house_owner_name:', ['house_owner_name' => "{$house_owner_name}%"]);

            }

            //9888 网点付款需求数据查询添加合同精确查找
            if (strlen($contract_no) > 0) {
                $builder->andWhere('pay_srd.contract_no = :contract_no: ', ['contract_no' => $contract_no]);
            }

            // 对接通用数据权限
            // 业务表参数
            $table_params = [
                'table_alias_name' => 'main',
                'create_id_field' => 'create_id',
                'create_node_department_id_filed' => 'cost_department_id',
            ];

            $builder = CommonDataPermissionService::getInstance()->getCommonDataPermission($user, $builder, Enums\SysConfigEnums::SYS_MODULE_PAYMENT, $table_params);

        }

        if (!empty($create_id)) {
            $builder->andWhere('(main.create_id LIKE :create_word: OR main.create_name LIKE :create_word: OR main.create_nickname LIKE :create_word:)', ['create_word' => "%$create_id%"]);
        }

        if (!empty($apply_no)) {
            $builder->andWhere('main.apply_no = :apply_no:', ['apply_no' => $apply_no]);
        }

        if (!empty($apply_start_date)) {
            $builder->andWhere('main.create_date >= :apply_start_date:', ['apply_start_date' => $apply_start_date]);
        }

        if (!empty($apply_end_date)) {
            $builder->andWhere('main.create_date <= :apply_end_date:', ['apply_end_date' => $apply_end_date]);
        }

        if (!empty($approve_at_start_date)) {
            $builder->andWhere('main.approved_at >= :approve_at_start_date:', ['approve_at_start_date' => $approve_at_start_date]);
        }

        if (!empty($approve_at_end_date)) {
            $builder->andWhere('main.approved_at <= :approve_at_end_date:', ['approve_at_end_date' => $approve_at_end_date]);
        }

        if (!empty($apply_status)) {
            $builder->andWhere('main.approval_status = :apply_status:', ['apply_status' => $apply_status]);
        }

        if (!empty($pay_status)) {
            $builder->andWhere('main.pay_status = :pay_state:', ['pay_state' => $pay_status]);
        }

        if ($if_download) {
            $builder->andWhere('main.ver = :ver:', ['ver' => 1]);
        }

        //查询条件cost_company_id
        if ($cost_company_id) {
            if (is_array($cost_company_id)) {
                $builder->andWhere('main.cost_company_id IN ({cost_company_id:array}) ', ['cost_company_id' => array_values($cost_company_id)]);
            } else {
                $builder->andWhere('main.cost_company_id= :cost_company_id: ', ['cost_company_id' => $cost_company_id]);
            }
        }

        return $builder;
    }

    /**
     * 列表数据格式处理
     * @param array $items
     * @param int $uid
     * @param bool $if_download
     * @return array
     */
    private function handleItems(array $items, $uid = 0,$if_download = false)
    {
        if (empty($items)) {
            return [];
        }
        $pay_status_item = array_replace(Enums::$payment_pay_status,[Enums::PAYMENT_PAY_STATUS_PART_PAY => 'payment_pay_status.' . Enums::PAYMENT_PAY_STATUS_PART_PAY]);
        foreach ($items as &$item) {
            $item['create_name'] = $this->getNameAndNickName($item['create_name'], $item['create_nickname']);

            $item['approval_status_text'] = static::$t[Enums::$payment_apply_status[$item['approval_status']]];
            $item['pay_status_text'] = static::$t[$pay_status_item[$item['pay_status']]];

            $item['payment_method_text'] = static::$t[Enums::$payment_method[$item['payment_method']]];
            $item['currency_text'] = static::$t[GlobalEnums::$currency_item[$item['currency']]];
            $item['create_department_name'] = $item['create_node_department_name'] ? $item['create_node_department_name'] : $item['create_department_name'];
            $item['can_copy'] = ($item['approval_status'] == Enums::CONTRACT_STATUS_REJECTED && $item['created_at'] > '2023-09-01 00:00:00') ? 1 : 2;


            $item['is_can_download'] = $this->isCanDownload($item, $uid);
            if (!$if_download && 1 == $item['ver']) {
                $item['total_amount'] = $item['tax_total_amount'];
            }

            // 不同类型列表的允许操作权限
            unset($item['payment_method']);
            unset($item['create_nickname']);
            unset($item['create_node_department_name']);
        }

        return $items;
    }

    /**
     * 网点租房付款 - 数据导出
     *
     * @param $condition
     * @param int $type
     * @param array $user
     * @return array
     * @throws BusinessException
     * @throws GuzzleException
     */
    public function dataExport($condition, $type = 0, $user = [])
    {
        if ($type == self::LIST_TYPE_DATA) {
            ini_set('memory_limit', '1024M');
        } else {
            ini_set('memory_limit', '512M');
        }

        $this->logger->info('init-memory-use-001:' . memory_usage());
        $data = $this->getList($condition, $user, $type, true);
        if ($data['code'] != ErrCode::$SUCCESS) {
            return $data;
        }

        $this->logger->info('init-memory-use-002:' . memory_usage());

        $data = $data['data']['items'];
        $new_data = [];

        // 获取金额详情列表 和 支付信息
        if (!empty($data)) {
            $departmentIds = array_column($data, 'cost_company_id');
            $data = array_column($data, null, 'id');
            $main_id_array = array_keys($data);
            $apply_nos = array_column($data, 'apply_no');
            // 费用所属公司
            $departments = SysDepartmentModel::find([
                'columns' => ' id, name',
                'conditions' => ' id in ({ids:array}) ',
                'bind' => [
                    'ids' => $departmentIds
                ]
            ])->toArray();
            $departments = array_column($departments,null,'id');

            // 费用所属部门
            $cost_department_ids = array_values(array_filter(array_column($data, 'cost_department_id')));
            $cost_departments = [];
            if (!empty($cost_department_ids)) {
                $cost_departments = SysDepartmentModel::find([
                    'columns' => 'id, name',
                    'conditions' => ' id in ({ids:array}) ',
                    'bind' => [
                        'ids' => $cost_department_ids
                    ]
                ])->toArray();
                $cost_departments = array_column($cost_departments,'name','id');
            }

            // 金额详情
            $builder = $this->modelsManager->createBuilder();
            $builder->from(['psr' => PaymentStoreRentingDetail::class]);
            $builder->inWhere('psr.store_renting_id', $main_id_array);
            $detail_item = $builder->getQuery()->execute()->toArray();

            //根据合同单号查询 每月应付日期

            $contract_nos = array_values(array_unique(array_filter(array_column($detail_item, 'contract_no'))));
            if (!empty($contract_nos)) {
                $contract_infos = ContractStoreRentingModel::Find([
                    'conditions' => 'contract_id  in ({contract_ids:array})',
                    'bind'       => ['contract_ids' => $contract_nos],
                    'columns'    => 'contract_id,rent_due_date'
                ])->toArray();
                $contract_infos = array_column($contract_infos, 'rent_due_date', 'contract_id');
            }

            // 合同押金、定金、合同金额详情
            $contractNoList = array_values(array_unique(array_filter(array_column($detail_item,'contract_no'))));
            $amountDetailArr = $this->conbineAmountDetail($contractNoList);

            // 支付信息
            $builder = $this->modelsManager->createBuilder();
            $builder->from(PaymentStoreRentingPay::class);
            $builder->inWhere('store_renting_id', $main_id_array);
            $pay_item = $builder->getQuery()->execute();

            if (!empty($pay_item)) {
                $pay_item = array_column($pay_item->toArray(), null, 'store_renting_id');
            } else {
                $pay_item = [];
            }

            // 支付模块的支票信息
            $payment_data = Payment::find(
                [
                    'conditions' => 'oa_type = :oa_type: and no in ({nos:array}) and pay_status = :pay_status:',
                    'bind' => ['oa_type' => Enums\BankFlowEnums::BANK_FLOW_OA_TYPE_PAYMENT_STORE_RENTING, 'nos' => $apply_nos, 'pay_status' => Enums\PayEnums::PAYMENT_MODULE_PAY_STATUS_PAY]
                ]
            )->toArray();
            $payment_id_apply_no = array_column($payment_data, 'no', 'id');
            $payment_ids = array_column($payment_data, 'id');
            // 查询支票信息
            $pay_check_data = [];
            if (!empty($payment_ids)) {
                $payment_check_list = PaymentCheck::find([
                    'conditions' => 'payment_id in ({ids:array}) and is_deleted = :is_deleted:',
                    'bind' => ['ids' => $payment_ids, 'is_deleted' => 0]
                ]);
                if (!empty($payment_check_list)) {
                    foreach ($payment_check_list as $item) {
                        $this_no = $payment_id_apply_no[$item->payment_id];
                        // 每个业务单据对应的多个支票信息
                        $pay_check_data[$this_no][] = [
                            'ticket_no' => $item->ticket_no,
                            'amount' => $item->amount,
                        ];
                    }
                }
            }
            // 网点租房付款费用类型枚举
            $store_rent_payment_cost_enums = EnumsService::getInstance()->getStoreRentPaymentCostTypeItem();

            //核算科目名字
            $ledgerIdToName = [];
            $ledger_res = LedgerAccountService::getInstance()->getList();
            if ($ledger_res['code'] == ErrCode::$SUCCESS) {
                $ledgerIdToName = array_column($ledger_res['data'],'name','id');
            }

            $this->logger->info('init-memory-use-003:' . memory_usage());
            /**
             * 查找每个业务单对应的详情id的顺序, 和支票数据按顺序对齐
             * 1. 构建数组$apply_id_detail_id[主表id] = [[详情id1],[详情id2]]
             * 2. 在循环中找到此详情数据的 $apply_id_detail_id[主表id] , 把这个详情数组从0排序
             * 3. 按排序找到对应的支票信息
             */
            $apply_id_detail_id = [];
            foreach ($detail_item as  $d_v) {
                $apply_id_detail_id[$d_v['store_renting_id']][] = $d_v['id'];
            }
            // 付款方式
            $leaseTypes  = (new ContractStoreRentingService())->getContractLeaseTypes();
            $rent_due_arr = ContractStoreRentingService::getInstance()->getRentDueDate();
            $pay_status = array_replace(Enums::$payment_pay_status,[Enums::PAYMENT_PAY_STATUS_PART_PAY => 'payment_pay_status.' . Enums::PAYMENT_PAY_STATUS_PART_PAY]);
            foreach ($detail_item as $key => $val) {
                $tmp_main_data = $data[$val['store_renting_id']] ?? [];
                if (empty($tmp_main_data)) {
                    continue;
                }

                $tmp_pay_data = $pay_item[$val['store_renting_id']] ?? [];
                $contractLeaseType = $amountDetailArr[$val['contract_no']]['contract_lease_type'] ?? '';
                $contractLeaseType = !empty($contractLeaseType) ? $leaseTypes[$contractLeaseType] : '';

                // 查找支票
                $tmp_check_data = [];
                if (isset($pay_check_data[$tmp_main_data['apply_no']]) && isset($apply_id_detail_id[$val['store_renting_id']])) {
                    // 给这一组详情id排序, 一组详情id归属于同一个主表id
                    sort($apply_id_detail_id[$val['store_renting_id']]);
                    // 重置索引
                    $detail_sort = array_values($apply_id_detail_id[$val['store_renting_id']]);
                    // 反转数组,得到 详情id=>排序
                    $detail_sort = array_flip($detail_sort);
                    // 找这个详情id的排序位置
                    if (isset($detail_sort[$val['id']])) {
                        $tmp_check_data = $pay_check_data[$tmp_main_data['apply_no']][$detail_sort[$val['id']]] ?? [];
                    }
                }
                $new_data[] = [
                    // 网点租房付款基本信息
                    $tmp_main_data['apply_no'],//付款申请编号
                    $tmp_main_data['create_name'],//申请人-姓名
                    $tmp_main_data['create_id'],//申请人-工号
                    $tmp_main_data['create_email'], // 申请人邮箱
                    $tmp_main_data['create_department_name'],//申请人-一级部门名字
                    $tmp_main_data['cost_center_name'],//费用-所属中心(pc_code)
                    $tmp_main_data['create_company_name'],//申请人-所属公司名字
                    empty($tmp_main_data['cost_department_id']) ?
                        $tmp_main_data['cost_center_department_name'] : ($cost_departments[$tmp_main_data['cost_department_id']] ?? ''),//费用所属部门
                    $tmp_main_data['cost_center_name'],//费用所属中心
                    $departments[$tmp_main_data['cost_company_id']]['name'] ?? '',//费用所属公司
                    self::$t[Enums::$payment_cost_store_type[$tmp_main_data['cost_store_type']]],//费用所属网点：1-总部； 2-网点
                    $tmp_main_data['payment_method_text'],//付款方式
                    self::$t[Enums\PayEnums::$pay_where_id_to_lang_key[$tmp_main_data['border_payment_type']]],//境内/境外支付
                    $tmp_main_data['currency_text'],//付款币种
                    $tmp_main_data['remark'],//备注

                    self::$t[$pay_status[$tmp_main_data['pay_status']]],//单据头支付状态
                    // 金额详情
                    self::$t[Enums::$payment_contract_status[$val['is_contract']]],//是否有合同
                    $val['id'],//行号
                    $val['contract_no'],//相关合同
                    $contractLeaseType,//付款方式
                    $amountDetailArr[$val['contract_no']]['amount_detail'] ?? '',//金额详情
                    bcadd('0', $amountDetailArr[$val['contract_no']]['deposit_amount'] ?? 0,2),//押金
                    bcadd('0', $amountDetailArr[$val['contract_no']]['contract_deposit'] ?? 0,2),//定金
                    $val['cost_department_id'] ? $val['cost_center_department_name'] : $val['store_name'],//V22269 总部取部门，网点取网点名称
                    $amountDetailArr[$val['contract_no']]['store_id'] ?? '',//网点编号
                    $val['cost_center_code'],//费用所属中心
                    self::$t[$store_rent_payment_cost_enums[$val['cost_type']]],//费用类型
                    $ledgerIdToName[$val['ledger_account_id']]??'',//核算科目

                    $val['sap_supplier_no'],// sap供应商编号
                    $val['certificate_desc'],// 凭证描述
                    $rent_due_arr[$contract_infos[$val['contract_no']] ?? ''] ?? '',//每月应付日
                    $val['due_date'],//应付日期
                    $val['cost_start_date'],//费用开始日期
                    $val['cost_end_date'],//费用结束日期
                    $val['amount'],  //不含税金额
                    $val['vat_rate'].'%', //vat 税率
                    $val['vat_amount'],//vat税额
                    $val['amount_has_tax'],//含税金额

                    $val['wht_category'],
                    $val['wht_tax_rate'] . '%',
                    $val['wht_amount'],
                    $val['actually_amount'],
                    $val['bank_name'],
                    $val['bank_account_name'],
                    $val['bank_account_no'],
                    $val['contact_phone'],
                    $val['contact_email'],


                    $tmp_main_data['total_amount'],//不含税金额总计
                    $tmp_main_data['vat_total_amount'],//vat 金额总计
                    $tmp_main_data['tax_total_amount'],//含税金额总计
                    $tmp_main_data['wht_total_amount'],// 金额总计信息
                    $tmp_main_data['actually_total_amount'],

                    // 支付信息
                    static::$t[Enums::$payment_apply_status[$tmp_main_data['approval_status']]], //申请状态
                    $tmp_main_data['approved_at'] ?? '',
                    isset($tmp_pay_data['is_pay']) ? $tmp_main_data['pay_status_text'] ?? '' : '',
                    $tmp_pay_data['pay_bank_name'] ?? '',
                    $tmp_pay_data['pay_bank_account'] ?? '',
                    $tmp_pay_data['pay_date'] ?? '',
                    $val['pay_status'] ? self::$t[$pay_status[$val['pay_status']]]: '',//单据行支付状态
                    $tmp_check_data['ticket_no'] ?? '', // 支票号码
                    $tmp_check_data['amount'] ?? '', // 支票金额

                    // 金额详情备注
                    $val['remark'],
                    $val['landlord_tax_no'],
                ];

            }

            $data = null;
            $detail_item = null;
            $pay_item = null;
        }

        $header = [
            static::$t->_('global.number'),             // 编号
            static::$t->_('global.applicant.name'),          // 申请人姓名
            static::$t->_('global.applicant.id'),             // 申请人工号
            static::$t->_('import_field_payment_create_email'),  // 申请人邮箱
            static::$t->_('re_field_apply_department_name'),         // 申请人所属部门
            static::$t->_('payment_store_renting_apply_center'),     // 申请人所属中心
            static::$t->_('payment_store_renting_apply_company'),     // 申请人所属公司
            static::$t->_('payment_store_renting_apply_department'),        // 费用所属部门
            static::$t->_('csr_field_cost_center'),        // 费用所属中心
            static::$t->_('expense_company'),        // 费用所属公司
            static::$t->_('payment_store_renting_cost_store'),             //费用所属网点：1-总部； 2-网点
            static::$t->_('payment_store_renting_pay_method'),     // 付款方式
            static::$t->_('overseas_payment_method'),     // 境内/境外支付
            static::$t->_('payment_store_renting_pay_currency'),     // 付款币种
            static::$t->_('payment_store_renting_remark'),     // 备注
            static::$t->_('payment_store_renting_pay_status'),     // 单据头支付状态
            static::$t->_('payment_store_renting_is_contract'),       // 是否有合同
            static::$t->_('payment_store_renting_detail_id'),     // 行号
            static::$t->_('payment_store_renting_contract_no'),       // 相关合同
            static::$t->_('store_renting_contract_payment_method'),  // 合同付款方式
            static::$t->_('store_renting_contract_amount_detail_str'),  // 合同金额发生期间+合同租金
            static::$t->_('store_renting_contract_deposit_amount'),  // 合同押金
            static::$t->_('store_renting_contract_pay_amount'),  // 合同定金
            static::$t->_('payment_store_renting_store_or_department_name'),  // 网点/部门名称
            static::$t->_('store_renting_contract_store_id'),  // 网点编号
            static::$t->_('payment_store_renting_cost_center'),         //费用所属中心
            static::$t->_('payment_store_renting_cost_type'),        // 费用类型
            static::$t->_('payment_store_renting_ledger_account'), //核算科目
            static::$t->_('import_field_payment_sap_supplier_no'),  // sap供应商编号
            static::$t->_('import_field_payment_certificate_desc'),  // 凭证描述
            static::$t->_('payment_store_renting_rent_due_date'),//每月房租应付日
            static::$t->_('payment_store_renting_due_date'),//应付日期
            static::$t->_('payment_store_renting_cost_start_date'), // 费用开始日期
            static::$t->_('payment_store_renting_cost_end_date'),  // 费用结束日期
            static::$t->_('csr_field_no_tax'),  // 不含税金额
            static::$t->_('payment_store_renting_vat_rate'), //vat 税率
            static::$t->_('payment_store_renting_vat_amount'),//vat税额
            static::$t->_('payment_store_renting_amount_has_tax'),//含税金额
            static::$t->_('payment_store_renting_wht_category'),  // WHT类别
            static::$t->_('payment_store_renting_wht_category_tax'),  // WHT税率
            static::$t->_('payment_store_renting_wht_amount'),  // WHT金额
            static::$t->_('payment_store_renting_actually_amount'),  // 实付金额
            static::$t->_('payment_store_renting_bank_name'),  // 银行名称
            static::$t->_('payment_store_renting_bank_acct_name'),  // 银行账户名称
            static::$t->_('payment_store_renting_bank_acct_no'),  // 银行账户号
            static::$t->_('payment_store_renting_contact_phone'),  // 联系人电话
            static::$t->_('payment_store_renting_contact_email'),  // 联系人邮箱
            static::$t->_('payment_store_renting_no_total_amount'),  // 不含税金额总计
            static::$t->_('payment_store_renting_vat_total_amount'),  // vat金额总计
            static::$t->_('payment_store_renting_tax_total_amount'),  // 含税金额总计

            static::$t->_('payment_store_renting_wht_total_amount'),  // WHT金额总计
            static::$t->_('payment_store_renting_actually_total_amount'),  // 实付金额总计
            static::$t->_('payment_store_renting_approval_status'),//申请状态
            static::$t->_('store_renting_contract_approve_at'), // 审批通过日期
            static::$t->_('payment_store_renting_is_pay'),  // 是否已付款
            static::$t->_('payment_store_renting_payment_bank'),  // 付款银行
            static::$t->_('payment_store_renting_payment_bank_account'),  // 付款账号
            static::$t->_('payment_store_renting_payment_date'),  // 付款日期
            static::$t->_('payment_store_renting_detail_pay_status'), //单据行支付状态
            static::$t->_('payment_store_renting_payment_check_ticket_no'),  // 支票号码
            static::$t->_('payment_store_renting_payment_check_amount'),  // 支票金额
            static::$t->_('payment_store_renting_remark'),  // 备注
            static::$t->_('payment_store_renting_landlord_tax_no'),  // 房东税务号
        ];

        $this->logger->info('init-memory-use-004:' . memory_usage());

        $file_name = "StoreRentingPayment_" . date("YmdHis");

        $result = $this->exportExcel($header, $new_data, $file_name);
        $result['message'] = $result['code'] == ErrCode::$SUCCESS ? 'success' : $result['data'];

        $this->logger->info('init-memory-use-005:' . memory_usage());

        return $result;
    }

    /**
     * 获取待支付的数量
     * @param int $user_id
     * @return int
     */
    public function getWaitingPayCount(int $user_id)
    {
        if (empty($user_id)) {
            return 0;
        }

        $pay_staff_ids = $this->getPayAuthStaffIdItem();
        if (!in_array($user_id, $pay_staff_ids)) {
            return 0;
        }

        return PaymentStoreRenting::count([
            'conditions' => 'is_pay_module = :is_pay_module: AND approval_status = :approval_status: AND pay_status = :pay_status:',
            'bind' => [
                'is_pay_module' => PayEnums::BIZ_DATA_IS_PAY_MODULE_NO,
                'approval_status' => Enums::PAYMENT_APPLY_STATUS_APPROVAL,
                'pay_status' => Enums::PAYMENT_PAY_STATUS_PENDING
            ],
        ]);
    }

    /**
     * 通过合同编号获取相关合同信息
     * @param int $user_id
     * @return mixed
     */
    public function getAllRelationContract($contract_no_array = []){
        if (empty($contract_no_array)) {
            return [];
        }
        // 分批查询,默认0个一组，防止合同编号过长导致查询失败
        $contract_no_array_list = array_chunk($contract_no_array,10);
        foreach ($contract_no_array_list as $contract_no) {

        }
    }

    /**
     * 组合合同金额详情
     * @param array $contractNoList
     * @return mixed
     */
    private function conbineAmountDetail($contractNoList = []){
        if (empty($contractNoList)) {
            return [];
        }

        // 付款单关联的网点合同详情-合同时间和金额
        $builder = $this->modelsManager->createBuilder();
        $builder->from(['csr' => ContractStoreRentingModel::class]);
        $builder->columns(['csr.contract_id','csr.deposit_amount','csr.contract_deposit','csr.store_id','csr.contract_lease_type',
            "group_concat(concat_ws('|',csrd.cost_start_date,csrd.cost_end_date,csrd.amount_no_tax)) amount_detail"]);
        $builder->leftjoin(ContractStoreRentingDetailModel::class, 'csrd.contract_store_renting_id = csr.id', 'csrd');
        $builder->inWhere('csr.contract_id', $contractNoList);
        $builder->groupBy('csr.id');
        $detail_item = $builder->getQuery()->execute()->toArray();

        $amountList = [];
        foreach ($detail_item as $k => $amount) {
            if (empty($amount['amount_detail'])) {
                continue;
            }
            $amountDetailList = [];
            $amount['amount_detail'] = explode(',',$amount['amount_detail']);
            foreach ($amount['amount_detail'] as $detail) {
                $detail = explode('|',$detail);
                if (!empty($detail[0]) && !empty($detail[1]) && isset($detail[2]) && isset($amount['contract_id'])) {
                    $amountDetailList[] = $detail[0].'至'.$detail[1].':'.$detail[2];
                }
            }
            if (empty($amountDetailList)) {
                continue;
            }
            $amountList[$amount['contract_id']] = [
                'amount_detail' => implode(';',$amountDetailList),
                'deposit_amount' => $amount['deposit_amount'],
                'contract_deposit' => $amount['contract_deposit'],
                'store_id' => $amount['store_id'],
                'contract_lease_type' => $amount['contract_lease_type'],
            ];
        }
        return $amountList;
    }

    /**
     * 押金单条数据详情处理
     * @Date: 9/27/22 3:24 PM
     * @param array $params
     * @return array
     * @author: peak pan
     **/
    public function depositDetail(array $params)
    {
        $lang = strtolower(substr(self::$language, -2));
        if (!in_array($lang, ['th', 'en', 'cn'])) {
            $lang = 'en';
        }
        $code    = ErrCode::$SUCCESS;
        $message = '';
        //获取付款申请主表信息
        $data = [];
        try {
            $main_model = PaymentStoreRentingDetail::findFirst([
                'id = :id:',
                'bind'    => ['id' => $params['id']],
                'columns' => [
                    'id',
                    'store_renting_id as re_id',
                    'cost_type as budget_id',
                    'ledger_account_id',
                    'store_name as cost_store_name',
                    'cost_center_code as cost_center_name',
                    'amount as amount_no_tax',
                    'vat_amount as amount_vat',
                    'wht_amount as amount_wht',
                    'amount_has_tax as amount_have_tax',
                    'wht_category',
                    'wht_tax_rate as wht_rate',
                    'wht_amount as amount_wht',
                    '"" as cost_category',
                    'contract_no',
                    'store_name',
                    'cost_start_date',
                    'cost_end_date',
                    'vat_rate',
                ]
            ]);
            if (empty($main_model)) {
                throw new ValidationException(self::$t['cheque_account_empty_data'], ErrCode::$VALIDATE_ERROR);
            }
            $payment_store_renting = PaymentStoreRenting::findFirst([
                'id = :id:',
                'bind' => ['id' => $main_model->re_id]
            ]);
            if (empty($payment_store_renting)) {
                throw new ValidationException(self::$t['cheque_account_empty_data'], ErrCode::$VALIDATE_ERROR);
            }

            $data = DepositService::getInstance()->getDepositInfo($main_model, $payment_store_renting, $params);

            $company_arr = SysDepartmentModel::findFirst([
                'conditions' => ' company_id = :company_id:',
                'bind'       => ['company_id' => $payment_store_renting->cost_company_id],
                'columns'    => ['company_name']
            ]);

            // 费用所属部门
            $cost_department_name = $payment_store_renting->cost_center_department_name ?? '';
            if (!empty($payment_store_renting->cost_department_id)) {
                $cost_department      = DepartmentModel::getFirst([
                    'conditions' => 'id = :id:',
                    'columns'    => 'name',
                    'bind'       => ['id' => $payment_store_renting->cost_department_id]
                ]);
                $cost_department_name = $cost_department->name ?? '';
            }

            $data['head'] = [
                'type'                       => static::$t[DepositEnums::$deposit_modules[$params['type']]],
                'id'                         => $main_model->id,
                'apply_no'                   => $payment_store_renting->apply_no,
                'apply_id'                   => $payment_store_renting->create_id,
                'apply_name'                 => $payment_store_renting->create_name . '(' . $payment_store_renting->create_nickname . ')' ?? '',
                'apply_email'                => $payment_store_renting->create_email ?? '',
                'cost_department_name'       => $cost_department_name ?? '',
                'apply_node_department_name' => $payment_store_renting->create_node_department_name,
                'create_company_name'        => $company_arr->company_name ?? '',
                'cost_store_type'            => static::$t[Enums::$payment_cost_store_type[$payment_store_renting->cost_store_type]],
                'currency'                   => static::$t[GlobalEnums::$currency_item[$payment_store_renting->currency]],//币种
                'cost_department_id'         => $payment_store_renting->cost_department_id,
            ];
        } catch (ValidationException $e) {
            $code    = $e->getCode();
            $message = $e->getMessage();

        } catch (\Exception $e) {
            $code         = ErrCode::$SYSTEM_ERROR;
            $message      = static::$t->_('retry_later');
            $real_message = $e->getMessage() . $e->getTraceAsString();
            $this->logger->warning('押金管理-租房-获取数据详情信息:' . $real_message);
        }

        return [
            'code'    => $code,
            'message' => $message,
            'data'    => $data,
        ];

    }


    /**
     *  押金列表分页列表 数据
     *
     * @Date: 9/27/22 3:27 PM
     * @param array $condition 条件
     * @param array $user
     * @param int $type 类型
     * @return  array
     * @author: peak pan
     */
    public function getDepositList(array $condition, array $user, int $type = 0)
    {
        $page_size = empty($condition['pageSize']) ? DepositEnums::PAGE_SIZE : $condition['pageSize'];
        $page_num  = empty($condition['pageNum']) ? DepositEnums::PAGE_NUM : $condition['pageNum'];
        $offset    = $page_size * ($page_num - DepositEnums::PAGE_NUM);

        $code         = ErrCode::$SUCCESS;
        $message      = 'success';
        $data = [
            'items'      => [],
            'pagination' => [
                'current_page' => $page_num,
                'per_page'     => $page_size,
                'total_count'  => 0,
            ],
        ];

        try {
            if (isset($condition['sta_date']) && isset($condition['end_date']) && $condition['sta_date'] > $condition['end_date']) {
                throw new ValidationException(self::$t['start_and_date_error'], ErrCode::$VALIDATE_ERROR);
            }

            if (isset($condition['sta_return_date']) && isset($condition['end_return_date']) && $condition['sta_return_date'] > $condition['end_return_date']) {
                throw new ValidationException(self::$t['start_and_date_error'], ErrCode::$VALIDATE_ERROR);
            }

            $condition['uid'] = $user['id'];
            if ($type == self::LIST_TYPE_PAY) {
                $pay_staff_id = $this->getPayAuthStaffIdItem();
                if (!in_array($user['id'], $pay_staff_id)) {
                    throw new ValidationException('no auth access', ErrCode::$STORE_RENTING_PAYMENT_PAY_AUTH_FAIL_ERROR);
                }
                $condition['pay_staff_id_item'] = $pay_staff_id;
            }

            $builder = $this->modelsManager->createBuilder();
            if (isset($condition['source']) && $condition['source'] == DepositEnums::IS_EXPORT) {
                $columns = [
                    'op.apply_no',//申请单号
                    'op.create_id as apply_id',//申请人工号
                    'op.create_name as apply_name',//申请人姓名
                    'op.create_date as created_at',//申请日期
                    'op.cost_company_id',//费用所属公司
                    'op.cost_center_department_name  as cost_department_name',//费用所属部门
                    'op.cost_department_id',
                    'op.cost_store_type',//费用所属网点/总部
                    'op.create_name as apply_name',//押金负责人
                    'opd.contract_no',//相关合同
                    'ca.status as contract_status',//合同押金状态
                    'csr.contract_end as expiry_date', 'de.contract_no as contract_no_b',
                    'op.currency', 'opd.cost_type as budget_id', '"" as product_id', 'opd.store_name as cost_store_name',//费用所属网点/总部
                    'opd.cost_center_code as cost_center_name',
                    'concat(opd.cost_start_date,"-",opd.cost_end_date) as cost_start_date',//费用发生期间
                    'opd.amount as amount_no_tax',//不含税金额（含WHT）
                    'opd.vat_rate as vat_rate',//SST税率
                    'opd.vat_amount as amount_vat',//SST税额
                    'opd.amount_has_tax as amount_have_tax',//含税金额
                    'opd.wht_category',//WHT类
                    'opd.id AS detail_id', // 明细ID
                    'opd.wht_tax_rate as wht_rate', 'opd.wht_amount as amount_wht', '"" as sum_money',
                    'dr.deposit_money',//押金总金额
                    'dr.status',//押金归还状
                    'de.return_money',//归还金额
                    'dr.loss_money as loss_money_return',//损失总金额
                    'dr.other_return_money',// 其他退款金额
                    'dr.other_return_info',// 其他退款说明
                    'dr.bank_flow_date',//银行流水日期
                    'dr.return_info', '"" as return_attachment',//归还详情附加
                    'dl.loss_bear_id',//损失承担方
                    'dl.loss_budget_id',//损失类型
                    'dl.loss_department_id',//损失部门名称 网点/总部
                    'dl.loss_money', '"" as loss_attachment', 'de.id as deposit_id', 'dr.id as deposit_return_id',
                    'dl.id as deposit_loss_id', 'de.apply_id as deposit_create_name',
                    'de.return_status', 'dl.loss_organization_id',
                    'op.create_node_department_name AS biz_apply_department_name',
                    'de.apply_node_department_name AS deposit_node_department_name',
                ];
            } else {
                $columns = [
                    'op.apply_no', 'opd.id', 'op.create_id as apply_id',
                    'op.create_name as apply_name', 'op.create_date as created_at',
                    'op.cost_company_id',//费用所属公司cost_department_id
                    'op.cost_center_department_name as cost_department_name',
                    'op.cost_department_id', 'op.cost_store_type', 'op.create_id',
                    'op.create_name', 'op.cost_company_id',
                    'op.create_node_department_name AS biz_apply_department_name',
                    'opd.store_name', 'opd.amount as amount_no_tax',
                    'opd.vat_amount as amount_vat',
                    'opd.wht_amount as amount_wht', 'op.currency', 'ca.status', 'de.return_money',
                    'de.deposit_money',//押金总金额
                    'de.loss_money',//损失金额
                    'opd.contract_no', 'de.contract_no as contract_no_b',
                    'de.return_status', 'csr.contract_end as expiry_date',
                    'de.return_status', 'de.apply_id as deposit_create_id', 'de.apply_name as deposit_create_name',
                    'de.apply_node_department_name AS deposit_node_department_name',
                ];
            }
            $builder->from(['opd' => PaymentStoreRentingDetail::class]);
            //组合搜索条件
            $builder->columns($columns);
            $builder = $this->getDepositCondition($builder, $condition, $type, $user);
            $count   = (int)$builder->columns('COUNT(DISTINCT opd.id) AS total')->getQuery()->getSingleResult()->total;

            if ($count) {
                $builder->columns($columns);
                if ($type == DepositEnums::LIST_TYPE_DATA_EXPORT) {
                    $builder->groupBy('dl.id,opd.id');
                } else {
                    $builder->groupBy('opd.id');
                }
                if (!in_array($type, [self::LIST_TYPE_AUDIT, self::LIST_TYPE_ASK])) {
                    $builder->orderBy('opd.id DESC');
                }
                $builder->limit($page_size, $offset);

                $items = $builder->getQuery()->execute()->toArray();
                $items = $this->handleDepositItems($items, $condition);
            }
            $data['items']                     = $items ?? [];
            $data['pagination']['total_count'] = $count;

        } catch (ValidationException $e) {
            $code    = $e->getCode();
            $message = $e->getMessage();

        } catch (\Exception $e) {
            $code         = ErrCode::$SYSTEM_ERROR;
            $message      = static::$t->_('retry_later');
            $real_message = $e->getMessage() . $e->getTraceAsString();
            $this->logger->warning('押金管理-租房-列表数据:' . $real_message);
        }

        return [
            'code'    => $code,
            'message' => $message,
            'data'    => $data,
        ];
    }

    /**
     *  押金列表数据处理
     * @Date: 9/27/22 3:27 PM
     * @param array $items 数据
     * @param array $condition 条件
     * @return  array
     * @author: peak pan
     */
    private function handleDepositItems(array $items, array $condition)
    {
        if (empty($items)) {
            return [];
        }
        $contract_no_status_by_name = [];
        $contract_end_at_arr        = [];
        $contract_no_ids            = array_values(array_filter(array_unique(array_column($items, 'contract_no_b'))));
        if (!empty($contract_no_ids)) {

            $contract_no_arr = ContractArchive::find([
                'conditions' => 'cno in ({cno:array})',
                'bind'       => [
                    'cno' => $contract_no_ids
                ],
                "columns"    => "cno,status",
            ])->toArray();
            if (!empty($contract_no_arr)) {
                $contract_no_status_by_name = array_column($contract_no_arr, 'status', 'cno');
            }
            //过期时间
            $contract_end_at = ContractStoreRentingModel::find([
                'conditions' => 'contract_id in ({contract_id:array})',
                'bind'       => [
                    'contract_id' => $contract_no_ids
                ],
                "columns"    => "contract_end,contract_id",
            ])->toArray();
            if (!empty($contract_end_at)) {
                $contract_end_at_arr = array_column($contract_end_at, 'contract_end', 'contract_id');
            }
        }
        $vat7_rate_list_arr = array_column(EnumsService::getInstance()->getFormatVatRateConfig(), 'label', 'value');
        $cost_company_list  = [];
        $cost_company_ids   = array_values(array_unique(array_filter(array_column($items, 'cost_company_id'))));
        if ($cost_company_ids) {
            $cost_company_arr  = (new DepartmentService())->getDepartmentInfoByIds($cost_company_ids);
            $cost_company_list = array_column($cost_company_arr, 'name', 'id');
        }
        $cost_department     = [];
        $cost_department_ids = array_values(array_unique(array_filter(array_column($items, 'cost_department_id'))));
        if (!empty($cost_department_ids)) {
            $cost_department = DepartmentModel::find([
                'conditions' => 'id in ({id:array})',
                'columns'    => 'id,name',
                'bind'       => ['id' => $cost_department_ids]
            ])->toArray();
        }
        if ($cost_department) {
            $cost_department_list = array_column($cost_department, 'name', 'id');
        }

        if (isset($condition['source']) && $condition['source'] == DepositEnums::IS_EXPORT) {
            $budget_id_arr         = EnumsService::getInstance()->getStoreRentPaymentCostTypeItem();
            $deposit_return_ids    = array_values(array_unique(array_filter(array_column($items, 'deposit_id'))));
            $return_attachment     = empty($deposit_return_ids) ? [] : DepositService::getInstance()->getIdByUrlAttach($deposit_return_ids, Enums::OSS_PAYMENT_STORE_TYPE_DEPOSIT_ADD);
            $loss_bear_name_arr    = array_column(array_merge(DepositEnums::$vendor_arr, (new PurchaseService())->getCooCostCompany()), 'cost_company_name', 'cost_company_id');
            $loss_budget_ids       = array_values(array_unique(array_filter(array_column($items, 'loss_budget_id'))));
            $loss_budget_arr       = empty($loss_budget_ids) ? [] : DepositService::getInstance()->getBudgetIdsByname($loss_budget_ids); //损失类型
            $loss_organization_arr = DepositEnums::$organization_type;

            foreach ($items as &$item) {
                $item['contract_no'] = $item['contract_no_b'] ? $item['contract_no_b'] : $item['contract_no'];
                //正常
                $item['contract_status_id'] = $item['contract_status'];
                $item['contract_status']    = empty($item['contract_no']) ? '' : static::$t['contract_status_name_normal'];
                if (in_array($item['contract_status_id'], [ContractEnums::CONTRACT_ARCHIVE_STATUS_INVALID, ContractEnums::CONTRACT_ARCHIVE_STATUS_TERMINAL]) || (!empty($item['expiry_date'])) && $item['expiry_date'] <= date('Y-m-d', time())) {
                    $item['contract_status'] = static::$t['contract_status_name_be_overdue'];//已过期
                }
                //如果是新的合同号
                if (!empty($item['contract_no_b'])) {
                    $item['contract_status_id'] = $contract_no_status_by_name[$item['contract_no_b']];

                    $item['contract_status'] = empty($item['contract_no']) ? '' : static::$t['contract_status_name_normal'];//正常
                    if (in_array($item['contract_status_id'], [ContractEnums::CONTRACT_ARCHIVE_STATUS_INVALID, ContractEnums::CONTRACT_ARCHIVE_STATUS_TERMINAL]) || (!empty($contract_end_at_arr[$item['contract_no_b']])) && $contract_end_at_arr[$item['contract_no_b']] <= date('Y-m-d', time())) {
                        $item['contract_status'] = static::$t['contract_status_name_be_overdue'];//已过期
                    }
                }
                $item['currency']             = static::$t[GlobalEnums::$currency_item[$item['currency']]];//币种
                $item['sum_money']            = (string)bcsub(bcadd($item['amount_no_tax'], $item['amount_vat'], 3), $item['amount_wht'], 2);//实付金额
                $item['sum_money']            = !empty($item['sum_money']) ? $item['sum_money'] : '';
                $item['deposit_money']        = $item['sum_money'];//押金总金额
                $item['deposit_apply_name']   = !empty($item['deposit_create_name']) ? $item['deposit_create_name'] : $item['apply_id'];
                $item['cost_store_type']      = !empty($item['cost_store_type']) ? static::$t[Enums::$payment_cost_store_type[$item['cost_store_type']]] : '';
                $item['create_company_name']  = !empty($item['cost_company_id']) ? $cost_company_list[$item['cost_company_id']] : '';
                $item['cost_department_name'] = $item['cost_department_name'] ?? '';
                if (!empty($item['cost_department_id'])) {
                    $item['cost_department_name'] = $cost_department_list[$item['cost_department_id']] ?? '';
                }
                $item['amount_no_tax']   = (string)$item['amount_no_tax'] ?? '';
                $item['amount_have_tax'] = (string)$item['amount_have_tax'] ?? '';
                $item['amount_wht']      = (string)$item['amount_wht'] ?? '';
                $item['wht_category']    = $item['wht_category'] ?? '';
                $item['amount_vat']      = empty($item['amount_vat']) ? '0' : (string)floatval($item['amount_vat']);
                $item['vat_rate']        = !empty($item['vat_rate']) ? (string)floatval($item['vat_rate']) . '%' : '0%';
                if (!empty($vat7_rate_list_arr) && !empty($vat7_rate_list_arr[$item['vat_rate']])) {
                    $item['vat_rate'] = !empty($item['vat_rate']) ? (string)$vat7_rate_list_arr[$item['vat_rate']] : '0%';
                }
                $item['wht_rate']         = !empty($item['wht_rate']) ? $item['wht_rate'] . '%' : '0%';
                $item['budget_id']        = !empty($item['budget_id']) ? static::$t[$budget_id_arr[$item['budget_id']]] : '';//预算分类
                $item['product_id']       = '';//明细分类
                $item['status']           = empty($item['status']) ? DepositEnums::DEPOSIT_RETURN_STATUS_NOT : $item['status'];
                $item['return_status_id'] = $item['return_status'] ?? '1';
                $item['return_status']    = empty($item['return_status_id']) ? '' : static::$t[DepositEnums::$contract_return_list[$item['return_status_id']]];

                $item['return_money']       = $item['return_money'] != '' ? bcdiv($item['return_money'], 1000, 2) : '';//归还金额
                $item['other_return_money'] = bcdiv($item['other_return_money'], 1000, 2); // 其他退款金额
                $item['loss_money_return']  = $item['loss_money_return'] != '' ? bcdiv($item['loss_money_return'], 1000, 2) : '';//损失总金额
                $item['return_attachment']  = !empty($return_attachment[$item['deposit_id']]) ?
                    implode(',', $return_attachment[$item['deposit_id']]) : '';//归还详情附加
                $item['loss_bear_id']       = !empty($item['loss_bear_id']) ? $loss_bear_name_arr[$item['loss_bear_id']] : '';//损失承担方
                $item['loss_budget_id']     = !empty($item['loss_budget_id']) ? $loss_budget_arr[$item['loss_budget_id']] : '';//损失类型
                $item['loss_department_id'] = !empty($item['loss_organization_id']) ? static::$t[$loss_organization_arr[$item['loss_organization_id']]] : '';//损失部门名称 网点/总部
                $item['loss_money']         = $item['loss_money'] != '' ? bcdiv($item['loss_money'], 1000, 2) : '';//损失金额
                $item['created_at']         = show_time_zone($item['created_at'], 'Y-m-d');
                $item['deposit_node_department_name'] = $item['deposit_node_department_name'] ?? $item['biz_apply_department_name'];
            }
        } else {
            foreach ($items as &$item) {
                $item['type']                 = DepositEnums::DEPOSIT_PAYMENT_STORE_RENTING;
                $item['return_money']         = $item['return_money'] != '' ? bcdiv($item['return_money'], 1000, 2) : '';
                $item['deposit_money']        = $item['loss_money'] != '' ? bcdiv($item['loss_money'], 1000, 2) : '';
                $item['sum_deposit_money']    = (string)bcsub(bcadd($item['amount_no_tax'], $item['amount_vat'], 2), $item['amount_wht'], 2);
                $item['contract_no']          = $item['contract_no_b'] ? $item['contract_no_b'] : $item['contract_no'];
                $item['contract_status']      = $item['status'] ?? '';
                $item['contract_status_name'] = empty($item['contract_no']) ? '' : static::$t['contract_status_name_normal'];//正常
                if (in_array($item['status'], [ContractEnums::CONTRACT_ARCHIVE_STATUS_INVALID, ContractEnums::CONTRACT_ARCHIVE_STATUS_TERMINAL]) || (!empty($item['expiry_date'])) && $item['expiry_date'] <= date('Y-m-d', time())) {
                    $item['contract_status_name'] = static::$t['contract_status_name_be_overdue'];//已过期
                }
                //如果是新的合同号
                if (!empty($item['contract_no_b'])) {
                    $item['contract_status']      = $contract_no_status_by_name[$item['contract_no_b']];
                    $item['contract_status_name'] = empty($item['contract_no']) ? '' : static::$t['contract_status_name_normal'];//正常
                    if (in_array($item['contract_status'], [ContractEnums::CONTRACT_ARCHIVE_STATUS_INVALID, ContractEnums::CONTRACT_ARCHIVE_STATUS_TERMINAL]) || (!empty($contract_end_at_arr[$item['contract_no_b']])) && $contract_end_at_arr[$item['contract_no_b']] <= date('Y-m-d', time())) {
                        $item['contract_status_name'] = static::$t['contract_status_name_be_overdue'];//已过期
                    }
                    $item['expiry_date'] = $contract_end_at_arr[$item['contract_no_b']] ?? '';
                }
                $item['currency_text']        = static::$t[GlobalEnums::$currency_item[$item['currency']]];
                $item['cost_store_type_text'] = $item['store_name'];
                $item['return_status_id']     = $item['return_status'] ?? '1';
                $item['return_status']        = empty($item['return_status_id']) ? '' : static::$t[DepositEnums::$contract_return_list[$item['return_status_id']]];
                $item['created_at']           = show_time_zone($item['created_at'], 'Y-m-d');
                $item['create_id']            = empty($item['deposit_create_id']) ? $item['create_id'] : $item['deposit_create_id'];
                $item['create_name']          = empty($item['deposit_create_id']) ? $item['create_name'] : $item['deposit_create_name'];
                $item['create_company_name']  = !empty($item['cost_company_id']) ? $cost_company_list[$item['cost_company_id']] : '';
                // 费用所属部门
                $item['cost_department_name'] = $item['cost_department_name'] ?? '';
                if (!empty($item['cost_department_id'])) {
                    $item['cost_department_name'] = $cost_department_list[$item['cost_department_id']] ?? '';
                }

                $item['deposit_node_department_name'] = $item['deposit_node_department_name'] ?? $item['biz_apply_department_name'];
            }
        }
        return $items;
    }

    /**
     *  列表复合搜索条件  押金
     *
     * @Date: 9/27/22 3:27 PM
     * @param object $builder 对象
     * @param array $condition 条件
     * @param int $type 模块类型
     * @param array $user
     * @return  array
     * @throws BusinessException
     * @author: peak pan
     */
    private function getDepositCondition(object $builder, array $condition, int $type, array $user = [])
    {
        $apply_no          = $condition['apply_no'] ?? '';
        $create_company_id = $condition['create_company_id'] ?? '';
        $cost_store_type   = $condition['cost_store_type'] ?? '';
        $contract_no       = $condition['contract_no'] ?? '';
        $sta_date          = $condition['sta_date'] ?? '';
        $end_date          = $condition['end_date'] ?? '';
        $create_id         = $condition['create_name'] ?? '';
        $sta_return_date   = $condition['sta_return_date'] ?? '';
        $end_return_date   = $condition['end_return_date'] ?? '';
        $return_status     = $condition['return_status'] ?? '';
        $detail_id         = $condition['detail_id'] ?? '';

        // 审批处理进度
        $flag               = $condition['flag'] ?? GlobalEnums::AUDIT_TAB_PENDING;
        $flag               = in_array($flag, GlobalEnums::AUDIT_TAB_STATE_ITEM) ? $flag : GlobalEnums::AUDIT_TAB_PENDING;

        // 是否接入通用数据权限
        $is_access_common_data_permission = $condition['is_access_common_data_permission'] ?? false;

        // 付款申请列表 当前登录用户提交的所有申请数据
        $builder->leftjoin(PaymentStoreRenting::class, 'op.id = opd.store_renting_id', 'op');
        $builder->leftjoin(DepositModel::class, 'de.detail_id = opd.id', 'de');
        $builder->leftjoin(DepositReturnModel::class, 'dr.deposit_id = de.id', 'dr');
        $builder->leftjoin(ContractStoreRentingModel::class, 'csr.contract_id = opd.contract_no and csr .contract_id != "" ', 'csr');
        $builder->leftjoin(ContractArchive::class, 'ca.cno=csr.contract_id', 'ca');

        $builder->andWhere('de.id IS NULL OR (de.id IS NOT NULL AND de.deposit_type = :deposit_type:)', ['deposit_type' => DepositEnums::DEPOSIT_PAYMENT_STORE_RENTING]);

        if (in_array($type, [self::LIST_TYPE_APPLY, self::LIST_TYPE_DATA, DepositEnums::LIST_TYPE_DATA_EXPORT]) && get_country_code() == GlobalEnums::TH_COUNTRY_CODE) {
            $builder->leftjoin(PaymentStoreRentingPay::class, 'op.id = srp.store_renting_id', 'srp');
            $builder->andWhere('srp.pay_date >= :pay_date:', ['pay_date' => DepositEnums::DEPOSIT_PAY_AT]);
        }

        if ($type == self::LIST_TYPE_APPLY) {

            $builder->inWhere('opd.cost_type', $condition['deposit_budget_ids']);
            $builder->andWhere('op.create_id = :uid: or de.apply_id = :uid: ', ['uid' => $condition['uid']]);

        } elseif ($type == self::LIST_TYPE_AUDIT) {

            $builder = (new WorkflowServiceV2())->getBizWorkflowOrderListByProcessState($builder, $flag, [Enums::DEPOSIT_RETURN_BIZ_TYPE], $condition['uid'], 'dr');
            $builder->inWhere('opd.cost_type', $condition['deposit_budget_ids']);

        } elseif ($type == self::LIST_TYPE_DATA) {
            $builder->inWhere('opd.cost_type', $condition['deposit_budget_ids']);
        } elseif ($type == DepositEnums::LIST_TYPE_DATA_EXPORT) {
            //数据导出  和列表不一致
            $builder->leftjoin(DepositLossModel::class, 'dl.deposit_return_id = dr.id', 'dl');
            if (!empty($condition['export_type'])) {
                $builder->inWhere('opd.cost_type', $condition['deposit_budget_ids']);
                $builder->andWhere('op.create_id = :uid:  or de.apply_id = :uid: ', ['uid' => $condition['uid']]);
            } else {
                $builder->inWhere('opd.cost_type', $condition['deposit_budget_ids']);
            }

        } elseif ($type == self::LIST_TYPE_ASK) {
            $biz_table_info = ['table_alias' => 'dr'];
            $builder        = (new FYRService())->getBizOrderConsultationReplyListByProcessState($builder, $condition['is_reply'], [Enums::DEPOSIT_RETURN_BIZ_TYPE], $condition['uid'], $biz_table_info);

        }

        // 对接通用数据权限
        if ($is_access_common_data_permission === true) {
            // 业务表参数
            $table_params = [
                'type' => SettingEnums::DATA_PERMISSION_TYPE_MULTI_ENTIEY,
                'entity_item' => [
                    'biz' => [
                        'table_alias_name' => 'op',
                        'create_id_field' => 'create_id',
                        'create_node_department_id_filed' => 'create_node_department_id',
                        'extra_condtions' => 'de.apply_id IS NULL', // 预留, 暂时不用
                    ],
                    'deposit' => [
                        'table_alias_name' => 'de',
                        'create_id_field' => 'apply_id',
                        'create_node_department_id_filed' => 'apply_node_department_id',
                        'extra_condtions' => 'de.apply_id IS NOT NULL', // 预留, 暂时不用
                    ],
                ]
            ];
            $builder = CommonDataPermissionService::getInstance()->getCommonDataPermission($user, $builder, SysConfigEnums::SYS_MODULE_DEPOSIT_RENT_PAYMENT, $table_params);
        }

        //申请编号
        if (!empty($apply_no)) {
            if (is_array($apply_no)) {
                $builder->inWhere('op.apply_no', $apply_no);
            } else {
                $builder->andWhere('op.apply_no = :apply_no:', ['apply_no' => $apply_no]);
            }
        }

        //申请时间-截止日期
        if (!empty($end_date) && !empty($sta_date)) {
            $sta_date .= ' 00:00:00';
            $end_date .= ' 23:59:59';
            $builder->betweenWhere('op.create_date', $sta_date, $end_date);
        }

        //脚本使用
        if (!empty($detail_id)) {
            $builder->inWhere('opd.id', $detail_id);
        }

        //归还-截止日期
        if (!empty($end_return_date) && !empty($sta_return_date)) {
            $sta_return_date .= ' 00:00:00';
            $end_return_date .= ' 23:59:59';
            $builder->betweenWhere('dr.return_date', $sta_return_date, $end_return_date);

            //如果有归还时间 且没有归还状态的时候 默认为已归还
            if (empty($return_status)) {
                $builder->andWhere('de.return_status = :return_status:', ['return_status' => DepositEnums::DEPOSIT_RETURN_STATUS_LAST_FILE]);
            } else if (in_array($return_status, [DepositEnums::DEPOSIT_RETURN_STATUS_NOT, DepositEnums::DEPOSIT_RETURN_STATUS_INTERVENTION, DepositEnums::DEPOSIT_RETURN_STATUS_DETERMINE])) {
                //如果有归还时间 且归还状态为 未归还、法务介入中、法务已确定的时候 为默认状态0
                $builder->andWhere('de.return_status = :return_status:', ['return_status' => DepositEnums::DEPOSIT_RETURN_STATUS_DEFAULT]);
            } else {
                //如果有归还时间 且归还状态不为未归还、法务介入中、法务已确定，空的时候 状态为当前选择的状态
                $builder->andWhere('de.return_status = :return_status:', ['return_status' => $return_status]);
            }
        } else {
            //如果有归还时间为空 且归还状态不为空按照选择的归还状态查询
            if (!empty($return_status)) {
                if ($return_status == DepositEnums::DEPOSIT_RETURN_STATUS_NOT) {
                    $builder->andWhere('(de.return_status = :return_status:) or (de.return_status is null)', ['return_status' => $return_status]);
                } else {
                    $builder->andWhere('de.return_status = :return_status:', ['return_status' => $return_status]);
                }
            }
        }

        //押金负责人
        if (!empty($create_id)) {
            $builder->andWhere('((  (de.apply_id="" or de.apply_id is null ) and (op.create_id = :create_id: or op.create_name = :create_id:) ) or ((de.apply_id = :create_id: or de.apply_name = :create_id:) and (de.apply_id !="" or de.apply_name !="")) )', ['create_id' => $create_id]);
        }
        //费用所属公司
        if (!empty($create_company_id)) {
            $builder->andWhere('op.cost_company_id = :cost_company_id:', ['cost_company_id' => $create_company_id]);
        }

        if (!empty($cost_store_type)) {
            if ($cost_store_type == Enums::HEAD_OFFICE_STORE_FLAG) {
                $builder->andWhere('opd.store_id = :store_id:', ['store_id' => Enums::PAYMENT_HEADER_STORE_ID]);
            } else {
                $builder->andWhere('opd.store_id = :store_id:', ['store_id' => $cost_store_type]);
            }
        }

        if (!empty($contract_no)) {
            if (is_array($contract_no)) {
                $builder->andWhere('(opd.contract_no in ({contract_no:array}) and (de.contract_no ="" or de.contract_no is null)) or (de.contract_no in ({contract_no:array}))', ['contract_no' => $contract_no]);
            } else {
                $builder->andWhere('((de.contract_no = "" or de.contract_no IS NULL ) and opd.contract_no = :contract_no:) or (de.contract_no = :contract_no:)', ['contract_no' => $contract_no]);
            }
        }

        $builder->andWhere('op.pay_status = :pay_status:', ['pay_status' => Enums::PAYMENT_PAY_STATUS_PAY]);

        return $builder;
    }

    /**
    *  批量和租房apply_no
    * @Date: 10/10/23 9:42 PM
    * @param array $cheque_arr  支票code
    * @return  array
    **/
    public function getPaymentStoreRentingApplyNo(array $cheque_arr)
    {
        if (empty($cheque_arr)) {
            return [];
        }
        $builder = $this->modelsManager->createBuilder();
        $builder->columns('cabr.oa_type,cabr.oa_biz_no,ca.cheque_code,ca.use_status,ca.exchange_status');
        $builder->from(['ca' => ChequeAccountModel::class]);
        $builder->leftjoin(ChequeAccountBusinessRelModel::class, 'cabr.cheque_account_id = ca.id', 'cabr');
        $builder->inWhere('ca.cheque_code', $cheque_arr);
        $builder->groupBy('ca.cheque_code');
        return $builder->getQuery()->execute()->toArray();
    }

    /**
     * 网点租房付款 - 付款审核-待处理-批量审核-汇总数据
     * @param array $params 请求参数组
     * @return array
     */
    public function batchAuditSummary($params)
    {
        $code = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $data = [];
        try {
            //获取本位币
            $default_currency = EnumsService::getInstance()->getSysCurrencyInfo();
            $currency = $default_currency['code'];
            $summary_info = PaymentStoreRenting::findFirst([
                'columns' => 'SUM(if(currency = ' . $currency . ', actually_total_amount, actually_total_amount * exchange_rate)) as actually_total_amount, COUNT(id) as total',
                'conditions' => 'id in({ids:array}) and approval_status = :approval_status:',
                'bind' => ['ids' => $params['ids'], 'approval_status' => Enums::WF_STATE_PENDING]
            ]);
            if ($summary_info && $summary_info->total) {
                $data['total'] = $summary_info->total;
                $data['actually_total_amount'] = number_format($summary_info->actually_total_amount, 2);
                $data['currency_text'] = $default_currency['symbol'];
            } else {
                throw new ValidationException(static::$t->_('payment_store_renting_summary_empty'), ErrCode::$VALIDATE_ERROR);
            }
        } catch (ValidationException $e) {
            $code    = $e->getCode();
            $message = $e->getMessage();
        }  catch (\Exception $e) {
            // 系统错误，不可对外抛出
            $code = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $this->logger->error('batchAuditSummary-网点租房付款 - 付款审核-待处理-批量审核-汇总数据失败-' . $e->getMessage());
        }
        return [
            'code' => $code,
            'message' => $message,
            'data' => $data
        ];
    }

    /**
     * 获取租房付款模块审批通过日期在上月1号（含）到本月1号（不含）、费用所属网点/总部=总部、明细行的费用部门 != 单据头上的费用部门的所有明细行数据
     * @param string $last_month_start 上月1号
     * @param string $current_month_start 本月1号
     * @return array
     */
    public function getPublicExpenseSplitData(string $last_month_start, string $current_month_start): array
    {
        $builder = $this->modelsManager->createBuilder();
        $builder->from(['main' => PaymentStoreRenting::class]);
        $builder->innerJoin(PaymentStoreRentingDetail::class, 'detail.store_renting_id = main.id', 'detail');
        //审批通过 && 审批通过日期在上月1号（含）到本月1号（不含）
        $builder->where('main.approval_status = :status: and main.approved_at >= :date_start: and main.approved_at < :date_end:', ['status' => Enums::WF_STATE_APPROVED, 'date_start' => $last_month_start, 'date_end' => $current_month_start]);
        //费用所属网点/总部 (1 :总部,2:网点) = 总部
        $builder->andWhere('main.cost_store_type = :cost_store_type:', ['cost_store_type' => Enums::PAYMENT_COST_STORE_TYPE_01]);
        //明细行的费用部门 != 单据头上的费用部门
        $builder->andWhere('detail.cost_department_id != main.cost_department_id');
        $builder->columns('main.apply_no,main.create_id,main.create_node_department_name,main.create_department_name,main.create_date,detail.cost_center_department_name,detail.cost_type,main.currency,detail.amount_has_tax,detail.cost_sys_department');
        $items = $builder->getQuery()->execute()->toArray();

        if (empty($items)) {
            return [];
        }

        // 网点租房付款费用类型枚举
        $store_rent_payment_cost_enums = EnumsService::getInstance()->getStoreRentPaymentCostTypeItem();

        //英文
        $en_language = self::getTranslation('en');
        $list        = [];
        foreach ($items as $item) {
            $list[] = [
                'module_zh'                   => static::$t->_('public_expense_split_notice_module.4'),
                'module_en'                   => $en_language['public_expense_split_notice_module.4'],
                'no'                          => $item['apply_no'], //租房付款单号
                'created_id'                  => $item['create_id'], //单据创建人工号
                'created_department_name'     => $item['create_node_department_name'], //单据创建人所属部门名称
                'created_sys_department_name' => $item['create_department_name'], //单据创建人所属一级部门名称
                'apply_date'                  => $item['create_date'], //申请日期 = 单据申请日期格式YYYY-MM-DD
                'main_cost_department_name'   => '',//预算部门 = 空
                'cost_department_name'        => $item['cost_center_department_name'], //明细行中费用网点/总部 = 明细行中费用网点/总部
                'cost_classify_zh'            => static::$t->_($store_rent_payment_cost_enums[$item['cost_type']]), //费用分类 = 明细行中的费用类型-中文
                'cost_classify_en'            => $en_language[$store_rent_payment_cost_enums[$item['cost_type']]], //费用分类 = 明细行中的费用类型-英文
                'cost_detail_zh'              => '', //费用明细
                'cost_detail_en'              => '', //费用明细
                'currency'                    => static::$t->_(GlobalEnums::$currency_item[$item['currency']]), //币种 = 表头中的币种
                'amount'                      => $item['amount_has_tax'],//含税金额 = 明细行中的含税金额
                'cost_sys_department'         => $item['cost_sys_department'],//费用一级部门ID
            ];
        }
        return $list;
    }


}
