<?php

namespace App\Modules\Payment\Services;

use App\Library\Enums;
use App\Library\Enums\GlobalEnums;
use App\Modules\Common\Models\EnvModel;
use App\Modules\Common\Services\EnumsService;
use App\Modules\Contract\Models\ContractArchive;
use App\Modules\Contract\Models\ContractStoreRentingModel;
use App\Modules\Contract\Services\ContractStoreRentingService;
use App\Modules\Payment\Models\PaymentStoreRenting;
use App\Modules\Payment\Models\PaymentStoreRentingDetail;
use App\Modules\Reimbursement\Models\OtherPccode;
use App\Modules\User\Models\DepartmentModel;
use App\Modules\User\Services\UserService;
use App\Modules\Reimbursement\Models\Pccode;
use App\Modules\Common\Services\StoreService;

class BaseService extends \App\Library\BaseService
{
    public static $memory_limit = true;//创建但据是否受内存影响,默认受，task创建单据不受限制会传递false
    // 列表类型
    const LIST_TYPE_APPLY = 1;
    const LIST_TYPE_AUDIT = 2;
    const LIST_TYPE_PAY = 3;
    const LIST_TYPE_DATA = 4;
    const LIST_TYPE_ASK =5;

    const SHORT_TEXT_LEN ='0,20';
    const LONG_TEXT_LEN = '0,50';
    const REQUIRED_SHORT_TEXT_LEN = '5,20';
    const REQUIRED_LONG_TEXT_LEN = '1,1000';

    const COST_TYPE_HEAD_OFFICE = 1;//成本中心总部
    const COST_TYPE_SYS_STORE = 2;//成本中心网点
    const IS_DELETED = 0;//未删除

    // 不允许批量上传的费用类型
    const NOT_ALLOW_BATCH_UPLOAD_COST_TYPES = 5;// 定金

    // 非必须参数
    public static $not_must_params = [
        '_url',
        'PHPSESSID'
    ];

    // 网点租房付款申请验证规则
    public static $validate_apply_param = [
        'apply_no' => 'Required|StrLenGeLe:14,20|>>>:numbering param error',
        'payment_method'=>'Required|IntIn:2,3|>>>:payment method param error',
        'currency' => 'Required|IntIn:'.GlobalEnums::VALIDATE_CURRENCY_PARAMS.'|>>>:currency param error',
        'remark' => 'StrLenGeLe:0,5000|>>>:remarks param error',
        'cost_store_type' => 'Required|IntIn:1,2|>>>: expense network param error',
//        'event_file' => [
//            'ArrLenGe:1|>>>:attachment param error',
//        ],
        "ver"   => 'Required|IntIn:1|>>>: ver param error',
        'cost_department_id' => 'Required|IntGe:0|>>>:department id param error',
        'cost_company_id' => 'Required|IntGe:0|>>>:company id param error',
        'bank_address' => 'StrLenGeLe:0,500|>>>:bank address param error',
        'swift_code' => 'StrLenGeLe:0,30|>>>:swift code param error'
    ];

    /**
     * 金额详情验证规则
     * @param array $params 请求参数组
     * @return string[]
     */
    protected static function getValidateAmountDetail(array $params): array
    {
        $validate_param = [
            'amount_detail'                      => 'Required|ArrLenGe:1|>>>:amount details param error',
            'amount_detail[*].sap_supplier_no'   => 'StrLenGeLe:0,50', // sap供应商编号
            'amount_detail[*].certificate_desc'  => 'StrLenGeLe:0,40', // 凭证描述
            'amount_detail[*].is_contract'       => 'Required|IntIn:0,1|>>>:is_contract param error',
            'amount_detail[*].contract_no'       => 'IfIntEq:is_contract,1|Required|StrLenGeLe:12,20|>>>:' . self::$t['payment_upload_error_000'],
            'amount_detail[*].cost_type_id'      => 'Required|IntGe:0|>>>:' . self::$t['payment_upload_error_002'],
            'amount_detail[*].due_date'          => 'Required|Date|>>>:' . self::$t['payment_upload_error_003'],
            'amount_detail[*].cost_start_date'   => 'Required|Date|>>>:' . self::$t['payment_upload_error_004'],
            'amount_detail[*].cost_end_date'     => 'Required|Date|>>>:' . self::$t['payment_upload_error_005'],
            'amount_detail[*].amount'            => 'Required|FloatGt:0|>>>:' . self::$t['payment_upload_error_006'],
            'amount_detail[*].wht_category_id'   => 'Required|IntGe:0|>>>:' . self::$t['payment_upload_error_007'],
            'amount_detail[*].wht_tax_rate_id'   => 'Required|FloatGe:0|>>>:' . self::$t['payment_upload_error_008'],
            'amount_detail[*].bank_name'         => 'StrLenGeLe:0,100|>>>:' . self::$t['payment_upload_error_009'],
            'amount_detail[*].bank_account_name' => 'StrLenGeLe:0,300|>>>:' . self::$t['payment_upload_error_010'],
            'amount_detail[*].bank_account_no'   => 'StrLenGeLe:0,100|>>>:' . self::$t['payment_upload_error_011'],
            'amount_detail[*].contact_phone'     => 'Required|StrLenGeLe:1,100|>>>:' . self::$t['payment_upload_error_012'],
            'amount_detail[*].contact_email'     => 'Required|Email|>>>:' . self::$t['payment_upload_error_013'],
            'amount_detail[*].remark'            => 'StrLenGeLe:0,5000|>>>:' . self::$t['payment_upload_error_014'],
            'amount_detail[*].vat_rate'          => 'Required|FloatGe:0|>>>:' . self::$t['payment_vat_rate_error'],
            'amount_detail[*].vat_amount'        => 'Required|FloatGeLt:0,**********|>>>:' . self::$t['payment_vat_amount_error'],
            'amount_detail[*].amount_has_tax'    => 'Required|FloatGt:0|>>>:' . self::$t['payment_amount_has_tax_error'],
            'amount_detail[*].cost_center_code'  => 'StrLenGeLe:0,255|>>>:' . self::$t['payment_upload_error_016'],
            'amount_detail[*].ledger_account_id' => 'IntGe:0|>>>:' . self::$t['payment_upload_error_017'],
            'amount_detail[*].landlord_tax_no'   => 'StrLenGeLe:0,30|>>>:' . self::$t['payment_amount_landlord_tax_no'],
            'amount_detail[*].index'             => 'Required|IntGe:0|>>>:index error',
        ];

        //V22269 金额详情 - 网点/总部必填拦截
        if (!empty($params['cost_store_type'])) {
            //费用所属网点：1-总部； 2-网点
            if ($params['cost_store_type'] == 1) {
                $validate_param['amount_detail[*].cost_department_id']          = 'Required|IntGt:0';
                $validate_param['amount_detail[*].cost_center_department_name'] = 'Required|StrLenGeLe:1,255';
            } elseif ($params['cost_store_type'] == 2) {
                $validate_param['amount_detail[*].store_id']   = 'Required|StrLenGeLe:2,32|>>>:' . self::$t['payment_upload_error_001'];
                $validate_param['amount_detail[*].store_name'] = 'Required|StrLenGeLe:1,255|>>>:' . self::$t['payment_upload_error_001'];
            }
        }
        return $validate_param;
    }

    // 支付信息校验规则
    public static $validate_pay_param = [
        'id' => 'Required|IntGe:1|>>>:id param error',
        'is_pay' => 'Required|IntIn:2,3|>>>:whether paid param error',
        'pay_date' =>'IfIntEq:is_pay,2|Required|Date|>>>:payment date param error',
        'remark' =>'IfIntEq:is_pay,3|Required|StrLenGeLe:1,5000|>>>:remark param error',
    ];
    // 支付信息校验规则 必填单独校验
    public static $validate_pay_required_param = [
        'pay_bank_name' =>'IfIntEq:is_pay,2|Required|StrLenGeLe:1,255|>>>:payment bank param error',
        'pay_bank_account' =>'IfIntEq:is_pay,2|Required|StrLenGeLe:1,200|>>>:pay bank account param error',
    ];

    // 详情查看校验
    public static $validate_detail_param = [
        'id' => 'Required|IntGe:1|>>>:id param error',
    ];

    // url校验
    public static $validate_url_param = [
        'name' => 'Required|StrLenGeLe:1,256|>>>:param error',
        'url' => 'Required|StrLenGeLe:64,1024|>>>:param error',
    ];

    // 网点租房合同搜索验证规则
    public static $validate_contract_search_param = [
        'contract_no' => 'Required|StrLenGeLe:1,20|>>>:contract_no param error',
    ];

    // 费用所属中心搜索验证规则
    public static $validate_cost_center_search_param = [
        'cost_center_name' => 'StrLenGeLe:0,50',
    ];

    // 网点搜索验证规则
    public static $validate_store_search_param = [
        'store_name' => 'Required|StrLenGeLe:1,255|>>>:store_name param error',
    ];

    private static $validate_update = [
        'id' => 'Required|IntGe:1|>>>:id param error'
    ];

    // 节点更新数据校验
    public static function getValidateUpdateParam(){
        return [
            'detail_id' => 'Required|IntGe:1|>>>:detail_id param error',
            'wht_category_id' => 'Required|IntGe:0|>>>:' . self::$t['payment_upload_error_007'],
            'wht_tax_rate_id' => 'Required|FloatGe:0|>>>:' . self::$t['payment_upload_error_008'],
            'ledger_account_id' => 'IntGe:0|>>>:' . self::$t['payment_upload_error_017'],
        ];
    }

    // 9888【OA|网点租房付款】功能优化 获取成本中心部门参数检测
    public static $validate_department = [
        'type' => 'Required|IntIn:'.self::COST_TYPE_HEAD_OFFICE.','.self::COST_TYPE_SYS_STORE,
    ];
    //总部的成本中心需要检测部门ID合法性
    public static $validate_cost_department_id = [
        'department_id' => 'Required|IntGe:1',
    ];
    //网点类型的成本中心需要检测网点ID合法性
    public static $validate_cost_store_id = [
         'department_id' => 'Required|StrLenGeLe:1,10',
    ];
    //费用类型ID检测合法性
    public static $validate_cost_type_id = [
        'cost_type_id' => 'Required|IntGe:1',
    ];
    public static $validate_supplement = [
        'id'                       => 'Required|IntGe:1',
        'required_supplement_file' => 'Required|ArrLenGeLe:0,50'
    ];

    //网点租房付款 - 付款审核-待处理-批量审核-汇总数据
    public static $validate_batch_audit_summary = [
        'ids' => 'Required|Arr|ArrLenGe:1',
        'ids[*]' => 'Required|IntGe:1'
    ];

    /**
     * 过滤空值 和 非必要参数
     * @param array $params
     * @param array $not_must
     * @return mixed
     */
    public static function handleParams(array $params, array $not_must)
    {
        $params = array_filter($params);

        if (empty($not_must) || !is_array($not_must)) {
            return $params;
        }

        foreach ($not_must as $value) {
            if (isset($params[$value])) {
                unset($params[$value]);
            }
        }

        return $params;
    }

    /**
     * 获取参数验证的规则
     * @param bool $is_update
     * @return array
     */
    public static function getValidateParams(bool $is_update = false)
    {
        $rules = [];
        if ($is_update) {
            $rules = array_merge($rules, self::$validate_update);
        }

        return array_merge(self::$validate_param, $rules);
    }

    /**
     * 获取用户信息
     * @param string $user_id
     * @return array
     */
    public function getHrStaffInfoById(string $user_id)
    {
        $model = (new UserService())->getUserByIdInRbi($user_id);
        if(empty($model)){
            return [];
        }

        return $model->toArray();
    }

    /**
     * 去bi里面取相关数据
     * @param int $userId
     * @return array
     */
    public function getUserMetaFromBi(int $userId)
    {
        $model = (new UserService())->getUserByIdInRbi($userId);
        if(empty($model)){
            return [];
        }

        $data = [];
        $data['create_id']  = $model->staff_info_id ?? '';
        $data['create_name'] = $model->name ?? '';
        $data['create_nick_name'] = $model->nick_name ?? '';
        $data['create_email'] = $model->email ?? '';

        // 一级部门
        $data['create_department_id'] = $model->sys_department_id;
        $data['create_department_name'] = '';
        $data['create_company_id'] = '';
        $data['create_company_name'] = '';

        // 二级部门
        $data['create_node_department_id'] = $model->node_department_id;
        $data['create_node_department_name'] = '';
        $data['create_node_company_id'] = '';
        $data['create_node_company_name'] = '';

        $department = DepartmentModel::find([
            "conditions" => "id in (?0,?1)",
            "bind" => [
                0 => $data['create_department_id'],
                1 => $data['create_node_department_id']
            ]
        ]);

        if (!empty($department)) {
            $department_item = array_column($department->toArray(),null,'id');

            $department_info = $department_item[$data['create_department_id']] ?? [];
            $node_department_info = $department_item[$data['create_node_department_id']] ?? [];

            $data['create_department_name'] = $department_info ? $department_info['name'] : '';
            $data['create_node_department_name'] = $node_department_info ? $node_department_info['name'] : '';

            $data['create_company_name'] = $department_info['type'] == 2 ? $department_info['company_name'] : '';
            $data['create_node_company_name'] = $node_department_info['type'] == 2 ? $node_department_info['company_name'] : '';

            $data['create_company_id'] = $department_info['type'] == 2 ? $department_info['company_id'] : 0;
            $data['create_node_company_id'] = $node_department_info['type'] == 2 ? $node_department_info['company_id'] : 0;
        }


        $data['create_display_department_id'] = $data['create_node_department_id'] ? $data['create_node_department_id'] : $data['create_department_id'];
        $data['create_display_department_name'] = $data['create_node_department_name'] ? $data['create_node_department_name'] : $data['create_department_name'];
        $data['create_display_company_name'] = $data['create_node_company_name'] ? $data['create_node_company_name'] : $data['create_company_name'];
        $data['create_display_company_id'] = $data['create_node_company_id'] ? $data['create_node_company_id'] : $data['create_company_id'];

        // 申请人的所属中心/部门
        $pc_code_info = $this->getPcCodeInfo((int)$data['create_display_department_id']);
        $data['create_center_id'] = $pc_code_info['id'] ?? 0;
        $data['create_center_name'] = $pc_code_info['pc_code'] ?? '';
        $data['create_center_department_name'] = $pc_code_info['department_name'] ?? '';
        $data['create_center_department_id'] = $pc_code_info['department_id'] ?? 0;

        $job = $model->getJobTitle();

        $data['create_job_title_name'] = '';
        $data['create_job_title_id'] = $model->job_title;
        $data['create_job_title_level'] = $model->job_title_level;
        if (!empty($job)) {
            $data['create_job_title_name'] = $job->job_name;
        }

        return $data;
    }

    /**
     * 根据部门id 获取 对应的pc_code信息
     * @param int $department_id
     * @return
     */
    protected function getPcCodeInfo(int $department_id)
    {
        if (empty($department_id)) {
            return [];
        }

        $condition = [
            'conditions' => 'department_id = :department_id:',
            'bind' => ['department_id' => $department_id],
            'columns' => ['id', 'department_id', 'department_name', 'pc_code']
        ];

        $data = Pccode::getFirst($condition);
        return $data ? $data->toArray() : [];
    }

    /**
     * 获取已归档的网点租房合同
     * @param string $contract_no
     * @return array
     */
    protected function getArchiveStoreRentingContract(string $contract_no = '')
    {
        try {
            $builder = $this->modelsManager->createBuilder();
            $builder->from(['store' => ContractStoreRentingModel::class]);
            $builder->leftjoin(ContractArchive::class, 'store.contract_id = archive.cno', 'archive');
            if ('MY' == get_country_code()) {
                $builder->where('archive.status in(1,2,4)');
            } else {
                $builder->where('archive.status in(2,4)');
            }

            if (!empty($contract_no)) {
                $builder->andWhere('store.contract_id LIKE :contract_id:', ['contract_id' => "%$contract_no%"]);
            }

            $builder->columns([
                'store.id',
                'store.contract_effect_date',
                'store.id AS contract_id',
                'archive.cno AS contract_no',
                'store.store_id',
                // 合同开始时间替换为生效时间
                // 'store.contract_begin AS contract_begin_date',
                'store.contract_effect_date AS contract_begin_date',
                'store.contract_end AS contract_end_date',
                'store.bank_collection AS bank_collection',
                'store.contract_tax_no',
                'store.rent_due_date',
                'store.is_main',
                'store.house_owner_type'
            ]);

            $items = $builder->getQuery()->execute()->toArray();
            $rent_due_arr = ContractStoreRentingService::getInstance()->getRentDueDate();
            foreach ($items as &$item) {
                $item['rent_due_date'] = $rent_due_arr[$item['rent_due_date']] ?? '';
            }

            return $items;

        } catch (\Exception $e) {
            $logger = $this->getDI()->get('logger');
            $logger->warning('付款管理 - 网点租房金额详情上传 - 获取网点租房合同, 获取异常: ' . $e->getMessage());
        }

        return [];
    }

    /**
     * 获取费用所属中心列表
     * @param array $params
     * @return array
     */
    protected function getAllCostCenterList($params = [])
    {
        try {
            $cost_center_name = $params['cost_center_name'] ?? '';
            if ('' == $cost_center_name) {
                $pcCodeParam = [
                    'columns' => ['id', 'pc_code']
                ];
                $otherPcCodeParam = [
                    'columns' => ['id', 'pc_code']
                ];
            } else {
                $pcCodeParam = [
                    'conditions' => 'pc_code like :pc_code:',
                    'bind' => ['pc_code' => '%'.$cost_center_name.'%'],
                    'columns' => ['id', 'pc_code']
                ];
                $otherPcCodeParam = [
                    'conditions' => 'pc_code like :pc_code:',
                    'bind' => ['pc_code' => '%'.$cost_center_name.'%'],
                    'columns' => ['id', 'pc_code']
                ];
            }
            $pcCodeList = Pccode::find($pcCodeParam)->toArray();
            $pcCodeList = array_column($pcCodeList,null,'pc_code');
            $otherPcCodeList = OtherPccode::find($otherPcCodeParam)->toArray();
            $otherPcCodeList = array_column($otherPcCodeList,null,'pc_code');

            return array_values(array_merge($pcCodeList,$otherPcCodeList));
        } catch (\Exception $e) {
            $logger = $this->getDI()->get('logger');
            $logger->warning('付款管理 - 网点租房金额详情上传 - 获取网点租房合同, 获取异常: ' . $e->getMessage());
        }

        return [];
    }

    /**
     * 根据一组合同编号获取对应的合同列表
     * @param array $params 合同编号组
     * @return array
     */
    protected function getStoreRentingContractList($params = [])
    {
        try {
            $params = array_filter(array_unique($params));

            if (empty($params)) {
                return [];
            }

            $builder = $this->modelsManager->createBuilder();
            $builder->from(['store' => ContractStoreRentingModel::class]);
            $builder->where('store.contract_status in(1,3)');
            $builder->inWhere('store.contract_id', $params);
            $builder->columns([
                'store.id AS contract_id',
                'store.contract_id AS contract_no',
                'store.store_id',
                'store.contract_begin AS contract_begin_date',
                'store.contract_end AS contract_end_date',
                'store.contract_effect_date',
                'store.rent_due_date',
                'store.is_main',
                'store.house_owner_type'
            ]);

            return $builder->getQuery()->execute()->toArray();
        } catch (\Exception $e) {
            $this->logger->warning('付款管理 - 网点租房金额详情上传 - 获取网点租房合同, 获取异常: ' . $e->getMessage());
        }

        return [];
    }


    /**
     * 获取系统所有网点: 总部 与 网点合并
     * @param array $params
     * @param array $user
     * @param bool $is_append_header_office
     * @return array
     */
    protected function getSysStoreList(array $params = [], array $user = [], bool $is_append_header_office = false)
    {
        $data = [];

        if ($is_append_header_office) {
            $data[] = [
                'id' => Enums::PAYMENT_HEADER_STORE_ID,
                'name' => Enums::PAYMENT_HEADER_STORE_NAME
            ];
        }

        $store = (new StoreService())->getSysStoreListByCondition($params);
        return $store ? trim_array(array_merge($data, $store)) : $data;
    }

    /**
     * 获取费用类型名称列表 name => key
     * @param string $data_channel
     * @return array
     */
    protected function getCostItem(string $data_channel = 'upload')
    {
        $cost_item = EnumsService::getInstance()->getStoreRentPaymentCostTypeItem();
        if ($data_channel == 'upload') {
            unset($cost_item[self::NOT_ALLOW_BATCH_UPLOAD_COST_TYPES]);
        }

        $data = [];
        foreach ($cost_item as $key => $value) {
            $data[self::$t[$value]] = $key;
        }

        return $data;
    }

    /**
     * 获取费用所属网点类型列表
     */
    public function getCostStoreTypeItem()
    {
        $cost_store_type_item = Enums::$payment_cost_store_type;

        $cost_store_type_item_tmp = [];
        foreach ($cost_store_type_item as $index => $t_key) {
            $cost_store_type_item_tmp[] = [
                'id' => $index,
                'label' => self::$t[$t_key]
            ];
        }

        return $cost_store_type_item_tmp;
    }

    /**
     * 获取付款方式列表
     */
    public function getPaymentMethodItem()
    {
        $item = Enums::$payment_method;

        $tmp_item = [];
        foreach ($item as $index => $t_key) {
            $tmp_item[] = [
                'id' => $index,
                'label' => self::$t[$t_key],
                'is_default' => in_array($index, Enums::$payment_method_default) ? 1 : 0,
            ];
        }

        return $tmp_item;
    }

    /**
     * 获取付款币种列表
     */
    public function getPaymentCurrencyItem()
    {
        $item = GlobalEnums::$currency_item;

        $tmp_item = [];
        foreach ($item as $index => $t_key) {
            $tmp_item[] = [
                'id' => $index,
                'label' => self::$t[$t_key],
                'is_default' => in_array($index, Enums::$payment_currency_default) ? 1 : 0,
            ];
        }

        return $tmp_item;
    }

    /**
     * 获取合同状态列表
     */
    public function getContractStatusItem()
    {
        $item = Enums::$payment_contract_status;

        $tmp_item = [];
        foreach ($item as $index => $t_key) {
            $tmp_item[] = [
                'id' => $index,
                'label' => self::$t[$t_key],
            ];
        }

        return $tmp_item;
    }

    /**
     * 获取费用类型列表
     * @return array
     */
    public function getCostTypeItem()
    {
        $item = EnumsService::getInstance()->getStoreRentPaymentCostTypeItem();
        $tmp_item = [];
        if (!empty($item)) {
            foreach ($item as $index => $t_key) {
                //根据费用类型ID获取核算科目列表
                $list = StoreRentingAddService::getInstance()->getLedgerInfoByCostTypeId($index);
                //产品说费用类型跟核算科目是一对一的关系
                $ledger = !empty($list['data']) ?$list['data'][0]['id'] : 0;
                $tmp_item[] = [
                    'id' => $index,
                    'label' => self::$t[$t_key],
                    'ledger' => $ledger,
                ];
            }
        }
        return $tmp_item;
    }

    /**
     * 获取核算科目列表
     * @return array
     */
    public function getLedgerItem()
    {
        $tmp_item = [];
        $list = StoreRentingAddService::getInstance()->getLedgerInfoByCostTypeId();
        if (!empty($list['data'])) {
            foreach ($list['data'] as $values) {
                $tmp_item[] = [
                    'id' => $values['id'],
                    'label' => $values['name_en'],
                ];
            }
        }
        return $tmp_item;
    }

    /**
     * 获取付款支付权限的工号清单
     * @return array
     */
    public function getPayAuthStaffIdItem()
    {
        $staff_ids = EnvModel::getEnvByCode(Enums::PAYMENT_PAY_STAFF_IDS_KEY);
        $staff_id_item = explode(',', $staff_ids);
        return $staff_id_item ? $staff_id_item : [];
    }

    /**
     * 获取所有网点审核中 和 审核通过的租房申请
     * @param array $conditions
     * @return array
     */
    public function getAllStoreApplyCostByConditions(array $conditions = [])
    {
        if (empty($conditions)) {
            return [];
        }
        if (self::$memory_limit) {
            ini_set('memory_limit', '512M');
        }

        try {
            $builder = $this->modelsManager->createBuilder();
            $builder->from(['main' => PaymentStoreRenting::class]);
            $builder->leftjoin(PaymentStoreRentingDetail::class, 'main.id = detail.store_renting_id', 'detail');
            if (!empty($conditions['approval_status'])) {
                $builder->inWhere('main.approval_status', $conditions['approval_status']);
            }
            // 未支付忽略掉
            $builder->inWhere('main.pay_status',[Enums::PAYMENT_PAY_STATUS_PENDING,Enums::PAYMENT_PAY_STATUS_PAY]);

            if (isset($conditions['is_contract']) && in_array($conditions['is_contract'], [0, 1])) {
                $builder->andWhere('detail.is_contract = :is_contract:',  ['is_contract' => $conditions['is_contract']]);
            }

            $builder->columns([
                'detail.store_id',
                'detail.contract_no',
                'detail.store_name',
                'detail.cost_type',
                'detail.cost_start_date',
                'detail.cost_end_date',
                'main.pay_status'
            ]);

            return $builder->getQuery()->execute()->toArray();

        } catch (\Exception $e) {
            $logger = $this->getDI()->get('logger');
            $logger->warning('付款管理 - 网点与费用类型/起止时间列表, 获取异常: ' . $e->getMessage());
        }

        return [];
    }

    /**
     * 同费用类型网点：上传费用日期 与 已存在的网点费用日期比较
     * 逻辑：只能在空闲日期段内
     * @param array $upload_data
     * @param array $store_contract_available_date_item
     * @return mixed
     */
    public function compareUploadCostDateAndExistStoreCostDate(array $upload_data, array $store_contract_available_date_item)
    {
        if (empty($upload_data)) {
            return false;
        }
        if (empty($store_contract_available_date_item)) {
            return true;
        }

        // 当前合同未上传过
        $upload_key = $upload_data['contract_no'] . '_' . $upload_data['cost_type_id'];
        if (empty($store_contract_available_date_item[$upload_key])) {
            return true;
        }

        // 押金项
        $cost_type_ids = EnvModel::getEnvByCode('deposit_amount_cost_type_ids','');
        $cost_type_ids = !empty($cost_type_ids) ? explode(',',$cost_type_ids) : [];
        // 是否都为押金项
        if (in_array($upload_data['cost_type_id'],$cost_type_ids)) {
            return true;
        }

        // 上传过, 验证是否在空闲日期段内
        foreach ($store_contract_available_date_item as $data) {
            // 同一合同/网点/同费用类型, 有可选的费用日期区间
            if ($data['is_available_date'] && $data['contract_no'] == $upload_data['contract_no'] && ($data['cost_type'] == $upload_data['cost_type_id'])) {
                // 是否在可用日期区间内
                foreach ($data['available_date_item'] as $_tmp_date) {
                    if (Enums::LOAN_PAY_STATUS_NOTPAY == $data['pay_status'] || ($upload_data['cost_start_date'] >= $_tmp_date['cost_start_date'] && $upload_data['cost_end_date'] <= $_tmp_date['cost_end_date'])) {
                        return true;
                    }
                }
            }
        }

        return false;
    }

    /**
     * 上传的合同网点费用日期比较
     * 逻辑：每项的费用日期不可交叉
     * @param array $curr_cost_data
     * @param array $upload_cost_data_item
     * @return array
     */
    public function compareUploadCostDate(array $curr_cost_data, array $upload_cost_data_item)
    {
        $return = [
            'result' => false,
            'code' => 1,
        ];

        if (empty($curr_cost_data) || empty($upload_cost_data_item)) {
            return $return;
        }

        // 押金项
        $cost_type_ids = EnvModel::getEnvByCode('deposit_amount_cost_type_ids','');
        $cost_type_ids = !empty($cost_type_ids) ? explode(',',$cost_type_ids) : [];
        // 是否都为押金项
        if (in_array($curr_cost_data['cost_type_id'],$cost_type_ids)) {
            $return['result'] = true;
            return $return;
        }

        // 删除列表中的当前待验证的项
        unset($upload_cost_data_item[$curr_cost_data['key']]);

        foreach ($upload_cost_data_item as $data) {
            // 同一合同/网点/同费用类型
            if (($data['contract_no'] == $curr_cost_data['contract_no']) && ($data['cost_type_id'] == $curr_cost_data['cost_type_id'])) {

                // 日期范围是否交叉
                if (($curr_cost_data['cost_start_date'] >= $data['cost_start_date']) && ($curr_cost_data['cost_start_date'] <= $data['cost_end_date'])) {
                    $return['code'] = 1;
                    return $return;
                }

                if (($curr_cost_data['cost_end_date'] >= $data['cost_start_date']) && ($curr_cost_data['cost_end_date'] <= $data['cost_end_date'])) {
                    $return['code'] = 2;
                    return $return;
                }

            }
        }

        $return['result'] = true;
        return $return;
    }

    /**
     * 提取网点合同可用的日期范围
     * PS: 归并同一合相同费用类型的费用起止日期
     * @param array $store_contract_list
     * @param array $contract_list
     * @return array
     * @mixed array
     */
    protected function extractAvailableStoreContractDate(array $store_contract_list = [], array $contract_list = [])
    {
        if (empty($store_contract_list) || empty($contract_list)) {
            return [];
        }

        // 归并同一合同编号, 同一费用类型的数据
        $_tmp_data = [];
        foreach ($store_contract_list as $k => $v) {
            $_tmp_data[$v['contract_no'].'_'.$v['cost_type']][] = $v;
        }

        unset($store_contract_list);

        // 计算未占用的空闲日期段
        $return_data = [];
        foreach ($_tmp_data as $c_s_k => $c_s_v) {
            // 保存计算出的可用的日期时段
            $_tmp_available_date_item = [];

            // 合同起止日期数据
            $contract_data = $contract_list[$c_s_v[0]['contract_no']] ?? [];

            // 审核中/通过合同分段数
            // [1] 计算每段日期中间间隔的空闲日期段
            $c_s_v = array_sort($c_s_v, 'cost_start_date', SORT_ASC);

            do {
                $current_element = current($c_s_v);
                $next_element = next($c_s_v);

                if ($next_element && ($current_element['cost_end_date'] < $next_element['cost_start_date'])) {
                    $_tmp_available_date_item[] = [
                        'cost_start_date' => $current_element['cost_end_date'],
                        'cost_end_date' => $next_element['cost_start_date'],
                    ];
                }

            } while (current($c_s_v));

            $first_section_data = reset($c_s_v);
            $last_section_data = end($c_s_v);

            // [2] 计算首尾空闲日期段
            if (!empty($contract_data)) {
                if ($contract_data['contract_begin_date'] < $first_section_data['cost_start_date']) {
                    $_tmp_available_date_item[] = [
                        'cost_start_date' => $contract_data['contract_begin_date'],
                        'cost_end_date' => $first_section_data['cost_start_date'],
                    ];
                }

                if ($contract_data['contract_end_date'] > $last_section_data['cost_end_date']) {
                    $_tmp_available_date_item[] = [
                        'cost_start_date' => $last_section_data['cost_end_date'],
                        'cost_end_date' => $contract_data['contract_end_date'],
                    ];
                }
            }

            // 合同七日时段计算结果
            $_tmp_contract_data = [
                'contract_no' => $first_section_data['contract_no'],
                'store_id' => $first_section_data['store_id'],
                'store_name' => $first_section_data['store_name'],
                'cost_type' => $first_section_data['cost_type'],
                'pay_status' => $first_section_data['pay_status'],

                // 是否有可共提交的日期
                'is_available_date' => $_tmp_available_date_item ? true : false,
                'available_date_item' => $_tmp_available_date_item,
            ];

            $return_data[$c_s_k] = $_tmp_contract_data;
        }

        unset($_tmp_data);

        return $return_data;
    }

    public function isCanDownload($item, $uid){
        if(empty($item)){
            return '0';
        }

        if(empty($uid)){
            return '1';
        }

        if($item['approval_status'] != Enums::WF_STATE_APPROVED){
            return '0';
        }

        return "1";
    }

    /**
     * 获取租房付款附件清单配置
     * @return array
     */
    public function getAttachmentAllConfig()
    {
        static $config_item = [];
        if (empty($config_item)) {
            $config_item = EnumsService::getInstance()->getSettingEnvValueMap('payment_store_renting_attachment_config');
            if (!empty($config_item)) {
                foreach ($config_item['attachment_list'] as &$attachment) {
                    $attachment['file_name_label'] = static::$t->_($attachment['file_name_key']);
                    unset($attachment['file_name_key'], $attachment['file_desc']);
                }
            }
        }
        return $config_item;
    }
}
