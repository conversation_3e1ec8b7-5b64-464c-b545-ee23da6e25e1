<?php

namespace App\Modules\Setting\Controllers;

use App\Library\Enums;
use App\Library\Enums\ReimbursementEnums;
use App\Library\ErrCode;
use App\Library\Exception\BusinessException;
use App\Library\Validation\Validation;
use App\Library\Validation\ValidationException;
use App\Modules\Common\Services\EnumsService;
use App\Modules\Reimbursement\Services\ConfigService as ReimbursementConfigService;
use App\Modules\PaperDocument\Services\ConfirmationService;
use app\modules\PaperDocument\services\ConfirmStaffService;
use App\Modules\Setting\Services\BaseService;
use App\Modules\Setting\Services\BusinessService;
use App\Modules\Setting\Services\LedgerService;
use App\Modules\Setting\Services\LoanStaffService;
use App\Modules\Setting\Services\MaterialService;
use App\Modules\Setting\Services\PaymentModeService;
use App\Modules\Setting\Services\SupplierBankService;
use App\Modules\Setting\Services\SettingInvoiceHeaderService;
use App\Modules\Setting\Services\WorkflowPayProgressMarkService;
use App\Modules\Shop\Services\GoodsManagementService;
use App\Util\RedisKey;
use Exception;
use Phalcon\Http\Response;
use Phalcon\Http\ResponseInterface;

class BusinessSettingController extends BaseController
{
    /**
     * 业务配置应用模块列表
     * @Permission(action='setting.business.get')
     *
     * @return Response|ResponseInterface
     */
    public function getBusinessModulesAction()
    {
        $params = trim_array($this->request->get());

        $list = BusinessService::getInstance()->getBusinessModulesList($params);

        return $this->returnJson(ErrCode::$SUCCESS, 'ok', $list);
    }

    /**
     * 开户行列表
     * @Permission(action='setting.business.get')
     * @return mixed
     * @throws ValidationException
     */
    public function getSupplierBankListAction()
    {
        $params = $this->request->get();

        Validation::validate($params, SupplierBankService::$validate_list_search);

        $list = SupplierBankService::getInstance()->getSupplierBankList($params);

        return $this->returnJson(ErrCode::$SUCCESS, 'ok', $list);
    }

    /**
     * 生成开户行编号
     * @Permission(action='setting.business.edit.add')
     *
     * @return Response|ResponseInterface
     * @throws Exception
     */
    public function genSerialNoAction()
    {
        // 员工权限校验
        $this->getSettingAuthorize();

        $lockKey  = md5('gen_bank_serial_no_' . $this->user['id']);
        $serialNo = $this->atomicLock(function () {
            return SupplierBankService::getInstance()->generateSerialNo();
        }, $lockKey, 10);

        if (is_string($serialNo) && $serialNo != false) {
            $code    = ErrCode::$SUCCESS;
            $message = 'ok';
            $data    = ['serial_no' => $serialNo];
        } else {
            $code    = ErrCode::$FREQUENT_VISIT_ERROR;
            $message = $this->t['sys_processing'];
            $data    = [];
        }

        return $this->returnJson($code, $message, $data);
    }

    /**
     * 开户行新增
     * @Permission(action='setting.business.edit.add')
     *
     * @return Response|ResponseInterface
     * @throws ValidationException
     */
    public function addSupplierBankAction()
    {
        // 员工权限校验
        $this->getSettingAuthorize();

        $data = $this->request->get();

        Validation::validate($data, SupplierBankService::$validate_params);

        $res = SupplierBankService::getInstance()->saveOne($data, $this->user);
        return $this->returnJson($res['code'], $res['message']);
    }

    /**
     * 开户行编辑
     * @Permission(action='setting.business.edit.edit')
     *
     * @return Response|ResponseInterface
     * @throws ValidationException
     */
    public function editSupplierBankAction()
    {
        // 员工权限校验
        $this->getSettingAuthorize();

        $data = $this->request->get();

        Validation::validate($data, SupplierBankService::$edit_validate_params);

        $res = SupplierBankService::getInstance()->editOne($data, $this->user);
        return $this->returnJson($res['code'], $res['message']);
    }

    /**
     * 核算科目列表查询
     * @Permission(action='setting.business.get')
     *
     * @return Response|ResponseInterface
     * @throws ValidationException
     */
    public function getLedgerAccountListAction()
    {
        $params = $this->request->get();

        Validation::validate($params, LedgerService::$validate_list_search);

        $list = LedgerService::getInstance()->getSupplierBankList($params);
        return $this->returnJson(ErrCode::$SUCCESS, 'ok', $list);
    }

    /**
     * 核算科目新增
     * @Permission(action='setting.business.edit.add')
     *
     * @return Response|ResponseInterface
     * @throws ValidationException
     */
    public function addLedgerAccountAction()
    {
        // 员工权限校验
        $this->getSettingAuthorize();

        $data = $this->request->get();

        Validation::validate($data, LedgerService::$validate_params);

        $res = LedgerService::getInstance()->saveOne($data, $this->user);
        return $this->returnJson($res['code'], $res['message']);
    }

    /**
     * 核算科目编辑
     * @Permission(action='setting.business.edit.edit')
     *
     * @return Response|ResponseInterface
     * @throws ValidationException
     */
    public function editLedgerAccountAction()
    {
        // 员工权限校验
        $this->getSettingAuthorize();

        $data = $this->request->get();

        Validation::validate($data, LedgerService::$edit_validate_params);

        $res = LedgerService::getInstance()->editOne($data, $this->user);
        return $this->returnJson($res['code'], $res['message']);
    }

    /**
     * 核算科目单个删除
     * @Permission(action='setting.business.edit.delete')
     *
     * @return Response|ResponseInterface
     * @throws ValidationException
     */
    public function deleteLedgerAction()
    {
        // 员工权限校验
        $this->getSettingAuthorize();

        $data = $this->request->get();

        Validation::validate($data, LedgerService::$single_validate_params);

        $res = LedgerService::getInstance()->batchDelete([$data['ledger_id']], $this->user);
        return $this->returnJson($res['code'], $res['message']);
    }

    /**
     * 核算科目批量删除
     * @Permission(action='setting.business.edit.delete')
     *
     * @return Response|ResponseInterface
     * @throws ValidationException
     */
    public function batchDeleteLedgerAction()
    {
        // 员工权限校验
        $this->getSettingAuthorize();

        $data = $this->request->get();

        Validation::validate($data, LedgerService::$delete_validate_params);

        $res = LedgerService::getInstance()->batchDelete($data['ledger_id'], $this->user);
        return $this->returnJson($res['code'], $res['message']);
    }

    /**
     * 产品编号查询
     * @Permission(action='setting.business.get')
     *
     * @return Response|ResponseInterface
     * @throws ValidationException
     */
    public function getMaterialListAction()
    {
        $params = $this->request->get();

        Validation::validate($params, MaterialService::$validate_list_search);

        $list = MaterialService::getInstance()->getMaterialList($params);

        return $this->returnJson(ErrCode::$SUCCESS, 'ok', $list);
    }

    /**
     * 产品编号新增
     * @Permission(action='setting.business.edit.add')
     *
     * @return Response|ResponseInterface
     * @throws ValidationException
     */
    public function addMaterialAction()
    {
        // 员工权限校验
        $this->getSettingAuthorize();

        $data = $this->request->get();

        Validation::validate($data, MaterialService::$validate_params);

        $res = MaterialService::getInstance()->saveOne($data, $this->user);
        return $this->returnJson($res['code'], $res['message']);
    }

    /**
     * 产品编号编辑
     * @Permission(action='setting.business.edit.edit')
     *
     * @return Response|ResponseInterface
     * @throws ValidationException
     */
    public function editMaterialAction()
    {
        // 员工权限校验
        $this->getSettingAuthorize();

        $data = $this->request->get();

        Validation::validate($data, MaterialService::$edit_validate_params);

        $res = MaterialService::getInstance()->editOne($data, $this->user);
        return $this->returnJson($res['code'], $res['message']);
    }

    /**
     * 产品编号单个删除
     * @Permission(action='setting.business.edit.delete')
     *
     * @return Response|ResponseInterface
     * @throws ValidationException
     */
    public function deleteMaterialAction()
    {
        // 员工权限校验
        $this->getSettingAuthorize();

        $data = $this->request->get();

        Validation::validate($data, MaterialService::$single_validate_params);

        $res = MaterialService::getInstance()->batchDelete([$data['id']], $this->user);
        return $this->returnJson($res['code'], $res['message']);
    }

    /**
     * 产品编号批量导入
     * @Permission(action='setting.business.edit.add')
     *
     * @return Response|ResponseInterface
     * @throws ValidationException
     */
    public function batchImportMaterialAction()
    {
        // 员工权限校验
        $this->getSettingAuthorize();

        $param      = $this->request->get();
        $excel_file = $this->request->getUploadedFiles();

        $res = MaterialService::getInstance()->batchImport($param, $excel_file, $this->user);
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }

    /**
     * 导入模板下载
     * @Token
     *
     */
    public function downloadTemplateAction()
    {
        // 加锁处理
        $lock_key = md5('product_no_import_template_download_' . $this->user['id']);
        $res      = $this->atomicLock(function () {
            return MaterialService::getInstance()->importTemplate();
        }, $lock_key, 10);

        if (isset($res['code'])) {
            $code    = $res['code'];
            $message = $res['message'];
            $data    = $res['data'];
        } else {
            $code    = ErrCode::$FREQUENT_VISIT_ERROR;
            $message = $this->t['sys_processing'];
            $data    = [];
        }

        return $this->returnJson($code, $message, $data);
    }


    /**
     * 借款员工配置列表
     * @Permission(action='setting.business.get')
     * @return Response|ResponseInterface
     * @throws ValidationException
     */
    public function getLoanStaffListAction()
    {
        $params = $this->request->get();

        $params = BaseService::handleParams($params, ['staff_info_id']);
        Validation::validate($params, LoanStaffService::$validate_list_search);

        $list = LoanStaffService::getInstance()->getLoanStaffList($params);

        return $this->returnJson(ErrCode::$SUCCESS, 'ok', $list);
    }

    /**
     * 借款员工配置新增
     * @Permission(action='setting.business.edit.add')
     * @return Response|ResponseInterface
     * @throws ValidationException
     */
    public function addLoanStaffAction()
    {
        // 员工权限校验
        $this->getSettingAuthorize();

        $data = $this->request->get();

        Validation::validate($data, LoanStaffService::$add_validate_params);

        $res = LoanStaffService::getInstance()->saveOne($data, $this->user);
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }

    /**
     * 借款员工配置编辑
     * @Permission(action='setting.business.edit.edit')
     * @return Response|ResponseInterface
     * @throws ValidationException
     */
    public function editLoanStaffAction()
    {
        // 员工权限校验
        $this->getSettingAuthorize();

        $data = $this->request->get();

        Validation::validate($data, LoanStaffService::$edit_validate_params);

        $res = LoanStaffService::getInstance()->editOne($data, $this->user);
        return $this->returnJson($res['code'], $res['message']);
    }

    /**
     * 借款员工配置删除
     * @Permission(action='setting.business.edit.delete')
     * @return Response|ResponseInterface
     * @throws ValidationException
     */
    public function deleteLoanStaffAction()
    {
        // 员工权限校验
        $this->getSettingAuthorize();

        $data = $this->request->get();

        Validation::validate($data, LoanStaffService::$delete_validate_params);

        $res = LoanStaffService::getInstance()->deleteLoanStaff($data['id'], $this->user);
        return $this->returnJson($res['code'], $res['message']);
    }

    /**
     * 付款模式列表
     * @Permission(action='setting.business.get')
     * @return mixed
     * @throws ValidationException
     */
    public function getPaymentModeListAction()
    {
        $params = $this->request->get();

        Validation::validate($params, PaymentModeService::$validate_list_search);

        $list = PaymentModeService::getInstance()->getPaymentModeList($params);

        return $this->returnJson(ErrCode::$SUCCESS, 'ok', $list);
    }

    /**
     * 付款模式key编号
     * @Permission(action='setting.business.edit.add')
     *
     * @return Response|ResponseInterface
     * @throws Exception
     */
    public function getPaymentModeNoAction()
    {
        // 员工权限校验
        $this->getSettingAuthorize();

        $lockKey  = md5('gen_payment_mode_serial_no_' . $this->user['id']);
        $serialNo = $this->atomicLock(function () {
            return PaymentModeService::getInstance()->generateSerialNo();
        }, $lockKey, 10);

        if (is_string($serialNo) && $serialNo != false) {
            $code    = ErrCode::$SUCCESS;
            $message = 'ok';
            $data    = ['payment_mode_code' => $serialNo];
        } else {
            $code    = ErrCode::$FREQUENT_VISIT_ERROR;
            $message = $this->t['sys_processing'];
            $data    = [];
        }

        return $this->returnJson($code, $message, $data);
    }

    /**
     * 付款模式新增
     * @Permission(action='setting.business.edit.add')
     * @return Response|ResponseInterface
     * @throws ValidationException
     */
    public function addPaymentModeAction()
    {
        // 员工权限校验
        $this->getSettingAuthorize();

        $data = $this->request->get();

        Validation::validate($data, PaymentModeService::$add_validate_params);

        $res = PaymentModeService::getInstance()->add($data, $this->user);
        return $this->returnJson($res['code'], $res['message']);
    }

    /**
     * 付款模式编辑
     * @Permission(action='setting.business.edit.edit')
     * @return Response|ResponseInterface
     * @throws ValidationException
     */
    public function editPaymentModeAction()
    {
        // 员工权限校验
        $this->getSettingAuthorize();

        $data = $this->request->get();
        Validation::validate($data, PaymentModeService::$edit_validate_params);

        $res = PaymentModeService::getInstance()->edit($data, $this->user);
        return $this->returnJson($res['code'], $res['message']);
    }

    /**
     * 获取 - 发票抬头列表
     * @Permission(action='setting.business.get')
     *
     * @api https://yapi.flashexpress.pub/project/133/interface/api/71162
     *
     * @return mixed
     */
    public function getInvoiceHeaderListAction()
    {
        $res = SettingInvoiceHeaderService::getInstance()->getList();
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }

    /**
     * 添加 - 发票抬头
     * @Permission(action='setting.business.get')
     * @api https://yapi.flashexpress.pub/project/133/interface/api/71167
     *
     * @return mixed
     * @throws ValidationException
     */
    public function addInvoiceHeaderAction()
    {
        // 员工权限校验
        $this->getSettingAuthorize();

        $params = trim_array($this->request->get());
        SettingInvoiceHeaderService::getInstance()->save_validate_params($params);

        $res = SettingInvoiceHeaderService::getInstance()->add($params, $this->user);
        return $this->returnJson($res['code'], $res['message']);
    }

    /**
     * 更新 - 发票抬头
     * @Permission(action='setting.business.get')
     * @api https://yapi.flashexpress.pub/project/133/interface/api/71172
     *
     * @return mixed
     * @throws ValidationException
     */
    public function updateInvoiceHeaderAction()
    {
        // 员工权限校验
        $this->getSettingAuthorize();

        $params = trim_array($this->request->get());
        SettingInvoiceHeaderService::getInstance()->save_validate_params($params, true);

        $res = SettingInvoiceHeaderService::getInstance()->update($params, $this->user);
        return $this->returnJson($res['code'], $res['message']);
    }

    /**
     * 单次借款金额限制列表
     * @Permission(action='setting.business.get')
     * @return Response|ResponseInterface
     */
    public function getLoanStaffGradeListAction()
    {
        $res = LoanStaffService::getInstance()->getLoanStaffGradeList();
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }

    /**
     * 单次借款金额限制编辑
     * @Permission(action='setting.business.edit.edit')
     *
     * @return Response|ResponseInterface
     * @throws ValidationException
     */
    public function editLoanStaffGradeAction()
    {
        // 员工权限校验
        $this->getSettingAuthorize();

        $data = trim_array($this->request->get());
        $res  = LoanStaffService::getInstance()->editLoanStaffGrade($data, $this->user);
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }

    /**
     * 获取支付模块审批流进度标记
     * @Permission(action='setting.business.get')
     *
     * @return mixed
     * @throws ValidationException
     */
    public function getWorkflowPayProgressMarkListAction()
    {
        $params = trim_array($this->request->get());
        Validation::validate($params, ['keywords' => 'Required|StrLenGeLe:0,100|>>>:params error[keywords]']);
        $list = WorkflowPayProgressMarkService::getInstance()->getList($params['keywords']);
        return $this->returnJson(ErrCode::$SUCCESS, 'success', $list);
    }

    /**
     * 获取支付模块审批流进度标记翻译key
     * @Permission(action='setting.business.edit.add')
     *
     * @return Response|ResponseInterface
     * @throws Exception
     */
    public function getProgessMarkKeyAction()
    {
        // 员工权限校验
        $this->getSettingAuthorize();

        $translation_key = WorkflowPayProgressMarkService::getInstance()->generateTranslationKey();
        return $this->returnJson(ErrCode::$SUCCESS, 'success', ['translation_key' => $translation_key]);
    }

    /**
     * 保存进度标记配置
     * @Permission(action='setting.business.edit')
     *
     * @return Response|ResponseInterface
     * @throws ValidationException
     */
    public function saveWorkflowPayProgressMarkAction()
    {
        // 员工权限校验
        $this->getSettingAuthorize();

        $params          = trim_array($this->request->get());
        $validate_params = [
            'translation_key' => 'Required|StrLenGeLe:1,32|>>>:params error[translation_key]',
            'remark'          => 'Required|StrLenGeLe:1,100|>>>:' . $this->t['pay_module_process_mark_save_error_001'],
        ];

        Validation::validate($params, $validate_params);
        $res = WorkflowPayProgressMarkService::getInstance()->saveOne($params, $this->user);
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }

    /**
     * 自动定价策略-添加
     * @Permission(action='setting.business.edit.add')
     * @api https://yapi.flashexpress.pub/project/133/interface/api/85070
     * @return Response|ResponseInterface
     * @throws ValidationException
     */
    public function saveInteriorAutoPricePolicyAction()
    {
        // 员工权限校验
        $this->getSettingAuthorize();

        $params = trim_array($this->request->get());
        Validation::validate($params, GoodsManagementService::$validate_auto_price_policy);
        $res = GoodsManagementService::getInstance()->saveInteriorAutoPricePolicy($params, $this->user);
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }

    /**
     * 自动定价策略-列表
     * @Permission(action='setting.business.get')
     * @api https://yapi.flashexpress.pub/project/133/interface/api/85073
     * @return Response|ResponseInterface
     */
    public function getInteriorAutoPricePolicyListAction()
    {
        $res = GoodsManagementService::getInstance()->getInteriorAutoPricePolicyList();
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }

    /**
     * 员工商城-分仓配置-列表
     * @api https://yapi.flashexpress.pub/project/133/interface/api/92300
     * @description: 获取员工商城分仓配置列表，支持按网点编号搜索和分页
     * @author: AI
     * @date: 2025-08-05 16:30:00
     * @Permission(action='setting.business.get')
     * @return Response|ResponseInterface
     */
    public function getInteriorWarehouseSetListAction()
    {
        $params = trim_array($this->request->get());
        $res = GoodsManagementService::getInstance()->getInteriorWarehouseSetList($params);
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }

    /**
     * 员工商城-分仓配置-新增
     * @api https://yapi.flashexpress.pub/project/133/interface/api/92300
     * @description: 新增员工商城分仓配置信息，所有字段都是必填
     * @author: AI
     * @date: 2025-08-05 16:30:00
     * @Permission(action='setting.business.edit.add')
     * @return Response|ResponseInterface
     * @throws ValidationException
     */
    public function saveInteriorWarehouseSetAction()
    {
        // 员工权限校验
        $this->getSettingAuthorize();

        $params = trim_array($this->request->get());
        Validation::validate($params, GoodsManagementService::$validate_warehouse_set_save);

        // 加锁处理，防止并发新增相同网点编码
        $lock_key = md5(RedisKey::INTERIOR_WAREHOUSE_SET_ADD . $params['sys_store_id']);
        $res = $this->atomicLock(function () use ($params) {
            return GoodsManagementService::getInstance()->saveInteriorWarehouseSet($params, $this->user);
        }, $lock_key, 10);

        return $this->returnJson($res['code'] ?? ErrCode::$SYSTEM_ERROR, $res['message'] ?? $this->t->_('retry_later'), $res['data'] ?? []);
    }

    /**
     * 员工商城-分仓配置-删除
     * @api https://yapi.flashexpress.pub/project/133/interface/api/92303
     * @description: 删除员工商城分仓配置信息
     * @author: AI
     * @date: 2025-08-05 16:30:00
     * @Permission(action='setting.business.edit.delete')
     * @return Response|ResponseInterface
     * @throws ValidationException
     */
    public function delInteriorWarehouseSetAction()
    {
        // 员工权限校验
        $this->getSettingAuthorize();
        $params = trim_array($this->request->get());
        Validation::validate($params, GoodsManagementService::$validate_warehouse_set_delete);

        // 加锁处理，防止并发删除
        $lock_key = md5(RedisKey::INTERIOR_WAREHOUSE_SET_DEL . $params['id']);
        $res = $this->atomicLock(function () use ($params) {
            return GoodsManagementService::getInstance()->delInteriorWarehouseSet($params);
        }, $lock_key, 10);

        return $this->returnJson($res['code'] ?? ErrCode::$SYSTEM_ERROR, $res['message'] ?? $this->t->_('retry_later'), $res['data'] ?? []);
    }

    /**
     * 获取货主列表配置
     * @api https://yapi.flashexpress.pub/project/133/interface/api/92309
     * @description: 获取SCM货主列表，用于下拉选择等场景
     * @return Response|ResponseInterface
     * @author: AI
     * @date: 2025-08-05 16:30:00
     * @Permission(menu='setting.business')
     */
    public function getCargoOwnerAction()
    {
        $res = GoodsManagementService::getInstance()->getCargoOwner();
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }

    /**
     * 出差报销时间限制详情
     * @Permission(action='setting.business.get')
     * @apidoc https://yapi.flashexpress.pub/project/133/interface/api/88469
     * @return Response|ResponseInterface
     */
    public function getReimbursementTripTimeLimitAction()
    {
        $res = ReimbursementConfigService::getInstance()->getReimbursementTripTimeLimit();
        return $this->returnJson(ErrCode::$SUCCESS, $this->t->_('success'), $res);
    }

    /**
     * 出差报销时间限制保存
     * @Permission(action='setting.business.edit')
     * @apidoc https://yapi.flashexpress.pub/project/133/interface/api/88472
     * @return Response|ResponseInterface
     * @throws ValidationException
     * @throws BusinessException
     */
    public function saveReimbursementTripTimeLimitAction()
    {
        // 员工权限校验
        $this->getSettingAuthorize();

        $params          = trim_array($this->request->get());
        $validate_params = [
            'id'                           => 'Required|IntEq:1|>>>:' . $this->t->_('params_error', ['param' => 'id']),
            'allowed_travel_end_date_days' => 'Required|IntGtLt:0,1000|>>>:' . $this->t->_('params_error',
                    ['param' => 'allowed_travel_end_date_days']),
            'is_limit'                     => 'Required|IntIn:0,1|>>>:' . $this->t->_('params_error',
                    ['param' => 'is_limit']),
            'next_month_specified_date'    => 'IfIntEq:is_limit,1|Required|IntGeLe:1,31|>>>:' . $this->t->_('params_error',
                    ['param' => 'next_month_specified_date']),
        ];
        Validation::validate($params, $validate_params);

        ReimbursementConfigService::getInstance()->saveReimbursementTripTimeLimit($params, $this->user);
        return $this->returnJson(ErrCode::$SUCCESS, $this->t->_('success'));
    }

    /**
     * 获取系统汇率
     * @Permission(action='setting.business.get')
     * @apidoc https://yapi.flashexpress.pub/project/133/interface/api/88475
     * @return Response|ResponseInterface
     */
    public function getSysExchangeRateAction()
    {
        $params = trim_array($this->request->get());
        $res    = ReimbursementConfigService::getInstance()->getSysExchangeRateList($params);
        return $this->returnJson(ErrCode::$SUCCESS, $this->t->_('success'), $res);
    }

    /**
     * 更新系统汇率
     * @Permission(menu='setting.business')
     * @apidoc https://yapi.flashexpress.pub/project/133/interface/api/88478
     * @return Response|ResponseInterface
     * @throws ValidationException
     */
    public function updateSysExchangeRateAction()
    {
        // 员工权限校验
        $this->getSettingAuthorize();

        $params          = trim_array($this->request->get());
        $validate_params = [
            'start_date' => 'Required|Date|>>>:' . $this->t->_('params_error', ['param' => 'start_date']),
            'end_date'   => 'Required|Date|>>>:' . $this->t->_('params_error', ['param' => 'end_date']),
        ];

        Validation::validate($params, $validate_params);

        if ($params['start_date'] != $params['end_date']) {
            throw new ValidationException($this->t->_('business_setting_save_error_001'), ErrCode::$VALIDATE_ERROR);
        }

        if ($params['start_date'] >= date('Y-m-d')) {
            throw new ValidationException($this->t->_('business_setting_save_error_009'), ErrCode::$VALIDATE_ERROR);
        }

        // 加锁处理
        $lock_key = md5(__METHOD__);
        $res      = $this->atomicLock(function () use ($params) {
            return ReimbursementConfigService::getInstance()->updateSysExchangeRateList($params['start_date'],
                $this->user);
        }, $lock_key, 30);

        if (isset($res['code'])) {
            $code    = $res['code'];
            $message = $res['message'];
        } else {
            $code    = ErrCode::$FREQUENT_VISIT_ERROR;
            $message = $this->t['business_setting_save_error_002'];
        }

        return $this->returnJson($code, $message);
    }

    /**
     * 报销-境内机票额度配置详情
     * @Permission(action='setting.business.get')
     * @apidoc https://yapi.flashexpress.pub/project/133/interface/api/88484
     * @return Response|ResponseInterface
     */
    public function getReimbursementDomesticAirTicketQuotaAction()
    {
        $res = ReimbursementConfigService::getInstance()->getReimbursementDomesticAirTicketQuota();
        return $this->returnJson(ErrCode::$SUCCESS, $this->t->_('success'), $res);
    }

    /**
     * 报销-保存境内机票额度配置
     * @Permission(action='setting.business.edit')
     * @apidoc https://yapi.flashexpress.pub/project/133/interface/api/88487
     * @return Response|ResponseInterface
     * @throws ValidationException
     * @throws BusinessException
     */
    public function saveReimbursementDomesticAirTicketQuotaAction()
    {
        // 员工权限校验
        $this->getSettingAuthorize();

        $params          = trim_array($this->request->get());
        $validate_params = [
            'id'                          => 'Required|IntEq:1|>>>:' . $this->t->_('params_error', ['param' => 'id']),
            'head_office_amount'          => 'Required|FloatGeLe:0,999999999.99|>>>:' . $this->t->_('params_error',
                    ['param' => 'head_office_amount']),
            'frontline_functional_amount' => 'Required|FloatGeLe:0,999999999.99|>>>:' . $this->t->_('params_error',
                    ['param' => 'frontline_functional_amount']),
            'frontline_operation_amount'  => 'Required|FloatGeLe:0,999999999.99|>>>:' . $this->t->_('params_error',
                    ['param' => 'frontline_operation_amount']),
        ];
        Validation::validate($params, $validate_params);

        ReimbursementConfigService::getInstance()->saveReimbursementDomesticAirTicketQuota($params, $this->user);
        return $this->returnJson(ErrCode::$SUCCESS, $this->t->_('success'));
    }

    /**
     * 获取报销-油费额度
     * @Permission(action='setting.business.get')
     * @apidoc https://yapi.flashexpress.pub/project/133/interface/api/88493
     * @return Response|ResponseInterface
     */
    public function getReimbursementFuelQuotaAction()
    {
        $params = trim_array($this->request->get());
        $res    = ReimbursementConfigService::getInstance()->getReimbursementFuelQuota($params);
        return $this->returnJson(ErrCode::$SUCCESS, $this->t->_('success'), $res);
    }

    /**
     * 添加报销-油费额度
     * @Permission(action='setting.business.edit')
     * @apidoc https://yapi.flashexpress.pub/project/133/interface/api/88496
     * @return Response|ResponseInterface
     * @throws ValidationException
     */
    public function addReimbursementFuelQuotaAction()
    {
        // 员工权限校验
        $this->getSettingAuthorize();

        $params = trim_array($this->request->get());

        $position_type   = implode(',', array_keys(ReimbursementEnums::$position_type_item));
        $expenses_type   = implode(',', array_keys(ReimbursementEnums::$expenses_type_item));
        $oil_type        = implode(',', array_keys(ReimbursementEnums::$oil_type_item));
        $validate_params = [
            'position_type' => "Required|IntIn:{$position_type}|>>>:" . $this->t->_('params_error',
                    ['param' => 'position_type']),
            'expenses_type' => "Required|IntIn:{$expenses_type}|>>>:" . $this->t->_('params_error',
                    ['param' => 'expenses_type']),
            'oil_type'      => "Required|IntIn:{$oil_type}|>>>:" . $this->t->_('params_error', ['param' => 'oil_type']),
            'rates'         => 'Required|FloatGeLe:0,999999999.99|>>>:' . $this->t->_('params_error',
                    ['param' => 'rates']),
        ];
        Validation::validate($params, $validate_params);

        // 加锁处理
        $lock_key = md5('reimbursement_fuel_quota_add_' . $params['position_type'] . '_' . $params['expenses_type'] . '_' . $params['oil_type']);
        $res      = $this->atomicLock(function () use ($params) {
            return ReimbursementConfigService::getInstance()->addReimbursementFuelQuota($params, $this->user);
        }, $lock_key, 20);

        if ($res === true) {
            $code    = ErrCode::$SUCCESS;
            $message = $this->t->_('success');
        } else {
            $code    = ErrCode::$FREQUENT_VISIT_ERROR;
            $message = $this->t['sys_processing'];
        }

        return $this->returnJson($code, $message);
    }

    /**
     * 删除报销-油费额度
     * @Permission(action='setting.business.edit')
     * @apidoc https://yapi.flashexpress.pub/project/133/interface/api/88499
     * @return Response|ResponseInterface
     * @throws ValidationException
     */
    public function delReimbursementFuelQuotaAction()
    {
        // 员工权限校验
        $this->getSettingAuthorize();

        $params = trim_array($this->request->get());

        $validate_params = [
            'id' => "Required|IntGt:0|>>>:" . $this->t->_('params_error', ['param' => 'id']),
        ];
        Validation::validate($params, $validate_params);

        // 加锁处理
        $lock_key = md5('reimbursement_fuel_quota_del_' . $params['id']);
        $res      = $this->atomicLock(function () use ($params) {
            return ReimbursementConfigService::getInstance()->delReimbursementFuelQuota($params, $this->user);
        }, $lock_key, 20);

        if ($res === true) {
            $code    = ErrCode::$SUCCESS;
            $message = $this->t->_('success');
        } else {
            $code    = ErrCode::$FREQUENT_VISIT_ERROR;
            $message = $this->t['sys_processing'];
        }

        return $this->returnJson($code, $message);
    }

    /**
     * 获取报销-境内住宿区域配置
     * @Permission(action='setting.business.get')
     * @apidoc https://yapi.flashexpress.pub/project/133/interface/api/88508
     * @return Response|ResponseInterface
     */
    public function getReimbursementDomesticAccommodationAreaAction()
    {
        $params = trim_array($this->request->get());
        $res    = ReimbursementConfigService::getInstance()->getReimbursementDomesticAccommodationArea($params);
        return $this->returnJson(ErrCode::$SUCCESS, $this->t->_('success'), $res);
    }

    /**
     * 添加报销-境内住宿区域配置
     * @Permission(action='setting.business.edit')
     * @apidoc https://yapi.flashexpress.pub/project/133/interface/api/88514
     * @return Response|ResponseInterface
     * @throws ValidationException
     */
    public function addReimbursementDomesticAccommodationAreaAction()
    {
        // 员工权限校验
        $this->getSettingAuthorize();

        $params = trim_array($this->request->get());

        $area_type       = implode(',', array_keys(ReimbursementEnums::$domestic_accommodation_area_type_item));
        $validate_params = [
            'area_type'     => "Required|IntIn:{$area_type}|>>>:" . $this->t->_('params_error',
                    ['param' => 'area_type']),
            'province_code' => 'Required|StrLenGe:1|>>>:' . $this->t->_('params_error', ['param' => 'province_code']),
            'city_code'     => 'Required|StrLenGe:0|>>>:' . $this->t->_('params_error', ['param' => 'city_code']),
        ];
        Validation::validate($params, $validate_params);

        // 加锁处理
        $lock_key = md5('reimbursement_domestic_accommodation_area_add_' . $params['province_code'] . '_' . $params['city_code']);
        $res      = $this->atomicLock(function () use ($params) {
            return ReimbursementConfigService::getInstance()->addReimbursementDomesticAccommodationArea($params,
                $this->user);
        }, $lock_key, 20);

        if ($res === true) {
            $code    = ErrCode::$SUCCESS;
            $message = $this->t->_('success');
        } else {
            $code    = ErrCode::$FREQUENT_VISIT_ERROR;
            $message = $this->t['sys_processing'];
        }

        return $this->returnJson($code, $message);
    }

    /**
     * 删除报销-境内住宿区域配置
     * @Permission(action='setting.business.edit')
     * @apidoc https://yapi.flashexpress.pub/project/133/interface/api/88517
     * @return Response|ResponseInterface
     * @throws ValidationException
     */
    public function delReimbursementDomesticAccommodationAreaAction()
    {
        // 员工权限校验
        $this->getSettingAuthorize();

        $params = trim_array($this->request->get());

        $validate_params = [
            'id' => "Required|IntGt:0|>>>:" . $this->t->_('params_error', ['param' => 'id']),
        ];
        Validation::validate($params, $validate_params);

        // 加锁处理
        $lock_key = md5('reimbursement_domestic_accommodation_area_del_' . $params['id']);
        $res      = $this->atomicLock(function () use ($params) {
            return ReimbursementConfigService::getInstance()->delReimbursementDomesticAccommodationArea($params,
                $this->user);
        }, $lock_key, 20);

        if ($res === true) {
            $code    = ErrCode::$SUCCESS;
            $message = $this->t->_('success');
        } else {
            $code    = ErrCode::$FREQUENT_VISIT_ERROR;
            $message = $this->t['sys_processing'];
        }

        return $this->returnJson($code, $message);
    }

    /**
     * 获取报销-境内住宿额度配置
     * @Permission(action='setting.business.get')
     * @apidoc https://yapi.flashexpress.pub/project/133/interface/api/88508
     * @return Response|ResponseInterface
     */
    public function getReimbursementDomesticAccommodationQuotaAction()
    {
        $params = trim_array($this->request->get());
        $res    = ReimbursementConfigService::getInstance()->getReimbursementDomesticAccommodationQuota($params);
        return $this->returnJson(ErrCode::$SUCCESS, $this->t->_('success'), $res);
    }

    /**
     * 添加报销-境内住宿额度配置
     * @Permission(action='setting.business.edit')
     * @apidoc https://yapi.flashexpress.pub/project/133/interface/api/88529
     * @return Response|ResponseInterface
     * @throws ValidationException
     */
    public function addReimbursementDomesticAccommodationQuotaAction()
    {
        // 员工权限校验
        $this->getSettingAuthorize();

        $params = trim_array($this->request->get());

        $position_type   = implode(',', array_keys(ReimbursementEnums::$position_type_item));
        $area_type       = implode(',', array_keys(ReimbursementEnums::$domestic_accommodation_quota_type_item));
        $job_level       = implode(',', array_column(Enums::$job_level, 'id'));
        $validate_params = [
            'position_type'    => "Required|IntIn:{$position_type}|>>>:" . $this->t->_('params_error',
                    ['param' => 'position_type']),
            'area_type'        => "Required|IntIn:{$area_type}|>>>:" . $this->t->_('params_error',
                    ['param' => 'area_type']),
            'job_level'        => "Required|IntIn:{$job_level}|>>>:" . $this->t->_('params_error',
                    ['param' => 'job_level']),
            'standards_amount' => 'Required|FloatGeLe:0,999999999.99|>>>:' . $this->t->_('params_error',
                    ['param' => 'standards_amount']),
        ];
        Validation::validate($params, $validate_params);

        // 加锁处理
        $lock_key = md5('reimbursement_domestic_accommodation_quota_add_' . $params['position_type'] . '_' . $params['area_type'] . '_' . $params['job_level']);
        $res      = $this->atomicLock(function () use ($params) {
            return ReimbursementConfigService::getInstance()->addReimbursementDomesticAccommodationQuota($params,
                $this->user);
        }, $lock_key, 20);

        if ($res === true) {
            $code    = ErrCode::$SUCCESS;
            $message = $this->t->_('success');
        } else {
            $code    = ErrCode::$FREQUENT_VISIT_ERROR;
            $message = $this->t['sys_processing'];
        }

        return $this->returnJson($code, $message);
    }

    /**
     * 删除报销-境内住宿额度配置
     * @Permission(action='setting.business.edit')
     * @apidoc https://yapi.flashexpress.pub/project/133/interface/api/88532
     * @return Response|ResponseInterface
     * @throws ValidationException
     */
    public function delReimbursementDomesticAccommodationQuotaAction()
    {
        // 员工权限校验
        $this->getSettingAuthorize();

        $params = trim_array($this->request->get());

        $validate_params = [
            'id' => "Required|IntGt:0|>>>:" . $this->t->_('params_error', ['param' => 'id']),
        ];
        Validation::validate($params, $validate_params);

        // 加锁处理
        $lock_key = md5('reimbursement_domestic_accommodation_quota_del_' . $params['id']);
        $res      = $this->atomicLock(function () use ($params) {
            return ReimbursementConfigService::getInstance()->delReimbursementDomesticAccommodationQuota($params,
                $this->user);
        }, $lock_key, 20);

        if ($res === true) {
            $code    = ErrCode::$SUCCESS;
            $message = $this->t->_('success');
        } else {
            $code    = ErrCode::$FREQUENT_VISIT_ERROR;
            $message = $this->t['sys_processing'];
        }

        return $this->returnJson($code, $message);
    }

    /**
     * 获取报销-境外住宿区域配置列表
     * @Permission(action='setting.business.get')
     * @apidoc https://yapi.flashexpress.pub/project/133/interface/api/88508
     * @return Response|ResponseInterface
     */
    public function getReimbursementOverseasAccommodationAreaAction()
    {
        $params = trim_array($this->request->get());
        $res    = ReimbursementConfigService::getInstance()->getReimbursementOverseasAccommodationArea($params);
        return $this->returnJson(ErrCode::$SUCCESS, $this->t->_('success'), $res);
    }

    /**
     * 添加报销-境外住宿区域配置
     * @Permission(action='setting.business.edit')
     * @apidoc https://yapi.flashexpress.pub/project/133/interface/api/88691
     * @return Response|ResponseInterface
     * @throws ValidationException
     */
    public function addReimbursementOverseasAccommodationAreaAction()
    {
        // 员工权限校验
        $this->getSettingAuthorize();

        $params = trim_array($this->request->get());

        $accommodation_type = implode(',', array_keys(ReimbursementEnums::$overseas_accommodation_type_item));
        $country_list       = EnumsService::getInstance()->getCountryListByWorking()['country_list'] ?? [];
        $country_id         = implode(',', array_keys($country_list));
        $validate_params    = [
            'accommodation_type' => "Required|IntIn:{$accommodation_type}|>>>:" . $this->t->_('params_error',
                    ['param' => 'accommodation_type']),
            'country_id'         => "Required|IntIn:{$country_id}|>>>:" . $this->t->_('params_error',
                    ['param' => 'country_id']),
        ];
        Validation::validate($params, $validate_params);

        // 加锁处理
        $lock_key = md5('reimbursement_overseas_accommodation_area_add_' . $params['country_id']);
        $res      = $this->atomicLock(function () use ($params) {
            return ReimbursementConfigService::getInstance()->addReimbursementOverseasAccommodationArea($params,
                $this->user);
        }, $lock_key, 20);

        if ($res === true) {
            $code    = ErrCode::$SUCCESS;
            $message = $this->t->_('success');
        } else {
            $code    = ErrCode::$FREQUENT_VISIT_ERROR;
            $message = $this->t['sys_processing'];
        }

        return $this->returnJson($code, $message);
    }

    /**
     * 删除报销-境外住宿区域配置
     * @Permission(action='setting.business.edit')
     * @apidoc https://yapi.flashexpress.pub/project/133/interface/api/88694
     * @return Response|ResponseInterface
     * @throws ValidationException
     */
    public function delReimbursementOverseasAccommodationAreaAction()
    {
        // 员工权限校验
        $this->getSettingAuthorize();

        $params = trim_array($this->request->get());

        $validate_params = [
            'id' => "Required|IntGt:0|>>>:" . $this->t->_('params_error', ['param' => 'id']),
        ];
        Validation::validate($params, $validate_params);

        // 加锁处理
        $lock_key = md5('reimbursement_overseas_accommodation_area_del_' . $params['id']);
        $res      = $this->atomicLock(function () use ($params) {
            return ReimbursementConfigService::getInstance()->delReimbursementOverseasAccommodationArea($params,
                $this->user);
        }, $lock_key, 20);

        if ($res === true) {
            $code    = ErrCode::$SUCCESS;
            $message = $this->t->_('success');
        } else {
            $code    = ErrCode::$FREQUENT_VISIT_ERROR;
            $message = $this->t['sys_processing'];
        }

        return $this->returnJson($code, $message);
    }

    /**
     * 获取报销-境外住宿额度配置列表
     * @Permission(action='setting.business.get')
     * @apidoc https://yapi.flashexpress.pub/project/133/interface/api/88700
     * @return Response|ResponseInterface
     */
    public function getReimbursementOverseasAccommodationQuotaAction()
    {
        $params = trim_array($this->request->get());
        $res    = ReimbursementConfigService::getInstance()->getReimbursementOverseasAccommodationQuota($params);
        return $this->returnJson(ErrCode::$SUCCESS, $this->t->_('success'), $res);
    }

    /**
     * 添加报销-境外住宿额度配置
     * @Permission(action='setting.business.edit')
     * @apidoc https://yapi.flashexpress.pub/project/133/interface/api/88709
     * @return Response|ResponseInterface
     * @throws ValidationException
     */
    public function addReimbursementOverseasAccommodationQuotaAction()
    {
        // 员工权限校验
        $this->getSettingAuthorize();

        $params = trim_array($this->request->get());

        $accommodation_type = implode(',', array_keys(ReimbursementEnums::$overseas_accommodation_type_item));
        $position_type      = implode(',', array_keys(ReimbursementEnums::$position_type_item));
        $validate_params    = [
            'position_type'      => "Required|IntIn:{$position_type}|>>>:" . $this->t->_('params_error',
                    ['param' => 'position_type']),
            'accommodation_type' => "Required|IntIn:{$accommodation_type}|>>>:" . $this->t->_('params_error',
                    ['param' => 'accommodation_type']),
            'standards_amount'   => 'Required|FloatGeLe:0,999999999.99|>>>:' . $this->t->_('params_error',
                    ['param' => 'standards_amount']),

        ];
        Validation::validate($params, $validate_params);

        // 加锁处理
        $lock_key = md5('reimbursement_overseas_accommodation_quota_add_' . $params['position_type'] . '_' . $params['accommodation_type']);
        $res      = $this->atomicLock(function () use ($params) {
            return ReimbursementConfigService::getInstance()->addReimbursementOverseasAccommodationQuota($params,
                $this->user);
        }, $lock_key, 20);

        if ($res === true) {
            $code    = ErrCode::$SUCCESS;
            $message = $this->t->_('success');
        } else {
            $code    = ErrCode::$FREQUENT_VISIT_ERROR;
            $message = $this->t['sys_processing'];
        }

        return $this->returnJson($code, $message);
    }

    /**
     * 删除报销-境外住宿额度配置
     * @Permission(action='setting.business.edit')
     * @apidoc https://yapi.flashexpress.pub/project/133/interface/api/88712
     * @return Response|ResponseInterface
     * @throws ValidationException
     */
    public function delReimbursementOverseasAccommodationQuotaAction()
    {
        // 员工权限校验
        $this->getSettingAuthorize();

        $params = trim_array($this->request->get());

        $validate_params = [
            'id' => "Required|IntGt:0|>>>:" . $this->t->_('params_error', ['param' => 'id']),
        ];
        Validation::validate($params, $validate_params);

        // 加锁处理
        $lock_key = md5('reimbursement_overseas_accommodation_quota_del_' . $params['id']);
        $res      = $this->atomicLock(function () use ($params) {
            return ReimbursementConfigService::getInstance()->delReimbursementOverseasAccommodationQuota($params,
                $this->user);
        }, $lock_key, 20);

        if ($res === true) {
            $code    = ErrCode::$SUCCESS;
            $message = $this->t->_('success');
        } else {
            $code    = ErrCode::$FREQUENT_VISIT_ERROR;
            $message = $this->t['sys_processing'];
        }

        return $this->returnJson($code, $message);
    }

    /**
     * 添加报销-租车费额度配置
     * @Permission(action='setting.business.edit')
     * @apidoc https://yapi.flashexpress.pub/project/133/interface/api/88715
     * @return Response|ResponseInterface
     * @throws ValidationException
     */
    public function addReimbursementCarRentalQuotaAction()
    {
        // 员工权限校验
        $this->getSettingAuthorize();

        $params = trim_array($this->request->get());

        $position_type   = implode(',', array_keys(ReimbursementEnums::$position_type_item));
        $job_level       = implode(',', array_column(Enums::$job_level, 'id'));
        $validate_params = [
            'position_type'              => "Required|IntIn:{$position_type}|>>>:" . $this->t->_('params_error',
                    ['param' => 'position_type']),
            'job_level'                  => "Required|IntIn:{$job_level}|>>>:" . $this->t->_('params_error',
                    ['param' => 'job_level']),
            'is_required_email_approval' => "Required|IntIn:0,1|>>>:" . $this->t->_('params_error',
                    ['param' => 'is_required_email_approval']),
            'standards_amount'           => 'Required|FloatGeLe:0,999999999.99|>>>:' . $this->t->_('params_error',
                    ['param' => 'standards_amount']),

        ];
        Validation::validate($params, $validate_params);

        // 加锁处理
        $lock_key = md5('reimbursement_car_rental_quota_add_' . $params['position_type'] . '_' . $params['job_level']);
        $res      = $this->atomicLock(function () use ($params) {
            return ReimbursementConfigService::getInstance()->addReimbursementCarRentalQuota($params,
                $this->user);
        }, $lock_key, 20);

        if ($res === true) {
            $code    = ErrCode::$SUCCESS;
            $message = $this->t->_('success');
        } else {
            $code    = ErrCode::$FREQUENT_VISIT_ERROR;
            $message = $this->t['sys_processing'];
        }

        return $this->returnJson($code, $message);
    }

    /**
     * 删除报销-租车费额度配置
     * @Permission(action='setting.business.edit')
     * @apidoc https://yapi.flashexpress.pub/project/133/interface/api/88718
     * @return Response|ResponseInterface
     * @throws ValidationException
     */
    public function delReimbursementCarRentalQuotaAction()
    {
        // 员工权限校验
        $this->getSettingAuthorize();

        $params = trim_array($this->request->get());

        $validate_params = [
            'id' => "Required|IntGt:0|>>>:" . $this->t->_('params_error', ['param' => 'id']),
        ];
        Validation::validate($params, $validate_params);

        // 加锁处理
        $lock_key = md5('reimbursement_car_rental_quota_del_' . $params['id']);
        $res      = $this->atomicLock(function () use ($params) {
            return ReimbursementConfigService::getInstance()->delReimbursementCarRentalQuota($params,
                $this->user);
        }, $lock_key, 20);

        if ($res === true) {
            $code    = ErrCode::$SUCCESS;
            $message = $this->t->_('success');
        } else {
            $code    = ErrCode::$FREQUENT_VISIT_ERROR;
            $message = $this->t['sys_processing'];
        }

        return $this->returnJson($code, $message);
    }

    /**
     * 获取报销-租车费额度配置列表
     * @Permission(action='setting.business.get')
     * @apidoc https://yapi.flashexpress.pub/project/133/interface/api/88721
     * @return Response|ResponseInterface
     */
    public function getReimbursementCarRentalQuotaAction()
    {
        $params = trim_array($this->request->get());
        $res    = ReimbursementConfigService::getInstance()->getReimbursementCarRentalQuota($params);
        return $this->returnJson(ErrCode::$SUCCESS, $this->t->_('success'), $res);
    }


    /**
     * 获取纸质单据确认人列表
     * @Permission(action='setting.business.get')
     * @return mixed
     * @throws ValidationException
     */
    public function getPaperDocumentConfirmStaffListAction()
    {
        $params = $this->request->get();
        Validation::validate($params, ConfirmStaffService::$validate_list_search);

        $list = ConfirmStaffService::getInstance()->getPaperDocumentConfirmStaffList($params);

        return $this->returnJson(ErrCode::$SUCCESS, 'ok', $list);
    }

    /**
     * 添加纸质单据确认人
     * @Permission(action='setting.business.edit.add')
     * @return mixed
     * @throws ValidationException
     */
    public function addPaperDocumentConfirmStaffAction()
    {
        $params = $this->request->get();
        Validation::validate($params, ConfirmStaffService::$validate_add);

        $result = ConfirmStaffService::getInstance()->add($params);

        return $this->returnJson(ErrCode::$SUCCESS, 'ok', $result);
    }

    /**
     * 编辑纸质单据确认人
     * @Permission(action='setting.business.edit.edit')
     * @return mixed
     * @throws ValidationException
     */
    public function editPaperDocumentConfirmStaffAction()
    {
        $params = $this->request->get();
        Validation::validate($params, ConfirmStaffService::$validate_edit);

        $result = ConfirmStaffService::getInstance()->edit($params);

        return $this->returnJson(ErrCode::$SUCCESS, 'ok', $result);
    }

    /**
     * 编辑纸质单据确认人
     * @Permission(action='setting.business.edit.delete')
     * @return mixed
     * @throws ValidationException
     */
    public function deletePaperDocumentConfirmStaffAction()
    {
        $params = $this->request->get();
        Validation::validate($params, ConfirmStaffService::$validate_detail);

        $result = ConfirmStaffService::getInstance()->delete($params);

        return $this->returnJson(ErrCode::$SUCCESS, 'ok', $result);
    }

    /**
     * 纸质单据确认人详情
     * @Permission(action='setting.business.get')
     * @return mixed
     * @throws ValidationException
     */
    public function getPaperDocumentConfirmDetailAction()
    {
        $params = $this->request->get();
        Validation::validate($params, ConfirmStaffService::$validate_detail);

        $list = ConfirmStaffService::getInstance()->detail($params);

        return $this->returnJson(ErrCode::$SUCCESS, 'ok', $list);
    }
}
