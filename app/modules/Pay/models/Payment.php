<?php
namespace App\Modules\Pay\Models;

use App\Library\Enums;
use App\Library\Enums\GlobalEnums;
use App\Library\Enums\BankFlowEnums;
use App\Models\Base;
use App\Models\oa\AgencyPaymentDetailModel;
use App\Modules\User\Models\AttachModel;

class Payment extends Base
{

    // initialize 在请求期间仅会被自动调用一次，目的是为模型实例进行初始化
    public function initialize()
    {
        $this->setConnectionService('db_oa');
        // setSource()方法指定数据库表
        $this->setSource('payment');

        $this->hasMany(
            'id',
            AttachModel::class,
            'oss_bucket_key', [
                'params' => [
                    'conditions' => "oss_bucket_type = " . Enums::OSS_BUCKET_TYPE_PAY_MODULE . " and deleted=0"
                ],
                "alias" => "Files",
            ]
        );

        $this->hasMany(
            'id',
            PaymentPay::class,
            'payment_id', [
                "alias" => "Pays",
            ]
        );

        //发票信息
        $this->hasMany(
            'id',
            PaymentCheck::class,
            'payment_id', [
                'params' => [
                    'conditions' => "is_deleted = 0"
                ],
                "alias" => "Checks",
            ]
        );

        //补充附件
        $this->hasMany(
            'id',
            AttachModel::class,
            'oss_bucket_key', [
                'params' => [
                    'conditions' => 'oss_bucket_type = ' . Enums::OSS_BUCKET_TYPE_PAY_MODULE_SUPPLEMENT . ' and deleted = ' . GlobalEnums::IS_NO_DELETED
                ],
                'alias' => 'Supplements',
            ]
        );

        //代理支付模块
        $this->hasOne(
            'no',
            AgencyPaymentDetailModel::class,
            'no',
            [
                'alias' => 'AgencyPaymentDetail',
            ]
        );
    }
}
