<?php

namespace App\Modules\Pay\Services;

use App\Library\BaseService;
use App\Library\Enums\BankFlowEnums;
use App\Library\Enums\PayEnums;
use App\Library\Enums\StaffInfoEnums;
use App\Library\Exception\BusinessException;
use App\Library\FlashPayHelper;
use App\Library\RocketMQ;
use App\Library\Validation\ValidationException;
use App\Modules\BankFlow\Models\BankAccountModel;
use App\Modules\Pay\Models\Payment;
use App\Modules\Pay\Services\FinalPayService;
use App\Modules\Pay\Services\FlashPayUpdateParams;
use App\Modules\Pay\Services\FlashPaySftpUpdateParams;
use App\Modules\Pay\Services\FlashPayApiUpdateParams;
use App\Modules\Third\Services\OaExternalApiService;
use App\Modules\User\Services\UserService;
use App\Library\BaseController;
use Exception;

/**
 * FlashPay支付状态更新通用服务类
 * @description: 提供FlashPay支付状态更新的通用方法，支持SFTP和API两种方式
 * @author: AI
 * @date: 2025-08-18
 */
class FlashPayStatusUpdateService extends BaseService
{
    private static $instance;

    private function __construct()
    {
    }

    private function __clone()
    {
    }

    /**
     * 获取单例实例
     * @return FlashPayStatusUpdateService
     */
    public static function getInstance()
    {
        if (!isset(self::$instance) || !(self::$instance instanceof self)) {
            self::$instance = new self;
        }
        return self::$instance;
    }

    /**
     * 更新支付状态 - 通用方法
     * @description: 根据FlashPay返回的交易状态更新支付记录，支持SFTP和API两种方式
     * @param FlashPayUpdateParams $params 参数对象
     * @return bool 更新结果
     * @throws Exception
     */
    public function updatePaymentStatus(FlashPayUpdateParams $params): bool
    {
        // 参数验证
        $params->validate();

        // 根据支付方式选择不同的更新策略
        if ($params instanceof FlashPaySftpUpdateParams) {
            $result = $this->updateSftpPaymentStatus($params);
        } elseif ($params instanceof FlashPayApiUpdateParams) {
            $result = $this->updateApiPaymentStatus($params);
        } else {
            throw new Exception('不支持的参数类型: ' . get_class($params));
        }
        return $result;
    }

    /**
     * 更新SFTP方式的支付状态
     * @description: 处理SFTP方式的支付状态更新逻辑
     * @param FlashPaySftpUpdateParams $params 参数对象
     * @return bool 更新结果
     * @throws BusinessException|ValidationException
     */
    private function updateSftpPaymentStatus(FlashPaySftpUpdateParams $params): bool
    {
        $payment           = $params->getPayment();
        $paymentOnlinePay  = $params->getPaymentOnlinePay();
        $transactionStatus = $params->getTransactionStatus();
        $excelResultData   = $params->getExcelResultData();
        $zeroTime                   = $excelResultData['creation_time'];
        $paymentOnlinePay->trade_no = $excelResultData['transaction_order_no'];
        $payment->out_trade_no      = $excelResultData['transaction_order_no'];
        switch (strtolower($transactionStatus)) {
            case 'success':
                // 支付成功
                $this->handleSuccessStatus($payment, $zeroTime,$excelResultData['transfer_end_time'],$excelResultData['creation_time'] );
                break;
            case 'failed':
                // 支付失败
                $this->handleFailedStatus($payment, $zeroTime, $excelResultData['transaction_fail_reasons']);
                break;
            default:
                // 未知状态，发送飞书预警
                $alertMessage = sprintf('FlashPay结果处理：未知交易状态 %s，交易号 %s', $transactionStatus, $payment->oa_trade_no);
                FlashPayHelper::sendNotice($alertMessage);
                throw new Exception('未知交易状态: ' . $transactionStatus);
        }
        if (!$payment->update()) {
            throw new Exception('保存支付记录1失败: ' .$payment->getErrorMessagesJson());
        }
        // 更新支付记录
        if (!$paymentOnlinePay->update()) {
            throw new Exception('更新支付online记录2失败: ' . $payment->getErrorMessagesJson());
        }

        return true;
    }

    /**
     * 更新API方式的支付状态
     * @description: 处理API方式的支付状态更新逻辑
     * @param FlashPayApiUpdateParams $params 参数对象
     * @return bool 更新结果
     * @throws BusinessException|ValidationException
     */
    private function updateApiPaymentStatus(FlashPayApiUpdateParams $params): bool
    {

        $payment           = $params->getPayment();
        $transactionStatus = $params->getTransactionStatus();
        $paymentData       = $params->getPaymentData();
        $paymentResult     = $params->getPaymentResult();

        $zeroTime = gmdate('Y-m-d H:i:s'); // 0时区

        // 交易状态：0交易待支付、2交易处理中、3交易成功、4交易失败、5交易关闭
        $payment->out_trade_status = $transactionStatus;

        if ($transactionStatus == PayEnums::PAYMENT_MODULE_PAY_OUT_TRADE_STATUS_SUCCESS) {
            // 交易成功处理
            $this->handleSuccessStatus($payment,$zeroTime, $paymentData['completeTime'],null );
        } else {
            if (in_array($transactionStatus, [
                PayEnums::PAYMENT_MODULE_PAY_OUT_TRADE_STATUS_FAILED,
                PayEnums::PAYMENT_MODULE_PAY_OUT_TRADE_STATUS_CLOSE,
            ])) {
                // 交易失败或关闭处理
                $this->handleFailedStatus($payment, $zeroTime);
            }
        }

        $payment->out_trade_code = $paymentResult['code'] ?? 0; // 外部交易code码
        $payment->updated_at     = $zeroTime;                   // 更新时间需要0时区存储

        if (!$payment->update()) {
            throw new Exception('保存支付记录失败: ' . implode(', ', $payment->getMessages()));
        }

        $this->logger->info(sprintf(
            'FlashPay API支付状态更新成功: 交易号=%s, 状态=%s',
            $payment->oa_trade_no ?? '',
            $transactionStatus
        ));
        return true;
    }

    /**
     * 处理支付成功状态
     * @description: 处理支付成功的状态更新逻辑
     * @param Payment $payment 支付对象
     * @param string $zeroTime 0时区时间
     * @param $transfer_end_time
     * @param $creation_time
     * @throws BusinessException
     * @throws ValidationException
     */
    private function handleSuccessStatus(Payment $payment, string $zeroTime,$transfer_end_time,$creation_time)
    {
        // 交易成功，将该支付状态由待付款（pay支付中）变更为已支付,记录pay交易号、支付时间
        $payment->pay_status      = PayEnums::PAYMENT_MODULE_PAY_STATUS_PAY;                   // 支付状态：已支付
        $payment->out_send_status = PayEnums::PAYMENT_MODULE_PAY_SEND_STATUS_SUCCESS;          // 外部交易发送状态:成功
        if (!empty($creation_time)) {
            $payment->out_trade_at = $creation_time;                 // pay交易时间
        }
        $payment->pay_bank_flow_date = $transfer_end_time ?? null;                                // 该笔出款在银行完成的时间
        $payment->payer_id           = $this->getPayer();                                         // 最终支付人
        $payment->payer_date         = $zeroTime;                                                 // 最终支付人操作时间需要0时区存储

        $paymentArr = $payment->toArray();

        // 只有报销模块支付才需要固化银行id
        if ($payment->oa_type == BankFlowEnums::BANK_FLOW_OA_TYPE_REIMBURSEMENT) {
            $bankAccount = BankAccountModel::findFirst([
                'columns'    => 'bank_id',
                'conditions' => 'account = :account:',
                'bind'       => ['account' => $payment->pay_bank_account],
            ]);
            if (!empty($bankAccount)) {
                $paymentArr['pay_bank_id'] = $bankAccount->bank_id;
            }
        }

        // 业务数据支付
        $us   = new UserService();
        $user = $us->getUserById($payment->payer_id);
        $user = (new BaseController())->format_user($user);

        // V21615 代理支付-异步通知
        if ($paymentArr['oa_type'] == BankFlowEnums::BANK_FLOW_OA_TYPE_AGENCY_PAYMENT) {
            $paymentArr['send_crowd'] = true;
        }

        FinalPayService::getInstance()->businessPay($paymentArr, $user);
        OaExternalApiService::getInstance()->addPaymentPushRecord($payment);

        //发送RocketMQ消息生成支付凭证
        $rmq = new RocketMQ('payment-voucher');
        $params = ['payment_id' =>$payment->id,];
        $rid = $rmq->sendToMsg($params,5);
        $this->logger->info('RocketMQ payment-voucher sendToMsg result:' . $rid);
    }

    /**
     * 处理支付失败状态
     * @description: 处理支付失败的状态更新逻辑
     * @param Payment $payment 支付对象
     * @param string $zeroTime 0时区时间
     * @throws BusinessException
     */
    public function handleFailedStatus($payment, $zeroTime,$failReasons = '')
    {
        // 交易失败，交易关闭将该支付状态由pay支付中改为pay支付失败
        $payment->pay_status            = PayEnums::PAYMENT_MODULE_PAY_STATUS_FLASH_PAY_FAILED; // 支付状态：pay支付失败
        $payment->out_send_status       = PayEnums::PAYMENT_MODULE_PAY_SEND_STATUS_FAILED;      // 外部交易发送状态:失败
        $payment->payer_id              = $this->getPayer();                                    // 最终支付人
        $payment->payer_date            = $zeroTime;                                            // 最终支付人操作时间需要0时区存储
        $payment->out_trade_fail_reason = $failReasons;                                         // 支付失败原因

        // 调用拒绝方法
        return $this->reject($payment, $payment->payer_id);
    }

    /**
     * 获取pay在线支付人
     * @description: 从pay_module_payer设置中获取第一个三级支付人作为驳回人
     * @return array
     */
    private function getPayer()
    {
        // 从pay_module_payer设置中获取第一个三级支付人作为驳回人
        $payerArr = \App\Modules\Common\Services\EnumsService::getInstance()->getPayModulePayer();
        return array_shift($payerArr);
    }

    /**
     * pay支付失败的单据需要驳回到一级支付人
     * @description: 调用FinalPayService的驳回方法
     * @param object $payment 支付单据对象信息
     * @param array $userId 三级支付人用户id
     * @throws \App\Library\Exception\BusinessException
     */
    private function reject($payment, $userId)
    {
        return FinalPayService::getInstance()->flashPayFailedReject($payment, $userId);
    }


}
