<?php

namespace App\Modules\Pay\Services;

use App\Library\CInterface\PayModelInterface;
use App\Library\Enums;
use App\Library\Enums\BankFlowEnums;
use App\Library\Enums\GlobalEnums;
use App\Library\ErrCode;
use App\Library\Exception\BusinessException;
use App\Library\FlashPayHelper;
use App\Library\Validation\ValidationException;
use App\Models\oa\ChequeAccountModel;
use App\Models\oa\PaymentFlashPayConfigModel;
use App\Models\oa\PaymentModel;
use App\Models\oa\SettingEnvModel;
use App\Models\oa\SysAttachmentModel;
use App\Models\oa\WorkflowRequestNodeCommentModel;
use App\Models\oa\WorkflowRequestNodeFyrMiddleModel;
use App\Modules\Common\Models\EnvModel;
use App\Modules\Common\Services\ContractService;
use App\Modules\Common\Services\EnumsService;
use App\Modules\Pay\Models\Payment;
use App\Modules\Pay\Models\PaymentCheck;
use App\Modules\Pay\Models\PaymentFlashPayBank;
use App\Modules\Pay\Models\PaymentPay;
use App\Modules\Purchase\Services\DepartmentService;
use App\Modules\User\Models\StaffInfoModel;
use App\Modules\User\Services\UserService;
use App\Modules\Wages\Models\WagesModel;
use App\Modules\Workflow\Models\WorkflowRequestModel;
use App\Modules\Workflow\Models\WorkflowRequestNodeAt;
use App\Modules\Workflow\Services\FYRService;
use App\Modules\Workflow\Services\WorkflowCommentService;
use App\Modules\Workflow\Services\WorkflowServiceV2;
use App\Library\Validation\Validation;
use App\Modules\Cheque\Services\ChequeService;
use App\Library\Enums\ChequeEnums;
use App\Models\oa\ChequeAccountBusinessRelModel;
use App\Library\Enums\PayEnums;
use App\Modules\BankFlow\Services\PayFlowService as BankFlowPayFlowService;
use App\Repository\oa\ChequeAccountRepository;
use Exception;
use GuzzleHttp\Exception\GuzzleException;

class PayService extends BaseService
{

    private static $instance;

    private function __construct()
    {
    }

    private function __clone()
    {
    }

    public static function getInstance()
    {
        if (!isset(self::$instance) || !(self::$instance instanceof self)) {
            self::$instance = new self;
        }
        return self::$instance;
    }

    /**
     * 是否为批量支付
     * @var bool
     */
    public $batchAudit = false;
    /**
     * 批量更新计划支付日期
     * @param $header
     * @param $importData
     * @param $currentUserId
     * @return array
     * @throws BusinessException
     */
    public function uploadBatchPlannedPayDate($header, $importData, $currentUserId): array
    {
        $return        = [
            'code'    => ErrCode::$SUCCESS,
            'message' => static::$t->_('success'),
        ];
        $data          = [
            'all_num'     => 0,  //总数量
            'success_num' => 0,  // 成功数量
            'failed_num'  => 0,  // 失败数量
            'url'         => '', //结果文件
        ];
        $header[]      = static::$t->_('error_message');
        $returnData    = [];
        $existOrderNos = [];
        $payment_date_cannot_precede_due_date_num = SettingEnvModel::getValByCode('payment_date_cannot_precede_due_date_num','0');
        // 收集所有单号用于查重
        $allOrderNos          = array_column($importData, 0);
        $duplicateNos         = array_diff_assoc($allOrderNos, array_unique($allOrderNos));
        $allOrderNosCondition = array_values(array_unique($allOrderNos));
        $selectDataIds        = [];
        $updateData           = [];
        if ($allOrderNosCondition) {
            //与支付管理-我的支付-待处理条件保持一致
            $builder = $this->modelsManager->createBuilder();
            $builder->from(['p' => Payment::class]);
            $builder->columns('p.id,p.no');
            $builder = (new WorkflowServiceV2())->getBizWorkflowOrderListByProcessState($builder,
                GlobalEnums::AUDIT_TAB_PENDING, [Enums::WF_PAY_TYPE], $currentUserId, 'p');
            $builder->inWhere('p.pay_status', [
                PayEnums::PAYMENT_MODULE_PAY_STATUS_PENDING,
                PayEnums::PAYMENT_MODULE_PAY_STATUS_ING,
                PayEnums::PAYMENT_MODULE_PAY_STATUS_FLASH_PAY_FAILED,
            ]);
            $builder->inWhere('p.no', $allOrderNosCondition);
            $builder->groupBy('p.id');
            $selectData    = $builder->getQuery()->execute()->toArray();
            $selectDataIds = array_column($selectData, 'id');
            if ($selectData) {
                $flow_level = (new PayFlowService())->getAuditLevelById($selectDataIds, $currentUserId);
            }
        }
        foreach ($importData as $index => $row) {
            $orderNo        = trim($row[0]);
            $plannedPayDate = trim($row[1]);
            $errors         = [];
            //跳过空行
            if (empty($orderNo) && empty($plannedPayDate)) {
                continue;
            }
            $this->logger->info(['orderNo' =>$orderNo,'plannedPayDate' => $plannedPayDate]);
            $data['all_num'] += 1;
            // 校验单号
            if (empty($orderNo)) {
                $errors[] = static::$t->_('order_number_required');//单号必填
            } else {
                // 检查单号是否存在于系统中
                $payInfo = PaymentModel::findFirst([
                    'columns'    => 'id,default_planned_pay_date,no',
                    'conditions' => 'no = :order_no:',
                    'bind'       => ['order_no' => $orderNo],
                ]);

                if (!$payInfo) {
                    $errors[] = static::$t->_('order_number_no_exit');//单号不存在
                } else {
                    // 检查是否为第一级支付人节点
                    $curLevelIsNotFirst = !isset($flow_level[$payInfo->id]) || $flow_level[$payInfo->id] != 1;
                    if ($curLevelIsNotFirst) {
                        $errors[] = static::$t->_('order_state_error');//单据状态不满足条件
                    }

                    // 检查当前操作人是否为一级支付审批人
                    if ($curLevelIsNotFirst || !in_array($payInfo->id, $selectDataIds)) {
                        $errors[] = static::$t->_('approver_not_operation');//非当前审批人不允许操作
                    }

                    // 检查单号是否重复
                    if (in_array($orderNo, $duplicateNos)) {
                        $errors[] = static::$t->_('duplicate_order_number');//单号重复
                    }

                    // 记录已存在的单号信息，用于后续更新
                    if (empty($errors)) {
                        $existOrderNos[$orderNo] = ['id'                       => $payInfo->id,
                                                    'default_planned_pay_date' => $payInfo->default_planned_pay_date,
                        ];
                    }
                }
            }

            // 校验计划支付日期
            if (empty($plannedPayDate)) {
                $errors[] = static::$t->_('planned_payment_date_required');//计划支付日期必填
            } else {
                // 检查日期格式
                if (preg_match('/^[0-9]{10}$/', $plannedPayDate)) {
                    $plannedPayDate = date('Y-m-d', $plannedPayDate);
                }
                if (!preg_match('/^\d{4}-\d{1,2}-\d{1,2}$/', $plannedPayDate)) {
                    $errors[] = static::$t->_('planned_payment_date_format_error');//计划支付日期不满足YYYY-MM-DD日期格式
                }else {
                    if (isset($existOrderNos[$orderNo]) && $defaultPayDate = $existOrderNos[$orderNo]['default_planned_pay_date']) {
                        if (isCountry('MY')) {
                            // 检查是否早于默认计划支付日期
                            $limit_date = strtotime($defaultPayDate);
                        } elseif (isCountry('TH')) {
                            // 检查是否早于默认计划支付日期
                            $limit_date = strtotime($defaultPayDate . ' - ' . $payment_date_cannot_precede_due_date_num . ' days');
                        } else {
                            $limit_date = strtotime($defaultPayDate);
                        }
                        if (strtotime($plannedPayDate) < $limit_date) {
                            $errors[] = static::$t->_('planned_pay_date_early_error',
                                ['date' => date('Y-m-d',$limit_date)]);//计划支付日期不能早于date
                        }

                    }
                }
            }

            // 如果有错误，记录到错误数据中
            if (!empty($errors)) {
                $returnData[] = [
                    $orderNo,
                    $plannedPayDate,
                    implode(',', $errors),
                ];
                $data['failed_num']++;
            } else {
                // 更新计划支付日期
                $payInfo                    = $existOrderNos[$orderNo];
                $updateData[$payInfo['id']] = $plannedPayDate;
                $returnData[]               = [
                    $orderNo,
                    $plannedPayDate,
                    '',
                ];
                $data['success_num']++;
            }
        }
        $db = $this->getDI()->get('db_oa');
        try {
            $db->begin();
            $paymentName = (new Payment())->getSource();
            //批量更新
            if ($data['failed_num'] == 0 && $updateData) {
                foreach ($updateData as $id => $plannedPayDate) {
                    $db->updateAsDict(
                        $paymentName,
                        ['planned_pay_date' => $plannedPayDate],
                        ['conditions' => "id =$id"]
                    );
                }
            }
            $db->commit();
        } catch (\Exception $e) {
            $db->rollback();
            $this->logger->error($e->getTraceAsString());
            $return['message'] = static::$t->_('retry_later');
            $return['code']    = ErrCode::$SYSTEM_ERROR;
            $return['data'] = $data;
            return $return;
        }
        // 如果有错误数据，生成错误文件
        if ($data['failed_num'] > 0) {
            $file_name = "Result_batch_updating_planned_payment_dates" . date("YmdHis");
            try {
                $result = $this->exportExcel($header, $returnData, $file_name);
            } catch (\Exception|GuzzleException $e) {
                $return['message'] = static::$t->_('retry_later');
                $return['code']    = ErrCode::$SYSTEM_ERROR;
                $return['data'] = $data;
                return $return;
            }
            $data['url']     = $result['data'] ?? '';
            $return['message'] = 'Fail';
        } else {
            unset($returnData);
            $return['message'] = 'Success';
        }

        $return['data'] = $data;
        return $return;
    }
    /**
     * 其他模块审核通过，创建支付模块=在try catch里面
     *
     * @param $model
     * @return boolean
     * @throws BusinessException
     */
    public function saveOne(PayModelInterface $model)
    {
        if (!$model instanceof PayModelInterface) {
            throw new BusinessException('not implement PayModelInterface', ErrCode::$BUSINESS_ERROR);
        }
        //业务数据打标记
        if ($model->updatePayTag() === false) {
            throw new BusinessException('update pay tag failed', ErrCode::$BUSINESS_ERROR);
        }
        $data = $model->getPayData();
        if (!in_array(get_country_code(), [GlobalEnums::TH_COUNTRY_CODE, GlobalEnums::MY_COUNTRY_CODE])) {
            $data['default_planned_pay_date'] = $data['planned_pay_date'] = null;
        }
        $pays = $data['pays'];
        unset($data['pays']);
        $payment = new Payment();
        $bool = $payment->i_create($data);
        if ($bool === false) {
            throw new BusinessException('支付模块创建失败=' . get_data_object_error_msg($payment), ErrCode::$CONTRACT_CREATE_ERROR);
        }

        $payment_id = $payment->id;

        if (!empty($pays)) {
            foreach ($pays as &$pay) {
                $pay['payment_id'] = $payment_id;
            }
            $payment_pay = new PaymentPay();
            $bool = $payment_pay->batch_insert($pays);
            if ($bool === false) {
                throw new BusinessException('支付模块关联付款人创建失败=' . get_data_object_error_msg($payment_pay), ErrCode::$CONTRACT_CREATE_ERROR);
            }
        }

        $bool = (new PayFlowService())->createRequest($payment, []);
        if ($bool === false) {
            throw new BusinessException('支付模块创建审批流失败=', ErrCode::$CONTRACT_CREATE_ERROR);
        }

        return true;
    }

    /**
     * 支付模块审核完成，调用其他模块
     *
     * @param $model
     * @param $data
     * @return boolean
     * @throws BusinessException
     */
    public function updatePay(PayModelInterface $model, $data)
    {
        if (!$model instanceof PayModelInterface) {
            throw new BusinessException('not implement PayModelInterface');
        }
        return $model->updatePay($data);
    }

    /**
     * 列表
     *
     * @param $staff_info_id
     * @param $condition
     * @param int $type 查询类型 1.支付列表 2.我的申请 3.意见征询 4.数据查询
     * @return array
     */
    public function getList($staff_info_id, $condition, $type = self::LIST_AUDIT_TYPE)
    {
        $condition['uid'] = $staff_info_id;
        $page_size = empty($condition['pageSize']) ? GlobalEnums::DEFAULT_PAGE_SIZE : $condition['pageSize'];
        $page_num = empty($condition['pageNum']) ? GlobalEnums::DEFAULT_PAGE_NUM : $condition['pageNum'];
        $offset = $page_size * ($page_num - 1);

        $code = ErrCode::$SUCCESS;
        $message = '';
        $data = [];

        try {
            $columns = 'p.is_online_pay,p.id,p.oa_type,p.no,p.apply_staff_id,p.apply_staff_name,p.cost_company_id,p.cost_company_name,p.apply_date,p.pay_method,p.pay_status,p.amount_total_actually,p.currency,p.default_planned_pay_date,p.planned_pay_date,p.bank_batch_number,p.bank_pay_type';

            // 审核模块的已处理列表, 展示处理时间
            if ($type == self::LIST_AUDIT_TYPE && isset($condition['flag'])) {
                if ($condition['flag'] == GlobalEnums::AUDIT_TAB_PROCESSED) {
                    $columns .= ',max(log.audit_at) AS audit_at';
                } else {
                    //16325需求对于在线支付需要判断银行名称、银行账号、银行户名
                    $columns .= ',GROUP_CONCAT(DISTINCT pay.bank_name) bank_name, GROUP_CONCAT(DISTINCT pay.bank_account) bank_account, GROUP_CONCAT(DISTINCT pay.bank_account_name) bank_account_name';
                }
            }

            $builder = $this->modelsManager->createBuilder();
            $builder->from(['p' => Payment::class]);
            $builder = $this->getCondition($builder, $condition, $type);
            $count = (int)$builder->columns('COUNT(DISTINCT p.id) AS total')->getQuery()->getSingleResult()->total;
            $items = [];
            if ($count) {
                $builder->columns($columns);
                $builder->groupBy('p.id');
                $builder->limit($page_size, $offset);
                $items = $builder->getQuery()->execute()->toArray();
                $items = $this->handleListItems($staff_info_id, $items, $type);
            }

            $data = [
                'items' => $items,
                'pagination' => [
                    'current_page' => $page_num,
                    'per_page' => $page_size,
                    'total_count' => $count,
                ],
            ];
        } catch (Exception $e) {
            $code = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $this->logger->error('pay-getList-failed:' . $e->getMessage());
        }

        return [
            'code' => $code,
            'message' => $message,
            'data' => $data,
        ];
    }

    /**
     * 审核待办统计
     * 待处理的 含 待支付和支付中的
     *
     * @param int $biz_type
     * @param int $user_id
     * @param int $workflow_process_state 审批处理标识 1-待处理 2-已处理 3-已征询
     *
     * @return mixed
     */
    public function getAuditPendingCount(int $biz_type, int $user_id, int $workflow_process_state = 1)
    {
        if (empty($biz_type) || empty($user_id) || !in_array($workflow_process_state, GlobalEnums::AUDIT_TAB_PENDING_STATE_ITEM)) {
            return 0;
        }

        $builder = $this->modelsManager->createBuilder();
        $builder->columns('COUNT(DISTINCT main.id) AS total');
        $builder->from(['main' => Payment::class]);
        $builder->leftjoin(WorkflowRequestModel::class, 'request.biz_value = main.id', 'request');
        $builder->leftjoin(WorkflowRequestNodeFyrMiddleModel::class, 'request.id = fyr_middle.request_id AND request.current_flow_node_id = fyr_middle.flow_node_id', 'fyr_middle');

        $builder->where('request.biz_type = :biz_type: AND request.state = :pending_state: AND request.is_abandon = :is_abandon: AND FIND_IN_SET(:current_user_id:, request.current_node_auditor_id)', [
            'biz_type' => $biz_type,
            'pending_state' => Enums::WF_STATE_PENDING,
            'is_abandon' => GlobalEnums::WORKFLOW_ABANDON_STATE_NO,
            'current_user_id' => $user_id,
        ]);

        switch ($workflow_process_state) {
            // 待处理
            case GlobalEnums::AUDIT_TAB_PENDING:
                $builder->andWhere('fyr_middle.id IS NULL OR NOT FIND_IN_SET(:current_user_id:, fyr_middle.node_fyr_auditor_ids)', ['current_user_id' => $user_id]);
                $builder->inWhere('main.pay_status', [
                    PayEnums::PAYMENT_MODULE_PAY_STATUS_PENDING,
                    PayEnums::PAYMENT_MODULE_PAY_STATUS_ING,
                    PayEnums::PAYMENT_MODULE_PAY_STATUS_FLASH_PAY_FAILED,
                ]);
                break;

            // 征询中
            case $workflow_process_state == GlobalEnums::AUDIT_TAB_CONSULTED:
                $builder->andWhere('FIND_IN_SET(:current_user_id:, fyr_middle.node_fyr_auditor_ids) AND NOT FIND_IN_SET(:current_user_id:, fyr_middle.node_reply_auditor_ids)', ['current_user_id' => $user_id]);
                break;

            // 征询已回复
            case $workflow_process_state == GlobalEnums::AUDIT_TAB_REPLIED:
                $builder->andWhere('FIND_IN_SET(:current_user_id:, fyr_middle.node_reply_auditor_ids)', ['current_user_id' => $user_id]);
                break;
        }

        return intval($builder->getQuery()->getSingleResult()->total);
    }

    /**
     * 获取意见征询回复待处理统计
     *
     * @param int $biz_type
     * @param int $user_id
     * @return mixed
     */
    public function getConsultationPendingCount(int $biz_type = 0, int $user_id = 0)
    {
        if (empty($biz_type) || empty($user_id)) {
            return 0;
        }

        $builder = $this->modelsManager->createBuilder();
        $builder->from(['main' => Payment::class]);
        $builder->leftjoin(WorkflowRequestModel::class, "request.biz_value = main.id", 'request');
        $builder->leftjoin(WorkflowRequestNodeAt::class, "request.id = reply.request_id", 'reply');
        $builder->andWhere('request.biz_type = :biz_type: AND request.is_abandon = :is_abandon:', [
            'biz_type' => $biz_type,
            'is_abandon' => GlobalEnums::WORKFLOW_ABANDON_STATE_NO,
        ]);
        $builder->andWhere('reply.staff_id = :current_user_id: AND reply.is_reply = :is_reply:', [
            'current_user_id' => $user_id,
            'is_reply' => GlobalEnums::CONSULTED_REPLY_STATE_PENDING,
        ]);

        $builder->inWhere('main.pay_status', [
            PayEnums::PAYMENT_MODULE_PAY_STATUS_PENDING,
            PayEnums::PAYMENT_MODULE_PAY_STATUS_ING,
            PayEnums::PAYMENT_MODULE_PAY_STATUS_BANKING,
        ]);

        $builder->columns('COUNT(DISTINCT reply.id) AS total');
        return intval($builder->getQuery()->getSingleResult()->total);
    }

    /**
     * @param $builder
     * @param $condition
     * @param int $type 查询类型 1.支付列表 2.我的申请 3.意见征询 4.数据查询
     * @return mixed
     * @date 2022/3/11
     */
    public function getCondition($builder, $condition, $type = self::LIST_AUDIT_TYPE)
    {
        $no = $condition['no'] ?? '';   //申请单号
        $apply_staff_id = $condition['apply_staff_id'] ?? '';   //申请人或姓名
        $pay_status = $condition['pay_status'] ?? 0;    //支付状态
        $pay_method = $condition['pay_method'] ?? 0;    //支付方式
        $currency = $condition['currency'] ?? 0;        //货币额类型
        $apply_date_start = $condition['apply_date_start'] ?? '';   //申请日期开始时间
        $apply_date_end = $condition['apply_date_end'] ?? '';       //申请日期结束时间
        $cost_company_id = $condition['cost_company_id'] ?? [];        //费用所属公司
        $pay_where = $condition['pay_where'] ?? '';        //境内境外支付
        $oa_type = $condition['oa_type'] ?? '';        //模块
        $ids = $condition['ids'] ?? '';        //勾选的列id
        $ticket_no = $condition['ticket_no'] ?? ''; //支票号
        $payer_date_start = $condition['payer_date_start'] ?? '';//支付日期开始时间
        $payer_date_end = $condition['payer_date_end'] ?? '';//支付日期结束时间
        if (isCountry(['TH','MY'])) {
            $planned_pay_date_start = $condition['planned_pay_date_start'] ?? '';//计划支付日期开始时间
            $planned_pay_date_end = $condition['planned_pay_date_end'] ?? '';//计划支付日期结束时间
            $overdue_unpaid = $condition['overdue_unpaid'] ?? 0;//到期未支付
            $bank_batch_number = $condition['bank_batch_number'] ?? '';//银行批次号查询
        }

        // 征询回复进度
        $is_reply = $condition['is_reply'] ?? GlobalEnums::CONSULTED_REPLY_STATE_PENDING;
        $is_reply = in_array($is_reply, GlobalEnums::CONSULTED_REPLY_STATE_ITEM) ? $is_reply : GlobalEnums::CONSULTED_REPLY_STATE_PENDING;

        // 审批处理进度
        $flag = $condition['flag'] ?? GlobalEnums::AUDIT_TAB_PENDING;
        $flag = in_array($flag, GlobalEnums::AUDIT_TAB_STATE_ITEM) ? $flag : GlobalEnums::AUDIT_TAB_PENDING;

        $builder->leftjoin(PaymentCheck::class, 'pc.payment_id = p.id  and pc.is_deleted = ' . GlobalEnums::IS_NO_DELETED, 'pc');

        if ($type == self::LIST_AUDIT_TYPE) {
            $builder = (new WorkflowServiceV2())->getBizWorkflowOrderListByProcessState($builder, $flag, [Enums::WF_PAY_TYPE], $condition['uid'], 'p');
            if ($flag == GlobalEnums::AUDIT_TAB_PENDING) {
                $builder->inWhere('p.pay_status', [PayEnums::PAYMENT_MODULE_PAY_STATUS_PENDING, PayEnums::PAYMENT_MODULE_PAY_STATUS_ING, PayEnums::PAYMENT_MODULE_PAY_STATUS_FLASH_PAY_FAILED]);
            } elseif ($flag == GlobalEnums::AUDIT_TAB_PROCESSED) {
                $builder->notInWhere('p.pay_status', [PayEnums::PAYMENT_MODULE_PAY_STATUS_PENDING, PayEnums::PAYMENT_MODULE_PAY_STATUS_FLASH_PAY_FAILED]);
            }

            //16325需求，非已处理的需要关联payment_pay
            if ($flag != GlobalEnums::AUDIT_TAB_PROCESSED) {
                $builder->leftjoin(PaymentPay::class, 'pay.payment_id = p.id', 'pay');
            }

        } else if ($type == self::LIST_APPLY_TYPE) {
            $builder->leftjoin(
                WorkflowRequestModel::class,
                'w.biz_type=' . Enums::WF_PAY_TYPE . ' and w.biz_value=p.id and is_abandon = 0 ',
                'w'
            );
            $builder->andWhere(
                'p.apply_staff_id = :apply_staff_id: or w.create_staff_id=:create_staff_id:',
                ['apply_staff_id' => $condition['uid'], 'create_staff_id' => $condition['uid']]
            );
            $builder->orderBy('p.id desc');

        } else if ($type == self::LIST_REPLY_TYPE) {
            // 意见征询回复列表
            $builder->leftjoin(WorkflowRequestModel::class, 'request.biz_value = p.id', 'request');
            $builder->leftjoin(WorkflowRequestNodeAt::class, 'request.id = reply.request_id', 'reply');
            $builder->andWhere('request.biz_type = :biz_type: AND request.is_abandon = :is_abandon:', [
                'biz_type' => Enums::WF_PAY_TYPE,
                'is_abandon' => GlobalEnums::WORKFLOW_ABANDON_STATE_NO,
            ]);
            $builder->andWhere('reply.staff_id = :current_user_id:', ['current_user_id' => $condition['uid']]);

            // 当前用户的回复数据
            switch ($is_reply) {
                // 待处理
                case GlobalEnums::CONSULTED_REPLY_STATE_PENDING:
                    // 待回复 + 待支付/支付中/银行支付中
                    $builder->andWhere('reply.is_reply = :is_reply:', ['is_reply' => GlobalEnums::CONSULTED_REPLY_STATE_PENDING]);
                    $builder->inWhere('p.pay_status', [
                        PayEnums::PAYMENT_MODULE_PAY_STATUS_PENDING,
                        PayEnums::PAYMENT_MODULE_PAY_STATUS_ING,
                        PayEnums::PAYMENT_MODULE_PAY_STATUS_BANKING,
                    ]);
                    $builder->orderBy('reply.created_at ASC');

                    break;

                // 已处理
                case GlobalEnums::CONSULTED_REPLY_STATE_PROCESSED:
                    $builder->andWhere('reply.is_reply = :is_reply:', ['is_reply' => GlobalEnums::CONSULTED_REPLY_STATE_PROCESSED]);
                    $builder->orderBy('reply.reply_at DESC');

                    break;
            }

        } else if ($type == self::LIST_DATA_TYPE) {
            $builder->orderBy('p.id desc');
        }

        if (!empty($ids)) {
            $builder->andWhere('p.id in ({ids:array})', ['ids' => $ids]);
        }

        if (!empty($no)) {
            if (is_array($no)) {
                $builder->inWhere('p.no', $no);
            } else {
                $builder->andWhere('p.no = :no:', ['no' => $no]);
            }
        }

        if (!empty($apply_staff_id)) {
            $builder->andWhere(
                'p.apply_staff_id = :apply_id: or p.apply_staff_name=:apply_id:',
                ['apply_id' => $apply_staff_id]
            );
        }

        if (!empty($pay_status)) {
            $builder->andWhere('p.pay_status = :pay_status:', ['pay_status' => $pay_status]);
        }

        if (!empty($pay_method)) {
            $builder->andWhere('p.pay_method = :pay_method:', ['pay_method' => $pay_method]);
        }

        if (!empty($currency)) {
            $builder->andWhere('p.currency = :currency:', ['currency' => $currency]);
        }

        if (!empty($apply_date_start)) {
            $builder->andWhere('p.apply_date >= :apply_date_start:', ['apply_date_start' => $apply_date_start]);
        }

        if (!empty($apply_date_end)) {
            $builder->andWhere('p.apply_date <= :apply_date_end:', ['apply_date_end' => $apply_date_end]);
        }

        if (!empty($cost_company_id)) {
            if (is_array($cost_company_id)) {
                $builder->andWhere('p.cost_company_id IN ({cost_company_id:array})', ['cost_company_id' => array_values($cost_company_id)]);
            } else {
                $builder->andWhere('p.cost_company_id = :cost_company_id:', ['cost_company_id' => $cost_company_id]);
            }
        }

        if (!empty($pay_where)) {
            $builder->andWhere('p.pay_where = :pay_where:', ['pay_where' => $pay_where]);
        }

        if (!empty($oa_type)) {
            $builder->andWhere('p.oa_type = :oa_type:', ['oa_type' => $oa_type]);
        }
        if (!empty($ticket_no)) {
            $builder->andWhere('pc.ticket_no = :ticket_no:', ['ticket_no' => $ticket_no]);
        }
        //支付日期开始时间
        if (!empty($payer_date_start)) {
            $builder->andWhere('p.payer_date >= :payer_date_start:', ['payer_date_start' => gmdate('Y-m-d H:i:s', strtotime($payer_date_start . ' 00:00:00'))]);
        }
        //支付日期结束时间
        if (!empty($payer_date_end)) {
            $builder->andWhere('p.payer_date <= :payer_date_end:', ['payer_date_end' => gmdate('Y-m-d H:i:s', strtotime($payer_date_end . ' 23:59:59'))]);
        }
        //计划支付日期开始时间
        if (!empty($planned_pay_date_start)) {
            $builder->andWhere('p.planned_pay_date >= :planned_pay_date_start:', ['planned_pay_date_start' => $planned_pay_date_start]);
        }
        //计划支付日期结束时间
        if (!empty($planned_pay_date_end)) {
            $builder->andWhere('p.planned_pay_date <= :planned_pay_date_end:', ['planned_pay_date_end' => $planned_pay_date_end]);
        }
        //到期未支付
        if (!empty($overdue_unpaid)) {
            $builder->andWhere('p.planned_pay_date <= :overdue_unpaid_date: and p.pay_status=:overdue_unpaid_pay_status:', ['overdue_unpaid_date' => date('Y-m-d'),'overdue_unpaid_pay_status' => Enums::PAYMENT_PAY_STATUS_PENDING]);
        }
        //银行批次号
        if (!empty($bank_batch_number)) {
            $builder->andWhere('p.bank_batch_number=:bank_batch_number:', ['bank_batch_number' => $bank_batch_number]);
        }
        return $builder;
    }

    /**
     * 列表数据处理
     *
     * @param $staff_info_id
     * @param $items
     * @param int $type
     * @return array
     * @throws BusinessException
     * @date 2022/3/17
     */
    public function handleListItems($staff_info_id, $items, $type = 1)
    {
        if (empty($items) || !is_array($items)) {
            return [];
        }
        if ($type == 1) {
            $ids = array_column($items, 'id');
            //获取 $flow_level[业务id]=>level
            $flow_level = (new PayFlowService())->getAuditLevelById($ids, $staff_info_id);
        }
        $sourcing = $wages = [];
        foreach ($items as $crowd_sourcing) {
            if ($crowd_sourcing['oa_type'] == BankFlowEnums::BANK_FLOW_OA_TYPE_WAGE) {
                $sourcing[] = $crowd_sourcing['no'];
            }
        }
        if (!empty($sourcing)) {

            $wages = WagesModel::find([
                'conditions' => 'no in ({no:array})',
                'bind' => ['no' => $sourcing],
            ])->toArray();
            $wages = array_column($wages, 'apply_type', 'no');
        }
        $bankPaymentTypeMap = $this->getBankPaymentTypeMap();
        foreach ($items as &$item) {
            $item['oa_type_crowd_sourcing'] = $wages[$item['no']] ?? '';
            $item['oa_type_text'] = static::$t->_(Enums\BankFlowEnums::$oa_type_id_to_lang_key[$item['oa_type']]);
            $item['pay_status_text'] = static::$t->_(PayEnums::$payment_module_pay_status[$item['pay_status']]);
            //新增【实付金额总计】
            $currency_text = static::$t->_(GlobalEnums::$currency_item[$item['currency']]);
            $item['amount_total_actually_text'] = $item['amount_total_actually'] . ' ' . $currency_text;
            //新增 支付方式
            $item['pay_method_text'] = static::$t->_(Enums::$payment_method[$item['pay_method']]);
            if ($type == 1) {
                $item['cur_level'] = $flow_level[$item['id']] ?? 0;
                $item['cur_level_text'] = static::$t->_('cur_level_' . $item['cur_level']);
            }
            //银行支付方式
            $item['bank_pay_type'] = empty($item['bank_pay_type']) ? '' : $item['bank_pay_type'];
            $item['bank_pay_type_text'] = empty($item['bank_pay_type']) ? '' : ($bankPaymentTypeMap[$item['bank_pay_type']] ?? '');
        }

        return $items;
    }

    /**
     * 获取单据详情
     *
     * @param int $id ID
     * @param int $user_id 员工工号
     * @param bool $is_data true：回复详情、数据查询详情，false我的支付详情
     * @return array
     */
    public function getDetail(int $id, int $user_id, bool $is_data = false)
    {
        $code = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $data = [];

        try {
            $item = Payment::findFirst([
                'conditions' => 'id = :id:',
                'bind' => ['id' => $id],
            ]);
            if (empty($item)) {
                throw new ValidationException('not found item', ErrCode::$VALIDATE_ERROR);
            }

            $service = new PayFlowService();
            $request = $service->getRequest($id);
            if (empty($request)) {
                throw new ValidationException('not found workflow', ErrCode::$VALIDATE_ERROR);
            }

            $business_level = $service->getAuditLevelById([$id], $user_id);
            $cur_level = $business_level[$id] ?? 0;

            $can_edit_field = null;

            // 待审批节点, 是否可评论
            $is_can_comment = false;
            if (!$is_data) {
                //是否可以访问
                $flag = $service->isCanView($request, $user_id);
                if ($flag !== true) {
                    throw new ValidationException($flag, ErrCode::$VALIDATE_ERROR);
                }

                $pending_node_info = $service->getPendingNodeInfo($request, $user_id);
                $can_edit_field = !empty($pending_node_info['can_edit_field']) ? $pending_node_info['can_edit_field'] : $can_edit_field;
                $is_can_comment = isset($pending_node_info['is_comment']) && $pending_node_info['is_comment'];
            }

            $pays = $item->getPays();
            $pays = $pays ? $pays->toArray() : [];

            $attachments = $item->getFiles();
            $attachments = $attachments ? $attachments->toArray() : [];

            //支票支付方式-支付记录信息
            $checks = $item->getChecks();
            $pay_check = $checks ? $checks->toArray() : [];

            $data = $item->toArray();
            $data['pay_date'] = !empty($data['pay_date']) ? date('Y-m-d', strtotime($data['pay_date'])) : '';
            //待回复征询ID
            $ask = (new FYRService())->getRequestToByReplyAsk($request, $user_id);
            $data['ask_id'] = $ask ? $ask->id : '';

            $data['pays'] = $pays;
            $data['attachments'] = $attachments;
            $data['supplements'] = $item->getSupplements()->toArray();
            $data['pay_check'] = $pay_check ? $pay_check : (!empty($data['pay_check']) ? json_decode($data['pay_check'], 1) : []);
            $data['can_edit_field'] = $can_edit_field;
            $data['auth_logs'] = $service->getAuditLogs($data);
            $data['cur_level'] = $cur_level;
            $data['is_can_comment'] = $is_can_comment;
            $data['bank_pay_type'] = empty($data['bank_pay_type']) ? '' : $data['bank_pay_type'];
            $bankPaymentTypeMap = $this->getBankPaymentTypeMap();
            $data['bank_pay_type_text'] = empty($data['bank_pay_type']) ? '' : ($bankPaymentTypeMap[$data['bank_pay_type']] ?? '');
            //未支付原因分类
            $data['not_pay_reason_category_text'] =$this->getNotPayReasonCategoryText($data);
            //计划支付日期不允许早于应付日期-x天
            $data['payment_date_cannot_precede_due_date_num'] =  (int) SettingEnvModel::getValByCode('payment_date_cannot_precede_due_date_num','0');

            $this->handleDetailItem($data);
        } catch (ValidationException $e) {
            $code = ErrCode::$VALIDATE_ERROR;
            $message = $e->getMessage();
        } catch (Exception $e) {
            $code = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        }
        if (!empty($real_message)) {
            $this->logger->error('pay-getDetail-failed:' . $real_message);
        }
        return [
            'code' => $code,
            'message' => $message,
            'data' => $data,
        ];
    }

    /**
     * 是否需要未支付原因分类
     *
     * @param $paymentInfo
     * @return bool
     * @throws ValidationException
     */
    public function needNotPayReasonsCategory($paymentInfo): bool
    {
        if (!isset($paymentInfo['oa_type'])) {
            throw new ValidationException('oa_type is not set');
        }
        return in_array(get_country_code(), [GlobalEnums::TH_COUNTRY_CODE, GlobalEnums::PH_COUNTRY_CODE, GlobalEnums::MY_COUNTRY_CODE]) && $paymentInfo['oa_type'] == BankFlowEnums::BANK_FLOW_OA_TYPE_AGENCY_PAYMENT;
    }

    /**
     * 未支付原因分类文本
     * @param $paymentInfo
     * @return string
     */
    public function getNotPayReasonCategoryText($paymentInfo): string
    {
        if (!in_array(get_country_code(), [GlobalEnums::TH_COUNTRY_CODE, GlobalEnums::PH_COUNTRY_CODE, GlobalEnums::MY_COUNTRY_CODE]) || empty($paymentInfo['not_pay_reason_category'])) {
            return '';
        }
        $map = $this->getAgencyNotPayReasonCategory();
        if (!isset($map[$paymentInfo['not_pay_reason_category']])) {
            return '';
        }
        return static::$t->_($map[$paymentInfo['not_pay_reason_category']]);

    }

    /**
     * 格式化详情
     *
     * @param $data
     */
    public function handleDetailItem(&$data)
    {
        $data['currency_text'] = static::$t->_(GlobalEnums::$currency_item[$data['currency']]);
        $data['pay_method_text'] = static::$t->_(Enums::$payment_method[$data['pay_method']]);
        $data['pay_where_text'] = static::$t->_(PayEnums::$pay_where_id_to_lang_key[$data['pay_where']]);
        $data['pay_bank_flow_date'] = $data['pay_bank_flow_date'] ?? '';
        $data['pay_date'] = $data['pay_date'] ?? '';
    }

    /**
     * 我的申请-获取详情
     * @param $id
     * @param $user_id
     * @param bool $is_data
     * @return array
     * @date 2022/3/3
     */
    public function getMyDetail($id, $user_id, $is_data = false)
    {
        $code = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $data = [];
        try {
            $item = Payment::findFirst([
                'conditions' => 'id = :id:',
                'bind' => ['id' => $id],
            ]);
            if (empty($item)) {
                throw new ValidationException('not found item', ErrCode::$VALIDATE_ERROR);
            }

            $service = new PayFlowService();
            $request = $service->getRequest($id);
            if (empty($request)) {
                throw new ValidationException('not found workflow', ErrCode::$VALIDATE_ERROR);
            }
            if ($item->apply_staff_id != $user_id && $request->create_staff_id != $user_id) {
                throw new ValidationException('no permission', ErrCode::$VALIDATE_ERROR);
            }
            $pays = $item->getPays();
            $pays = $pays ? $pays->toArray() : [];

            $attachments = $item->getFiles();
            $attachments = $attachments ? $attachments->toArray() : [];

            $data = $item->toArray();

            $data['oa_type_crowd_sourcing'] = '';
            if ($data['oa_type'] == BankFlowEnums::BANK_FLOW_OA_TYPE_WAGE) {
                $wages = WagesModel::findFirst([
                    'columns' => ['apply_type'],
                    'conditions' => 'no = :no:',
                    'bind' => ['no' => $data['no']],
                ]);
                $data['oa_type_crowd_sourcing'] = $wages->apply_type ?? '';
            }

            $data['pays'] = $pays;
            $data['attachments'] = $attachments;
            $data['auth_logs'] = (new PayFlowService())->getAuditLogs($data);

            $this->handleDetailItem($data);
        } catch (ValidationException $e) {
            $code = ErrCode::$VALIDATE_ERROR;
            $message = $e->getMessage();
        } catch (Exception $e) {
            $code = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        }

        if (!empty($real_message)) {
            $this->logger->error('pay-getDetail-failed:' . $real_message);
        }
        return [
            'code' => $code,
            'message' => $message,
            'data' => $data,
        ];
    }


    /**
     * FlashPay使用SFTP方式的额度
     * @return mixed|true
     * @throws ValidationException
     */
    public function getUseFlashPaySftpQuota()
    {
        if(!isCountry('TH')){
            return false;
        }
        static $quota;
        if (empty($quota)) {
            $quota = EnumsService::getInstance()->getSettingEnvValue('use_flash_pay_sftp_quota');
        }
        if(empty($quota) || $quota < 0 || !is_numeric($quota)){
            throw new ValidationException('use_flash_pay_sftp_quota is not set', ErrCode::$VALIDATE_ERROR);
        }
        return $quota;

    }


    /**
     * 审批 支付选择 是或否
     *
     * @param array $params 审批数据组
     * @param array $user 审批人信息组
     * @param integer $level 审批级别, 1一级、2二级、3三级
     * @return array
     * @date 2022/3/18
     */
    public function audit($params, $user, $level = 0)
    {
        $code = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $data = [];

        $db = $this->getDI()->get('db_oa');

        try {
            $db->begin();
            $item = Payment::findFirst([
                'conditions' => 'id = :id:',
                'bind' => ['id' => $params['id']],
                'for_update' => true,
            ]);
            if (empty($item)) {
                throw new ValidationException('not found the item', ErrCode::$VALIDATE_ERROR);
            }
            $tmp = [];
            $pay = new PayFlowService();
            $itemArr = [];
            if (get_country_code() == 'MY') {
                $plannedPayDate = $this->batchAudit ? $item->planned_pay_date : ($params['planned_pay_date'] ?? null);
                //计划支付日期-N天是否晚于当前日期
                if (!empty($plannedPayDate) && $level == 1) {
                    $checkDays = SettingEnvModel::getValByCode('planned_pay_date_check_days');
                    if ($checkDays && strtotime($plannedPayDate . ' -' . $checkDays . ' days') > strtotime(date('Y-m-d'))) {
                        if ($this->batchAudit) {
                            $validateMess = static::$t->_('batch_planned_pay_date_check_days',
                                ['no' => $item->no, 'planned_pay_date' => $plannedPayDate, 'days' => $checkDays]);
                        } else {
                            $validateMess = static::$t->_('planned_pay_date_check_days',
                                ['days' => $checkDays]);
                        }
                        throw new ValidationException($validateMess, ErrCode::$VALIDATE_ERROR);
                    }
                }
                //批量支付没有计划支付日期
                if (!$this->batchAudit) {
                    $item->planned_pay_date = empty($params['planned_pay_date']) ? null : $params['planned_pay_date'];
                }
                $item->bank_batch_number = empty($params['bank_batch_number']) ? '' : $params['bank_batch_number'];
                $item->bank_pay_type     = empty($params['bank_pay_type']) ? 0 : $params['bank_pay_type'];
            } elseif (isCountry('TH')) {
                if ($params['is_pay'] == PayEnums::IS_PAY_YES && $level == PayEnums::PAYMENT_ONE_LEVEL_AUDIT && !empty($params['planned_pay_date']) && $item->default_planned_pay_date) {
                    $payment_date_cannot_precede_due_date_num = SettingEnvModel::getValByCode('payment_date_cannot_precede_due_date_num',
                        '0');
                    $limit_date                               = strtotime($item->default_planned_pay_date . ' - ' . $payment_date_cannot_precede_due_date_num . ' days');
                    if (strtotime($params['planned_pay_date']) < $limit_date) {
                        throw new ValidationException(static::$t->_('planned_pay_date_early_error',
                            ['date' => date('Y-m-d', $limit_date)]), ErrCode::$VALIDATE_ERROR);
                    }
                    $item->planned_pay_date = $params['planned_pay_date'];
                }
                if (empty($item->planned_pay_date)) {
                    $planned_pay_date_required = static::$t->_('22402_planned_pay_date_required');
                    FlashPayHelper::sendNotice($planned_pay_date_required . ' no :' . $item->no);
                    throw new ValidationException($planned_pay_date_required,
                        ErrCode::$VALIDATE_ERROR);
                }
            }

            $item->updated_at = gmdate('Y-m-d H:i:s');    //更新时间，保持created_at，updated_at，payer_date 0时区存储格式
            $item->save();
            //支付=是的时候调用通过
            if ($params['is_pay'] == PayEnums::IS_PAY_YES) {
                //附件
                $tmp['attachments'] = $params['attachments'] ?? [];
                //支付备注
                $tmp['pay_remark'] = $params['pay_remark'] ?? '';

                switch ($item->pay_method) {
                    case Enums::PAYMENT_METHOD_CASH:
                        //付款日期
                        if (isset($params['pay_date']) && !empty($params['pay_date'])) {
                            $tmp['pay_date'] = $params['pay_date'];
                        }
                        break;
                    case Enums::PAYMENT_METHOD_BANK_TRANSFER:
                        if (isset($params['pay_bank_name']) && !empty($params['pay_bank_name'])) {
                            $tmp['pay_bank_name'] = $params['pay_bank_name'];
                        }
                        if (isset($params['pay_bank_account']) && !empty($params['pay_bank_account'])) {
                            $tmp['pay_bank_account'] = $params['pay_bank_account'];
                        }
                        if (isset($params['pay_bank_flow_date'])) {
                            //若银行流水为空也需要保存
                            $tmp['pay_bank_flow_date'] = $params['pay_bank_flow_date'];
                        }

                        //16325需求，新增银行转帐-在线支付逻辑判断；一级支付人才需要走以下逻辑验证
                        if ($level == PayEnums::PAYMENT_ONE_LEVEL_AUDIT) {
                            $tmp['is_online_pay'] = $this->checkPaymentOnlinePay($item, $tmp['pay_bank_name'] ?? '', $tmp['pay_bank_account'] ?? '');
                        }
                        break;
                    case Enums::PAYMENT_METHOD_CHECK:
                        //16325需求需要区分是否对接了支票模块
                        if ($this->checkChequeIsOpen()) {
                            //对接支票模块，走以下逻辑，只是加了开关代码逻辑无任何改动；后期这里需要高峰优化
                            if (in_array($level, [PayEnums::PAYMENT_TWO_LEVEL_AUDIT])) {
                                //支票号xxxx关联多个单据，请使用批量操作
                                $this->getEditPaymentCheck($params);
                            }

                            if (isset($params['pay_check']) && !empty($params['pay_check'])) {

                                //如果是支票 可修改的时候开始回调支票数据 且只能编辑一次
                                $flow_level = (new PayFlowService())->getAuditLevelById([$params['id']], $user['id']);

                                if ($flow_level[$params['id']] == ChequeEnums::CHEQUE_FLOW_LEVEL) {
                                    $pay_check_arr_list = $params['pay_check'];
                                    $pay_check_arr = [];

                                    //循环传入的支票号数据，支票号数据可以重复，处理成一个支票号对应多条数据
                                    foreach ($pay_check_arr_list as $check) {
                                        //先把支票号放入一个盒子
                                        $pay_check_arr[$check['ticket_no']][] = $check;
                                        if (!empty($pay_check_arr[$check['ticket_no']])) {

                                            //判断同一个支票号签票必须相同
                                            if (count(array_unique(array_column($pay_check_arr[$check['ticket_no']], 'check_date'))) > 1) {
                                                throw new ValidationException(static::$t->_('ticket_no_check_date_inequality'), ErrCode::$VALIDATE_ERROR);
                                            }
                                            //判断同一个支票号计划承兑日期必须相同
                                            if (count(array_unique(array_column($pay_check_arr[$check['ticket_no']], 'date'))) > 1) {
                                                throw new ValidationException(static::$t->_('ticket_no_date_inequality'), ErrCode::$VALIDATE_ERROR);
                                            }
                                            //判断同一个支票号收款人必须相同
                                            if (count(array_unique(array_column($pay_check_arr[$check['ticket_no']], 'payee_name'))) > 1) {
                                                throw new ValidationException(static::$t->_('ticket_no_payee_name_inequality'), ErrCode::$VALIDATE_ERROR);
                                            }

                                            //判断同一个支票号币种必须相同
                                            if (count(array_unique(array_column($pay_check_arr[$check['ticket_no']], 'currency'))) > 1) {
                                                throw new ValidationException(static::$t->_('ticket_no_currency_inequality'), ErrCode::$VALIDATE_ERROR);
                                            }
                                        }
                                    }
                                    //传入的支票号到台账获取数据 用于循环的时候比对状态
                                    $ticket_no_arr = array_values(array_keys($pay_check_arr));
                                    $cheque_account_list = ChequeAccountModel::find([
                                        'cheque_code in ({cheque_code:array})',
                                        'bind' => [
                                            'cheque_code' => $ticket_no_arr,
                                        ],
                                        'columns' => 'use_status,currency,valid_days,cheque_code',
                                    ])->toArray();

                                    if (empty($cheque_account_list)) {
                                        throw new ValidationException(static::$t->_('cheque_not_empty'), ErrCode::$VALIDATE_ERROR);
                                    }
                                    $cheque_account_list = array_column($cheque_account_list, null, 'cheque_code');

                                    //开始循环 ，如果提交的支票号是[1,1,2,3,3],就会变成[1=>[[1],[1]],2=>[[2]],[3=>[[3],[3]]]] 这样计算合计金额方便
                                    foreach ($pay_check_arr as $key => $check_code) {
                                        ChequeService::getInstance()->saveChequeAccount($key, $check_code, $cheque_account_list, $db);
                                    }
                                }
                                $sum_amount = 0;
                                foreach ($params['pay_check'] as &$check_value) {
                                    if (!empty($check_value['check_date'])) {
                                        Validation::validate($params, ['pay_check[*].check_date' => 'Date']);
                                    }
                                    $check_value['payment_id'] = $item->id;
                                    $check_value['currency'] = $item->currency;
                                    $check_value['currency_text'] = static::$t->_(GlobalEnums::$currency_item[$item->currency]);
                                    $sum_amount += $check_value['amount'];
                                }

                                if (!empty(bccomp($sum_amount, $item->amount_total_actually, 2))) {
                                    throw new ValidationException(static::$t->_('cheque_account_pay_amount_not_actual_amount'), ErrCode::$VALIDATE_ERROR);
                                }
                                // 验证支票数量必须等于支付信息数量
                                $pays = $item->getPays();
                                $pays = $pays ? $pays->toArray() : [];
                                if (count($params['pay_check']) != count($pays) && !in_array($item->oa_type, [BankFlowEnums::BANK_FLOW_OA_TYPE_ORDINARY_PAYMENT, BankFlowEnums::BANK_FLOW_OA_TYPE_PURCHASE_PAYMENT])) {
                                    throw new ValidationException(static::$t->_('pay_check_count_error'), ErrCode::$VALIDATE_ERROR);
                                }
                                $tmp['pay_check'] = $params['pay_check'];
                            }
                        } else {
                            //未对接支票模块，走以下逻辑；一级支付人才需要走以下逻辑验证
                            if (isset($params['pay_check']) && !empty($params['pay_check']) && $level == PayEnums::PAYMENT_ONE_LEVEL_AUDIT) {
                                $sum_amount = 0;
                                foreach ($params['pay_check'] as &$check_value) {
                                    $check_value['payment_id'] = $item->id;
                                    $check_value['currency'] = $item->currency;
                                    $check_value['currency_text'] = static::$t->_(GlobalEnums::$currency_item[$item->currency]);
                                    $sum_amount += $check_value['amount'];
                                }
                                if ($sum_amount > $item->amount_total_actually) {
                                    throw new ValidationException(static::$t->_('pay_check_all_amount_error'), ErrCode::$VALIDATE_ERROR);
                                }
                                $tmp['pay_check'] = $params['pay_check'];
                            }
                        }
                        break;
                }
                $tmp['is_pay'] = $params['is_pay'];
                $res = $pay->approve($params['id'], $params['note'] ?? '', $user, $tmp);
                if (!empty($res->approved_at)) {
                    //通过后，有可能会更改字段
                    $item = Payment::findFirst([
                        'conditions' => 'id = :id:',
                        'bind' => ['id' => $params['id']],
                    ]);
                    //16325需求，若是在线支付单据则标记为PAY支付中，否则标记为银行支付中
                    $item->pay_status = ($item->is_online_pay == PayEnums::IS_ONLINE_PAY_YES) ? PayEnums::PAYMENT_MODULE_PAY_STATUS_FLASH_PAY_ING : PayEnums::PAYMENT_MODULE_PAY_STATUS_BANKING;
                    $flash_pay_sftp_quota = $this->getUseFlashPaySftpQuota();
                    $this->logger->info('泰国二级支付人通过，开始判断是否走sftp，支付金额：' . $item->amount_total_actually . ' sftp额度：' . $flash_pay_sftp_quota . ' no : ' . $item->no);
                    if (isCountry('TH') && $level == PayEnums::PAYMENT_TWO_LEVEL_AUDIT && $flash_pay_sftp_quota > 0  && $item->is_online_pay == PayEnums::IS_ONLINE_PAY_YES && $item->pay_status == PayEnums::PAYMENT_MODULE_PAY_STATUS_FLASH_PAY_ING) {
                        $item->flash_pay_method = (bccomp($item->amount_total_actually, $flash_pay_sftp_quota, 2) >= 0) ? PayEnums::PAYMENT_MODULE_FLASH_PAY_METHOD_SFTP : PayEnums::PAYMENT_MODULE_FLASH_PAY_METHOD_API;
                    }
                    //兼容自动审批通过；关于pay在线支付的信息按照默认数据库默认值【兼容pay支付失败又重新走支付审批的单子】
                    $item->out_trade_no = '';//外部交易号，如FlashPay交易号
                    $item->out_batch_number = null;//FlashPay SFTP 批次号
                    $item->out_trade_at = null;//外部交易时间
                    $item->out_trade_status = PayEnums::PAYMENT_MODULE_PAY_OUT_TRADE_STATUS_WAIT;//外部交易状态：0交易待支付
                    $item->out_send_status = PayEnums::PAYMENT_MODULE_PAY_SEND_STATUS_NO;//发送状态：0未传输
                    $item->out_send_at = null;//外部交易发送时间
                    $item->out_trade_code = null;//外部交易code码
                    $item->payer_id = null;//最终支付人
                    $item->payer_date = null;//最终支付日期
                    $item->save();

                    $itemArr = $item->toArray();
                } else {
                    //通过后，有可能会更改字段
                    $item = Payment::findFirst([
                        'conditions' => 'id = :id:',
                        'bind' => ['id' => $params['id']],
                    ]);
                    //16325需求，支付状态=待支付、pay支付失败的单子，都变更为支付中
                    if ($item->pay_status == PayEnums::PAYMENT_MODULE_PAY_STATUS_PENDING || $item->pay_status == PayEnums::PAYMENT_MODULE_PAY_STATUS_FLASH_PAY_FAILED) {
                        $item->pay_status = PayEnums::PAYMENT_MODULE_PAY_STATUS_ING;
                        //关于pay在线支付的信息按照默认数据库默认值【兼容pay支付失败又重新走支付审批的单子】
                        $item->out_trade_no = '';//外部交易号，如FlashPay交易号
                        $item->out_trade_at = null;//外部交易时间
                        $item->out_batch_number = null;//FlashPay SFTP 批次号
                        $item->out_trade_status = PayEnums::PAYMENT_MODULE_PAY_OUT_TRADE_STATUS_WAIT;//外部交易状态：0交易待支付
                        $item->out_send_status = PayEnums::PAYMENT_MODULE_PAY_SEND_STATUS_NO;//发送状态：0未传输
                        $item->out_send_at = null;//外部交易发送时间
                        $item->out_trade_code = null;//外部交易code码
                        $item->payer_id = null;//最终支付人
                        $item->payer_date = null;//最终支付日期
                        $item->save();
                    }
                    $itemArr = $item->toArray();
                }
            } else {
                //默认是支付失败, 让申请人去修改支付信息或撤回
                $pay_status = PayEnums::PAYMENT_MODULE_PAY_STATUS_FAILED;
                //泰国一级支付人选择支付否, 直接变成未支付(终态)
                $end_pay_condition = get_country_code() == GlobalEnums::TH_COUNTRY_CODE && $level == PayEnums::PAYMENT_ONE_LEVEL_AUDIT;
                if ($end_pay_condition) {
                    $pay_status = PayEnums::PAYMENT_MODULE_PAY_STATUS_NOTPAY;
                }
                $note = $params['not_pay_reason'];
                $item->is_pay = PayEnums::IS_PAY_NO;
                $item->not_pay_reason = $note;
                if (PayService::getInstance()->needNotPayReasonsCategory($item->toArray())) {
                    if (empty($params['not_pay_reason_category'])) {
                        throw new ValidationException(static::$t->_('not_pay_reason_category_is_required'),
                            ErrCode::$VALIDATE_ERROR);
                    }
                    $item->not_pay_reason_category = $params['not_pay_reason_category'];
                }
                $item->pay_status = $pay_status;
                $item->save();
                $pay->reject($params['id'], $note, $user);
                //16325修改为对接支票模块，才走以下逻辑
                if ($item->pay_method == Enums::PAYMENT_METHOD_CHECK && $this->checkChequeIsOpen()) {
                    $old_checks_list = $item->getChecks();
                    $old_checks_arr = $old_checks_list->toArray();
                    if (!empty($old_checks_arr)) {
                        $ids = implode(',', array_column($old_checks_arr, 'id'));
                        $update_payment_check = $db->updateAsDict(
                            (new PaymentCheck())->getSource(),
                            [
                                'is_deleted' => GlobalEnums::IS_DELETED,
                                'updated_at' => date('Y-m-d H:i:s', time()),
                            ],
                            [
                                'conditions' => " id IN ($ids)",
                            ]
                        );
                        if ($update_payment_check === false) {
                            throw new BusinessException('支票数据情况老数据失败, 数据: ' . json_encode($old_checks_arr, JSON_UNESCAPED_UNICODE) . '; 原因可能是: ' . get_data_object_error_msg($update_payment_check), ErrCode::$BUSINESS_ERROR);
                        }
                    }
                }
                //如果是泰国一级支付人选择否, 直接变成未支付
                if ($end_pay_condition) {
                    //业务单更新, 更新为未支付(终态,不再进行支付)
                    $item_arr = array_merge($item->toArray(), ['send_crowd' => true]);
                    FinalPayService::getInstance()->businessPay($item_arr, $user);
                }

                //不支付  发送邮件
                $this->sendEmail($item, [$item->apply_staff_id]);
            }

            //如果状态是已支付，才是完成。
            //if ($itemArr['pay_status'] == Enums::PAYMENT_MODULE_PAY_STATUS_PAY) {
            //    //业务数据支付
            //    $this->businessPay($itemArr,$user);
            //}
            $db->commit();
        } catch (ValidationException $e) {
            $code = $e->getCode();
            $message = $e->getMessage();
            $db->rollback();
        } catch (BusinessException $e) {
            $code = $e->getCode();
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        } catch (Exception $e) {
            $code = ErrCode::$SYSTEM_ERROR;
            $real_message = $e->getMessage();
            $message = static::$t->_('retry_later');
        }
        if (!empty($real_message)) {
            $db->rollback();
            $this->logger->error('pay-audit-failed:' . $real_message);
        }

        return [
            'code' => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data' => $data,
        ];
    }

    /**
     * 第二级的拒绝，拒绝后，回到第一级
     *
     * @param array $params 条件
     * @param array $user 用户数据
     * @param boolean $type true 为编辑驳回  false 为批量驳回
     * @return array
     */
    public function reject(array $params, array $user, bool $type = true)
    {
        $code = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $data = [];

        $db = $this->getDI()->get('db_oa');
        try {
            $db->begin();

            $item = Payment::findFirst([
                'conditions' => 'id = :id:',
                'bind' => ['id' => $params['id']],
                'for_update' => true,
            ]);
            if (empty($item)) {
                throw new ValidationException('not found item', ErrCode::$VALIDATE_ERROR);
            }

            $service = new PayFlowService();
            $request = $service->getRequest($params['id']);
            if (empty($request)) {
                throw new ValidationException('not found workflow', ErrCode::$VALIDATE_ERROR);
            }

            //检测申请单支付审批节点不一致,且只能是第二级
            $business_level = (new PayFlowService())->getAuditLevelById([$params['id']], $user['id']);
            $first_level = reset($business_level);
            if ($first_level != PayEnums::PAYMENT_TWO_LEVEL_AUDIT) {
                throw new ValidationException(static::$t->_('pay_level_not_second'), ErrCode::$VALIDATE_ERROR);
            }

            //16325需求需要区分是否对接了支票模块;
            if ($this->checkChequeIsOpen()) {
                //对接了支票模块走以下逻辑
                if ($type && $item->pay_method == Enums::PAYMENT_METHOD_CHECK) {
                    $this->getEditPaymentCheck($params);
                }

                $pay = new PayFlowService();
                //批量驳回,原因可以不填
                $reject_reason = $params['reject_reason'] ?? '';
                $pay->reject($params['id'], $reject_reason, $user);
                //支付为否的时候调用
                $revoke_cheque_account = ChequeService::getInstance()->revokeChequeAccount($params['id'], $db);
                if ($revoke_cheque_account['code'] != ErrCode::$SUCCESS) {
                    throw new ValidationException(static::$t->_('cheque_account_update_fail'), ErrCode::$CHEQUE_ACCOUNT_UPDATE_ERROR);
                }
                //废弃当前审批->重新生成审批流
                $recommit_result = (new PayFlowService())->recommit($item, $user);
                if (!$recommit_result) {
                    throw new BusinessException('重新发起审批流失败=', ErrCode::$PAYMENT_UPDATE_PAY_STATUS_ERROR);
                }

                if ($item->pay_method == Enums::PAYMENT_METHOD_CHECK) {
                    $old_checks_list = $item->getChecks();
                    $old_checks_arr = $old_checks_list->toArray();
                    if (!empty($old_checks_arr)) {
                        $ids = implode(',', array_column($old_checks_arr, 'id'));
                        $update_payment_check = $db->updateAsDict(
                            (new PaymentCheck())->getSource(),
                            [
                                'is_deleted' => GlobalEnums::IS_DELETED,
                                'updated_at' => date('Y-m-d H:i:s', time()),
                            ],
                            [
                                'conditions' => " id IN ($ids)",
                            ]
                        );
                        if ($update_payment_check === false) {
                            throw new BusinessException('支票数据情况老数据失败, 数据: ' . json_encode($old_checks_arr, JSON_UNESCAPED_UNICODE) . '; 原因可能是: ' . get_data_object_error_msg($update_payment_check), ErrCode::$BUSINESS_ERROR);
                        }
                    }
                }
            } else {
                //未对接了支票模块走以下逻辑
                $pay = new PayFlowService();

                //获取一级审批节点
                $node = $pay->getFirstNode($request->flow_id);

                //批量驳回,原因可以不填
                $reject_reason = $params['reject_reason'] ?? '';
                $pay->reject($params['id'], $reject_reason, $user);

                //回到一级审批
                $request->current_flow_node_id = $node->id;
                $request->current_node_auditor_id = $node->auditor_id;
                $request->state = Enums::WF_STATE_PENDING;//待审批
                $request->save();
            }

            $db->commit();
        } catch (ValidationException $e) {
            $code = $e->getCode();
            $message = $e->getMessage();
            $db->rollback();
        } catch (Exception $e) {
            $code = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage();
            $db->rollback();
        }

        if (!empty($real_message)) {
            $this->logger->error('pay-reject-failed:' . $real_message);
        }

        return [
            'code' => $code,
            'message' => $message,
            'data' => $data,
        ];
    }

    /**
     * 获取导出列表
     *
     * @param array $condition 导出查询条件
     * @param int $type 子菜单类型 4-数据查询
     * @return array
     * @throws GuzzleException
     */
    public function getExportList($condition = [], $type = self::LIST_DATA_TYPE)
    {
        $code = ErrCode::$SUCCESS;
        $message = '';
        $data = [];

        try {
            ini_set('memory_limit', '2048M');

            $builder = $this->modelsManager->createBuilder();
            $builder->columns(
                'distinct p.id,p.oa_type,p.no,p.apply_staff_id,p.apply_staff_name,p.cost_company_name,p.cost_department_name,p.pay_method,p.is_pay,p.pay_bank_name,p.pay_bank_account,p.pay_date,p.pay_bank_flow_date,p.not_pay_reason,p.pay_remark,p.pay_check,p.not_pay_reason,
                pp.bank_account_name,pp.bank_name,pp.bank_account,p.currency,p.amount_remark,pp.amount,
                p.amount_total_actually,p.amount_total_no_tax,p.amount_total_vat,p.amount_total_have_tax,p.amount_total_wht,p.amount_total_have_tax_no_wht,
                p.amount_loan,p.amount_reserve,p.amount_discount,p.default_planned_pay_date,p.planned_pay_date,p.bank_batch_number,p.bank_pay_type'
            );
            $builder->from(['p' => Payment::class]);
            $builder->leftjoin(PaymentPay::class, 'pp.payment_id = p.id', 'pp');
            $builder = $this->getCondition($builder, $condition, $type);
            $items = $builder->getQuery()->execute()->toArray();
            $this->handleExportListItems($items);

            $header = [
                static::$t->_('payment_export_module'),             //模块
                static::$t->_('global.number'),             //编号
                static::$t->_('global.applicant.name'),     //申请人姓名
                static::$t->_('global.applicant.id'),       //申请人工号
                static::$t->_('re_field_apply_company_name'),   //申请人所属公司
                static::$t->_('bank_flow_export_field_create_department_name'),    //申请人所属部门
                static::$t->_('pay_field_pay_method'),      //支付方式
                static::$t->_('pay_field_currency'),            //支付币种
                static::$t->_('global.remark'),                 //备注
                static::$t->_('pay_field_bank_account_name'),   //收款人银行账户名称
                static::$t->_('pay_field_bank_name'),           //收款银行名称
                static::$t->_('pay_field_bank_account'),        //收款账号
                static::$t->_('pay_field_account'),             //支付金额
                static::$t->_('total.amount.actually.paid'),     //实付金额总计
                static::$t->_('payment_store_renting_no_total_amount'), //不含税金额总计
                static::$t->_('payment_store_renting_vat_total_amount'), //vat总计
                static::$t->_('pay_field_amount_total_have_tax'),       // 含税金额总计（含VAT含WHT）
                static::$t->_('payment_store_renting_wht_total_amount'),    //wht金额总计
                static::$t->_('pay_field_amount_total_have_tax_no_wht'),    //含税金额总计（含VAT不含WHT）
                static::$t->_('pay_field_amount_loan'),                 //冲减借款金额
                static::$t->_('pay_field_amount_reserve'),              //冲减备用金金额
                static::$t->_('purchase.discount'),                      //折扣
                static::$t->_('payment_export_is_pay'),                      //是否支付
            ];
            if (isCountry(['TH','MY'])) {
                $header[] = static::$t->_('planned_pay_date');        //计划支付日期
                $header[] = static::$t->_('default_planned_pay_date');//应付日期(默认计划支付日期)
            }
            $header[] = static::$t->_('payment_export_pay_bank_name');   //支付银行
            $header[] = static::$t->_('payment_export_pay_bank_account');//银行账号
            if (get_country_code() == 'MY') {
                $header[] = static::$t->_('bank_batch_number');//银行批次号
                $header[] = static::$t->_('bank_pay_type_text');    //银行支付方式
            }
            $header[] = static::$t->_('payment_export_pay_date');            //支付日期
            $header[] = static::$t->_('payment_export_pay_check.ticket_no'); //支票号
            $header[] = static::$t->_('payment_export_pay_check.date');      //承兑日期
            $header[] = static::$t->_('payment_export_pay_check.amount');    //支票金额
            $header[] = static::$t->_('payment_export_pay_check.payee_name');//收款人名称
            $header[] = static::$t->_('payment_export_pay_bank_flow_date');  //银行流水日期
            $header[] = static::$t->_('payment_export_pay_remark');          //备注
            $header[] = static::$t->_('payment_export_not_pay_reason');      //未支付原因

            $fields = ['oa_type_text', 'no', 'apply_staff_name', 'apply_staff_id', 'cost_company_name', 'cost_department_name', 'pay_method_text', 'currency_text', 'amount_remark', 'bank_account_name', 'bank_name', 'bank_account',
                'amount', 'amount_total_actually', 'amount_total_no_tax', 'amount_total_vat', 'amount_total_have_tax', 'amount_total_wht',
                'amount_total_have_tax_no_wht', 'amount_loan', 'amount_reserve', 'amount_discount', 'is_pay_text',];
            if (isCountry(['TH','MY'])) {
                $fields[] = 'planned_pay_date';        //计划支付日期
                $fields[] = 'default_planned_pay_date';//应付日期(默认计划支付日期)
            }
            $fields[] = 'pay_bank_name';
            $fields[] = 'pay_bank_account';
            if (get_country_code() == 'MY') {
                $fields[] = 'bank_batch_number';     //银行批次号
                $fields[] = 'bank_pay_type_text';//银行支付方式
            }
            $fields[] = 'pay_date';
            $fields[] = 'pay_check_ticket_no';
            $fields[] = 'pay_check_date';
            $fields[] = 'pay_check_amount';
            $fields[] = 'pay_check_payee_name';
            $fields[] = 'pay_bank_flow_date';
            $fields[] = 'pay_remark';
            $fields[] = 'not_pay_reason';

            $exportData = [];
            foreach ($items as $item) {
                $tmp = [];
                foreach ($fields as $field) {
                    $tmp[] = $item[$field];
                }

                $exportData[] = $tmp;
            }

            $res = $this->exportExcel($header, $exportData);
            $data = $res['data'];

        } catch (Exception $e) {
            $code = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $this->logger->error('pay-getList-failed:' . $e->getMessage());
        }

        return [
            'code' => $code,
            'message' => $message,
            'data' => $data,
        ];
    }

    /**
     * 处理导出列表数据
     *
     * @param array $items 导出列表
     * @return array
     */
    public function handleExportListItems(&$items)
    {
        if (empty($items) || !is_array($items)) {
            return [];
        }
        /**
         * 13784【MY|OA|银行流水】 银行流水及支付模块迁移
         * https://flashexpress.feishu.cn/docx/doxcn8oZcw7n1PUJRdbcOuHdeTd
         * payment——pay_check字段为支付模块的支付信息，现在表中储存了jason字段，本次将其改成附表形式。
         * 新数据走表，老数据还是读pay_check字段
         */
        $payment_id_arr = array_column($items, 'id');
        $payment_check_list = PaymentCheck::find([
            'conditions' => 'payment_id in ({ids:array}) and is_deleted = :is_deleted:',
            'bind' => ['ids' => $payment_id_arr, 'is_deleted' => 0],
        ])->toArray();
        $pay_check_data = [];
        if (!empty($payment_check_list)) {
            foreach ($payment_check_list as $item) {
                $pay_check_data[$item['payment_id']] [] = $item;
            }
        }
        $bankPaymentTypeMap = $this->getBankPaymentTypeMap();
        foreach ($items as &$item) {
            $item['oa_type_text'] = static::$t->_(Enums\BankFlowEnums::$oa_type_id_to_lang_key[$item['oa_type']]);
            $item['pay_method_text'] = static::$t->_(Enums::$payment_method[$item['pay_method']]);
            $item['currency_text'] = static::$t->_(GlobalEnums::$currency_item[$item['currency']]);
            $item['is_pay_text'] = static::$t->_(PayEnums::$payment_is_pay_key[$item['is_pay']]);
            $item['bank_pay_type'] = empty($item['bank_pay_type']) ? '' : $item['bank_pay_type'];
            $item['bank_pay_type_text'] = empty($item['bank_pay_type']) ? '' : ($bankPaymentTypeMap[$item['bank_pay_type']] ?? '');
            //支票
            $item['pay_check_ticket_no'] = '';
            $item['pay_check_date'] = '';
            $item['pay_check_amount'] = '';
            $item['pay_check_payee_name'] = '';
            if ($item['pay_method'] == Enums::PAYMENT_METHOD_CHECK) {
                //支票支付方式，才做如下逻辑处理
                if (!empty($pay_check_data[$item['id']])) {
                    //表里存在发票信息
                    $pay_check_array = $pay_check_data[$item['id']];
                } else {
                    $pay_check_array = json_decode($item['pay_check'], true);
                }
                if (!empty($pay_check_array)) {
                    $pay_check_ticket_no = array_column($pay_check_array, 'ticket_no');
                    $pay_check_date = array_column($pay_check_array, 'date');
                    $pay_check_amount = array_column($pay_check_array, 'amount');
                    $pay_check_payee_name = array_column($pay_check_array, 'payee_name');
                    $item['pay_check_ticket_no'] = implode(';', $pay_check_ticket_no);
                    $item['pay_check_date'] = implode(';', $pay_check_date);
                    $item['pay_check_amount'] = implode(';', $pay_check_amount);
                    $item['pay_check_payee_name'] = implode(';', $pay_check_payee_name);
                }
            }
        }
    }

    /**
     * 支付模块对应付款银行列表
     *
     * @return array
     */
    public function getBankList()
    {
        $code = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $data = [];

        try {
            $json = EnvModel::getEnvByCode('payment_bank', '');
            if (!empty($json)) {
                $tmp = json_decode($json, 1);
                if (!empty($tmp)) {
                    $data = $tmp;
                }
            }
        } catch (Exception $e) {
            $code = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        }

        if (!empty($real_message)) {
            $this->logger->error('pay-getBankList-failed:' . $real_message);
        }

        return [
            'code' => $code,
            'message' => $message,
            'data' => $data,
        ];
    }

    /**
     * 撤回操作
     *
     * @param $id
     * @param $reason
     * @param $user
     * @return bool
     * @throws ValidationException
     * @throws Exception
     * @date 2022/3/3
     */
    public function withdraw($id, $reason, $user)
    {
        //查询数据
        $item = Payment::findFirst([
            'conditions' => 'id = :id:',
            'bind' => ['id' => $id],
        ]);
        if (empty($item)) {
            throw new ValidationException('not found item', ErrCode::$VALIDATE_ERROR);
        }
        $data = $item->toArray();
        //验证状态
        if (!in_array($data['pay_status'], [PayEnums::PAYMENT_MODULE_PAY_STATUS_PENDING, PayEnums::PAYMENT_MODULE_PAY_STATUS_FAILED])) {
            throw new ValidationException(static::$t->_('pay_status_can_not_withdraw'), ErrCode::$VALIDATE_ERROR);
        }
        $db = $this->getDI()->get('db_oa');
        try {
            $db->begin();
            //更新状态为撤回
            $payment_model = new Payment();
            $updata_data = [
                'pay_status' => PayEnums::PAYMENT_MODULE_PAY_STATUS_NOTPAY,
                'cancel_reason' => $reason,
                'cancel_id' => $user['id'],
                'cancel_at' => gmdate('Y-m-d H:i:s'),
                'updated_at' => gmdate('Y-m-d H:i:s'),
            ];
            $update_success = $db->updateAsDict(
                $payment_model->getSource(),
                $updata_data,
                [
                    'conditions' => 'id = ? and pay_status = ?',
                    'bind' => [$id, $data['pay_status']],
                ]
            );
            if (!$update_success) {
                throw new BusinessException('payment_withdraw_failed', ErrCode::$PAYMENT_WITHDRAW_ERROR);
            }
            /**
             * 待支付状态下,需要撤回审批流,让后续审批人不能再操作
             * 支付失败状态下,审批人已经没有操作了,无需撤回审批流(而且审批流只支持待审批的撤回)
             * */
            if ($data['pay_status'] == PayEnums::PAYMENT_MODULE_PAY_STATUS_PENDING) {
                //审批流撤回
                $pay_flow_service = new PayFlowService();
                $pay_flow_service->cancel($data['id'], $reason, $user);
            }
            //更新后的数据,merge后边的元素覆盖前边的
            $new_payment = array_merge($data, $updata_data, ['send_crowd' => true]);
            //业务单更新, 更新为未支付(终态,不再进行支付)
            FinalPayService::getInstance()->businessPay($new_payment, $user, $data['pay_status']);
            $db->commit();
        } catch (Exception $e) {
            $db->rollback();
            throw $e;
        }
        return true;
    }

    /**
     * 批量审批通过
     *
     * @param array $data 审批数据组
     * @param array $user 审批人信息组
     * @param integer $level 审批级别
     * @return bool
     * @throws Exception
     * @date 2022/3/8
     */
    public function batchAudit($data, $user, $level)
    {
        ini_set('memory_limit', '1024M');
        //开启事务
        $db = $this->getDI()->get('db_oa');
        try {
            $db->begin();

            //16325需求需要区分是否对接了支票模块 、21773增加只有支票走原先逻辑，其他都做单个支付逻辑
            if ($this->checkChequeIsOpen() && $data['pay_method'] == Enums::PAYMENT_METHOD_CHECK) {
                //对接支票模块，走以下逻辑，只是加了开关代码逻辑无任何改动；
                if (empty($level) && $data['is_pay'] == PayEnums::IS_PAY_YES && $data['pay_method'] != Enums::PAYMENT_METHOD_CHECK) {
                    //合并支付 必须是支票的
                    throw new ValidationException(static::$t->_('batch_payment_merge_mast_cheque'), ErrCode::$VALIDATE_ERROR);
                }
                if ($data['pay_method'] == Enums::PAYMENT_METHOD_CHECK) {
                    $batch_data = $this->getPaymentArr($data);
                }
                //审批
                if (isset($data['pay_check'])) {
                    $amount_total_actually = bc_add_batch(array_column($batch_data['data_list'], 'amount'), 2);
                    $sum_amount = bc_add_batch(array_column($data['pay_check'], 'amount'), 2);
                    if (!empty(bccomp($sum_amount, $amount_total_actually))) {
                        throw new ValidationException(static::$t->_('cheque_account_pay_amount_not_actual_amount'), ErrCode::$VALIDATE_ERROR);
                    }
                    $pay_check_arr_list = $data['pay_check'];
                    $pay_check_arr = [];
                    //循环传入的支票号数据，支票号数据可以重复，处理成一个支票号对应多条数据
                    foreach ($pay_check_arr_list as $check) {
                        //先把支票号放入一个盒子
                        $pay_check_arr[$check['ticket_no']][] = $check;
                        if (!empty($pay_check_arr[$check['ticket_no']])) {

                            //判断同一个支票号签票必须相同
                            if (count(array_unique(array_column($pay_check_arr[$check['ticket_no']], 'check_date'))) > PayEnums::BATCH_PAYMENT_NOT_MAX_ONE) {
                                throw new ValidationException(static::$t->_('ticket_no_check_date_inequality'), ErrCode::$VALIDATE_ERROR);
                            }
                            //判断同一个支票号计划承兑日期必须相同
                            if (count(array_unique(array_column($pay_check_arr[$check['ticket_no']], 'date'))) > PayEnums::BATCH_PAYMENT_NOT_MAX_ONE) {
                                throw new ValidationException(static::$t->_('ticket_no_date_inequality'), ErrCode::$VALIDATE_ERROR);
                            }
                            //判断同一个支票号收款人必须相同
                            if (count(array_unique(array_column($pay_check_arr[$check['ticket_no']], 'payee_name'))) > PayEnums::BATCH_PAYMENT_NOT_MAX_ONE) {
                                throw new ValidationException(static::$t->_('ticket_no_payee_name_inequality'), ErrCode::$VALIDATE_ERROR);
                            }

                            //判断同一个支票号币种必须相同
                            if (count(array_unique(array_column($pay_check_arr[$check['ticket_no']], 'currency'))) > PayEnums::BATCH_PAYMENT_NOT_MAX_ONE) {
                                throw new ValidationException(static::$t->_('ticket_no_currency_inequality'), ErrCode::$VALIDATE_ERROR);
                            }
                        }
                    }
                    //传入的支票号到台账获取数据 用于循环的时候比对状态
                    $ticket_no_arr = array_values(array_keys($pay_check_arr));
                    $cheque_account_list = ChequeAccountModel::find(
                        [
                            'cheque_code in ({cheque_code:array})',
                            'bind' => [
                                'cheque_code' => $ticket_no_arr,
                            ],
                            'columns' => 'use_status,currency,valid_days,cheque_code',
                        ])->toArray();

                    if (empty($cheque_account_list)) {
                        throw new ValidationException(static::$t->_('cheque_not_empty'), ErrCode::$VALIDATE_ERROR);
                    }
                    $cheque_account_list = array_column($cheque_account_list, null, 'cheque_code');
                    //开始循环 ，如果提交的支票号是[1,1,2,3,3],就会变成[1=>[[1],[1]],2=>[[2]],[3=>[[3],[3]]]] 这样计算合计金额方便
                    foreach ($pay_check_arr as $key => $check_code) {
                        ChequeService::getInstance()->saveChequeAccount($key, $check_code, $cheque_account_list, $db);
                    }
                }
                $audit_data = $data;
                unset($audit_data['id_batch']);
                foreach ($data['id_batch'] as $id) {
                    $audit_data['id'] = $id;
                    $audit_result = $this->batchAuditAll($audit_data, $user);
                    if (!isset($audit_result['code']) || $audit_result['code'] != ErrCode::$SUCCESS) {
                        if ($audit_result['code'] == ErrCode::$VALIDATE_ERROR) {
                            throw new ValidationException($audit_result['message'], $audit_result['code']);
                        } else {
                            throw new BusinessException('audit failed, payment_id=' . $id);
                        }
                    }
                }
            } else {
                //未对接支票模块，走以下逻辑【还原之前的代码逻辑】
                if (!is_array($data['id_batch'])) {
                    throw new ValidationException('param:id_batch error', ErrCode::$VALIDATE_ERROR);
                }
                $ids = $data['id_batch'];
                $payment_data = Payment::find([
                    'conditions' => 'id in ({ids:array})',
                    'bind' => ['ids' => $ids],
                ])->toArray();
                if (empty($payment_data)) {
                    throw new ValidationException('payment data is empty', ErrCode::$VALIDATE_ERROR);
                }
                //检测不能是支票
                $pay_method = array_unique(array_column($payment_data, 'pay_method'));
                if (in_array(Enums::PAYMENT_METHOD_CHECK, $pay_method)) {
                    throw new ValidationException(static::$t->_('pay_method_can_not_be_check'), ErrCode::$VALIDATE_ERROR);
                }
                //支付方式必须一致；不可为空
                if (empty($pay_method) || count($pay_method) > 1) {
                    throw new ValidationException(static::$t->_('pay_method_inconsistent'), ErrCode::$VALIDATE_ERROR);
                }
                //单据上的费用所属公司不同，不允许一起批量提交。
                $cost_company_id = array_unique(array_column($payment_data, 'cost_company_id'));
                if (empty($cost_company_id) || count($cost_company_id) > 1) {
                    throw new ValidationException(static::$t->_('pay_cost_company_id_inconsistent'), ErrCode::$VALIDATE_ERROR);
                }

                //代理支付不允许与其他模块合并支付！
                $unique_oa_type     = array_unique(array_column($payment_data, 'oa_type'));
                if (count($unique_oa_type) > 1 && in_array(BankFlowEnums::BANK_FLOW_OA_TYPE_AGENCY_PAYMENT, $unique_oa_type)) {
                    throw new ValidationException(static::$t->_('proxy_payment_is_not_allowed_to_be_combined_with_other_modules'),
                        ErrCode::$VALIDATE_ERROR);
                }

                //审批
                $audit_data = $data;
                unset($audit_data['id_batch']);
                //标记批量支付
                $this->batchAudit = true;
                foreach ($data['id_batch'] as $id) {
                    $audit_data['id'] = $id;
                    $audit_result = $this->audit($audit_data, $user, $level);
                    if (!isset($audit_result['code']) || $audit_result['code'] != ErrCode::$SUCCESS) {
                        if ($audit_result['code'] == ErrCode::$VALIDATE_ERROR) {
                            throw new ValidationException($audit_result['message'], $audit_result['code']);
                        } else {
                            throw new Exception($audit_result['message'], $audit_result['code']);
                        }
                    }
                }
            }

            $db->commit();
        } catch (Exception $e) {
            $db->rollback();
            throw $e;
        }
        return true;
    }

    /**
     * 检测一批单号审批级别是否一致, 返回级别
     *
     * @param $business_ids
     * @param $user_id
     * @return mixed
     * @throws BusinessException
     * @throws ValidationException
     * @date 2022/3/18
     */
    public function checkLevel($business_ids, $user_id)
    {
        //检测申请单支付审批节点不一致
        $business_level = (new PayFlowService())->getAuditLevelById($business_ids, $user_id);
        $first_level = reset($business_level);
        foreach ($business_level as $level) {
            if ($level != $first_level) {
                throw new ValidationException(static::$t->_('pay_level_not_equal'), ErrCode::$VALIDATE_ERROR);
            }
        }
        return $first_level;
    }

    /**
     * 批量审批驳回-驳回到第一级审批
     * @param $data
     * @param $user
     * @return bool
     * @throws Exception
     * @date 2022/3/8
     */
    public function batchReject($data, $user)
    {
        //验证
        if (!is_array($data['id_batch'])) {
            throw new ValidationException('param:id_batch error', ErrCode::$VALIDATE_ERROR);
        }
        $ids = $data['id_batch'];
        $payment_data = Payment::find([
            'conditions' => 'id in ({ids:array})',
            'bind' => ['ids' => $ids],
        ])->toArray();
        if (empty($payment_data)) {
            throw new ValidationException('payment data is empty', ErrCode::$VALIDATE_ERROR);
        }
        //检测申请单支付审批节点不一致,且只能是第二级
        $business_ids = array_column($payment_data, 'id');
        $business_level = (new PayFlowService())->getAuditLevelById($business_ids, $user['id']);
        $first_level = reset($business_level);
        if ($first_level != PayEnums::PAYMENT_TWO_LEVEL_AUDIT) {
            throw new ValidationException(static::$t->_('pay_level_not_second'), ErrCode::$VALIDATE_ERROR);
        }
        foreach ($business_level as $level) {
            if ($level != $first_level) {
                throw new ValidationException(static::$t->_('pay_level_not_equal'), ErrCode::$VALIDATE_ERROR);
            }
        }

        foreach ($payment_data as $payment_v) {
            if ($payment_v['pay_status'] != PayEnums::PAYMENT_MODULE_PAY_STATUS_ING) {
                throw new ValidationException('pay_status_not_ing', ErrCode::$VALIDATE_ERROR);
            }
        }
        //开启事务
        $db = $this->getDI()->get('db_oa');
        try {
            $db->begin();
            //审批
            $audit_data = $data;
            unset($audit_data['id_batch']);
            foreach ($data['id_batch'] as $id) {
                $audit_data['id'] = $id;
                $audit_result = $this->reject($audit_data, $user, false);
                if (!isset($audit_result['code']) || $audit_result['code'] != ErrCode::$SUCCESS) {
                    throw new BusinessException('audit failed, payment_id=' . $id, ErrCode::$BUSINESS_ERROR);
                }
            }
            $db->commit();
        } catch (Exception $e) {
            $db->rollback();
            throw $e;
        }
        return true;
    }

    /**
     * 获取银行信息
     *
     * @param $data
     * @return array
     * @throws BusinessException
     * @throws ValidationException
     * @date 2022/3/9
     */
    public function getPay($data)
    {
        $item = Payment::findFirst(
            [
                'conditions' => 'id = :id:',
                'bind' => ['id' => $data['id']],
            ]
        );
        if (empty($item)) {
            throw new ValidationException('not found item', ErrCode::$VALIDATE_ERROR);
        }
        $itemArr = $item->toArray();
        $model = BankFlowPayFlowService::getInstance()->getModelByTypeId($itemArr['oa_type']);
        $pay_id = $data['pay_id'] ?? '';
        return $model->getBankInfo($itemArr['no'], $pay_id);
    }

    /**
     * 更新银行信息
     *
     * @param $data
     * @param $user
     * @return array
     * @date 2022/3/9
     */
    public function updatePayData($data, $user)
    {
        $code = ErrCode::$SUCCESS;
        $message = 'success';

        //开启事务
        $db = $this->getDI()->get('db_oa');
        $db->begin();

        try {
            $item = Payment::findFirst([
                'conditions' => 'id = :id:',
                'bind' => ['id' => $data['payment_id']],
            ]);
            if (empty($item)) {
                throw new ValidationException(static::$t->_('pay_update_payinfo_error_003'), ErrCode::$VALIDATE_ERROR);
            }

            //验证状态, 只有待支付和支付失败才能更新
            if (!in_array($item->pay_status, [PayEnums::PAYMENT_MODULE_PAY_STATUS_PENDING, PayEnums::PAYMENT_MODULE_PAY_STATUS_FAILED])) {
                throw new ValidationException(static::$t->_('pay_update_payinfo_error_002'), ErrCode::$VALIDATE_ERROR);
            }

            // 付款单明细行更新
            foreach ($data['pay'] as $pay) {
                $payment_pay_item = PaymentPay::findFirst([
                    'conditions' => 'id = :id:',
                    'bind' => ['id' => $pay['id']],
                ]);
                if (empty($payment_pay_item) || $payment_pay_item->payment_id != $data['payment_id']) {
                    throw new ValidationException(static::$t->_('pay_update_payinfo_error_001'), ErrCode::$VALIDATE_ERROR);
                }

                $payment_pay_item->bank_name = $pay['bank_name'];
                $payment_pay_item->bank_account = $pay['bank_account'];
                $payment_pay_item->bank_account_name = $pay['bank_account_name'];
                $payment_pay_item->bank_address = $pay['bank_address'];
                $payment_pay_item->swift_code = $pay['swift_code'];
                if ($payment_pay_item->save() === false) {
                    throw new BusinessException('支付模块更新付款人信息失败, 原因可能是=' . get_data_object_error_msg($payment_pay_item), ErrCode::$PAYMENT_UPDATE_PAY_ERROR);
                }
            }

            // 支付失败状态下, 1.需要修改支付状态为待支付 2.重新发起审批流
            if ($item->pay_status == PayEnums::PAYMENT_MODULE_PAY_STATUS_FAILED) {
                $item_update = [
                    'pay_status' => PayEnums::PAYMENT_MODULE_PAY_STATUS_PENDING,
                ];
                if ($item->i_update($item_update) === false) {
                    throw new BusinessException('支付模块更新支付状态失败, 原因可能是=' . get_data_object_error_msg($item), ErrCode::$PAYMENT_UPDATE_PAY_STATUS_ERROR);
                }

                $recommit_result = (new PayFlowService())->recommit($item, $user);
                if (!$recommit_result) {
                    throw new BusinessException('重新发起审批流失败', ErrCode::$PAYMENT_UPDATE_PAY_STATUS_ERROR);
                }
            }

            $this->logger->info("sync_update_pyeeinfo_payment_no={$item->no}");

            // 同步 回更 业务模块对应的收款人银行信息校验
            $this->syncPayeeInfoValidate($item->oa_type, $data);

            // 同步业务模块
            $data['payment_no'] = $item->no;
            $biz_model = BankFlowPayFlowService::getInstance()->getModelByTypeId($item->oa_type);
            $biz_model->syncUpdatePayeeInfo($data, $user);

            $db->commit();

        } catch (ValidationException $e) {
            $db->rollback();
            $code = $e->getCode();
            $message = $e->getMessage();

        } catch (BusinessException $e) {
            $db->rollback();
            $code = $e->getCode();
            $message = static::$t->_('retry_later');
            $this->logger->warning('支付模块-我的申请-更新支付信息异常, ' . $e->getMessage());

        } catch (Exception $e) {
            $db->rollback();
            $code = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $this->logger->error('支付模块-我的申请-更新支付信息异常, ' . $e->getMessage());
        }

        return [
            'code' => $code,
            'message' => $message,
        ];
    }

    /**
     * 更新支付信息 -> 回更业务模块收款人信息的相关校验
     *
     * @param int $oa_type
     * @param array $params
     * @return bool
     * @throws ValidationException
     */
    protected function syncPayeeInfoValidate(int $oa_type, array $params)
    {
        // 租房付款针对行信息同步的校验
        // 校验相同合同号的收款账号/收款银行/收款户名是否相同，如果不同, 则拦截提示
        if ($oa_type == BankFlowEnums::BANK_FLOW_OA_TYPE_PAYMENT_STORE_RENTING) {
            // 有合同行的支付信息 合同号 => 支付信息串
            $contract_payee_item = [];
            foreach ($params['pay'] as $index => $pay) {
                if (!empty($pay['contract_no'])) {
                    $contract_payee_item[$pay['contract_no']]['index'][] = $index + 1;
                    $payee_encode_id = md5("{$pay['bank_account']}\t{$pay['bank_account']}\t{$pay['bank_account']}");
                    $contract_payee_item[$pay['contract_no']]['payee_list'][] = $payee_encode_id;
                }
            }

            $error_hint = '';
            foreach ($contract_payee_item as $contract_no => $item) {
                // 收款人信息超过1个, 说明同一个合同关联的收款人信息不一致
                $payee_num = count(array_filter(array_unique($item['payee_list'])));
                if ($payee_num > 1) {
                    $error_hint .= $contract_no . ' ' . implode(',', $item['index']) . '; ';
                }
            }

            if (!empty($error_hint)) {
                $error_hint = trim($error_hint, '; ');
                throw new ValidationException(static::$t->_('pay_update_payinfo_error_004', ['error_hint' => $error_hint]), ErrCode::$VALIDATE_ERROR);
            }
        }

        return true;
    }

    /**
     * 获取支付模块枚举
     *
     * @return array
     * @date 2022/3/12
     * @return array
     */
    public function getEnums()
    {
        $tool = new ContractService();
        //境内境外支付不要"空"
        $pay_where_enums = PayEnums::$pay_where_id_to_lang_key;
        unset($pay_where_enums[0]);
        //只要报销/普通付款/租房付款/借款/备用金/采购/薪酬扣款/薪资发放
        $oa_type_enums = Enums\BankFlowEnums::$oa_type_id_to_lang_key;
        $enums_item = [
            'pay_status' => $tool->getAmityFormat(PayEnums::$payment_module_pay_status),
            'pay_where' => $tool->getAmityFormat($pay_where_enums),
            'payment_method' => $tool->getAmityFormat(Enums::$payment_method),
            'currency' => $tool->getAmityFormat(GlobalEnums::$currency_item),
            'oa_type' => $tool->getAmityFormat($oa_type_enums),
            'out_send_status' => $tool->getAmityFormat(PayEnums::$out_send_status),
            'flash_pay_bank' => $tool->getAmityFormat($this->getFlashPayBank()),//收款人的银行和FLASH PAY的银行对照码
            'amount_total_actually_is_zero' => $tool->getAmityFormat(PayEnums::$payment_is_pay_key),//实付金额等于0,0否1是
            'agency_not_pay_reason_category' =>  $tool->getAmityFormat($this->getAgencyNotPayReasonCategory()),//代理支付未支付原因分类
        ];
        $enums_item['cost_company'] = DepartmentService::getInstance()->getCooCostCompany();
        if (get_country_code() == 'MY') {
            $enums_item['bank_payment_type_map'] = $tool->getAmityFormat($this->getBankPaymentTypeMap(),false);
        }
        return $enums_item;
    }

    /**
     * 银行支付方式枚举
     * @return array|mixed
     */
    public function getBankPaymentTypeMap()
    {
        $bank_payment_type_map =   SettingEnvModel::getValByCode('bank_payment_type_map');
        if ($bank_payment_type_map) {
            return json_decode($bank_payment_type_map,true);
        }
        return [];
    }
    /**
     * 业务单查询支付数据
     *
     * @param $oa_type
     * @param $no
     * @return array
     * @date 2022/3/12
     */
    public function getPaymentByBusinessNo($oa_type, $no)
    {
        $payment_data = Payment::findFirst([
            'conditions' => 'oa_type = :oa_type: and no = :no:',
            'bind' => ['oa_type' => $oa_type, 'no' => $no],
        ]);
        if (!$payment_data) {
            return [];
        }
        return $payment_data ? $payment_data->toArray() : [];
    }

    /**
     * @Desc:【14829】支付数据自动超时结束任务
     * @return bool
     * @throws ValidationException
     * @author: W_uniQue
     * @Time: 2022/10/21 17:26
     */
    public function autoOvertimeWithdraw()
    {
        //获取未支付的数据
        $pay_list = Payment::find([
            'conditions' => 'pay_status = :pay_status:',
            'bind' => ['pay_status' => PayEnums::PAYMENT_MODULE_PAY_STATUS_FAILED],
        ])->toArray();
        if (!$pay_list) {
            throw new ValidationException('没有未支付的数据', ErrCode::$VALIDATE_ERROR);
        }
        $staff_ids = array_values(array_unique(array_column($pay_list, 'apply_staff_id')));
        //获取员工数据
        $staff_lists = (new UserService())->getUserListByStaffIds($staff_ids);
        $user = [];
        foreach ($staff_lists as $staff) {
            //字段特殊处理，满足撤回接口用户参数要求
            $user[$staff->staff_info_id] = [
                'id' => $staff->staff_info_id,
                'name' => $staff->name,
                'nick_name' => $staff->nick_name ?? '',
                'email' => $staff->email ?? '',
                'mobile' => $staff->mobile ?? '',
                'job_title' => $staff->getJobTitle()->job_name ?? '',
                'department' => $staff->getDepartment()->name ?? '',
                'state' => $staff->state,
            ];
        }
        foreach ($pay_list as $pay) {
            try {
                if (!isset($user[$pay['apply_staff_id']])) {
                    throw new Exception("用户数据查询为空【{$pay['apply_staff_id']}】", ErrCode::$SYSTEM_ERROR);
                }
                //离职人员，或者支付失败操作时间大于30天
                if ($user[$pay['apply_staff_id']]['state'] == Enums\StaffInfoEnums::STAFF_STATE_LEAVE || strtotime('+30 days',
                        strtotime($pay['updated_at'])) < time()) {
                    //撤回操作
                    $res = $this->withdraw($pay['id'], 'auto-operations', $user[$pay['apply_staff_id']]);
                    if (!$res) {
                        throw new Exception('withdraw return false');
                    }
                    echo "【{$pay['id']}】支付数据撤销成功。" . PHP_EOL;
                }
            } catch (Exception $e) {
                echo "【{$pay['id']}】，【{$pay['apply_staff_id']}】，【{$pay['updated_at']}】支付数据撤销失败。" . PHP_EOL;
                $this->logger->error("auto-overtime-withdraw-failed:【{$pay['id']}】" . $e->getMessage());
            }
        }
        return true;
    }


    /**
     * 校验批量提交 等级
     *
     * @Date: 1/5/23 7:16 PM
     * @param array $params
     * @param array $user
     * @return  array
     * @author: peak pan
     **/
    public function checkBatchPayment(array $params, array $user)
    {
        $code = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $payment_arr = [];
        $params['id_batch'] = array_values($params['id_batch']);
        try {

            $level = PayService::getInstance()->checkLevel($params['id_batch'], $user['id']);
            if ($level == PayEnums::PAYMENT_ONE_LEVEL_AUDIT) {
                $payment_arr = $this->getPaymentArr($params, $level);
            } else if ($level == PayEnums::PAYMENT_TWO_LEVEL_AUDIT) {
                //第二支付人勾选批量支付的时候
                $payment_arr = $this->getPaymentSecondaryArr($params, $level);
            }
            if ($params['is_pay_bank'] == PayEnums::BIZ_DATA_IS_PAY_MODULE_YES) {
                //19606需求增加银行转账合并支付方式,所以需要区分支付方式
                Validation::validate($params, ['pay_method' => 'Required|IntIn:' . GlobalEnums::PAYMENT_METHOD_BANK_TRANSFER . ',' . GlobalEnums::PAYMENT_METHOD_CHECK]);
                if ($params['pay_method'] == GlobalEnums::PAYMENT_METHOD_BANK_TRANSFER) {
                    $this->validateBankBatchPayment($params);
                } else {
                    //第三级支付人勾选批量支付的时候
                    $level = PayEnums::PAYMENT_THREE_LEVEL_AUDIT;
                    $payment_arr = $this->getPaymentSecondaryArr($params, $level);
                }
            }

        } catch (ValidationException $e) {
            $code = $e->getCode();
            $message = $e->getMessage();
        } catch (Exception $e) {
            $code = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        }
        if (!empty($real_message)) {
            $this->logger->warning('校验批量提交 ,获取支付数据 校验批量提交数据统一异常:' . $real_message);
        }

        return [
            'code' => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data' => $payment_arr,
        ];
    }


    /**
     * 获取payment数据 校验批量提交数据统一
     *
     * @Token
     * @Date: 1/5/23 7:16 PM
     * @param array $params
     * @param int level   一级审核 二级审核   三级审核
     * @return  array
     * @throws ValidationException
     * @author: peak pan
     **/
    public function getPaymentArr(array $params)
    {
        $builder = $this->modelsManager->createBuilder();
        $builder->columns('pp.bank_name, pp.bank_account, pp.bank_account_name, p.currency, p.cost_company_id, pp.amount, pp.id, p.oa_type, p.no, p.id as payment_id');
        $builder->from(['pp' => paymentPay::class]);
        $builder->leftjoin(Payment::class, 'p.id = pp.payment_id', 'p');
        $builder->inwhere('p.id', $params['id_batch']);
        $payment_arr = $builder->getQuery()->execute()->toArray();


        if (empty($payment_arr)) {
            throw new ValidationException(static::$t->_('data_empty_or_read_data_failed'), ErrCode::$VALIDATE_ERROR);
        }
        $bank_name = array_unique(array_column($payment_arr, 'bank_name'));
        $bank_account = array_unique(array_column($payment_arr, 'bank_account'));
        $bank_account_name = array_unique(array_column($payment_arr, 'bank_account_name'));


        if (empty($bank_name[0]) || empty($bank_account[0]) || empty($bank_account_name[0])) {
            //批量支付支票，收款人必须一致，且不能为空
            throw new ValidationException(static::$t->_('cheque_batch_payment_payee_not_agreement_not_null'), ErrCode::$VALIDATE_ERROR);
        }

        if (count($bank_name) > PayEnums::BATCH_PAYMENT_NOT_MAX_ONE || count($bank_account) > PayEnums::BATCH_PAYMENT_NOT_MAX_ONE || count($bank_account_name) > PayEnums::BATCH_PAYMENT_NOT_MAX_ONE) {
            //批量支付支票，收款人必须一致。
            throw new ValidationException(static::$t->_('cheque_batch_payment_payee_not_agreement'), ErrCode::$VALIDATE_ERROR);
        }

        $currency = array_unique(array_column($payment_arr, 'currency'));
        if (count($currency) > PayEnums::BATCH_PAYMENT_NOT_MAX_ONE) {
            //不同币种的支票付款不可批量支付。
            throw new ValidationException(static::$t->_('cheque_batch_not_currency_payment'), ErrCode::$VALIDATE_ERROR);
        }

        $cost_company_id = array_unique(array_column($payment_arr, 'cost_company_id'));
        if (count($cost_company_id) > PayEnums::BATCH_PAYMENT_NOT_MAX_ONE) {
            //费用所属公司不同，不允许批量支付。
            throw new ValidationException(static::$t->_('cheque_batch_cost_company_payment'), ErrCode::$VALIDATE_ERROR);
        }
        $data['actual_amount_total'] = bc_add_batch(array_column($payment_arr, 'amount'), 2);
        $data['currency'] = static::$t->_(GlobalEnums::$currency_item[$currency[0]]);
        $data['currency_id'] = $currency[0];
        $data['payee_account'] = $bank_account[0];//收款人账号
        $data['payee_name'] = $bank_account_name[0];  //收款人姓名
        $data['data_list'] = $payment_arr;  //查询结果数据
        return $data;

    }


    /**
     * 获取payment数据 校验批量支付二级
     *
     * @Token
     * @Date: 1/5/23 7:16 PM
     * @param array $params
     * @param int $level 一级审核   2级审核  3级审核
     * @return  array
     * @throws ValidationException
     * @author: peak pan
     **/
    public function getPaymentSecondaryArr(array $params, int $level)
    {
        $builder = $this->modelsManager->createBuilder();
        $builder->columns('pp.bank_name, pp.bank_account, pp.bank_account_name, p.currency as p_currency, p.cost_company_id, pp.amount, pp.id, p.oa_type, p.no,p.id as payment_id, p.pay_method, pc.ticket_no, pc.bank_name, pc.amount, pc.currency, pc.currency_text, pc.check_date, pc.date, pc.payee_name, pc.remark,p.pay_remark');
        $builder->from(['pp' => paymentPay::class]);
        $builder->leftjoin(Payment::class, 'p.id = pp.payment_id', 'p');
        $builder->leftjoin(PaymentCheck::class, 'pc.payment_pay_id = pp.id', 'pc');
        $builder->andwhere('pc.is_deleted = :is_deleted:', ['is_deleted' => GlobalEnums::IS_NO_DELETED]);
        $builder->inwhere('p.id', $params['id_batch']);
        $payment_arr = $builder->getQuery()->execute()->toArray();

        if (empty($payment_arr)) {
            throw new ValidationException(static::$t->_('data_empty_or_read_data_failed'), ErrCode::$VALIDATE_ERROR);
        }

        $pay_method = array_unique(array_column($payment_arr, 'pay_method'));

        if (count($pay_method) > PayEnums::BATCH_PAYMENT_NOT_MAX_ONE || $pay_method[0] != Enums::PAY_METHOD_PRELOAD) {
            //若勾选的单据的支付方式都是支票判断
            throw new ValidationException(static::$t->_('cheque_account_batch_payment_pay_method'), ErrCode::$VALIDATE_ERROR);
        }

        $ticket_no = array_values(array_unique(array_filter(array_column($payment_arr, 'ticket_no'))));
        $ticket_no_arr = PaymentCheck::find([
            'conditions' => 'ticket_no =:ticket_no: and is_deleted = :is_deleted:',
            'bind' => [
                'ticket_no' => $ticket_no[0],
                'is_deleted' => GlobalEnums::IS_NO_DELETED,
            ],
            'group' => 'payment_id',
        ])->toArray();

        if (count($params['id_batch']) > PayEnums::BATCH_PAYMENT_NOT_MAX_ONE || empty($params['is_pay_bank'])) {
            //批量校验
            if (count($ticket_no) > PayEnums::BATCH_PAYMENT_NOT_MAX_ONE) {
                if ($level == PayEnums::PAYMENT_THREE_LEVEL_AUDIT) {
                    //支票号不同，不可合并支付
                    throw new ValidationException(static::$t->_('cheque_account_batch_payment_merge'), ErrCode::$VALIDATE_ERROR);
                } else {
                    //每次只能勾选一个支票号对应所有的关联订单
                    throw new ValidationException(static::$t->_('cheque_account_batch_payment_max_min_one'), ErrCode::$VALIDATE_ERROR);
                }
            }
        }
        if (count($params['id_batch']) != count($ticket_no_arr)) {
            //支票号%ticket_no%关联多个单据，请一起勾选再批量支付  支票号%ticket_no%关联多个单据，请一起勾选再批量驳回
            $title_key = 'cheque_account_select_batch_payment';
            if ($params['type'] == PayEnums::PAYMENT_TWO_LEVEL_AUDIT) {
                $title_key = 'cheque_account_select_batch_reject';
            }
            //支票号%ticket_no%关联多个单据，请一起勾选再合并支付
            if ($level == PayEnums::PAYMENT_THREE_LEVEL_AUDIT) {
                $title_key = 'cheque_account_select_batch_bill_all_payment';
            }
            throw new ValidationException(static::$t->_($title_key, ['ticket_no' => $ticket_no[0]]), ErrCode::$VALIDATE_ERROR);
        }
        $data['actual_amount_total'] = bc_add_batch(array_column($payment_arr, 'amount'), 2);
        $data['currency'] = isset($payment_arr[0]['p_currency']) ? static::$t->_(GlobalEnums::$currency_item[$payment_arr[0]['p_currency']]) : '';
        $data['currency_id'] = $payment_arr[0]['p_currency'] ?? '';
        $data['pay_remark'] = $payment_arr[0]['pay_remark'] ?? '';
        $item = Payment::findFirst(
            [
                'conditions' => 'id = :id:',
                'bind' => ['id' => $payment_arr[0]['payment_id']],
            ]
        );
        $attachments = $item->getFiles();
        $data['attachments'] = $attachments ? $attachments->toArray() : [];
        $data['data_list'] = $payment_arr;  //查询结果数据
        return $data;
    }

    /**
     * 单个编辑和驳回校验
     *
     * @Token
     * @Date: 1/5/23 7:16 PM
     * @param array $params 条件
     * @throws ValidationException
     * @author: peak pan
     **/
    public function getEditPaymentCheck(array $params)
    {
        $ticket_no = $params['pay_check'][0]['ticket_no'] ?? '';
        if (empty($params['pay_check'])) {
            $payment_check_obj = PaymentCheck::findFirst(
                [
                    'conditions' => 'payment_id = :payment_id: and is_deleted = :is_deleted:',
                    'bind' => ['payment_id' => $params['id'], 'is_deleted' => GlobalEnums::IS_NO_DELETED],
                ]
            );
            if (!empty($payment_check_obj)) {
                $ticket_no = $payment_check_obj->ticket_no;
            }
        }

        if (empty($ticket_no)) {
            throw new ValidationException(static::$t->_('data_empty_or_read_data_failed'), ErrCode::$VALIDATE_ERROR);
        }

        $builder = $this->modelsManager->createBuilder();
        $builder->columns('cabr.id,cabr.oa_biz_no, pc.ticket_no');
        $builder->from(['cabr' => ChequeAccountBusinessRelModel::class]);
        $builder->leftjoin(PaymentCheck::class, 'pc.id = cabr.payment_check_id', 'pc');
        $builder->where('pc.ticket_no = :ticket_no: and pc.is_deleted = :is_deleted:', ['ticket_no' => $ticket_no, 'is_deleted' => GlobalEnums::IS_NO_DELETED]);
        $builder->groupBy('cabr.oa_biz_no');
        $cheque_account_business_arr = $builder->getQuery()->execute()->toArray();

        if (empty($cheque_account_business_arr)) {
            throw new ValidationException(static::$t->_('data_empty_or_read_data_failed'), ErrCode::$VALIDATE_ERROR);
        }

        $oa_biz_no = array_unique(array_unique(array_column($cheque_account_business_arr, 'oa_biz_no')));
        if (count($oa_biz_no) > PayEnums::BATCH_PAYMENT_NOT_MAX_ONE) {
            throw new ValidationException(static::$t->_('cheque_account_select_batch_bill_payment', ['ticket_no' => $ticket_no]), ErrCode::$VALIDATE_ERROR);
        }

    }


    /**
     * 一级批量支付
     *
     * @param array $params 条件
     * @param array $user 用户数据
     * @return array
     * @date 2022/3/18
     */
    public function batchAuditAll(array $params, array $user)
    {
        $code = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $data = [];
        try {
            $item = Payment::findFirst(
                [
                    'conditions' => 'id = :id:',
                    'bind' => ['id' => $params['id']],
                ]
            );
            if (empty($item)) {
                throw new ValidationException('not found the item');
            }

            $tmp = [];
            $pay = new PayFlowService();
            $itemArr = [];
            $item->updated_at = gmdate('Y-m-d H:i:s');    //更新时间，保持created_at，updated_at，payer_date 0时区存储格式
            //支付=是的时候调用通过
            if ($params['is_pay'] == PayEnums::IS_PAY_YES) {
                //附件
                $tmp['attachments'] = $params['attachments'] ?? [];
                //支付备注
                $tmp['pay_remark'] = $params['pay_remark'] ?? '';

                if ($params['pay_method'] == Enums::PAYMENT_METHOD_CASH) {
                    if (isset($params['pay_date']) && !empty($params['pay_date'])) {
                        $tmp['pay_date'] = $params['pay_date'];
                    }
                }
                if ($params['pay_method'] == Enums::PAYMENT_METHOD_BANK_TRANSFER) {
                    if (isset($params['pay_bank_name']) && !empty($params['pay_bank_name'])) {
                        $tmp['pay_bank_name'] = $params['pay_bank_name'];
                    }
                    if (isset($params['pay_bank_account']) && !empty($params['pay_bank_account'])) {
                        $tmp['pay_bank_account'] = $params['pay_bank_account'];
                    }
                    if (isset($params['pay_bank_flow_date']) && !empty($params['pay_bank_flow_date'])) {
                        $tmp['pay_bank_flow_date'] = $params['pay_bank_flow_date'];
                    }
                }

                if (isset($params['pay_check']) && !empty($params['pay_check'])) {
                    $tmp['pay_check'] = $params['pay_check'];
                }

                $tmp['is_pay'] = $params['is_pay'];
                $tmp['is_batch'] = true; //true 批量提交 false 单个提交
                $res = $pay->approve($params['id'], $params['note'] ?? '', $user, $tmp);
                if (!empty($res->approved_at)) {
                    //通过后，有可能会更改字段
                    $item = Payment::findFirst([
                        'conditions' => 'id = :id:',
                        'bind' => ['id' => $params['id']],
                    ]);

                    //$item->is_pay = 1;
                    $item->pay_status = PayEnums::PAYMENT_MODULE_PAY_STATUS_BANKING;
                    if (!$item->save()) {
                        throw new BusinessException('一级审核人修改数据失败', ErrCode::$PAYMENT_UPDATE_PAY_STATUS_ERROR);
                    }
                } else {
                    //通过后，有可能会更改字段
                    $item = Payment::findFirst([
                        'conditions' => 'id = :id:',
                        'bind' => ['id' => $params['id']],
                    ]);
                    if ($item->pay_status == PayEnums::PAYMENT_MODULE_PAY_STATUS_PENDING) {
                        $item->pay_status = PayEnums::PAYMENT_MODULE_PAY_STATUS_ING;
                        if (!$item->save()) {
                            throw new BusinessException('一级审核人修改数据失败', ErrCode::$PAYMENT_UPDATE_PAY_STATUS_ERROR);
                        }
                    }
                }
            } else {
                $note = $params['not_pay_reason'];
                $item->is_pay = PayEnums::IS_PAY_NO;
                $item->not_pay_reason = $note;
                $item->pay_status = PayEnums::PAYMENT_MODULE_PAY_STATUS_FAILED;
                if (!$item->save()) {
                    throw new BusinessException('一级审核人支付选择否,提交的数据修改失败', ErrCode::$PAYMENT_UPDATE_PAY_DATA_ERROR);
                }
                $pay->reject($params['id'], $note, $user);
                if ($item->pay_method == Enums::PAYMENT_METHOD_CHECK) {
                    $old_checks_list = $item->getChecks();
                    if (!empty($old_checks_list)) {
                        foreach ($old_checks_list as $check) {
                            $check->is_deleted = GlobalEnums::IS_DELETED;
                            $check->updated_at = date('Y-m-d H:i:s', time());
                            if ($check->save() === false) {
                                throw new BusinessException('支票数据情况老数据失败, 数据: ' . json_encode($check->toArray(), JSON_UNESCAPED_UNICODE) . '; 原因可能是: ' . get_data_object_error_msg($check), ErrCode::$BUSINESS_ERROR);
                            }
                        }
                    }
                }
                $this->sendEmail($item, [$item->apply_staff_id]);
            }
        } catch (ValidationException $e) {
            $code = $e->getCode();
            $message = $e->getMessage();
        } catch (BusinessException $e) {
            $code = $e->getCode();
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        } catch (Exception $e) {
            $code = ErrCode::$SYSTEM_ERROR;
            $real_message = $e->getMessage();
            $message = static::$t->_('retry_later');
        }
        if (!empty($real_message)) {
            $this->logger->error('batch_audit_all_failed:' . $real_message);
        }
        return [
            'code' => $code,
            'message' => $message,
            'data' => $data,
        ];
    }


    /**
     * 发送邮件给指定人
     *
     * @param $item
     * @param $nodeAuditors
     * @return string|true
     */
    public function sendEmail($item, $nodeAuditors)
    {
        try {
            if (empty($item->id)) {
                throw new ValidationException(static::$t->_('data_empty_or_read_data_failed'), ErrCode::$VALIDATE_ERROR);
            }

            // 只在测试环境，给测试人员发邮件。
            $test_flag = in_array(env('runtime'), ['dev', 'test', 'tra', 'training']);

            // 开发/测试/tra环境收件人取指定配置的
            if ($test_flag) {
                $email_addressee_val = EnvModel::getEnvByCode(GlobalEnums::EMAIL_ORIGINAL_SPONSOR_EMAIL_CODE);
                $emails = !empty($email_addressee_val) ? explode(',', $email_addressee_val) : [];
            } else {
                // 生产取工号对应的
                $emails = (new StaffInfoModel())->getEmails($nodeAuditors);
            }

            // 如果收件人邮箱为空，则不发
            if (empty($emails)) {
                throw new ValidationException("no emails addressee, stop send, oa_type[{$item->oa_type}], no[{$item->no}],[" . json_encode($nodeAuditors, JSON_UNESCAPED_UNICODE) . ']');
            }

            // 语言初始化
            $orgLang = self::$language;

            // 邮件模板语言定义
            $language_setting = $this->getNoticeEmailTemplateLang();
            $first_lang = $language_setting['first_lang'];
            $second_lang = $language_setting['second_lang'];

            // 切为系统语言
            self::setLanguage($orgLang);

            // 各环境各国家dashboard页地址
            $url = EnvModel::getEnvByCode(GlobalEnums::OA_DASHBOARD_PAGE_URL_CODE);
            $url = !empty($url) ? $url : GlobalEnums::OA_DASHBOARD_PAGE_URL_DEFAULT;
            $email_title = BankFlowEnums::$oa_type_id_to_lang_key[$item->oa_type];
            $first_title = $first_lang->_($email_title) . '-' . $item->no;
            $second_title = $second_lang->_($email_title) . '-' . $item->no;
            $whereKey = "cancel_payment_email_where";
            $waitKey = 'cancel_payment_email_title_msg';
            $contentKey = "cancel_payment_email_to_content";
            $first_wait = $first_lang->_($waitKey);
            $second_wait = $second_lang->_($waitKey);

            $first_where = $first_lang->_($whereKey);
            $second_where = $second_lang->_($whereKey);

            $title = $first_wait . $first_title . $second_wait . $second_title;
            $first_content = $first_lang->_($contentKey, ["title" => $first_title, "where" => $first_where]);
            $second_content = $second_lang->_($contentKey, ["title" => $second_title, "where" => $second_where]);

            $html = <<<EOF
    hi,<br/>
    <span style="margin-left: 16px"></span>{$first_content}<a href="{$url}" target="_blank">{$url}</a><br/>
    <span style="margin-left: 16px"></span>{$second_content}<a href="{$url}" target="_blank">{$url}</a>

EOF;

            // 邮件发送
            $log = ["emails" => $emails, "title" => $title, "html" => $html];
            $send_res = $this->mailer->sendAsync($emails, $title, $html);
            if ($send_res) {
                $this->logger->info("sendEmail 发送成功！[OA-支付模块-我的支付/银行支付-支付否] " . json_encode($log, JSON_UNESCAPED_UNICODE));
            } else {
                $this->logger->warning("sendEmail 发送失败！[OA-支付模块-我的支付/银行支付-支付否] " . json_encode($log, JSON_UNESCAPED_UNICODE));
            }

        } catch (ValidationException $e) {
            $this->logger->info("sendEmail 校验异常！[OA-支付模块-我的支付/银行支付-支付否] 原因可能是：" . $e->getMessage());

        } catch (BusinessException $e) {
            $this->logger->info("sendEmail 业务异常！[OA-支付模块-我的支付/银行支付-支付否] 原因可能是：" . $e->getMessage());

        } catch (Exception $e) {
            $this->logger->warning("sendEmail 发送异常！[OA-支付模块-我的支付/银行支付-支付否] 原因可能是：" . $e->getMessage());
        }
    }

    /**
     * 根据费用所属公司获取FlashPay在线支付配置
     *
     * @param integer $cost_company_id 公司ID
     * @return mixed
     * @throws ValidationException
     */
    public function getFlashPayConfigByCostCompanyId($cost_company_id,$pay_bank_account)
    {
        $flash_pay_config = PaymentFlashPayConfigModel::findFirst([
            'conditions' => 'cost_company_id = :cost_company_id: and flashpay_sftp_shopname = :flashpay_sftp_shopname:',
            'bind' => ['cost_company_id' => $cost_company_id,'flashpay_sftp_shopname' => $pay_bank_account],
        ]);
        if (empty($flash_pay_config)) {
            //未开启给予提示
            throw new ValidationException(static::$t->_('payment_pay_flash_pay_account'), ErrCode::$VALIDATE_ERROR);
        }
        return $flash_pay_config;
    }

    /**
     * 获取收款人的银行和FLASH PAY的银行对照码
     *
     * @return array
     */
    public function getFlashPayBank(): array
    {
        $model = PaymentFlashPayBank::find([
            'conditions' => 'is_deleted = 0',
            'columns'    => 'bank_name,flash_pay_code',
        ])->toArray();
        if (!empty($model)) {
            return array_column($model, 'flash_pay_code', 'bank_name');
        }
        return [];
    }

    /**
     * 代理支付-未支付原因分类
     * @return array
     */
    public function getAgencyNotPayReasonCategory(): array
    {
        $str = EnumsService::getInstance()->getSettingEnvValue('agency_not_pay_reason_category');
        if (empty($str)) {
            return [];
        }
        $list   = explode(',', $str);
        $result = [];
        foreach ($list as $item) {
            $result[$item] = 'agency_not_pay_reason_category.' . $item;
        }
        return $result;
    }


    /**
     * 16325需求，新增银行转帐-在线支付逻辑判断；返回是否是在线支付
     *
     * @param object $payment 支付单据对象
     * @param string $pay_bank_name 支付银行
     * @param string $pay_bank_account 支付银行账号
     * @return int 是否在线支付,0否，1是
     * @throws ValidationException
     */
    public function checkPaymentOnlinePay($payment, $pay_bank_name, $pay_bank_account)
    {
        //第一步先判断选择的支付银行是否是在线支付银行 = 是
        if ($pay_bank_name && in_array($pay_bank_name, PayEnums::ONLINE_PAY)) {
            //第二步单据实付金额必须大于0
            if (bccomp($payment->amount_total_actually, 0, 2) !== 1) {
                throw new ValidationException(static::$t->_('payment_pay_amount_total_actually_error'), ErrCode::$VALIDATE_ERROR);
            }

            //第三步单据的费用所属公司信息缺失无法在线支付
            if (empty($payment->cost_company_id)) {
                throw new ValidationException(static::$t->_('payment_pay_flash_pay_module'), ErrCode::$VALIDATE_ERROR);
            }

            //第四步判断该单据所属公司是否开启flash pay在线支付
            $flash_pay_config = $this->getFlashPayConfigByCostCompanyId($payment->cost_company_id,$pay_bank_account);
            if (empty($pay_bank_account) || $pay_bank_account != $flash_pay_config->flashpay_sftp_shopname) {
                throw new ValidationException(static::$t->_('payment_pay_flash_pay_account'), ErrCode::$VALIDATE_ERROR);
            }

            //第五步判断该单据所在模块是否开启flash pay在线支付
            if (empty($flash_pay_config->oa_type) || !in_array($payment->oa_type, explode(',', $flash_pay_config->oa_type))) {
                throw new ValidationException(static::$t->_('payment_pay_flash_pay_module'), ErrCode::$VALIDATE_ERROR);
            }

            //第六步判断该单据的币种是否开启flash pay在线支付
            if (empty($flash_pay_config->currency) || !in_array($payment->currency, explode(',', $flash_pay_config->currency))) {
                throw new ValidationException(static::$t->_('payment_pay_flash_pay_currency'), ErrCode::$VALIDATE_ERROR);
            }

            //第七步判断收款信息栏收款人账号、收款人户名必须有值且必须一致；不一致或为空给予提示
            $pays = $payment->getPays();
            $pays = $pays ? $pays->toArray() : [];
            if (empty($pays)) {
                throw new ValidationException(static::$t->_('payment_pay_flash_pay_pays'), ErrCode::$VALIDATE_ERROR);
            }
            //收款人账号,只能有一个且有值
            $bank_account = array_unique(array_filter(array_column($pays, 'bank_account')));
            if (empty($bank_account) || count($bank_account) > 1) {
                throw new ValidationException(static::$t->_('payment_pay_flash_pay_pays'), ErrCode::$VALIDATE_ERROR);
            }
            //收款人户名,只能有一个且有值
            $bank_account_name = array_unique(array_filter(array_column($pays, 'bank_account_name')));
            if (empty($bank_account_name) || count($bank_account_name) > 1) {
                throw new ValidationException(static::$t->_('payment_pay_flash_pay_pays'), ErrCode::$VALIDATE_ERROR);
            }
            //收款人银行,只能有一个且有值
            $bank_name = array_unique(array_filter(array_column($pays, 'bank_name')));
            if (empty($bank_name) || count($bank_name) > 1) {
                throw new ValidationException(static::$t->_('payment_pay_flash_pay_pays'), ErrCode::$VALIDATE_ERROR);
            }

            //第八步判断收款信息栏收款人银行必须有映射的PAY的银行账号编码;未配置，或者收款人银行为空或者找不到映射关系
            $flash_pay_bank = $this->getFlashPayBank();
            if (empty($flash_pay_bank)) {
                throw new ValidationException(static::$t->_('payment_pay_flash_pay_bank'), ErrCode::$VALIDATE_ERROR);
            }
            if (!isset($flash_pay_bank[$bank_name[0]])) {
                FlashPayHelper::sendNotice("收款银行名称在FlashPay映射关系中未配置,收款人银行名称：{$bank_name[0]}");
                throw new ValidationException(static::$t->_('payment_pay_flash_pay_bank'), ErrCode::$VALIDATE_ERROR);
            }
            return PayEnums::IS_ONLINE_PAY_YES;
        }
        return PayEnums::IS_ONLINE_PAY_NO;
    }

    /**
     * 检测支票模块是否开启
     *
     * @return string|null
     */
    public function checkChequeIsOpen()
    {
        return EnumsService::getInstance()->getChequeModuleStatus();
    }

    /**
     * 批量获取指定业务模块单据的支付状态
     *
     * @param array $order_no_list 单据号列表
     * @param int $oa_type OA模块标识
     * @return array [no => pay_status]
     */
    public function batchGetOrdersPayStatusMap(array $order_no_list, int $oa_type)
    {
        $list = [];
        $order_no_list = array_values(array_filter(array_unique($order_no_list)));
        if (empty($order_no_list)) {
            return $list;
        }

        $list = Payment::find([
            'conditions' => 'no IN ({nos:array}) AND oa_type = :oa_type:',
            'bind' => ['nos' => $order_no_list, 'oa_type' => $oa_type],
            'columns' => ['no', 'pay_status'],
        ])->toArray();
        foreach ($list as &$value) {
            $value['pay_status'] = static::$t->_(PayEnums::$payment_module_pay_status[$value['pay_status']]);
        }

        return array_column($list, 'pay_status', 'no');
    }

    /**
     * 补充附件
     *
     * @param array $data 附件信息组
     * @return array
     */
    public function addAttachment($data)
    {
        $code = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $bool = false;
        $db = $this->getDI()->get('db_oa');
        try {
            $db->begin();
            //支付单据
            $payment = Payment::findFirst([
                'conditions' => 'id = :id:',
                'bind' => ['id' => $data['id']],
            ]);
            //非未支付的单据才可补充附件
            if (empty($payment) || $payment->pay_status == PayEnums::PAYMENT_MODULE_PAY_STATUS_NOTPAY) {
                throw new ValidationException(static::$t->_('payment_can_not_add_attachment'), ErrCode::$VALIDATE_ERROR);
            }

            $attachment_arr = [];
            foreach ($data['attachment_arr'] as $item) {
                $attachment_arr[] = [
                    'oss_bucket_type' => Enums::OSS_BUCKET_TYPE_PAY_MODULE_SUPPLEMENT,
                    'oss_bucket_key' => $data['id'],
                    'bucket_name' => $item['bucket_name'],
                    'object_key' => $item['object_key'],
                    'file_name' => $item['file_name'],
                    'created_at' => date('Y-m-d H:i:s'),
                ];
            }
            //先删除原来的附件信息
            $bool = $payment->getSupplements()->delete();
            if ($bool === false) {
                throw new BusinessException('支付管理-数据查询-补充附件-删除原先补充附件 失败：' . $data['id'], ErrCode::$BUSINESS_ERROR);
            }

            //再插入新的附件信息
            if (!empty($attachment_arr)) {
                $sys_attachment_model = new SysAttachmentModel();
                $bool = $sys_attachment_model->batch_insert($attachment_arr);
                if ($bool === false) {
                    throw new BusinessException('支付管理-数据查询-补充附件 失败：' . json_encode($attachment_arr, JSON_UNESCAPED_UNICODE) . ';可能的原因是:' . get_data_object_error_msg($sys_attachment_model), ErrCode::$BUSINESS_ERROR);
                }
            }
            $db->commit();
        } catch (ValidationException $e) {
            $code = $e->getCode();
            $message = $e->getMessage();
        } catch (BusinessException $e) {
            // 业务错误（数据错误等），不可对外抛出
            $code = $e->getCode();
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        } catch (Exception $e) {
            // 系统错误，不可对外抛出
            $code = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        }

        if (!empty($real_message)) {
            $this->logger->error('pay-add-attachment-failed:' . $real_message);
        }
        if (!empty($message)) {
            $db->rollback();
        }

        return [
            'code' => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data' => $bool,
        ];
    }

    /**
     * 添加评论
     *
     * @param array $params
     * @param array $user
     * @return array
     */
    public function addWorkflowComment(array $params, array $user)
    {
        $code = ErrCode::$SUCCESS;
        $message = 'success';

        try {
            // 审批流
            $request = (new PayFlowService())->getRequest($params['id']);
            if (empty($request)) {
                throw new ValidationException(static::$t->_('workflow_add_comment_error_001', ['biz_id' => $params['id']]), ErrCode::$VALIDATE_ERROR);
            }

            // 个人支付(一二级节点)的评论
            if ($request->state == Enums::WF_STATE_PENDING) {
                WorkflowCommentService::getInstance()->addComment($request, $params, $user, false);

            } else {
                // 银行支付(三级节点)的评论
                // 是否是三级节点支付人
                if (!FinalPayService::getInstance()->checkPayer($user['id'])) {
                    throw new ValidationException(static::$t->_('pay_method_payer_error'), ErrCode::$VALIDATE_ERROR);
                }

                // 添加
                $comment_model = new WorkflowRequestNodeCommentModel();
                $comment_model->request_id = $request->id;
                $comment_model->flow_id = $request->flow_id;
                $comment_model->node_id = $request->current_flow_node_id;
                $comment_model->sub_node_id = 0;
                $comment_model->staff_id = $user['id'];
                $comment_model->staff_name = $this->getNameAndNickName($user['name'], $user['nick_name']);
                $comment_model->staff_department = $user['department'];
                $comment_model->staff_job_title = $user['job_title'];
                $comment_model->content = $params['content'];
                $comment_model->progress_mark_id = $params['progress_mark_id'];
                $comment_model->created_at = date('Y-m-d H:i:s');

                $this->logger->info('添加的评论:' . json_encode($comment_model->toArray(), JSON_UNESCAPED_UNICODE));
                if ($comment_model->save() === false) {
                    throw new BusinessException('审批流评论添加失败,原因可能是: ' . get_data_object_error_msg($comment_model), ErrCode::$BUSINESS_ERROR);
                }
            }

            // 转为各业务模块
            $payment = Payment::findFirst([
                'conditions' => 'id = :id:',
                'bind' => ['id' => $request->biz_value],
            ]);
            if (!empty($payment)) {
                $model = BankFlowPayFlowService::getInstance()->getModelByTypeId($payment->oa_type);
                $model = $model->getModelByNo($payment->no);
                $biz_type = BankFlowEnums::$oa_type_and_wf_biz_type_map[$payment->oa_type] ?? Enums::WF_PAY_TYPE;
                WorkflowCommentService::getInstance()->sendCommentNotice($biz_type, $model->id, $params['content'], $user);
            }

        } catch (ValidationException $e) {
            // 校验错误，可对外抛出
            $code = $e->getCode();
            $message = $e->getMessage();

        } catch (BusinessException $e) {
            // 业务错误，不可对外抛出
            $code = $e->getCode();
            $message = static::$t->_('retry_later');
            $this->logger->warning('add_comment_failed: ' . $e->getMessage());

        } catch (Exception $e) {
            // 系统错误，不可对外抛出
            $code = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $this->logger->error('add_comment_failed: ' . $e->getMessage());
        }

        return [
            'code' => $code,
            'message' => $message,
        ];
    }

    /**
     * 银行转账合并支付方式验证拦截
     * @param array $params 请求参数组
     * @throws ValidationException
     */
    public function validateBankBatchPayment($params)
    {
        $payment_list = Payment::find([
            'conditions' => 'id in ({ids:array})',
            'bind' => ['ids' => $params['id_batch']],
        ])->toArray();
        if (empty($payment_list)) {
            throw new ValidationException(static::$t->_('data_empty_or_read_data_failed'), ErrCode::$VALIDATE_ERROR);
        }

        $pay_method = array_unique(array_column($payment_list, 'pay_method'));
        //不同支付方式，不可批量支付
        if (count($pay_method) > PayEnums::BATCH_PAYMENT_NOT_MAX_ONE || $pay_method[0] != GlobalEnums::PAYMENT_METHOD_BANK_TRANSFER) {
            throw new ValidationException(static::$t->_('cheque_account_batch_payment_pay_method'), ErrCode::$VALIDATE_ERROR);
        }
        $pay_bank_account_list = array_filter(array_column($payment_list, 'pay_bank_account'));
        if (count($pay_bank_account_list) != count($payment_list)) {
            throw new ValidationException(static::$t->_('batch_payment_pay_bank_account_invalid'), ErrCode::$VALIDATE_ERROR);
        }
    }

    /**
     * 生成导入支票的上传模板
     *
     * @param array $user
     * @return array
     * @throws BusinessException
     * @throws GuzzleException
     */
    public function generateChequeUploadTemplate(array $user)
    {
        $header = [
            static::$t->_('pay_upload_cheque_template_01'), // 支票号
            static::$t->_('pay_upload_cheque_template_02'), // 支票支付金额
            static::$t->_('pay_upload_cheque_template_03'), // 签票日期
            static::$t->_('pay_upload_cheque_template_04'), // 计划承兑日期
            static::$t->_('pay_upload_cheque_template_05'), // 收款人名称
            static::$t->_('pay_upload_cheque_template_06'), // 备注
        ];

        $file_name = 'Cheque Upload Template.xlsx';
        return $this->exportExcel($header, [], $file_name);
    }

    /**
     * 批量导入支票
     *
     * @param int $order_currency 单据币种
     * @param array $excel_data 支票文件数据
     * @param array $user 当前操作人
     * @return array
     * @throws BusinessException
     * @throws GuzzleException
     * @throws ValidationException
     */
    public function batchUploadChequeFile(int $order_currency, array $excel_data, array $user)
    {
        $excel_header = array_shift($excel_data);
        $excel_header[6] = static::$t->_('pay_upload_cheque_template_07');

        $upload_total = (int)EnumsService::getInstance()->getSettingEnvValue('pay_upload_cheque_total_rows');
        $excel_total_row = count($excel_data);
        if ($excel_total_row > $upload_total) {
            throw new ValidationException(static::$t->_('pay_upload_cheque_file_error_04', ['upload_total' => $upload_total]));
        }

        // 获取支票在台账的信息
        $cheque_code_list = array_column($excel_data, 0);
        $cheque_list = ChequeAccountRepository::getInstance()->getChequeListByChequeCodes(array_values(array_filter(array_unique($cheque_code_list))));
        if (empty($cheque_list)) {
            throw new ValidationException(static::$t->_('pay_upload_cheque_file_error_05'));
        }

        $cheque_list = array_column($cheque_list, null, 'cheque_code');

        $order_currency_text = static::$t->_(GlobalEnums::$currency_item[$order_currency] ?? '') ?? '';

        // 遍历每行支票, 分别验证
        $pay_check = [];
        $cheque_code_list_count = array_count_values($cheque_code_list);
        foreach ($excel_data as $k => $row) {
            $row = trim_array(array_slice($row,0, 6));

            // 前5项均必填
            if (
                mb_strlen($row[0]) < 1 ||
                mb_strlen($row[1]) < 1 ||
                mb_strlen($row[2]) < 1 ||
                mb_strlen($row[3]) < 1 ||
                mb_strlen($row[4]) < 1
            ) {
                $excel_data[$k][6] = static::$t->_('pay_upload_cheque_file_error_06');
                continue;
            }

            // 支票号是否重复
            if ($cheque_code_list_count[$row[0]] > 1) {
                $excel_data[$k][6] = static::$t->_('pay_upload_cheque_file_error_09');
                continue;
            }

            // 支票信息校验
            $cheque_info = $cheque_list[$row[0]] ?? [];
            if (empty($cheque_info)) {
                $excel_data[$k][6] = static::$t->_('pay_upload_cheque_file_error_11');
                continue;
            }

            // 支票使用状态
            if ($cheque_info['use_status'] != ChequeEnums::CHECKS_NUMBER_USE_STATUS_ONE) {
                $excel_data[$k][6] = static::$t->_('pay_upload_cheque_file_error_07');
                continue;
            }

            // 支票与单据币种
            if ($cheque_info['currency'] != $order_currency) {
                $excel_data[$k][6] = static::$t->_('pay_upload_cheque_file_error_08');
                continue;
            }

            // 支票金额
            if (!is_numeric($row[1])) {
                $excel_data[$k][6] = static::$t->_('pay_upload_cheque_file_error_10');
                continue;
            }

            $cheque_amount = number_format($row[1], 2, '.', '');
            if ($cheque_amount <= 0) {
                $excel_data[$k][6] = static::$t->_('pay_upload_cheque_file_error_10');
                continue;
            }

            // 日期校验
            // 签票日期
            $ticket_signing_date_res = $this->handleChequeUploadFileDate($row[2]);
            $excel_data[$k][2] = $ticket_signing_date_res['src_date'];
            $ticket_signing_date = $ticket_signing_date_res['format_date'];
            if (!is_date_format($ticket_signing_date)) {
                $excel_data[$k][6] = static::$t->_('pay_upload_cheque_file_error_12');
                continue;
            }

            // 计划承兑日期
            $exchange_date_res = $this->handleChequeUploadFileDate($row[3]);
            $excel_data[$k][3] = $exchange_date_res['src_date'];
            $exchange_date = $exchange_date_res['format_date'];
            if (!is_date_format($exchange_date)) {
                $excel_data[$k][6] = static::$t->_('pay_upload_cheque_file_error_13');
                continue;
            }

            // 校验通过
            $excel_data[$k][6] = static::$t->_('excel_result_validation_pass');
            $repeat_cheque_codes[] = $row[0];

            // 界面展示的数据
            $pay_check[] = [
                'ticket_no' => $row[0], // 支票号
                'bank_name' => $cheque_info['bank_number'], // 支付银行
                'amount' => $cheque_amount, // 支票支付金额
                'currency' => $order_currency, // 币种枚举
                'currency_text' => $order_currency_text, // 币种标签
                'check_date' => $ticket_signing_date, // 签票日期
                'date' => $exchange_date, // 计划承兑日期
                'remark' => mb_substr($row[5], 0, 500), // 备注截取前500字符
                'payee_name' => $row[4], // 收款人名称(户名)
                'payee_account' => '', // 收款人账号
                'payment_pay_id' => '', // 支付明细表ID
                'cheque_account_id' => $cheque_info['id'], // 支票台账ID
            ];
        }

        // 校验通过的行数
        $success_total_row = count($pay_check);
        $error_total_row = $excel_total_row - $success_total_row;

        $file_name = 'Import Cheque Results.xlsx';
        $excel_file = $this->exportExcel($excel_header, $excel_data, $file_name);

        return [
            'all_num' => $excel_total_row, // 数据总条数
            'success_num' => $success_total_row, // 校验成功的行数
            'failed_num' => $error_total_row, // 校验有问题的行数
            'pay_check' => $pay_check, // 校验成功的行数
            'url' =>$excel_file['data'] ?? '', // 导入支票的结果文件
        ];
    }

    /**
     * 导入支票日期格式处理
     * @param string $file_src_date
     * @return mixed
     */
    protected function handleChequeUploadFileDate(string $file_src_date)
    {
        $data = [
            'src_date' => $file_src_date, // 格式化前的日期
            'format_date' => '', // 格式化后的日期
        ];

        // 时间戳
        if (preg_match('/\d{10}/', $file_src_date)) {
            $data['src_date'] = date('d/m/Y', $file_src_date);
            $data['format_date'] = date('Y-m-d', $file_src_date);
        } else if (stripos($file_src_date, '/') > 0) {
            $date_item = explode('/', $file_src_date);
            $data['format_date'] = $date_item[2] . '-' . $date_item[1] . '-' . $date_item[0];
        } else if (stripos($file_src_date, '-') > 0) {
            $date_item = explode('-', $file_src_date);
            $data['format_date'] = $date_item[2] . '-' . $date_item[1] . '-' . $date_item[0];
        }

        return $data;
    }

}
