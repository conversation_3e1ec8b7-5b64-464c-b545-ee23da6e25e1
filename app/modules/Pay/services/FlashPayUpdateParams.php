<?php

namespace App\Modules\Pay\Services;

use App\Models\oa\PaymentOnlinePayModel;
use App\Modules\Pay\Models\Payment;

/**
 * FlashPay支付状态更新参数基类
 * @description: FlashPay支付状态更新的参数基类
 * @author: AI
 * @date: 2025-08-18
 */
abstract class FlashPayUpdateParams
{
    /**
     * @var Payment 支付记录
     */
    protected $payment;

    /**
     * @var
     */
    protected $paymentOnlinePay;

    /**
     * @var string 交易状态
     */
    protected $transactionStatus;

    /**
     * @var object 日志对象
     */
    protected $logger;

    /**
     * 构造函数
     * @param Payment $payment 支付记录
     * @param string $transactionStatus 交易状态
     */
    public function __construct(Payment $payment, $transactionStatus)
    {
        $this->payment           = $payment;
        $this->transactionStatus = $transactionStatus;
    }

    /**
     * 获取支付记录
     * @return Payment
     */
    public function getPayment()
    {
        return $this->payment;
    }

    /**
     * 获取支付记录
     * @return mixed
     */
    public function getPaymentOnlinePay()
    {
        return $this->paymentOnlinePay;
    }

    /**
     * 获取交易状态
     * @return string
     */
    public function getTransactionStatus()
    {
        return $this->transactionStatus;
    }

    /**
     * 获取支付方式
     * @return string
     */
    abstract public function getPaymentMethod();

    /**
     * 验证参数
     * @return bool
     * @throws \Exception
     */
    abstract public function validate();
}
