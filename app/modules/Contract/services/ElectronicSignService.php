<?php
/**
 * Created by PhpStorm.
 * Date: 2023/7/18
 * Time: 16:27
 */

namespace App\Modules\Contract\Services;

use App\Library\Enums;
use App\Library\Enums\ContractEnums;
use App\Library\Enums\GlobalEnums;
use App\Library\ErrCode;
use App\Library\Exception\BusinessException;
use App\Library\Validation\ValidationException;
use App\Models\oa\ContractElectronicClientOtpLogModel;
use App\Models\oa\SysAttachmentModel;
use App\Modules\Common\Services\AttachmentService;
use App\Modules\User\Services\UserService;
use App\Repository\HrStaffRepository;
use App\Repository\oa\ContractElectronicSignInfoRepository;
use App\Traits\TokenTrait;
use App\Util\RedisKey;
use Exception;
use Phalcon\Mvc\Model\Resultset;
use Phalcon\Mvc\Phalcon\Mvc\Model;

class ElectronicSignService extends BaseService
{
    use TokenTrait;

    private static $instance;

    private function __construct()
    {
    }

    private function __clone()
    {
    }

    public static function getInstance()
    {
        if (!isset(self::$instance) || !(self::$instance instanceof self)) {
            self::$instance = new self;
        }
        return self::$instance;
    }

    /**
     * sign_key 检测
     *
     * @param string $sign_key
     * @return array
     * @throws ValidationException
     */
    public function checkSignKey(string $sign_key)
    {
        if (mb_substr_count($sign_key, '-') !== 2) {
            throw new ValidationException(static::$t->_('electronic_contract_sign_error_001'), ErrCode::$REQUEST_SIGN_LINK_INVALID);
        }

        $params = explode('-', $sign_key);
        $electronic_key = $params[0];// 电子合同编号key
        $email_key = $params[1];// 待签约人邮箱key
        $link_scence = $params[2];// 邮箱链接认证场景:

        $check_otp_scence_item = [
            ContractEnums::ELECTRONIC_CONTRACT_CLIENT_OTP_BUSINESS_SIGN_LOGIN,
            ContractEnums::ELECTRONIC_CONTRACT_CLIENT_OTP_POA_SIGN_LOGIN,
            ContractEnums::ELECTRONIC_CONTRACT_CLIENT_OTP_BUSINESS_DOWNLOAD_LOGIN,
        ];
        if (!in_array($link_scence, $check_otp_scence_item)) {
            throw new ValidationException(static::$t->_('electronic_contract_sign_error_001'), ErrCode::$REQUEST_SIGN_LINK_INVALID);
        }

        return $this->getElectronicSignInfo($electronic_key, $email_key, $link_scence);
    }

    /**
     * 认证场景: 发送otp 邮箱 验证码
     *
     * @param string $sign_key 未认证的sign_key, 来自邮箱链接参数
     * @return mixed
     * @throws BusinessException
     * @throws ValidationException
     */
    public function sendOtpCodeV1(string $sign_key)
    {
        // 未认证的访问, 先验证sign_key
        $sign_info = $this->checkSignKey($sign_key);

        $sign_key_item = explode('-', $sign_key);
        return $this->sendOtpCode($sign_key_item[0], $sign_key_item[1], $sign_key_item[2], $sign_info);
    }

    /**
     * 发送otp 邮箱 验证码 公共方法
     *
     * @param string $electronic_key 电子合同标识
     * @param string $email_key 邮箱标识
     * @param int $otp_scence otp 验证码场景
     * @param array $sign_info 签字信息, 比如 邮箱/电子合同名称……
     * @return bool
     * @throws BusinessException
     * @throws ValidationException
     */
    public function sendOtpCode(string $electronic_key, string $email_key, int $otp_scence, array $sign_info)
    {
        $otp_code_key = md5(RedisKey::ELECTRONIC_CONTRACT_CLIENT_OTP_CODE_PREFIX . $electronic_key . '_' . $email_key . '_' . $otp_scence);

        $cache_data = $this->getCache($otp_code_key);
        if (!empty($cache_data)) {
            $cache_data = json_decode($cache_data, true);
            $seconds = ContractEnums::ELECTRONIC_CONTRACT_CLIENT_OTP_CODE_SEND_FREQUENCY - (time() - $cache_data['send_time']);
            if ($seconds > 0) {
                throw new ValidationException(static::$t->_('electronic_contract_sign_error_003', ['seconds' => $seconds]), ErrCode::$VALIDATE_ERROR);
            }
        }

        // 生成 otp code
        $otp_code = get_random_number(ContractEnums::ELECTRONIC_CONTRACT_CLIENT_OTP_CODE_LENGTH);

        // 写入缓存
        $otp_uuid = generate_uuid('otp_key');
        $cache_data = json_encode([
            'otp_code' => $otp_code,
            'send_time' => time(),
            'otp_uuid' => $otp_uuid
        ]);
        if (!$this->setCache($otp_code_key, $cache_data, ContractEnums::ELECTRONIC_CONTRACT_CLIENT_OTP_CODE_VALIDITY_PERIOD * 60)) {
            throw new ValidationException(static::$t->_('electronic_contract_sign_error_002'), ErrCode::$VALIDATE_ERROR);
        }

        // 根据sign_key查找对应的接收验证码邮箱: 商务 / POA
        if ($email_key == $sign_info['custom_company_sign_key']) {
            $recipient_email = $sign_info['custom_contact_email'];
        } else {
            $recipient_email = array_column($sign_info['custom_poa_item'], null, 'sign_key')[$email_key]['sign_email'] ?? [];
        }

        // 发送邮件
        $email_var = [
            'emails' => [$recipient_email],
            'contract_name' => $sign_info['contract_name'],
            'contract_lang' => $sign_info['lang'],
            'department_id' => $sign_info['department_id'],
            'otp_code' => $otp_code,
            'valid_minutes' => ContractEnums::ELECTRONIC_CONTRACT_CLIENT_OTP_CODE_VALIDITY_PERIOD
        ];

        $this->sendElectronicContractEmailNotice(ContractEnums::ELECTRONIC_CONTRACT_EMIAL_SCENCE_OTP_CODE, $email_var);

        // 商务上传POA图片otp code 和 POA提交签字的otp code 需记录发送日志
        if (in_array($otp_scence, [ContractEnums::ELECTRONIC_CONTRACT_CLIENT_OTP_BUSINESS_UPLOAD_SIGN, ContractEnums::ELECTRONIC_CONTRACT_CLIENT_OTP_POA_SUBMIT_SIGN])) {
            $otp_log = [
                'electronic_no' => $sign_info['electronic_no'],
                'sign_version' => $sign_info['sign_version'],
                'sender_uuid' => $sign_info['current_email_key'],
                'sender_role' => $sign_info['sign_role'],
                'otp_uuid' => $otp_uuid,
                'otp_scence' => $otp_scence,
                'otp_email' => $recipient_email,
                'otp_email_uuid' => $email_key,
                'otp_code' => $otp_code,
                'send_ip' => get_client_real_ip(),
                'send_at' => date('Y-m-d H:i:s'),
            ];
            (new ContractElectronicClientOtpLogModel())->i_create($otp_log);
        }

        return true;
    }

    /**
     * 认证场景验证码校验
     *
     * @param string $sign_key 未认证的sign_key, 来自邮箱链接参数
     * @param string $otp_code 提交的otp验证码
     * @return mixed
     * @throws BusinessException
     * @throws ValidationException
     */
    public function optCodeCheckV1(string $sign_key, string $otp_code)
    {
        $data = ['check_res' => false];

        // otp code 验证
        $sign_key_item = explode('-', $sign_key);
        $this->otpCodeCheck($sign_key_item[0], $sign_key_item[1], $sign_key_item[2], $otp_code);

        // 验证通过
        // 1 获取电子合同信息
        $sign_info = $this->getElectronicSignInfo($sign_key_item[0], $sign_key_item[1], $sign_key_item[2]);

        // 2 鉴权通过: 生成token
        $this->setTokenType(ContractEnums::ELECTRONIC_CONTRACT_CLIENT_SIGN_TOKEN_TYPE);
        $token = $this->generateToken(['id' => $sign_key]);
        $data['token'] = $token->__toString();
        $data['token_type'] = 'Bearer';
        $data['sign_role'] = $sign_info['sign_role'];
        $data['check_res'] = true;

        // 3. sign_token 设置缓存
        $this->saveSignToken($sign_key_item[0], $sign_key_item[1], $data['token']);

        $this->logger->info("电子合同-甲方签约-otp认证通过: emsk={$sign_key}, token_data=" . json_encode($data, JSON_UNESCAPED_UNICODE));

        return $data;
    }

    /**
     * 缓存sign_token
     *
     * @param string $electronic_key
     * @param string $sign_key
     * @param string $sign_token
     * @return bool
     * @throws BusinessException
     */
    public function saveSignToken(string $electronic_key, string $sign_key, string $sign_token)
    {
        $cache_key = md5(RedisKey::ELECTRONIC_CONTRACT_CLIENT_SIGN_TOKEN_PREFIX . $electronic_key . '_' . $sign_key);

        $this->setTokenType(ContractEnums::ELECTRONIC_CONTRACT_CLIENT_SIGN_TOKEN_TYPE);
        $expiration = $this->getTokenValidityPeriod();
        $log_data = [
            'cache_key' => $cache_key,
            'expiration' => $expiration,
            'electronic_key' => $electronic_key,
            'sign_key' => $sign_key,
            'sign_token' => $sign_token,
            'add_time' => date('Y-m-d H:i:s')
        ];
        $log_data = json_encode($log_data, JSON_UNESCAPED_UNICODE);

        if (!$this->setCache($cache_key, $sign_token, $expiration)) {
            throw new BusinessException('电子合同-甲方签约-OTP认证-sign_token缓存设置失败=' . $log_data, ErrCode::$BUSINESS_ERROR);
        }

        $this->logger->info('电子合同-甲方签约-OTP认证-sign_token缓存设置成功=' . $log_data);

        return true;
    }

    /**
     * 验证客户sign_token
     *
     * @param string $token_sign_key (合同key 和 签字人key)
     * @return bool
     * @throws Exception
     */
    public function checkSignToken(string $token_sign_key)
    {
        $key_item = explode('-', $token_sign_key);
        $electronic_key = $key_item[0] ?? '';
        $sign_key = $key_item[1] ?? '';
        $cache_key = md5(RedisKey::ELECTRONIC_CONTRACT_CLIENT_SIGN_TOKEN_PREFIX . $electronic_key . '_' . $sign_key);
        if (empty($this->getCache($cache_key))) {
            $log_data = [
                'token_sign_key' => $token_sign_key,
                'electronic_key' => $electronic_key,
                'sign_key' => $sign_key,
                'cache_key' => $cache_key
            ];

            $this->logger->notice(['电子合同-甲方签约-sign_token校验异常' => $log_data]);
            throw new Exception('电子合同-甲方签约-checkSignToken 异常');
        }

        return true;
    }

    /**
     * otp code 验证 公共方法
     *
     * @param string $electronic_key 电子合同标识
     * @param string $email_key 邮箱标识
     * @param int $otp_scence otp 验证码场景
     * @param string $otp_code 提交的验证码
     * @param array $sign_info
     * @return bool
     * @throws ValidationException
     */
    public function otpCodeCheck(string $electronic_key, string $email_key, int $otp_scence, string $otp_code, array $sign_info = [])
    {
        $otp_code_key = md5(RedisKey::ELECTRONIC_CONTRACT_CLIENT_OTP_CODE_PREFIX . $electronic_key . '_' . $email_key . '_' . $otp_scence);

        $cache_data = $this->getCache($otp_code_key);
        if (empty($cache_data)) {
            throw new ValidationException(static::$t->_('electronic_contract_sign_error_004'), ErrCode::$VALIDATE_ERROR);
        }

        $cache_data = json_decode($cache_data, true);
        if ($cache_data['otp_code'] != $otp_code) {
            throw new ValidationException(static::$t->_('electronic_contract_sign_error_004'), ErrCode::$VALIDATE_ERROR);
        }

        $this->delCache($otp_code_key);

        // 商务上传图片otp验证 和 POA提交签字的otp验证, 需记录otp验证码使用日志
        if (in_array($otp_scence, [ContractEnums::ELECTRONIC_CONTRACT_CLIENT_OTP_BUSINESS_UPLOAD_SIGN, ContractEnums::ELECTRONIC_CONTRACT_CLIENT_OTP_POA_SUBMIT_SIGN])) {
            $otp_log_model = ContractElectronicClientOtpLogModel::findFirst([
                'conditions' => 'otp_uuid = :otp_uuid:',
                'bind' => ['otp_uuid' => $cache_data['otp_uuid']]
            ]);

            if (!empty($otp_log_model)) {
                $otp_log = [
                    'use_status' => 1,
                    'user_uuid' => $sign_info['current_email_key'],
                    'user_role' => $sign_info['sign_role'],
                    'use_ip' => get_client_real_ip(),
                    'use_at' => date('Y-m-d H:i:s'),
                ];
                $otp_log_model->i_update($otp_log);
            }
        }

        return true;
    }

    /**
     * 获取电子合同信息
     *
     * @param string $electronic_key 电子合同标识
     * @param string $email_key 签字邮箱标识
     * @param int $link_scence 链接场景
     * @return array
     * @throws ValidationException
     */
    public function getElectronicSignInfo(string $electronic_key, string $email_key, int $link_scence)
    {
        // 验证电子合同签约信息
        $sign_info_model = ContractElectronicSignInfoRepository::getInstance()->getSignInfoByElectronicKey($electronic_key);
        if (empty($sign_info_model)) {
            throw new ValidationException(static::$t->_('electronic_contract_sign_error_001'), ErrCode::$REQUEST_SIGN_LINK_INVALID);
        }

        // 验证电子合同主信息
        $main_model = $sign_info_model->getElectronicContract();
        if (empty($main_model)) {
            throw new ValidationException(static::$t->_('electronic_contract_sign_error_001'), ErrCode::$REQUEST_SIGN_LINK_INVALID);
        }

        // 待签约验证
        $waiting_sign_status_item = [ContractEnums::CONTRACT_SIGN_STATUS_2, ContractEnums::CONTRACT_SIGN_STATUS_6];
        $waiting_sign_scence_item = [
            ContractEnums::ELECTRONIC_CONTRACT_CLIENT_OTP_BUSINESS_SIGN_LOGIN,
            ContractEnums::ELECTRONIC_CONTRACT_CLIENT_OTP_POA_SIGN_LOGIN,
        ];
        if (in_array($link_scence, $waiting_sign_scence_item) && ($main_model->approve_status != Enums::WF_STATE_APPROVED || !in_array($main_model->sign_status, $waiting_sign_status_item))) {
            throw new ValidationException(static::$t->_('electronic_contract_sign_error_001'), ErrCode::$REQUEST_SIGN_LINK_INVALID);
        }

        // 下载验证
        // 商务下载签字版合同, 需检测是否在下载的有效期
        if ($link_scence == ContractEnums::ELECTRONIC_CONTRACT_CLIENT_OTP_BUSINESS_DOWNLOAD_LOGIN) {
            if ($main_model->approve_status != Enums::WF_STATE_APPROVED || $main_model->sign_status != ContractEnums::CONTRACT_SIGN_STATUS_3) {
                throw new ValidationException(static::$t->_('electronic_contract_sign_error_001'), ErrCode::$REQUEST_SIGN_LINK_INVALID);
            }

            $sign_completed_date = mb_substr($main_model->sign_completed_at, 0, 10);
            $expired_date = date('Y-m-d', strtotime("$sign_completed_date +{$sign_info_model->download_link_valid_days} day"));
            if (date('Y-m-d') > $expired_date) {
                throw new ValidationException(static::$t->_('electronic_contract_sign_error_001'), ErrCode::$REQUEST_SIGN_LINK_INVALID);
            }
        }

        // 验证签约邮箱 + 签约人身份: 是商务签约 还是 POA签约
        if ($sign_info_model->custom_contact_email_key == $email_key) {
            $sign_role = ContractEnums::ELECTRONIC_CONTRACT_SIGN_ROLE_BUSINESS;
            $sign_email = $sign_info_model->custom_contact_email;
        } else {
            $custom_poa_item = !empty($sign_info_model->custom_poa_item) ? json_decode($sign_info_model->custom_poa_item, true) : [];
            $custom_poa_item = array_column($custom_poa_item, null, 'sign_key');
            $poa_info = $custom_poa_item[$email_key] ?? [];
            $sign_role = !empty($poa_info) ? ContractEnums::ELECTRONIC_CONTRACT_SIGN_ROLE_POA : '';
            $sign_email = $poa_info['sign_email'] ?? '';
        }

        if (empty($sign_role)) {
            throw new ValidationException(static::$t->_('electronic_contract_sign_error_001'), ErrCode::$REQUEST_SIGN_LINK_INVALID);
        }

        // 商务 和 POA的链接场景校验
        $business_link_scence_item = [
            ContractEnums::ELECTRONIC_CONTRACT_CLIENT_OTP_BUSINESS_SIGN_LOGIN,
            ContractEnums::ELECTRONIC_CONTRACT_CLIENT_OTP_BUSINESS_DOWNLOAD_LOGIN,
        ];
        if ($sign_role == ContractEnums::ELECTRONIC_CONTRACT_SIGN_ROLE_BUSINESS && !in_array($link_scence, $business_link_scence_item)) {
            throw new ValidationException(static::$t->_('electronic_contract_sign_error_001'), ErrCode::$REQUEST_SIGN_LINK_INVALID);
        }

        if ($sign_role == ContractEnums::ELECTRONIC_CONTRACT_SIGN_ROLE_POA && $link_scence != ContractEnums::ELECTRONIC_CONTRACT_CLIENT_OTP_POA_SIGN_LOGIN) {
            throw new ValidationException(static::$t->_('electronic_contract_sign_error_001'), ErrCode::$REQUEST_SIGN_LINK_INVALID);
        }

        // 合同适用部门相关配置
        $department_config = $this->getContractApplicableDepartmentConfig($main_model->department_id);

        $_pdf_url = !empty($main_model->file_url) ? \App\Library\OssHelper::downloadFileHcm($main_model->file_url) : '';
        
        return [
            'electronic_no' => $sign_info_model->electronic_no,
            'electronic_key' => $sign_info_model->electronic_key,
            'sign_version' => $sign_info_model->sign_version,
            'contract_name' => $main_model->contract_name,
            'lang' => $main_model->lang,
            'sign_role' => $sign_role,
            'email' => $sign_email,
            'file_url' => $_pdf_url['file_url'] ?? '',
            'custom_company_sign_img' => $sign_info_model->custom_company_sign_img,
            'custom_company_sign_key' => $sign_info_model->custom_contact_email_key,
            'custom_contact_email' => $sign_info_model->custom_contact_email,
            'custom_poa_item' => json_decode($sign_info_model->custom_poa_item, true),
            'current_email_key' => $email_key,
            'department_id' => $main_model->department_id,
            'is_show_return_person_signature' => $main_model->is_show_return_person_signature,
            'return_person_signature_img'     => $sign_info_model->return_person_signature_img,
            'company_id'                      => $department_config['company_id'] ?? '',
        ];
    }

    /**
     * 获取电子合同pdf文件内容
     *
     * @param array $sign_info 签字信息
     * @return mixed
     * @throws ValidationException
     */
    public function getElectronicContractFileContent(array $sign_info)
    {
        $header_info = @get_headers($sign_info['file_url'], true) ?? [];
        if (!isset($header_info[0]) || !stripos($header_info[0] ?? '', '200')) {
            throw new ValidationException(static::$t->_('electronic_contract_sign_error_005'), ErrCode::$VALIDATE_ERROR);
        }

        // 中文乱码
        $filename = urlencode($sign_info['contract_name'] . '.pdf');
        $filename = str_replace("+", "%20", $filename);
        $header_info['file_name'] = $filename;

        return [
            'sign_info' => $sign_info,
            'header_info' => $header_info
        ];
    }

    /**
     * 获取签约页详情
     *
     * @param array $sign_info
     * @return mixed
     * @throws BusinessException
     */
    public function getSignPageDetail(array $sign_info)
    {
        $page_data = [];

        // 是否显示上传文件资料表单-Retail
        $is_show_upload_file = false;

        // 是否显示上传退件签字人的签名
        $is_show_return_person_signature = false;
        $return_person_signature_img     = '';

        // 商务签字: 公司签章 + POA签章
        if ($sign_info['sign_role'] == ContractEnums::ELECTRONIC_CONTRACT_SIGN_ROLE_BUSINESS) {
            $is_retail_departmemnt           = $this->isRetailManagement($sign_info['department_id']);
            $is_show_upload_file             = $is_retail_departmemnt;
            $is_show_return_person_signature = $sign_info['is_show_return_person_signature'] == 1;
            $return_person_signature_img     = $sign_info['return_person_signature_img'];

            $page_data = [
                'custom_company_sign_img' => $sign_info['custom_company_sign_img'],
                'custom_company_sign_key' => $sign_info['custom_company_sign_key'],
                'custom_poa_item' => $sign_info['custom_poa_item']
            ];

        } else if ($sign_info['sign_role'] == ContractEnums::ELECTRONIC_CONTRACT_SIGN_ROLE_POA) {
            // POA 签字: 返回当前POA的签字信息
            $poa_item = array_column($sign_info['custom_poa_item'], null, 'sign_key');
            $page_data['custom_poa_item'] = [$poa_item[$sign_info['current_email_key']]] ?? [];
        }

        // 相关资料列表
        $related_file_list = [];
        if ($is_show_upload_file) {
            $sign_info_model = ContractElectronicSignInfoRepository::getInstance()->getSignInfoByElectronicKey($sign_info['electronic_key']);
            $_related_file_list = $sign_info_model->getRelatedFileList()->toArray();
            foreach ($_related_file_list as $file) {
                $_pdf_url = !empty($file['object_key']) ? \App\Library\OssHelper::downloadFileHcm($file['object_key']) : '';
                $related_file_list[] = [
                    'id'            => $file['object_key'],
                    'business_type' => AttachmentService::BUSINESS_TYPE_RELATED_FILE_LIST,
                    'bucket_name' => $file['bucket_name'],
                    'file_name' => $file['file_name'],
                    'object_key' => $file['object_key'],
                    'object_url' => $_pdf_url['file_url'] ?? '',
                    'created_at' => $file['created_at'],
                ];
            }
        }

        $page_data['is_show_upload_file']             = $is_show_upload_file;
        $page_data['related_file_list']               = $related_file_list;
        $page_data['is_show_return_person_signature'] = $is_show_return_person_signature;
        $page_data['return_person_signature_img']     = $return_person_signature_img;

        return $page_data;
    }

    /**
     * 获取签约页详情
     *
     * @param array $params 提交参数
     * @param array $sign_info 签字信息
     * @return mixed
     * @throws BusinessException
     * @throws ValidationException
     */
    public function saveFieldValue(array $params, array $sign_info)
    {
        $save_data = [];

        $sign_info_model = ContractElectronicSignInfoRepository::getInstance()->getSignInfoByElectronicKey($sign_info['electronic_key']);

        // 相关资料
        if ($params['field_name'] == ContractEnums::ELECTRONIC_CONTRACT_FIELD_RELATED_FILE) {
            if ($params['sign_key'] != $sign_info['custom_company_sign_key']) {
                throw new ValidationException(static::$t->_('electronic_contract_sign_error_006'), ErrCode::$VALIDATE_ERROR);
            }

            $file_info = $params['field_value'][0];
            if ($file_info['action'] == 'add') {
                $attachment_data = [
                    'oss_bucket_type' => Enums::OSS_BUCKET_TYPE_ELECTRONIC_CONTRACT_CLIENT_RELATED_FILE,
                    'sub_type' => 0,
                    'oss_bucket_key' => $sign_info_model->id,
                    'bucket_name' => $file_info['bucket_name'],
                    'object_key' => $file_info['object_key'],
                    'file_name' => $file_info['file_name']
                ];

                $attachment_model = new SysAttachmentModel();
                if ($attachment_model->i_create($attachment_data) == false) {
                    throw new BusinessException('电子合同-异步商务上传相关资料异常,原因可能是:' . get_data_object_error_msg($attachment_model) . ';数据:' . json_encode($attachment_data, JSON_UNESCAPED_UNICODE), ErrCode::$BUSINESS_ERROR);
                }

            } else if ($file_info['action'] == 'del') {
                $exist_files = $sign_info_model->getRelatedFileList();
                foreach ($exist_files as $file_model) {
                    if ($file_model->object_key == $file_info['object_key']) {
                        $file_model->deleted = GlobalEnums::IS_DELETED;
                        if ($file_model->save() === false) {
                            throw new BusinessException('电子合同-异步商务上传相关资料异常,原因可能是:' . get_data_object_error_msg($file_model), ErrCode::$BUSINESS_ERROR);
                        }
                    }
                }
            }

        } else if (in_array($params['field_name'], [ContractEnums::ELECTRONIC_CONTRACT_FIELD_COMPANY_SIGN_IMG, ContractEnums::ELECTRONIC_CONTRACT_FIELD_RETURN_PERSON_SIGNATURE_IMG])) {
            // 公司签名/退件签字人签名 字段
            if ($params['sign_key'] != $sign_info['custom_company_sign_key']) {
                throw new ValidationException(static::$t->_('electronic_contract_sign_error_006'), ErrCode::$VALIDATE_ERROR);
            }

            $save_data[$params['field_name']] = $params['field_value'];
        } else {
            // POA 职位 或 签名字段
            if (empty($sign_info['custom_poa_item'])) {
                throw new ValidationException(static::$t->_('electronic_contract_sign_error_006'), ErrCode::$VALIDATE_ERROR);
            }

            if (!in_array($params['sign_key'], array_column($sign_info['custom_poa_item'], 'sign_key'))) {
                throw new ValidationException(static::$t->_('electronic_contract_sign_error_006'), ErrCode::$VALIDATE_ERROR);
            }

            foreach ($sign_info['custom_poa_item'] as &$item) {
                if ($item['sign_key'] == $params['sign_key']) {
                    $item[$params['field_name']] = $params['field_value'];
                }
            }

            $save_data['custom_poa_item'] = json_encode($sign_info['custom_poa_item'], JSON_UNESCAPED_UNICODE);
        }
        $save_data['updated_at'] = date('Y-m-d H:i:s');

        $this->logger->info('电子合同-甲方签约-异步更新表单字段, 更新前: ' . json_encode($sign_info_model->toArray(), JSON_UNESCAPED_UNICODE));
        $this->logger->info('电子合同-甲方签约-异步更新表单字段, 待更新: ' . json_encode($save_data, JSON_UNESCAPED_UNICODE));
        if ($sign_info_model->i_update($save_data) === false) {
            throw new BusinessException('电子合同-甲方签约-异步更新表单字段异常,' . get_data_object_error_msg($sign_info_model), ErrCode::$BUSINESS_ERROR);
        }

        $this->logger->info('电子合同-甲方签约-异步更新表单字段, 更新后: ' . json_encode($sign_info_model->toArray(), JSON_UNESCAPED_UNICODE));

        return true;
    }

    /**
     * 转POA签字, 给 POA发邮箱链接
     *
     * @param string $sign_key 邮箱标识
     * @param array $sign_info 签字信息, 比如 邮箱/电子合同名称……
     * @return bool
     * @throws BusinessException
     * @throws ValidationException
     */
    public function transferPOASign(string $sign_key, array $sign_info)
    {
        // POA 验证
        $poa_info = array_column($sign_info['custom_poa_item'], null, 'sign_key')[$sign_key] ?? [];
        if (empty($poa_info['sign_email'])) {
            throw new ValidationException(static::$t->_('electronic_contract_sign_error_007'), ErrCode::$VALIDATE_ERROR);
        }

        // 邮箱链接参数
        $sign_link_params = [
            'electronic_key' => $sign_info['electronic_key'],
            'email_key'      => $sign_key,
            'department_id'  => $sign_info['department_id'],
        ];

        // 发送邮件
        $email_var = [
            'emails' => [$poa_info['sign_email']],
            'contract_name' => $sign_info['contract_name'],
            'contract_lang' => $sign_info['lang'],
            'department_id' => $sign_info['department_id'],
            'sign_link' => $this->generateEmailSignLink($sign_link_params, ContractEnums::ELECTRONIC_CONTRACT_CLIENT_OTP_POA_SIGN_LOGIN),
        ];

        $this->sendElectronicContractEmailNotice(ContractEnums::ELECTRONIC_CONTRACT_EMIAL_SCENCE_WAITING_POA_SIGN, $email_var);

        return true;
    }

    /**
     * 保存签字图片
     *
     * @param array $params 提交参数
     * @param array $sign_info 签字信息
     * @return mixed
     */
    public function saveSignImage(array $params, array $sign_info)
    {
        $code = ErrCode::$SUCCESS;
        $message = 'success';

        $db = $this->getDI()->get('db_oa');
        $db->begin();

        try {
            // 验证码校验
            $this->otpCodeCheck($sign_info['electronic_key'], $params['sign_key'], $params['otp_scence'], $params['code'], $sign_info);

            // POA签字校验和存储
            if (empty($sign_info['custom_poa_item'])) {
                throw new ValidationException(static::$t->_('electronic_contract_sign_error_008'), ErrCode::$VALIDATE_ERROR);
            }

            $custom_poa_item = array_column($sign_info['custom_poa_item'], null, 'sign_key');
            if (empty($custom_poa_item[$params['sign_key']])) {
                throw new ValidationException(static::$t->_('electronic_contract_sign_error_008'), ErrCode::$VALIDATE_ERROR);
            }

            $custom_poa_item[$params['sign_key']]['sign_img'] = $params['sign_img'];

            $save_data = [
                'custom_poa_item' => json_encode(array_values($custom_poa_item), JSON_UNESCAPED_UNICODE)
            ];

            $sign_info_model = ContractElectronicSignInfoRepository::getInstance()->getSignInfoByElectronicKey($sign_info['electronic_key']);
            $this->logger->info('电子合同-甲方签约-上传POA签名图片, 更新前: ' . json_encode($sign_info_model->toArray(), JSON_UNESCAPED_UNICODE));
            $this->logger->info('电子合同-甲方签约-上传POA签名图片, 待更新: ' . json_encode($save_data, JSON_UNESCAPED_UNICODE));

            if ($sign_info_model->i_update($save_data) === false) {
                throw new BusinessException('contract_electronic_sign_info save 失败,' . get_data_object_error_msg($sign_info_model), ErrCode::$BUSINESS_ERROR);
            }

            $this->logger->info('电子合同-甲方签约-上传POA签名图片, 更新后: ' . json_encode($sign_info_model->toArray(), JSON_UNESCAPED_UNICODE));

            // POA提交签名时, 验证POA签约是否完成
            if ($sign_info['sign_role'] == ContractEnums::ELECTRONIC_CONTRACT_SIGN_ROLE_POA) {
                $sign_img_count = count(array_filter(array_column($custom_poa_item, 'sign_img')));
                if ($sign_img_count == count($custom_poa_item)) {
                    // 邮箱链接参数
                    $sign_link_params = [
                        'electronic_key' => $sign_info_model->electronic_key,
                        'email_key'      => $sign_info_model->custom_contact_email_key,
                        'department_id'  => $sign_info['department_id'],
                    ];

                    // 发送邮件
                    $email_var = [
                        'emails' => [$sign_info_model->custom_contact_email],
                        'contract_lang' => $sign_info['lang'],
                        'department_id' => $sign_info['department_id'],
                        'sign_link' => $this->generateEmailSignLink($sign_link_params, ContractEnums::ELECTRONIC_CONTRACT_CLIENT_OTP_BUSINESS_SIGN_LOGIN),
                    ];

                    $this->sendElectronicContractEmailNotice(ContractEnums::ELECTRONIC_CONTRACT_EMIAL_SCENC_POA_SIGNED, $email_var);
                }
            }

            $db->commit();

        } catch (ValidationException $e) {
            $db->rollback();

            $code = $e->getCode();
            $message = $e->getMessage();
        } catch (BusinessException $e) {
            $db->rollback();

            $code = $e->getCode();
            $message = static::$t->_('retry_later');
            $this->logger->warning('电子合同-甲方签约-提交签字图片异常, ' . $e->getMessage());
        } catch (Exception $e) {
            $db->rollback();

            $code = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $this->logger->error('电子合同-甲方签约-提交签字图片异常, ' . $e->getMessage());
        }

        return [
            'code' => $code,
            'message' => $message
        ];
    }

    /**
     * 保存所有签字-商务提交签字
     *
     * @param array $params 提交参数
     * @param array $sign_info 签字信息
     * @return mixed
     */
    public function saveAllSign(array $params, array $sign_info)
    {
        $code = ErrCode::$SUCCESS;
        $message = 'success';

        $db = $this->getDI()->get('db_oa');
        $db->begin();

        try {
            // 验证提交的POA与系统的POA是否一致
            $submit_poa_sign_keys = array_column($params['custom_poa_item'], 'sign_key');
            $poa_sign_keys = array_column($sign_info['custom_poa_item'], 'sign_key');

            if (!empty(array_diff($submit_poa_sign_keys, $poa_sign_keys)) || !empty(array_diff($poa_sign_keys, $submit_poa_sign_keys))) {
                throw new ValidationException(static::$t->_('electronic_contract_sign_error_008'), ErrCode::$VALIDATE_ERROR);
            }

            // POA签字校验和存储
            $custom_poa_item = array_column($params['custom_poa_item'], null, 'sign_key');
            foreach ($sign_info['custom_poa_item'] as $k => $item) {
                $poa_sign_info = $custom_poa_item[$item['sign_key']];
                $sign_info['custom_poa_item'][$k]['sign_job_title'] = $poa_sign_info['sign_job_title'];
                $sign_info['custom_poa_item'][$k]['sign_img'] = $poa_sign_info['sign_img'];
            }

            $return_person_signature_img = $params['return_person_signature_img'] ?? '';

            // 更新签约信息: 初始化复核相关数据
            $save_data = [
                'pdf_page_total' => 0,
                'reviewed_page_total' => 0,
                'review_finsh_staff' => 0,
                'review_finsh_at' => null,
                'reviewer_sign_img' => '',
                'custom_company_sign_img' => $params['custom_company_sign_img'],
                'return_person_signature_img' => $return_person_signature_img,
                'custom_poa_item' => json_encode($sign_info['custom_poa_item'], JSON_UNESCAPED_UNICODE),
                'custom_sign_submited_at' => date('Y-m-d H:i:s'),
                'custom_sign_submited_ip' => get_client_real_ip(),
                'updated_at' => date('Y-m-d H:i:s')
            ];

            // 不同部门的差异处理
            $is_create_workflow = false;
            if ($this->isGroupProjectManagement($sign_info['department_id'])) {
                $sign_status = ContractEnums::CONTRACT_SIGN_STATUS_8;
            } elseif ($this->isRetailManagement($sign_info['department_id']) || $this->isFFM($sign_info['department_id'])) {
                $save_data['business_audit_status'] = Enums::WF_STATE_PENDING;
                $sign_status                        = ContractEnums::CONTRACT_SIGN_STATUS_5;
                $is_create_workflow                 = true;
            } else {
                $sign_status = ContractEnums::CONTRACT_SIGN_STATUS_3;
            }

            $sign_info_model = ContractElectronicSignInfoRepository::getInstance()->getSignInfoByElectronicKey($sign_info['electronic_key']);
            $this->logger->info('电子合同-甲方签约-商务提交签名表单, 更新前: ' . json_encode($sign_info_model->toArray(), JSON_UNESCAPED_UNICODE));
            $this->logger->info('电子合同-甲方签约-商务提交签名表单, 待更新: ' . json_encode($save_data, JSON_UNESCAPED_UNICODE));
            if ($sign_info_model->i_update($save_data) === false) {
                throw new BusinessException('contract_electronic_sign_info save 失败,' . get_data_object_error_msg($sign_info_model), ErrCode::$BUSINESS_ERROR);
            }

            $this->logger->info('电子合同-甲方签约-商务提交签名表单, 更新后: ' . json_encode($sign_info_model->toArray(), JSON_UNESCAPED_UNICODE));

            // 甲方相关资料批量处理
            $this->batchSaveRelatedFiles($sign_info_model, $params['related_file_list']);

            // 电子合同主数据
            $main_model = $sign_info_model->getElectronicContract();

            // 更新电子合同表单数据
            $form_data_model = $main_model->getFormData();
            $form_data = !empty($form_data_model->form_data) ? json_decode($form_data_model->form_data, true) : [];
            $form_data['custom_company_sign_img'] = $params['custom_company_sign_img'];
            $form_data['custom_company_sign_date'] = date('Y-m-d');
            $form_data['return_sign_picture'] = $return_person_signature_img;

            $form_data_custom_poa_item = [];
            foreach ($sign_info['custom_poa_item'] as $poa) {
                $form_data_custom_poa_item[] = [
                    'sign_name' => $poa['sign_name'],
                    'sign_job_title' => $poa['sign_job_title'],
                    'sign_img' => $poa['sign_img'],
                ];
            }

            $form_data['custom_poa_item'] = $form_data_custom_poa_item;

            $this->logger->info('电子合同-甲方商务签字完成, pdf原文件=' . gen_file_url(['object_key' => $main_model->file_url]));

            // 生成甲方签字版合同
            $pdf_file_info = $this->generateElectronicContractPdfFile($main_model->department_id, $main_model->ftl_file_url, $form_data);

            $this->logger->info('电子合同-甲方商务签字完成, pdf新文件=' . $pdf_file_info['object_url']);

            // 更新电子合同
            $model_update = [
                'file_url' => $pdf_file_info['object_key'],
                'bucket_name' => $pdf_file_info['bucket_name'],
                'sign_status' => $sign_status,
                'updated_at' => date('Y-m-d H:i:s'),
            ];
            if ($main_model->save($model_update) === false) {
                throw new BusinessException('contract_electronic save 失败,' . get_data_object_error_msg($main_model) . '; data=' . json_encode($model_update, JSON_UNESCAPED_UNICODE), ErrCode::$BUSINESS_ERROR);
            }

            // 更新电子合同表单数据
            ContractElectronicService::getInstance()->updateElectronicContractFormData($form_data_model, $form_data);

            // Retail/FFM 创建签字复核审批流
            if ($is_create_workflow) {
                if ($this->isRetailManagement($sign_info['department_id'])) {
                    $sign_info_model->flow_id = Enums::WF_CONTRACT_ELECTRONIC_RETAIL_REVIEW_SIGN_WF_ID;
                } elseif ($this->isFFM($sign_info['department_id'])) {
                    $sign_info_model->flow_id = Enums::WF_CONTRACT_ELECTRONIC_REVIEW_SIGN_WF_ID_FFM;
                }

                $created_info = UserService::getInstance()->getLoginUser($sign_info_model->created_id);
                $flow_res     = ContractElectronicFlowService::getInstance()->createRequest($sign_info_model, $created_info);
                if (!$flow_res) {
                    throw new BusinessException('Retail 签字复核审批流创建失败=' . $sign_info_model->electronic_no, ErrCode::$BUSINESS_ERROR);
                }
            }

            $db->commit();

            try {
                // PMD部门的电子合同: 给BD发复核通知
                if ($this->isGroupProjectManagement($sign_info['department_id'])) {
                    // 甲方签字完毕, 给乙方BD发送复核签字提醒
                    $created_email = (new HrStaffRepository())->getStaffById($main_model->created_id)['email'] ?? '';
                    $email_var = [
                        'emails'        => [$created_email],
                        'electronic_no' => $sign_info['electronic_no'],
                        'department_id' => $sign_info['department_id'],
                        'contract_lang' => $sign_info['lang'],
                    ];

                    $this->sendElectronicContractEmailNotice(ContractEnums::ELECTRONIC_CONTRACT_EMIAL_SCENCE_WAITING_REVIEW, $email_var);
                }

            } catch (Exception $e) {
                $this->logger->warning('电子合同-甲方签约-商务提交签名表单: 商务已成功提交, 给乙方BD发送邮件异常, message=' . $e->getMessage());
            }

        } catch (ValidationException $e) {
            $db->rollback();

            $code = $e->getCode();
            $message = $e->getMessage();
        } catch (BusinessException $e) {
            $db->rollback();

            $code = $e->getCode();
            $message = static::$t->_('retry_later');
            $this->logger->warning('电子合同-甲方签约-商务提交签字异常, ' . $e->getMessage());
        } catch (Exception $e) {
            $db->rollback();

            $code = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $this->logger->error('电子合同-甲方签约-商务提交签字异常, ' . $e->getMessage());
        }

        return [
            'code' => $code,
            'message' => $message
        ];
    }

    /**
     * 批量存储商务提交的相关资料
     *
     * @param object $sign_info_model
     * @param array $related_file_list
     * @return bool
     * @throws BusinessException
     */
    protected function batchSaveRelatedFiles(object $sign_info_model, array $related_file_list)
    {
        if (empty($related_file_list)) {
            return true;
        }

        // 甲方上传的相关资料处理
        $exist_related_file_models = $sign_info_model->getRelatedFileList();
        $this->logger->info('电子合同-甲方签约-商务提交签名表单, 相关资料已有数据: ' . json_encode($exist_related_file_models->toArray(), JSON_UNESCAPED_UNICODE));

        // 删除已有的资料
        if ($exist_related_file_models->delete() === false) {
            throw new BusinessException('甲方相关资料已有数据删除失败,' . get_data_object_error_msg($exist_related_file_models), ErrCode::$BUSINESS_ERROR);
        }

        // 写入终版的资料
        $attachment_data = [];
        foreach ($related_file_list as $file) {
            $attachment_data[] = [
                'oss_bucket_type' => Enums::OSS_BUCKET_TYPE_ELECTRONIC_CONTRACT_CLIENT_RELATED_FILE,
                'sub_type' => 0,
                'oss_bucket_key' => $sign_info_model->id,
                'bucket_name' => $file['bucket_name'],
                'object_key' => $file['object_key'],
                'file_name' => $file['file_name'],
            ];
        }

        $this->logger->info('电子合同-甲方签约-商务提交签名表单, 相关资料新数据: ' . json_encode($attachment_data, JSON_UNESCAPED_UNICODE));

        $attachment_model = new SysAttachmentModel();
        if (!empty($attachment_data) && $attachment_model->batch_insert($attachment_data) === false) {
            throw new BusinessException('甲方相关资料批量写入失败', ErrCode::$BUSINESS_ERROR);
        }

        return true;
    }

}
