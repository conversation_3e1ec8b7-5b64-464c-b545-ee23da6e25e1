<?php
/**
 * Created by PhpStorm.
 * Date: 2023/7/13
 * Time: 16:27
 */

namespace App\Modules\Contract\Services;

use App\Library\Enums\ContractEnums;
use App\Library\Enums\GlobalEnums;
use App\Library\ErrCode;
use App\Library\Exception\BusinessException;
use App\Library\Validation\ValidationException;
use App\Models\oa\ContractSubFileModel;
use App\Models\oa\ContractSubFileRelateModel;
use App\Modules\Common\Services\EnumsService;
use Exception;
use GuzzleHttp\Exception\GuzzleException;

class ContractSubFileService extends BaseService
{

    private static $instance;

    public function __construct()
    {
    }

    private function __clone()
    {
    }

    public static function getInstance()
    {
        if (!isset(self::$instance) || !(self::$instance instanceof self)) {
            self::$instance = new self;
        }
        return self::$instance;
    }


    public static $validate_list_search = [
        'pageSize' => 'Required|IntGt:0',                  //每页条数
        'pageNum'  => 'Required|IntGt:0'                  //页码
    ];

    public static $validate_edit = [
        'id'              => 'Required|IntGe:1',
        'activation_time' => 'Required|Date',                  //生效开始时间
        'outage_time'     => 'Required|Date',                    //生效结束时间
        'contract_type'   => 'Required|Arr', //适用合同类型
        'file_name'       => 'Required|StrLenGeLe:1,200',
        'ver_no'          => 'Required|StrLenGeLe:1,100',
        'department_id'   => 'Required|Arr',
        'department_name' => 'Required|Arr',
    ];

    public static function validateEdit()
    {
        $validate_edit = static::$validate_edit;
        $contract_enums = ContractSubFileService::getInstance()->getElectronicContractEnums();
        $subfile_type = implode(',', $contract_enums['subfile_type']);
        $validate_edit['file_type'] = 'Required|IntIn:' . $subfile_type;
        return $validate_edit;
    }


    public function edit($data, $user)
    {
        $code    = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $db      = $this->getDI()->get('db_oa');
        $db->begin();
        try {
            $exists = ContractSubFileModel::findFirst([
                'conditions' => 'id = :id:',
                'bind'       => ['id' => $data['id']],
            ]);

            // 子文件不存在
            if (empty($exists)) {
                throw new ValidationException(static::$t->_('contract_sub_file_get_fail'), ErrCode::$VALIDATE_ERROR);
            }

            //判断子文件状态
            $state = ContractEnums::SUB_FILE_STATUS_1;
            if (date('Y-m-d H:i:s') > $data['outage_time'] . ' 23:59:59') {
                $state = ContractEnums::SUB_FILE_STATUS_2;
            }

            if (date('Y-m-d H:i:s') < $data['activation_time'] . ' 00:00:01') {
                $state = ContractEnums::SUB_FILE_STATUS_3;
            }

            if (date('Y-m-d H:i:s') < $data['outage_time'] . ' 23:59:59' && date('Y-m-d H:i:s') > $data['activation_time'] . ' 00:00:01') {
                $state = ContractEnums::SUB_FILE_STATUS_1;
            }

            $exists->file_type       = $data['file_type'];
            $exists->file_name       = $data['file_name'];
            $exists->ver_no          = $data['ver_no'];
            $exists->state           = $state;
            $exists->department_id   = implode(',', $data['department_id']);
            $exists->department_name = implode(',', $data['department_name']);
            $exists->activation_time = $data['activation_time'] . ' 00:00:00';
            $exists->outage_time     = $data['outage_time'] . ' 23:59:59';
            $exists->updated_id      = $user['id'];
            $exists->updated_at      = date('Y-m-d H:i:s');

            if ($exists->save() === false) {
                throw new BusinessException('子文件修改失败, 原因可能是: ' . get_data_object_error_msg($exists) . '; 数据: ' . json_encode($exists->toArray(), JSON_UNESCAPED_UNICODE), ErrCode::$CONTRACT_CREATE_ERROR);
            }

            //删除和contract_type 映射关系 新建新的关系
            $relate = ContractSubFileRelateModel::find([
                'conditions' => 'relate_unique_no = :relate_unique_no:',
                'bind'       => ['relate_unique_no' => $exists->unique_no],
            ]);

            if ($relate->delete() === false) {
                throw new BusinessException('子文件修改删除对应关系失败, 原因可能是: ' . get_data_object_error_msg($relate) . '; 数据: ' . json_encode($relate->toArray(), JSON_UNESCAPED_UNICODE), ErrCode::$CONTRACT_CREATE_ERROR);
            }

            $insert_data = [];
            foreach ($data['contract_type'] as $value) {
                $insert_data[] = [
                    'relate_unique_no' => $exists->unique_no,
                    'contract_type'    => $value
                ];
            }

            $relate_model = new ContractSubFileRelateModel();
            if ($relate_model->batch_insert($insert_data) === false) {
                throw new BusinessException('pmd子文件修改对应关系入库失败, 数据: ' . json_encode($insert_data, JSON_UNESCAPED_UNICODE), ErrCode::$CONTRACT_CREATE_ERROR);
            }

            $db->commit();
        } catch (ValidationException $e) {
            // 校验错误，可对外抛出
            $code    = $e->getCode();
            $message = $e->getMessage();

        } catch (BusinessException $e) {
            // 业务错误（数据错误等），不可对外抛出
            $code         = $e->getCode();
            $message      = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        } catch (Exception $e) {
            // 业务错误（数据错误等），不可对外抛出
            $code         = $e->getCode();
            $message      = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        }
        if (!empty($message)) {
            $db->rollback();
        }
        if (!empty($real_message)) {
            $this->logger->warning('sub-file-contract-update-failed:' . $real_message);
        }

        return [
            'code'    => $code,
            'message' => $message
        ];

    }


    /**
     * @param $condition
     * @return array
     */
    public function getList($condition)
    {
        $file_type     = $condition['file_type'] ?? '';
        $department_id = $condition['department_id'] ?? '';
        $contract_type = $condition['contract_type'] ?? '';
        $state         = $condition['state'] ?? 0;
        $start_time    = $condition['start_time'] ?? '';
        $end_time      = $condition['end_time'] ?? '';

        $page_size = empty($condition['pageSize']) ? GlobalEnums::DEFAULT_PAGE_SIZE : $condition['pageSize'];
        $page_num  = empty($condition['pageNum']) ? GlobalEnums::DEFAULT_PAGE_NUM : $condition['pageNum'];
        $offset    = $page_size * ($page_num - 1);
        $builder   = $this->modelsManager->createBuilder();
        $builder->from(['c' => ContractSubFileModel::class]);
        $builder->leftjoin(ContractSubFileRelateModel::class, 'c.unique_no = r.relate_unique_no', 'r');

        if (!empty($file_type)) {
            $builder->andWhere('c.file_type = :file_type:', ['file_type' => $file_type]);
        }

        if (!empty($department_id)) {
            $builder->andWhere('FIND_IN_SET(:department_ids:,c.department_id)', ['department_ids' => $department_id]);
        }

        if (!empty($contract_type)) {
            $builder->andWhere('r.contract_type = :contract_type:', ['contract_type' => $contract_type]);
        }

        if (!empty($state)) {
            $builder->andWhere('c.state = :state:', ['state' => $state]);
        }

        if (!empty($start_time)) {
            $builder->andWhere('c.activation_time <= :activation_time:', ['activation_time' => $start_time . ' 00:00:01']);
        }

        if (!empty($end_time)) {
            $builder->andWhere('c.outage_time >= :outage_time:', ['outage_time' => $end_time . ' 23:59:59']);
        }
        $count = (int)$builder->columns('COUNT(DISTINCT c.id) AS total')->getQuery()->getSingleResult()->total;
        if ($count > 0) {
            $builder->groupBy('c.id');

            $builder->columns('distinct c.id,group_concat(r.contract_type) contract_type,file_name,file_type,unique_no,ver_no,lang,department_name,activation_time,outage_time,state,file_url');
            $builder->limit($page_size, $offset);
            $items = $builder->getQuery()->execute()->toArray();

            $items = $this->handelItems($items);
        }

        return [
            'items'      => $items ?? [],
            'pagination' => [
                'current_page' => $page_num,
                'per_page'     => $page_size,
                'total_count'  => $count,
            ]
        ];
    }


    public function getDetail($params)
    {
        $code    = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $id      = $params['id'];

        try {
            $contract = ContractSubFileModel::findFirst([
                'conditions' => 'id =:id:',
                'bind'       => ['id' => $id]
            ]);
            if (empty($contract)) {
                throw new ValidationException(static::$t->_('contract_data_not_exist'), ErrCode::$CONTRACT_GET_INFO_ERROR);
            }

            $contract_types = $contract->getContractTypes()->toArray();
            $contract = $contract->toArray();

            $contract['contract_type'] = array_column($contract_types, 'contract_type');
            $contract['department_id'] = explode(',', $contract['department_id']);
            unset($contract['file_form_rule']);
            unset($contract['contract_content']);

        } catch (ValidationException $e) {
            $code    = $e->getCode();
            $message = $e->getMessage();
        }

        return [
            'code'    => $code,
            'message' => $message,
            'data'    => $contract ?? []
        ];
    }


    public function handelItems($items)
    {
        $enums               = $this->getEnums();
        $subfile_type_enums  = $enums['subfile_type'];
        $contract_type_enums = $enums['contract_type'] ?? [];
        $subfile_type_enums  = array_column($subfile_type_enums, 'name', 'id');
        $contract_type_enums = array_column($contract_type_enums, 'name', 'id');
        $state               = ContractEnums::$contract_sub_file_status;

        foreach ($items as &$item) {
            $item['contract_type'] = explode(',', $item['contract_type']);
            foreach ($item['contract_type'] as $value) {
                $item['contract_type_text'][] = $contract_type_enums[$value] ?? '';
            }
            $item['file_name']          = $subfile_type_enums[$item['file_type']] ?? $item['file_name'];
            $item['contract_type_text'] = implode(',', $item['contract_type_text']);
            $item['state_text']         = static::$t->_($state[$item['state']]);

        }
        return $items;
    }

    public function fileList($data)
    {
        $builder = $this->modelsManager->createBuilder();
        $builder->from(['c' => ContractSubFileModel::class]);
        $builder->leftjoin(ContractSubFileRelateModel::class, 'c.unique_no = r.relate_unique_no', 'r');
        $builder->andWhere('r.contract_type = :contract_type:', ['contract_type' => $data['contract_type']]);
        $builder->andWhere('FIND_IN_SET(:department_ids:,c.department_id)', ['department_ids' => $data['department_id']]);
        $builder->andWhere('c.state = :state:', ['state' => ContractEnums::SUB_FILE_STATUS_1]);
        $builder->columns('distinct c.id,c.file_name,c.file_type,c.unique_no');
        $builder->groupBy('c.file_type');
        $file_list = $builder->getQuery()->execute()->toArray();
        return $file_list ?? [];
    }

    /**
     * 更新条款 - 查看
     * @param array $params 请求参数组
     * @return array
     */
    public function getUpdateDetail($params)
    {
        $code = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $data = [];
        try {
            $contract = ContractSubFileModel::findFirst([
                'conditions' => 'id = :id:',
                'bind' => ['id' => $params['id']],
            ]);
            if (empty($contract)) {
                throw new ValidationException(static::$t->_('contract_data_not_exist'), ErrCode::$CONTRACT_GET_INFO_ERROR);
            }
            //获取头、尾、样式
            $data = $this->getElectronicContractHtmlConfig($contract->department_id); //更新条款
            $data['id'] = $contract->id;
            $data['file_type'] = $contract->file_type;
            $data['lang'] = $contract->lang;
            $data['unique_no'] = $contract->unique_no;
            $data['contract_content'] = $contract->contract_content;
            //文件类型
            $contract_enums = EnumsService::getInstance()->getSettingEnvValueMap('pmd_contract_enums');
            $subfile_type = array_column($contract_enums['subfile_type'], 'name', 'id');
            $data['file_type_text'] = static::$t->_($subfile_type[$data['file_type']]);
            unset($data['base_form'], $data['base_form_rule_lang']);
        } catch (ValidationException $e) {
            $code    = $e->getCode();
            $message = $e->getMessage();
        } catch (BusinessException $e) {
            $code    = $e->getCode();
            $message = static::$t->_('retry_later');
            $this->logger->warning('电子合同-子文件编辑-详情' . $e->getMessage());
        } catch (Exception $e) {
            $code    = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $this->logger->error('电子合同-子文件编辑-详情' . $e->getMessage());
        }

        return [
            'code' => $code,
            'message' => $message,
            'data' => $data,
        ];
    }

    /**
     * 更新条款 - 保存
     * @param array $data 请求参数组
     * @param array $user 当前登陆者信息组
     * @return array
     * @throws GuzzleException
     */
    public function update($data, $user)
    {
        $code = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $db = $this->getDI()->get('db_oa');
        $db->begin();
        try {
            $sub_file_info = ContractSubFileModel::findFirst([
                'conditions' => 'id = :id:',
                'bind' => ['id' => $data['id']],
            ]);

            // 子文件不存在
            if (empty($sub_file_info)) {
                throw new ValidationException(static::$t->_('contract_sub_file_get_fail'), ErrCode::$VALIDATE_ERROR);
            }

            //只能是使用中
            if ($sub_file_info->state != ContractEnums::SUB_FILE_STATUS_1) {
                throw new ValidationException(static::$t->_('contract_sub_file_non_use'), ErrCode::$VALIDATE_ERROR);
            }

            $sub_file_info->updated_id = $user['id'];
            $sub_file_info->contract_content = $data['contract_content'];
            if ($sub_file_info->save() === false) {
                throw new BusinessException('子文件更新条款 - 保存内容失败, 原因可能是: ' . get_data_object_error_msg($sub_file_info) . '; 数据: ' . json_encode($sub_file_info->toArray(), JSON_UNESCAPED_UNICODE), ErrCode::$CONTRACT_CREATE_ERROR);
            }

            // 模板处理
            $pdf_template_data = [
                'subfile_info' => [['unique_no' => $sub_file_info->unique_no]],
                'department_id' => $sub_file_info->department_id,
                'ver' => $sub_file_info->ver_no,
                'lang' => $sub_file_info->lang
            ];

            if ($this->isFlashHomeOperationV2($sub_file_info->department_id)) {
                $template_data = ContractTemplateVersionService::getInstance()->handleFhTemplateData($pdf_template_data);
            } else {
                if (isCountry('MY')) {
                    $service       = reBuildCountryInstance(new ContractTemplateVersionService());
                    $template_data = $service->handleTemplateData($pdf_template_data);//更新条款
                } else {
                    $template_data = ContractTemplateVersionService::getInstance()->handleTemplateData($pdf_template_data);//更新条款
                }
            }


            $sub_file_info->file_url = gen_file_url(['object_key' => $template_data['pdf_url']]);
            $sub_file_info->updated_at = date('Y-m-d H:i:s');
            if ($sub_file_info->save() === false) {
                throw new BusinessException('子文件更新条款 - 保存pdf地址失败, 原因可能是: ' . get_data_object_error_msg($sub_file_info) . '; 数据: ' . json_encode($sub_file_info->toArray(), JSON_UNESCAPED_UNICODE), ErrCode::$CONTRACT_CREATE_ERROR);
            }
            $db->commit();
        } catch (ValidationException $e) {
            // 校验错误，可对外抛出
            $code = $e->getCode();
            $message = $e->getMessage();
        } catch (BusinessException $e) {
            // 业务错误（数据错误等），不可对外抛出
            $code = $e->getCode();
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        } catch (Exception $e) {
            // 业务错误（数据错误等），不可对外抛出
            $code = $e->getCode();
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        }
        if (!empty($real_message)) {
            $this->logger->warning('sub-file-contract-update-save-failed:' . $real_message);
        }

        if (!empty($message)) {
            $db->rollback();
        }
        return [
            'code' => $code,
            'message' => $message,
            'data' => []
        ];
    }

}