<?php

/** @noinspection PhpUnhandledExceptionInspection */

/**
 *StoreRentingContractService.php
 * Created by: Lqz.
 * Description:
 * User: Administrator
 * CreateTime: 2020/9/9 0009 20:51
 */

namespace App\Modules\Contract\Services;

use App\Library\Enums;
use App\Library\Enums\GlobalEnums;
use App\Library\Enums\ContractEnums;
use App\Library\Enums\WarehouseEnums;
use App\Library\ErrCode;
use App\Library\Exception\BusinessException;
use App\Library\OssHelper;
use App\Library\Validation\Validation;
use App\Library\Validation\ValidationException;
use App\Models\oa\ContractArchiveEditLogNewModel;
use App\Models\oa\ContractStoreRentingRemindModel;
use App\Models\oa\SettingEnvModel;
use App\Models\oa\SysAttachmentModel;
use App\Modules\Common\Models\EnvModel;
use App\Modules\Common\Services\AttachmentService;
use App\Modules\Common\Services\EnumsService;
use App\Modules\Common\Services\StoreService;
use App\Modules\Common\Services\WaterMarkerService;
use App\Modules\Contract\Models\Contract;
use App\Modules\Contract\Models\ContractArchive;
use App\Modules\Contract\Models\ContractArchiveEditLogModel;
use App\Modules\Contract\Models\ContractStoreRentingDetailModel;
use App\Modules\Contract\Models\ContractStoreRentingModel;
use App\Modules\Contract\Models\ContractStoreRentingMoneyModel;
use App\Modules\Contract\Models\HrStaffInfoModel;
use App\Modules\Contract\Models\SysCityModel;
use App\Modules\Contract\Models\SysDepartmentModel;
use App\Modules\Contract\Models\SysDistrictModel;
use App\Modules\Contract\Models\SysManageRegionModel;
use App\Modules\Contract\Models\SysProvinceModel;
use App\Modules\Contract\Models\SysStoreModel;
use App\Modules\OrdinaryPayment\Models\OrdinaryPayment;
use App\Modules\OrdinaryPayment\Models\OrdinaryPaymentDetail;
use App\Modules\Organization\Services\DepartmentService;
use App\Modules\Contract\Models\ContractStoreRentingArea;
use App\Modules\Payment\Models\PaymentStoreRenting;
use App\Modules\Payment\Models\PaymentStoreRentingDetail;
use App\Modules\Purchase\Services\BaseService as PurchaseService;
use App\Modules\Setting\Services\CommonDataPermissionService;
use App\Modules\User\Models\AttachModel;
use App\Modules\User\Models\DepartmentModel;
use App\Modules\User\Models\StaffInfoModel;
use App\Modules\User\Services\UserService;
use App\Modules\Warehouse\Services\ThreadService;
use App\Modules\Warehouse\Services\WarehouseService;
use App\Modules\Workflow\Models\WorkflowNodeModel;
use App\Modules\Workflow\Models\WorkflowRequestModel;
use App\Modules\Workflow\Models\WorkflowRequestNodeAt;
use App\Modules\Workflow\Models\WorkflowRequestNodeAuditorModel;
use App\Modules\Workflow\Models\WorkflowRequestNodeFYR;
use App\Modules\Workflow\Models\WorkflowSubNodeModel;
use App\Modules\Workflow\Models\WorkflowUpdateLogModel;
use App\Modules\Workflow\Services\FYRService;
use App\Modules\Workflow\Services\WorkflowServiceV2;
use App\Modules\User\Services\StaffService;
use App\Models\oa\ContractStoreRentingEditLogModel;
use App\Repository\HrStaffRepository;
use App\Repository\oa\SysAttachmentRepository;
use App\Repository\StoreRepository;
use Exception;
use GuzzleHttp\Exception\GuzzleException;
use Phalcon\Mvc\Model\Resultset;
use Phalcon\Mvc\Phalcon\Mvc\Model;


class ContractStoreRentingService extends BaseService
{
    private static $instance;

    public static function getInstance()
    {
        if (!isset(self::$instance) || !(self::$instance instanceof self)) {
            self::$instance = new self;
        }
        return self::$instance;
    }

    public static $validate_archive = [
        'archive_id'                       => 'Required|IntGe:1',                                        //合同归档ID
        'holder_name'                      => 'Required|StrLenGeLe:1,30',                       //纸质合同保管人
    ];

    public static $validate_contract = [
        'manage_id'                   => 'Required|Int',  // 管理者id
        // 合同属性
        'is_main'                     => 'Required|IntIn:' . Enums::CONTRACT_TYPES, // 合同属性  （新）
//        'main_contract_id'            => 'Str',// 主合同号（新）
        'contract_lang'               => 'Required|Int', // 合同语言
        //网点信息
        'store_cate'                  => 'Required',        //网点类型（新）
        'store_id'                    => 'Required|Str',   // 网点编号
        'lon_lat'                     => 'Required',         // {经度},{维度}
        'store_addr'                  => 'Required|StrLenGeLe:0,500',    // 网点地址
        //房屋信息
        'house_owner_name'            => 'Required|StrLenGeLe:0,300',    //房东姓名
        'house_contract_area'         => 'Required|FloatGeLe:0,**********',  // 房屋合约面积
        'house_actual_area'           => 'Required|FloatGeLe:0,**********',   //房屋实际面积
        'house_owner_type'            => 'Required|IntIn:' . ContractEnums::HOUSE_OWNER_TYPES, //房东类型
        'house_equip_list'            => 'Required|StrLenGeLe:0,500',     //房屋设备清单
        'land_type_content'           => 'StrLenGeLe:0,50',     //地契类型自定义内容
        'leaser_type_content'         => 'StrLenGeLe:0,100',     //出租人类型自定义内容
        'rent_due_date'               => 'Required|IntGeLe:1,31',  //房租应付日
        'cost_company_id'             => 'Required|Str',
        //合同信息

//        'contract_id'                 => 'Required|StrLenGeLe:0,50', //合同编号
        'contract_begin'              => 'Required|Date', // 合同开始日期
        'contract_end'                => 'Required|Date',  // 合同结束日期
        'is_rent_free'                => 'Required|IntIn:1,2|>>>: is rent free err', //是否有免租期1有 2无
        'rent_free_time'              => 'IfStrEq:is_rent_free,1|Required|Arr',         //免租期
        'rent_free_time[*]'           => 'IfStrEq:is_rent_free,1|Required|Date',

        'contract_effect_date'        => 'IfIntIn:is_main,1,2|Required|Date',  // 合同生效日期
        'contract_lease_type'         => 'Required|IntIn:1,2,3,4,5|>>>: contract lease type err',         //合同租期类型(1.月租,2.年租)

//        'contract_benefits'      => 'FloatGeLe:0,**********', //合同保证金
        'money_symbol'                => 'Required|IntIn:'.GlobalEnums::VALIDATE_CURRENCY_PARAMS.'|>>>: money  type err',                  // 货币符号 （新）
        'deposit_amount'              => 'Required|FloatGeLe:0,**********',  // 合同押金
//        'monthly_payment_type'   => 'Required|Int',         //每月付房租的日期
        'contract_signer_name'        => 'Required|StrLenGeLe:0,500', // 合同签署人姓名
        //付款信息:
        'bank_collection'             => 'Required|Arr',   //付款信息 集合
        'bank_collection[*].sap_supplier_no' => 'StrLenGeLe:0,50',   //付款信息 集合

        //税费信息
        'property_tax'                => 'Str',         //房产税付款人
        'duty_stamp'                  => 'Str',          //印花税付款人

        // 附件列表
        'attachment_list'             => 'Required|Obj',

        // 合同信息: 明细必填
        'amount_detail'               => 'Required|Arr|ArrLenGe:1|>>>:params error[amount_detail]',

        // 税费信息：明细必填
        'areaInfo'                    => 'IfIntIn:is_main,1,2|Required|Arr|ArrLenGe:1|>>>:params error[areaInfo]',

        //  增加字段   合同信息
        'contract_total_amount'               => 'IfIntIn:is_main,1,2|Required|FloatGeLe:0,**********',  //合同总金额

        'billboard_tax_payer'                  => 'Str',
        'amount_deposit'                      => 'FloatGeLe:0,**********', //定金
        //新增
        'land_tax_payer'              => 'Str',          //土地税付款人
        'fire_insurance_payer'        => 'Str',          //火灾保险费付款人
        'antimoth_payer'              => 'Str',          //房屋杀虫防蛀费付款人
    ];

    // 非LOI校验条件
    public static $validate_not_loi_contract_list = [
        'provinces'                   => 'Required',        // 省份大区
        'contract_name'               => 'Required|StrLenGeLe:0,500', //合同名称
        'signer_phone'                => 'Required|StrLenGeLe:0,50', //签署人联系电话
        'full_refund_conditions'      => 'Required|StrLenGeLe:0,500', // 押金全退的条件
        'contract_remarks'            => 'Required|StrLenGeLe:0,500', //合同备注
        'hourse_owner_addr'           => 'IfIntIn:is_main,1,2|Required|StrLenGeLe:1,1000',     //房东收件地址
        'total_amount_monthly'        => 'IfIntIn:is_main,1,2|Required|FloatGeLe:0,**********',         // 房产税金额
        'duty_stamp'                  => 'Str',          //印花税付款人
        'total_amount'                => 'IfIntIn:is_main,1,2|IfIntEq:money_symbol,'.Enums\GlobalEnums::CURRENCY_PHP.'|Required|FloatGeLe:0,**********',  //印花税金额
        'signboard_tax'               => 'IfIntIn:is_main,1,2|Required|FloatGeLe:0,**********',  //广告税额
        'land_tax_amount'             => 'IfIntIn:is_main,1,2|Required|FloatGeLe:0,**********',  //土地税金额
        'fire_insurance_amount'       => 'IfIntIn:is_main,1,2|Required|FloatGeLe:0,**********',  //火灾保险费金额
        'antimoth_amount'             => 'IfIntIn:is_main,1,2|Required|FloatGeLe:0,**********',  //房屋杀虫防蛀费金额
    ];

    // 合同租金信息
    public static $validate_contract_money_list = [
        'label'  => 'Required|StrLenGeLe:1,100',         // 合同租金金额期间
        'amount' => 'Required|FloatGeLe:0,**********', // 合同 租金
    ];

    // 合同租金信息
    public static $validate_contract_money_list_not_required = [
        'label'  => 'StrLenGeLe:0,100',         // 合同租金金额期间
        'amount' => 'StrLenGeLe:0,10',          // 合同 租金，有可能为空
    ];

    // 未归档信息校验
    public static $validate_not_achieve_collection = [
        'lon_lat'                     => 'Required|Str', // {经度},{维度}
        'contract_effect_date'        => 'IfIntIn:is_main,1,2|Required|Date',  // 合同生效日期

        //合同附件
        'attachment_list'                => 'Obj'
    ];

    // 已归档信息校验
    public static $validate_achieve_collection = [
        //网点信息
        'store_cate'                  => 'Required',        //网点类型（新）
        'store_id'                    => 'Required|Str',   // 网点编号
        'lon_lat'                     => 'Required|Str', // {经度},{维度}
        'provinces'                   => 'IfIntIn:store_cate,-1|Required',
        'store_addr'                  => 'Required|StrLenGeLe:0,500',    // 网点地址
        // 付款
        'hourse_owner_addr'           => 'Required',
        'bank_collection'             => 'Required|Arr',

        // 附件
        'attachment_list'                => 'Obj'
    ];

    // 收款信息
    public static $validate_bank_collection = [
        'pay_type'          => 'Required|StrIn:1,2',
        'bank_name'         => 'Str',    //银行名称
        'bank_account_name' => 'Str',                    //银行账户名称
        'bank_account'      => 'Str',                         // 银行账号
        'contact_code'      => 'Required|Str',                      //联系人证件号
        'contact_mobile'    => 'Required',                        // 联系人电话
        // 'contact_emial'     => 'Required|Regexp:/^[a-zA-Z0-9_-]+@[a-zA-Z0-9_-]+(\.[a-zA-Z0-9_-]+)+$/|>>>:Email error',                          //联系人邮箱
       // 'pay_amount'        => 'Required|FloatGeLe:0,**********'        // 汇款金额

    ];

    public static $cancel_validate = [
        'cancel_reason' => 'Required|StrLenGeLe:1,300',
        'id'            => 'Required|Int',
    ];

    public static $approve_validate = [
        'remark' => 'Required|StrLenGeLe:0,300',
        'id'     => 'Required|Int',
        'update_data.bank_collection' => 'Arr'
    ];
    public static $reject_validate = [
        'remark' => 'Required|StrLenGeLe:1,300',
        'id'     => 'Required|Int',
    ];
    public static $invalid_validate = [
        'remark' => 'Required|StrLenGeLe:1,300',
        'id'     => 'Required|Int',
        // 附件
        'pdf_noneed_file'                => 'ArrLenGeLe:0,5',
        'pdf_noneed_file[*].file_name'   => 'StrLenGeLe:0,300',
        'pdf_noneed_file[*].bucket_name' => 'StrLenGeLe:0,300',
        'pdf_noneed_file[*].object_key'  => 'StrLenGeLe:0,300',
    ];
    public static $terminal_validate = [
        'id'                => 'Required|Int',
        'terminal_at'       => 'Required|Date',
        'terminal_reason'   => 'Required|StrLenGeLe:1,300'
    ];
    public static $get_relate_mainList = [
        'is_main'     => 'Required|StrEq:1',
    ];

    public static $get_relate_contract = [
        'contract_id'  => 'Required|StrLenGeLe:0,50', //合同编号
    ];

    public static $get_relate_store = [
        'id' => 'Int|>>>:params error[id]', //合同ID
        'store_id' => 'Required|StrLenGeLe:1,15|>>>:params error[store_id]', //网点编号
        'warehouse_id' => 'StrLenGeLe:0,15|>>>:params error[warehouse_id]', //仓库ID
        'start_date' => 'Required|Date|>>>:params error[start_date]', //合同开始日期
        'end_date' => 'Required|Date|>>>:params error[end_date]' //合同结束日期
    ];

    public static $get_landlord_type = [
        'house_owner_type'  => 'Required|IntIn:1,2,3', //房东类型传参
    ];

    public static $ph_contract_validate = [
        'contract_deadline' => 'Required|StrLenGeLe:0,50', //合同期限
        'contract_tax_no'   => 'IfIntIn:is_main,1,2|Required|StrLenGeLe:1,100',//房东税号TIN
        'due_date'          => '', //应付日期
        'amount_paid'       => 'IfIntIn:is_main,1,2|Required|FloatGeLe:0,**********',
        'contract_total_amount_contain_wht' => 'IfIntIn:is_main,1,2|Required|FloatGtLe:0,**********.99',
    ];

    public static $amount_detail_validate = [
        'cost_start_date'     => 'Required|Date|>>>:cost start date error', //费用发生期间-开始日期
        'cost_end_date'       => 'Required|Date|>>>:cost end date error', //费用发生期间-结束日期
        'amount_no_tax'       => 'Required|FloatGeLt:0,1000000000|>>>:amount no tax value error', //不含税金额
        'wht_category' => 'Required|IntGe:0|>>>:WHT category error',
        'vat_rate'           => 'Required|FloatGe:0',
        'amount_vat'          => 'Required|FloatGeLt:0,1000000000|>>>:amount vat value error', //VAT7%金额  税金额
        'amount_has_tax'      =>	'Required|FloatGeLt:0,1000000000|>>>:amount has tax value error',//含税金额
        'wht_rate'            => 'Required|FloatGe:0|>>>:WHT rate error2', //WHT 税率
        'amount_wht'            => 'Required|FloatGeLt:0,**********|>>>: amount wht  error', //WHT 金额
    ];

    public static $area_info_validate = [
        'area_start_time'     => 'Required|Date|>>>:area start date error', //费用发生期间-开始日期
        'area_end_time'     => 'Required|Date|>>>:area end date error', //费用发生期间-开始日期
        'area_service_amount_no_tax'       => 'Required|FloatGeLt:0,1000000000|>>>: area amount no tax value error', //不含税金额
        'area_vat_rate'           => 'Required|FloatGeLe:0,100',
        'area_service_amount_vat'          => 'Required|FloatGeLt:0,1000000000|>>>:area amount vat value error', //VAT7%金额  税金额
        'area_wht_category' => 'Required|IntGe:0|>>>: area WHT category error',
        'area_wht_rate'            => 'Required|FloatGe:0|>>>: area WHT rate error1', //WHT 税率
        'area_amount_wht'            => 'Required|FloatGeLe:0,**********|>>>: area WHT rate error',
    ];

    public static $get_replace_cno_validate = [
        'cno' => 'Required|StrLenGe:7',
    ];


    public static $validate_transfer_param = [
        'id' => 'Required|IntGe:1',                                  //合同ID
        'contract_leader_id' =>'Required|IntGe:1'
    ];

    public static $validate_batch_transfer_param = [
        'store_id'  => 'Required|ArrLenGeLe:1,1000', //网点编号
        'contract_leader_id' =>'Required|IntGe:1'
    ];

    // 计算月每平方米租金（含WHT含VAT）
    public static $calculate_month_rent_has_wht_validate = [
        'contract_total_amount' => 'Required|FloatGeLe:0,**********.99', //合同总金额（含WHT含VAT）
        'house_actual_area'   => 'Required|FloatGeLe:0,**********', //房屋实际面积
        'contract_begin'      => 'Required|Date', //合同开始日期
        'contract_end'        => 'Required|Date', //合同结束日期
    ];

    /**
     * 获取非LOI校验条件
     * @return string[]
     */
    public function getValidateNotLoiContractList()
    {
        $validate = self::$validate_not_loi_contract_list;

        //V22497 针对泰国对房产税金额、土地税金额、火灾保险费金额拦截逻辑做移除操作，不再存储
        if (get_country_code() == GlobalEnums::TH_COUNTRY_CODE) {
            unset($validate['total_amount_monthly'], $validate['land_tax_amount'], $validate['fire_insurance_amount']);
        }
        return $validate;
    }

    /**
     * 获取仅针对泰国的验证规则
     * @param array $params 参数组
     * @param array $validateContract 验证规则组
     * @return array
     */
    public function getValidateThailand(array $params, array $validateContract): array
    {
        if (get_country_code() != GlobalEnums::TH_COUNTRY_CODE) {
            return $validateContract;
        }

        // 地契类型
        if (!isset($params['land_type']) || !in_array($params['land_type'], array_values(array_keys(ContractEnums::$land_type_item)))) {
            $validateContract['land_type'] = 'Required|Arr'; //地契数组类型
            $validateContract['land_type[*]'] = 'IntIn:' . implode(',', array_keys(ContractEnums::$land_type_item)); //地契类型值
        }

        // 出租人
        if (!isset($params['leaser_type']) || !in_array($params['leaser_type'], array_values(array_keys(ContractEnums::$leaser_type_item)))) {
            $validateContract['leaser_type'] = 'Required|IntIn:' . implode(',', array_keys(ContractEnums::$leaser_type_item)); //出租人类型
        }
        //V22497 增加如下参数校验
        $validateContract['withholding_tax_liability_bearer'] = 'Required|IntIn:' . implode(',', array_keys(WarehouseEnums::$thread_withholding_tax_liability_bearer)) .'|>>>:' . static::$t->_('params_error', ['param' => 'withholding_tax_liability_bearer']);//承担扣缴税款责任方
        $validateContract['water_bill_payment_type']          = 'Required|IntIn:' . implode(',', array_keys(WarehouseEnums::$thread_water_bill_payment_type)) . '|>>>:' . static::$t->_('params_error', ['param' => 'water_bill_payment_type']);         //水费支付类型
        $validateContract['electricity_bill_payment_type']    = 'Required|IntIn:' . implode(',', array_keys(WarehouseEnums::$thread_electricity_bill_payment_type)) . '|>>>:' . static::$t->_('params_error', ['param' => 'electricity_bill_payment_type']);   //电费支付类型
        $validateContract['water_billing_method']             = 'Required|IntIn:' . implode(',', array_keys(WarehouseEnums::$thread_water_billing_method)) . '|>>>:' . static::$t->_('params_error', ['param' => 'water_billing_method']);            //水费使用计费方式
        $validateContract['electricity_billing_method']       = 'Required|IntIn:' . implode(',', array_keys(WarehouseEnums::$thread_electricity_billing_method)) . '|>>>:' . static::$t->_('params_error', ['param' => 'electricity_billing_method']);      //电费使用计费方式
        $validateContract['water_usage_units']                = 'Required|FloatGeLe:0,*********.99|>>>:' . static::$t->_('params_error', ['param' => 'water_usage_units']);               //水费使用单位数
        $validateContract['electricity_usage_units']          = 'Required|FloatGeLe:0,*********.99|>>>:' . static::$t->_('params_error', ['param' => 'electricity_usage_units']);         //电费使用单位数
        $validateContract['duty_stamp']                       = 'Required|IntIn:1,2|>>>:' . static::$t->_('params_error', ['param' => 'duty_stamp']);//印花税付款人
        $validateContract['land_tax_payer']                   = 'Required|IntIn:1,2|>>>:' . static::$t->_('params_error', ['param' => 'land_tax_payer']);//土地税付款人
        $validateContract['issue_withholding_tax_address']    = 'Required|StrLenGeLe:1,1000|>>>:' . static::$t->_('params_error', ['param' => 'issue_withholding_tax_address']);//开具扣缴税款单据的地址

        return $validateContract;
    }

    /**
     * 获取税费信息验证规则
     * @return string[]
     */
    public function getAreaInfoValidate(): array
    {
        $area_info_validate = self::$area_info_validate;
        if (GlobalEnums::TH_COUNTRY_CODE == get_country_code()) {
            $area_info_validate['tax_type'] = 'Required|IntIn:'.implode(',', array_keys(ContractEnums::$tax_type_items)) .'|>>>:' . static::$t->_('params_error', ['param' => 'tax_type']); //税费类型
        }
        return $area_info_validate;
    }

    public function getIsMainList()
    {
        $data = [
            Enums::CONTRACT_IS_MASTER_YES => self::$t->_('contract_is_master.1'),
            Enums::CONTRACT_IS_MASTER_NO  => self::$t->_('contract_is_master.2'),
        ];
        $countryCode = strtoupper(env('country_code', 'TH'));
        if ('PH' == $countryCode || 'MY' == $countryCode) {
            $data[Enums::CONTRACT_IS_LOI_YES] = self::$t->_('contract_is_master.3');
        }

        return $data;
    }

    /**
     * 网点租房合同付款方式
     * @return array
     */
    public function getContractLeaseTypes()
    {
        $item = EnumsService::getInstance()->getStoreRentPaymentContractPayTypeItem();
        if (!empty($item)) {
            foreach ($item as $index => &$t_key) {
                $t_key = self::$t[$t_key];
            }
            return $item;
        }
        return [];
    }

    /**
     * 获取网点列表
     * Created by: Lqz.
     * @param $storeCate
     * @return mixed
     * CreateTime: 2020/9/10 0010 16:44
     */
    public function getStoreList($storeCate)
    {
        if (!$storeCate) {
            return null;
        }
        $_store = [
            'id'             => Enums::PAYMENT_HEADER_STORE_ID,
            'name'           => Enums::PAYMENT_HEADER_STORE_NAME,
            'lat'            => '1.01',
            'lng'            => '1.01',
            'detail_address' => null,
            'province_name'  => null,
            'region_name'    => null,
        ];
        $where  = ' 1 = 1 and state = 1';
        if (!empty($storeCate) && $storeCate!='all') {
            if ($storeCate == -1) {//ho（header office 总部）
                return [$_store];
            } else {
                $where .= " AND category = {$storeCate}";
            }
        } else {
            $where .= " AND category != 6 and id != 'TH01080102'";
        }
        $where     .= " AND id not in ('TH01010101','TH01010104','TH01080102','TH02020402','TH99999998','TH99999999','TH47011801')";
        $storeObj  = SysStoreModel::find([
            'conditions' => $where,
            'columns'    => 'id,name,lat,lng,detail_address,province_code,manage_region,city_code,district_code,postal_code',
            'order'      => 'name asc'
        ]);
        $storeList = $storeObj->toArray();
        if ($storeList) {
            $provincesCodes  = array_column($storeList, 'province_code');
            $cityCodes       = array_column($storeList, 'city_code');
            $districtCodes   = array_column($storeList, 'district_code');

            $manageRegionIds = array_column($storeList, 'manage_region');

            $provincesArr = [];
            if(!empty($provincesCodes)){
                $provincesArr = SysProvinceModel::find(
                    [
                        'conditions' => 'code in ({codeArr:array})',
                        'bind' => [
                            'codeArr' => $provincesCodes
                        ],
                        'columns' => 'code,name'
                    ]
                )->toArray();
                $provincesArr = array_column($provincesArr, 'name', 'code');
            }


            $cityArr = [];
            if(!empty($cityCodes)){
                $cityArr = SysCityModel::find(
                    [
                        'conditions' => 'code in ({codeArr:array})',
                        'bind' => [
                            'codeArr' => $cityCodes
                        ],
                        'columns' => 'code,name'
                    ]
                )->toArray();
                $cityArr = array_column($cityArr, 'name', 'code');
            }


            $districtArr = [];
            if(!empty($districtCodes)){
                $districtArr = SysDistrictModel::find(
                    [
                        'conditions' => 'code in ({codeArr:array})',
                        'bind' => [
                            'codeArr' => $districtCodes
                        ],
                        'columns' => 'code,name'
                    ]
                )->toArray();
                $districtArr = array_column($districtArr, 'name', 'code');
            }



            $regionArr = [];
            if(!empty($manageRegionIds)){
                $regionArr = SysManageRegionModel::find(
                    [
                        'conditions' => 'id in ({ids:array}) and deleted = 0 ',
                        'bind' => [
                            'ids' => $manageRegionIds
                        ],
                        'columns' => 'id,name'
                    ]
                )->toArray();
                $regionArr = array_column($regionArr, 'name', 'id');
            }

            foreach ($storeList as &$store) {
                $store['province_name'] = $provincesArr[$store['province_code']] ?? '';
                $store['region_name']   = $regionArr[$store['manage_region']] ?? '';
                $store['detail_address'] = ($store['detail_address']??'')." ".($districtArr[$store['district_code']] ?? '')."-".($cityArr[$store['city_code']]??'').'-'.$store['province_name']."-".($store['postal_code']??'');
            }
        }

        return $storeList;
    }

    /**
     * 获取主合同号列表
     * Created by: Lqz.
     * @param $loginUser
     * @param $contractId
     * @return array
     * CreateTime: 2020/9/11 0011 11:01
     */
    public function getMainContractList($loginUser, $contractId)
    {
        if (!$contractId) {
            return [];
        }
        $conditions   = 'contract_id = :contractId:';
        $bind         = [
            'contractId' => $contractId
        ];
        $column       = 'id,contract_id,contract_name';
        $limit        = 10;
        $contractsObj = ContractStoreRentingModel::find([
            'conditions' => $conditions,
            'bind'       => $bind,
            'columns'    => $column,
            'limit'      => $limit,
        ]);
        return $contractsObj->toArray();
    }

    /**
     * 获取单个合同详情
     * Created by: Lqz.
     *
     * @param $id
     * @return mixed
     * CreateTime: 2020/9/11 0011 11:03
     * @throws BusinessException
     */
    public function getContractInfo($id)
    {
        if (!$id) {
            return null;
        }

        $contract = ContractStoreRentingModel::findFirst([
            'conditions' => 'id = :id:',
            'bind'       => ['id' => $id]
        ]);
        if ($contract) {
            $contract_model = clone $contract;
            $contract = $contract->toArray();
        } else {
            return null;
        }
        // 关联合同编号id
        if (!empty($contract['main_contract_id'])) {
            $contractRel = ContractStoreRentingModel::findFirst([
                'conditions' => 'contract_id = :id:',
                'bind'       => ['id' => $contract['main_contract_id']]
            ]);
        }
        $contract['main_id'] = $contractRel->id ?? 0;
        // loi关联合同
        if (!empty($contract['relate_contract_id'])) {
            $contractLoi = ContractStoreRentingModel::findFirst([
                'conditions' => 'contract_id = :id:',
                'bind'       => ['id' => $contract['relate_contract_id']]
            ]);
        }
        // LOI关联合同
        $contract['relate_contract_loi_id'] = $contractLoi->id ?? 0;

        // 历史合同是否关联了仓库
        $contract['relevant_contract_is_related_warehouse'] = false;
        if (!empty($contract['relevant_contract_id'])) {
            $related_warehouse_info = ContractStoreRentingModel::findFirst([
                'conditions' => 'id = :id:',
                'bind' => ['id' => $contract['relevant_contract_id']],
                'columns' => ['warehouse_id']
            ]);

            $contract['relevant_contract_is_related_warehouse'] = !empty($related_warehouse_info->warehouse_id);
        }

        // 获取网点名称
        $store = (new StoreRepository())->getStoreDetail($contract['store_id'], 0);
        $contract['store_name'] = $store ? $store['name'] : '';
        $contract_money_list         = $this->getStoreRentingMoney($contract['id'], 'contract_money');
        $actual_monthly_payment_list = $this->getStoreRentingMoney($contract['id'], 'actual_payment');

        $contract['bank_collection'] = json_decode($contract['bank_collection'], true);
        if ($contract['bank_collection'] and empty($contract['apply_at'])) {
            foreach ($contract['bank_collection'] as $key => $_c) {
                if (isset($_c['bank_name_1'])) {
                    $contract['bank_collection'][$key]['bank_name']         = $_c['bank_name_1'];
                    $contract['bank_collection'][$key]['bank_account_name'] = $_c['bank_account_title_1'];
                    $contract['bank_collection'][$key]['bank_account']      = $_c['bank_book_no_1'];
                    $contract['bank_collection'][$key]['contact_mobile']    = $_c['contact_mobile_1'];
                    $contract['bank_collection'][$key]['contact_emial']     = '';
                   // $contract['bank_collection'][$key]['pay_amount']        = $_c['remit_amout'];
                    $contract['bank_collection'][$key]['contact_type']      = '';
                    $contract['bank_collection'][$key]['contact_code']      = '';
                }
            }
        }

        $contract['contract_effect_date'] = ($contract['contract_effect_date']=='0000-00-00')?'':$contract['contract_effect_date'];


        // v18530 获取应展示的合同附件(合同自身的, 包含原正文附件/其他附件)
        $contract['attachment_list'] = $this->getContractSelfAttachmentList($contract_model);

        // 去掉原正文附件/其他附件的参数
        unset($contract_model, $contract['pdf_required_name'], $contract['pdf_noneed_name']);

        // 其他场景附件
        $pdfRejectName = json_decode($contract['pdf_reject_name'], true);
        if (!empty($pdfRejectName) && is_array($pdfRejectName)) {
            foreach ($pdfRejectName as &$item){
                $item['id'] = $item['object_key'] ?? '';
                $item['business_type'] = AttachmentService::BUSINESS_TYPE_CONTRACT_PDF_REJECT_NAME;
            }
            $contract['pdf_reject_name'] = $pdfRejectName;
        } else {
            $contract['pdf_reject_name'] = [];
        }

        $contract['contract_money_list']         = $contract_money_list;
        $contract['actual_monthly_payment_list'] = $actual_monthly_payment_list;

        // 地契类型id转数组
        $contract['land_type']                   = !empty($contract['land_type']) ? explode(',',$contract['land_type']) : [1];

        //区分新老版本 ver

        $contract_amount_detail = $this->getStoreRentingDetail($contract['id']);
        foreach ($contract_amount_detail as &$detail) {
            $detail['rent_no_wht'] = $detail['rent_no_wht'] ?? '';
        }
        $contract['areaInfo'] = ContractStoreRentingArea::find("contract_store_renting_id = {$contract['id']}")->toArray();
        if(!empty($contract['areaInfo'])){
            foreach ($contract['areaInfo'] as &$area){
                $area['area_start_time'] = (empty($area['start_time']) || '0000-00-00 00:00:00' == $area['start_time']) ? '' : date('Y-m-d',strtotime($area['start_time']));
                $area['area_end_time']   = (empty($area['end_time']) || '0000-00-00 00:00:00' == $area['end_time']) ? '' : date('Y-m-d',strtotime($area['end_time']));
                $area['tax_type']        = $area['tax_type'] ?? 0;
                $area['tax_type_text']   = static::$t->_(ContractEnums::$tax_type_items[$area['tax_type']] ?? '');
                $area['tax_amount_has_wht'] = $area['tax_amount_has_wht'] ?? '';
                $area['tax_amount_no_wht']  = $area['tax_amount_no_wht'] ?? '';
            }
        }

        $contract['amount_detail'] = $contract_amount_detail;
        if(is_array($contract['rent_free_time'])){
            $contract['rent_free_time'] = explode(',', trim($contract['rent_free_time']));
        }
        $contract['rent_free_time'] = trim($contract['rent_free_time']);

        $contract_archive = ContractArchive::findFirst([
            'conditions' => 'cno = :cno:',
            'bind'       => [
                'cno' => $contract['contract_id']
            ],
            'for_update' => true,
        ]);

        $contract['pdf_noneed_file'] = $contract['pdf_terminal_file'] = [];
        if (isset($contract_archive->id)) {
            // 归档合同废除附件
            if ($contract['contract_status'] == Enums::CONTRACT_STATUS_APPROVAL) {
                if (!empty($contract_archive->pdf_noneed_file)) {
                    $contract['pdf_noneed_file'] = json_decode($contract_archive->pdf_noneed_file, true);
                    foreach ($contract['pdf_noneed_file'] as $k=>$item){
                        $contract['pdf_noneed_file'][$k]['id'] = $item['object_key'] ?? '';
                        $contract['pdf_noneed_file'][$k]['business_type'] = AttachmentService::BUSINESS_TYPE_CONTRACT_PDF_NONEED_FILE;
                    }
                }
            }
            // 归档合同终止附件
            $contract['pdf_terminal_file'] = AttachModel::find([
                'conditions' => 'oss_bucket_key = :oss_bucket_key: AND oss_bucket_type = :oss_bucket_type: AND deleted = :deleted:',
                'bind' => ['oss_bucket_key' => $contract_archive->id, 'oss_bucket_type' => ContractEnums::OSS_BUCKET_TYPE_TERMINAL_FILE, 'deleted' => 0],
                'columns' => ['id', 'oss_bucket_type', 'file_name', 'bucket_name', 'object_key']
            ])->toArray();
        }
        //当前页面头部显示1.申请 2.作废 3.终止
        $contract['current_audit_type'] = ContractEnums::CONTRACT_AUDIT_TYPE_APPLY;
        //作废详情
        $contract['invalid_detail'] = (object)[];
        if (!empty($contract_archive) && in_array($contract_archive->status, [ContractEnums::CONTRACT_ARCHIVE_STATUS_INVALID, ContractEnums::CONTRACT_ARCHIVE_STATUS_INVALID_ING])) {
            $contract['current_audit_type'] = ContractEnums::CONTRACT_AUDIT_TYPE_INVALID;
            $contract['invalid_detail'] = [
                'invalid_reason_type' => $contract_archive->invalid_reason_type,
                'invalid_reason_type_text' => isset(ContractEnums::$invalid_reason_type_list[$contract_archive->invalid_reason_type]) ? static::$t->_(ContractEnums::$invalid_reason_type_list[$contract_archive->invalid_reason_type]) : '',
                'invalid_refund_method' => $contract_archive->invalid_refund_method,
                'invalid_refund_method_text' => isset(ContractEnums::$invalid_refund_method_list[$contract_archive->invalid_refund_method]) ? static::$t->_(ContractEnums::$invalid_refund_method_list[$contract_archive->invalid_refund_method]) : '',
                'invalid_refund_begin' => $contract_archive->invalid_refund_begin,
                'invalid_refund_end' => $contract_archive->invalid_refund_end,
                'invalid_replace_cno' => $contract_archive->invalid_replace_cno,
                'invalid_replace_cno_id' => '',
                'invalid_replace_begin' => $contract_archive->invalid_replace_begin,
                'invalid_replace_end' => $contract_archive->invalid_replace_end,
                'invalid_reason' => $contract_archive->invalid_reason,
                'invalid_file' => $contract['pdf_noneed_file'],
            ];
            //需要查询替换合同的主键,前端用来查询
            if (!empty($contract['invalid_detail']['invalid_replace_cno'])) {
                $store_contract = ContractStoreRentingModel::findFirst([
                    'columns' => 'id',
                    'conditions' => 'contract_id = :contract_id:',
                    'bind'       => ['contract_id' => $contract['invalid_detail']['invalid_replace_cno']]
                ]);
                $contract['invalid_detail']['invalid_replace_cno_id'] = $store_contract ? $store_contract->id : '';
            }
        }
        //终止详情
        $contract['terminal_detail'] = (object)[];
        if (!empty($contract_archive) && in_array($contract_archive->status, [ContractEnums::CONTRACT_ARCHIVE_STATUS_TERMINAL, ContractEnums::CONTRACT_ARCHIVE_STATUS_TERMINAL_ING])) {
            $contract['current_audit_type'] = ContractEnums::CONTRACT_AUDIT_TYPE_TERMINAL;
            $contract['terminal_detail'] = [
                'terminal_at' => $contract_archive->terminal_at,
                'terminal_reason' => $contract_archive->terminal_reason,
                'terminal_file' => $contract['pdf_terminal_file'],
            ];
        }

        //V22497 增加以下字段返回
        $contract['withholding_tax_liability_bearer'] = $contract['withholding_tax_liability_bearer'] ? $contract['withholding_tax_liability_bearer'] : '';
        $contract['water_bill_payment_type']          = $contract['water_bill_payment_type'] ? $contract['water_bill_payment_type'] : '';
        $contract['electricity_bill_payment_type']    = $contract['electricity_bill_payment_type'] ? $contract['electricity_bill_payment_type'] : '';
        $contract['water_billing_method']             = $contract['water_billing_method'] ? $contract['water_billing_method'] : '';
        $contract['electricity_billing_method']       = $contract['electricity_billing_method'] ? $contract['electricity_billing_method'] : '';
        $contract['water_usage_units']                = !is_null($contract['water_usage_units']) ? $contract['water_usage_units'] : '';
        $contract['electricity_usage_units']          = !is_null($contract['electricity_usage_units']) ? $contract['electricity_usage_units'] : '';
        $contract['contract_total_amount_no_wht']     = !is_null($contract['contract_total_amount_no_wht']) ? $contract['contract_total_amount_no_wht'] : '';
        $contract['month_rent_has_wht']               = !is_null($contract['month_rent_has_wht']) ? $contract['month_rent_has_wht'] : '';
        return $contract;
    }

    public function getStoreRentingMoney($contractId = '', $type = '')
    {
        $data = ContractStoreRentingMoneyModel::find([
            'conditions' => 'store_renting_contract_id=:contract_id: and  type=:type:',
            'bind'       => [
                'contract_id' => $contractId,
                'type'        => $type
            ],
        ]);
        return $data->toArray();
    }

    /**
     * 获取租房合同自身应显示的附件
     *
     * @param object $main_object 租房合同主数据对象
     * @return array|object
     * @throws BusinessException
     */
    protected function getContractSelfAttachmentList(object $main_object)
    {
        // 1. 获取该合同配置的需显示的附件(合同类型 + 房东类型)
        $show_file_config_item = $this->getShowAttachmentConfig($main_object->is_main, $main_object->house_owner_type);
        if (empty($show_file_config_item)) {
            return (object)[];
        }

        // 2. 获取合同自身已存储的附件
        $exist_file_list = $main_object->getSelfAttachments()->toArray();
        $exist_file_list_by_sub_type_map = [];
        foreach ($exist_file_list as $file) {
            $exist_file_list_by_sub_type_map[$file['sub_type']][] = [
                'id' => $file['id'],
                'bucket_name' => $file['bucket_name'],
                'object_key' => $file['object_key'],
                'file_name' => $file['file_name'],
            ];
        }
        unset($exist_file_list);

        // 3. 整合该合同应显示的附件
        $attachment_list = [];
        foreach ($show_file_config_item as $file_field => $attr) {
            $attachment_list[$file_field] = $exist_file_list_by_sub_type_map[$attr['file_sub_type']] ?? [];
        }

        return $attachment_list;
    }

    /**
     * 获取网点类型
     * Created by: Lqz.
     * @param $userDept array 用户信息
     * @param $contractInfo array 是否通过申请人查询
     * @return mixed
     * CreateTime: 2020/9/10 0010 19:27
     */
    public function getStoreCateList($userDept, $contractInfo = [])
    {
        // 获取所有部门网点列表
        $storesList = (new StoreService())->getDepartStoreType();
        $types = [];
        foreach ($storesList as $store) {
            $types[$store['department_id']][$store['type']] = $store['name'];
        }
        $types['other']['-1'] = Enums::PAYMENT_HEADER_STORE_NAME;
        // 申请人部门ID
        $deptId = $userDept['sys_department_id'] ?? '';

        // 老挝, 取全部配置[不区分分公司]
        if ('LA'== get_country_code()) {
            if ((new DepartmentService())->isFlashLaosByDepartmentId($deptId)) {
                return $types[0];
            } else {
                return $types['other'];
            }
        }


        // 获取申请人所在部门
        if (!empty($contractInfo)) {
            $userDept = HrStaffInfoModel::getUserInfo($contractInfo['manage_id'], 'sys_department_id');
            $deptId = $userDept['sys_department_id'] ?? '';
        } else {
            $deptId = $userDept['sys_department_id'] ?? '';
        }

        $sysDepartment = StaffService::getInstance()->getParentDepartment($deptId, 1);

        if(trim($sysDepartment['id'])==Enums::SYS_DEPARTMENT_ONE_ID && get_country_code()=='TH'){
            $deptId = Enums::SYS_DEPARTMENT_ONE_ID;
        }

        $is_ffm = (new DepartmentService())->isFFMByDepartmentId($deptId);
        if ($is_ffm) {
            // fullfillment 公司, 按国家配置展示
            switch (get_country_code()) {
                case 'MY':
                    return $types[Enums::$company_types['MalaysiaFulfillment']];
                default:
                    return $types[Enums::$company_types['FlashFullfillment']];
            }
        } else if ($deptId && isset($types[$deptId])) {
            return $types[$deptId];
        } else {
            return $types['other'];
        }
    }

    /**
     * 获取所有网点类型
     * Created by: Wq.
     * @return mixed
     * CreateTime: 2021/9/26 0010 19:27
     */
    public function getAllStoreCateList()
    {
        // 获取所有部门网点列表
        $storesList = (new StoreService())->getDepartStoreType();

        $types = [];
        foreach ($storesList as $store) {
            $types[$store['type']] = $store['name'];
        }
        $types['-1'] = Enums::PAYMENT_HEADER_STORE_NAME;

        return $types;
    }

    public function generateContractId($contractObj)
    {
        $contractNo       = ($contractObj && !empty($contractObj->contract_no)) ? $contractObj->contract_no + 1 : 1;
        $num              = 4 - strlen($contractNo);
        $contractNoPrefix = $num > 0 ? str_repeat('0', $num) : '';
//        $code             = 'FEXFZ' . date('Ymd') . $contractNoPrefix . $contractNo;
        $code             =  date('Ymd') . $contractNoPrefix . $contractNo;
        return [
            'code'        => $code,
            'contract_no' => $contractNo
        ];
    }

    /**
     * 其它额外参数验证: 租房合同新增 / 编辑提交 / 续签提交 / 重新提交 / 归档编辑提交 等场景公用
     *
     * @param array $data 请求入参
     * @return mixed
     * @throws BusinessException
     * @throws ValidationException
     */
    public function extendValidation(array $data)
    {
        // 合同总金额（不含VAT含WHT）不能大于合同总金额（含WHT含VAT）
        if (!empty($data['contract_total_amount_contain_wht'])) {
            if ($data['contract_total_amount_contain_wht'] > $data['contract_total_amount']) {
                throw new ValidationException(static::$t->_('rent_contract_save_error_002', ['VAT_SST' => EnumsService::getInstance()->getVatSStRateName()]), ErrCode::$VALIDATE_ERROR);
            }
        }

        // 免租期
        if (!empty($data['rent_free_time'])) {
            if (strtotime($data['rent_free_time'][0]) > strtotime($data['rent_free_time'][1])) {
                throw new ValidationException(static::$t->_('rent_contract_save_error_003'), ErrCode::$VALIDATE_ERROR);
            }

            if ((strtotime($data['rent_free_time'][0]) > strtotime($data['contract_end'])) || (strtotime($data['rent_free_time'][1]) < strtotime($data['contract_begin']))) {
                throw new ValidationException(static::$t->_('rent_free_time_is_wrong'), ErrCode::$VALIDATE_ERROR);
            }
        }

        // 校验合同生效日期
        if (!empty($data['contract_effect_date'])) {
            if (strtotime($data['contract_effect_date']) > strtotime($data['contract_end']) || strtotime($data['contract_effect_date']) < strtotime($data['contract_begin'])) {
                throw new ValidationException(static::$t->_('rent_contract_save_error_004'), ErrCode::$VALIDATE_ERROR);
            }
        }

        $country_code = get_country_code();

        // 付款信息 去除前后空白字符
        $data['bank_collection'] = trim_array($data['bank_collection']);
        if ($country_code == GlobalEnums::PH_COUNTRY_CODE) {
            foreach ($data['bank_collection'] as $bank) {
                if ($bank['pay_type'] == Enums::CONTRACT_PAY_TYPE_TICKET && empty($bank['bank_account_name'])) {
                    throw new ValidationException(static::$t->_('rent_contract_save_error_007'), ErrCode::$VALIDATE_ERROR);
                }
            }
        }

        // 校验器规则
        $validate = [
            'is_confirmed' => 'Required|Bool|>>>:' . static::$t->_('params_error', ['param' => 'is_confirmed']),
        ];

        // 仓库相关字段初始化
        $data['warehouse_id'] = $data['warehouse_id'] ?? '';
        $data['warehouse_name'] = '';
        $data['warehouse_real_area'] = '';
        $data['warehouse_latitude'] = '';
        $data['warehouse_longitude'] = '';
        $data['warehouse_province_name'] = '';
        $data['warehouse_city_name'] = '';
        $data['warehouse_district_name'] = '';

        // 泰国/菲律宾/马来
        if (in_array($country_code, [GlobalEnums::TH_COUNTRY_CODE, GlobalEnums::PH_COUNTRY_CODE, GlobalEnums::MY_COUNTRY_CODE])) {
            // 若仓库ID有值, 则从仓库信息表取出额外字段
            if (!empty($data['warehouse_id'])) {
                $warehouse_info = WarehouseService::getInstance()->getBaseInfo($data['warehouse_id']);
                $data['warehouse_name'] = $warehouse_info['warehouse_name'] ?? '';
                $data['warehouse_real_area'] = $warehouse_info['real_area'] ?? '';
                $data['warehouse_latitude'] = $warehouse_info['warehouse_latitude'] ?? '';
                $data['warehouse_longitude'] = $warehouse_info['warehouse_longitude'] ?? '';
                $data['warehouse_province_name'] = $warehouse_info['province_name'] ?? '';
                $data['warehouse_city_name'] = $warehouse_info['city_name'] ?? '';
                $data['warehouse_district_name'] = $warehouse_info['district_name'] ?? '';
            }

            // 仓库信息的校验
            $validate['warehouse_id'] = 'Required|StrLenGeLe:0,32|>>>:params error[warehouse_id]';

            // 当网点类型为指定配置的网点类型时, 仓库信息必填
            $warehouse_required_for_store_types = EnumsService::getInstance()->getSettingEnvValueIds('warehouse_required_for_store_types');
            if (in_array($data['store_cate'], $warehouse_required_for_store_types)) {
                $validate['warehouse_id'] = 'Required|StrLenGeLe:1,32|>>>:params error[warehouse_id]';

                // 仓库实际面积 与 合同中的实际面积校验: 不相等则提示
                if (bccomp($data['house_actual_area'], $data['warehouse_real_area'], 2) != 0) {
                    throw new ValidationException(static::$t->_('rent_contract_save_error_001'), ErrCode::$VALIDATE_ERROR);
                }
            }

            Validation::validate($data, $validate);

            //国家=菲律宾 && 合同类型=主合同或附属合同 && 付款币种=PHP，则需要根据合同总金额（不含VAT含WHT）计算印花税金额
            if (in_array($data['is_main'], [Enums::CONTRACT_IS_MASTER_YES, Enums::CONTRACT_IS_MASTER_NO]) && $data['money_symbol'] == GlobalEnums::CURRENCY_PHP) {
                $contract_total_amount_contain_wht = $data['contract_total_amount_contain_wht'];
                if (bccomp($contract_total_amount_contain_wht, 2000, 2) <= 0) {
                    //1. 合同总金额小于等于2000PHP：印花税金额=6；
                    $calculation_total_amount = 6;
                } else {
                    //2. 合同总金额超过2000PHP：印花税金额 = （合同总金额（不含VAT含WHT）-2000）/1000 *2+6
                    $calculation_total_amount = sprintf("%.2f", ((floatval($contract_total_amount_contain_wht) - 2000) / 1000 * 2 + 6));
                }
                if (bccomp($calculation_total_amount, $data['total_amount'], 2) !== 0) {
                    throw new ValidationException(static::$t->_('contract_store_renting_stamp_tax_error'), ErrCode::$VALIDATE_ERROR);
                }
            }
        }

        // 租房合同附件校验
        // 获取需显示的合同附件配置 并 生成校验规则 进行校验
        $contract_attachmnet_config = $this->getShowAttachmentConfig($data['is_main'], $data['house_owner_type']);
        if (!empty($contract_attachmnet_config)) {
            $file_validate = [];
            foreach ($contract_attachmnet_config as $file_field => $attr) {
                $min_num = $attr['is_required'] ? 1 : 0;
                $file_validate[$file_field] = "Required|ArrLenGeLe:{$min_num},{$attr['max_upload_num']}|>>>:params error[attachment_list.{$file_field}]";
            }

            Validation::validate($data['attachment_list'], $file_validate);
        }

        // 未经二次确认, 需校验 总金额 和 明细行金额合计
        $data['is_need_confirm'] = false;
        $data['detail_total_amount_has_tax'] = '0.00';
        $data['detail_total_amount_no_tax'] = '0.00';
        if (!$data['is_confirmed'] && (!empty($data['contract_total_amount']) || !empty($data['contract_total_amount_contain_wht']))) {
            $detail_amount_item = $this->calDetailTotalAmount($data['contract_lease_type'], $data['amount_detail']);
            if (
                (!empty($data['contract_total_amount']) && bccomp($data['contract_total_amount'], $detail_amount_item['detail_total_amount_has_tax'], 3) != 0)
                ||
                (!empty($data['contract_total_amount_contain_wht']) && bccomp($data['contract_total_amount_contain_wht'], $detail_amount_item['detail_total_amount_no_tax'], 3) != 0)
            ) {
                $data['is_need_confirm'] = true;
                $data['detail_total_amount_has_tax'] = $detail_amount_item['detail_total_amount_has_tax'];
                $data['detail_total_amount_no_tax'] = $detail_amount_item['detail_total_amount_no_tax'];
            }
        }

        return $data;
    }

    /**
     * 计算明细行含税租金 和 不含税租金 合计金额
     *
     * @param int $lease_type 付款方式: 1-月付; 2-年付; 3-季付; 4-半年付; 5-一次性付清
     * @param array $amount_detail 金额详情
     * @return array
     */
    protected function calDetailTotalAmount(int $lease_type, array $amount_detail)
    {
        // 含税金额合计
        $detail_total_amount_has_tax = 0;

        // 不含税金额合计
        $detail_total_amount_no_tax = 0;

        foreach ($amount_detail as $detial) {
            // 一次性
            if ($lease_type == 5) {
                $detail_total_amount_has_tax += $detial['amount_has_tax'];
                $detail_total_amount_no_tax += $detial['amount_no_tax'];
            } else {
                // 期间实际月数
                $total_month = cal_total_months($detial['cost_start_date'], $detial['cost_end_date']);

                switch ($lease_type) {
                    case 1:
                        $detail_total_amount_has_tax += $total_month * $detial['amount_has_tax'];
                        $detail_total_amount_no_tax += $total_month * $detial['amount_no_tax'];
                        break;
                    case 3:
                        $detail_total_amount_has_tax += $total_month / 3 * $detial['amount_has_tax'];
                        $detail_total_amount_no_tax += $total_month / 3 * $detial['amount_no_tax'];
                        break;
                    case 4:
                        $detail_total_amount_has_tax += $total_month / 6 * $detial['amount_has_tax'];
                        $detail_total_amount_no_tax += $total_month / 6 * $detial['amount_no_tax'];
                        break;
                    case 2:
                        $detail_total_amount_has_tax += $total_month / 12 * $detial['amount_has_tax'];
                        $detail_total_amount_no_tax += $total_month / 12 * $detial['amount_no_tax'];
                        break;
                }
            }
        }

        return [
            'detail_total_amount_has_tax' => number_format(round($detail_total_amount_has_tax, 2), 2, '.', ''),
            'detail_total_amount_no_tax' => number_format(round($detail_total_amount_no_tax, 2), 2, '.', ''),
        ];
    }

    public function saveContract($loginUser, $params, $is_renewal_contract = false)
    {
        $uid          = $loginUser['id'];
        $country_code = get_country_code();
        if (isset($params['id']) && $params['id']) {
            // 编辑保存, 仅能更新申请人的数据
            $model = ContractStoreRentingModel::findFirst([
                'conditions' => 'id = :id: AND ( manage_id = :manage_id: or contract_leader_id = :manage_id:)',
                'bind'       => ['id' => $params['id'], 'manage_id' => $uid]
            ]);

            if (empty($model)) {
                return [
                    'error_msg' => static::$t->_('contract_data_not_exist', ['id' => $params['id'] . '-' . $uid])
                ];
            }

        } else {
            // 新增
            $model              = new ContractStoreRentingModel();
            $lastContract       = ContractStoreRentingModel::findFirst([
                'order'      => 'id desc',
                'columns'    => 'id,contract_no',
                'for_update' => true
            ]);
            $contractIdArr      = $this->generateContractId($lastContract);
            $model->contract_id = $contractIdArr['code'];
            $model->contract_no = $contractIdArr['contract_no'];
            $exist              = ContractStoreRentingModel::findFirst([
                'conditions' => 'contract_id = :contract_id:',
                'bind'       => ['contract_id' => $contractIdArr['code']],
                'for_update' => true
            ]);

            if ($exist) {
                return [
                    'error_msg' => " the contract_id 【{$exist->contract_id}】 is exist! ",
                ];
            }

            //续签验证
            if ($is_renewal_contract) {
                $relate_model = ContractStoreRentingModel::findFirst([
                    'conditions' => 'contract_id = :contract_id:',
                    'bind'       => ['contract_id' => $params['relevant_contract_no']]
                ]);

                if (empty($relate_model)) {
                    return [
                        'error_msg' => static::$t->_('contract_data_not_exist', ['no' => $params['relevant_contract_no'] . '-' . $uid])
                    ];
                }

                // 历史合同有仓库信息的, 在续签时 不可 修改
                if (!empty($relate_model->warehouse_id) && $params['warehouse_id'] != $relate_model->warehouse_id) {
                    throw new ValidationException(static::$t->_('rent_renewal_contract_submit_error_1', ['old_contract_no' => $params['relevant_contract_no']]), ErrCode::$VALIDATE_ERROR);
                }

                $contract_archive_model = ContractArchive::findFirst([
                    'conditions' => 'cno = :cno:',
                    'bind'       => [
                        'cno' => $params['relevant_contract_no']
                    ],
                    'columns'    => 'cno,status'
                ]);

                if (empty($contract_archive_model)) {
                    return [
                        'error_msg' => static::$t->_('contract_data_not_exist', ['no' => $params['relevant_contract_no'] . '-' . $uid])
                    ];
                }

                if (!($relate_model->is_renewal == ContractEnums::IS_RENEWAL_1 && $relate_model->contract_status == Enums::CONTRACT_STATUS_APPROVAL && in_array($contract_archive_model->status ?? '', [ContractEnums::CONTRACT_ARCHIVE_STATUS_APPROVAL, ContractEnums::CONTRACT_ARCHIVE_STATUS_TERMINAL]) && in_array($relate_model->is_main, [Enums::CONTRACT_IS_MASTER_YES, Enums::CONTRACT_IS_LOI_YES]))) {
                    return [
                        'error_msg' => static::$t->_('contract_cannot_be_renewed', ['no' => $params['relevant_contract_no'] . '-' . $uid])
                    ];
                }
            }

            // 仅新增场景需存储的字段
            $model->apply_at = date('Y-m-d H:i:s');
            $model->created_at = date('Y-m-d H:i:s');
            $model->contract_status = Enums::CONTRACT_STATUS_PENDING;

            //需求15258 : 新增时把[网点合同签署提醒]相关待处理任务置为已处理
            if ($country_code == GlobalEnums::PH_COUNTRY_CODE) {
                if ($params['is_main'] == Enums::CONTRACT_IS_MASTER_YES) { //主合同
                    //查询此网点的所有待处理提醒
                    $remind_data = ContractStoreRentingRemindModel::find([
                        'columns' => 'id',
                        'conditions' => 'store_id = :store_id: and task_type in ({task_type:array}) and task_status = :task_status:',
                        'bind' => [
                            'store_id' => $params['store_id'],
                            'task_type' => [ContractEnums::TASK_TYPE_CONTRACT_EXPIRE, ContractEnums::TASK_TYPE_CONTRACT_SIGN],
                            'task_status' => ContractEnums::TASK_STATUS_TODO
                        ]
                    ])->toArray();
                } elseif ($params['is_main'] == Enums::CONTRACT_IS_LOI_YES) { //意向书
                    //查询此网点的所有待处理提醒
                    $remind_data = ContractStoreRentingRemindModel::find([
                        'columns' => 'id',
                        'conditions' => 'store_id = :store_id: and task_type in ({task_type:array}) and task_status = :task_status:',
                        'bind' => [
                            'store_id' => $params['store_id'],
                            'task_type' => [ContractEnums::TASK_TYPE_CONTRACT_EXPIRE, ContractEnums::TASK_TYPE_LOI_SIGN],
                            'task_status' => ContractEnums::TASK_STATUS_TODO
                        ]
                    ])->toArray();
                }
                //如果数据不为空,执行更新
                if (!empty($remind_data)) {
                    $update = [
                        'task_status' => ContractEnums::TASK_STATUS_DONE,
                        'updated_at' => date('Y-m-d H:i:s'),
                        'finished_at' => date('Y-m-d H:i:s'),
                    ];
                    //批量更新
                    $ids = array_column($remind_data, 'id');
                    $ids = implode(',', array_values($ids));
                    $update_result = $this->getDI()->get('db_oa')->updateAsDict(
                        (new ContractStoreRentingRemindModel())->getSource(),
                        $update,
                        ['conditions' => "id IN ($ids) and task_status = ?", 'bind' => [ContractEnums::TASK_STATUS_TODO]]
                    );
                    if (!$update_result) {
                        $this->logger->warning('网点合同签署提醒-更新状态失败, ids=' . $ids . ' ; store_id=' . $params['store_id'] . ' ; is_main=' . $params['is_main']);
                        throw new BusinessException('网点合同签署提醒-更新状态失败', ErrCode::$BUSINESS_ERROR);
                    }
                }
            }
        }

        $coo_company_list = (new PurchaseService())->getCooCostCompany();

        //根据费用所属部门查询对应的COO/CEO下的BU级部门
        $cost_company_arr = array_column($coo_company_list,'cost_company_name','cost_company_id');

        $exchange_rate = EnumsService::getInstance()->getCurrencyExchangeRate($params['money_symbol']);
        $exchange_rate = $exchange_rate ? $exchange_rate : 1;


        // 新增 和 编辑 需存储的公共字段
        $model->contract_lang               = $params['contract_lang'];
        $model->manage_id                   = $uid;
        $model->create_department_id        = $loginUser['node_department_id'];
        $model->contract_leader_id          = $uid;
        $model->contract_money              = $params['contract_money']??0;
        $model->actual_monthly_payment      = 0;
        $model->is_main                     = $params['is_main'];
        $model->main_contract_id            = $params['main_contract_id'] ?? null;
        $model->store_cate                  = $params['store_cate'];
        $model->store_id                    = $params['store_id'];
        $model->provinces                   = $params['provinces'];
        $model->lon_lat                     = $params['lon_lat'];
        $model->store_addr                  = $params['store_addr'];
        $model->house_owner_name            = $params['house_owner_name'];
        $model->house_contract_area         = $params['house_contract_area'];
        $model->house_actual_area           = $params['house_actual_area'];
        $model->house_owner_type            = $params['house_owner_type'];
        $model->house_equip_list            = $params['house_equip_list'];

        $model->land_type                   = $params['land_type'] ? implode(',',$params['land_type']) : '1';
        $model->land_type_content           = $params['land_type_content'] ?? '';
        $model->leaser_type                 = $params['leaser_type'] ?? 1;
        $model->leaser_type_content         = $params['leaser_type_content'] ?? '';
        $model->rent_due_date               = $params['rent_due_date'] ?? 1;
        $model->is_rent_free                = $params['is_rent_free'] ?? 1;


        $model->contract_name               = $params['contract_name'];
        $model->contract_deadline           = $params['contract_deadline']??'0';
        $model->contract_begin              = $params['contract_begin'];
        $model->contract_end                = $params['contract_end'];
        $model->rent_free_time = (!empty($params['rent_free_time']))?(implode(",", $params['rent_free_time'])) : ' ';

        $model->exempted_amount_months      = $params['exempted_amount_months'];
        $model->contract_effect_date        = !empty($params['contract_effect_date']) ? $params['contract_effect_date'] : '0000-00-00';
        $model->contract_lease_type         = $params['contract_lease_type'];

        $model->contract_benefits           = ' ';
        $model->money_symbol                = $params['money_symbol'];
        $model->exchange_rate               = $exchange_rate;
        $model->deposit_amount              = $params['deposit_amount'];
        $model->monthly_payment_type        = null;
        $model->contract_signer_name        = $params['contract_signer_name'];
        $model->signer_phone                = $params['signer_phone'];
        $model->notice_renewal_days         = $params['notice_renewal_days'] ?? '';
        $model->full_refund_conditions      = $params['full_refund_conditions'];
        $model->renovation_days             = $params['renovation_days'] ?? '';
        $model->contract_remarks            = $params['contract_remarks'];
        $model->bank_collection             = json_encode($params['bank_collection'], JSON_UNESCAPED_UNICODE);
        $model->hourse_owner_addr           = $params['hourse_owner_addr'];

        $model->property_tax                = $params['property_tax'];
        $model->total_amount_monthly        = $country_code == GlobalEnums::TH_COUNTRY_CODE ? '' : $params['total_amount_monthly'];//房产税金额
        $model->duty_stamp                  = $params['duty_stamp'];
        $model->total_amount                = $params['total_amount'];
        $model->signboard_tax               = $params['signboard_tax'];
        $model->pdf_required_name           = '';//正文附件 v18530 迁移到 系统附件表 json_encode($params['pdf_required_name'], JSON_UNESCAPED_UNICODE)
        $model->pdf_noneed_name             = '';//其他附件 v18530 迁移到 系统附件表 json_encode($params['pdf_noneed_name'], JSON_UNESCAPED_UNICODE)

        // 增加
        $model->contract_total_amount       = $params['contract_total_amount'];
        $model->billboard_tax_payer         = $params['billboard_tax_payer'];
        $model->contract_deposit            = $params['contract_deposit']??'';
        $model->ver                         = $params['ver'];
        $model->due_date                    = $params['due_date'];
        $model->amount_paid                 = $params['amount_paid'];
        $model->contract_tax_no             = $params['contract_tax_no'];
        $model->land_tax_payer              = $params['land_tax_payer'];
        $model->fire_insurance_payer        = $params['fire_insurance_payer'];
        $model->antimoth_payer              = $params['antimoth_payer'];
        $model->land_tax_amount             = $country_code == GlobalEnums::TH_COUNTRY_CODE ? '' : $params['land_tax_amount'];//土地税金额
        $model->fire_insurance_amount       = $country_code == GlobalEnums::TH_COUNTRY_CODE ? '' : $params['fire_insurance_amount'];//火灾保险费金额;
        $model->antimoth_amount             = $params['antimoth_amount'];
        $model->relate_contract_id          = $params['relate_contract_id']??'';
        $model->cost_company_id             = $params['cost_company_id'];
        $model->cost_company_name           = $cost_company_arr[$params['cost_company_id']]??'';
        $model->contract_total_amount_contain_wht = $country_code == GlobalEnums::TH_COUNTRY_CODE ? '' : ($params['contract_total_amount_contain_wht'] ?? 0.00);
        $model->contract_total_amount_no_wht= $params['contract_total_amount_no_wht'] ?? null;//合同总金额（不含WHT含VAT）
        $model->month_rent_has_wht          = $country_code == GlobalEnums::TH_COUNTRY_CODE ? $this->calculateMonthRentHasWht($params) : null;//月每平方米租金（含WHT含VAT）
        $model->update_at                   = date('Y-m-d H:i:s');
        $model->relevant_contract_no        = empty($params['relevant_contract_no']) ? '' : $params['relevant_contract_no'];
        $model->is_renewal                  = ContractEnums::IS_RENEWAL_1;

        // 仓库信息
        $model->warehouse_id                     = $params['warehouse_id'];
        $model->warehouse_latitude               = $params['warehouse_latitude'];
        $model->warehouse_longitude              = $params['warehouse_longitude'];
        $model->warehouse_name                   = $params['warehouse_name'];
        $model->warehouse_province_name          = $params['warehouse_province_name'];
        $model->warehouse_city_name              = $params['warehouse_city_name'];
        $model->warehouse_district_name          = $params['warehouse_district_name'];
        $model->warehouse_real_area              = $params['warehouse_real_area'];
        $model->warehouse_price_id               = $params['warehouse_price_id'] ?? 0;
        $model->warehouse_price_no               = $params['warehouse_price_no'] ?? '';
        $model->withholding_tax_liability_bearer = $params['withholding_tax_liability_bearer'] ?? 0;//扣缴税责任承担方
        $model->water_bill_payment_type          = $params['water_bill_payment_type'] ?? 0;         //水费支付类型
        $model->electricity_bill_payment_type    = $params['electricity_bill_payment_type'] ?? 0;   //电费支付类型
        $model->water_billing_method             = $params['water_billing_method'] ?? 0;            //水费使用计费方式
        $model->electricity_billing_method       = $params['electricity_billing_method'] ?? 0;      //电费使用计费方式
        $model->water_usage_units                = $params['water_usage_units'] ?? null;            //水费使用单位数
        $model->electricity_usage_units          = $params['electricity_usage_units'] ?? null;      //电费使用单位数
        $model->issue_withholding_tax_address    = $params['issue_withholding_tax_address'] ?? '';  //开具扣缴税款单据的地址

        // 编辑
        if (isset($params['id']) && $params['id']) {
            $model->contract_leader_id = $params['contract_leader_id'];
        }

        //续签逻辑处理
        if ($is_renewal_contract) {
            $model->renewal_contract_type = ContractEnums::RENEWAL_CONTRACT_TYPE_2;
            $model->contract_leader_id = $relate_model->contract_leader_id;

            //关联合同续签标记
            $relate_model->is_renewal = ContractEnums::IS_RENEWAL_2;
            $model->relevant_contract_id = $relate_model->id;

            if ($relate_model->save() === false) {
                throw new BusinessException('续签合同的关联合同保存失败, 原因可能是:' . get_data_object_error_msg($relate_model) . '; data=' . json_encode($relate_model->toArray(), JSON_UNESCAPED_UNICODE), ErrCode::$BUSINESS_ERROR);
            }
        }

        // 获取负责人所属部门ID和名称
        $leader_node_dept_info = (new HrStaffRepository())->getStaffNodeDepartmentInfoByIds([$model->contract_leader_id])[$model->contract_leader_id] ?? [];
        $model->leader_node_department_id = $leader_node_dept_info['node_department_id'] ?? 0;
        $model->leader_node_department_name = $leader_node_dept_info['node_department_name'] ?? '';

        if ($model->save() === false) {
            throw new BusinessException('租房合同保存失败, 原因可能是:' . get_data_object_error_msg($model) . '; data=' . json_encode($model->toArray(), JSON_UNESCAPED_UNICODE), ErrCode::$BUSINESS_ERROR);
        }

        $contractId                  = $model->id;
        $contract_money_list         = $params['contract_money_list'] ?? [];
        $actual_monthly_payment_list = $params['actual_monthly_payment_list'] ?? [];

        $moneyIdsArr1                = array_column($contract_money_list, 'id');
        $moneyIdsArr2                = array_column($actual_monthly_payment_list, 'id');
        // 新版本通过amount_detai来取
        $moneyIdsArr                 = array_merge($moneyIdsArr1, $moneyIdsArr2);
        // 老数据禁止编辑
        if ($moneyIdsArr) {
            return [
                'error_msg' => 'old contract data save error'
            ];
        }

        //  添加附表amount_detail(租金信息)
        $this->saveAmountDetailData($params['amount_detail'], $contractId, ['contract_begin' => $params['contract_begin'], 'contract_end' => $params['contract_end']]);

        //新增 区域相关信息 一对多关系（原来是一对一） 直接删除 然后插入
        $this->saveCommonAreaDetailData($params['areaInfo'], $contractId);

        // 处理租房合同附件
        $this->saveCommonAttachmentFiles($params['attachment_list'], $model);

        // 返回租房合同主数据
        return $model->toArray();
    }

    /**
     * 处理租房合同税费信息数据
     *
     * @param array $area_info_detail
     * @param int $contract_id
     * @return bool
     * @throws BusinessException
     */
    protected function saveCommonAreaDetailData(array $area_info_detail = [], int $contract_id)
    {
        $old_area_models = ContractStoreRentingArea::find([
            'conditions' => 'contract_store_renting_id = :contract_store_renting_id:',
            'bind'       => ['contract_store_renting_id' => $contract_id]
        ]);
        $old_area_data = $old_area_models->toArray();
        if (!empty($old_area_data)) {
            if ($old_area_models->delete() === false) {
                throw new BusinessException('租房合同-税费信息老数据删除失败, 原因可能是:' . get_data_object_error_msg($old_area_models) . '; 数据=' . json_encode($old_area_data, JSON_UNESCAPED_UNICODE), ErrCode::$BUSINESS_ERROR);
            }
        }

        if (!empty($area_info_detail)) {
            $area_item = [];
            foreach ($area_info_detail as $v) {
                $area_item[] = [
                    'contract_store_renting_id' => $contract_id,
                    'start_time' => $v['area_start_time'] ? $v['area_start_time'] : null,
                    'end_time' => $v['area_end_time'] ? $v['area_end_time'] : null,
                    'area_service_amount_no_tax' => $v['area_service_amount_no_tax'] ? $v['area_service_amount_no_tax'] : 0,
                    'area_vat_rate' => $v['area_vat_rate'] ? $v['area_vat_rate'] : 0,
                    'area_service_amount_vat' => $v['area_service_amount_vat'] ? $v['area_service_amount_vat'] : 0,
                    'area_wht_category' => $v['area_wht_category'] ? $v['area_wht_category'] : 0,
                    'area_wht_rate' => $v['area_wht_rate'] ? $v['area_wht_rate'] : 0,
                    'area_amount_wht' => $v['area_amount_wht'] ? $v['area_amount_wht'] : 0,
                    'created_at' => date('Y-m-d H:i:s'),
                    'updated_at' => date('Y-m-d H:i:s'),
                    'tax_type' => $v['tax_type'] ?? 0,//税费类型
                    'tax_amount_has_wht' => bcadd($v['area_service_amount_no_tax'], $v['area_service_amount_vat'], 2),//税费金额（含WHT含VAT） = 税费金额（含WHT）+税费VAT税额
                    'tax_amount_no_wht' => bcsub(bcadd($v['area_service_amount_no_tax'], $v['area_service_amount_vat'], 2), $v['area_amount_wht'], 2),//税费金额（不含WHT含VAT = 税费金额（含WHT含VAT）-税费WHT金额
                ];
            }

            if ((new ContractStoreRentingArea())->batch_insert($area_item) === false) {
                throw new BusinessException('租房合同-税费信息批量写入失败, 数据=' . json_encode($area_item, JSON_UNESCAPED_UNICODE), ErrCode::$BUSINESS_ERROR);
            }
        }

        return true;
    }

    /**
     * 存储租房合同的附件数据
     *
     * @param array $attachment_list 新提交的附件
     * @param object $rent_model 租房合同Model
     * @return array
     * @throws BusinessException
     */
    protected function saveCommonAttachmentFiles(array $attachment_list, object $rent_model)
    {
        $return_data = [
            'old_attachments' => [],
            'new_attachments' => [],
        ];

        $attachment_ids = [];
        foreach ($attachment_list as $fileGroup) {
            if (empty($fileGroup)) continue;

            foreach ($fileGroup as $file) {
                if (!empty($file['id'])) {
                    $attachment_ids[] = $file['id'];
                }
            }
        }
        
        // 1. 查找租房合同的附件老数据
        $old_attachment_models = $rent_model->getSelfAttachments();
        $old_attachments = $old_attachment_models->toArray();
        $return_data['old_attachments'] = $old_attachments;

        $this->logger->info("租房合同老附件数据为[cno={$rent_model->contract_id}]:" . json_encode($old_attachments, JSON_UNESCAPED_UNICODE));

        // 2. 删数老附件
        $conditions = 'oss_bucket_key = :key: and oss_bucket_type in ({oss_bucket_type:array}) AND deleted = 0';
        $bind = ['key' => $rent_model->id, 'oss_bucket_type' => [ContractEnums::RENT_CONTRACT_FILE_OSS_BUCKET_TYPE]];
        if (!empty($attachment_ids)) {
            //没有历史附件需要保留，直接删除所有;有历史附件需要保留，则只需删除非保留的附件即可
            $conditions .= ' and id not in({attachment_ids:array})';
            $bind['attachment_ids'] = $attachment_ids;
        }
        $bool = AttachModel::find([
            'conditions' => $conditions,
            'bind'       => $bind
        ])->delete();
        if ($bool === false) {
            throw new BusinessException("合同原附件删除失败, oss_bucket_key={$rent_model->id}", ErrCode::$CONTRACT_UPDATE_ERROR);
        }

        // 3. 写入新附件(只写入附件配置中显示属性的附件)
        // 3.1 需显示的租房合同附件
        $file_config_item = $this->getShowAttachmentConfig($rent_model->is_main, $rent_model->house_owner_type);

        $new_file_item = [];
        foreach ($file_config_item as $field_name => $attr) {
            $current_files = $attachment_list[$field_name] ?? [];
            
            foreach ($current_files as $file) {
                //有附件id说明是原有的无需处理
                if (!empty($file['id'])) {
                    continue;
                }
                $new_file_item[] = [
                    'oss_bucket_key' => $rent_model->id,
                    'oss_bucket_type' => ContractEnums::RENT_CONTRACT_FILE_OSS_BUCKET_TYPE,
                    'sub_type' => $attr['file_sub_type'] ?? 0,
                    'bucket_name' => $file['bucket_name'],
                    'object_key' => $file['object_key'],
                    'file_name' => $file['file_name'],
                    'created_at' => date('Y-m-d H:i:s')
                ];
            }
        }

        $return_data['new_attachments'] = $new_file_item;

        $this->logger->info("租房合同新附件数据为[cno={$rent_model->contract_id}]:" . json_encode($new_file_item, JSON_UNESCAPED_UNICODE));

        if (!empty($new_file_item)) {
            if ((new SysAttachmentModel())->batch_insert($new_file_item) === false) {
                throw new BusinessException('租房合同新附件批量写入失败, contract_id = ' . $rent_model->contract_id, ErrCode::$BUSINESS_ERROR);
            }
        }

        return $return_data;
    }

    public function updateContract($param)
    {
        try {
            $contract_model = new ContractStoreRentingModel();
            $result_update  = $contract_model->update_record($param, $param['id'], 'id');
            if (!$result_update) {
                return 0;
                //return $this->ajax_return('error', 0, []);
            } else {
                return 1;
                //return $this->ajax_return('success', 1, []);
            }
        } catch (Exception $e) {
            return [$e->getMessage(), '-1', []];
        }
    }

    /**
     * 合同新增
     */
    public function addContract($param)
    {
        try {
            $contract_model = new ContractStoreRentingModel();
            $result_insert  = $contract_model->insert_record($param);
            if (!$result_insert) {
                return 0;
            } else {
                return 1;
            }
        } catch (Exception $e) {
            return [$e->getMessage(), '-1', []];
        }
    }

    /**
     * 保存到 contract 表
     * Created by: Lqz.
     * CreateTime: 2020/9/14 0014 15:03
     *
     * @param $contract
     * @param array $user
     * @return mixed
     * @throws BusinessException
     */
    public function saveRelationContract($contract, $user = [])
    {
        $contractModel = Contract::findFirst([
            'conditions' => 'cno = :cno:',
            'bind'       => ['cno' => $contract['contract_id']]
        ]);

        if (!$contractModel) {
            $contractModel = new Contract();

            // 新增时需初始化的字段
            $contractModel->create_id = $contract['manage_id'];
            $contractModel->create_name = $this->getNameAndNickName($user['name'] ?? '', $user['nick_name'] ?? '');
            $contractModel->create_department = $user['department'] ?? '';

            $contractModel->created_at = date('Y-m-d H:i:s');
            $contractModel->status = Enums::CONTRACT_STATUS_PENDING;
        }

        $contractModel->cno              = $contract['contract_id'];
        $contractModel->cname            = $contract['contract_name'];
        $contractModel->is_master        = ($contract['is_main'] == 1) ? Enums::CONTRACT_IS_MASTER_YES :$contract['is_main'];
        $contractModel->sub_cno          = $contract['main_contract_id'];
        $contractModel->payment_currency = $contract['money_symbol'];
        $contractModel->amount           = $contract['contract_money'];
        $contractModel->contract_type    = 'store_renting_contract';
        $contractModel->updated_at       = date('Y-m-d H:i:s');

        if ($contractModel->save() === false) {
            throw new BusinessException('租房合同数据同步到contract保存失败, 原因可能是:' . get_data_object_error_msg($contractModel) . ' ; 数据=' . json_encode($contractModel->toArray(), JSON_UNESCAPED_UNICODE), ErrCode::$BUSINESS_ERROR);
        }

        return true;
    }

    /**
     * 创建审批流
     * Created by: Lqz.
     *
     * @param $contract
     * @param $loginUser
     * CreateTime: 2020/9/14 0014 19:37
     * @param int $biz_type
     * @return array|Resultset|Model
     * @throws BusinessException
     * @throws ValidationException
     */
    public function saveWkFlow($contract, $loginUser, $biz_type = Enums::WF_STORE_RENTING_CONTRACT_TYPE)
    {
        $userDept = HrStaffInfoModel::getUserInfo($loginUser['id'], 'sys_department_id');
        if (!$userDept) {
            return [
                'error_msg' => 'sys_department_id not found'
            ];
        }

        $data['flow_id']  = $this->getFlowId($userDept, $contract, $biz_type);
        $data['id']       = $contract['id'];
        $data['name']     = $contract['contract_id'] . '审批申请';
        $data['biz_type'] = $biz_type;
        $info             = $this->_getWkOtherParams($contract, $loginUser, $biz_type);
        return (new WorkflowServiceV2())->createRequest($data, $loginUser, $info);
    }

    /**
     * 重新提交审批流
     *
     * @param $contract
     * @param $loginUser
     * @param int $biz_type
     * @return array|Resultset|Model
     * @throws BusinessException
     * @throws ValidationException
     * @date 2023/2/10
     */
    public function recommitWorkFlow($contract, $loginUser, $biz_type = Enums::WF_STORE_RENTING_CONTRACT_TYPE)
    {
        //租房合同申请 根据合同续约类型判断 走申请还是续签审批流
        if ($biz_type == Enums::WF_STORE_RENTING_CONTRACT_TYPE && $contract['renewal_contract_type'] == ContractEnums::RENEWAL_CONTRACT_TYPE_2) {
            $biz_type = Enums::WF_STORE_RENTING_CONTRACT_RENEWAL_TYPE;
        }
        $req = $this->_getWkReq($contract['id'], $biz_type);
        if (empty($req)) {
            throw new BusinessException("没有找到req=" . $contract['id'], ErrCode::$BUSINESS_ERROR);
        }

        //老的改成被遗弃
        $req->is_abandon = GlobalEnums::WORKFLOW_ABANDON_STATE_YES;
        $req->save();

        return $this->saveWkFlow($contract, $loginUser, $biz_type);
    }

    private function _getWkOtherParams($contract = [], $loginUser = [], $biz_type = '')
    {
        if ($contract['ver'] == 0) {
            $contract_money_list = $contract['contract_money_list'];
            $amountArr           = array_column($contract_money_list, 'amount');
            rsort($amountArr);
            $amount = $amountArr[0];
        } else {//新版审批流 汇率转换
            // 金额根据币种汇率转换为系统默认币种的额度
            $amount = (new EnumsService())->amountExchangeRateCalculation($contract['contract_total_amount'], $contract['exchange_rate'], 2);
        }


            $user_info          = (new UserService())->getLoginUser($contract['contract_leader_id']);
            $submitter_id       = $contract['contract_leader_id'];
            $node_department_id = $user_info['node_department_id'];
            $department_id      = $user_info['sys_department_id'];

        // 菲律宾的快递公司 Network Management
        $sys_department_ids = EnumsService::getInstance()->getSettingEnvValueMap('sys_department_ids');
        $store_category = (($sys_department_ids['network_management'] ?? '') == $department_id) ? 1 : 0;


        return [
            'amount'              => $amount,
            'currency'            => GlobalEnums::CURRENCY_THB,          // 泰铢
            'submitter_id'        => $submitter_id,
            'store_id'            => $contract['store_id'],
            'create_staff_id'     => $submitter_id,
            'contract_lang'       => $contract['contract_lang'],
            'contract_lease_type' => 2,//不在区分月租年租等一律走年组
            'node_department_id'  => $node_department_id,
            'department_id'       => $department_id,
            'create_company_id'   => $node_department_id,
            'store_category'      => $store_category,
        ];
    }

    /**
     * 获取审批流
     * Created by: Lqz.
     * @param $id
     * @param $uid
     * @param int $biz_type
     * @return array
     * CreateTime: 2020/9/14 0014 21:52
     */
    public function getWkflow($id, $uid, $biz_type = Enums::WF_STORE_RENTING_CONTRACT_TYPE)
    {
        //租房合同申请 根据合同续约类型判断 走申请还是续签审批流
        if ($biz_type == Enums::WF_STORE_RENTING_CONTRACT_TYPE) {
            $store_renting_model = ContractStoreRentingModel::findFirst([
                'conditions' => 'id = :id:',
                'bind'       => ['id' => $id]
            ]);

            if (!empty($store_renting_model) && $store_renting_model->renewal_contract_type == ContractEnums::RENEWAL_CONTRACT_TYPE_2) {
                $biz_type = Enums::WF_STORE_RENTING_CONTRACT_RENEWAL_TYPE;
            }
        }
        $wk_request = $this->_getWkReq($id, $biz_type);
        $wk_flow = [];
        $ask = [];
        if ($wk_request) {
            $wk_flow = (new WorkflowServiceV2())->getAuditLogs($wk_request);
            $ask = (new FYRService())->getRequestToByReplyAsk($wk_request, $uid);
        }
        return [$wk_flow,$ask];
    }

    public function _getWkReq($id, $biz_type = Enums::WF_STORE_RENTING_CONTRACT_TYPE)
    {
        //租房合同申请 根据合同续约类型判断 走申请还是续签审批流
        if ($biz_type == Enums::WF_STORE_RENTING_CONTRACT_TYPE) {
            $store_renting_model = ContractStoreRentingModel::findFirst([
                'conditions' => 'id = :id:',
                'bind'       => ['id' => $id]
            ]);

            if (!empty($store_renting_model) && $store_renting_model->renewal_contract_type == ContractEnums::RENEWAL_CONTRACT_TYPE_2) {
                $biz_type = Enums::WF_STORE_RENTING_CONTRACT_RENEWAL_TYPE;
            }
        }
        static $workRequest = [];
        if (empty($workRequest[$biz_type][$id])) {
            $workRequest[$biz_type][$id] = WorkflowRequestModel::findFirst(
                [
                    'conditions' => 'biz_type = :type: and biz_value= :lid: and is_abandon = :is_abandon:',
                    'bind' => ['type' => $biz_type, 'lid' => $id, 'is_abandon' => GlobalEnums::WORKFLOW_ABANDON_STATE_NO],
                    'order' => 'id DESC'
                ]
            );
        }
        return $workRequest[$biz_type][$id];
    }

    /**
     * 检测当前租房合同的申请审批是否处于AP节点待审批状态
     * @param $contract_id
     * @return bool
     * @throws ValidationException
     * @date 2023/1/12
     */
    public function checkApNode($contract_id)
    {
        $request = $this->_getWkReq($contract_id, Enums::WF_STORE_RENTING_CONTRACT_TYPE);
        if (!$request || $request->state != Enums::WF_STATE_PENDING || empty($request->current_flow_node_id)) {
            throw new ValidationException(static::$t->_('invalid_replace_contract_not_ap'), ErrCode::$VALIDATE_ERROR);
        }
        //查询当前审批节点是否有子节点
        $node = WorkflowNodeModel::findFirst($request->current_flow_node_id);
        if (!$node) {
            throw new ValidationException(static::$t->_('invalid_replace_contract_not_ap'), ErrCode::$VALIDATE_ERROR);
        }
        //AP节点的tag
        $ap_tag = [Enums::WF_NODE_TAG_AP_LOCAL, Enums::WF_NODE_TAG_AP_BEIJING, Enums::WF_NODE_TAG_APS_LOCAL, Enums::WF_NODE_TAG_APS_BEIJING];
        if ($node->node_audit_type == Enums::WF_NODE_AUDIT_TYPE_SUB_NODE_COUNTERSIGN) {
            //查询子节点中的AP节点
            $sub_node = WorkflowSubNodeModel::find(
                [
                    'columns' => 'id',
                    'conditions' => 'parent_node_id = :parent_node_id: and node_tag in ({node_tag:array)}',
                    'bind' => ['parent_node_id' => $node->id, 'node_tag' => $ap_tag],
                ]
            )->toArray();
            if (empty($sub_node)) {
                throw new ValidationException(static::$t->_('invalid_replace_contract_not_ap'), ErrCode::$VALIDATE_ERROR);
            }
            $sub_ap_node = array_values(array_column($sub_node, 'id'));
            //查询是否AP子节点待审批
            $is_sub_ap_audit = WorkflowRequestNodeAuditorModel::findFirst([
                'columns' => 'id',
                'conditions' => 'request_id = :request_id: and audit_status = :audit_status: and sub_node_id in ({sub_node_id:array)}',
                'bind' => [
                    'request_id' => $request->id,
                    'sub_node_id' => $sub_ap_node,
                    'audit_status' => Enums::WF_STATE_PENDING
                ],
            ]);
            if (!$is_sub_ap_audit) {
                throw new ValidationException(static::$t->_('invalid_replace_contract_not_ap'), ErrCode::$VALIDATE_ERROR);
            }
        } else {
            //当前节点是否是AP节点
            if (!in_array($node->node_tag, $ap_tag)) {
                throw new ValidationException(static::$t->_('invalid_replace_contract_not_ap'), ErrCode::$VALIDATE_ERROR);
            }
        }
        return true;
    }

    /**
     * 根据biz_type 查询所有ap节点和ap子节点审批中的业务主键
     * @param int $biz_type
     * @return array
     * @date 2023/1/17
     */
    public function getApNodeByBizType($biz_type = Enums::WF_STORE_RENTING_CONTRACT_TYPE)
    {
        //查询所有flow_id
        $request = WorkflowRequestModel::find([
            'columns' => 'id, flow_id',
            'conditions' => 'biz_type = :biz_type: and state = :state: and is_abandon = :is_abandon:',
            'bind' => [
                'biz_type' => $biz_type,
                'state' => Enums::WF_STATE_PENDING,
                'is_abandon' => GlobalEnums::WORKFLOW_ABANDON_STATE_NO
            ],
        ])->toArray();
        if (empty($request)) {
            return [];
        }
        $request_ids = array_values(array_unique(array_column($request, 'id')));
        $flow_ids = array_values(array_unique(array_column($request, 'flow_id')));
        $ap_tag = [Enums::WF_NODE_TAG_AP_LOCAL, Enums::WF_NODE_TAG_AP_BEIJING, Enums::WF_NODE_TAG_APS_LOCAL, Enums::WF_NODE_TAG_APS_BEIJING];
        //查询每个flow_id的ap节点
        $node_data = WorkflowNodeModel::find([
            'conditions' => 'flow_id in ({flow_id:array}) and (node_tag in ({node_tag:array}) or node_audit_type = :node_audit_type:)',
            'bind' => [
                'flow_id' => $flow_ids,
                'node_tag' => $ap_tag,
                'node_audit_type' => Enums::WF_NODE_AUDIT_TYPE_SUB_NODE_COUNTERSIGN
            ]
        ])->toArray();
        //主节点ap和子节点node_id
        $node_ap_id = $sub_node_id = [];
        foreach ($node_data as $node_info) {
            if ($node_info['node_audit_type'] == Enums::WF_NODE_AUDIT_TYPE_SUB_NODE_COUNTERSIGN) {
                $sub_node_id[] = $node_info['id'];
            } else {
                $node_ap_id[] = $node_info['id'];
            }
        }
        //查询子节点ap
        $sub_node_ap_id = [];
        if (!empty($sub_node_id)) {
            $sub_node_id = array_values(array_unique($sub_node_id));
            $sub_node = WorkflowSubNodeModel::find([
                'columns' => 'id',
                'conditions' => 'parent_node_id in ({parent_node_id:array}) and node_tag in ({node_tag:array})',
                'bind' => ['parent_node_id' => $sub_node_id, 'node_tag' => $ap_tag],
            ])->toArray();
            $sub_node_ap_id = array_column($sub_node, 'id');
        }

        //查询主节点和子节点AP审批的业务id
        $biz_value_node = $biz_value_sub_node = [];
        if (!empty($node_ap_id)) {
            $node_ap_id = array_values(array_unique($node_ap_id));
            //查询AP节点审批
            $ap_audit = WorkflowRequestNodeAuditorModel::find([
                'columns' => 'id,biz_value',
                'conditions' => 'request_id in ({request_ids:array}) and audit_status = :audit_status: and flow_node_id in ({flow_node_ids:array})',
                'bind' => [
                    'request_ids' => $request_ids,
                    'audit_status' => Enums::WF_STATE_PENDING,
                    'flow_node_ids' => $node_ap_id
                ]
            ])->toArray();
            $biz_value_node = array_values(array_unique(array_column($ap_audit, 'biz_value')));
        }
        if (!empty($sub_node_ap_id)) {
            $sub_node_ap_id = array_values(array_unique($sub_node_ap_id));
            //查询AP节点审批
            $ap_audit = WorkflowRequestNodeAuditorModel::find([
                'columns' => 'biz_value',
                'conditions' => 'request_id in ({request_ids:array}) and audit_status = :audit_status: and sub_node_id in ({sub_node_ids:array})',
                'bind' => [
                    'request_ids' => $request_ids,
                    'sub_node_ids' => $sub_node_ap_id,
                    'audit_status' => Enums::WF_STATE_PENDING
                ],
            ])->toArray();
            $biz_value_sub_node = array_values(array_unique(array_column($ap_audit, 'biz_value')));
        }

        return array_merge($biz_value_node, $biz_value_sub_node);
    }

    public function getMyList($loginUser, $params, $audit = false, $is_fyr = false, $apply = false)
    {
        $limit  = (isset($params['limit']) && !empty($params['limit'])) ? $params['limit'] : 20;
        $page   = (isset($params['page']) && !empty($params['page'])) ? $params['page'] : 1;
        $export = $params['export'] ?? false;
        $offset = $limit * ($page - 1);

        try {
            $builder = $this->modelsManager->createBuilder();
            $builder->from(['c' => ContractStoreRentingModel::class]);
            $builder = $this->getCondition($builder, $params, $loginUser, $audit, $is_fyr);

            $builder->columns('COUNT(DISTINCT c.id) AS total');
            $count = (int) $builder->getQuery()->getSingleResult()->total;

            $items = [];
            if ($count) {
                $columns = '
                    c.id,
                    c.manage_id,
                    c.contract_leader_id,
                    c.created_at,
                    c.contract_id,
                    c.contract_name,
                    c.store_id,
                    c.contract_begin,
                    c.contract_end,
                    c.house_contract_area,
                    c.house_actual_area,
                    c.contract_lease_type,
                    c.monthly_payment_type,
                    c.contract_status,
                    c.state,
                    date(c.apply_at) as apply_at,
                    c.ver,
                    c.money_symbol,
                    c.is_main,
                    c.invalid_status,
                    c.terminal_status,
                    c.renewal_contract_type,
                    c.is_renewal
                ';

                if ($audit) {
                    $columns .= ', request.biz_type, request.state as contract_status';
                    if (!empty($params['contract_status']) && !empty($params['flag']) && $params['flag'] == GlobalEnums::AUDIT_TAB_PROCESSED) {
                        if (!empty($params['biz_type']) && $params['biz_type'] == Enums::WF_STORE_RENTING_CONTRACT_INVALID_TYPE) {
                            $columns .= ', c.invalid_status as contract_status';
                        } else if (!empty($params['biz_type']) && $params['biz_type'] == Enums::WF_STORE_RENTING_CONTRACT_TERMINAL_TYPE) {
                            $columns .= ', c.terminal_status as contract_status';
                        }
                    }
                }

                // 审核模块的已处理列表, 展示处理时间
                if ($audit && isset($params['flag']) && $params['flag'] == GlobalEnums::AUDIT_TAB_PROCESSED) {
                    $columns .= ',log.audit_at';
                }

                $builder->columns($columns);
                $builder->groupBy('c.id');

                if (!$audit && !$is_fyr) {
                    $builder->orderBy('c.id desc');
                }

                if (!$export) {
                    $builder->limit($limit, $offset);
                }
                $items = $builder->getQuery()->execute()->toArray();
                $items = $this->handleItems($items, $apply);
            }

            $data  = [
                'items'      => $items,
                'pagination' => [
                    'current_page' => $page,
                    'page_limit'   => $limit,
                    'total_count'  => $count,
                ]
            ];
        } catch (Exception $e) {
            $msg    = $e->getMessage();
            $this->logger->warning('contract-getmylist-failed:' . $msg);
            return [
                'error_msg' => $msg
            ];
        }

        return $data;
    }

    /**
     * 获取意见征询回复待处理统计
     *
     * @param array $biz_type_item
     * @param int $user_id
     * @return mixed
     */
    public function getConsultationPendingCount(array $biz_type_item = [], int $user_id = 0)
    {
        if (empty($biz_type_item) || empty($user_id)) {
            return 0;
        }

        $builder = $this->modelsManager->createBuilder();
        $builder->from(['main' => ContractStoreRentingModel::class]);
        $builder->leftjoin(WorkflowRequestModel::class, 'request.biz_value = main.id', 'request');
        $builder->leftjoin(WorkflowRequestNodeAt::class, 'request.id = reply.request_id', 'reply');
        $builder->where('request.is_abandon = :is_abandon:', ['is_abandon' => GlobalEnums::WORKFLOW_ABANDON_STATE_NO]);
        $builder->inWhere('request.biz_type', $biz_type_item);
        $builder->andWhere('reply.staff_id = :current_user_id: AND reply.is_reply = :is_reply:', [
            'current_user_id' => $user_id,
            'is_reply' => GlobalEnums::CONSULTED_REPLY_STATE_PENDING
        ]);

        // 待审批 + 已归档: 已审批 + 非已作废/已终止
        $builder->leftjoin(ContractArchive::class, 'archive.cno = main.contract_id', 'archive');
        $where = 'request.state = :pending_state: OR (request.state = :approval_state: AND archive.status NOT IN ({archive_status:array}))';
        $conditions = [
            'pending_state' => Enums::WF_STATE_PENDING,
            'approval_state' => Enums::WF_STATE_APPROVED,
            'archive_status' => [
                ContractEnums::CONTRACT_ARCHIVE_STATUS_INVALID,
                ContractEnums::CONTRACT_ARCHIVE_STATUS_TERMINAL
            ],
        ];

        $builder->andWhere($where, $conditions);
        $builder->columns('COUNT(DISTINCT reply.id) AS total');
        return intval($builder->getQuery()->getSingleResult()->total);
    }

    private function getCondition($builder, $params, $loginUser, $audit, $is_fyr)
    {
        $uid = $loginUser['id'];

        $storeCate          = $params['store_cate'] ?? '';
        $storeId            = $params['store_id'] ?? '';
        $contractBeginLeft  = $params['contract_begin_left'] ?? '';
        $contractBeginRight = $params['contract_begin_right'] ?? '';
        $contractEndLeft    = $params['contract_end_left'] ?? '';
        $contractEndRight   = $params['contract_end_right'] ?? '';
        $leaseType          = $params['lease_type'] ?? '';
        $contractStatus     = $params['contract_status'] ?? '';
        $contractId         = $params['contract_id'] ?? '';
        $contractName       = $params['contract_name'] ?? '';
        $applyAtLeft        = $params['apply_at_left'] ?? '';
        $applyAtRight       = $params['apply_at_right'] ?? '';
        $is_main            = $params['is_main'] ?? '';
        $invalid_status = $params['invalid_status'] ?? 0;
        $terminal_status = $params['terminal_status'] ?? 0;
        $renewal_contract_type = $params['renewal_contract_type'] ?? '';
        $biz_type = $params['biz_type'] ?? '';
        $effective_status = $params['effective_status'] ?? 0;

        // 征询回复进度
        $is_reply           = $params['is_reply'] ?? GlobalEnums::CONSULTED_REPLY_STATE_PENDING;
        $is_reply           = in_array($is_reply, GlobalEnums::CONSULTED_REPLY_STATE_ITEM) ? $is_reply : GlobalEnums::CONSULTED_REPLY_STATE_PENDING;

        // 审批处理进度
        $flag               = $params['flag'] ?? GlobalEnums::AUDIT_TAB_PENDING;
        $flag               = in_array($flag, GlobalEnums::AUDIT_TAB_STATE_ITEM) ? $flag : GlobalEnums::AUDIT_TAB_PENDING;

        $staffId            = $params['staff_id'] ?? '';

        //审核列表
        if ($audit) {
            if (!empty($biz_type)) {
                $biz_types = [$biz_type];
            } else {
                $biz_types = [
                    Enums::WF_STORE_RENTING_CONTRACT_TYPE,
                    Enums::WF_STORE_RENTING_CONTRACT_INVALID_TYPE,
                    Enums::WF_STORE_RENTING_CONTRACT_TERMINAL_TYPE,
                    Enums::WF_STORE_RENTING_CONTRACT_RENEWAL_TYPE
                ];
            }

            $builder = (new WorkflowServiceV2())->getBizWorkflowOrderListByProcessState($builder, $flag, $biz_types, $uid, 'c');
            $contractStatus = '';
            if (in_array($biz_type, [Enums::WF_STORE_RENTING_CONTRACT_TYPE, Enums::WF_STORE_RENTING_CONTRACT_RENEWAL_TYPE])) {
                $contractStatus = $params['contract_status'] ?? '';
            } else if ($biz_type == Enums::WF_STORE_RENTING_CONTRACT_INVALID_TYPE) {
                $invalid_status = $params['contract_status'] ?? 0;
            } else if ($biz_type == Enums::WF_STORE_RENTING_CONTRACT_TERMINAL_TYPE) {
                $terminal_status = $params['contract_status'] ?? 0;
            }

        } else if ($is_fyr) {
            // 意见征询
            $builder->leftjoin(WorkflowRequestModel::class, 'request.biz_value = c.id', 'request');
            $builder->leftjoin(WorkflowRequestNodeAt::class, 'request.id = reply.request_id', 'reply');
            $builder->andWhere('request.is_abandon = :is_abandon:', ['is_abandon' => GlobalEnums::WORKFLOW_ABANDON_STATE_NO]);
            $builder->inWhere('request.biz_type', [Enums::WF_STORE_RENTING_CONTRACT_TYPE, Enums::WF_STORE_RENTING_CONTRACT_RENEWAL_TYPE]);
            $builder->andWhere('reply.staff_id = :current_user_id:', ['current_user_id' => $uid]);

            // 当前用户的回复数据
            switch ($is_reply) {
                // 待处理
                case GlobalEnums::CONSULTED_REPLY_STATE_PENDING:
                    $builder->andWhere('reply.is_reply = :is_reply:', ['is_reply' => GlobalEnums::CONSULTED_REPLY_STATE_PENDING]);

                    // 待审批 + 已归档: 已审批 + 非已作废/已终止
                    $builder->leftjoin(ContractArchive::class, 'archive.cno = c.contract_id', 'archive');
                    $where = 'request.state = :pending_state: OR (request.state = :approval_state: AND archive.status NOT IN ({archive_status:array}))';
                    $conditions = [
                        'pending_state' => Enums::WF_STATE_PENDING,
                        'approval_state' => Enums::WF_STATE_APPROVED,
                        'archive_status' => [
                            ContractEnums::CONTRACT_ARCHIVE_STATUS_INVALID,
                            ContractEnums::CONTRACT_ARCHIVE_STATUS_TERMINAL
                        ],
                    ];

                    $builder->andWhere($where, $conditions);
                    $builder->orderBy('reply.created_at ASC');
                    break;

                // 已处理
                case GlobalEnums::CONSULTED_REPLY_STATE_PROCESSED:
                    $builder->andWhere('reply.is_reply = :is_reply:', ['is_reply' => GlobalEnums::CONSULTED_REPLY_STATE_PROCESSED]);
                    $builder->orderBy('reply.reply_at DESC');
                    break;
            }

        } else {
            //如果uid不为NULL，数据或者下载
            if ($uid) {
                $builder->andWhere('c.manage_id = :uid:', ['uid' => $uid]);
                $builder->orWhere('c.contract_leader_id = :contract_leader_id:', ['contract_leader_id' => $uid]);
            }

            //V22497 合同有效状态
            if (!empty($effective_status)) {
                $builder->andWhere('c.effective_status = :effective_status:', ['effective_status' => $effective_status]);
            }
        }

        if (!empty($contractStatus)) {
            $builder->andWhere('c.contract_status = :status:', ['status' => $contractStatus]);
        }
        //作废审批状态
        if (!empty($invalid_status)) {
            $builder->andWhere('c.invalid_status = :invalid_status:', ['invalid_status' => $invalid_status]);
        }
        //终止审批状态
        if (!empty($terminal_status)) {
            $builder->andWhere('c.terminal_status = :terminal_status:', ['terminal_status' => $terminal_status]);
        }


        if (!empty($is_main)) {
            $builder->andWhere('c.is_main = :is_main:', ['is_main' => $is_main]);
        }

        if (!empty($storeCate)) {
            $builder->andWhere('c.store_cate = :storeCate:', ['storeCate' => $storeCate]);
        }

        if (!empty($storeId)) {
            $builder->andWhere('c.store_id = :storeId:', ['storeId' => $storeId]);
        }
        if (!empty($contractBeginLeft)) {
            $builder->andWhere('c.contract_begin >= :contractBeginLeft: and c.contract_begin <= :contractBeginRight:', ['contractBeginLeft' => date('Y-m-d 00:00:00', strtotime($contractBeginLeft))]);
        }

        if (!empty($contractBeginRight)) {
            $builder->andWhere('c.contract_begin <= :contractBeginRight:', ['contractBeginRight' => date('Y-m-d 23:59:59', strtotime($contractBeginRight))]);
        }

        if (!empty($contractEndLeft)) {
            $builder->andWhere('c.contract_end >=:contractEndLeft:', ['contractEndLeft' => date('Y-m-d 00:00:00', strtotime($contractEndLeft))]);
        }

        if (!empty($contractEndRight)) {
            $builder->andWhere('c.contract_end <=:contractEndRight: ', ['contractEndRight' => date('Y-m-d 23:59:59', strtotime($contractEndRight))]);
        }

        if (!empty($applyAtLeft)) {
            $builder->andWhere('c.created_at >=:applyAtLeft:', ['applyAtLeft' => date('Y-m-d 00:00:00', strtotime($applyAtLeft))]);
        }

        if (!empty($applyAtRight)) {
            $builder->andWhere('c.created_at <=:applyAtRight: ', ['applyAtRight' => date('Y-m-d 23:59:59', strtotime($applyAtRight))]);
        }

        if (!empty($leaseType)) {
            $builder->andWhere('c.contract_lease_type = :leaseType:', ['leaseType' => $leaseType]);
        }

        if (!empty($contractId)) {
            $builder->andWhere('c.contract_id = :contractId:', ['contractId' => $contractId]);
        }

        if (!empty($contractName)) {
            $builder->andWhere('c.contract_name = :contractName:', ['contractName' => $contractName]);
        }

        if ($audit != true && !empty($staffId)) {
            $builder->orWhere("(c.manage_id=:manage_id: and apply_at is Null)", ['manage_id' => $staffId]);
        }

        if (!empty($renewal_contract_type)) {
            $builder->andWhere('c.renewal_contract_type = :renewal_contract_type:', ['renewal_contract_type' => $renewal_contract_type]);
        }
        return $builder;
    }

    /**
     * 处理每组数据
     * Created by: Lqz.
     *
     * @param $items
     * @param bool $apply
     * @return array
     * CreateTime: 2020/9/15 0015 23:18
     */
    private function handleItems($items, $apply = false)
    {
        $manageIds = array_column($items, 'manage_id');
        $staffs    = [];
        $depts     = [];
        //查询员工和部门
        if ($manageIds) {
            $staffsObj = HrStaffInfoModel::find([
                'conditions' => 'staff_info_id in ({manageIds:array})',
                'bind'       => [
                    'manageIds' => $manageIds
                ],
                'column'     => 'staff_info_id,name,sys_department_id,nick_name'
            ]);
            $staffs    = $staffsObj->toArray();
            $staffs    = array_column($staffs, null, 'staff_info_id');
            $deptIds   = array_column($staffs, 'sys_department_id');
            if ($deptIds) {
                $deptsObj = SysDepartmentModel::find([
                    'conditions' => 'id in ({deptIds:array})',
                    'bind'       => [
                        'deptIds' => $deptIds
                    ],
                    'columns'    => 'id,name'
                ]);
                $depts    = $deptsObj->toArray();
                $depts    = array_column($depts, null, 'id');
            }
        }

        // 申请列表的专有处理
        $approvaled_biz_ids = [];// 已开始审批的单据ID列表
        if ($apply) {
            // 查询归档状态
            $contract_no_ids = array_values(array_filter(array_unique(array_column($items, 'contract_id'))));
            if (!empty($contract_no_ids)) {
                $contract_no_arr = ContractArchive::find([
                    'conditions' => 'cno in ({cno:array})',
                    'bind'       => [
                        'cno' => $contract_no_ids
                    ],
                    'columns'    => 'cno,status'
                ])->toArray();

                $contract_no_status = array_column($contract_no_arr, 'status', 'cno');
            }

            // 判断合同是否可编辑
            $biz_ids = array_column($items, 'id');
            $biz_types = [
                Enums::WF_STORE_RENTING_CONTRACT_TYPE,
                Enums::WF_STORE_RENTING_CONTRACT_RENEWAL_TYPE
            ];

            // 获取已开始审批的单据列表(含终态)
            $approvaled_biz_ids = (new WorkflowServiceV2())->getApprovedWorkflowBizValueList($biz_ids, $biz_types);
        }

        //查询合同金额

        //新老版本数据区分（）
        $ver = [0,1];
        $store_renting_contract_id = [];
        foreach ($items as $value) {
            foreach ($ver as $v1) {
                if ($value['ver'] == $v1) {
                    $store_renting_contract_id[$v1][] = $value['id'];
                }
            }
        }
        $moneyArr = [];

        //新老版本数据处理
        $moneyArr1 =$moneyArr2=[];
        if (isset($store_renting_contract_id[0]) && is_array($store_renting_contract_id[0])) {//老版本数据处理

                $moneyObj = ContractStoreRentingMoneyModel::find([
                    'conditions' => 'store_renting_contract_id in ({store_renting_contract_id:array}) and type = :type:',
                    'bind'       => [
                        'store_renting_contract_id' => $store_renting_contract_id[0],
                        'type'                      => 'contract_money'
                    ],
                    'group'      => 'store_renting_contract_id',
                    'columns'    => 'store_renting_contract_id, max(amount) as amount'
                ]);
                $moneyArr1 = $moneyObj->toArray();
                $moneyArr1 = array_column($moneyArr1, 'amount', 'store_renting_contract_id');


        }
        if (isset($store_renting_contract_id[1])&&is_array($store_renting_contract_id[1])) {

            $moneyObj = ContractStoreRentingDetailModel::find([
                'conditions' => 'contract_store_renting_id in ({contract_store_renting_id:array}) ',
                'bind'       => [
                    'contract_store_renting_id' => $store_renting_contract_id[1],
                ],
                'group'      => 'contract_store_renting_id',
                'columns'    => 'contract_store_renting_id, max(amount_no_tax) as amount'
            ]);
            $moneyArr2 = $moneyObj->toArray();
            $moneyArr2 = array_column($moneyArr2, 'amount', 'contract_store_renting_id');
        }

        $moneyArr = $moneyArr1 + $moneyArr2;

        $storeIds  = array_column($items, 'store_id');
        $storeList = [];
        if ($storeIds) {
            $where = 'id in ({storeIds:array})';

            $storeObj  = SysStoreModel::find([
                'conditions' => $where,
                'bind'       => [
                    'storeIds' => $storeIds
                ],
                'columns'    => 'id,name'
            ]);
            $storeList = $storeObj->toArray();
            $storeList = array_column($storeList, 'name', 'id');
        }
        $leaseTypes  = $this->getContractLeaseTypes();
        $auditStatus = $this->getAuditStatus();
        $renewal_type = array_column($this->getLangRenewalType(), 'name', 'id');
        $biz_type_arr = array_column($this->getApproveType(), 'name', 'id');

        $staff_ids  = array_values(array_unique(array_column($items, 'contract_leader_id')));

        if (!empty($staff_ids)) {
            $user_infos = HrStaffInfoModel::find([
                'conditions' => 'staff_info_id in ({ids:array})',
                'bind'       => ['ids' => $staff_ids],
                'columns'    => 'name,nick_name,staff_info_id'

            ])->toArray();
            $user_infos = array_column($user_infos, null, 'staff_info_id');
        }

        foreach ($items as &$item) {
            $user_info = $user_infos[$item['contract_leader_id']] ?? '';

            $_deptId                     = isset($staffs[$item['manage_id']]) ? $staffs[$item['manage_id']]['sys_department_id'] : '';
            $_staffName                  = isset($staffs[$item['manage_id']]) ? $staffs[$item['manage_id']]['name']."({$staffs[$item['manage_id']]['nick_name']})": '-';
            $_deptName                   = isset($depts[$_deptId]) ? $depts[$_deptId]['name'] : '-';
            $item['dept_name']           = $_deptName;
            $item['staff_name']          = $_staffName;
            $item['apply_at']            = !empty($item['apply_at']) ? $item['apply_at'] : $item['created_at'];
            $item['store_name']          = $storeList[$item['store_id']] ?? $item['store_id'];
            $item['lease_type_txt']      = $leaseTypes[$item['contract_lease_type']] ?? '-';
            $item['contract_status_txt'] = isset($auditStatus[$item['contract_status']]) ? $auditStatus[$item['contract_status']] : $this->getOldStatusTxt($item['state']);
            $money                       = isset($moneyArr[$item['id']]) ? ($moneyArr[$item['id']] ?? 0) : $item['contract_money'];
            $item['contract_money_fmt']  = number_format($money, 2);
            $item['archive_status_txt']  = !empty($item['status']) ? self::$t->_('contract_archive_status.' . $item['status']) : '-';
            $item['_created_at']         =  $item['created_at'];
            $item['money_symbol_txt']    = static::$t[GlobalEnums::$currency_item[$item['money_symbol']]];
            $item['invalid_status_txt']  = isset($auditStatus[$item['invalid_status']]) ? $auditStatus[$item['invalid_status']] : '-';
            $item['terminal_status_txt']  = isset($auditStatus[$item['terminal_status']]) ? $auditStatus[$item['terminal_status']] : '-';
            $item['is_show_renewal']    = ContractEnums::IS_NO_SHOW_RENEWAL;

            // 默认不可编辑
            $item['can_edit'] = 0;

            $item['is_can_recommit'] = $apply && in_array($item['contract_status'], [Enums::CONTRACT_STATUS_REJECTED, Enums::CONTRACT_STATUS_CANCEL]);
            if ($apply) {
                // 申请页: 是否可编辑 -> 已开始审批或终态的单据, 不可编辑; 第一个节点未开始审批的, 可编辑
                $item['can_edit'] = in_array($item['id'], $approvaled_biz_ids) ? 0 : 1;

                if ($item['is_renewal'] == ContractEnums::IS_RENEWAL_1 && $item['contract_status'] == Enums::CONTRACT_STATUS_APPROVAL && in_array($contract_no_status[$item['contract_id']] ?? '', [ContractEnums::CONTRACT_ARCHIVE_STATUS_APPROVAL, ContractEnums::CONTRACT_ARCHIVE_STATUS_TERMINAL]) && in_array($item['is_main'], [Enums::CONTRACT_IS_MASTER_YES, Enums::CONTRACT_IS_LOI_YES])) {
                    $item['is_show_renewal'] = ContractEnums::IS_SHOW_RENEWAL;

                }
            }
            $item['renewal_contract_type_text'] = $renewal_type[$item['renewal_contract_type']];
            $item['is_main'] = $this->getLangIsMainName()[$item['is_main']];

            $item['biz_type_text'] = empty($item['biz_type']) ? '' : $biz_type_arr[$item['biz_type']];
            $item['contract_leader_name'] = $this->getNameAndNickName($user_info['name'] ?? '', $user_info['nick_name'] ?? '');

        }

        return $items;
    }

    public function cancel($loginUser, $params)
    {
        $cancelReason = $params['cancel_reason'];
        $id           = $params['id'];
        $uid          = $loginUser['id'];

        $wkRequest = $this->_getWkReq($id);
        if (empty($wkRequest)) {
            throw new ValidationException('workflow info null', ErrCode::$VALIDATE_ERROR);
        }
        $contract = ContractStoreRentingModel::findFirst([
            'conditions' => 'id = :id: and contract_leader_id = :contract_leader_id:',
            'bind'       => [
                'id' => $id,
                'contract_leader_id' => $uid
            ],
            'for_update' => true,
        ]);

        if (empty($contract)) {
            throw new ValidationException(static::$t->_('biz_order_data_is_null', ['biz_id' => $id]), ErrCode::$VALIDATE_ERROR);
        }

        if ($contract->contract_status != Enums::CONTRACT_STATUS_PENDING) {
            throw new ValidationException(static::$t->_('contract_withdrawal_failed'), ErrCode::$VALIDATE_ERROR);
        }

        $contract_update_data = [
            'cancel_reason' => $cancelReason,
            'contract_status' => Enums::CONTRACT_STATUS_CANCEL,
        ];
        if ($contract->i_update($contract_update_data) === false) {
            throw new BusinessException("租房合同撤回-主表更新失败[id={$id}],原因可能是=" . get_data_object_error_msg($contract), ErrCode::$BUSINESS_ERROR);
        }

        $contractArr                        = $contract->toArray();
        $contractArr['contract_money_list'] = [];
        if ($contract->ver == 0) {
            $listObj = ContractStoreRentingMoneyModel::find([
                'conditions' => 'store_renting_contract_id = :store_renting_contract_id: and type = :type:',
                'bind'       => [
                    'store_renting_contract_id' => $id,
                    'type'                      => 'contract_money'
                ]

            ]);

            $contractArr['contract_money_list'] = $listObj->toArray();
        }

        $tableContractObj                   = self::getTableContractData($contract->contract_id);
        if ($tableContractObj) {
            $tableContractObj->status = Enums::CONTRACT_STATUS_CANCEL;
            $tableContractObj->updated_at = date('Y-m-d H:i:s');
            if ($tableContractObj->save() === false) {
                throw new BusinessException("租房合同撤回-合同表更新失败[id={$contract->contract_id}],原因可能是=" . get_data_object_error_msg($tableContractObj), ErrCode::$BUSINESS_ERROR);
            }
        }

        $info = $this->_getWkOtherParams($contractArr, $loginUser);
        $result = (new WorkflowServiceV2())->doCancel($wkRequest, $loginUser, $info, $cancelReason);
        if ($result === false) {
            throw new BusinessException('workflow cancel error', ErrCode::$BUSINESS_ERROR);
        }

        return true;
    }


    public function getAuditStatus()
    {
        $auditStatus = Enums::$contract_status;
        foreach ($auditStatus as &$status) {
            $status = self::$t->_($status);
        }
        return $auditStatus;
    }

    /**
     * 审批操作 用来分配当前审批的是哪个业务流程(申请,作废,终止)
     * @param $params
     * @param $user
     * @param string $action 可选 'approve' | 'reject'
     * @return array|mixed
     * @throws BusinessException
     * @throws ValidationException
     * @date 2023/1/11
     */
    public function auditMain($params, $user, $action = 'approve')
    {
        //查询当前处在哪个审批类型
        $request = WorkflowRequestModel::findFirst(
            [
                'conditions' => 'biz_type in ({biz_type:array}) and biz_value= :lid: and state = :state: and is_abandon = :is_abandon:',
                'bind' => [
                    'biz_type' => [Enums::WF_STORE_RENTING_CONTRACT_TYPE, Enums::WF_STORE_RENTING_CONTRACT_INVALID_TYPE, Enums::WF_STORE_RENTING_CONTRACT_TERMINAL_TYPE, Enums::WF_STORE_RENTING_CONTRACT_RENEWAL_TYPE],
                    'lid' => $params['id'],
                    'state' => Enums::WF_STATE_PENDING,
                    'is_abandon' => GlobalEnums::WORKFLOW_ABANDON_STATE_NO
                ]
            ]
        );
        if (!$request) {
            $this->logger->info('审批失败-数据异常,未查询到审批数据 params=: ' . json_encode($params, JSON_UNESCAPED_UNICODE));
            throw new ValidationException(static::$t->_('work_flow_request_empty_data'), ErrCode::$VALIDATE_ERROR);
        }
        if (in_array($request->biz_type, [Enums::WF_STORE_RENTING_CONTRACT_TYPE, Enums::WF_STORE_RENTING_CONTRACT_RENEWAL_TYPE])) {
            if ($action == 'approve') {
                return $this->approve($user, $params);
            }
            return $this->reject($user, $params);
        } else {
            //作废和终止审批
            if ($action == 'approve') {
                return $this->approveInvalid($user, $params, $request->biz_type);
            }
            return $this->rejectInvalid($user, $params, $request->biz_type);
        }
    }
    /**
     * 审核通过
     * Created by: Lqz.
     *
     * @param $loginUser
     * @param $params
     * @return mixed
     * CreateTime: 2020/9/16 0016 20:00
     */
    public function approve($loginUser, $params)
    {
        $code = ErrCode::$SUCCESS;
        $message = 'success';

        $db = $this->getDI()->get('db_oa');
        $db->begin();

        try {
            $remark   = $params['remark'];
            $id       = $params['id'];
            $update_data = $params['update_data'] ?? [];
            $uid      = $loginUser['id'];

            $wkRequest = $this->_getWkReq($id);
            if (empty($wkRequest)) {
                throw new ValidationException(static::$t->_('work_flow_request_empty_data'), ErrCode::$VALIDATE_ERROR);
            }

            $contract = ContractStoreRentingModel::findFirst([
                'conditions' => 'id = :id:',
                'bind'       => ['id' => $id],
                'for_update' => true,
            ]);

            if (empty($contract)) {
                throw new ValidationException(static::$t->_('store_renting_contract_info_null', ['sr_id' => $id]), ErrCode::$VALIDATE_ERROR);
            }

            if ($contract->contract_status != Enums::WF_STATE_PENDING) {
                throw new ValidationException(static::$t->_('workflow_action_status_check_error'), ErrCode::$VALIDATE_ERROR);
            }

            if (!isset($contract->apply_at)) {
                throw new ValidationException(static::$t->_('rent_contract_apply_at_error'), ErrCode::$VALIDATE_ERROR);
            }

            $contractArr                        = $contract->toArray();
            $contractArr['contract_money_list'] = [];
            if ($contract->ver == 0) {
                $listObj = ContractStoreRentingMoneyModel::find([
                    'conditions' => 'store_renting_contract_id = :store_renting_contract_id: and type = :type:',
                    'bind'       => [
                        'store_renting_contract_id' => $id,
                        'type'                      => 'contract_money'
                    ]
                ]);

                $contractArr['contract_money_list'] = $listObj->toArray();
            }

            // 如果更新数据不为空
            if (!empty($update_data)) {
                $can_edit_field = (new ContractFlowService())->getCanEditFieldByReq($wkRequest, $uid);
                if (!empty($can_edit_field)) {
                    $this->dealEditField($contract, $can_edit_field, $update_data, $wkRequest, $loginUser);
                }
            }

            $info = $this->_getWkOtherParams($contractArr, $loginUser);
            $result = (new WorkflowServiceV2())->doApprove($wkRequest, $loginUser, $info, $remark);
            if ($result === false) {
                throw new BusinessException('网点租房合同审批失败, 请检查; info = ' . json_encode($info, JSON_UNESCAPED_UNICODE), ErrCode::$BUSINESS_ERROR);
            }

            if (!empty($result->approved_at)) {
                $contract->contract_status = Enums::CONTRACT_STATUS_APPROVAL;
                $contract->approved_at     = $result->approved_at;
                if ($contract->save() === false) {
                    throw new BusinessException('网点租房合同审批通过, 租房合同表更新失败, 原因可能是: ' . get_data_object_error_msg($contract) . '; 数据: ' . json_encode($contract->toArray(), JSON_UNESCAPED_UNICODE), ErrCode::$BUSINESS_ERROR);
                }

                $tableContractObj          = self::getTableContractData($contract->contract_id);
                if ($tableContractObj) {
                    $tableContractObj->status      = Enums::CONTRACT_STATUS_APPROVAL;
                    $tableContractObj->approved_at = $result->approved_at;
                    if ($tableContractObj->save() === false) {
                        throw new BusinessException('网点租房合同审批通过, contract表同步更新失败, 原因可能是: ' . get_data_object_error_msg($tableContractObj) . '; 数据: ' . json_encode($tableContractObj->toArray(), JSON_UNESCAPED_UNICODE), ErrCode::$BUSINESS_ERROR);
                    }
                }

                // 归档处理
                $this->saveArchive($id, $loginUser);

                // 合同提交/重新提交 | 续签, 终审通过, 若关联了仓库, 则回更仓库相关网点信息
                $country_code = get_country_code();
                if (in_array($country_code, [GlobalEnums::TH_COUNTRY_CODE, GlobalEnums::PH_COUNTRY_CODE, GlobalEnums::MY_COUNTRY_CODE]) && !empty($contract->warehouse_id)) {
                    $sync_contract_data = [
                        'contract_no'    => $contract->contract_id,
                        'warehouse_id'   => $contract->warehouse_id,
                        'store_id'       => $contract->store_id,
                        'store_category' => $contract->store_cate,
                        'begin_date'     => $contract->contract_begin,
                        'created_id'     => $contract->manage_id,
                    ];
                    WarehouseService::getInstance()->syncRelatedWarehouseData($sync_contract_data);
                }

                // V21475 网点租房合同审批通过,如果关联了仓库并且仓库关联了线索，并且线索状态为签约流程中时将线索状态更新为待付款，将线索关联的仓库需求状态更新为待付款;如果线索关联的仓库验证状态为待验证，则将仓库验证状态更新为取消验证
                if (in_array($country_code, [GlobalEnums::PH_COUNTRY_CODE, GlobalEnums::TH_COUNTRY_CODE]) && !empty($contract->warehouse_id)) {
                    switch ($country_code) {
                        case GlobalEnums::TH_COUNTRY_CODE:
                            ThreadService::getInstance()->syncRelatedTheadApproveTH($contract->warehouse_id, $loginUser);
                            break;
                        default:
                            ThreadService::getInstance()->syncRelatedTheadApprove($contract->warehouse_id, $loginUser);
                            break;
                    }
                }

            }

            $db->commit();

        } catch (ValidationException $e) {
            $db->rollback();

            $code    = $e->getCode();
            $message = $e->getMessage();
        } catch (BusinessException $e) {
            $db->rollback();

            $code    = $e->getCode();
            $message = $this->t->_('retry_later');
            $this->logger->warning('租房合同审批通过异常: ' . $e->getMessage());
        } catch (Exception $e) {
            $db->rollback();

            $code    = $e->getCode();
            $message = $this->t->_('retry_later');
            $this->logger->error('租房合同审批通过异常: ' . $e->getMessage());
        }

        return [
            'code'    => $code,
            'message' => $message,
            'data'    => [],
        ];
    }

    public static function getTableContractData($con)
    {
        return Contract::findFirst([
            'conditions' => 'cno = :cno:',
            'bind'       => [
                'cno' => $con
            ]
        ]);
    }

    /**
     * 审核驳回
     * Created by: Lqz.
     * @param $loginUser
     * @param $params
     * @return array|bool|string[]
     * @throws BusinessException
     * @throws ValidationException
     * CreateTime: 2020/9/16 0016 20:36
     */
    public function reject($loginUser, $params)
    {
        $remark   = $params['remark'];
        $id       = $params['id'];

        $wkRequest = $this->_getWkReq($id);
        if (empty($wkRequest)) {
            throw new ValidationException('workflow info null', ErrCode::$VALIDATE_ERROR);
        }

        $contract = ContractStoreRentingModel::findFirst([
            'conditions' => 'id = :id:',
            'bind'       => ['id' => $id],
            'for_update' => true,
        ]);
        if ($contract->contract_status == Enums::CONTRACT_STATUS_CANCEL) {
            throw new ValidationException(static::$t->_('contract_has_been_withdrawal'), ErrCode::$VALIDATE_ERROR);
        }

        if (!isset($contract->apply_at)) {
            throw new ValidationException('server reject  error!', ErrCode::$VALIDATE_ERROR);
        }

        $contractArr                        = $contract->toArray();

        $contractArr['contract_money_list'] = [];
        if ($contract->ver == 0) {
            $listObj = ContractStoreRentingMoneyModel::find([
                'conditions' => 'store_renting_contract_id = :store_renting_contract_id: and type = :type:',
                'bind'       => [
                    'store_renting_contract_id' => $id,
                    'type'                      => 'contract_money'
                ]

            ]);

            $contractArr['contract_money_list'] = $listObj->toArray();
        }

        $contract->contract_status = Enums::CONTRACT_STATUS_REJECTED;
        $contract->rejected_at     = date('Y-m-d H:i:s');
        $contract->pdf_reject_name = json_encode($params['pdf_reject_name'], JSON_UNESCAPED_UNICODE);
        if ($contract->save() === false) {
            throw new BusinessException('save contract reject_at error', ErrCode::$BUSINESS_ERROR);
        }

        $info = $this->_getWkOtherParams($contractArr, $loginUser);
        $result = (new WorkflowServiceV2())->doReject($wkRequest, $loginUser, $info, $remark);
        if ($result === false) {
            throw new BusinessException('workflow reject error', ErrCode::$BUSINESS_ERROR);
        }

        return true;
    }

    /**
     * 合同作废验证
     * @param $params
     * @throws ValidationException
     * @date 2023/1/9
     */
    public function invalidValidate($params)
    {
        $base_validate = ContractStoreRentingService::$invalid_validate;
        if (in_array(get_country_code(), [GlobalEnums::TH_COUNTRY_CODE, GlobalEnums::MY_COUNTRY_CODE])) {
            $base_validate['invalid_reason_type'] = 'Required|IntIn:' . ContractEnums::VALIDATE_INVALID_REASON_TYPE;
        }
        Validation::validate($params, $base_validate);
        // 1. 租赁关系解除，已支付款项退回
        if (isset($params['invalid_reason_type']) && $params['invalid_reason_type'] == ContractEnums::INVALID_REASON_TYPE_RELIEVE) {
            //基础校验
            $tmp_validate = [
                'invalid_refund_method' => 'Required|IntIn:' . ContractEnums::VALIDATE_INVALID_REFUND_METHOD,
                'invalid_refund_begin' => 'Required|Date',
                'invalid_refund_end' => 'Required|Date',
            ];
            Validation::validate($params, $tmp_validate);

            //额外校验开始时间,结束时间
            if ($params['invalid_refund_begin'] < date('Y-m-d')) {
                throw new ValidationException(static::$t->_('invalid_refund_begin_before_today'), ErrCode::$VALIDATE_ERROR);
            }
            if ($params['invalid_refund_end'] > date('Y-m-d', strtotime('+1 year', strtotime($params['invalid_refund_begin'])))) {
                throw new ValidationException(static::$t->_('invalid_refund_begin_after_year'), ErrCode::$VALIDATE_ERROR);
            }
        } elseif (isset($params['invalid_reason_type']) && $params['invalid_reason_type'] == ContractEnums::INVALID_REASON_TYPE_RE_SIGN) {
            // 2. 重新签订合同，已支付款项转移至新合同
            //基础校验
            $tmp_validate = [
                'invalid_replace_cno' => 'Required|StrLenGeLe:1,20',
                'invalid_replace_begin' => 'Required|Date',
                'invalid_replace_end' => 'Required|Date',
            ];
            Validation::validate($params, $tmp_validate);

            //额外校验合同状态是否AP审批中
            $contract = ContractStoreRentingModel::findFirst([
                'conditions' => 'contract_id = :contract_id:',
                'bind' => ['contract_id' => $params['invalid_replace_cno']]
            ]);
            if (!$contract) {
                throw new ValidationException(static::$t->_('invalid_replace_contract_not_exist'), ErrCode::$VALIDATE_ERROR);
            }
            if ($contract->contract_status != Enums::CONTRACT_STATUS_PENDING) {
                throw new ValidationException(static::$t->_('invalid_replace_contract_status_error'), ErrCode::$VALIDATE_ERROR);
            }
            //查询合同是否在AP节点
            $this->checkApNode($contract->id);
            //开始时间结束时间必须在合同时间范围内
            if ($params['invalid_replace_begin'] < $contract->contract_begin) {
                throw new ValidationException(static::$t->_('invalid_replace_different_contract'), ErrCode::$VALIDATE_ERROR);
            }
            if ($params['invalid_replace_end'] > $contract->contract_end) {
                throw new ValidationException(static::$t->_('invalid_replace_different_contract'), ErrCode::$VALIDATE_ERROR);
            }
        }
    }
    /**
     * 归档合同作废
     * Created by: Wq.
     * @param array $params
     * @param  array $user
     * @return array
     * CreateTime: 2021/10/19 0016 20:36
     */
    public function invalid($params, $user)
    {
        $code = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $data = [];
        $db = $this->getDI()->get('db_oa');
        $db->begin();
        try {
            $contract_archive = ContractArchive::findFirst([
                'conditions' => 'id = :id:',
                'bind' => [
                    'id' => $params['id']
                ],
                'for_update' => true,
            ]);
            if (!isset($contract_archive->id)) {
                throw new ValidationException(static::$t->_('archive_contract_is_not_exist'), ErrCode::$VALIDATE_ERROR);
            }
            if (in_array($contract_archive->status, [ContractEnums::CONTRACT_ARCHIVE_STATUS_INVALID, ContractEnums::CONTRACT_ARCHIVE_STATUS_INVALID_ING])) {
                throw new ValidationException(static::$t->_('contract_has_been_invalided'), ErrCode::$VALIDATE_ERROR);
            }
            if (!in_array($contract_archive->status, [ContractEnums::CONTRACT_ARCHIVE_STATUS_APPROVAL,
                ContractEnums::CONTRACT_ARCHIVE_STATUS_NO_UPLOAD])) {
                throw new ValidationException(static::$t->_('archive_contract_status_is_not_approval'), ErrCode::$VALIDATE_ERROR);
            }
            //记录修改前的状态,驳回时回退到此状态
            $contract_archive->before_status = $contract_archive->status;
            $contract_archive->status = ContractEnums::CONTRACT_ARCHIVE_STATUS_INVALID_ING;
            $contract_archive->updated_at = date('Y-m-d H:i:s');
            // 作废原因
            $contract_archive->invalid_reason = $params['remark'];
            // 作废附件
            $contract_archive->pdf_noneed_file = json_encode($params['pdf_noneed_file'], JSON_UNESCAPED_UNICODE);
            //作废日期
            $contract_archive->invalid_at = date('Y-m-d');
            // 作废原因选项
            $contract_archive->invalid_reason_type = $params['invalid_reason_type'] ?? 0;
            if (isset($params['invalid_reason_type'])) {
                if ($params['invalid_reason_type'] == ContractEnums::INVALID_REASON_TYPE_RELIEVE) {
                    //1. 租赁关系解除，已支付款项退回 :款项退回方式,款项退回日期
                    $contract_archive->invalid_refund_method = $params['invalid_refund_method'];
                    $contract_archive->invalid_refund_begin = $params['invalid_refund_begin'];
                    $contract_archive->invalid_refund_end = $params['invalid_refund_end'];
                    $contract_archive->invalid_replace_cno = '';
                    $contract_archive->invalid_replace_begin = null;
                    $contract_archive->invalid_replace_end = null;
                } elseif ($params['invalid_reason_type'] == ContractEnums::INVALID_REASON_TYPE_RE_SIGN) {
                    //2. 重新签订合同，已支付款项转移至新合同 :新合同编号,费用替补日期
                    $contract_archive->invalid_replace_cno = $params['invalid_replace_cno'];
                    $contract_archive->invalid_replace_begin = $params['invalid_replace_begin'];
                    $contract_archive->invalid_replace_end = $params['invalid_replace_end'];
                    $contract_archive->invalid_refund_method = 0;
                    $contract_archive->invalid_refund_begin = null;
                    $contract_archive->invalid_refund_end = null;
                }
            }

            //作废待审核
            $contract_archive->invalid_approve_status = Enums::CONTRACT_STATUS_PENDING;
            $res = $contract_archive->save();
            if ($res === false) {
                throw new BusinessException('Invalid contract error & params contractArchive=>' . json_encode($contract_archive->toArray(), JSON_UNESCAPED_UNICODE) . '可能的原因是:' . get_data_object_error_msg($contract_archive), ErrCode::$BUSINESS_ERROR);
            }

            $model = ContractStoreRentingModel::findFirst([
                'conditions' => 'contract_id = :id:',
                'bind' => ['id' => $contract_archive->cno]
            ]);
            if (empty($model)) {
                $this->logger->error('Invalid contract error & params model is empty');
                throw new BusinessException('contract id not exist', ErrCode::$BUSINESS_ERROR);
            }
            //同步审批字段
            $model->invalid_status = $contract_archive->invalid_approve_status;
            if ($model->save() === false) {
                throw new BusinessException('Invalid contract error & save ContractStoreRenting=>' . json_encode($model->toArray(), JSON_UNESCAPED_UNICODE) . '可能的原因是:' . get_data_object_error_msg($model), ErrCode::$BUSINESS_ERROR);
            }
            // 编辑后字段更新记录
            $log_model = new ContractArchiveEditLogModel();
            $log_model->contract_id = $model->contract_id;
            $log_model->contract_name = $model->contract_name;
            $log_model->contract_begin = $model->contract_begin;
            $log_model->contract_end = $model->contract_end;
            $log_model->is_invalid = ContractEnums::CONTRACT_STATUS_IS_INVALID_AUDIT;
            $log_model->operator_id = $user['id'];
            $log_model->operator_name = $user['name'];
            $log_model->created_at = date('Y-m-d H:i:s');
            $log_model->updated_at = date('Y-m-d H:i:s');
            $save_store_flag = $log_model->save();
            if ($save_store_flag === false) {
                throw new BusinessException('Invalid contract error & params logModel=>' . json_encode($log_model->toArray(), JSON_UNESCAPED_UNICODE) . '可能的原因是:' . get_data_object_error_msg($log_model), ErrCode::$BUSINESS_ERROR);
            }
            // 发起审批
            $biz_type = Enums::WF_STORE_RENTING_CONTRACT_INVALID_TYPE;
            if (!empty($this->_getWkReq($model->id, $biz_type))) {
                $flow_bool = $this->recommitWorkFlow($model->toArray(), $user, $biz_type);
            } else {
                $flow_bool = $this->saveWkFlow($model->toArray(), $user, $biz_type);
            }
            if ($flow_bool === false || isset($flow_bool['error_msg'])) {
                throw new BusinessException('store contract invalid create work flow failed', ErrCode::$CONTRACT_CREATE_WORK_FLOW_ERROR);
            }
            $db->commit();
        } catch (ValidationException $e) {
            // 校验错误，可对外抛出
            $code = $e->getCode();
            $message = $e->getMessage();
        } catch (BusinessException $e) {
            $code = $e->getCode();
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage();
            $this->logger->warning('Invalid-contract-failed:' . $real_message . ' params :' . json_encode($params));
        } catch (Exception $e) {
            $code = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage();
            $this->logger->warning('Invalid-contract-failed:' . $real_message . ' params :' . json_encode($params));
        }
        if (!empty($message)) {
            $db->rollback();
        }
        return [
            'code' => $code,
            'message' => $message,
            'data' => $data
        ];
    }

    /**
     * 归档合同终止
     * Created by: Wq.
     * @param array $params
     * @param  array $loginUser
     * @return array
     * @throws BusinessException
     * CreateTime: 2021/10/19 0016 20:36
     */
    public function terminal($params, $loginUser)
    {
        $code = ErrCode::$SUCCESS;
        $message = 'ok';
        $id = $params['id'] ?? 0;
        $terminal_at = $params['terminal_at'] ?? '';
        $terminal_reason = $params['terminal_reason'] ?? '';
        $terminal_attachments = $params['pdf_terminal_file'] ?? [];

        $db = $this->getDI()->get('db_oa');
        $db->begin();

        try {
            $contract_archive = ContractArchive::findFirst([
                'conditions' => 'id = :id:',
                'bind' => [
                    'id' => $id
                ],
                'for_update' => true,
            ]);
            $contract_archive_arr = !empty($contract_archive) ? $contract_archive->toArray() : [];
            // 编辑前合同数据
            $this->logger->info('编辑归档合同前数据:  传参 => ' . json_encode($params, JSON_UNESCAPED_UNICODE).'; 归档合同数据 => ' . json_encode($contract_archive_arr, JSON_UNESCAPED_UNICODE));

            if (!isset($contract_archive->id)) {
                throw new ValidationException(static::$t->_('archive_contract_is_not_exist'), ErrCode::$VALIDATE_ERROR);
            }
            if (!in_array($contract_archive->status, [ContractEnums::CONTRACT_ARCHIVE_STATUS_APPROVAL,
                ContractEnums::CONTRACT_ARCHIVE_STATUS_NO_UPLOAD])) {
                throw new ValidationException(static::$t->_('archive_contract_status_is_not_approval'), ErrCode::$VALIDATE_ERROR);
            }
            //记录修改前的状态,驳回时回退到此状态
            $contract_archive->before_status = $contract_archive->status;
            $contract_archive->status = ContractEnums::CONTRACT_ARCHIVE_STATUS_TERMINAL_ING;
            $contract_archive->updated_at = date('Y-m-d H:i:s');
            $contract_archive->terminal_at = $terminal_at;
            $contract_archive->terminal_reason = $terminal_reason;
            //终止待审核
            $contract_archive->terminal_approve_status = Enums::CONTRACT_STATUS_PENDING;

            $res = $contract_archive->save();
            if ($res === false) {
                throw new BusinessException('save terminal contract error', ErrCode::$BUSINESS_ERROR);
            }

            // 上传附件前数据
            $this->logger->info('附件上传前数据 => ' . json_encode($terminal_attachments, JSON_UNESCAPED_UNICODE));

            //删除历史附件
            $attachment_ids = array_values(array_filter(array_column($terminal_attachments, 'id')));
            $conditions = 'oss_bucket_key = :key: and oss_bucket_type in ({oss_bucket_type:array})';
            $bind = ['key' => $contract_archive->id, 'oss_bucket_type' => [ContractEnums::OSS_BUCKET_TYPE_TERMINAL_FILE]];
            if (!empty($attachment_ids)) {
                //没有历史附件需要保留，直接删除所有;有历史附件需要保留，则只需删除非保留的附件即可
                $conditions .= ' and id not in({attachment_ids:array})';
                $bind['attachment_ids'] = $attachment_ids;
            }
            $bool = AttachModel::find([
                'conditions' => $conditions,
                'bind'       => $bind
            ])->delete();
            if ($bool === false) {
                throw new BusinessException("合同终止删除历史附件失败, oss_bucket_key={$contract_archive->id}", ErrCode::$BUSINESS_ERROR);
            }
            
            // 终止附件添加
            if (!empty($terminal_attachments)) {
                foreach ($terminal_attachments as $one_attachment) {
                    //有附件id说明是原有的无需处理
                    if (!empty($one_attachment['id'])) {
                        continue;
                    }
                    $attachment = new AttachModel();
                    $attachment->oss_bucket_type = ContractEnums::OSS_BUCKET_TYPE_TERMINAL_FILE;
                    $attachment->oss_bucket_key = $contract_archive->id;
                    $attachment->bucket_name = $one_attachment['bucket_name'];
                    $attachment->object_key = $one_attachment['object_key'];
                    $attachment->file_name = $one_attachment['file_name'];

                    $bool = $attachment->save();
                    if ($bool === false) {
                        throw new BusinessException('合同终止添加附件失败, data: '. json_encode($one_attachment, JSON_UNESCAPED_UNICODE) . ' 可能的原因是: '. get_data_object_error_msg($attachment), ErrCode::$BUSINESS_ERROR);
                    }
                }
            }

            $model = ContractStoreRentingModel::findFirst([
                'conditions' => 'contract_id = :id:',
                'bind' => ['id' => $contract_archive->cno]
            ]);
            if (empty($model)) {
                throw new BusinessException('contract id not exist', ErrCode::$BUSINESS_ERROR);
            }
            //同步审批字段
            $model->terminal_status = $contract_archive->terminal_approve_status;
            if ($model->save() === false) {
                throw new BusinessException('terminal contract error & save ContractStoreRenting=>' . json_encode($model->toArray(), JSON_UNESCAPED_UNICODE) . '可能的原因是:' . get_data_object_error_msg($model), ErrCode::$BUSINESS_ERROR);
            }
            // 编辑后字段更新记录
            $log_model = new ContractArchiveEditLogModel();
            $log_model->contract_id = $model->contract_id;
            $log_model->contract_name = $model->contract_name;
            $log_model->is_terminal = ContractEnums::CONTRACT_STATUS_IS_TERMINAL_AUDIT;
            $log_model->operator_id = $loginUser['id'];
            $log_model->operator_name = $loginUser['name'];
            $log_model->created_at = date('Y-m-d H:i:s');
            $log_model->updated_at = date('Y-m-d H:i:s');

            // 归档合同编辑前日志数据
            $log_model_arr = !empty($log_model) ? $log_model->toArray() : [];
            $this->logger->info('归档合同编辑前日志数据 => ' . json_encode($log_model_arr, JSON_UNESCAPED_UNICODE));

            $save_store_flag = $log_model->save();
            if ($save_store_flag === false) {
                throw new BusinessException('Terminal contract error', ErrCode::$BUSINESS_ERROR);
            }
            // 发起审批
            $biz_type = Enums::WF_STORE_RENTING_CONTRACT_TERMINAL_TYPE;
            if (!empty($this->_getWkReq($model->id, $biz_type))) {
                $flow_bool = $this->recommitWorkFlow($model->toArray(), $loginUser, $biz_type);
            } else {
                $flow_bool = $this->saveWkFlow($model->toArray(), $loginUser, $biz_type);
            }
            if ($flow_bool === false || isset($flow_bool['error_msg'])) {
                throw new BusinessException('store contract terminal create work flow failed', ErrCode::$CONTRACT_CREATE_WORK_FLOW_ERROR);
            }
            $db->commit();
        } catch (ValidationException $e) {
            // 校验错误，可对外抛出
            $code = $e->getCode();
            $message = $e->getMessage();
        } catch (BusinessException $e) {
            $code = $e->getCode();
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        } catch (Exception $e) {
            $code = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        }

        if (!empty($real_message)) {
            $db->rollback();
            $this->logger->error("Terminal contract error =>" . $real_message);
        }

        return [
            'code' => $code,
            'message' => $message,
            'data' => []
        ];
    }

    /**
     * 保存合同归档
     * Created by: Wangqi.
     *
     * @param int $id
     * @param array $loginUser
     * @return mixed
     * CreateTime: 2021/10/19 0016 20:36
     * @throws BusinessException
     */
    public function saveArchive(int $id, array $loginUser = [])
    {
        $contract = ContractStoreRentingModel::findFirst([
            'conditions' => 'id = :id:',
            'bind'       => ['id' => $id]
        ]);
        if (empty($contract)) {
            throw new BusinessException("网点租房合同归档-合同信息为空, 不可归档, ID => $id", ErrCode::$BUSINESS_ERROR);
        }

        if ($contract->contract_status != Enums::WF_STATE_APPROVED) {
            throw new BusinessException("网点租房合同归档-合同审批未通过, 不可归档, ID => $id", ErrCode::$BUSINESS_ERROR);
        }

        $archive = ContractArchive::findFirst([
            'conditions' => 'cno = :cno:',
            'bind'       => ['cno' => $contract->contract_id],
            'columns'    => 'id'
        ]);
        if (!empty($archive)) {
            // 同一个合同编号的归档数据已存在(需再次核实确认是否是同一个合同 或 合同号重复导致)
            throw new BusinessException("网点租房合同归档-归档数据已存在, 请核实: archive_id-{$archive->id}, cid-{$id}, cno-{$contract->contract_id}", ErrCode::$BUSINESS_ERROR);
        }

        $contract = $contract->toArray();
        $contract = $this->_handleData($contract, $loginUser);
        $model = (new ContractArchive());
        $bool = $model->i_create($contract);
        if ($bool === false) {
            throw new BusinessException('网点租房合同归档-归档数据写入失败: 原因可能是: ' . get_data_object_error_msg($model) . '; 待归档数据: ' . json_encode($contract, JSON_UNESCAPED_UNICODE), ErrCode::$BUSINESS_ERROR);
        }

        return true;
    }

    private function _handleData($contract, $user)
    {
        if (empty($contract) || !is_array($contract)) {
            return [];
        }
        $userInfo                         = HrStaffInfoModel::getUserInfo($contract['manage_id'], 'name');
        $new_contract['cno']              = $contract['contract_id'];     //合同编号
        $new_contract['cname']            = $contract['contract_name'];        //合同名称
        $new_contract['status']           = 1;                            //归档状态，1:待归档，2:已归档'
        $new_contract['template_id']      = $contract['template_id'];            //模版ID
        $new_contract['is_master']        = $contract['is_main'] == 1 ? 1 : 0;         //是否是主合同
        $new_contract['sub_cno']          = $contract['main_contract_id'];               //关联的主合同编号
        $new_contract['amount']           = !empty($contract['contract_money']) ? $contract['contract_money'] * 1000 : 0;                  //合同金额，千分位数字
        $new_contract['payment_currency'] = $contract['money_symbol'];          //付款币种，1:泰铢，2:美元，3:人民币
        $new_contract['create_id']        = $contract['manage_id'] ?? 0;               //申请人
        $new_contract['create_name']      = $userInfo['name'] ?? '';           //申请人姓名
        $new_contract['create_department_id'] = $contract['create_department_id'] ?? 0;//申请人直属部门ID
        $new_contract['created_at']       = date('Y-m-d H:i:s');
        $new_contract['approved_at']      = $contract['approved_at'];
        $new_contract['contract_type']    = ContractEnums::CONTRACT_TYPE_STORING;
        return $new_contract;
    }

    /**
     *  获取归档详情
     *
     * @param $id
     * @param $user
     * @return array
     * @throws BusinessException
     * @throws ValidationException
     */
    public function getArchiveDetail($id, $user)
    {
        $res = $this->getDetail($id, $user);

        if (!$res['contract_info']) {
            throw new ValidationException(static::$t->_('archive_contract_is_not_exist'), ErrCode::$VALIDATE_ERROR);
        }
        $archive = ContractArchive::findFirst([
            'conditions' => 'cno = :cno: ',
            'bind'       => ['cno' => $res['contract_info']['contract_id']]
        ]);

        if (empty($archive)) {
            throw new ValidationException(static::$t->_('archive_contract_is_not_exist'), ErrCode::$VALIDATE_ERROR);
        }

//        if (in_array($archive->status, [ContractEnums::CONTRACT_ARCHIVE_STATUS_PENDING, ContractEnums::CONTRACT_ARCHIVE_STATUS_APPROVAL, ContractEnums::CONTRACT_ARCHIVE_STATUS_NO_UPLOAD])) {
            $data                 = $archive->toArray();
            $status               = ContractEnums::$contract_archive_status[$data['status']] ?? '';
            $data['status_title'] = static::$t->_($status);

            // 合同分类map
            $contract_category_map = EnumsService::getInstance()->getContractCategorysMap();

            // 直属分类信息
            $contract_category_info = $contract_category_map[$data['template_id']] ?? [];
            $data['template_title'] = $contract_category_info['label'] ?? '';

            // 若直属分类是二级分类, 则需拼接一级分类
            if (isset($contract_category_info['level']) && $contract_category_info['level'] > 1) {
                $data['template_title'] = $contract_category_map[$contract_category_info['ancestry_id']]['label'] . '/' . $data['template_title'];
            }

            $data['real_amount']            = bcdiv($data['amount'], 1000, 2);
            $is_master                      = Enums::$contract_is_master[$data['is_master']] ?? '';
            $data['is_master_title']        = static::$t->_($is_master);
            $payment_currency               = GlobalEnums::$currency_item[$data['payment_currency']] ?? '';
            $data['payment_currency_title'] = static::$t->_($payment_currency);
            $data['contract_file']          = $this->getRemotePath($data['contract_file'], AttachmentService::BUSINESS_TYPE_CONTRACT_ARCHIVE_CONTRACT_FILE);

            // 待签字合同附件
            $data['contract_signature'] = AttachModel::find([
                'conditions' => 'oss_bucket_key = :oss_bucket_key: AND oss_bucket_type = :oss_bucket_type: AND deleted = :deleted:',
                'bind'       => ['oss_bucket_key' => $data['id'], 'oss_bucket_type' => ContractEnums::OSS_BUCKET_TYPE_CONTRACT_SIGNATURE_FILE, 'deleted' => GlobalEnums::IS_NO_DELETED],
                'columns'    => ['id','oss_bucket_type', 'file_name', 'bucket_name', 'object_key']
            ])->toArray();
            $res['archive'] = $data ?? [];
//        }

        // 当前用户是否是超管
        $res['store_access_status'] = EnumsService::getInstance()->isPrivilegeStaffId($user['id']) ? 1 : 0;

        return $res;
    }

    /**
     *  获取归档合同编辑日志列表
     */
    public function getContractArchieveEditLogs($data){
        // 网点合同
        $contract = ContractStoreRentingModel::findFirst([
            'conditions' => 'id = :id:',
            'bind'       => ['id' => $data['id']]
        ]);
        $contract = !empty($contract) ? $contract->toArray() : [];
        $archiveLogs = ContractArchiveEditLogModel::find([
            'conditions' => 'contract_id = :contract_id: ',
            'bind'       => ['contract_id' => $contract['contract_id']]
        ]);

        $archiveLogs = !empty($archiveLogs) ? $archiveLogs->toArray() : [];
        // 取所有网点分类
        $storeList = (new StoreService())->getDepartStoreType();
        $storeList = array_column($storeList,'name','type');
        $storeList['-1'] = Enums::PAYMENT_HEADER_STORE_NAME;
        // 归档合同作废原因
        $archive = ContractArchive::findFirst([
            'conditions' => 'cno = :cno:',
            'bind'       => ['cno' => $contract['contract_id']]
        ]);
        if ($archiveLogs) {
            foreach ($archiveLogs as &$log) {
                $log['contract_begin'] = '0000-00-00' == $log['contract_begin'] ? '' : $log['contract_begin'];
                $log['contract_begin_old'] = '0000-00-00' == $log['contract_begin_old'] ? '' : $log['contract_begin_old'];
                $log['contract_end'] = '0000-00-00' == $log['contract_end'] ? '' : $log['contract_end'];
                $log['contract_end_old'] = '0000-00-00' == $log['contract_end_old'] ? '' : $log['contract_end_old'];
                $log['contract_effect_date'] = '0000-00-00' == $log['contract_effect_date'] ? '' : $log['contract_effect_date'];
                $log['contract_effect_date_old'] = '0000-00-00' == $log['contract_effect_date_old'] ? '' : $log['contract_effect_date_old'];
                if ($log['store_name_old'] == $log['store_name']) {
                    $log['store_name_old'] = $log['store_name'] = '';
                }
                if ($log['store_cate_old'] == $log['store_cate']) {
                    $log['store_cate_old'] = $log['store_cate'] = '';
                } else {
                    $log['store_cate_old'] = isset($storeList[$log['store_cate_old']]) ? $storeList[$log['store_cate_old']] : '';
                    $log['store_cate'] = isset($storeList[$log['store_cate']]) ? $storeList[$log['store_cate']] : '';
                }
                if ($log['contract_effect_date_old'] == $log['contract_effect_date']) {
                    $log['contract_effect_date_old'] = $log['contract_effect_date'] = null;
                }
                if ($this->compare_bank_collection($log['bank_collection_old'], $log['bank_collection'])) {
                    $log['bank_collection_old'] = $log['bank_collection'] = '';
                } else {
                    $result = $this->transferToPaymentInfoStr($log['bank_collection'],$log['bank_collection_old']);

                    $log['bank_collection_old'] = !empty($result['before']) ? implode(';',$result['before']) : '';
                    $log['bank_collection'] = !empty($result['after']) ? implode(';',$result['after']) : '';
                }
                if ($log['hourse_owner_addr_old'] == $log['hourse_owner_addr']) {
                    $log['hourse_owner_addr_old'] = $log['hourse_owner_addr'] = '';
                }
                // 归档合同作废原因
                $log['invalid_reason'] = isset($archive->invalid_reason) && ContractEnums::CONTRACT_STATUS_IS_INVALID == $log['is_invalid'] && !empty($archive->invalid_reason) ? (static::$t->_('contract_archive_status.3') . "，" . $archive->invalid_reason) : '';
                // 审核状态
                if (!empty($log['need_workflow'])) {
                    $log['modify_status'] = static::$t->_(Enums::$contract_status[$contract['contract_status']]);
                } else {
                    $log['modify_status'] = '';
                }
                $log['need_workflow_text'] = !empty($log['need_workflow']) ? static::$t->_('purchase_order_is_tax_1') : static::$t->_('purchase_order_is_tax_0');
                // 合同终止状态
                $log['terminal_status_text'] = !empty($log['is_terminal']) ? static::$t->_('contract_archive_status.5').'，'.
                    static::$t->_('contract_achive_ternimal_at').$archive->terminal_at.'，'.$archive->terminal_reason : '';
                // 地契类型
                if (!empty($log['land_type']) || !empty($log['land_type_content'])) {
                    $log['land_type'] = explode(',',$log['land_type']);
                    $log['land_type'] = $this->transferToLandTypeStr($log['land_type'],$log['land_type_content']);
                }
                // 修改前前地契类型
                if (!empty($log['land_type_old']) || !empty($log['land_type_content_old'])) {
                    $log['land_type_old'] = explode(',',$log['land_type_old']);
                    $log['land_type_old'] = $this->transferToLandTypeStr($log['land_type_old'],$log['land_type_content_old']);
                }
                // 出租人
                if (!empty($log['leaser_type']) || !empty($log['leaser_type_content'])) {
                    $log['leaser_type'] = $this->transferToLeaserTypeStr($log['leaser_type'],$log['leaser_type_content']);
                }
                // 修改前出租人
                if (!empty($log['leaser_type_old']) || !empty($log['leaser_type_content_old'])) {
                    $log['leaser_type_old'] = $this->transferToLeaserTypeStr($log['leaser_type_old'],$log['leaser_type_content_old']);
                }
            }
        }

        return $archiveLogs;
    }

    public function archivePerform($archive_id, $data, $user)
    {
        $code    = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $archive = ContractArchive::findFirst([
            'conditions' => 'id = :id: and status in({status:array})',
            'bind'       => ['id' => $archive_id, 'status' => [ContractEnums::CONTRACT_ARCHIVE_STATUS_PENDING,
                ContractEnums::CONTRACT_ARCHIVE_STATUS_NO_UPLOAD]]
        ]);
        if (!$archive) {
            return [
                'code'    => ErrCode::$CONTRACT_ARCHIVE_GET_INFO_ERROR,
                'message' => static::$t->_('contract_archive_done')
            ];
        }
        $data = $this->handleData($data, $user);
        try {
            $bool = $archive->i_update($data);
            if ($bool === false) {
                throw new BusinessException('更新归档信息失败', ErrCode::$CONTRACT_ARCHIVE_UPDATE_INFO_ERROR);
            }
        } catch (BusinessException $e) {
            $code         = $e->getCode();
            $message      = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        } catch (Exception $e) {
            $code         = ErrCode::$SYSTEM_ERROR;
            $message      = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        }

        if (!empty($real_message)) {
            $this->logger->warning('contract-create-failed:' . $real_message);
        }
        return [
            'code'    => $code,
            'message' => $message,
        ];
    }

    private function handleData($data, $user)
    {
        if (empty($data) || !is_array($data)) {
            return [];
        }

        // 租房合同的盖章合同附件
        if (!empty($data['contract_file_arr'])) {
            $data['contract_file'] = $this->handle_oss_file($data['contract_file_arr']);
        }

        // 租房合同的签字合同
        if (!empty($data['contract_signature_file_arr'])) {
            $signatureFile = $data['contract_signature_file_arr'];
            $attachment = new AttachModel();
            $attachment->oss_bucket_type = ContractEnums::OSS_BUCKET_TYPE_CONTRACT_SIGNATURE_FILE;
            $attachment->oss_bucket_key = $data['archive_id'];
            $attachment->bucket_name = $signatureFile[0]['bucket_name'];
            $attachment->object_key = $signatureFile[0]['object_key'];
            $attachment->file_name = $signatureFile[0]['file_name'];

            $bool = $attachment->save();
            if ($bool === false) {
                throw new BusinessException('合同上传签字合同附件创建失败', ErrCode::$CONTRACT_CREATE_ERROR);
            }
        }

        // 根据上传合同文件类型判断
        if (!empty($data['contract_file_arr'])) {
            $data['status']        = ContractEnums::CONTRACT_ARCHIVE_STATUS_APPROVAL;  // 已归档
        } elseif (!empty($data['contract_signature_file_arr'])) {
            $data['status']        = ContractEnums::CONTRACT_ARCHIVE_STATUS_NO_UPLOAD;  // 待上传盖章合同
        } else {
            $data['status']        = 0;  // 都未上传的错误数据
        }
        $data['updated_at']    = date('Y-m-d H:i:s');
        $data['filing_at']     = date('Y-m-d H:i:s');
        $data['filing_id']     = $user['id'] ?? 0;
        $data['filing_name']   = $user['name'] ?? '';
        $data['holder_name']   = $data['holder_name'] ?? '';
        $data['fair_date']     = $data['fair_date'] ?? null;
        return $data;
    }

    /**
     * 获取归档列表
     * Created by: Lqz.
     *
     * @param $params
     * @param $loginUser
     * @param $type
     * @return array
     * CreateTime: 2020/9/17 0017 11:58
     */
    public function getArchiveList($params, $loginUser, $type)
    {
        $limit  = (isset($params['limit']) && !empty($params['limit'])) ? $params['limit'] : GlobalEnums::DEFAULT_PAGE_SIZE;
        $page   = (isset($params['page']) && !empty($params['page'])) ? $params['page'] : GlobalEnums::DEFAULT_PAGE_NUM;
        $export = $params['export'] ?? false;
        $offset = $limit * ($page - 1);

        try {
            $builder    = $this->modelsManager->createBuilder();
            $builder->from(['c' => ContractStoreRentingModel::class]);
            $builder->leftjoin(ContractArchive::class, 'c_a.cno=c.contract_id', 'c_a');
            $builder->andWhere('c_a.id is not null');
            $builder = $this->_getAllCondition($builder, $params, $loginUser, $type);

            // 符合条件的单据数
            $count = (int)$builder->columns('COUNT(DISTINCT(c.id)) AS t_count')->getQuery()->getSingleResult()->t_count;

            $items = [];
            if ($count) {
                $column_str = 'c.manage_id,c.id,c.created_at,c.contract_id,c.contract_name,c.store_id,c.contract_begin,c.contract_end,c.house_contract_area,c.house_actual_area,c.contract_total_amount,c.contract_lease_type,c.monthly_payment_type,c.contract_status,c.state,date(c.apply_at) as apply_at,c_a.cno,c_a.status,c_a.filing_at,c_a.filing_name,c_a.holder_name,c_a.status as archive_status,c_a.filing_at, c_a.filing_name, c_a.holder_name, c_a.id as archive_id,c.contract_leader_id, c.ver,c.money_symbol,c.is_main,c_a.invalid_at,c_a.terminal_at,c.leader_node_department_name';
                $builder->columns($column_str);

                if (!$export) {
                    $builder->limit($limit, $offset);
                }

                $builder->orderBy('c.id DESC');
                $builder->groupBy('c.id');
                $items = $builder->getQuery()->execute()->toArray();
                $items = $this->_handleArchiveItems($items, $export);
            }

            //导出
            if ($export) {
                return $this->_exportNewArchive($items);
            }

            $data = [
                'items'      => $items,
                'pagination' => [
                    'current_page' => $page,
                    'page_limit'   => $limit,
                    'total_count'  => $count,
                ]
            ];
        } catch (Exception $e) {
            $msg    = $e->getMessage();
            $this->logger->warning('contract-getmylist-failed:' . $msg);
            return [
                'error_msg' => $msg
            ];
        }

        return $data;
    }


    public function getAllList($params, $loginUser, $type)
    {
        $limit  = (isset($params['limit']) && !empty($params['limit'])) ? $params['limit'] : 20;
        $page   = (isset($params['page']) && !empty($params['page'])) ? $params['page'] : 1;
        $export = $params['export'] ?? false;
        $offset = $limit * ($page - 1);

        if ($export) {
            ini_set('memory_limit', '1024M');
        }

        try {
            $builder    = $this->modelsManager->createBuilder();
            $builder->from(['c' => ContractStoreRentingModel::class]);
            $builder->leftjoin(ContractArchive::class, 'c_a.cno=c.contract_id', 'c_a');

            $builder = $this->_getAllCondition($builder, $params, $loginUser, $type);
            $count = (int) $builder->columns('COUNT(DISTINCT(c.id)) AS total')->getQuery()->getSingleResult()->total;

            $items = [];
            if ($count) {
                if ($export) {
                    $column_str ='distinct 
                     c.id,   
                     c.manage_id,
                     c.contract_status,
                     date(c.apply_at) as apply_at,
                     c_a.cno,
                     c_a.status,
                     c_a.filing_at,
                     c_a.filing_name,
                     c_a.holder_name,
                     c_a.invalid_at,
                     c.store_id,
                     c.store_cate,
                     c.provinces,
                     c.lon_lat,
                     c.store_addr,
                     c.house_owner_name,
                     c.house_contract_area,
                     c.house_actual_area,
                     c.house_owner_type,
                     c.contract_name,
                     c.contract_id,
                     c.contract_deadline,
                     c.contract_begin,
                     c.contract_end,
                     c.rent_free_time,
                     c.contract_effect_date,
                     c.contract_lease_type,
                     c.money_symbol,
                     c.contract_benefits,
                     c.deposit_amount,
                     c.ver,
                     c.monthly_payment_type,
                     c.contract_signer_name,
                     c.signer_phone,
                     c.contract_remarks,
                     c.bank_collection,
                     c.hourse_owner_addr,
                     c.monthly_area_service,
                     c.vat,
                     c.property_tax,
                     c.total_amount_monthly,
                     c.duty_stamp,
                     c.total_amount,
                     c.signboard_tax,
                     c.state,
                     c.created_at,
                     c.withholding_tax,
                     c.wt_area_service,
                     c.contract_total_amount,
                     c.billboard_tax_payer,
                     c.contract_deposit,
                     c.land_tax_payer,
                     c.fire_insurance_payer,
                     c.antimoth_payer,
                     c.land_tax_amount,
                     c.fire_insurance_amount,
                     c.antimoth_amount,
                     c.is_main,
                     c.contract_lang,
                     c.house_equip_list,
                     c.contract_tax_no,
                     c.amount_paid,
                     c.full_refund_conditions,
                     c_a.terminal_at,
                     c.land_type,
                     c.leaser_type,
                     c.cost_company_name,
                     c.main_contract_id,
                     c.relate_contract_id,
                     c.contract_leader_id,
                     c.leader_node_department_name,
                     c.contract_total_amount_no_wht,
                     c.rent_due_date,
                     c.month_rent_has_wht,
                     c.withholding_tax_liability_bearer,
                     c.water_bill_payment_type,
                     c.electricity_bill_payment_type,
                     c.water_billing_method,
                     c.electricity_billing_method,
                     c.water_usage_units,
                     c.electricity_usage_units,
                     c.issue_withholding_tax_address
                ';
                } else {
                    $column_str = 'distinct c.manage_id,c.id,c.created_at,c.contract_id,c.contract_name,c.store_id,c.contract_begin,c.contract_end,c.house_contract_area,c.contract_lease_type,c.monthly_payment_type,c.contract_status,c.state,date(c.apply_at) as apply_at,c.house_actual_area,c.ver,c.money_symbol,c_a.cno,c_a.status,c_a.filing_at,c_a.filing_name,c_a.holder_name,c_a.status as archive_status,c_a.filing_at, c_a.filing_name, c_a.holder_name, c_a.id as archive_id,c.is_main,c.contract_leader_id,c.house_owner_name,c.leader_node_department_name';

                    $builder->limit($limit, $offset);
                }

                $builder->columns($column_str);
                $builder->orderBy('c.id desc');
                $items = $builder->getQuery()->execute()->toArray();
            }

            if ($export) {
                $tArr = $this->_handleArchiveItemsForExport($items);
                return $this->_exportAll($tArr['items'], $tArr['maxLen'], $tArr['maxBankLen'], $tArr['maxAreaLen']);
            } else {
                $items = $this->_handleArchiveItems($items);
            }

            $data = [
                'items'      => $items,
                'pagination' => [
                    'current_page' => $page,
                    'page_limit'   => $limit,
                    'total_count'  => $count,
                ]
            ];
        } catch (Exception $e) {
            $msg    = $e->getMessage();
            $this->logger->warning('contract-getmylist-failed:' . $msg);
            return [
                'error_msg' => $msg
            ];
        }

        return $data;
    }

    /**
     * 数据查询列表 / 归档列表 调用
     *
     * @param $builder
     * @param $params
     * @param $loginUser
     * @param int $type
     * @return mixed
     * @throws BusinessException
     */
    private function _getAllCondition($builder, $params, $loginUser, $type = 0)
    {
        $storeCate          = $params['store_cate'] ?? '';
        $storeId            = $params['store_id'] ?? '';
        $contractBeginLeft  = $params['contract_begin_left'] ?? '';
        $contractBeginRight = $params['contract_begin_right'] ?? '';
        $contractEndLeft    = $params['contract_end_left'] ?? '';
        $contractEndRight   = $params['contract_end_right'] ?? '';
        $contractStatus     = $params['contract_status'] ?? '';
        $leaseType     = $params['lease_type'] ?? '';
        $contractId    = $params['contract_id'] ?? '';
        $contractName  = $params['contract_name'] ?? '';
        $applyAtLeft = $params['apply_at_left'] ?? '';
        $applyAtRight = $params['apply_at_right'] ?? '';
        $archiveStatus = $params['archive_status'] ?? '';
        $filingAtLeft  = $params['filing_at_left'] ?? '';
        $filingAtRight = $params['filing_at_right'] ?? '';
        $is_main = $params['is_main'] ?? '';
        $staffId = $params['staff_id'] ?? '';
        $biz_type = $params['biz_type'] ?? '';
        $contract_leader_id = $params['contract_leader_id'] ?? '';
        $house_owner_name   = $params['house_owner_name'] ?? '';
        $effective_status = $params['effective_status'] ?? 0;


        if ($staffId) {
            $builder->andWhere('c.manage_id = :uid:', ['uid' => $staffId]);
        }
        if ($archiveStatus) {
            $builder->andWhere('c_a.status = :archiveStatus:', ['archiveStatus' => $archiveStatus]);
        }
        if ($filingAtLeft) {
            $builder->andWhere('c_a.filing_at >=:filingAtLeft:', ['filingAtLeft' => date('Y-m-d 00:00:00', strtotime($filingAtLeft))]);
        }
        if ($filingAtRight) {
            $builder->andWhere('c_a.filing_at <=:filingAtRight:', ['filingAtRight' => date('Y-m-d 23:59:59', strtotime($filingAtRight))]);
        }

        //合同申请时间
        if ($applyAtLeft) {
            $builder->andWhere('c.apply_at >=:applyAtLeft:', ['applyAtLeft' => date('Y-m-d 00:00:00', strtotime($applyAtLeft))]);
        }
        if ($applyAtRight) {
            $builder->andWhere('c.apply_at <=:applyAtRight:', ['applyAtRight' => date('Y-m-d 23:59:59', strtotime($applyAtRight))]);
        }
        if (!empty($is_main)) {
           $builder->andWhere('c.is_main = :is_main:', ['is_main' => $is_main]);
        }
        if (!empty($contractStatus)) {
            $builder->andWhere('c.contract_status = :contract_status:', ['contract_status' => $contractStatus]);
        }
        if (!empty($storeCate)) {
            $builder->andWhere('c.store_cate = :storeCate:', ['storeCate' => $storeCate]);
        }
        if (!empty($storeId)) {
            $builder->andWhere('c.store_id = :storeId:', ['storeId' => $storeId]);
        }
        if (!empty($contractBeginLeft)) {
            $builder->andWhere('c.contract_begin >= :contractBeginLeft: and c.contract_begin <= :contractBeginRight:', ['contractBeginLeft' => date('Y-m-d 00:00:00', strtotime($contractBeginLeft))]);
        }
        if (!empty($contractBeginRight)) {
            $builder->andWhere('c.contract_begin <= :contractBeginRight:', ['contractBeginRight' => date('Y-m-d 23:59:59', strtotime($contractBeginRight))]);
        }
        if (!empty($contractEndLeft)) {
            $builder->andWhere('c.contract_end >=:contractEndLeft:', ['contractEndLeft' => date('Y-m-d 00:00:00', strtotime($contractEndLeft))]);
        }
        if (!empty($contractEndRight)) {
            $builder->andWhere('c.contract_end <=:contractEndRight: ', ['contractEndRight' => date('Y-m-d 23:59:59', strtotime($contractEndRight))]);
        }

        if (!empty($leaseType)) {
            $builder->andWhere('c.contract_lease_type = :leaseType:', ['leaseType' => $leaseType]);
        }
        if (!empty($contractId)) {
            $builder->andWhere('c.contract_id = :contractId:', ['contractId' => $contractId]);
        }
        if (!empty($contractName)) {
            $builder->andWhere('c.contract_name = :contractName:', ['contractName' => $contractName]);
        }
        if(!empty($params['export'])){//导出按照新版本数据
            $builder->andWhere('c.ver = :ver:', ['ver' => 1]);

        }
        if (!empty($contract_leader_id)) {
            $builder->andWhere('c.contract_leader_id = :contract_leader_id:', ['contract_leader_id' => $contract_leader_id]);

        }
        if (!empty($house_owner_name)) {
            $builder->andWhere('c.house_owner_name LIKE :house_owner_name:', ['house_owner_name' => "{$house_owner_name}%"]);
        }
        if ($type == self::LIST_TYPE_SEARCH) {
            // 对接通用数据权限
            // 业务表参数
            $table_params = [
                'table_alias_name' => 'c',
                'create_id_field' => 'contract_leader_id',
                'create_node_department_id_filed' => 'create_department_id',
            ];
            $builder = CommonDataPermissionService::getInstance()->getCommonDataPermission($loginUser, $builder, Enums\SysConfigEnums::SYS_MODULE_STORE_RENT_CONTRACT, $table_params);

            // V22497 合同有效状态
            if (!empty($effective_status)) {
                $builder->andWhere('c.effective_status = :effective_status:', ['effective_status' => $effective_status]);
            }
        }

        return $builder;
    }

    /**
     * 处理每组数据
     * Created by: Lqz.
     *
     * @param array $items
     * @param bool $export
     * @return array
     * CreateTime: 2020/9/15 0015 23:18
     */
    private function _handleArchiveItems(array $items, $export = false)
    {
        $manageIds = array_column($items, 'manage_id');
        $staffs    = [];
        $depts     = [];

        //查询员工和部门
        // 申请人
        if ($manageIds) {
            $staffs = HrStaffInfoModel::find([
                'conditions' => 'staff_info_id in ({manageIds:array})',
                'bind'       => [
                    'manageIds' => $manageIds
                ],
                'column'     => 'staff_info_id,name,sys_department_id'
            ])->toArray();
            $staffs    = array_column($staffs, null, 'staff_info_id');

            $deptIds   = array_column($staffs, 'sys_department_id');
            if ($deptIds) {
                $depts = SysDepartmentModel::find([
                    'conditions' => 'id in ({deptIds:array})',
                    'bind'       => [
                        'deptIds' => $deptIds
                    ],
                    'columns'    => 'id,name'
                ])->toArray();
                $depts    = array_column($depts, null, 'id');
            }
        }

        //查询合同金额
        //新老版本数据区分（）
        $ver = [0,1];
        $store_renting_contract_id = [];
        foreach ($items as $value) {
            foreach ($ver as $v1) {
                if ($value['ver'] == $v1) {
                    $store_renting_contract_id[$v1][] = $value['id'];
                }
            }
        }
        $moneyArr =$moneyArr1=$moneyArr2= [];

        //新老版本数据处理
        if (isset($store_renting_contract_id[0]) && is_array($store_renting_contract_id[0])) {//老版本数据处理

            if ($store_renting_contract_id) {

                $moneyObj = ContractStoreRentingMoneyModel::find([
                    'conditions' => 'store_renting_contract_id in ({store_renting_contract_id:array}) and type = :type:',
                    'bind'       => [
                        'store_renting_contract_id' => $store_renting_contract_id[0],
                        'type'                      => 'contract_money'
                    ],
                    'group'      => 'store_renting_contract_id',
                    'columns'    => 'store_renting_contract_id, max(amount) as amount'
                ]);
                $moneyArr1 = $moneyObj->toArray();
                $moneyArr1 = array_column($moneyArr1, 'amount', 'store_renting_contract_id');
            }
        }
        if (isset($store_renting_contract_id[1])&&is_array($store_renting_contract_id[1])) {
            $moneyObj = ContractStoreRentingDetailModel::find([
                'conditions' => 'contract_store_renting_id in ({contract_store_renting_id:array}) ',
                'bind'       => [
                    'contract_store_renting_id' => $store_renting_contract_id[1],
                ],
                'group'      => 'contract_store_renting_id',
                'columns'    => 'contract_store_renting_id, max(amount_no_tax) as amount'
            ]);
            $moneyArr2 = $moneyObj->toArray();
            $moneyArr2 = array_column($moneyArr2, 'amount', 'contract_store_renting_id');
        }
        $moneyArr = $moneyArr1 + $moneyArr2;
        $storeIds  = array_column($items, 'store_id');
        $storeList = [];
        if ($storeIds) {
            $where = 'id in ({storeIds:array})';

            $storeObj  = SysStoreModel::find([
                'conditions' => $where,
                'bind'       => [
                    'storeIds' => $storeIds
                ],
                'columns'    => 'id,name'
            ]);
            $storeList = $storeObj->toArray();
            $storeList = array_column($storeList, 'name', 'id');
        }
        $leaseTypes  = $this->getContractLeaseTypes();
        $auditStatus = $this->getAuditStatus();

        if (!$export) {
            // 获取已存在的付款单
            $paymentOrder = $this->getUsingPaymentOrder(array_column($items,'cno'));
        }

        // 负责人
        $staff_ids  = array_values(array_unique(array_column($items, 'contract_leader_id')));
        if (!empty($staff_ids)) {
            $user_infos = HrStaffInfoModel::find([
                'conditions' => 'staff_info_id in ({ids:array})',
                'bind'       => ['ids' => $staff_ids],
                'columns'    => 'name,nick_name,staff_info_id'

            ])->toArray();
            $user_infos = array_column($user_infos, null, 'staff_info_id');

        }

        foreach ($items as &$item) {
            $user_info = $user_infos[$item['contract_leader_id']] ?? '';
            $_deptId                     = isset($staffs[$item['manage_id']]) ? $staffs[$item['manage_id']]['sys_department_id'] : '';
            $_staffName                  = isset($staffs[$item['manage_id']]) ? $staffs[$item['manage_id']]['name'] : '-';
            $_deptName                   = isset($depts[$_deptId]) ? $depts[$_deptId]['name'] : '-';
            $item['dept_name']           = $_deptName;
            $item['staff_name']          = $_staffName;
            $item['apply_at']            = !empty($item['apply_at']) ? $item['apply_at'] : $item['created_at'];
            $item['store_name']          = $storeList[$item['store_id']] ?? $item['store_id'];
            $item['lease_type_txt']      = $leaseTypes[$item['contract_lease_type']] ?? '-';
            $item['contract_status_txt'] = isset($auditStatus[$item['contract_status']]) ? $auditStatus[$item['contract_status']] : $this->getOldStatusTxt($item['state']);
            $money                       = isset($moneyArr[$item['id']]) ? ($moneyArr[$item['id']] ?? 0) : $item['contract_money'];
            $item['contract_money_fmt']  = number_format($money, 2);
            $item['archive_status_txt']  = !empty($item['status']) ? self::$t->_('contract_archive_status.' . $item['status']) : '-';
            $item['money_symbol_txt']                    =       static::$t[GlobalEnums::$currency_item[$item['money_symbol']]];

            // 付款申请按钮展示(已经申请过付款的不展示)
            $item['payment_apply_status'] = (isset($paymentOrder) && in_array($item['contract_id'],$paymentOrder)) ? 0 : 1;
            $item['is_main'] =$this->getLangIsMainName()[$item['is_main']];
            $item['contract_leader_name'] = $this->getNameAndNickName($user_info['name'] ?? '', $user_info['nick_name'] ?? '');
        }

        return $items;
    }


    private function _handleArchiveItemsForExport($items)
    {
        $manageIds = array_values(array_filter(array_unique(array_column($items, 'manage_id'))));

        $staffs = [];
        $depts = [];
        //查询员工和部门
        // 申请人
        if ($manageIds) {
            $staffs = HrStaffInfoModel::find([
                'conditions' => 'staff_info_id in ({manageIds:array})',
                'bind' => ['manageIds' => $manageIds],
                'column' => 'staff_info_id, name, sys_department_id'
            ])->toArray();
            $staffs = array_column($staffs, null, 'staff_info_id');

            $deptIds = array_values(array_filter(array_unique(array_column($staffs, 'sys_department_id'))));
            if ($deptIds) {
                $depts = SysDepartmentModel::find([
                    'conditions' => 'id in ({deptIds:array})',
                    'bind' => ['deptIds' => $deptIds],
                    'columns' => 'id, name'
                ])->toArray();
                $depts = array_column($depts, null, 'id');
            }
        }

        //查询合同金额 和 获取新增的 区域信息
        $moneyArr = [];
        $area_info = [];

        $store_renting_contract_id = array_values(array_filter(array_unique(array_column($items, 'id'))));
        if (!empty($store_renting_contract_id)) {
            $tempArr = ContractStoreRentingDetailModel::find([
                'conditions' => 'contract_store_renting_id in ({contract_store_renting_id:array})',
                'bind' => ['contract_store_renting_id' => $store_renting_contract_id],
                'columns' => 'contract_store_renting_id, cost_start_date, cost_end_date, amount_no_tax, vat_rate, amount_vat, amount_has_tax, wht_category, wht_rate, amount_wht, rent_no_wht'
            ])->toArray();
            foreach ($tempArr as $key => $val) {
                $temp = [];
                $temp['cost_start_date'] = $val['cost_start_date'];
                $temp['cost_end_date'] = $val['cost_end_date'];
                $temp['amount_no_tax'] = $val['amount_no_tax'];
                $temp['vat_rate'] = $val['vat_rate'];
                $temp['amount_vat'] = $val['amount_vat'];
                $temp['amount_has_tax'] = $val['amount_has_tax'];
                $temp['wht_category'] = $val['wht_category'];
                $temp['wht_rate'] = $val['wht_rate'];
                $temp['amount_wht'] = $val['amount_wht'];
                $temp['rent_time'] = $val['cost_start_date'].'～'.$val['cost_end_date'];
                $temp['cost_start_date'] = $val['cost_start_date'];
                $temp['rent_no_wht'] = $val['rent_no_wht'];
                $moneyArr[$val['contract_store_renting_id']][] = $temp;
            }
            unset($tempArr);

            $area_arr = ContractStoreRentingArea::find([
                'conditions' => 'contract_store_renting_id in ({contract_store_renting_id:array})',
                'bind' => ['contract_store_renting_id' => $store_renting_contract_id],
                'columns' => 'start_time, end_time, contract_store_renting_id, area_service_amount_no_tax, area_vat_rate, area_service_amount_vat, area_wht_category, area_wht_rate, area_amount_wht, tax_type, tax_amount_has_wht, tax_amount_no_wht'
            ])->toArray();
            foreach ($area_arr as $v) {
                $row['rent_time'] = $v['start_time'] . '~' . $v['end_time'];
                $row['area_service_amount_no_tax'] = $v['area_service_amount_no_tax'];
                $row['area_vat_rate'] = $v['area_vat_rate'];
                $row['area_service_amount_vat'] = $v['area_service_amount_vat'];
                $row['area_wht_category'] = $v['area_wht_category'];
                $row['area_wht_rate'] = $v['area_wht_rate'];
                $row['area_amount_wht'] = $v['area_amount_wht'];
                $row['tax_type'] = $v['tax_type'];
                $row['tax_amount_has_wht'] = $v['tax_amount_has_wht'];
                $row['tax_amount_no_wht'] = $v['tax_amount_no_wht'];
                $area_info[$v['contract_store_renting_id']][] = $row;
            }
            unset($area_arr);
        }

        $storeList = [];
        $storeIds = array_values(array_filter(array_unique(array_column($items, 'store_id'))));
        if ($storeIds) {
            $where = 'id in ({storeIds:array})';
            $storeList = SysStoreModel::find([
                'conditions' => $where,
                'bind' => ['storeIds' => $storeIds],
                'columns' => 'id,name'
            ])->toArray();
            $storeList = array_column($storeList, 'name', 'id');
        }

        $leaseTypes = $this->getContractLeaseTypes();
        $leaser_type = $this->getLeaserType();
        $land_type  = $this->getLandType();
        $auditStatus = $this->getAuditStatus();

        $maxLen = 1;
        $maxBankLen = 1;
        $maxAreaLen = 1;
        foreach ($items as &$item) {
            $_deptId = isset($staffs[$item['manage_id']]) ? $staffs[$item['manage_id']]['sys_department_id'] : '';
            $_staffName = isset($staffs[$item['manage_id']]) ? $staffs[$item['manage_id']]['name'] : '-';
            $_deptName = isset($depts[$_deptId]) ? $depts[$_deptId]['name'] : '-';

            $item['dept_name'] = $_deptName;
            $item['staff_name'] = $_staffName;
            $item['contract_status_txt'] = isset($auditStatus[$item['contract_status']]) ? $auditStatus[$item['contract_status']] : $this->getOldStatusTxt($item['state']);
            $item['apply_at'] = !empty($item['apply_at']) ? $item['apply_at'] : $item['created_at'];

            $item['archive_status_txt'] = !empty($item['status']) ? self::$t->_('contract_archive_status.' . $item['status']) : '-';

            $item['store_name'] = $storeList[$item['store_id']] ?? $item['store_id'];
            $item['store_category_txt'] = $this->getStoreCateById($item['store_cate']);
            $item['lease_type_txt'] = $leaseTypes[$item['contract_lease_type']] ?? '-';

            //实际付款=税后
            $item['amount_detail'] = $moneyArr[$item['id']] ?? [];
            $maxLen = max($maxLen, count($item['amount_detail']));

            //区域信息
            $item['areaInfo'] = empty($area_info[$item['id']]) ? [] : $area_info[$item['id']];
            $maxAreaLen = max($maxAreaLen, count($item['areaInfo']));

            //银行信息
            $item['bank_collection'] = json_decode($item['bank_collection'],1);
            $maxBankLen = max($maxBankLen,count($item['bank_collection']));
            $item['leaser_type'] = $leaser_type[$item['leaser_type']] ?? '';

            //房屋类型
            $item['house_owner_type_txt'] = $this->getHouseOwnerTypeTxt($item['house_owner_type']);
            $item['money_symbol_txt'] = static::$t[GlobalEnums::$currency_item[$item['money_symbol']]];

            if (!empty($item['land_type'])) {
                $land_type_str = '';
                foreach (explode(',', $item['land_type']) as $k_1 => $v_1) {
                    $land_type_str .= $land_type[$v_1] ?? '';
                }

                $item['land_type'] = $land_type_str;
            }

            //除泰国外为空
            if (GlobalEnums::TH_COUNTRY_CODE != get_country_code()) {
                $item['leaser_type'] = '';
                $item['land_type']   = '';
            }
        }

        return ['items' => $items, 'maxLen' => $maxLen, 'maxBankLen' => $maxBankLen, 'maxAreaLen' => $maxAreaLen];
    }


    public function getArchiveStatus()
    {
        $item = ContractEnums::$contract_archive_status;
        if (!empty($item)) {
            foreach ($item as $index => &$t_key) {
                $t_key = self::$t[$t_key];
            }
            return $item;
        }
        return [];
    }

    /**
     * 归档合同-下载
     *
     * @param int $id
     * @return mixed
     * @throws GuzzleException
     */
    public function genArchiveDownload($id = 0)
    {
        $code = ErrCode::$SUCCESS;
        $message = 'success';
        $data = [];

        try {
            $contract = ContractStoreRentingModel::findFirst([
                'conditions' => 'id = :id: AND contract_status = :contract_status:',
                'bind' => ['id' => $id, 'contract_status' => Enums::CONTRACT_STATUS_APPROVAL]
            ]);
            if (empty($contract)) {
                throw new ValidationException(static::$t->_('data_not_exist_by_id', ['id' => $id]), ErrCode::$VALIDATE_ERROR);
            }

            // 获取租房合同正文附件
            $text_files = SysAttachmentRepository::getInstance()->getAttachmentsByBizParams($id, ContractEnums::RENT_CONTRACT_FILE_OSS_BUCKET_TYPE, ContractEnums::RENT_CONTRACT_SUB_TYPE_FILE_TEXT);
            if (empty($text_files)) {
                throw new ValidationException(static::$t->_('rent_contract_download_text_file_error'), ErrCode::$VALIDATE_ERROR);
            }

            // pdf 下载
            $file_path = $text_files[0]['object_key'];
            $file_name = $text_files[0]['file_name'];

            $file_url  = $this->getShowPath($file_path,true);
            $file_name = !empty($file_name) ? $file_name : 'StoreRentingContract_' . date('ymdHis') . '.pdf';

            $path = !empty($file_url['object_key']) ? OssHelper::downloadFileHcm($file_url['object_key'], 600) : '';
            $path = $path['file_url'] ?? '';
            // pdf 加水印
            $water_pdf_info = WaterMarkerService::getInstance()->addWaterMarkerToPdfFileV2($path, $file_name, true);
            if (!empty($water_pdf_info['object_key'])) {
                $result = OssHelper::downloadFileHcm($water_pdf_info['object_key']);
                $data['url'] = $result['file_url'];
            } else {
                $data['url'] = $path;
            }

            if (empty($data['url'])) {
                throw new ValidationException(static::$t->_('rent_contract_download_fail_error'), ErrCode::$VALIDATE_ERROR);
            }

        } catch (ValidationException $e) {
            $code = $e->getCode();
            $message = $e->getMessage();

        } catch (Exception $e) {
            $code = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $this->logger->warning('租房合同归档-正文附件下载异常, 原因可能是 . ' . $e->getMessage());
        }

        return [
            'code' => $code,
            'message' => $message,
            'data' => $data
        ];
    }


    public function getShowPath($path, $is_return_object_key = false)
    {
        if (empty($is_return_object_key)) {
            return $this->getDI()->get('config')->application->img_prefix . $path;
        } else {
            return ['url' => $this->getDI()->get('config')->application->img_prefix . $path, 'object_key' => $path];
        }
    }

    private function _exportNewArchive($items)
    {

        $new_data = [];
        foreach ($items as $key => $val) {
            $terminal_at = '';
            if (in_array($val['status'], [ContractEnums::CONTRACT_ARCHIVE_STATUS_TERMINAL, ContractEnums::CONTRACT_ARCHIVE_STATUS_TERMINAL_ING])) {
                $terminal_at = $val['terminal_at'];
            }
            $new_data[] = [
                $val['contract_id'],
                $val['dept_name'],
                $val['staff_name'],
                $val['manage_id'],
                $val['contract_name'],
                $val['store_name'],
                $val['contract_begin'],
                $val['contract_end'],
                $val['house_contract_area'],
                $val['lease_type_txt'],
                $val['contract_total_amount'],
                $val['money_symbol_txt'],
                $val['archive_status_txt'],
                $val['invalid_at'],
                $terminal_at,
                $val['filing_at'],
                $val['filing_name'],
                $val['holder_name'],
            ];
        }

        $file_name = "filing_rental_contracts_" . date("YmdHis");
        $header    = [
            static::$t->_('csr_field_contract_id'),//'合同编号',
            static::$t->_('csr_field_dept_name'),  //'申请人部门',
            static::$t->_('csr_field_staff_name'), //'申请人姓名',
            static::$t->_('csr_field_manage_id'),//'申请人工号',
            static::$t->_('csr_field_contract_name'),//'合同名称',
            static::$t->_('csr_field_store_name'),//'网点名称',
            static::$t->_('csr_field_contract_begin'),//'合同开始时间',
            static::$t->_('csr_field_contract_end'),//'合同结束时间',
            static::$t->_('csr_field_house_contract_area'),//'房屋合约面积',
            static::$t->_('payment_store_renting_pay_method'),//'付款方式',
            static::$t->_('csr_field_contract_total_amount'),//合同总金额（含WHT含VAT,
            static::$t->_('re_field_currency_text'),//'币种',
            static::$t->_('csr_filed_archive_status'),//'合同归档状态',
            static::$t->_('contract_export_field_invalid_at'),//'作废日期',
            static::$t->_('contract_export_field_terminal_at'),//合同终止日期
            static::$t->_('csr_field_filing_at'),//'合同归档日期',
            static::$t->_('csr_field_filing_name'),//'归档人',
            static::$t->_('csr_field_holder_name')//'原合同保管人'

        ];
        return $this->exportExcel($header, $new_data, $file_name);
    }


    private function _exportAll($items, $maxRentBeforeLen = 1, $maxBankLen = 1, $maxAreaLen = 1)
    {
        $new_data = [];
        $payer_res = $this->getPayer();

        $wht_cat_map = EnumsService::getInstance()->getWhtRateCategoryMap(0);
        $country_code = get_country_code();
        foreach ($items as $key => $val) {
            $tmp = [
                static::$t->_(Enums::$contract_is_master[$val['is_main']]),    //合同类型
                static::$t->_(Enums::$store_contract_lang[$val['contract_lang']]),  //合同语言
                empty($val['main_contract_id']) ? $val['relate_contract_id'] : $val['main_contract_id'],//主合同/loi 编号
                $val['dept_name'],  //申请人部门
                $val['staff_name'], //申请人姓名
                $val['manage_id'],  //申请人工号
                $val['contract_leader_id'],  // 负责人工号
                $val['leader_node_department_name'],  // 负责部门
                $val['contract_status_txt'],//申请状态

                $val['apply_at'],   //合同申请日期
                $val['archive_status_txt'],//合同归档状态
                $val['invalid_at'],     //合同作废日期
                $val['filing_at']??'',  //合同归档日期
                $val['terminal_at']??'',  // 合同终止日期
                $val['filing_name']??'',//合同归档人
                $val['holder_name']??'',//合同原件保管人

                $val['store_category_txt'],//网点类型
                $val['store_name'],//网点名称
                $val['store_id'],//网点编号
                $val['provinces'],//省份/大区
                $val['lon_lat'],//网点经纬度,
                $val['store_addr'],//网点地址

                $val['house_owner_name'],//房东姓名
                $val['house_contract_area'],//房屋合约面积
                $val['house_actual_area'],//房屋实际面积
                $val['house_owner_type_txt'],//房东类型
                $val['house_equip_list'],//设备清单
                $val['land_type'],//地契类型
                $val['leaser_type'],//出租人

                $val['contract_tax_no'],//房东税号

                $val['contract_name'],  //合同名称
                $val['contract_id'],    //合同编号
                $val['contract_deadline'],//合同期限

                $val['contract_begin'],//合同开始日期
                $val['contract_end'],//合同结束日期

                $val['rent_free_time'],//免租期
                $val['contract_effect_date'],//合同生效日期
                $val['lease_type_txt'], //合同租期->付款方式
                $val['amount_paid'], //已付金额
            ];


            for ($i=0; $i < $maxRentBeforeLen; $i++) {
                // 需判断无值的情况
                $_tmp_amount_detail = $val['amount_detail'][$i] ?? [];
                if (!empty($_tmp_amount_detail)) {
                    $tmp[] = $_tmp_amount_detail['rent_time'];
                    $tmp[] = $_tmp_amount_detail['amount_no_tax'];
                    $tmp[] = $_tmp_amount_detail['vat_rate'].'%';
                    $tmp[] = $_tmp_amount_detail['amount_vat'];
                    $tmp[] = $_tmp_amount_detail['amount_has_tax'];
                    $tmp[] = $wht_cat_map[$_tmp_amount_detail['wht_category']] ?? '';
                    $tmp[] = $_tmp_amount_detail['wht_rate'].'%';
                    $tmp[] = $_tmp_amount_detail["amount_wht"];
                    if ($country_code == GlobalEnums::TH_COUNTRY_CODE) {
                        $tmp[] = $_tmp_amount_detail['rent_no_wht'];
                    }
                } else {
                    $tmp[] = '';
                    $tmp[] = '';
                    $tmp[] = '';
                    $tmp[] = '';
                    $tmp[] = '';
                    $tmp[] = '';
                    $tmp[] = '';
                    $tmp[] = '';
                    if ($country_code == GlobalEnums::TH_COUNTRY_CODE) {
                        $tmp[] = '';
                    }
                }
            }

            $tmp[] = $val['contract_total_amount']; //合同总金额
            if ($country_code == GlobalEnums::TH_COUNTRY_CODE) {
                $tmp[] = $val['contract_total_amount_no_wht']; //合同总金额（不含WHT含VAT）
                $tmp[] = $val['rent_due_date'] < 10 ? ('0'. $val['rent_due_date']) : $val['rent_due_date'];//每月房租应付日
                $tmp[] = $val['month_rent_has_wht'];//月每平方米租金(含WHT含VAT)
            }

            $tmp[] = $val['deposit_amount'];    //合同押金
            $tmp[] = $val['contract_deposit']??'';  //合同定金
            $tmp[] = $val['cost_company_name']??'';  //合同所属公司
            $tmp[] = $val['money_symbol_txt']??'';  //币种

            $tmp[] = $val['contract_signer_name']??'';  //合同签署人姓名
            $tmp[] = $val['signer_phone']??'';//合同签署人联系电话
            $tmp[] = $val['full_refund_conditions']??'';//押金劝退条件
            $tmp[] = $val['contract_remarks']??'';//合同备注


            for ($i = 0; $i < $maxBankLen; $i++) {
                if (isset($val['bank_collection'][$i])) {
                    $tBank = $val['bank_collection'][$i];

                    //老版数据格式
                    if (isset($tBank['bank_name_1'])) {
                        //9
                        $tmp[]        = static::$t->_(Enums::$contract_pay_type_map[$tBank['pay_type_1'] ?? ''] ?? '') ?? '';  //付款方式
                        $tmp[]        = $tBank['bank_name_1'] ?? '';
                        $tmp[]        = $tBank['bank_account_title_1'] ?? '';
                        $tmp[]        = $tBank['bank_book_no_1'] ?? '';
                        $tmp[]        = $tBank['contact_mobile_1'] ?? '';
                        $tmp[]        = '';
                        // 老数据结构里key, 经排查用不到, 留着会导致Excel数据列与标题列不匹配
//                        $tmp[]        = $tBank['remit_amout'] ?? '';
                        $tmp[]        = '';
                        $tmp[]        = '';
                        $tmp[]        = '';
                    } else {
                        //9
                        $tmp[] = static::$t->_(Enums::$contract_pay_type_map[$tBank['pay_type'] ?? ''] ?? '') ?? '';  //付款方式
                        $tmp[] = $tBank['bank_name'] ?? '';  //银行名称
                        $tmp[] = $tBank['bank_account_name'] ?? '';  //银行账户名称
                        $tmp[] = $tBank['bank_account'] ?? '';   //银行账户号
                        $tmp[] = $tBank['contact_mobile'] ?? ''; //联系人电话号码
                        $tmp[] = $tBank['contact_emial'] ?? ''; //联系人邮箱
                      //  $tmp[] = $tBank['pay_amount'];     //汇款金额
                        $tmp[] = $this->getHouseOwnerTypeTxt($tBank['contact_type'] ?? '') ?? '';   //联系人类型 ,1=公司，2=个人，3=中介
                        $tmp[] = $tBank['contact_code'] ?? '';   //联系人身份证号/税号
                        $tmp[] = $tBank['sap_supplier_no']??''; //sap供应商号
                    }
                } else {
                    //9
                    $tmp[] = '';
                    $tmp[] = '';
                    $tmp[] = '';
                    $tmp[] = '';
                    $tmp[] = '';
                    $tmp[] = '';
                    $tmp[] = '';
                    $tmp[] = '';
                    $tmp[] = '';
                }
            }

            $tmp[] = $val['hourse_owner_addr'] ?? '';//房东收件地址
            if ($country_code == GlobalEnums::TH_COUNTRY_CODE) {
                $tmp[] = $val['issue_withholding_tax_address'] ?? '';//开具扣缴税款单据的地址
            }

            for ($i=0; $i < $maxAreaLen; $i++) {
                $_tmp_area_info = $val['areaInfo'][$i] ?? [];
                if (!empty($_tmp_area_info)) {
                    if ($country_code == GlobalEnums::TH_COUNTRY_CODE) {
                        $tmp[] = static::$t->_(ContractEnums::$tax_type_items[$_tmp_area_info['tax_type'] ?? '']) ?? '';
                    }
                    $tmp[] = $_tmp_area_info['rent_time'];
                    $tmp[] = $_tmp_area_info['area_service_amount_no_tax'];
                    $tmp[] = $_tmp_area_info['area_vat_rate'].'%';
                    $tmp[] = $_tmp_area_info['area_service_amount_vat'];
                    if ($country_code == GlobalEnums::TH_COUNTRY_CODE) {
                        $tmp[] = $_tmp_area_info['tax_amount_has_wht'];//税费金额（含WHT含VAT）
                    }
                    $tmp[] = $_tmp_area_info['area_amount_wht'];
                    if ($country_code == GlobalEnums::TH_COUNTRY_CODE) {
                        $tmp[] = $_tmp_area_info['tax_amount_no_wht'];//税费金额（不含WHT含VAT）
                    }
                } else {
                    if ($country_code == GlobalEnums::TH_COUNTRY_CODE) {
                        $tmp[] = '';
                    }
                    $tmp[] = '';
                    $tmp[] = '';
                    $tmp[] = '';
                    $tmp[] = '';
                    if ($country_code == GlobalEnums::TH_COUNTRY_CODE) {
                        $tmp[] = '';
                    }
                    $tmp[] = '';
                    if ($country_code == GlobalEnums::TH_COUNTRY_CODE) {
                        $tmp[] = '';
                    }
                }
            }

            $tmp[] = $payer_res[$val['property_tax']] ?? '';      //房产税付款人
            $tmp[] = $val['total_amount_monthly'] ?? '';  //房产税金额
            $tmp[] = $payer_res[$val['duty_stamp']] ?? '';    //印花税付款人
            $tmp[] = $val['total_amount'] ?? '';  //印花税金额
            $tmp[] = $payer_res[$val['billboard_tax_payer']] ?? '';//广告牌税付款人
            $tmp[] = $val['signboard_tax'] ?? '';//广告牌税金额
            $tmp[] = $payer_res[$val['land_tax_payer']] ?? '';//土地税付款人
            $tmp[] = $val['land_tax_amount'] ?? '';//土地税税
            $tmp[] = $payer_res[$val['fire_insurance_payer']] ?? '';//火灾保险费付款人
            $tmp[] = $val['fire_insurance_amount'] ?? '';//火灾保险费金税
            $tmp[] = $payer_res[$val['antimoth_payer']] ?? '';//房屋杀虫防蛀费付款人
            $tmp[] = $val['antimoth_amount'] ?? '';//房屋杀虫防蛀费金额
            if ($country_code == GlobalEnums::TH_COUNTRY_CODE) {
                //V22497
                $tmp[] = static::$t->_(WarehouseEnums::$thread_withholding_tax_liability_bearer[$val['withholding_tax_liability_bearer']] ?? ''); //承担扣缴税款责任方
                $tmp[] = static::$t->_(WarehouseEnums::$thread_water_bill_payment_type[$val['water_bill_payment_type']] ?? ''); //水费支付类型
                $tmp[] = static::$t->_(WarehouseEnums::$thread_electricity_bill_payment_type[$val['electricity_bill_payment_type']] ?? ''); //电费支付类型
                $tmp[] = static::$t->_(WarehouseEnums::$thread_water_billing_method[$val['water_billing_method']] ?? ''); //水费使用计费方式
                $tmp[] = static::$t->_(WarehouseEnums::$thread_electricity_billing_method[$val['electricity_billing_method']] ?? ''); //电费使用计费方式
                $tmp[] = $val['water_usage_units'] ?? '';//水费使用单位数
                $tmp[] = $val['electricity_usage_units'] ?? '';//电费使用单位数
            }


            $new_data[] = $tmp;
        }

        $file_name = "rental_contract_" . date("YmdHis") . '.xlsx';
        $header    = $this->getAllExportHeaders($maxRentBeforeLen,$maxBankLen,$maxAreaLen);
        return $this->exportExcel($header, $new_data, $file_name);
    }

    public function getContactType()
    {
        return [
            Enums::CONTRACT_CONTRACT_TYPE_COMPANY => self::$t->_('contract_contract_type_company'),
            Enums::CONTRACT_CONTRACT_TYPE_PERSION => self::$t->_('contract_contract_type_persion'),
            Enums::CONTRACT_CONTRACT_TYPE_AGENT   => self::$t->_('contract_contract_type_agent'),
        ];
    }

    // 获取地契类型
    public function getLandType()
    {
        $land_type_item = [];
        foreach (ContractEnums::$land_type_item as $k => $item) {
            $land_type_item[$k] = self::$t->_($item);
        }

        return $land_type_item;
    }

    // 获取出租人
    public function getLeaserType()
    {
        $leaser_type_item = [];
        foreach (ContractEnums::$leaser_type_item as $k => $item) {
            $leaser_type_item[$k] = self::$t->_($item);
        }

        return $leaser_type_item;
    }
    
    /**
     * 合同有效状态
     * @return array|string[]
     */
    public function getEffectiveStatus()
    {
        $item = ContractEnums::$rent_contract_effective_status;
        if (!empty($item)) {
            foreach ($item as $index => &$t_key) {
                $t_key = self::$t[$t_key];
            }
            return $item;
        }
        return [];
    }

    // 房租应付日
    public function getRentDueDate()
    {
        $dateList = [];
        for ($i = 1; $i <= 31; $i++) {
            if ($i < 10) {
                $dateList[$i] = '0' . $i;
            } else {
                $dateList[$i] = strval($i);
            }
        }

        return $dateList;

    }

    public function getHouseOwnerType()
    {
        $house_owner = [
            ContractEnums::HOUSE_OWNER_TYPE_COMPANY => static::$t->_('house_owner_type_1'),
            ContractEnums::HOUSE_OWNER_TYPE_PERSON  => static::$t->_('house_owner_type_2'),
            ContractEnums::HOUSE_OWNER_TYPE_AGENT   => static::$t->_('house_owner_type_3')

        ];

        foreach ($house_owner as $k => $v) {
            $house_owners[] = [
                'id'   => $k,
                'name' => $v
            ];
        }

        return $house_owners ?? [];
    }

    /**
     *
     * @param int $maxRentBeforeLen
     * @param int $maxRentAfterLen
     * @param int $maxBankLen
     * @param int $maxAreaLen
     * @return array
     */
    public function getAllExportHeaders($maxRentBeforeLen,$maxBankLen,$maxAreaLen){
        $country_code = get_country_code();
        $headerLangArr = [
            static::$t->_('csr_field_contract_type'),
            static::$t->_('csr_field_contract_lang'),
            static::$t->_('csr_field_contract_no_loi'),
            static::$t->_('csr_field_dept_name'),
            static::$t->_('csr_field_staff_name'),
            static::$t->_('csr_field_manage_id'),
            static::$t->_('csr_field_contract_leader_id'),
            static::$t->_('csr_field_contract_leader_department'),
            static::$t->_('csr_field_contract_status'),
            static::$t->_('csr_field_apply_at'),
            static::$t->_('csr_filed_archive_status'),
            static::$t->_('contract_export_field_invalid_at'),
            static::$t->_('csr_field_filing_at'),
            static::$t->_('contract_achive_ternimal_at'),
            static::$t->_('csr_field_filing_name'),
            static::$t->_('csr_field_holder_name'),
            static::$t->_('csr_filed_store_category'),
            static::$t->_('csr_field_store_name'),
            static::$t->_('csr_field_store_id'),
            static::$t->_('csr_field_store_province'),
            static::$t->_('csr_field_store_lng_lat'),
            static::$t->_('csr_field_store_address'),
            static::$t->_('csr_field_house_owner_name'),
            static::$t->_('csr_field_house_contract_area'),
            static::$t->_('csr_field_house_actual_area'),
            static::$t->_('csr_field_house_owner_type'),
            static::$t->_('csr_field_house_equip_list'),
            static::$t->_('csr_field_land_type'),//地契类型
            static::$t->_('csr_field_leaser_type'),//出租人
            static::$t->_('csr_field_contract_tax_no'),
            static::$t->_('csr_field_contract_name'),
            static::$t->_('csr_field_contract_id'),
            static::$t->_('csr_field_contract_deadline'),
            static::$t->_('csr_field_contract_begin'),
            static::$t->_('csr_field_contract_end'),
            static::$t->_('csr_field_rent_free_time'),
            static::$t->_('csr_field_contract_effect_date'),
            static::$t->_('payment_store_renting_pay_method'),
            static::$t->_('payment_store_renting_amount_paid'),
        ];


        //租金税前
        for($i=0;$i<$maxRentBeforeLen;$i++){
            //发生期间
           // $headerLangArr[] = static::$t->_('csr_field_rent_before')." ".($i+1).static::$t->_('csr_field_happen');
            //金额
         //   $headerLangArr[] = static::$t->_('csr_field_rent_before')." ".($i+1).static::$t->_('csr_field_amount');
            $headerLangArr[]= static::$t->_('csr_field_amount_happen').($i+1);
            $headerLangArr[] = static::$t->_('csr_field_date_no_tax').($i+1);//todo 显示对应每半年
            $headerLangArr[] = static::$t->_('payment_store_renting_vat_rate').($i+1);
            $headerLangArr[] = static::$t->_('csr_field_amount_vat').($i+1);
            $headerLangArr[] = static::$t->_('csr_field_amount_has_tax').($i+1);
            $headerLangArr[] = static::$t->_('payment_store_renting_wht_category').($i+1);
            $headerLangArr[] = static::$t->_('payment_store_renting_wht_category_tax').($i+1);
            $headerLangArr[] = static::$t->_('csr_field_amount_wht').($i+1);
            if ($country_code == GlobalEnums::TH_COUNTRY_CODE) {
                $headerLangArr[] = static::$t->_('22497_rent_no_wht').($i+1);//每月／季度／半年／年租金(不含WHT含VAT)
            }
        }


        $headerLangArr[] = static::$t->_('csr_field_contract_total_amount');
        if ($country_code == GlobalEnums::TH_COUNTRY_CODE) {
            $headerLangArr[] = static::$t->_('22497_contract_total_amount_no_wht');//合同总金额(不含WHT含VAT)
            $headerLangArr[] = static::$t->_('22497_rent_due_date');//每月房租应付日
            $headerLangArr[] = static::$t->_('22497_month_rent_has_wht');//月每平方米租金
        }
        $headerLangArr[] = static::$t->_('csr_field_deposit_amount');
        $headerLangArr[] = static::$t->_('csr_field_contract_deposit');//合同定金
        $headerLangArr[] = static::$t->_('csr_field_cost_company_name');//合同所属公司

        $headerLangArr[] = static::$t->_('csr_field_money_type');//付款币种

        $headerLangArr[] = static::$t->_('csr_field_contract_signer_name');
        $headerLangArr[] = static::$t->_('csr_field_signer_phone');
        $headerLangArr[] = static::$t->_('csr_field_full_refund_conditions');
        $headerLangArr[] = static::$t->_('csr_field_contract_remarks');//合同备注


        for($i=0;$i<$maxBankLen;$i++){
            $headerLangArr[] = static::$t->_('payment_method')." ".($i+1);
            $headerLangArr[] = static::$t->_('csr_field_bank_name')." ".($i+1);
            $headerLangArr[] = static::$t->_('csr_field_bank_account_name')." ".($i+1);
            $headerLangArr[] = static::$t->_('csr_field_bank_account')." ".($i+1);
            $headerLangArr[] = static::$t->_('csr_field_contact_mobile')." ".($i+1);
            $headerLangArr[] = static::$t->_('csr_field_contact_email')." ".($i+1);
        //    $headerLangArr[] = static::$t->_('csr_field_pay_amount')." ".($i+1);
            $headerLangArr[] = static::$t->_('csr_field_contact_type')." ".($i+1);
            $headerLangArr[] = static::$t->_('csr_field_contact_code')." ".($i+1);
            $headerLangArr[] = static::$t->_('csr_field_sap_supplier_no')." ".($i+1);

        }


        $headerLangArr[] = static::$t->_('csr_field_hourse_owner_addr');//房东收件地址
        if ($country_code == GlobalEnums::TH_COUNTRY_CODE) {
            $headerLangArr[] = static::$t->_('22497_issue_withholding_tax_address');//开具扣缴税款单据的地址
        }

        for($i=0; $i<$maxAreaLen; $i++){
            if ($country_code == GlobalEnums::TH_COUNTRY_CODE) {
                $headerLangArr[] = static::$t->_('22497_tax_type')." ".($i+1);//税费类型
            }
            $headerLangArr[] = static::$t->_('csr_field_area_service_rent_time')." ".($i+1);
            if ($country_code == GlobalEnums::TH_COUNTRY_CODE) {
                $headerLangArr[] = static::$t->_('22497_area_service_amount_no_tax')." ".($i+1);//税费金额（含WHT）
                $headerLangArr[] = static::$t->_('22497_area_vat_rate')." ".($i+1);//税费VAT税率
                $headerLangArr[] = static::$t->_('22497_area_service_amount_vat')." ".($i+1);//税费VAT税额
                $headerLangArr[] = static::$t->_('22497_tax_amount_has_wht')." ".($i+1);//税费金额（含WHT含VAT）
                $headerLangArr[] = static::$t->_('22497_area_amount_wht')." ".($i+1);//税费WHT金额
                $headerLangArr[] = static::$t->_('22497_tax_amount_no_wht')." ".($i+1);//税费金额（不含WHT含VAT）

            } else {
                $headerLangArr[] = static::$t->_('csr_field_area_service_amount_no_tax')." ".($i+1);
                $headerLangArr[] = static::$t->_('csr_field_area_service_amount_vat_rate')." ".($i+1);
                $headerLangArr[] = static::$t->_('csr_field_area_service_amount_vat')." ".($i+1);
                $headerLangArr[] = static::$t->_('csr_field_area_amount_wht')." ".($i+1);
            }
        }

//        $headerLangArr[] = static::$t->_('csr_field_area_service_amount_no_tax');
//        $headerLangArr[] = static::$t->_('csr_field_area_service_amount_vat');//区域服务费vat税额
//        //服务预扣税
//        $headerLangArr[] = static::$t->_('csr_field_area_amount_wht');//区域服务费wht金额


        $headerLangArr[] = static::$t->_('csr_field_property_tax');// 房产税付款人
        $headerLangArr[] = static::$t->_('csr_field_total_amount_monthly');//房产税金额
        $headerLangArr[] = static::$t->_('csr_field_duty_stamp');//印花税付款人
        $headerLangArr[] = static::$t->_('csr_field_total_amount');// 印花税金额'
        $headerLangArr[] = static::$t->_('csr_field_billboard_tax_payer');// 广告牌税付款人'

        $headerLangArr[] = static::$t->_('csr_field_new_signboard_tax');//广告牌税金额
        $headerLangArr[] = static::$t->_('csr_field_land_tax_payer');//土地税付款人
        $headerLangArr[] = static::$t->_('csr_field_land_tax_amount');//土地税金额
        $headerLangArr[] = static::$t->_('csr_field_fire_insurance_payer');//火灾保险费付款人
        $headerLangArr[] = static::$t->_('csr_field_fire_insurance_amount');//火灾保险费金额
        $headerLangArr[] = static::$t->_('csr_field_antimoth_payer');//房屋杀虫防蛀费付款人
        $headerLangArr[] = static::$t->_('csr_field_antimoth_amount');//房屋杀虫防蛀费金额
        if ($country_code == GlobalEnums::TH_COUNTRY_CODE) {
            $headerLangArr[] = static::$t->_('22497_withholding_tax_liability_bearer'); //承担扣缴税款责任方
            $headerLangArr[] = static::$t->_('22497_water_bill_payment_type');//水费支付类型
            $headerLangArr[] = static::$t->_('22497_electricity_bill_payment_type');//电费支付类型
            $headerLangArr[] = static::$t->_('22497_water_billing_method');//水费使用计费方式
            $headerLangArr[] = static::$t->_('22497_electricity_billing_method');//电费使用计费方式
            $headerLangArr[] = static::$t->_('22497_water_usage_units');//水费使用单位数
            $headerLangArr[] = static::$t->_('22497_electricity_usage_units');//电费使用单位数
        }


        return $headerLangArr;

    }

    /**
     * 根据网点类型ID获得类型
     * @param $cate_id
     * @return string
     */
    public function getStoreCateById($cate_id)
    {
        static $all_store_cate_map = null;
        if (is_null($all_store_cate_map)) {
            $all_store_cate_map = $this->getAllStoreCate();
            $all_store_cate_map = array_column($all_store_cate_map, 'name', 'id');
        }

        return $all_store_cate_map[$cate_id] ?? '-';
    }

    /**
     * 获取租房合同模块所有的网点类型枚举
     */
    public function getAllStoreCate()
    {
        // 所有网点类型: Head Office + 指定的网点类型
        $all_store_cate_enums = [
            Enums::HEAD_OFFICE_STORE_FLAG => Enums::PAYMENT_HEADER_STORE_NAME,
            Enums::STORE_CATEGORY_SP => Enums::STORE_CATEGORY_SP_LABEL,
            Enums::STORE_CATEGORY_DC => Enums::STORE_CATEGORY_DC_LABEL,
            Enums::STORE_CATEGORY_SHOP_PICKUP_ONLY => Enums::STORE_CATEGORY_SHOP_PICKUP_ONLY_LABEL,
            Enums::STORE_CATEGORY_SHOP_PICKUP_DELIVERY => Enums::STORE_CATEGORY_SHOP_PICKUP_DELIVERY_LABEL,
            Enums::STORE_CATEGORY_USHOP => Enums::STORE_CATEGORY_USHOP_LABEL,
            Enums::STORE_CATEGORY_HUB => Enums::STORE_CATEGORY_HUB_LABEL,
            Enums::STORE_CATEGORY_OS => Enums::STORE_CATEGORY_OS_LABEL,
            Enums::STORE_CATEGORY_BDC => Enums::STORE_CATEGORY_BDC_LABEL,
            Enums::STORE_CATEGORY_FFM => Enums::STORE_CATEGORY_FFM_LABEL,
            Enums::STORE_CATEGORY_BHUB => Enums::STORE_CATEGORY_BHUB_LABEL,
            Enums::STORE_CATEGORY_CDC => Enums::STORE_CATEGORY_CDC_LABEL,
            Enums::STORE_CATEGORY_PDC => Enums::STORE_CATEGORY_PDC_LABEL
        ];

        $all_store_cate_list = [];
        foreach ($all_store_cate_enums as $k => $v) {
            $all_store_cate_list[] = [
                'id'   => $k,
                'name' => $v
            ];
        }

        return $all_store_cate_list;
    }

    /**
     * 获得老状态文字,应该是bi老的合同状态。
     * @param $id
     * @return mixed
     */
    public function getOldStatusTxt($id){
        return static::$t->_("csr_old_status_".$id);
    }


    public function getHouseOwnerTypeTxt($id){
        if(empty($id)){
            return '';
        }
        return static::$t->_('house_owner_type_'.$id);
    }

    public function getPayer()
    {
        return [
            1=>self::$t->_('company_nature.2'),
            2=>self::$t->_('house_owner')
        ];
    }

    public function getPayType(){
        return [
            1=>self::$t->_('global.payment.method.2'),
            2=>self::$t->_('global.payment.method.3')
        ];
    }

    /**
     * 保存租房合同的信息明细数据
     *
     * @param array $amount_detail
     * @param $contract_id
     * @param array $contract_date
     * @return bool
     * @throws BusinessException
     * @throws ValidationException
     */
    protected function saveAmountDetailData(array $amount_detail, $contract_id, $contract_date = [])
    {
        // 删除旧数据
        $old_detail_models = ContractStoreRentingDetailModel::find([
            'conditions' => 'contract_store_renting_id = :contract_store_renting_id:',
            'bind'       => ['contract_store_renting_id' => $contract_id]
        ]);
        $old_detail_data = $old_detail_models->toArray();
        if (!empty($old_detail_data)) {
            if ($old_detail_models->delete() === false) {
                throw new BusinessException('租房合同-租金明细老数据删除失败, 原因可能是:' . get_data_object_error_msg($old_detail_models) . '; 数据=' . json_encode($old_detail_data, JSON_UNESCAPED_UNICODE), ErrCode::$BUSINESS_ERROR);
            }
        }

        // 写入新数据
        $amount_detail_item = []; //金额明细表数据
        foreach ($amount_detail as $index => $item) {
            if ($item['cost_start_date'] < $contract_date['contract_begin']) {
                throw new ValidationException(self::$t['contract_date_error_001'], ErrCode::$VALIDATE_ERROR);
            }
            if ($item['cost_end_date'] > $contract_date['contract_end']) {
                throw new ValidationException(self::$t['contract_date_error_002'], ErrCode::$VALIDATE_ERROR);
            }
            $amount_no_tax = $amount_vat_rate = $amount_vat_rate = 0;

            // 不含税金额
            $amount_no_tax = round($item['amount_no_tax'], 2);

            $amount_vat_rate = round($item['vat_rate']/100,2);

            // vat 7% 金额
            $amount_vat = $item['amount_vat'];
            $amount_detail_item[] = [
                'contract_store_renting_id' => $contract_id,
                'cost_start_date'           => $item['cost_start_date'], //费用发生日期
                'cost_end_date'             => $item['cost_end_date'], //费用结束日期
                'amount_no_tax'             => $amount_no_tax, //不含税金额
                'amount_vat'                => $amount_vat,//vat 金额 （不含税金额的7%）
                'amount_has_tax'            => $amount_no_tax + $amount_vat,//含税金额
                'wht_category'              => !empty($item['wht_category']) ? $item['wht_category'] : '0',//WHT类别
                'wht_rate'                  => $item['wht_rate'],//WHT税率
                'amount_wht'                => $item['amount_wht'],//WHT金额
                'vat_rate'                  => $item['vat_rate'],
                'created_at'                => date('Y-m-d H:i:s'),
                'updated_at'                => date('Y-m-d H:i:s'),
                'rent_no_wht'               => bcsub(bcadd($amount_no_tax, $amount_vat, 2), $item['amount_wht'], 2),//租金(不含WHT含VAT) = 含税租金(含WHT)-WHT金额
            ];
        }

        if ((new ContractStoreRentingDetailModel())->batch_insert($amount_detail_item) === false) {
            throw new BusinessException('租房合同-租金明细新数据写入失败, data=' . json_encode($amount_detail_item, JSON_UNESCAPED_UNICODE), ErrCode::$BUSINESS_ERROR);
        }

        return true;
    }

    public function getStoreRentingDetail($contractId = '')
    {
        $data = ContractStoreRentingDetailModel::find([
            'conditions' => 'contract_store_renting_id=:contract_id:',
            'bind'       => [
                'contract_id' => $contractId,
            ],
        ]);
        return $data->toArray();
    }

    public function getFlowId($userDept, $model, $biz_type = Enums::WF_STORE_RENTING_CONTRACT_TYPE)
    {
        $countryCode = get_country_code();
        if ($biz_type == Enums::WF_STORE_RENTING_CONTRACT_TYPE) {
            //合同申请审批流
            // 老挝只有一条审批流
            if (GlobalEnums::LA_COUNTRY_CODE == $countryCode) {
                return Enums::CONTRACT_STORE_RENTING_FFM_WF_ID;
            }
            if (GlobalEnums::PH_COUNTRY_CODE == $countryCode) {
                return $this->getPhFlowId($model);
            }
            $deptId = $userDept['sys_department_id'] ?? '';
            $is_ffm = (new DepartmentService())->isSonByDepartmentId($deptId);
            if ($is_ffm) {
                return Enums::CONTRACT_STORE_RENTING_FFM_WF_ID;
            }
            if ($model['is_main'] == Enums::CONTRACT_IS_LOI_YES && in_array($countryCode, [GlobalEnums::MY_COUNTRY_CODE])) {
                return $this->getFlowIdForLoi($model);
            }
            return Enums::CONTRACT_STORE_RENTING_NOT_FFM_WF_ID;
        } else if ($biz_type == Enums::WF_STORE_RENTING_CONTRACT_RENEWAL_TYPE) {
            //合同申请审批流
            // 老挝只有一条审批流
            if (GlobalEnums::LA_COUNTRY_CODE == $countryCode) {
                return Enums::CONTRACT_STORE_RENTING_FFM_RENEWAL_WF_ID;
            }
            if (GlobalEnums::PH_COUNTRY_CODE == $countryCode) {
                return $this->getPhFlowId($model, $biz_type);
            }
            $deptId = $userDept['sys_department_id'] ?? '';
            $is_ffm = (new DepartmentService())->isSonByDepartmentId($deptId);
            if ($is_ffm) {
                return Enums::CONTRACT_STORE_RENTING_FFM_RENEWAL_WF_ID;
            }
            if ($model['is_main'] == Enums::CONTRACT_IS_LOI_YES && in_array($countryCode, [GlobalEnums::MY_COUNTRY_CODE])) {
                return $this->getFlowIdForLoi($model, $biz_type);
            }
            return Enums::CONTRACT_STORE_RENTING_NOT_FFM_RENEWAL_WF_ID;
        }elseif ($biz_type == Enums::WF_STORE_RENTING_CONTRACT_INVALID_TYPE) {
            //合同作废审批流
            if ($model['is_main'] == Enums::CONTRACT_IS_LOI_YES && in_array($countryCode, [GlobalEnums::PH_COUNTRY_CODE,GlobalEnums::MY_COUNTRY_CODE])) {
                return $this->getFlowIdForLoi($model, $biz_type);
            }
            return Enums::CONTRACT_INVALID_WF_ID;
        } elseif ($biz_type == Enums::WF_STORE_RENTING_CONTRACT_TERMINAL_TYPE) {
            //合同终止审批流
            if ($model['is_main'] == Enums::CONTRACT_IS_LOI_YES && in_array($countryCode, [GlobalEnums::PH_COUNTRY_CODE,GlobalEnums::MY_COUNTRY_CODE])) {
                return $this->getFlowIdForLoi($model, $biz_type);
            }
            return Enums::CONTRACT_TERMINAL_WF_ID;
        } else {
            throw new ValidationException(static::$t->_('contract_store_renting_not_found_flow_id'), ErrCode::$VALIDATE_ERROR);
        }

    }


    /**
     * @param $id
     * @param $biz_type
     * @return \Phalcon\Mvc\Model
     */
    public function getRequest($id, $biz_type)
    {
        return WorkflowRequestModel::findFirst(
            [
                'biz_type = :type: and biz_value= :id: and is_abandon = :is_abandon:',
                'bind' => ['type' => $biz_type, 'id' => $id, 'is_abandon' => GlobalEnums::WORKFLOW_ABANDON_STATE_NO]
            ]
        );
    }

    /**
     * @param null $model
     * @param int $biz_type
     * @return int
     * @throws ValidationException
     */
    public function getFlowIdForLoi($model = null, $biz_type = Enums::WF_STORE_RENTING_CONTRACT_TYPE)
    {
        if ($biz_type == Enums::WF_STORE_RENTING_CONTRACT_TYPE) {
            $store   = (new WorkflowServiceV2())->getStoreById($model['store_id'] ?? 0);
            switch ($store['category']) {
                //network
                case 1:
                case 2:
                case 10:
                    $flow_id = Enums::CONTRACT_STORE_RENTING_LOI_NETWORK_WF_ID; //报销网点（Network）
                    break;
                //hub
                case 8:
                case 9:
                case 12:
                    $flow_id = Enums::CONTRACT_STORE_RENTING_LOI_HUB_WF_ID; //网点（hub）
                    break;
                default:
                    $flow_id = Enums::CONTRACT_STORE_RENTING_LOI_OTHER_WF_ID; //其它）
                    break;
            }
        } else if ($biz_type == Enums::WF_STORE_RENTING_CONTRACT_RENEWAL_TYPE) {
            $store = (new WorkflowServiceV2())->getStoreById($model['store_id'] ?? 0);
            switch ($store['category']) {
                //network
                case 1:
                case 2:
                case 10:
                    $flow_id = Enums::CONTRACT_STORE_RENTING_LOI_NETWORK_RENEWAL_WF_ID; //报销网点（Network）
                    break;
                //hub
                case 8:
                case 9:
                case 12:
                    $flow_id = Enums::CONTRACT_STORE_RENTING_LOI_HUB_RENEWAL_WF_ID; //网点（hub）
                    break;
                default:
                    $flow_id = Enums::CONTRACT_STORE_RENTING_LOI_OTHER_RENEWAL_WF_ID; //其它）
                    break;
            }
        }elseif ($biz_type == Enums::WF_STORE_RENTING_CONTRACT_INVALID_TYPE) {
            $flow_id = Enums::CONTRACT_INVALID_LOI_WF_ID;
        } elseif ($biz_type == Enums::WF_STORE_RENTING_CONTRACT_TERMINAL_TYPE) {
            $flow_id = Enums::CONTRACT_TERMINAL_LOI_WF_ID;
        } else {
            throw new ValidationException(static::$t->_('contract_store_renting_not_found_flow_id'), ErrCode::$VALIDATE_ERROR);
        }
        return $flow_id;
    }

    /**
     * 菲律宾新增和续签审批流
     * @param null $model
     * @param int $biz_type
     * @return int
     * @throws ValidationException
     */
    public function getPhFlowId($model = null, $biz_type = Enums::WF_STORE_RENTING_CONTRACT_TYPE)
    {
        $store        = (new WorkflowServiceV2())->getStoreById($model['store_id'] ?? 0);
        $store_config = EnumsService::getInstance()->getSettingEnvValueMap('contract_store_renting_store_cate_config');
        $is_main      = $model['is_main'];
        if ($biz_type == Enums::WF_STORE_RENTING_CONTRACT_TYPE) {

            if (in_array($store['category'], $store_config['network'])) {
                if ($is_main == Enums::CONTRACT_IS_LOI_YES) {
                    $flow_id = Enums::CONTRACT_STORE_RENTING_LOI_NETWORK_WF_1_ID; //意向书网点（Network）
                } else {
                    $flow_id = Enums::CONTRACT_STORE_RENTING_NETWORK_WF_ID; //合同或者附属合同 网点（Network）

                }

            } else if (in_array($store['category'], $store_config['hub'])) {
                if ($is_main == Enums::CONTRACT_IS_LOI_YES) {
                    $flow_id = Enums::CONTRACT_STORE_RENTING_LOI_HUB_WF_ID; //网点（hub）
                } else {
                    $flow_id = Enums::CONTRACT_STORE_RENTING_HUB_WF_ID; //网点（hub）

                }
            } else {
                if ($is_main == Enums::CONTRACT_IS_LOI_YES) {
                    $flow_id = Enums::CONTRACT_STORE_RENTING_LOI_OTHER_WF_ID; //其它
                } else {
                    $flow_id = Enums::CONTRACT_STORE_RENTING_OTHER_WF_ID; //其它
                }
            }

        } else if ($biz_type == Enums::WF_STORE_RENTING_CONTRACT_RENEWAL_TYPE) {

            if (in_array($store['category'], $store_config['network'])) {
                if ($is_main == Enums::CONTRACT_IS_LOI_YES) {
                    $flow_id = Enums::CONTRACT_STORE_RENTING_LOI_NETWORK_RENEWAL_WF_1_ID; //网点（Network）
                } else {
                    $flow_id = Enums::CONTRACT_STORE_RENTING_NETWORK_RENEWAL_WF_ID; //网点（Network）

                }

            } else if (in_array($store['category'], $store_config['hub'])) {
                if ($is_main == Enums::CONTRACT_IS_LOI_YES) {

                    $flow_id = Enums::CONTRACT_STORE_RENTING_LOI_HUB_RENEWAL_WF_ID; //网点（hub）
                } else {
                    $flow_id = Enums::CONTRACT_STORE_RENTING_HUB_WF_RENEWAL_ID; //网点（hub）

                }

            } else {
                if ($is_main == Enums::CONTRACT_IS_LOI_YES) {

                    $flow_id = Enums::CONTRACT_STORE_RENTING_LOI_OTHER_RENEWAL_WF_ID; //其它
                } else {
                    $flow_id = Enums::CONTRACT_STORE_RENTING_OTHER_RENEWAL_WF_ID; //其它

                }
            }

        } else {
            throw new ValidationException(static::$t->_('contract_store_renting_not_found_flow_id'), ErrCode::$VALIDATE_ERROR);
        }
        return $flow_id;
    }

    /**
     * 归档合同编辑保存
     * @param $loginUser
     * @param $params
     * @return array
     */
    public function editContract($loginUser, $params)
    {
        $code = ErrCode::$SUCCESS;
        $message = static::$t->_('success');

        //保存合同信息
        $db = $this->getDI()->get('db_oa');
        $db->begin();
        try {
            $model = ContractStoreRentingModel::findFirst([
                'conditions' => 'id = :id:',
                'bind' => ['id' => $params['id']]
            ]);
            if (empty($model)) {
                throw new ValidationException(static::$t->_('store_renting_contract_info_null', ['sr_id' => $params['id']]), ErrCode::$VALIDATE_ERROR);
            }

            $archive_model = ContractArchive::findFirst([
                'conditions' => 'cno = :cno:',
                'bind'       => ['cno' => $model->contract_id]
            ]);
            if (empty($archive_model)) {
                throw new ValidationException(static::$t->_('archive_contract_is_not_exist'), ErrCode::$VALIDATE_ERROR);
            }

            // 租金信息 和 税费信息明细行验证, 不可增删
            $renting_detail_models = $model->getRentingDetail();
            $renting_detail_array = $renting_detail_models->toArray();
            $submit_amount_detail_ids = array_column($params['amount_detail'], 'id');
            $exist_amount_detail_ids = array_column($renting_detail_array, 'id');
            if (!arrays_is_equal($submit_amount_detail_ids, $exist_amount_detail_ids)) {
                throw new ValidationException(static::$t->_('rent_archive_contract_save_error_001'), ErrCode::$VALIDATE_ERROR);
            }

            $renting_area_models = $model->getRentingArea();
            $renting_area_array = $renting_area_models->toArray();
            $submit_area_info_ids = array_column($params['areaInfo'], 'id');
            $exist_area_info_ids = array_column($renting_area_array, 'id');
            if (!arrays_is_equal($submit_area_info_ids, $exist_area_info_ids)) {
                throw new ValidationException(static::$t->_('rent_archive_contract_save_error_002'), ErrCode::$VALIDATE_ERROR);
            }

            $oldModel = clone $model;

            // 合同编辑前后的数据
            $before_bank_collection = empty($model->bank_collection) ? json_decode($model->bank_collection, true) : [];
            $edit_before_data_new = [
                'base_info' => $model->toArray(),
                'bank_collection' => is_array($before_bank_collection) ? $before_bank_collection : [],
                'amount_detail' => $renting_detail_array,
                'area_info' => $renting_area_array,
                'attachment_list' => [],
            ];
            $edit_after_data_new = [
                'base_info' => [],
                'bank_collection' => $params['bank_collection'],
                'amount_detail' => [],
                'area_info' => [],
                'attachment_list' => [],
            ];

            // 保存前合同数据记录
            $this->logger->info('editContract before & ContractStoreRentingModel=>' . json_encode($model->toArray(), JSON_UNESCAPED_UNICODE));

            // 网点信息
            $model->provinces                   = $params['provinces'];
            $model->lon_lat                     = $params['lon_lat'];
            $model->store_addr                  = $params['store_addr'];

            // 付款信息
            $model->bank_collection             = json_encode($params['bank_collection'], JSON_UNESCAPED_UNICODE);
            // 其他附件
            $model->pdf_noneed_name             = '';// v18530 迁移到系统附件表 json_encode($params['pdf_noneed_name'], JSON_UNESCAPED_UNICODE);
            // 地契类型
            $model->land_type                   = !empty($params['land_type']) ? implode(',',$params['land_type']) : '';
            $model->land_type_content           = $params['land_type_content'];
            // 出租人类型
            $model->leaser_type                 = $params['leaser_type'];
            $model->leaser_type_content         = $params['leaser_type_content'];
            // 房租应付日
            $model->rent_due_date               = $params['rent_due_date'];

            // 房屋合约面积
            $model->house_contract_area = $params['house_contract_area'];

            // 房屋实际面积
            $model->house_actual_area = $params['house_actual_area'];

            // 是否有免租期
            $model->is_rent_free = $params['is_rent_free'];

            // 合同总金额（含WHT含VAT）
            $model->contract_total_amount = $params['contract_total_amount'];

            // 合同总金额（不含VAT含WHT）
            $model->contract_total_amount_contain_wht = $params['contract_total_amount_contain_wht'] ?? '0.00';

            // 合同押金
            $model->deposit_amount = $params['deposit_amount'];

            // 房东姓名
            $model->house_owner_name = $params['house_owner_name'];

            // 合同签署人姓名
            $model->contract_signer_name = $params['contract_signer_name'];

            // 合同备注
            $model->contract_remarks = $params['contract_remarks'];

            // 需提前通知续租/不续签时间
            $model->notice_renewal_days = $params['notice_renewal_days'] ?? '';

            // 装修所需时间
            $model->renovation_days = $params['renovation_days'] ?? '';

            $valueChangeArr = [];
            $is_modify_update = false;

            // 仓库信息
            if ($oldModel->warehouse_id != $params['warehouse_id']) {
                $model->warehouse_id = $params['warehouse_id'];
                $model->warehouse_latitude = $params['warehouse_latitude'];
                $model->warehouse_longitude = $params['warehouse_longitude'];
                $model->warehouse_name = $params['warehouse_name'];
                $model->warehouse_province_name = $params['warehouse_province_name'];
                $model->warehouse_city_name = $params['warehouse_city_name'];
                $model->warehouse_district_name = $params['warehouse_district_name'];
                $model->warehouse_real_area = $params['warehouse_real_area'];

                // 进修改记录
                $valueChangeArr['warehouse_id'] = $params['warehouse_id'];
            }

            //V22497 增加以下字段存储
            $model->warehouse_price_id               = $params['warehouse_price_id'] ?? 0;
            $model->warehouse_price_no               = $params['warehouse_price_no'] ?? '';
            $model->withholding_tax_liability_bearer = $params['withholding_tax_liability_bearer'] ?? 0;//扣缴税责任承担方
            $model->water_bill_payment_type          = $params['water_bill_payment_type'] ?? 0;         //水费支付类型
            $model->electricity_bill_payment_type    = $params['electricity_bill_payment_type'] ?? 0;   //电费支付类型
            $model->water_billing_method             = $params['water_billing_method'] ?? 0;            //水费使用计费方式
            $model->electricity_billing_method       = $params['electricity_billing_method'] ?? 0;      //电费使用计费方式
            $model->water_usage_units                = $params['water_usage_units'] ?? null;            //水费使用单位数
            $model->electricity_usage_units          = $params['electricity_usage_units'] ?? null;      //电费使用单位数
            $model->contract_total_amount_no_wht     = $params['contract_total_amount_no_wht'] ?? null;  //合同总金额（不含WHT含VAT）
            $model->month_rent_has_wht               = get_country_code() == GlobalEnums::TH_COUNTRY_CODE ? $this->calculateMonthRentHasWht($params) : null; //月每平方米租金（含WHT含VAT）
            $model->issue_withholding_tax_address    = $params['issue_withholding_tax_address'] ?? '';  //开具扣缴税款单据的地址

            $store_name = '';
            $storeArr = SysStoreModel::find([
                'conditions' => 'id in ({ids:array})',
                'bind' => ['ids' => [$params['store_id'], $model->store_id]],
                'columns' => ['id', 'name']
            ])->toArray();
            $storeMap = array_column($storeArr,'name','id');
            if ($oldModel->store_id != $params['store_id']) {
                $valueChangeArr['store_name'] = isset($storeMap[$params['store_id']]) ? $storeMap[$params['store_id']] : '';
                $store_name = $storeMap[$model->store_id];
                $model->store_id = $params['store_id'];
            }
            if ($oldModel->store_cate != $params['store_cate']) {
                $valueChangeArr['store_cate'] = $params['store_cate'];
                $model->store_cate = $params['store_cate'];
            }
            // 合同开始时间
            if ($oldModel->contract_begin != $params['contract_begin']) {
                $is_modify_update = true;
                $valueChangeArr['contract_begin'] = !empty($params['contract_begin']) ? $params['contract_begin'] : '0000-00-00';
                $model->contract_begin = $params['contract_begin'];
            }
            // 合同结束时间
            if ($oldModel->contract_end != $params['contract_end']) {
                $is_modify_update = true;
                $valueChangeArr['contract_end'] = !empty($params['contract_end']) ? $params['contract_end'] : '0000-00-00';
                $model->contract_end = $params['contract_end'];
            }
            // 合同免租日期
            $rent_free_time_str = implode(',',$params['rent_free_time']);
            $rent_free_time_str = !empty($rent_free_time_str) ? $rent_free_time_str : ' ';
            if (trim($oldModel->rent_free_time) != trim($rent_free_time_str)) {
                $is_modify_update = true;
                $valueChangeArr['rent_free_time'] =
                $model->rent_free_time = !empty(trim($rent_free_time_str)) ? $rent_free_time_str : ' ';
            } elseif(empty(trim($rent_free_time_str))) {
                $model->rent_free_time = $rent_free_time_str;
            }
            // 合同生效日期
            $contract_effect_date = '0000-00-00' == $oldModel->contract_effect_date ? '' : $oldModel->contract_effect_date;
            if ($contract_effect_date != trim($params['contract_effect_date'])) {
                $is_modify_update = true;
                $valueChangeArr['contract_effect_date'] = !empty($params['contract_effect_date']) ? $params['contract_effect_date'] : '0000-00-00';
                $model->contract_effect_date = $params['contract_effect_date'];
            }
            $model->contract_effect_date = !empty($model->contract_effect_date) ? $model->contract_effect_date : '0000-00-00';
            if ($oldModel->hourse_owner_addr != $params['hourse_owner_addr']) {
                $valueChangeArr['hourse_owner_addr'] = $params['hourse_owner_addr'];
                $model->hourse_owner_addr = $params['hourse_owner_addr'];
            }
            // 地契类型变更
            if ($oldModel->land_type != $model->land_type || $oldModel->land_type_content != $model->land_type_content){
                $valueChangeArr['land_type'] = $model->land_type;
                $valueChangeArr['land_type_content'] = $model->land_type_content;
            }
            // 出租人变更
            if ($oldModel->leaser_type != $model->leaser_type || $oldModel->leaser_type_content != $model->leaser_type_content) {
                $valueChangeArr['leaser_type'] = $model->leaser_type;
                $valueChangeArr['leaser_type_content'] = $model->leaser_type_content;
            }

            // 房租应付日
            if ($oldModel->rent_due_date != $model->rent_due_date) {
                $rentDueDate = $this->getRentDueDate();
                $valueChangeArr['rent_due_date'] = $rentDueDate[$model->rent_due_date];
                $oldModel->rent_due_date = $rentDueDate[$oldModel->rent_due_date];
            }

            // 付款信息
            $old_bank_collection = json_decode($oldModel->bank_collection,true);
            $bank_collection = json_decode($model->bank_collection,true);
            $is_diff = false;
            if (count($old_bank_collection) != count($bank_collection)) {
                $is_modify_update = true;
                $is_diff = true;
            }

            foreach ($bank_collection as $k => $bank_info) {
                if ($bank_info['contact_mobile'] != $old_bank_collection[$k]['contact_mobile']
                    || $bank_info['contact_emial'] != $old_bank_collection[$k]['contact_emial']
                    || $bank_info['sap_supplier_no'] != $old_bank_collection[$k]['sap_supplier_no']
                ) {
                    $is_diff = true;
                    break;
                }
            }
            if ($is_diff) {
                $valueChangeArr['bank_collection'] = $model->bank_collection;
            }

            // 更新修改说明
            if (in_array($archive_model->status, [
                ContractEnums::CONTRACT_ARCHIVE_STATUS_APPROVAL,
                ContractEnums::CONTRACT_ARCHIVE_STATUS_NO_UPLOAD
            ])
            ) {
                $model->modify_desc = $params['modify_desc'];
            }

            // 当前用户是否是超管: 超管的编辑提交, 不需要走审批流 v21111
            $is_privilege_staff = EnumsService::getInstance()->isPrivilegeStaffId($loginUser['id']);

            // 涉及到合同开始/结束日期,免租期,合同生效日期更改,以及新增/删除付款信息 需要重走审批流
            if (in_array($archive_model->status, [
                    ContractEnums::CONTRACT_ARCHIVE_STATUS_APPROVAL,
                    ContractEnums::CONTRACT_ARCHIVE_STATUS_NO_UPLOAD,
                ]) && $is_modify_update && !$is_privilege_staff
            ) {
                // 审核状态修改
                $model->contract_status = Enums::CONTRACT_STATUS_PENDING;
                $model->apply_at        = date('Y-m-d H:i:s');
                $model->approved_at = $model->rejected_at = null;
                $contract = $model->toArray();

                // 删除前日志
                $this->logger->info('delete before & contract archive model=>' . json_encode($archive_model->toArray(), JSON_UNESCAPED_UNICODE));

                // 删除已归档的合同
                if ($archive_model->delete() === false) {
                    throw new BusinessException('归档合同编辑提交-已归档数据删除失败, 原因可能是:' . get_data_object_error_msg($archive_model), ErrCode::$BUSINESS_ERROR);
                }

                // 审批更新
                $flow_bool = ContractStoreRentingService::getInstance()->recommitWorkFlow($contract, $loginUser);
                if ((is_bool($flow_bool) && $flow_bool === false) || isset($flow_bool['error_msg'])) {
                    throw new BusinessException('归档合同编辑提交-审批流创建失败, result=' . json_encode($flow_bool, JSON_UNESCAPED_UNICODE), ErrCode::$BUSINESS_ERROR);
                }
            }

            // 编辑后合同日志记录
            $this->logger->info('after & ContractStoreRentingModel=>' . json_encode($model->toArray(), JSON_UNESCAPED_UNICODE));
            if ($model->save() === false) {
                throw new BusinessException('归档合同编辑提交-主数据保存失败, 原因可能是:' . get_data_object_error_msg($model), ErrCode::$BUSINESS_ERROR);
            }

            $edit_after_data_new['base_info'] = $model->toArray();

            // 租金信息明细 更新
            $submit_amount_details = array_column($params['amount_detail'], null, 'id');
            foreach ($renting_detail_models as $renting_model) {
                $amount_info = $submit_amount_details[$renting_model->id];
                $amount_no_tax = round($amount_info['amount_no_tax'], 2);
                $amount_vat = round($amount_info['amount_vat'], 2);

                $renting_model->cost_start_date = $amount_info['cost_start_date'];
                $renting_model->cost_end_date = $amount_info['cost_end_date'];
                $renting_model->amount_no_tax = $amount_no_tax;
                $renting_model->vat_rate = $amount_info['vat_rate'];
                $renting_model->amount_vat = $amount_vat;
                $renting_model->amount_has_tax = $amount_no_tax + $amount_vat;
                $renting_model->wht_category = !empty($amount_info['wht_category']) ? $amount_info['wht_category'] : '0';
                $renting_model->wht_rate = $amount_info['wht_rate'];
                $renting_model->amount_wht = $amount_info['amount_wht'];
                $renting_model->updated_at = date('Y-m-d H:i:s');
                $renting_model->rent_no_wht = bcsub(bcadd($amount_no_tax, $amount_vat, 2), $amount_info['amount_wht'], 2);//租金(不含WHT含VAT) = 含税租金(含WHT)-WHT金额

                $renting_new_data = $renting_model->toArray();

                $this->logger->info(['renting_detail_update_' . $renting_model->id => $renting_new_data]);
                if ($renting_model->save() === false) {
                    throw new BusinessException("租金明细行保存失败[id={$renting_model->id}], 原因可能是" . get_data_object_error_msg($renting_model), ErrCode::$BUSINESS_ERROR);
                }

                $edit_after_data_new['amount_detail'][] = $renting_new_data;
            }

            // 税费信息明细 更新
            $submit_area_info = array_column($params['areaInfo'], null, 'id');
            foreach ($renting_area_models as $area_model) {
                $area_info = $submit_area_info[$area_model->id];

                $area_model->start_time = $area_info['area_start_time'] ? $area_info['area_start_time'] : null;
                $area_model->end_time = $area_info['area_end_time'] ? $area_info['area_end_time'] : null;
                $area_model->area_service_amount_no_tax = $area_info['area_service_amount_no_tax'] ? $area_info['area_service_amount_no_tax'] : 0;
                $area_model->area_vat_rate = $area_info['area_vat_rate'] ? $area_info['area_vat_rate'] : 0;
                $area_model->area_service_amount_vat = $area_info['area_service_amount_vat'] ? $area_info['area_service_amount_vat'] : 0;
                $area_model->area_wht_category = $area_info['area_wht_category'] ? $area_info['area_wht_category'] : 0;
                $area_model->area_wht_rate = $area_info['area_wht_rate'] ? $area_info['area_wht_rate'] : 0;
                $area_model->area_amount_wht = $area_info['area_amount_wht'] ? $area_info['area_amount_wht'] : 0;
                $area_model->updated_at = date('Y-m-d H:i:s');
                $area_model->tax_type = $area_info['tax_type'] ?? 0;
                $area_model->tax_amount_has_wht = bcadd($area_model->area_service_amount_no_tax, $model->area_service_amount_vat, 2);//税费金额（含WHT含VAT） = 税费金额（含WHT）+税费VAT税额
                $area_model->tax_amount_no_wht = bcsub(bcadd($area_model->area_service_amount_no_tax, $area_model->area_service_amount_vat, 2), $area_model->area_amount_wht, 2);//税费金额（不含WHT含VAT) = 税费金额（含WHT含VAT）-税费WHT金额

                $area_new_data = $area_model->toArray();

                $this->logger->info(['renting_area_update_' . $area_model->id => $area_new_data]);
                if ($area_model->save() === false) {
                    throw new BusinessException("税费明细行保存失败[id={$area_model->id}], 原因可能是" . get_data_object_error_msg($area_model), ErrCode::$BUSINESS_ERROR);
                }

                $edit_after_data_new['area_info'][] = $area_new_data;
            }

            // 处理租房合同附件
            $attachment_save_res = $this->saveCommonAttachmentFiles($params['attachment_list'], $model);
            $edit_before_data_new['attachment_list'] = $attachment_save_res['old_attachments'];
            $edit_after_data_new['attachment_list'] = $attachment_save_res['new_attachments'];

            if (!empty($valueChangeArr)) {
                // 编辑后字段更新记录
                $logModel = new ContractArchiveEditLogModel();
                $logModel->provinces = $model->provinces;
                $logModel->lon_lat = $model->lon_lat;
                $logModel->store_addr = $model->store_addr;
                $logModel->contract_name = $model->contract_name;
                $logModel->contract_id = $model->contract_id;
                $logModel->operator_id = $loginUser['id'];
                $logModel->operator_name = $loginUser['name'];
                $logModel->created_at = date('Y-m-d H:i:s');
                $logModel->updated_at = date('Y-m-d H:i:s');

                foreach ($valueChangeArr as $k => $value) {
                    $kold = $k . '_old';
                    // bank_collection
                    if ('bank_collection' == $k) {
                        $logModel->$kold = $this->trimBankCollection($oldModel->$k);
                        $logModel->$k = $this->trimBankCollection($value);
                        continue;
                    }
                    $logModel->$kold = 'store_name' == $k ? $store_name : $oldModel->$k;
                    $logModel->$k = $value;
                }

                // 是否走审批流
                if (in_array($archive_model->status, [ContractEnums::CONTRACT_ARCHIVE_STATUS_APPROVAL, ContractEnums::CONTRACT_ARCHIVE_STATUS_NO_UPLOAD])) {
                    $logModel->modify_desc = $params['modify_desc'];// 更新修改说明
                }

                if (in_array($archive_model->status, [ContractEnums::CONTRACT_ARCHIVE_STATUS_APPROVAL, ContractEnums::CONTRACT_ARCHIVE_STATUS_NO_UPLOAD]) && $is_modify_update) {
                    $logModel->need_workflow = 1; // 需要审批
                }

                if ($logModel->save() === false) {
                    throw new BusinessException('归档合同编辑提交-编辑日志保存失败, 原因可能是:' . get_data_object_error_msg($logModel) . '; 数据=' . json_encode($logModel->toArray(), JSON_UNESCAPED_UNICODE), ErrCode::$BUSINESS_ERROR);
                }
            }

            // 编辑日志
            $edit_before_data_new['base_info']['store_name'] = $storeMap[$oldModel->store_id] ?? '';
            $edit_after_data_new['base_info']['store_name'] = $storeMap[$model->store_id] ?? '';
            $this->createAchiveContractEditLog($model, $edit_before_data_new, $edit_after_data_new, $loginUser);

            $db->commit();

        }  catch (ValidationException $e) {
            $code = $e->getCode();
            $message = $e->getMessage();

        } catch (BusinessException $e) {
            $code = ErrCode::$BUSINESS_ERROR;
            $message = static::$t->_('retry_later');
            $this->logger->warning('edit_archive_contract_save_error, message = ' . $e->getMessage());

        } catch (Exception $e) {
            $code = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $this->logger->error('edit_archive_contract_save_error, message = ' . $e->getMessage());
        }

        if ($code != ErrCode::$SUCCESS && isset($db)) {
            $db->rollback();
        }

        return [
            'code' => $code,
            'message' => $message,
        ];
    }

    /**
     * 归档合同编辑日志(New)
     *
     * @param object $model
     * @param array $old_data
     * @param array $new_data
     * @param array $user
     * @return mixed
     * @throws BusinessException
     */
    public function createAchiveContractEditLog(object $model, array $old_data, array $new_data, array $user)
    {
        // 提取可编辑字段的新旧数据
        $before_data = [];
        $after_data = [];

        $rent_archive_contract_edit_fields = ContractEnums::$rent_archive_contract_edit_fields;
        foreach ($rent_archive_contract_edit_fields as $data_index => $field_list) {
            if ($data_index == 'bank_collection') {
                continue;
            }

            if ($data_index == 'base_info') {
                foreach ($field_list as $field) {
                    $before_data[$data_index][$field] = $old_data[$data_index][$field];
                    $after_data[$data_index][$field] = $new_data[$data_index][$field];
                }

                continue;
            }

            foreach ($field_list as $field) {
                foreach ($old_data[$data_index] as $old_index => $old_item) {
                    $before_data[$data_index][$old_index][$field] = $old_item[$field];
                }

                foreach ($new_data[$data_index] as $new_index => $new_item) {
                    $after_data[$data_index][$new_index][$field] = $new_item[$field];
                }
            }
        }

        $achive_edit_log = [
            'before_data' => json_encode($before_data, JSON_UNESCAPED_UNICODE),
            'after_data' => json_encode($after_data, JSON_UNESCAPED_UNICODE),
            'contract_no' => $model->contract_id,
            'created_id' =>  $user['id'],
            'created_name' =>  $user['name'],
            'created_at' =>  date('Y-m-d H:i:s'),
        ];

        $this->logger->info(['contract_archive_edit_log_new' => $achive_edit_log]);

        $archive_edit_log_model = new ContractArchiveEditLogNewModel();
        if ($archive_edit_log_model->i_create($achive_edit_log) === false) {
            throw new BusinessException('归档合同编辑保存日志创建失败, 原因可能是 '. get_data_object_error_msg($archive_edit_log_model), ErrCode::$BUSINESS_ERROR);
        }

        return true;
    }

    public function transferToLandTypeStr($land_types,$land_type_content = ''){
        $land_type_list = [];
        foreach ($land_types as $land_type) {
            if ($land_type == ContractEnums::LAND_TYPE_6 && !empty($land_type_content)) {
                $land_type_list[] = static::$t->_(ContractEnums::$land_type_item[$land_type]).'('. $land_type_content .')';
            } else {
                $land_type_list[] = static::$t->_(ContractEnums::$land_type_item[$land_type]);
            }
        }

        return !empty($land_type_list) ? implode(',',$land_type_list) : '';
    }

    public function transferToLeaserTypeStr($leaser_type,$leaser_type_content = ''){
        if ($leaser_type == ContractEnums::LEASER_TYPE_AGENT && !empty($leaser_type_content)) {
            $leaser_type_str = static::$t->_(ContractEnums::$leaser_type_item[$leaser_type]).'('. $leaser_type_content .')';
        } else {
            $leaser_type_str = static::$t->_(ContractEnums::$leaser_type_item[$leaser_type]);
        }

        return $leaser_type_str;
    }

    /**
     * 付款信息更新字符串转换
     */
    public function transferToPaymentInfoStr($bankInfoNew,$bankInfoOld) {
        if (empty($bankInfoNew) || empty($bankInfoOld)) {
            return '';
        }
        $bankInfoNew = json_decode($bankInfoNew,true);
        $bankInfoOld = json_decode($bankInfoOld,true);
        $result = [
            'before' => [],
            'after' => []
        ];
        $indexs = [
            'pay_type' => static::$t->_('payment_method'),
            'bank_name' => static::$t->_('payment_store_renting_bank_name'),
            'bank_account_name' => static::$t->_('csr_field_bank_account_name'),
            'bank_account' => static::$t->_('csr_field_bank_account'),
            'contact_mobile' => static::$t->_('csr_field_contact_mobile'),
            'contact_emial' => static::$t->_('csr_field_contact_email'),
            'contact_type' => static::$t->_('csr_field_contact_type'),
            'contact_code' => static::$t->_('csr_field_contact_code'),
            'sap_supplier_no' => static::$t->_('csr_field_sap_supplier_no'),
        ];
        $emptyStr = static::$t->_('pay_where.0');
        foreach ($bankInfoNew as $k => $info) {
            $beforeResult = $afterResult = [];
            foreach ($info as $index => $field) {
                if (!isset($indexs[$index])) {
                    continue;
                }
                $index_name = $indexs[$index];
                if ('contact_type' == $index) {
                    $bankInfoOld[$k][$index] = static::$t->_(Enums::$contactTypeEmunsTxt[$bankInfoOld[$k][$index]]);

                    $field = static::$t->_(Enums::$contactTypeEmunsTxt[$field]);
                }
                if ('pay_type' == $index) {
                    $bankInfoOld[$k][$index] = static::$t->_(Enums::$contract_pay_type_map[$bankInfoOld[$k][$index]]);

                    $field = static::$t->_(Enums::$contract_pay_type_map[$field]);
                }
                $field = !empty($field) ? $field : $emptyStr;
                if (!isset($bankInfoOld[$k][$index])) {
                    $beforeResult[] = "{$index_name}:{$emptyStr}";
                    $afterResult[] = "{$index_name}:".$field;
                } else {
                    $bankInfoOld[$k][$index] = !empty($bankInfoOld[$k][$index]) ? $bankInfoOld[$k][$index] : $emptyStr;
                    $beforeResult[] = "{$index_name}:".$bankInfoOld[$k][$index];
                    $afterResult[] = "{$index_name}:".$field;
                }
            }

            if (!empty($beforeResult)) {
                $result['before'][] = ($k+1)."、".implode(',',$beforeResult);
                $result['after'][] = ($k+1)."、".implode(',',$afterResult);
            }
        }

        $bankInfoOldCount = count($bankInfoOld);
        $bankInfoNewCount = count($bankInfoNew);
        $beforeResult = $afterResult = [];
        if ($bankInfoOldCount > $bankInfoNewCount) {
            for ($i= $bankInfoNewCount; $i < $bankInfoOldCount; $i++) {
                foreach ($bankInfoOld[$i] as $index => $field) {
                    if (!isset($indexs[$index])) {
                        continue;
                    }
                    $index_name = $indexs[$index];
                    if ('contact_type' == $index) {
                        $field = static::$t->_(Enums::$contactTypeEmunsTxt[$field]);
                    }
                    if ('pay_type' == $index) {
                        $field = static::$t->_(Enums::$contract_pay_type_map[$field]);
                    }
                    $beforeResult[] = "{$index_name}:".$field;
                    $afterResult[] = "{$index_name}:''";
                }
                $result['before'][] = ($i+1)."、".implode(',',$beforeResult);
                $result['after'][] = ($i+1)."、{$emptyStr}";
            }
        }

        return $result;
    }

    /**
     * 比较付款信息字段
     */
    public function compare_bank_collection($bank_collection_old, $bank_collection){
        // 初始化空值的数据类型
        $bank_collection_old = !empty($bank_collection_old) ? $bank_collection_old : [];
        $bank_collection = !empty($bank_collection) ? $bank_collection : [];

        if (is_string($bank_collection_old)) {
            $bank_collection_old = json_decode($bank_collection_old,true);
        }

        if (is_string($bank_collection)) {
            $bank_collection = json_decode($bank_collection,true);
        }

        if (count($bank_collection_old) != count($bank_collection)) {
            return false;
        }

        foreach ($bank_collection_old as $i => $bankinfo) {
            foreach ($bankinfo as $key => $item) {
                if (!isset($bank_collection[$i][$key]) || $item != $bank_collection[$i][$key]) {
                    return false;
                }
            }
        }
        // 兼容历史不存在字段区别
        foreach ($bank_collection as $i => $bankinfo) {
            foreach ($bankinfo as $key => $item) {
                if (!isset($bank_collection_old[$i][$key])) {
                    return false;
                }
            }
        }

        return true;
    }

    /**
     * 获取待审核/已通过租房付款单
     * @param array $contractNo
     * @return array
     */
    public function getUsingPaymentOrder($contractNo = [])
    {
        $resultPaymentOrder = [];
        $limit = 10;
        $page = 1;

        // 每页$limit个单
        $chunkContractNo = array_chunk(array_unique($contractNo), $limit);

        foreach ($chunkContractNo as $cno) {
            $builder = $this->modelsManager->createBuilder();

            $column_str = 'pd.contract_no';
            $builder->columns($column_str);
            $builder->from(['pd' => PaymentStoreRentingDetail::class]);
            $builder->leftjoin(PaymentStoreRenting::class, 'pd.store_renting_id=p.id', 'p');
            $builder->where('p.approval_status in(1,3) and p.pay_status in(1,2) and pd.contract_no in("' . implode('","', $cno) . '")');

            $offset = $limit * ($page - 1);
            $builder->limit($limit, $offset);
            $tmpResult = $builder->getQuery()->execute()->toArray();
            if (!empty($tmpResult)) {
                $resultPaymentOrder = array_merge($resultPaymentOrder, array_column($tmpResult, 'contract_no'));
            }
            $page++;
        }

        return array_unique($resultPaymentOrder);
    }

    public function trimBankCollection($bankCollections = ''){
        $bankCollections = json_decode($bankCollections,true);
        if (empty($bankCollections)) {
            return '';
        }
        foreach ($bankCollections as $i => $bankInfo) {
            foreach ($bankInfo as $k => $field) {
                if (false !== strpos($k,'_1')) {
                    $bankCollections[$i][substr($k,0,-2)] = $field;
                    unset($bankCollections[$i][$k]);
                }
            }
        }
        return json_encode($bankCollections, JSON_UNESCAPED_UNICODE);
    }

    /**
     * 枚举
     *
     * @Date: 2021-11-09 19:49
     * @return array[] *
     **@author: peak pan
     */
    public function getLangIsMain()
    {
        return [
            ['id' => Enums::CONTRACT_IS_MASTER_YES, 'name' => self::$t->_(Enums::$contract_is_master[Enums::CONTRACT_IS_MASTER_YES])],
            ['id' => Enums::CONTRACT_IS_MASTER_NO, 'name' => self::$t->_(Enums::$contract_is_master[Enums::CONTRACT_IS_MASTER_NO])],
            ['id' => Enums::CONTRACT_IS_LOI_YES, 'name' => self::$t->_(Enums::$contract_is_master[Enums::CONTRACT_IS_LOI_YES])]
        ];
    }


    /**
     * 枚举
     * @
     *
     * @Date: 2021-11-09 19:49
     * @return array
     **@author: peak pan
     */
    public function getLangIsMainName()
    {
        return [
            Enums::CONTRACT_IS_MASTER_YES=> self::$t->_(Enums::$contract_is_master[Enums::CONTRACT_IS_MASTER_YES]),
            Enums::CONTRACT_IS_MASTER_NO => self::$t->_(Enums::$contract_is_master[Enums::CONTRACT_IS_MASTER_NO]),
            Enums::CONTRACT_IS_LOI_YES => self::$t->_(Enums::$contract_is_master[Enums::CONTRACT_IS_LOI_YES])
        ];
    }


    /**
     * 获取loi list数据
     *
     * @Date: 2021-11-09 20:33
     * @param $params
     * @param $store_cate_list
     * @return array
     * @author: peak pan
     */
    public function relateMainList($params, $store_cate_list)
    {
        $store_cate = array_column($store_cate_list, 'id');
        if (empty($store_cate)) {
            return [];
        }

        //父目录  不为空
        if (!empty($params['contract_id'])) {
            $con['conditions'] = "is_main=:is_main: and  contract_status=:contract_status: and contract_id like :contract_id: and store_cate in ({store_cate:array}) ";
            $con['bind']['is_main'] = Enums::CONTRACT_IS_LOI_YES;
            $con['bind']['contract_status'] =  Enums::WF_STATE_APPROVED;
            $con['bind']['store_cate'] =  $store_cate;
            $con['bind']['contract_id'] = "%{$params['contract_id']}%";
        } else {
            $con['conditions'] = "is_main=:is_main: and  contract_status=:contract_status: and store_cate in ({store_cate:array})";
            $con['bind']['is_main'] = Enums::CONTRACT_IS_LOI_YES;
            $con['bind']['contract_status'] =  Enums::WF_STATE_APPROVED;
            $con['bind']['store_cate'] =  $store_cate;
        }
        $con['columns'] = 'contract_id, id';

        $main_items = ContractStoreRentingModel::find($con)->toArray();
        if (!empty($main_items)) {
            $main_items = array_column($main_items, null, 'contract_id');
            $main_list = array_values(array_keys($main_items));
        } else {
            return [];
        }

        $con_t = [];
        //父目录  不为空
        $con_t['conditions'] = " is_main=:is_main: and  contract_status not in ({contract_status:array})  and  relate_contract_id>0 ";
        $con_t['bind']['is_main'] = Enums::CONTRACT_IS_MASTER_YES;
        $con_t['bind']['contract_status'] = [Enums::WF_STATE_REJECTED, Enums::WF_STATE_CANCEL];
        //$con_t['bind']['store_cate'] = $store_cate;
        $con_t['columns'] = 'id, relate_contract_id';
        $loi_main_items = ContractStoreRentingModel::find($con_t)->toArray();

         $loi_main_list = [];
        if (!empty($loi_main_items)) {
            $loi_main_items = array_column($loi_main_items, null, 'relate_contract_id');
            $loi_main_list = array_values(array_keys($loi_main_items));
        }

        $diff_list = array_values(array_diff($main_list, $loi_main_list));
        $diff_list = !empty($diff_list) ? $diff_list : [];

        // 合同id信息
        $list_ids = [];
        foreach ($diff_list as $id) {
            if (in_array($id, $main_list)) {
                $list_ids[$id] = [
                    'id' => $main_items[$id]['id']
                ];
            }
            if (in_array($id, $loi_main_list)) {
                $list_ids[$id] = [
                    'id' => $loi_main_items[$id]['id']
                ];
            }
        }

        return ['list' => $diff_list, 'list_info' => $list_ids];
    }


    /**
     * 根据loi合同编号查出合同的具体支付信息
     *
     * @Date: 2021-11-16 20:40
     * @param $params
     * @return array
     * @throws BusinessException
     * @author: peak pan
     */
    public function rentingBankCollection($params)
    {
        $model = ContractStoreRentingModel::findFirst([
            'columns' => '*',
            'conditions' => 'contract_id = :contract_id:',
            'bind'       => [
                //'is_main' => Enums::CONTRACT_IS_LOI_YES,
                'contract_id' => trim($params['contract_id']),
            ],
        ]);
        if (!empty($model)) {
            $main_list = array_map('trim', $model->toArray());
            $main_list['contract_effect_date'] = trim($main_list['contract_effect_date']) == '0000-00-00' ? '' : $main_list['contract_effect_date'];
            $main_list['store_list'] = $this->getStoreList($main_list['store_cate']);

            $bank_collection = json_decode($main_list['bank_collection'], true) ?? '';
            if ($bank_collection) {
                $contactType = ContractStoreRentingService::getInstance()->getContactType();
                foreach ($bank_collection as &$bank_list){
                    $bank_list['contact_type'] = $contactType[$bank_list['contact_type']];
                }
            }
            $main_list['bank_collection'] = $bank_collection;

            // 获取合同自身附件列表 v18530 正文附件 和 其他附件已迁移到 系统附件表
            $main_list['attachment_list'] = $this->getContractSelfAttachmentList($model);
            unset($main_list['pdf_required_name'], $main_list['pdf_noneed_name']);

            $renting_area = $model->getRentingArea()->toArray();
            foreach ($renting_area as &$r_area) {
                $r_area['area_start_time'] = date('Y-m-d', strtotime($r_area['start_time']));
                $r_area['area_end_time'] = date('Y-m-d', strtotime($r_area['end_time']));
            }
            $main_list['renting_area'] = $renting_area;
            $main_list['renting_detail'] = $model->getRentingDetail();
            $main_list['renting_money'] = $model->getRentingMoney();

            // 地契类型数组转换
            $main_list['land_type'] = !empty($main_list['land_type']) ? explode(',', $main_list['land_type']) : [];
            unset($main_list['id']);
        } else {
            $main_list = [];
        }

        return $main_list;
    }


    /**
     * 发送邮件给发起者
     * @Date: 2021-11-17 22:05
     * @author: peak pan
     * @return:
     **/
    public function sendEmail($request, $nodeAuditors, $flag = 0)
    {
        try {
            if (empty($request)) {
                throw new ValidationException("no biz main data");
            }

            // 只在测试环境，给测试人员发邮件。
            $test_flag = in_array(env('runtime'), ['dev','test','tra', 'training']);

            // 开发/测试/tra环境收件人取指定配置的
            if ($test_flag) {
                $email_addressee_code = $flag ? GlobalEnums::EMAIL_NOTICE_WAITING_PAY_CODE : GlobalEnums::EMAIL_NOTICE_WAITING_AUDIT_CODE;
                $email_addressee_val = EnvModel::getEnvByCode($email_addressee_code);
                $emails = !empty($email_addressee_val) ? explode(',', $email_addressee_val) : [];
            } else {
                // 生产取工号对应的
                $emails = (new StaffInfoModel())->getEmails($nodeAuditors);
            }

            // 如果收件人邮箱为空，则不发
            if (empty($emails)) {
                throw new ValidationException("no emails addressee, stop send. [channel=audit_completion, flag=$flag, biz_id/no.={$request->contract_id}], recipient_ids=".json_encode($nodeAuditors, JSON_UNESCAPED_UNICODE).']');
            }

            // 语言初始化
            $orgLang = self::$language;

            // 邮件模板语言定义
            $language_setting = $this->getNoticeEmailTemplateLang();
            $first_lang = $language_setting['first_lang'];
            $second_lang = $language_setting['second_lang'];

            // 切为系统语言
            self::setLanguage($orgLang);

            $title = $first_lang->_("msg_email_title_renting");
            $contentKeyEn ="msg_email_title_renting_content_en";
            $contentKeyCn ="msg_email_title_renting_content_cn";
            $first_where = $first_lang->_($contentKeyEn, ["code" => $request->contract_id ?? '']);
            $second_where = $second_lang->_($contentKeyCn, ["code" => $request->contract_id ?? '']);

            $html = <<<EOF
    <span style="margin-left: 16px"></span>{$first_where}<br/>
    <span style="margin-left: 16px"></span>{$second_where}

EOF;

            // 邮件发送
            $log = ["emails" => $emails, "title" => $title, "html" => $html];
            $send_res = $this->mailer->sendAsync($emails, $title, $html);
            if ($send_res) {
                $this->logger->info('sendEmail 发送成功！[channel=audit_completion] ' . json_encode($log, JSON_UNESCAPED_UNICODE));
            } else {
                $this->logger->warning('sendEmail 发送失败！[channel=audit_completion] ' . json_encode($log, JSON_UNESCAPED_UNICODE));
            }

        } catch (ValidationException $e) {
            $this->logger->info("sendEmail 校验异常！[channel=audit_completion] 原因可能是：" . $e->getMessage());

        } catch (BusinessException $e) {
            $this->logger->notice("sendEmail 业务异常！[channel=audit_completion] 原因可能是：" . $e->getMessage());

        } catch (Exception $e) {
            $this->logger->warning('sendEmail 发送失败！[channel=audit_completion] 原因可能是：' . $e->getMessage());
        }
    }


    /**
     * 根据网点编号查出关联的网点合同
     * @Date: 2021-12-06 20:40
     * @author: wangqi
     * @param array $params
     * @return array
     */
    public function getRelationStoreList($params = [])
    {
        $store_id = trim($params['store_id']);
        $current_start_date = $params['start_date'] ?? '';
        $current_end_date = $params['end_date'] ?? '';

        // 取出网点下 或 仓库下的合同列表 [网点类型是总部的 以及 归档状态是 待作废/已作废的 不参与]
        $columns = [
            'main.id',
            'main.contract_id',
            'main.store_cate',
            'main.is_main',
            'main.contract_status',
            'main.contract_begin',
            'main.contract_end',
            'archive.status AS archive_status',
            'archive.terminal_at',
        ];

        $builder = $this->modelsManager->createBuilder();
        $builder->columns($columns);
        $builder->from(['main' => ContractStoreRentingModel::class]);
        $builder->leftjoin(ContractArchive::class, 'main.contract_id = archive.cno', 'archive');
        $builder->where('main.store_cate != :store_cate:', ['store_cate' => Enums::HEAD_OFFICE_STORE_FLAG]);
        $builder->inWhere('main.is_main', [Enums::CONTRACT_IS_MASTER_YES, Enums::CONTRACT_IS_MASTER_NO]);
        $builder->andWhere('archive.id IS NULL OR archive.status NOT IN ({status_item:array})', [
            'status_item' => [ContractEnums::CONTRACT_ARCHIVE_STATUS_INVALID_ING, ContractEnums::CONTRACT_ARCHIVE_STATUS_INVALID]
        ]);

        if (!empty($params['warehouse_id'])) {
            $builder->andWhere('main.warehouse_id = :warehouse_id:', ['warehouse_id' => $params['warehouse_id']]);
        } else {
            $builder->andWhere('main.store_id = :store_id:', ['store_id' => $store_id]);
        }
        $item = $builder->getQuery()->execute()->toArray();

        // 取出合同期间与当前合同期间有交集的
        $result = [];
        foreach ($item as $k => $val) {
            // 去除本合同
            if (!empty($params['id']) && $params['id'] == $val['id']) {
                continue;
            }

            $_start_date = $val['contract_begin'];
            $_end_date = $val['contract_end'];

            // 待终止/已终止的合同结束日期 取终止日期
            if (in_array($val['archive_status'], [ContractEnums::CONTRACT_ARCHIVE_STATUS_TERMINAL_ING, ContractEnums::CONTRACT_ARCHIVE_STATUS_TERMINAL]) && !empty($val['terminal_at'])) {
                $_end_date = $val['terminal_at'];
            }

            // 对比合同的起止期间 是否 有重复
            if ($current_start_date <= $_end_date && $current_end_date >= $_start_date) {
                $result[] = [
                    'id' => $val['id'],
                    'contract_id' => $val['contract_id'],
                    'store_cate' => $val['store_cate'],
                    'is_main_text' => static::$t->_(Enums::$contract_is_master[$val['is_main']]),// 合同类型
                ];
            }
        }


        return $result;
    }

    /**
     * 根据房东类型获取wht列
     * @Date: 2021-12-06 20:40
     * @author: wangqi
     * @param array $params
     * @return array
     **/
    public function getWhtList($params = []){
        $item = EnumsService::getInstance()->getWhtRateCategoryMap();
        $wht_tax_config = EnumsService::getInstance()->getWhtRateMap();

        $houseOwnerType = $params['house_owner_type'] ?? ContractEnums::HOUSE_OWNER_TYPE_COMPANY; // 默认企业

        $tmp_item = [];
        foreach ($item as $index => $t_key) {
            // 企业没有pnd3选项/个人没有pnd53选项
            if ((ContractEnums::HOUSE_OWNER_TYPE_COMPANY == $houseOwnerType && ContractEnums::WHT_CATEGORY_PND3 == $t_key)
                ||
                (ContractEnums::HOUSE_OWNER_TYPE_PERSON == $houseOwnerType && ContractEnums::WHT_CATEGORY_PND53 == $t_key)
            ) {
                continue;
            }

            // tax
            $tax_item = $wht_tax_config[$index]['rate_list'];
            foreach ($tax_item as $tax_index => $tax_value) {
                $tax_item[$tax_index] = [
                    'id' => $tax_value['value'],
                    'label' => $tax_value['label']
                ];
            }

            $tmp_item[] = [
                'id' => $index,
                'label' => $t_key,
                'tax_item' => $tax_item
            ];
        }

        return $tmp_item;
    }

    /**
     * 任务：导出自定义条件的合同
     * 说明：含附件，含审批日志
     *
     * @param $params
     * @param array $loginUser
     * @param int $type
     * @return array
     */
    public function getAllListByTask($params, $loginUser = [], $type = 0)
    {
        parent::setLanguage($params['lang'] ?? 'zh-CN');

        // 找指定部门下的员工
        $create_ids = $staff_item = [];
        if (!empty($params['ancestry'])) {
            $builder = $this->modelsManager->createBuilder();
            $builder->columns([
                's.staff_info_id',
                's.name AS staff_name',
                'd.id AS department_id',
                'd.name AS department_name',
            ]);
            $builder->from(['s' => HrStaffInfoModel::class]);
            $builder->leftJoin(SysDepartmentModel::class, 's.node_department_id = d.id', 'd');
            $builder->where('d.ancestry_v3 LIKE :ancestry: ', ['ancestry' => "{$params['ancestry']}%"]);
            $staff_item = $builder->getQuery()->execute()->toArray();
            $staff_item = array_column($staff_item, null, 'staff_info_id');
            $create_ids = array_keys($staff_item);
        }

        try {
            $builder    = $this->modelsManager->createBuilder();

            $column_str = [
                'distinct c.id',
                'c.manage_id',
                'c.contract_status',
                'date(c.apply_at) as apply_at',
                'c_a.id AS archive_id',// 取签字合同附件用
                'c_a.contract_file',// 盖章合同
                'c_a.cno',
                'c_a.status',
                'c_a.filing_at',
                'c_a.filing_name',
                'c_a.holder_name',
                'c_a.invalid_at',
                'c.store_id',
                'c.store_cate',
                'c.provinces',
                'c.lon_lat',
                'c.store_addr',
                'c.house_owner_name',
                'c.house_contract_area',
                'c.house_actual_area',
                'c.house_owner_type',
                'c.contract_name',
                'c.contract_id',
                'c.contract_deadline',
                'c.contract_begin',
                'c.contract_end',
                'c.rent_free_time',
                'c.contract_effect_date',
                'c.contract_lease_type',
                'c.money_symbol',
                'c.contract_benefits',
                'c.deposit_amount',
                'c.ver',
                'c.monthly_payment_type',
                'c.contract_signer_name',
                'c.signer_phone',
                'c.contract_remarks',
                'c.bank_collection',
                'c.hourse_owner_addr',
                'c.monthly_area_service',
                'c.vat',
                'c.property_tax',
                'c.total_amount_monthly',
                'c.duty_stamp',
                'c.total_amount',
                'c.signboard_tax',
                'c.state',
                'c.created_at',
                'c.withholding_tax',
                'c.wt_area_service',
                'c.contract_total_amount',
                'c.billboard_tax_payer',
                'c.contract_deposit',
                'c.land_tax_payer',
                'c.fire_insurance_payer',
                'c.antimoth_payer',
                'c.land_tax_amount',
                'c.fire_insurance_amount',
                'c.antimoth_amount',
                'c.is_main',
                'c.contract_lang',
                'c.house_equip_list',
                'c.contract_tax_no',
                'c.amount_paid',
                'c.full_refund_conditions',
                'c_a.terminal_at',
                'c.land_type',
                'c.leaser_type',
                'c.cost_company_name',
                'c.main_contract_id',
                'c.relate_contract_id',
                'c_a.pdf_noneed_file', // 作废附件
                'w.id AS wid',
                'c.contract_leader_id',
                'c.leader_node_department_name',
            ];

            $builder->columns($column_str);
            $builder->from(['c' => ContractStoreRentingModel::class]);
            $builder->leftJoin(WorkflowRequestModel::class, 'w.biz_type = ' . Enums::WF_STORE_RENTING_CONTRACT_TYPE . ' AND w.biz_value=c.id', 'w');
            $builder->leftjoin(ContractArchive::class, 'c_a.cno=c.contract_id', 'c_a');
            if (!empty($create_ids)) {
                $builder->inWhere('c.manage_id', array_values($create_ids));
            }

            $builder = $this->_getAllCondition($builder, $params, $loginUser, $type);
            $builder->orderBy('c.id desc');
            $items = $builder->getQuery()->execute()->toArray();

            if (empty($items)) {
                return [
                    'code' => 0,
                    'message' => '无符合条件的数据'
                ];
            }

            $tArr = $this->_handleArchiveItemsForExport($items);

            $archive_ids = array_values(array_unique(array_filter(array_column($items, 'archive_id'))));
            $sign_attachment = [];
            $termination_attachment = [];
            if (!empty($archive_ids)) {
                // 签字合同
                $sign_attachment = AttachModel::find([
                    'conditions' => 'oss_bucket_type = :oss_bucket_type: AND oss_bucket_key IN ({keys:array}) AND deleted=0',
                    'bind' => [
                        'oss_bucket_type' => ContractEnums::OSS_BUCKET_TYPE_CONTRACT_SIGNATURE_FILE,
                        'keys' => $archive_ids
                    ]
                ])->toArray();
                $sign_attachment = merge_attachments($sign_attachment);

                // 合同终止附件
                $termination_attachment = AttachModel::find([
                    'conditions' => 'oss_bucket_type = :oss_bucket_type: AND oss_bucket_key IN ({keys:array}) AND deleted=0',
                    'bind' => [
                        'oss_bucket_type' => ContractEnums::OSS_BUCKET_TYPE_TERMINAL_FILE,
                        'keys' => $archive_ids
                    ]
                ])->toArray();
                $termination_attachment = merge_attachments($termination_attachment);
            }

            // 意见征询附件
            // 意见征询/回复附件
            $request_ids = array_values(array_filter(array_unique(array_column($items, 'wid'))));
            $ask_reply_attachment = [];
            if (!empty($request_ids)) {
                $fyr_ids = WorkflowRequestNodeFYR::find([
                    'conditions' => 'request_id IN ({request_ids:array})',
                    'bind' => ['request_ids' => $request_ids],
                    'columns' => ['id', 'request_id', 'action_type'],
                    'order' => 'id ASC'
                ])->toArray();
                $fyr_ids = array_column($fyr_ids, 'request_id', 'id');

                if (!empty($fyr_ids)) {
                    $consult_attachment = AttachModel::find([
                        'conditions' => 'oss_bucket_type = :oss_bucket_type: AND oss_bucket_key IN ({keys:array}) AND deleted=0',
                        'bind' => [
                            'oss_bucket_type' => Enums::OSS_BUCKET_TYPE_FRY_ATTACH,
                            'keys' => array_keys($fyr_ids)
                        ]
                    ])->toArray();
                    $consult_attachment = merge_attachments($consult_attachment);
                    // 归并同一个工单的意见征询/回复附件
                    foreach ($fyr_ids as $fyr_id => $request_id) {
                        if (empty($consult_attachment[$fyr_id])) {
                            continue;
                        }

                        if (isset($ask_reply_attachment[$request_id])) {
                            $ask_reply_attachment[$request_id] .= " ;\r\n" . $consult_attachment[$fyr_id];
                        } else {
                            $ask_reply_attachment[$request_id] = $consult_attachment[$fyr_id];
                        }
                    }
                    $consult_attachment = null;
                }
            }

            $items = null;

            // 明细字段数动态计算
            $maxRentBeforeLen = $tArr['maxLen'];
            $maxBankLen = $tArr['maxBankLen'];
            $maxAreaLen = $tArr['maxAreaLen'];

            // 组装Excel数据
            $new_data = [];
            $payer_res = $this->getPayer();

            $workflow_service_v2_entity = new WorkflowServiceV2();

            $wht_cat_map = EnumsService::getInstance()->getWhtRateCategoryMap(0);
            foreach ($tArr['items'] as $key => $val) {
                $tmp = [
                    static::$t->_(Enums::$contract_is_master[$val['is_main']]),    //合同类型
                    !empty($val['contract_lang']) ? static::$t->_(Enums::$store_contract_lang[$val['contract_lang']]) : '',  //合同语言
                    empty($val['main_contract_id']) ? $val['relate_contract_id'] : $val['main_contract_id'],//主合同/loi 编号
                    $val['dept_name'],  //申请人部门
                    $val['staff_name'], //申请人姓名
                    $val['manage_id'],  //申请人工号
                    $val['contract_leader_id'],  // 负责人工号
                    $val['leader_node_department_name'],  // 负责部门
                    $val['contract_status_txt'],//申请状态

                    $val['apply_at'],   //合同申请日期
                    $val['archive_status_txt'],//合同归档状态
                    $val['invalid_at'],     //合同作废日期
                    $val['filing_at'] ?? '',  //合同归档日期
                    $val['terminal_at'] ?? '',  // 合同终止日期
                    $val['filing_name'] ?? '',//合同归档人
                    $val['holder_name'] ?? '',//合同原件保管人

                    $val['store_category_txt'],//网点类型
                    $val['store_name'],//网点名称
                    $val['store_id'],//网点编号
                    $val['provinces'],//省份/大区
                    $val['lon_lat'],//网点经纬度,
                    $val['store_addr'],//网点地址

                    $val['house_owner_name'],//房东姓名
                    $val['house_contract_area'],//房屋合约面积
                    $val['house_actual_area'],//房屋实际面积
                    $val['house_owner_type_txt'],//房东类型
                    $val['house_equip_list'],//设备清单
                    $val['land_type'],//地契类型
                    $val['leaser_type'],//出租人
                    $val['contract_tax_no'],//房东税号

                    $val['contract_name'],  //合同名称
                    $val['contract_id'],    //合同编号
                    $val['contract_deadline'],//合同期限

                    $val['contract_begin'],//合同开始日期
                    $val['contract_end'],//合同结束日期

                    $val['rent_free_time'],//免租期
                    $val['contract_effect_date'],//合同生效日期
                    $val['lease_type_txt'], //合同租期->付款方式
                    $val['amount_paid'], //已付金额
                ];

                for ($i = 0; $i < $maxRentBeforeLen; $i++) {
                    // 需判断无值的情况
                    $_tmp_amount_detail = $val['amount_detail'][$i] ?? [];
                    if (!empty($_tmp_amount_detail)) {
                        $tmp[] = $_tmp_amount_detail['rent_time'];
                        $tmp[] = $_tmp_amount_detail['amount_no_tax'];
                        $tmp[] = $_tmp_amount_detail['vat_rate'] . '%';
                        $tmp[] = $_tmp_amount_detail['amount_vat'];
                        $tmp[] = $_tmp_amount_detail['amount_has_tax'];
                        $tmp[] = $wht_cat_map[$_tmp_amount_detail['wht_category']] ?? '';
                        $tmp[] = $_tmp_amount_detail['wht_rate'] . '%';
                        $tmp[] = $_tmp_amount_detail["amount_wht"];
                    } else {
                        $tmp[] = '';
                        $tmp[] = '';
                        $tmp[] = '';
                        $tmp[] = '';
                        $tmp[] = '';
                        $tmp[] = '';
                        $tmp[] = '';
                        $tmp[] = '';
                    }
                }

                $tmp[] = $val['contract_total_amount']; //合同总金额

                $tmp[] = $val['deposit_amount'];    //合同押金
                $tmp[] = $val['contract_deposit'] ?? '';  //合同定金
                $tmp[] = $val['cost_company_name'] ?? '';  //合同所属公司
                $tmp[] = $val['money_symbol_txt'] ?? '';  //币种

                $tmp[] = $val['contract_signer_name'] ?? '';  //合同签署人姓名
                $tmp[] = $val['signer_phone'] ?? '';//合同签署人联系电话
                $tmp[] = $val['full_refund_conditions'] ?? '';//押金劝退条件
                $tmp[] = $val['contract_remarks'] ?? '';//合同备注

                for ($i = 0; $i < $maxBankLen; $i++) {
                    if (isset($val['bank_collection'][$i])) {
                        $tBank = $val['bank_collection'][$i];

                        //老版数据格式
                        if (isset($tBank['bank_name_1'])) {
                            $tmp[] = static::$t->_(Enums::$contract_pay_type_map[$tBank['pay_type_1'] ?? ''] ?? '') ?? '';  //付款方式
                            $tmp[] = $tBank['bank_name_1'] ?? '';
                            $tmp[] = $tBank['bank_account_title_1'] ?? '';
                            $tmp[] = $tBank['bank_book_no_1'] ?? '';
                            $tmp[] = $tBank['contact_mobile_1'] ?? '';
                            $tmp[] = '';
                            // 老数据结构里key, 经排查用不到, 留着会导致Excel数据列与标题列不匹配
//                            $tmp[]        = $tBank['remit_amout'] ?? '';
                            $tmp[] = '';
                            $tmp[] = '';
                            $tmp[] = '';

                        } else {
                            $tmp[] = static::$t->_(Enums::$contract_pay_type_map[$tBank['pay_type'] ?? ''] ?? '') ?? '';  //付款方式
                            $tmp[] = $tBank['bank_name'] ?? '';  //银行名称
                            $tmp[] = $tBank['bank_account_name'] ?? '';  //银行账户名称
                            $tmp[] = $tBank['bank_account'] ?? '';   //银行账户号
                            $tmp[] = $tBank['contact_mobile'] ?? ''; //联系人电话号码
                            $tmp[] = $tBank['contact_emial'] ?? ''; //联系人邮箱
                            //  $tmp[] = $tBank['pay_amount'];     //汇款金额
                            $tmp[] = $this->getHouseOwnerTypeTxt($tBank['contact_type'] ?? '') ?? '';   //联系人类型 ,1=公司，2=个人，3=中介
                            $tmp[] = $tBank['contact_code'] ?? '';   //联系人身份证号/税号
                            $tmp[] = $tBank['sap_supplier_no'] ?? ''; //sap供应商号
                        }

                    } else {
                        $tmp[] = '';
                        $tmp[] = '';
                        $tmp[] = '';
                        $tmp[] = '';
                        $tmp[] = '';
                        $tmp[] = '';
                        $tmp[] = '';
                        $tmp[] = '';
                        $tmp[] = '';
                    }
                }

                $tmp[] = $val['hourse_owner_addr'] ?? '';//房东收件地址

                for ($i = 0; $i < $maxAreaLen; $i++) {
                    $_tmp_area_info = $val['areaInfo'][$i] ?? [];
                    if (!empty($_tmp_area_info)) {
                        $tmp[] = $_tmp_area_info['rent_time'];
                        $tmp[] = $_tmp_area_info['area_service_amount_no_tax'];
                        $tmp[] = $_tmp_area_info['area_vat_rate'] . '%';
                        $tmp[] = $_tmp_area_info['area_service_amount_vat'];
                        $tmp[] = $_tmp_area_info['area_amount_wht'];
                    } else {
                        $tmp[] = '';
                        $tmp[] = '';
                        $tmp[] = '';
                        $tmp[] = '';
                        $tmp[] = '';
                    }
                }

                $tmp[] = $payer_res[$val['property_tax']] ?? '';      //房产税付款人
                $tmp[] = $val['total_amount_monthly'] ?? '';  //房产税金额
                $tmp[] = $payer_res[$val['duty_stamp']] ?? '';    //印花税付款人
                $tmp[] = $val['total_amount'] ?? '';  //印花税金额
                $tmp[] = $payer_res[$val['billboard_tax_payer']] ?? '';//广告牌税付款人
                $tmp[] = $val['signboard_tax'] ?? '';//广告牌税金额
                $tmp[] = $payer_res[$val['land_tax_payer']] ?? '';//土地税付款人
                $tmp[] = $val['land_tax_amount'] ?? '';//土地税税
                $tmp[] = $payer_res[$val['fire_insurance_payer']] ?? '';//火灾保险费付款人
                $tmp[] = $val['fire_insurance_amount'] ?? '';//火灾保险费金税
                $tmp[] = $payer_res[$val['antimoth_payer']] ?? '';//房屋杀虫防蛀费付款人
                $tmp[] = $val['antimoth_amount'] ?? '';//房屋杀虫防蛀费金额


                // 补充字段
                // 合同正文
                $pdf_required_item = [];
                $text_contract = '';
                foreach ($pdf_required_item as $_v) {
                    if ($_v['object_url']) {
                        $text_contract .= $_v['file_name'] . ': ' . $_v['object_url'] . " ;\r\n";
                    }
                }

                // 合同附件
                $pdf_noneed_item = [];
                $contract_attachment = '';
                foreach ($pdf_noneed_item as $_v) {
                    if ($_v['object_url']) {
                        $contract_attachment .= $_v['file_name'] . ': ' . $_v['object_url'] . " ;\r\n";
                    }
                }

                // 盖章合同
                $stamp_attachment_item = $this->getRemotePath($val['contract_file'],AttachmentService::BUSINESS_TYPE_CONTRACT_ARCHIVE_CONTRACT_FILE);
                $stamp_attachment = '';
                if (!empty($val['contract_file'])) {
                    $stamp_attachment_item = merge_attachments($stamp_attachment_item);

                    foreach ($stamp_attachment_item as $_v) {
                        $stamp_attachment .= $_v . " ;\r\n";
                    }
                }

                // 合同作废附件
                $pdf_noneed_file_item = json_decode($val['pdf_noneed_file'], true) ?? [];
                $pdf_noneed_attachment = '';
                foreach ($pdf_noneed_file_item as $_v) {
                    $pdf_noneed_attachment .= $_v['file_name'] . ': ' . $_v['object_url'] . " ;\r\n";
                }

                // 合同审批日志
                $request_model = WorkflowRequestModel::findFirst($val['wid']);
                $flow_logs = $workflow_service_v2_entity->getAuditLogs($request_model);

                // 格式: 操作行为 - 操作人 - 姓名 - 操作时间(审批时间或等待时长待审批时有)
                // 暂不处理子节点会签审批 与 审批人会签审批的展示情况
                $_wk_log = '';
                foreach ($flow_logs as $log) {
                    $_staff_info = '';
                    $_curr_log = $log['action_name'] . ' - ' . 'AUDIT_STAFF_INFO' . ' - ' . $log['audit_at'];

                    // 待审批人为多个的情况
                    if (!empty($log['list'])) {
                        foreach ($log['list'] as $sub_log) {
                            $_staff_info .= $sub_log['staff_id'].'['.$sub_log['staff_name'].']/';
                        }

                        $_staff_info = trim($_staff_info, ' /');

                    } else {
                        // 审批人为一
                        $_staff_info = $log['staff_id'].'['.$log['staff_name'].']';

                    }

                    // 意见征询日志
                    if (!empty($log['fyr_list'])) {
//                    var_dump($log['fyr_list']);die;
                    }

                    $_wk_log .= str_replace('AUDIT_STAFF_INFO', $_staff_info, $_curr_log) . "\r\n";
                }

                $tmp[] = $text_contract; // 合同正文
                $tmp[] = $contract_attachment; // 合同附件
                $tmp[] = $sign_attachment[$val['archive_id'] ?? ''] ?? ''; // 签字合同
                $tmp[] = $stamp_attachment; // 盖章合同
                $tmp[] = $termination_attachment[$val['archive_id'] ?? ''] ?? ''; // 终止附件
                $tmp[] = $pdf_noneed_attachment; // 作废附件
                $tmp[] = $ask_reply_attachment[$val['wid']] ?? ''; // 意见征询/回复附件
                $tmp[] = $_wk_log; // 审批日志明细
                $new_data[] = $tmp;
            }

            $file_name = get_country_code() . '_rent_contract_' . date('YmdHis') . '.xlsx';
            $header    = $this->getAllExportHeaders($maxRentBeforeLen,$maxBankLen,$maxAreaLen);
            $append_header = [
                '合同正文',
                '合同附件',
                '签字合同',
                '盖章合同',
                '终止附件',
                '作废附件',
                '意见征询/回复附件',
                '审批日志明细',
            ];
            $header = array_merge($header, $append_header);
            $data = $this->exportExcel($header, $new_data, $file_name);

        } catch (Exception $e) {
            return [
                'error_msg' => $e->getMessage()
            ];
        }

        return $data;
    }
    /**
     * 处理更新数据
     * @param object $item 要更新可审批编辑数据对象信息
     * @param array $edit_field 在workflow_node表中can_edit_field 配置参数组
     * @param array $update_data 要在审批过程中要变更的数据
     * @param object $work_req 当前审批节点数据对象信息
     * @param array $user 当前登陆操作人信息
     */
    public function dealEditField($item, $edit_field, $update_data, $work_req, $user)
    {
        $mainData = [];
        $logData = [];
        $logData['meta_bank_collection'] = [];
        $logData['main'] = [];
        $update_flag = false;
        //如果修改付款信息中的值(bank_collection字段),需要特殊处理
        /**
         * 'bank_collection'=>[
                [
                    'index'=>'0',//行
                    'bank_name'=>'222'
                    'sap_supplier_no'=>'222';
                ],
                [
                    'index'=>'2',//行
                    'bank_name'=>'222'
                    'sap_supplier_no'=>'222';
                ]
            ]
         */
        if (isset($edit_field['meta_bank_collection']) && !empty($edit_field['meta_bank_collection'])) {
            //清洗数据,二维数组中只留允许修改的字段,index做clear_update_data的key
            $clear_update_data = [];
            foreach ($update_data['bank_collection'] as $bc_k=>$bc_v){
                foreach ($bc_v as $v_k=>$v_v){
                    if(in_array($v_k,$edit_field['meta_bank_collection'])){
                        $clear_update_data[$bc_v['index']][$v_k] = $v_v;
                    }
                }
            }
            //执行修改
            if(!empty($clear_update_data)){
                //表中的值(json存储的二维数组)
                $bank_collection = json_decode($item->bank_collection,true);
                $bank_collection = array_values($bank_collection);
                $need_update = 0;
                //修改可以修改的字段
                $new_bank_collection = $bank_collection;
                foreach ($bank_collection as $index=>$bank_v){
                    //改这一行的
                    if (isset($clear_update_data[$index])){
                        //本行修改目标数据 ['bank_name'=>222,'sap_supplier_no'=>'222']
                        $tmp_kv_value = $clear_update_data[$index];
                        //本行可以修改的字段值
                        foreach ($tmp_kv_value as $field=>$field_value){
                            if ($field_value != $bank_v[$field]){
                                $log = [];
                                $log['before'] = $bank_v[$field];
                                $log['after'] = $field_value;
                                $log['field_name'] = $field;
                                $logData['meta_bank_collection'][] = $log;
                                $need_update = 1;
                                $new_bank_collection[$index][$field] = $field_value;
                            }
                        }
                    }
                }
                //如果字段有修改,则执行修改
                if($need_update === 1){
                    $update_flag = true;
                    $new_bank_collection = array_values($new_bank_collection);
                    $mainData['bank_collection'] = json_encode($new_bank_collection);
                }
            }
        }
        if (!empty($edit_field['main'])) {
            foreach ($edit_field['main'] as $key) {
                //如果没有定义该参数，则不修改,isset针对如果像日期字段设置为null需要存储时，就会标记为true，导致日期重置不会被修改
                if (!isset($update_data[$key]) && $key != 'bank_flow_date') {
                    continue;
                }
                $tmp = $update_data[$key];
                //不相等，才记录
                if ($tmp != $item->$key) {
                    $log = [];
                    $log['before'] = $item->$key;
                    $log['after'] = $tmp;
                    $log['field_name'] = $key;
                    $logData['main'][] = $log;
                    $update_flag = true;
                    $mainData[$key] = $tmp;
                }
            }
        }
        //有变更数据则更新主表信息
        if (!empty($mainData)) {
            $item->update($mainData);
        }
        //记录审批操作过程中更新日志
        if ($update_flag) {
            $log = new WorkflowUpdateLogModel();
            $log->save(
                [
                    'request_id' => $work_req->id,
                    'flow_id' => $work_req->flow_id,
                    'flow_node_id' => $work_req->current_flow_node_id,
                    'staff_id' => $user['id'],
                    'staff_name' => $this->getNameAndNickName($user['name'], $user['nick_name']),
                    'staff_department' => $user['department'],
                    'staff_job_title' => $user['job_title'],
                    'content' => json_encode($logData, JSON_UNESCAPED_UNICODE),
                    'created_at' => date('Y-m-d H:i:s'),
                ]
            );
        }
    }

    /**
     * 下载合同
     * Created by: Lqz.
     *
     * @param $id
     * @param $loginUser
     * @return mixed
     * @throws GuzzleException
     */
    public function download($id, $loginUser)
    {
        $code = ErrCode::$SUCCESS;
        $message = 'message';
        $data = [];

        try {
            $exist = WorkflowRequestModel::findFirst([
                'conditions' => "biz_type = :biz_type: and biz_value = :id: and FIND_IN_SET(:uid:,viewer_ids)",
                'bind'       => ['id' => $id, 'uid' => $loginUser['id'], 'biz_type' => Enums::WF_STORE_RENTING_CONTRACT_TYPE]
            ]);
            if (empty($exist)) {
                throw new ValidationException(static::$t->_('no_permission_to_download'), ErrCode::$CONTRACT_GET_INFO_NO_AUTH_ERROR);
            }

            $contract = ContractStoreRentingModel::findFirst([
                'conditions' => 'id = :id: and contract_status = ' . Enums::CONTRACT_STATUS_APPROVAL,
                'bind'       => ['id' => $id]
            ]);
            if (empty($contract)) {
                throw new ValidationException(static::$t->_('data_not_exist_by_id', ['id' => $id]), ErrCode::$VALIDATE_ERROR);
            }

            // 获取租房合同正文附件
            $text_files = SysAttachmentRepository::getInstance()->getAttachmentsByBizParams($id, ContractEnums::RENT_CONTRACT_FILE_OSS_BUCKET_TYPE, ContractEnums::RENT_CONTRACT_SUB_TYPE_FILE_TEXT);
            if (empty($text_files)) {
                throw new ValidationException(static::$t->_('rent_contract_download_text_file_error'), ErrCode::$VALIDATE_ERROR);
            }

            // 判断合同是否即合同的归档状态为待归档，已归档，待上传盖章合同，已作废，待作废，已终止，待终止其中的一种
            $archive = ContractArchive::findFirst([
                'conditions' => 'cno = :cno:',
                'bind'       => ['cno' => $contract->contract_id]
            ]);
            if (empty($archive)) {
                // 归档处理
                $this->saveArchive($id, $loginUser);
            }

            // pdf 下载
            $file_path = $text_files[0]['object_key'];
            $file_name = $text_files[0]['file_name'];
            $file_name = !empty($file_name) ? $file_name : 'StoreRentingContract_' . date('ymdHis') . '.pdf';

            $path = OssHelper::downloadFileHcm($file_path, 600);
            $path = $path['file_url'] ?? '';

            // pdf 加水印
            $water_pdf_info = WaterMarkerService::getInstance()->addWaterMarkerToPdfFileV2($path, $file_name, true);
            if (!empty($water_pdf_info['object_key'])) {
                $result = OssHelper::downloadFileHcm($water_pdf_info['object_key']);
                $data['url'] = $result['file_url'];
            } else {
                $data['url'] = $path;
            }

            if (empty($data['url'])) {
                throw new ValidationException(static::$t->_('rent_contract_download_fail_error'), ErrCode::$VALIDATE_ERROR);
            }

        } catch (ValidationException $e) {
            $code = $e->getCode();
            $message = $e->getMessage();

        } catch (Exception $e) {
            $code = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $this->logger->warning('租房合同申请-正文附件下载异常, 原因可能是 . ' . $e->getMessage());
        }

        return [
            'code' => $code,
            'message' => $message,
            'data' => $data
        ];
    }

    /**
     * 查询作废操作需要的初始值
     * @param $cno
     * @return array
     * @date 2023/1/9
     */
    public function getInvalidParams($cno)
    {
        $code = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $data = [];
        try {
            //模糊查询审批中的网点合同
            $contract_info = ContractStoreRentingModel::findFirst([
                'columns' => 'contract_id as cno, contract_name, contract_begin, contract_end',
                'conditions' => 'contract_id = :contract_id:',
                'bind' => [
                    'contract_id' => $cno,
                ]
            ]);
            if (!$contract_info) {
                throw new ValidationException(static::$t->_('contract_store_renting_not_exist_error'), ErrCode::$VALIDATE_ERROR);
            }
            // 查询是否请款 普通付款或租房付款只有存在有效数据就认定为已请款
            $is_payment = ContractEnums::IS_PAYMENT_NO;
            // 最晚的请款时间, 用来限制合同终止
            $payment_end_date = '';
            // 普通付款-付款分类-房租的id(budget_object表中的id)
            $rent_budget_id = SettingEnvModel::getValByCode('ordinary_payment_rent_budget_id');
            // 租房付款-费用类型-房租的id(payment_cost_type表中的id)
            $rent_cost_id = SettingEnvModel::getValByCode('rent_cost_type_ids');
            if (!empty($rent_budget_id)) {
                // 是否请款-普通付款
                $builder = $this->modelsManager->createBuilder();
                $builder->columns('od.id,od.cost_end_date');
                $builder->from(['od' => OrdinaryPaymentDetail::class]);
                $builder->leftjoin(OrdinaryPayment::class, 'od.ordinary_payment_id = o.id', 'o');
                $builder->andWhere('od.contract_no = :contract_no:', ['contract_no' => $cno]);
                $builder->andWhere('od.budget_id = :budget_id:', ['budget_id' => $rent_budget_id]);
                $builder->andWhere(
                    'o.approval_status = :pending: OR (o.approval_status = :pass: and o.pay_status in ({pay_status:array}))',
                    [
                        'pending' => Enums::ORDINARY_PAYMENT_APPROVAL_STATUS_PENDING,
                        'pass' => Enums::ORDINARY_PAYMENT_APPROVAL_STATUS_PASSED,
                        'pay_status' => [Enums::ORDINARY_PAYMENT_STATUS_PENDING, Enums::ORDINARY_PAYMENT_STATUS_PAY],
                    ]
                );
                $builder->orderBy('od.cost_end_date desc');
                $builder->limit(1);
                $ordinary_apply = $builder->getQuery()->execute()->toArray();
                if (!empty($ordinary_apply)) {
                    $is_payment = ContractEnums::IS_PAYMENT_YES;
                    $payment_end_date = $ordinary_apply[0]['cost_end_date'];
                }
            }
            if (!empty($rent_cost_id)) {
                // 是否请款-租房付款
                $builder = $this->modelsManager->createBuilder();
                $builder->columns('pd.id,pd.cost_end_date');
                $builder->from(['pd' => PaymentStoreRentingDetail::class]);
                $builder->leftjoin(PaymentStoreRenting::class, 'pd.store_renting_id = p.id', 'p');
                $builder->andWhere('pd.contract_no = :contract_no:', ['contract_no' => $cno]);
                $builder->andWhere('pd.cost_type = :cost_type:', ['cost_type' => $rent_cost_id]);
                $builder->andWhere(
                    'p.approval_status = :pending: OR (p.approval_status = :pass: and p.pay_status in ({pay_status:array}))',
                    [
                        'pending' => Enums::PAYMENT_APPLY_STATUS_PENDING,
                        'pass' => Enums::PAYMENT_APPLY_STATUS_APPROVAL,
                        'pay_status' => [Enums::PAYMENT_PAY_STATUS_PENDING, Enums::PAYMENT_PAY_STATUS_PAY],
                    ]
                );
                $builder->orderBy('pd.cost_end_date desc');
                $builder->limit(1);
                $payment_apply = $builder->getQuery()->execute()->toArray();
                if (!empty($payment_apply)) {
                    $is_payment = ContractEnums::IS_PAYMENT_YES;
                    //取最晚的时间
                    if (empty($payment_end_date) || $payment_end_date < $payment_apply[0]['cost_end_date']) {
                        $payment_end_date = $payment_apply[0]['cost_end_date'];
                    }
                }
            }


            //枚举
            $enums_arr = [
                'invalid_refund_method' => ContractEnums::$invalid_refund_method_list,
                'invalid_reason_type' => ContractEnums::$invalid_reason_type_list,
            ];
            $transfer_enums = [];
            foreach ($enums_arr as $key => $value) {
                foreach ($value as $k => $v) {
                    $transfer_enums[$key][] = [
                        'value' => $k,
                        'label' => static::$t->_($v)
                    ];
                }
            }
            //返回值
            $data = [
                'is_payment' => $is_payment,
                'payment_end_date' => $payment_end_date,
                'invalid_refund_method' => $transfer_enums['invalid_refund_method'],
                'invalid_reason_type' => $transfer_enums['invalid_reason_type'],
            ];
        } catch (ValidationException $e) {
            // 校验错误，可对外抛出
            $code = $e->getCode();
            $message = $e->getMessage();
        } catch (Exception $e) {
            $code = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage();
            $this->logger->warning('getInvalidParams-failed:' . $real_message . ' cno :' . $cno);
        }
        return [
            'code' => $code,
            'message' => $message,
            'data' => $data
        ];
    }

    /**
     * 根据合同编号模糊查询租房合同
     * @param $cno
     * @return array
     * @date 2023/1/9
     */
    public function getReplaceCno($cno)
    {
        $code = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $data = [];
        try {
            //查询这些合同中财务审批节点审批中的业务主键
            $ap_audit_ids = $this->getApNodeByBizType();
            $ap_audit_ids = array_values(array_unique($ap_audit_ids));
            //模糊查询AP审批中的网点合同
            if (!empty($ap_audit_ids)) {
                $data = ContractStoreRentingModel::find([
                    'columns' => 'contract_id as cno, contract_name, contract_begin, contract_end',
                    'conditions' => 'id in ({ids:array}) and contract_id like :contract_id: and contract_status = :contract_status:',
                    'bind' => [
                        'ids' => $ap_audit_ids,
                        'contract_id' => $cno . '%',
                        'contract_status' => Enums::CONTRACT_STATUS_PENDING,
                    ]
                ])->toArray();
            }
        } catch (Exception $e) {
            $code = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage();
            $this->logger->warning('getReplaceCno-failed:' . $real_message . ' cno :' . $cno);
        }
        return [
            'code' => $code,
            'message' => $message,
            'data' => $data
        ];
    }

    /**
     * 合同作废/合同终止审批通过
     * @param $user
     * @param $params
     * @param $biz_type
     * @return array
     * @date 2023/1/11
     */
    public function approveInvalid($user, $params, $biz_type)
    {
        $code = ErrCode::$SUCCESS;
        $message = 'success';

        $db = $this->getDI()->get('db_oa');
        $db->begin();
        try {
            $remark = $params['remark'];
            $id = $params['id'];
            if ($biz_type == Enums::WF_STORE_RENTING_CONTRACT_INVALID_TYPE) {
                $audit_fields = 'invalid_status';
            } elseif ($biz_type == Enums::WF_STORE_RENTING_CONTRACT_TERMINAL_TYPE) {
                $audit_fields = 'terminal_status';
            } else {
                throw new BusinessException('合同作废/合同终止审批通过, 不支持的业务类型, biz_type: ' . $biz_type, ErrCode::$BUSINESS_ERROR);
            }
            $work_request = $this->_getWkReq($id, $biz_type);
            if (empty($work_request)) {
                throw new ValidationException(static::$t->_('work_flow_request_empty_data'), ErrCode::$VALIDATE_ERROR);
            }
            //查询租房合同
            $contract = ContractStoreRentingModel::findFirst([
                'conditions' => 'id = :id:',
                'bind' => ['id' => $id],
                'for_update' => true,
            ]);
            if (empty($contract)) {
                throw new ValidationException(static::$t->_('store_renting_contract_info_null', ['sr_id' => $id]), ErrCode::$VALIDATE_ERROR);
            }
            if ($contract->$audit_fields != Enums::WF_STATE_PENDING) {
                throw new ValidationException(static::$t->_('workflow_action_status_check_error'), ErrCode::$VALIDATE_ERROR);
            }
            //查询合同归档
            $contract_archive = ContractArchive::findFirst([
                'conditions' => 'cno = :cno:',
                'bind' => [
                    'cno' => $contract->contract_id
                ],
                'for_update' => true,
            ]);
            if (empty($contract_archive)) {
                throw new ValidationException(static::$t->_('archive_contract_is_not_exist', ['sr_id' => $id]), ErrCode::$VALIDATE_ERROR);
            }
            $contract_arr = $contract->toArray();
            $contract_arr['contract_money_list'] = [];
            if ($contract->ver == 0) {
                $list_obj = ContractStoreRentingMoneyModel::find([
                    'conditions' => 'store_renting_contract_id = :store_renting_contract_id: and type = :type:',
                    'bind' => [
                        'store_renting_contract_id' => $id,
                        'type' => 'contract_money'
                    ]
                ]);

                $contract_arr['contract_money_list'] = $list_obj->toArray();
            }
            $info = $this->_getWkOtherParams($contract_arr, $user, $biz_type);
            $result = (new WorkflowServiceV2())->doApprove($work_request, $user, $info, $remark);
            if ($result === false) {
                throw new BusinessException('网点租房合同审批失败, 请检查; info = ' . json_encode($info, JSON_UNESCAPED_UNICODE), ErrCode::$BUSINESS_ERROR);
            }
            if (!empty($result->approved_at)) {
                if ($biz_type == Enums::WF_STORE_RENTING_CONTRACT_INVALID_TYPE) {
                    //租房合同已通过
                    $contract->invalid_status = Enums::CONTRACT_STATUS_APPROVAL;
                    $contract->effective_status = ContractEnums::RENT_CONTRACT_EFFECTIVE_STATUS_INVALID;
                    //归档合同已通过,状态已作废
                    $contract_archive->invalid_approve_status = Enums::CONTRACT_STATUS_APPROVAL;
                    $contract_archive->status = ContractEnums::CONTRACT_ARCHIVE_STATUS_INVALID;
                } elseif ($biz_type == Enums::WF_STORE_RENTING_CONTRACT_TERMINAL_TYPE) {
                    //租房合同已通过
                    $contract->terminal_status = Enums::CONTRACT_STATUS_APPROVAL;
                    $contract->effective_status = ContractEnums::RENT_CONTRACT_EFFECTIVE_STATUS_INVALID;
                    //归档合同已通过,状态已终止
                    $contract_archive->terminal_approve_status = Enums::CONTRACT_STATUS_APPROVAL;
                    $contract_archive->status = ContractEnums::CONTRACT_ARCHIVE_STATUS_TERMINAL;
                }
                if ($contract->save() === false) {
                    throw new BusinessException('合同作废/合同终止审批通过, 租房合同表更新失败, 原因可能是: ' . get_data_object_error_msg($contract) . '; 数据: ' . json_encode($contract->toArray(), JSON_UNESCAPED_UNICODE), ErrCode::$BUSINESS_ERROR);
                }
                if ($contract_archive->save() === false) {
                    throw new BusinessException('合同作废/合同终止审批通过, 合同归档表更新失败, 原因可能是: ' . get_data_object_error_msg($contract) . '; 数据: ' . json_encode($contract->toArray(), JSON_UNESCAPED_UNICODE), ErrCode::$BUSINESS_ERROR);
                }
            }

            $db->commit();

        } catch (ValidationException $e) {
            $db->rollback();
            $code = $e->getCode();
            $message = $e->getMessage();
        } catch (BusinessException $e) {
            $db->rollback();
            $code = $e->getCode();
            $message = $this->t->_('retry_later');
            $this->logger->warning('approveInvalid-failed: ' . $e->getMessage() . ';biz_type:' . $biz_type);
        } catch (Exception $e) {
            $db->rollback();
            $code = $e->getCode();
            $message = $this->t->_('retry_later');
            $this->logger->error('approveInvalid-failed: ' . $e->getMessage() . ';biz_type:' . $biz_type);
        }

        return [
            'code' => $code,
            'message' => $message,
            'data' => []
        ];
    }

    /**
     * 合同作废/合同终止审批驳回
     * @param $user
     * @param $params
     * @param $biz_type
     * @return bool
     * @throws BusinessException
     * @throws ValidationException
     * @date 2023/1/11
     */
    public function rejectInvalid($user, $params, $biz_type)
    {
        $remark = $params['remark'];
        $id = $params['id'];
        if ($biz_type == Enums::WF_STORE_RENTING_CONTRACT_INVALID_TYPE) {
            $audit_fields = 'invalid_status';
        } elseif ($biz_type == Enums::WF_STORE_RENTING_CONTRACT_TERMINAL_TYPE) {
            $audit_fields = 'terminal_status';
        } else {
            throw new BusinessException('合同作废/合同终止审批通过, 不支持的业务类型, biz_type: ' . $biz_type, ErrCode::$BUSINESS_ERROR);
        }
        $work_request = $this->_getWkReq($id, $biz_type);
        if (empty($work_request)) {
            throw new ValidationException(static::$t->_('work_flow_request_empty_data'), ErrCode::$VALIDATE_ERROR);
        }
        //查询租房付款
        $contract = ContractStoreRentingModel::findFirst([
            'conditions' => 'id = :id:',
            'bind' => ['id' => $id],
            'for_update' => true,
        ]);
        if (empty($contract)) {
            throw new ValidationException(static::$t->_('store_renting_contract_info_null', ['sr_id' => $id]), ErrCode::$VALIDATE_ERROR);
        }
        if ($contract->$audit_fields != Enums::WF_STATE_PENDING) {
            throw new ValidationException(static::$t->_('workflow_action_status_check_error'), ErrCode::$VALIDATE_ERROR);
        }
        //查询合同归档
        $contract_archive = ContractArchive::findFirst([
            'conditions' => 'cno = :cno:',
            'bind' => [
                'cno' => $contract->contract_id
            ],
            'for_update' => true,
        ]);
        if (empty($contract_archive)) {
            throw new ValidationException(static::$t->_('archive_contract_is_not_exist', ['sr_id' => $id]), ErrCode::$VALIDATE_ERROR);
        }

        $contract_arr = $contract->toArray();

        $contract_arr['contract_money_list'] = [];
        if ($contract->ver == 0) {
            $listObj = ContractStoreRentingMoneyModel::find([
                'conditions' => 'store_renting_contract_id = :store_renting_contract_id: and type = :type:',
                'bind' => [
                    'store_renting_contract_id' => $id,
                    'type' => 'contract_money'
                ]

            ]);

            $contract_arr['contract_money_list'] = $listObj->toArray();
        }
        $info = $this->_getWkOtherParams($contract_arr, $user, $biz_type);
        $result = (new WorkflowServiceV2())->doReject($work_request, $user, $info, $remark);
        if ($result === false) {
            throw new BusinessException('workflow reject error', ErrCode::$BUSINESS_ERROR);
        }
        if ($biz_type == Enums::WF_STORE_RENTING_CONTRACT_INVALID_TYPE) {
            //租房合同驳回
            $contract->invalid_status = Enums::CONTRACT_STATUS_REJECTED;
            //归档合同已通过,状态已作废
            $contract_archive->invalid_approve_status = Enums::CONTRACT_STATUS_REJECTED;
            $contract_archive->status = $contract_archive->before_status;
        } elseif ($biz_type == Enums::WF_STORE_RENTING_CONTRACT_TERMINAL_TYPE) {
            //租房合同已通过
            $contract->terminal_status = Enums::CONTRACT_STATUS_REJECTED;
            //归档合同已通过,状态已终止
            $contract_archive->terminal_approve_status = Enums::CONTRACT_STATUS_REJECTED;
            $contract_archive->status = $contract_archive->before_status;
        }
        if ($contract->save() === false) {
            throw new BusinessException('合同作废/合同终止审批通过, 租房合同表更新失败, 原因可能是: ' . get_data_object_error_msg($contract) . '; 数据: ' . json_encode($contract->toArray(), JSON_UNESCAPED_UNICODE), ErrCode::$BUSINESS_ERROR);
        }
        if ($contract_archive->save() === false) {
            throw new BusinessException('合同作废/合同终止审批通过, 合同归档表更新失败, 原因可能是: ' . get_data_object_error_msg($contract) . '; 数据: ' . json_encode($contract->toArray(), JSON_UNESCAPED_UNICODE), ErrCode::$BUSINESS_ERROR);
        }
        return true;
    }


    /**
     * 枚举签约类型
     **/
    public function getLangRenewalType()
    {
        $renewal_type = [];
        foreach (ContractEnums::$contract_renewal_type as $key => $value) {
            $renewal_type[] = [
                'id'   => (string)$key,
                'name' => static::$t->_($value)
            ];
        }
        return $renewal_type;
    }

    /**
     * 枚举审批类型
     * */

    public function getApproveType()
    {
        $approve_type     = [];
        $approve_type_arr = [
            Enums::WF_STORE_RENTING_CONTRACT_TYPE          => static::$t->_('store_renting_contract_type'),
            Enums::WF_STORE_RENTING_CONTRACT_INVALID_TYPE  => static::$t->_('store_renting_contract_invalid_type'),
            Enums::WF_STORE_RENTING_CONTRACT_TERMINAL_TYPE => static::$t->_('store_renting_contract_terminal_type'),
            Enums::WF_STORE_RENTING_CONTRACT_RENEWAL_TYPE  => static::$t->_('store_renting_contract_renewal_type'),
        ];

        foreach ($approve_type_arr as $key => $value) {
            $approve_type[] = [
                'id'   => $key,
                'name' => $value
            ];
        }
        return $approve_type;

    }

    /**
     * 签约初始化数据
     *
     * @param $id
     * @param $user
     * @return array
     */
    public function renewalDefault(int $id, array $user)
    {
        $code    = ErrCode::$SUCCESS;
        $message = 'ok';

        try {
            $contract      = $this->getContractInfo($id);
            if (empty($contract)) {
                throw new ValidationException(static::$t->_('data_not_exist_by_id', ['id' => $id]), ErrCode::$VALIDATE_ERROR);
            }

            $contract_info = [];

            $contract_info['is_main']              = $contract['is_main'];
            $contract_info['store_cate']           = $contract['store_cate'];
            $contract_info['store_id']             = $contract['store_id'];
            $contract_info['store_name']           = $contract['store_name'];
            $contract_info['provinces']            = $contract['provinces'];
            $contract_info['lon_lat']              = $contract['lon_lat'];
            $contract_info['store_addr']           = $contract['store_addr'];
            $contract_info['contract_tax_no']      = $contract['contract_tax_no'];
            $contract_info['house_owner_name']     = $contract['house_owner_name'];
            $contract_info['house_contract_area']  = $contract['house_contract_area'];
            $contract_info['house_actual_area']    = $contract['house_actual_area'];
            $contract_info['house_owner_type']     = $contract['house_owner_type'];
            $contract_info['house_equip_list']     = $contract['house_equip_list'];
            $contract_info['relevant_contract_no'] = $contract['contract_id'];
            $contract_info['relevant_contract_id'] = $id;

            // 附件列表
            if ($contract['attachment_list']) {
                foreach ($contract['attachment_list'] as $file_field => $value) {
                    $contract_info['attachment_list'][$file_field] = [];
                }
            } else {
                $contract_info['attachment_list'] = (object)[];
            }

            // 合同的仓库信息
            $contract_info['warehouse_id'] = $contract['warehouse_id'];
            $contract_info['warehouse_latitude'] = $contract['warehouse_latitude'];
            $contract_info['warehouse_longitude'] = $contract['warehouse_longitude'];
            $contract_info['warehouse_name'] = $contract['warehouse_name'];
            $contract_info['warehouse_province_name'] = $contract['warehouse_province_name'];
            $contract_info['warehouse_city_name'] = $contract['warehouse_city_name'];
            $contract_info['warehouse_district_name'] = $contract['warehouse_district_name'];
            $contract_info['warehouse_real_area'] = $contract['warehouse_real_area'];

            $contract_info['renewal_contract_type'] = ContractEnums::RENEWAL_CONTRACT_TYPE_2;
            $userDept                          = HrStaffInfoModel::getUserInfo($user['id'], 'sys_department_id');

            $store_cate_list = ContractStoreRentingService::getInstance()->getStoreCateList($userDept, ['manage_id' => $contract['manage_id']]);

            unset($contract);

            //COO/CEO下的BU级公司列表
            $coo_company_list = (new PurchaseService())->getCooCostCompany();

            $csrf_token = (new CsrfTokenServer())->getCsrfToken();
            //根据费用所属部门查询对应的COO/CEO下的BU级部门
            $cost_company_id   = '';
            $cost_company_name = '';
            $cost_company_kv   = array_column($coo_company_list, 'cost_company_name', 'cost_company_id');
            if (!empty($t) && key_exists($t->company_id, $cost_company_kv)) {
                $cost_company_id   = $t->company_id;
                $cost_company_name = $cost_company_kv[$t->company_id];
            }

            $store_cate_lists = [];

            foreach ($store_cate_list as $k => $v) {
                $store_cate_lists[] = [
                    'id'   => $k,
                    'name' => $v
                ];
            }
            $res['csrf_token']        = $csrf_token;
            $res['contract_info']     = $contract_info;
            $res['store_cate_list']   = $store_cate_lists;
            $res['wkflow']            = [];
            $res['invalid_wkflow']    = [];
            $res['terminal_wkflow']   = [];
            $res['cost_company_id']   = $cost_company_id;
            $res['cost_company_name'] = $cost_company_name;

            // 仓库信息必填的网点类型配置列表
            $res['warehouse_required_for_store_types'] = EnumsService::getInstance()->getSettingEnvValueIds('warehouse_required_for_store_types');

        } catch (ValidationException $e) {
            $code    = $e->getCode();
            $message = $e->getMessage();

        } catch (Exception $e) {
            $code    = $e->getCode();
            $message = $this->t->_('retry_later');
            $this->logger->error('renewal-default-failed: ' . $e->getMessage() . ';id:' . $id);
        }

        return [
            'code'    => $code,
            'message' => $message,
            'data'    => $res ?? []
        ];
    }

    public function getCommonParams()
    {
        $lease_type         = [];
        $payer              = [];
        $pay_type           = [];
        $contract_status    = [];
        $archive_status     = [];
        $lang_list          = [];
        $is_main_list       = [];
        $contract_type_list = [];
        $land_type          = [];
        $rent_du_date       = [];
        $leaser_type        = [];
        $currency           = [];
        $effective_status   = [];

        foreach (GlobalEnums::$currency_item as $code => $t_key) {
            $currency[] = [
                'code'  => $code,
                'label' => static::$t[$t_key],
            ];
        }

        foreach (($this->getContractLeaseTypes()) as $k => $v) {
            $lease_type[] = [
                'id'   => (string)$k,
                'name' => $v
            ];
        }

        foreach ($this->getPayer() as $k => $v) {
            $payer[] = [
                'id'   => (string)$k,
                'name' => $v
            ];
        }


        foreach ($this->getPayType() as $k => $v) {
            $pay_type[] = [
                'id'   => (string)$k,
                'name' => $v
            ];

        }


        foreach ($this->getAuditStatus() as $k => $v) {
            $contract_status[] = [
                'id'   => (string)$k,
                'name' => $v
            ];
        }


        foreach ($this->getArchiveStatus() as $k => $v) {
            $archive_status[] = [
                'id'   => (string)$k,
                'name' => $v
            ];
        }


        foreach ((EnumsService::getInstance()->getContractLang()) as $k => $v) {
            $lang_list[] = [
                'id'   => (string)$k,
                'name' => $v
            ];
        }


        foreach ($this->getContactType() as $k => $v) {
            $contract_type_list[] = [
                'id'   => (string)$k,
                'name' => $v
            ];
        }

        foreach ($this->getLandType() as $k => $v) {
            $land_type[] = [
                'id'   => (string)$k,
                'name' => $v
            ];
        }

        foreach ($this->getRentDueDate() as $k => $v) {
            $rent_du_date[] = [
                'id'   => (string)$k,
                'name' => $v
            ];
        }

        foreach ($this->getIsMainList() as $k => $v) {
            $is_main_list[] = [
                'id'   => (string)$k,
                'name' => $v
            ];
        }

        foreach ($this->getLeaserType() as $k => $v) {
            $leaser_type[] = [
                'id'   => (string)$k,
                'name' => $v
            ];
        }
        foreach ($this->getEffectiveStatus() as $k => $v) {
            $effective_status[] = [
                'value'   => (string)$k,
                'label' => $v,
            ];
        }

        $res          = [
            'lease_types'            => $lease_type,
            'leaser_type'            => $leaser_type,
            'wht_cate'               => EnumsService::getInstance()->getFormatWhtRateConfig(),
            'vat7_rate_list'         => EnumsService::getInstance()->getFormatVatRateConfig(),
            'payer'                  => $payer,
            'pay_type'               => $pay_type,
            'contract_business_type' => $this->getApproveType(),
            'renewal_type'           => $this->getLangRenewalType(),
            'archive_status'         => $archive_status,
            'contract_status'        => $contract_status,
            'lang_list'              => $lang_list,

            'is_main_list'      => $is_main_list,
            'contact_type_list' => $contract_type_list,
            'land_type'         => $land_type,
            'house_owner'       => $this->getHouseOwnerType(),
            'currency'          => $currency,
            'rent_due_date'     => $rent_du_date,
            'coo_company_list'  => (new PurchaseService())->getCooCostCompany(),
            'effective_status'  => $effective_status,
        ];
        $country_code = get_country_code();
        if (GlobalEnums::PH_COUNTRY_CODE == $country_code || GlobalEnums::MY_COUNTRY_CODE == $country_code) {
            $res['loi_wht_cate'] = EnumsService::getInstance()->getFormatAllWhtRateConfig();
        }

        if (GlobalEnums::TH_COUNTRY_CODE == $country_code) {
            $enums_arr = [
                'withholding_tax_liability_bearer' => WarehouseEnums::$thread_withholding_tax_liability_bearer, //承担扣缴税款责任方
                'water_bill_payment_type'          => WarehouseEnums::$thread_water_bill_payment_type, //水费支付类型
                'electricity_bill_payment_type'    => WarehouseEnums::$thread_electricity_bill_payment_type,//电费支付类型
                'water_billing_method'             => WarehouseEnums::$thread_water_billing_method, //水费使用计费方式
                'electricity_billing_method'       => WarehouseEnums::$thread_electricity_billing_method, //电费使用计费方式
                'land_tax_liability_bearer'        => WarehouseEnums::$thread_land_tax_liability_bearer, //土地税承担责任方
                'stamp_duty_liability_bearer'      => WarehouseEnums::$thread_stamp_duty_liability_bearer,//印花税承担责任方
                'tax_type'                         => ContractEnums::$tax_type_items,//税费类型：区域服务费、物业费、土地税，房产税，火灾保险费
            ];
            foreach ($enums_arr as $key => $value) {
                foreach ($value as $k => $v) {
                    $res[$key][] = [
                        'value' => (string)$k,
                        'label' => static::$t->_($v),
                    ];
                }
            }
        }

        $res['is_main_type'] = $this->getLangIsMain();

        return $res;
    }

    /**
     * @param $id
     * @param $user
     * @param int $sub_menu_type
     * @return array
     * @throws BusinessException
     */
    public function getDetail($id, $user, $sub_menu_type = 0)
    {
        if (empty($id)) {
            $csrf_token = (new CsrfTokenServer())->getCsrfToken();
        }

        $contract         = $this->getContractInfo($id);
        $user_dept        = HrStaffInfoModel::getUserInfo($user['id'], 'sys_department_id');
        $department       = DepartmentModel::findFirst([
            'conditions' => 'id = :id:',
            'bind'       => [
                "id" => $user_dept['sys_department_id'] ?? '',
            ]
        ]);
        $store_cate_list  = ContractStoreRentingService::getInstance()->getStoreCateList($user_dept, $contract);
        $store_cate_lists = [];
        foreach ($store_cate_list as $k => $v) {
            $store_cate_lists[] = [
                'id'   => $k,
                'name' => $v
            ];
        }

        $work_flow        = null;
        $invalid_workflow = $terminal_workflow = [];
        $archive_info = null;
        if ($contract) {
            [$work_flow, $ask] = $this->getWkflow($id, $user['id']);
            // 更改审批流状态字段
            $count = count($work_flow);
            foreach ($work_flow as $k => $wkinfo) {
                if (($k < $count - 1) && Enums::WF_ACTION_APPLY == $wkinfo['action']) {
                    $work_flow[$k]['action_name'] = self::$t->_('flow_audit_action.7');
                }
            }
            $contract['ask_id'] = '';
            if (!empty($ask)) {
                $contract['ask_id'] = $ask->id;
            }
            $workRequest                = $this->_getWkReq($id);
            $can_edit_field             = (new ContractFlowService())->getCanEditFieldByReq($workRequest, $user['id']);
            $contract['can_edit_field'] = $can_edit_field === false ? (object)[] : $can_edit_field;
            $contract['can_edit']       = $can_edit_field === false ? 0 : 1;
            //作废审批流
            $invalid_workflow = $this->getWkflow($id, $user['id'], Enums::WF_STORE_RENTING_CONTRACT_INVALID_TYPE);
            //终止审批流
            $terminal_workflow = $this->getWkflow($id, $user['id'], Enums::WF_STORE_RENTING_CONTRACT_TERMINAL_TYPE);

            // 是否取归档信息(需审核通过)
            if ($sub_menu_type == self::LIST_TYPE_SEARCH && $contract['contract_status'] == Enums::WF_STATE_APPROVED) {
                $archive = ContractArchive::findFirst([
                    'conditions' => 'cno = :cno:',
                    'bind' => ['cno' => $contract['contract_id']]
                ]);

                // 需要展示归档信息的归档状态
                $archive_status_item = [
                    ContractEnums::CONTRACT_ARCHIVE_STATUS_APPROVAL,
                    ContractEnums::CONTRACT_ARCHIVE_STATUS_INVALID_ING,
                    ContractEnums::CONTRACT_ARCHIVE_STATUS_TERMINAL_ING,
                    ContractEnums::CONTRACT_ARCHIVE_STATUS_INVALID,
                    ContractEnums::CONTRACT_ARCHIVE_STATUS_TERMINAL,
                ];

                if (!empty($archive) && in_array($archive->status, $archive_status_item)) {
                    $archive_info['filing_name'] = $archive->filing_name;// 归档人姓名
                    $archive_info['filing_id'] = $archive->filing_id;// 归档人工号
                    $archive_info['filing_at'] = $archive->filing_at;// 归档时间
                    $archive_info['holder_name'] = $archive->holder_name;// 合同原件保管人
                    $archive_info['contract_file'] = $this->getRemotePath($archive->contract_file,AttachmentService::BUSINESS_TYPE_CONTRACT_ARCHIVE_CONTRACT_FILE);// 盖章合同
                    $archive_info['contract_signature'] = $archive->getSignatureFiles()->toArray();// 双方签字合同
                }
            }
        }

        //COO/CEO下的BU级公司列表
        $coo_company_list = (new PurchaseService())->getCooCostCompany();

        //根据费用所属部门查询对应的COO/CEO下的BU级部门
        $cost_company_id   = '';
        $cost_company_name = '';
        $cost_company_kv   = array_column($coo_company_list, 'cost_company_name', 'cost_company_id');
        if (!empty($department) && key_exists($department->company_id, $cost_company_kv)) {
            $cost_company_id   = $department->company_id;
            $cost_company_name = $cost_company_kv[$department->company_id];
        }

        // 仓库信息必填的网点类型配置列表
        $warehouse_required_for_store_types = EnumsService::getInstance()->getSettingEnvValueIds('warehouse_required_for_store_types');

        return [
            'csrf_token'        => $csrf_token ?? '',
            'store_cate_list'   => $store_cate_lists,
            'contract_info'     => $contract,
            'archive_info'      => $archive_info,
            'wkflow'            => $work_flow,
            'invalid_wkflow'    => $invalid_workflow[0] ?? [],
            'terminal_wkflow'   => $terminal_workflow[0] ?? [],
            'cost_company_id'   => $cost_company_id,
            'cost_company_name' => $cost_company_name,
            'warehouse_required_for_store_types' => $warehouse_required_for_store_types
        ];
    }
    /**
     * 合同转交
     * @param $params
     * @param $uid
     * @return array
     */
    public function contractTransfer($params, $uid)
    {
        $code    = ErrCode::$SUCCESS;
        $message = '';

        $db = $this->getDI()->get('db_oa');
        $db->begin();
        try {
            //验证工号
            $staff_info = HrStaffInfoModel::findFirst([
                'conditions' => 'staff_info_id = :staff_info_id: and  state = :state: and formal = :formal: and wait_leave_state = :wait_leave_state:',
                'bind'       => [
                    'staff_info_id'    => $params['contract_leader_id'],
                    'formal'           => Enums\StaffInfoEnums::FORMAL_IN,
                    'state'            => Enums\StaffInfoEnums::STAFF_STATE_IN,
                    'wait_leave_state' => Enums\StaffInfoEnums::STAFF_WAIT_LEAVE_STATE_NO,
                ],
            ]);

            if (empty($staff_info)) {
                throw new ValidationException(static::$t->_('batchimportstaff_3'), ErrCode::$VALIDATE_ERROR);
            }

            $contract = ContractStoreRentingModel::findFirst([
                'conditions' => 'id = :id:',
                'bind'       => ['id' => $params['id']]

            ]);

            if (empty($contract)) {
                throw new ValidationException(static::$t->_('store_renting_contract_info_null'), ErrCode::$VALIDATE_ERROR);
            }

            // 转交前
            $this->logger->info(['contract_store_renting_transfer_before_data' => $contract->toArray()]);

            $before_contract_leader_id    = $contract->contract_leader_id;
            $contract->contract_leader_id = $params['contract_leader_id'];

            // 新负责人所属部门ID和名称
            $leader_node_dept_info = (new HrStaffRepository())->getStaffNodeDepartmentInfoByIds([$contract->contract_leader_id]);
            $leader_node_dept_info = $leader_node_dept_info[$contract->contract_leader_id] ?? [];
            $contract->leader_node_department_id = $leader_node_dept_info['node_department_id'] ?? 0;
            $contract->leader_node_department_name = $leader_node_dept_info['node_department_name'] ?? '';
            if ($contract->save() === false) {
                throw new BusinessException('合同转交: ' . get_data_object_error_msg($contract) . '; 数据: ' . json_encode($params, JSON_UNESCAPED_UNICODE), ErrCode::$BUSINESS_ERROR);
            }

            // 转交后
            $this->logger->info(['contract_store_renting_transfer_after_data' => $contract->toArray()]);

            //记录日志
            $log_model = new  ContractStoreRentingEditLogModel();
            $log_data = [
                'contract_no' => $contract->contract_id,
                'staff_id'    => $uid,
                'content'     => json_encode(['before' => $before_contract_leader_id, 'after' => $params['contract_leader_id'], 'field' => 'contract_leader_id']),
                'created_at'  => date('Y-m-d H:i:s')
            ];

            if ($log_model->save($log_data) === false) {
                throw new BusinessException('合同转交日志: ' . get_data_object_error_msg($log_model) . '; 数据: ' . json_encode($log_data, JSON_UNESCAPED_UNICODE), ErrCode::$BUSINESS_ERROR);
            }

            $db->commit();

        } catch (ValidationException $e) {
            $code    = $e->getCode();
            $message = $e->getMessage();
        } catch (BusinessException $e) {
            $code    = $e->getCode();
            $message = $this->t->_('retry_later');
            $this->logger->warning('contractTransfer-failed: ' . $e->getMessage());
        } catch (Exception $e) {
            $code    = $e->getCode();
            $message = $this->t->_('retry_later');
            $this->logger->error('contractTransfer-failed: ' . $e->getMessage());
        }

        if (!empty($message)) {
            $db->rollback();
        }

        return [
            'code'    => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data'    => []
        ];
    }

    /**
     * 批量合同转交
     * @param $params
     * @param $uid
     * @return array
     */
    public function batchContractTransfer($params, $uid)
    {
        $code    = ErrCode::$SUCCESS;
        $message = '';

        $db = $this->getDI()->get('db_oa');
        $db->begin();
        try {
            //验证工号
            $staff_info = HrStaffInfoModel::findFirst([
                'conditions' => 'staff_info_id = :staff_info_id: and  state = :state: and formal = :formal: and wait_leave_state = :wait_leave_state:',
                'bind'       => [
                    'staff_info_id'    => $params['contract_leader_id'],
                    'formal'           => Enums\StaffInfoEnums::FORMAL_IN,
                    'state'            => Enums\StaffInfoEnums::STAFF_STATE_IN,
                    'wait_leave_state' => Enums\StaffInfoEnums::STAFF_WAIT_LEAVE_STATE_NO,
                ],
            ]);

            if (empty($staff_info)) {
                throw new ValidationException(static::$t->_('batchimportstaff_3'), ErrCode::$VALIDATE_ERROR);
            }


            $contract = ContractStoreRentingModel::find([
                'conditions' => 'store_id  in ({ids:array})',
                'bind'       => ['ids' => array_values(array_unique($params['store_id']))]

            ])->toArray();

            if (empty($contract)) {
                throw new ValidationException(static::$t->_('store_renting_contract_info_null'), ErrCode::$VALIDATE_ERROR);
            }

            foreach ($contract as $item) {
                $log_data[] = [
                    'contract_no' => $item['contract_id'],
                    'staff_id'    => $uid,
                    'content'     => json_encode(['before' => $item['contract_leader_id'], 'after' => $params['contract_leader_id'], 'field' => 'contract_leader_id']),
                    'created_at'  => date('Y-m-d H:i:s')
                ];
            }

            // 新负责人所属部门ID和名称
            $leader_node_dept_info = (new HrStaffRepository())->getStaffNodeDepartmentInfoByIds([$params['contract_leader_id']]);
            $leader_node_dept_info = $leader_node_dept_info[$params['contract_leader_id']] ?? [];
            $leader_node_department_id = $leader_node_dept_info['node_department_id'] ?? 0;
            $leader_node_department_name = $leader_node_dept_info['node_department_name'] ?? '';

            // 待更新字段
            $update_data = [
                'contract_leader_id' => $params['contract_leader_id'],
                'leader_node_department_id' => $leader_node_department_id,
                'leader_node_department_name' => $leader_node_department_name,
            ];

            $this->logger->info(['store_renting_contract_batch_transfer_after_data' => $update_data]);

            $ids = implode(array_values(array_column($contract, 'id')), ',');

            $update_bool = $db->updateAsDict(
                (new  ContractStoreRentingModel())->getSource(),
                $update_data,
                ['conditions' => "id in ($ids)"]
            );
            if ($update_bool === false) {
                throw new BusinessException('合同转交: ' . get_data_object_error_msg($db) . '; 数据: ' . json_encode($params, JSON_UNESCAPED_UNICODE), ErrCode::$BUSINESS_ERROR);
            }

            //记录日志
            if (!empty($log_data)) {
                $log_model = new  ContractStoreRentingEditLogModel();

                if ($log_model->batch_insert($log_data) === false) {
                    throw new BusinessException('合同转交日志: ' . get_data_object_error_msg($log_model) . '; 数据: ' . json_encode($log_data, JSON_UNESCAPED_UNICODE), ErrCode::$BUSINESS_ERROR);
                }
            }

            $db->commit();

        } catch (ValidationException $e) {
            $code    = $e->getCode();
            $message = $e->getMessage();
        } catch (BusinessException $e) {
            $code    = $e->getCode();
            $message = static::$t->_('retry_later');
            $this->logger->warning('batchContractTransfer-failed: ' . $e->getMessage());
        } catch (Exception $e) {
            $code    = $e->getCode();
            $message = static::$t->_('retry_later');
            $this->logger->error('batchContractTransfer-failed: ' . $e->getMessage());

        }
        if (!empty($message)) {
            $db->rollback();
        }

        return [
            'code'    => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data'    => []
        ];

    }

    /**
     *
     * 重新提交
     *
     * @param $user
     * @param $params
     * @return array|string[]
     */
    public function reCommit($user, $params)
    {
        $code    = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $db = $this->getDI()->get('db_oa');
        $db->begin();

        try {

            $model = ContractStoreRentingModel::findFirst([
                'conditions' => 'id = :id:',
                'bind'       => ['id' => $params['id']]
            ]);

            if (empty($model)) {
                throw new ValidationException(static::$t->_('store_renting_contract_info_null'), ErrCode::$VALIDATE_ERROR);
            }

            if (!in_array($model->contract_status, [Enums::CONTRACT_STATUS_REJECTED, Enums::CONTRACT_STATUS_CANCEL])) {
                throw new ValidationException(static::$t->_('store_renting_contract_status_wrong'), ErrCode::$VALIDATE_ERROR);
            }

            $coo_company_list = (new PurchaseService())->getCooCostCompany();

            //根据费用所属部门查询对应的COO/CEO下的BU级部门
            $cost_company_arr = array_column($coo_company_list, 'cost_company_name', 'cost_company_id');

            $exchange_rate = EnumsService::getInstance()->getCurrencyExchangeRate($params['money_symbol']);
            $exchange_rate = $exchange_rate ? $exchange_rate : 1;

            $country_code = get_country_code();
            // 新增 和 编辑 需存储的公共字段
            $model->contract_lang          = $params['contract_lang'];
            $model->manage_id              = $user['id'];
            $model->create_department_id   = $user['node_department_id'];
            $model->contract_leader_id     = $user['id'];
            $model->contract_money         = $params['contract_money'] ?? 0;
            $model->actual_monthly_payment = 0;
            $model->is_main                = $params['is_main'];
            $model->main_contract_id       = $params['main_contract_id'] ?? null;
            $model->store_cate             = $params['store_cate'];
            $model->store_id               = $params['store_id'];
            $model->provinces              = $params['provinces'];
            $model->lon_lat                = $params['lon_lat'];
            $model->store_addr             = $params['store_addr'];
            $model->house_owner_name       = $params['house_owner_name'];
            $model->house_contract_area    = $params['house_contract_area'];
            $model->house_actual_area      = $params['house_actual_area'];
            $model->house_owner_type       = $params['house_owner_type'];
            $model->house_equip_list       = $params['house_equip_list'];

            $model->land_type           = $params['land_type'] ? implode(',', $params['land_type']) : '1';
            $model->land_type_content   = $params['land_type_content'] ?? '';
            $model->leaser_type         = $params['leaser_type'] ?? 1;
            $model->leaser_type_content = $params['leaser_type_content'] ?? '';
            $model->rent_due_date       = $params['rent_due_date'] ?? 1;

            $model->contract_name     = $params['contract_name'];
            $model->contract_deadline = $params['contract_deadline'] ?? '0';
            $model->contract_begin    = $params['contract_begin'];
            $model->contract_end      = $params['contract_end'];
            $model->rent_free_time    = (!empty($params['rent_free_time'])) ? (implode(",", $params['rent_free_time'])) : ' ';

            $model->exempted_amount_months = $params['exempted_amount_months'];
            $model->contract_effect_date   = !empty($params['contract_effect_date']) ? $params['contract_effect_date'] : '0000-00-00';
            $model->contract_lease_type    = $params['contract_lease_type'];

            $model->contract_benefits      = ' ';
            $model->money_symbol           = $params['money_symbol'];
            $model->exchange_rate          = $exchange_rate;
            $model->deposit_amount         = $params['deposit_amount'];
            $model->monthly_payment_type   = null;
            $model->contract_signer_name   = $params['contract_signer_name'];
            $model->signer_phone           = $params['signer_phone'];
            $model->notice_renewal_days    = $params['notice_renewal_days'] ?? '';
            $model->full_refund_conditions = $params['full_refund_conditions'];
            $model->renovation_days        = $params['renovation_days'] ?? '';
            $model->contract_remarks       = $params['contract_remarks'];
            $model->bank_collection        = json_encode($params['bank_collection'], JSON_UNESCAPED_UNICODE);
            $model->hourse_owner_addr      = $params['hourse_owner_addr'];

            $model->property_tax         = $params['property_tax'];
            $model->total_amount_monthly = $country_code == GlobalEnums::TH_COUNTRY_CODE ? '' : $params['total_amount_monthly'];//房产税金额;
            $model->duty_stamp           = $params['duty_stamp'];
            $model->total_amount         = $params['total_amount'];
            $model->signboard_tax        = $params['signboard_tax'];
            $model->pdf_required_name    = ''; // 正文附件 v18530 迁移到系统附件表 json_encode($params['pdf_required_name'], JSON_UNESCAPED_UNICODE);
            $model->pdf_noneed_name      = ''; // 其他附件 v18530 迁移到系统附件表 json_encode($params['pdf_noneed_name'], JSON_UNESCAPED_UNICODE);

            // 增加
            $model->contract_total_amount             = $params['contract_total_amount'];
            $model->billboard_tax_payer               = $params['billboard_tax_payer'];
            $model->contract_deposit                  = $params['contract_deposit'] ?? '';
            $model->ver                               = $params['ver'];
            $model->due_date                          = $params['due_date'];
            $model->amount_paid                       = $params['amount_paid'];
            $model->contract_tax_no                   = $params['contract_tax_no'];
            $model->land_tax_payer                    = $params['land_tax_payer'];
            $model->fire_insurance_payer              = $params['fire_insurance_payer'];
            $model->antimoth_payer                    = $params['antimoth_payer'];
            $model->land_tax_amount                   = $country_code == GlobalEnums::TH_COUNTRY_CODE ? '' : $params['land_tax_amount'];//土地税金额;
            $model->fire_insurance_amount             = $country_code == GlobalEnums::TH_COUNTRY_CODE ? '' : $params['fire_insurance_amount'];//火灾保险费金额;
            $model->antimoth_amount                   = $params['antimoth_amount'];
            $model->relate_contract_id                = $params['relate_contract_id'] ?? '';
            $model->cost_company_id                   = $params['cost_company_id'];
            $model->cost_company_name                 = $cost_company_arr[$params['cost_company_id']] ?? '';
            $model->contract_total_amount_contain_wht = $country_code == GlobalEnums::TH_COUNTRY_CODE ? '' : ($params['contract_total_amount_contain_wht'] ?? 0.00);
            $model->contract_total_amount_no_wht      = $params['contract_total_amount_no_wht'] ?? null;//合同总金额（不含WHT含VAT）
            $model->month_rent_has_wht                = $country_code == GlobalEnums::TH_COUNTRY_CODE ? $this->calculateMonthRentHasWht($params) : null;//月每平方米租金（含WHT含VAT）
            $model->update_at                         = date('Y-m-d H:i:s');
            $model->relevant_contract_no              = empty($params['relevant_contract_no']) ? '' : $params['relevant_contract_no'];
            $model->contract_leader_id                = $params['contract_leader_id'];
            $model->contract_status                   = Enums::CONTRACT_STATUS_PENDING;

            // 仓库信息
            $model->warehouse_id                     = $params['warehouse_id'];
            $model->warehouse_latitude               = $params['warehouse_latitude'];
            $model->warehouse_longitude              = $params['warehouse_longitude'];
            $model->warehouse_name                   = $params['warehouse_name'];
            $model->warehouse_province_name          = $params['warehouse_province_name'];
            $model->warehouse_city_name              = $params['warehouse_city_name'];
            $model->warehouse_district_name          = $params['warehouse_district_name'];
            $model->warehouse_real_area              = $params['warehouse_real_area'];
            $model->warehouse_price_id               = $params['warehouse_price_id'] ?? 0;
            $model->warehouse_price_no               = $params['warehouse_price_no'] ?? '';
            $model->withholding_tax_liability_bearer = $params['withholding_tax_liability_bearer'] ?? 0;//扣缴税责任承担方
            $model->water_bill_payment_type          = $params['water_bill_payment_type'] ?? 0;         //水费支付类型
            $model->electricity_bill_payment_type    = $params['electricity_bill_payment_type'] ?? 0;   //电费支付类型
            $model->water_billing_method             = $params['water_billing_method'] ?? 0;            //水费使用计费方式
            $model->electricity_billing_method       = $params['electricity_billing_method'] ?? 0;      //电费使用计费方式
            $model->water_usage_units                = $params['water_usage_units'] ?? null;            //水费使用单位数
            $model->electricity_usage_units          = $params['electricity_usage_units'] ?? null;      //电费使用单位数
            $model->issue_withholding_tax_address    = $params['issue_withholding_tax_address'] ?? '';  //开具扣缴税款单据的地址

            // 负责人所属部门ID和名称
            $leader_node_dept_info = (new HrStaffRepository())->getStaffNodeDepartmentInfoByIds([$model->contract_leader_id])[$model->contract_leader_id] ?? [];
            $model->leader_node_department_id = $leader_node_dept_info['node_department_id'] ?? 0;
            $model->leader_node_department_name = $leader_node_dept_info['node_department_name'] ?? '';

            if ($model->save() === false) {
                throw new BusinessException('租房合同重新提交失败，数据：' . json_encode($model->toArray(), JSON_UNESCAPED_UNICODE) . '原因：' . get_data_object_error_msg($model), ErrCode::$BUSINESS_ERROR);
            }

            $contractId                  = $model->id;
            $contract_money_list         = $params['contract_money_list'] ?? [];
            $actual_monthly_payment_list = $params['actual_monthly_payment_list'] ?? [];

            $moneyIdsArr1 = array_column($contract_money_list, 'id');
            $moneyIdsArr2 = array_column($actual_monthly_payment_list, 'id');
            // 新版本通过amount_detai来取
            $moneyIdsArr = array_merge($moneyIdsArr1, $moneyIdsArr2);
            // 老数据禁止编辑
            if ($moneyIdsArr) {
                return [
                    'error_msg' => 'old contract data save error'
                ];
            }

            //  添加附表amount_detail(租金信息)
            $this->saveAmountDetailData($params['amount_detail'], $contractId, ['contract_begin' => $params['contract_begin'], 'contract_end' => $params['contract_end']]);

            //新增 区域相关信息 一对多关系（原来是一对一） 直接删除 然后插入
            $this->saveCommonAreaDetailData($params['areaInfo'], $contractId);

            // 处理租房合同附件
            $this->saveCommonAttachmentFiles($params['attachment_list'], $model);

            $contract = $model->toArray();

            //重新创建审批流
            $flow_bool = $this->recommitWorkFlow($contract, $user, Enums::WF_STORE_RENTING_CONTRACT_TYPE);

            if ($flow_bool === false || isset($flow_bool['error_msg'])) {
                throw new BusinessException('租房合同重新提交审批流创建失败,合同号' . $model->contract_id . 'user_id:' . $user['id']);
            }

            $db->commit();

        } catch (ValidationException $e) {
            $code    = $e->getCode();
            $message = $e->getMessage();
        } catch (BusinessException $e) {
            // 业务错误（数据错误等），不可对外抛出
            $code         = $e->getCode();
            $message      = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        } catch (Exception $e) {
            // 业务错误（数据错误等），不可对外抛出
            $code         = $e->getCode();
            $message      = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        }

        if (!empty($message)) {
            $db->rollback();
        }

        if (!empty($real_message)) {
            $this->logger->warning('contract-store-renting-recommit-failed:' . $real_message);

        }

        return [
            'code'    => $code,
            'message' => $message
        ];

    }

    /**
     * 获取抄送的摘要
     * @param $biz_value
     * @return array
     */
    public function getSummary($biz_value)
    {
        $summary = [];
        //查询详情
        $contract = ContractStoreRentingModel::findFirst([
            'conditions' => 'id = :id:',
            'bind'       => ['id' => $biz_value]
        ]);
        if (empty($contract)) {
            return $summary;
        }

        GlobalEnums::init();
        $currency_symbol_item = GlobalEnums::$currency_symbol_map;

        //仓库id
        $summary['cc_summary_contract_rent_warehouse_id'] = !empty($contract->warehouse_id) ? $contract->warehouse_id : '--';

        //网点名称
        $store_info = (new StoreRepository)->getStoreDetail($contract->store_id, '');
        $summary['cc_summary_contract_rent_store_name'] = $store_info['name'] ?? '';

        //合同总金额（含WHT含VAT）  0显示"0币种",空显示"--币种"
        $contract_total_amount = $contract->contract_total_amount === '' ? '--' : number_format($contract->contract_total_amount, 2);
        $currency_symbol = $currency_symbol_item[$contract->money_symbol] ?? '';
        $summary['cc_summary_contract_rent_total_amount'] = $contract_total_amount . $currency_symbol;

        //合同开始日期 和 结束日期
        $summary['cc_summary_contract_rent_contract_begin'] = !empty($contract->contract_begin) ? $contract->contract_begin : '';
        $summary['cc_summary_contract_rent_contract_end'] = !empty($contract->contract_end) ? $contract->contract_end : '';

        return $summary;
    }

    /**
     * 获取租房合同所有的附件配置
     */
    public function getAttachmentAllConfig()
    {
        static $list = [];
        if (empty($list)) {
            $config_item = EnumsService::getInstance()->getSettingEnvValueMap('rent_contract_attachment_config');
            if (!is_array($config_item)) {
                throw new BusinessException('setting_env.code=rent_contract_attachment_config 配置格式有误, 请检查', ErrCode::$BUSINESS_ERROR);
            }

            foreach ($config_item as $contract_type => $contract_info) {
                foreach ($contract_info['house_owner_type_list'] as $house_owner_type => $house_owner_info) {
                    // 当某个合同类型 和 房东类型下的附件未配置时, 其下的附件列表返回空数组
                    if (empty($house_owner_info['attachment_list'])) {
                        $list[$contract_type][$house_owner_type] = [];
                        continue;
                    }

                    foreach ($house_owner_info['attachment_list'] as $attachment) {
                        $attachment['file_name_label'] = static::$t->_($attachment['file_name_key']);
                        unset($attachment['file_name_key'], $attachment['file_desc']);
                        $list[$contract_type][$house_owner_type][] = $attachment;
                    }
                }
            }
        }

        return $list;
    }

    /**
     * 获取合同需显示的附件配置
     *
     * @param int $contract_type 合同类型
     * @param int $house_owner_type 房东类型
     * @return array
     * @throws BusinessException
     */
    protected function getShowAttachmentConfig(int $contract_type, int $house_owner_type)
    {
        $config = $this->getAttachmentAllConfig();
        $curr_contract_file_list = $config[$contract_type][$house_owner_type] ?? [];
        $curr_contract_file_list = array_map(function($v) {
            if ($v['is_show'] == true) {
                return $v;
            }

        }, $curr_contract_file_list);

        return array_filter(array_column($curr_contract_file_list, null, 'field_name'));
    }

    /**
     * 获取仓库关联的租房合同列表
     *
     * @param string $warehouse_id
     * @return array
     */
    public function getRelatedListByWarehouseId(string $warehouse_id)
    {
        if (empty($warehouse_id)) {
            return [];
        }

        // 获取关联的租房合同
        $rent_contract_list = ContractStoreRentingModel::find([
            'conditions' => 'warehouse_id = :warehouse_id:',
            'bind' => ['warehouse_id' => $warehouse_id],
            'columns' => [
                'contract_id AS contract_no',
                'is_main AS contract_type',
                'contract_begin AS contract_begin_date',
                'contract_end AS contract_end_date',
                'contract_status',
            ],
            'order' => 'id DESC'
        ])->toArray();
        if (empty($rent_contract_list)) {
            return [];
        }
        $rent_contract_nos = array_column($rent_contract_list, 'contract_no');

        // 是否归档
        $archive_list = ContractArchive::find([
            'conditions' => 'cno IN ({cno:array}) AND contract_type = :contract_type:',
            'bind' => ['cno' => $rent_contract_nos, 'contract_type' => ContractEnums::CONTRACT_TYPE_STORING],
            'columns' => ['status AS archive_status', 'terminal_at', 'cno']
        ])->toArray();
        $archive_list = array_column($archive_list, null, 'cno');

        foreach ($rent_contract_list as &$value) {
            $archive_info = $archive_list[$value['contract_no']] ?? [];
            if (!empty($archive_info)) {
                if (in_array($archive_info['archive_status'], [ContractEnums::CONTRACT_ARCHIVE_STATUS_TERMINAL, ContractEnums::CONTRACT_ARCHIVE_STATUS_TERMINAL_ING])) {
                    $value['contract_end_date'] = $archive_info['terminal_at'];
                }

                $value['contract_status_label'] = static::$t->_(ContractEnums::$contract_archive_status[$archive_info['archive_status']]);
                $value['archive_status'] = $archive_info['archive_status'];
            } else {
                $value['contract_status_label'] = static::$t->_(Enums::$contract_status[$value['contract_status']]);
                $value['archive_status'] = '0';
            }

            $value['contract_type_label'] = static::$t->_(Enums::$contract_is_master[$value['contract_type']]);
        }

        return $rent_contract_list;
    }

    /**
     *
     * @param array $params
     *
     * @return mixed
     *
     */
    public function terminalCheck(array $params)
    {
        // 当前合同终止日期 是否 存在未支付的房租
        $is_have_unpaid_rent = true;

        $contract_no = $params['cno'];
        $terminal_date = date('Y-m-d', strtotime($params['terminal_date'] . '-1 day'));

        // 房租的费用类型枚举
        $rent_cost_type = 1;

        $columns = [
            'detail.cost_start_date',
            'detail.cost_end_date',
        ];

        $builder = $this->modelsManager->createBuilder();
        $builder->from(['main' => PaymentStoreRenting::class]);
        $builder->leftjoin(PaymentStoreRentingDetail::class, 'detail.store_renting_id = main.id', 'detail');
        $builder->where('detail.contract_no = :contract_no:', ['contract_no' => $contract_no]);
        $builder->andWhere('detail.cost_type = :cost_type:', ['cost_type' => $rent_cost_type]);
        $builder->andWhere('(main.pay_status = :paid_status:) OR (main.approval_status = :approved_status: AND main.pay_status = :to_be_paid:)', [
            'paid_status' => Enums::PAYMENT_PAY_STATUS_PAY,
            'approved_status' => Enums::PAYMENT_APPLY_STATUS_APPROVAL,
            'to_be_paid' => Enums::PAYMENT_PAY_STATUS_PENDING,
        ]);

        $builder->columns($columns);
        $items = $builder->getQuery()->execute()->toArray();

        foreach ($items as $val) {
            if ($terminal_date >= $val['cost_start_date'] && $terminal_date <= $val['cost_end_date']) {
                $is_have_unpaid_rent = false;
                break;
            }
        }

        return [
            'is_have_unpaid_rent' => $is_have_unpaid_rent
        ];
    }

    /**
     * 
     * 获取租房合同关联的租房付款明细列表
     *
     * @param array $condition
     * @return array
     */
    public function getRelatedPaymentDetailList(array $condition)
    {
        $page_size = empty($condition['pageSize']) ? GlobalEnums::DEFAULT_PAGE_SIZE : $condition['pageSize'];
        $page_num = empty($condition['pageNum']) ? GlobalEnums::DEFAULT_PAGE_NUM : $condition['pageNum'];
        $offset = $page_size * ($page_num - 1);

        $data = [
            'items' => [],
            'pagination' => [
                'current_page' => (int)$page_num,
                'per_page' => (int)$page_size,
                'total_count' => 0,
            ],
        ];

        $builder = $this->modelsManager->createBuilder();
        $builder->from(['detail' => PaymentStoreRentingDetail::class]);
        $builder->leftjoin(PaymentStoreRenting::class, 'detail.store_renting_id = main.id', 'main');
        $builder->where('detail.contract_no = :contract_no:', ['contract_no' => $condition['contract_no']]);
        $builder->andWhere('main.pay_status = :pay_status:', ['pay_status' => Enums::PAYMENT_PAY_STATUS_PAY]);
        $count = (int)$builder->columns('COUNT(detail.id) AS total')->getQuery()->getSingleResult()->total;

        $data['pagination']['total_count'] = $count;
        if ($count) {
            $builder->columns([
                'main.id',
                'main.apply_no',
                'main.create_id',
                'main.create_name',
                'main.create_node_department_name',
                'main.currency',
                'main.pay_status',
                'detail.store_name',
                'detail.bank_account_name',
                'detail.cost_start_date',
                'detail.cost_end_date',
                'detail.due_date',
                'detail.amount', // 不含税金额
                'detail.vat_amount',
                'detail.wht_amount',
                'detail.actually_amount',
            ]);
            $builder->orderBy('detail.cost_start_date ASC');

            if (!isset($condition['is_export']) || $condition['is_export'] != true) {
                $builder->limit($page_size, $offset);
            }

            $items = $builder->getQuery()->execute()->toArray();

            foreach ($items as &$value) {
                // 支付状态
                $value['pay_status'] = static::$t->_(Enums::$payment_pay_status[$value['pay_status'] ?? ''] ?? '') ?? '';

                // 币种
                $value['currency'] = static::$t->_(GlobalEnums::$currency_item[$value['currency']]);

                // 不含税金额
                $value['no_wht_amount'] = bcsub($value['amount'], $value['wht_amount'], 2);
            }

            $data['items'] = $items;
        }

        return $data;

    }

    /**
     * 导出租房合同关联的租房付款明细列表
     *
     * @param array $condition
     * @return array
     * @throws BusinessException
     * @throws GuzzleException
     */
    public function exportRelatedPaymentDetailList(array $condition)
    {
        $condition['is_export'] = true;
        $items = $this->getRelatedPaymentDetailList($condition)['items'] ?? [];

        // 导出
        $list = [];
        foreach ($items as $val) {
            $list[] = [
                $val['apply_no'],
                $val['create_id'],
                $val['create_name'],
                $val['create_node_department_name'],
                $val['store_name'],
                $val['bank_account_name'],
                $val['cost_start_date'],
                $val['cost_end_date'],
                $val['due_date'],
                $val['currency'],
                $val['no_wht_amount'],
                $val['vat_amount'],
                $val['wht_amount'],
                $val['actually_amount'],
                $val['pay_status'],
            ];
        }

        $header = [
            static::$t->_('payment_detail_export_001'),
            static::$t->_('payment_detail_export_002'),
            static::$t->_('payment_detail_export_003'),
            static::$t->_('payment_detail_export_004'),
            static::$t->_('payment_detail_export_005'),
            static::$t->_('payment_detail_export_006'),
            static::$t->_('payment_detail_export_007'),
            static::$t->_('payment_detail_export_008'),
            static::$t->_('payment_detail_export_009'),
            static::$t->_('payment_detail_export_010'),
            static::$t->_('payment_detail_export_011'),
            static::$t->_('payment_detail_export_012', ['VAT_SST' => EnumsService::getInstance()->getVatSStRateName()]),
            static::$t->_('payment_detail_export_013'),
            static::$t->_('payment_detail_export_014'),
            static::$t->_('payment_detail_export_015'),
        ];

        $file_name = 'payment_detail_' . date('YmdHis') . '.xlsx';
        return $this->exportExcel($header, $list, $file_name)['data'] ?? '';
    }

    /**
     * 月每平方米租金（含WHT含VAT）
     * 根据月租金/房屋实际面积
     * 1. 用合同总金额（含WHT含VAT）/合同有效期（月）/房屋实际面积
     * 2. 其中合同有效期计算月计算逻辑：合同开始日期，合同结束日期
     *      1. 实际月数 = (首月数 + 中间月数 + 尾月数)四舍五入保留2位小数
     *      2. 中间月数: (结束年 - 开始年) * 12 + (结束月 - 开始月)  - 1
     *      3. 首月: ((首月月末日期 - 开始日期) + 1) / 首月天数 -中间过程不处理小数
     *      4. 尾月: ((结束日期 - 尾月月初日期) + 1)）/ 尾月天数-中间过程不处理小数
     * @param array $params 参数组
     * @return void
     */
    public function calculateMonthRentHasWht(array $params)
    {
        //合同总金额（含WHT含VAT）
        $contract_total_amount = $params['contract_total_amount'] ?? 0;
        //房屋实际面积
        $house_actual_area = $params['house_actual_area'] ?? 0;
        //合同开始日期
        $contract_begin = $params['contract_begin'] ?? '';
        //合同结束日期
        $contract_end = $params['contract_end'] ?? '';
        if (empty($contract_begin) || empty($contract_end) || empty($house_actual_area) || empty($contract_total_amount)) {
            return 0;
        }
        $month = cal_total_months($contract_begin, $contract_end);
        return sprintf('%.2f', $contract_total_amount / $month / $house_actual_area);
    }
}
