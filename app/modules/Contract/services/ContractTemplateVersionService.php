<?php
/**
 * Created by PhpStorm.
 * Date: 2023/7/13
 * Time: 16:27
 */

namespace App\Modules\Contract\Services;

use App\Library\Enums\ContractEnums;
use App\Library\Enums\GlobalEnums;
use App\Library\ErrCode;
use App\Library\Exception\BusinessException;
use App\Library\OssHelper;
use App\Library\Validation\ValidationException;
use App\Models\oa\ContractSubFileModel;
use App\Models\oa\ContractTemplateVersionModel;
use App\Models\oa\ContractTemplateBaseModel;

use App\Modules\Common\Services\CommonService;
use App\Modules\Common\Services\ContractService;
use App\Modules\Common\Services\EnumsService;
use App\Modules\User\Models\DepartmentModel;
use App\Repository\DepartmentRepository;
use App\Util\RedisKey;
use GuzzleHttp\Exception\GuzzleException;


class ContractTemplateVersionService extends BaseService
{

    private static $instance;

    public function __construct()
    {
    }

    private function __clone()
    {
    }

    public static function getInstance()
    {
        if (!isset(self::$instance) || !(self::$instance instanceof self)) {
            self::$instance = new self;
        }
        return self::$instance;
    }

    public static $validate_edit = [
        'id'                        => 'Required|IntGe:1',
        'department_id'             => 'Required|StrLenGeLe:1,200',
        'template_activation_time'  => 'Required|Date',                  //生效开始时间
        'template_downtime'         => 'Required|Date',                    //生效结束时间
        'subfile_info'              => 'Required|Arr|ArrLenGeLe:1,10',
        'subfile_info[*]'           => 'Required|Obj',
        'subfile_info[*].unique_no' => 'Required|StrLenGeLe:1,32',
        'subfile_info[*].file_name' => 'Required|StrLenGeLe:1,200',
        'subfile_info[*].file_type' => 'Required|Int'
    ];

    public static $validate_add = [
        'relate_id'                 => 'Required|IntGe:1',
        'contract_type'             => 'Required|Int', //适用合同类型
        'contract_name'             => 'Required|StrLenGeLe:1,100',
        'lang'                      => 'Required|StrLenGeLe:1,10',
        'ver'                       => 'Required|StrLenGeLe:1,100',
        'department_id'             => 'Required|StrLenGeLe:1,200',
        'template_activation_time'  => 'Required|Date',                  //生效开始时间
        'template_downtime'         => 'Required|Date',                    //生效结束时间
        'subfile_info'              => 'Required|Arr|ArrLenGeLe:1,10',
        'subfile_info[*]'           => 'Required|Obj',
        'subfile_info[*].unique_no' => 'Required|StrLenGeLe:1,32',
        'subfile_info[*].file_name' => 'Required|StrLenGeLe:1,200',
        'subfile_info[*].file_type' => 'Required|Int'
    ];


    public function add($data, $user)
    {
        $code    = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $db      = $this->getDI()->get('db_oa');
        $db->begin();
        try {
            //查询版本号是否重复
            $exists = ContractTemplateVersionModel::findFirst([
                'conditions' => 'ver = :ver:',
                'bind'       => ['ver' => $data['ver']],
                'columns'    => ['id']
            ]);

            if (!empty($exists)) {
                throw new ValidationException(static::$t->_('contract_template_ver_num_repeat'), ErrCode::$VALIDATE_ERROR);
            }

            //校验同一个模版分类且语言相同 使用时间不可重叠
            $template_data = ContractTemplateVersionModel::find([
                'conditions' => 'relate_id = :relate_id: and lang = :lang:',
                'bind'       => ['relate_id' => $data['relate_id'], 'lang' => $data['lang']],
            ])->toArray();

            $template_activation_time = empty($data['template_activation_time']) ? '' : $data['template_activation_time'] . ' 00:00:01';
            $template_downtime        = empty($data['template_downtime']) ? '' : $data['template_downtime'] . ' 23:59:59';
            if (!empty($template_data)) {
                foreach ($template_data as $item) {
                    if (!empty($template_activation_time) && !($item['template_activation_time'] >= $template_downtime || $item['template_downtime'] <= $template_activation_time)) {
                        //时间不可重叠
                        throw new ValidationException(static::$t->_('contract_template_ver_time_repeat'), ErrCode::$VALIDATE_ERROR);
                    }
                }
            }

            $state = ContractEnums::CONTRACT_TEMPLATE_STATE_1;
            if (date('Y-m-d H:i:s') > $template_downtime) {
                $state = ContractEnums::CONTRACT_TEMPLATE_STATE_3;
            }

            if (date('Y-m-d H:i:s') < $template_activation_time) {
                $state = ContractEnums::CONTRACT_TEMPLATE_STATE_1;
            }

            if (date('Y-m-d H:i:s') < $template_downtime && date('Y-m-d H:i:s') > $template_activation_time) {
                $state = ContractEnums::CONTRACT_TEMPLATE_STATE_2;
            }

            // 合同模板版本处理
            if ($this->isFlashHomeOperationV2($data['department_id'])) {
                // FH 合同模板版本处理
                $template_data = $this->handleFhTemplateData($data);
            } else {
                if (isCountry('MY')) {
                    //马来字段和泰国差异太大单独处理
                    $service = reBuildCountryInstance(new ContractTemplateVersionService());
                    $template_data = $service->handleTemplateData($data);
                } else {
                    // PMD / Retail / FFM 合同模板版本处理
                    $template_data = $this->handleTemplateData($data); //模板版本新增
                }
            }

            // 获取关联模板的额外字段
            $base_info = ContractTemplateBaseModel::findFirst([
                'conditions' => 'id = :id:',
                'bind'       => ['id' => $data['relate_id']],
                'columns'    => ['is_combination_supplemental_agreement', 'is_show_return_person_signature']
            ]);

            $insert_data = [
                'template_no'              => BaseService::genSerialNo('GX', RedisKey::CONTRACT_TEMPLATE_VERSION_COUNTER),
                'relate_id'                => $data['relate_id'],
                'contract_type'            => $data['contract_type'],
                'contract_name'            => $data['contract_name'],
                'lang'                     => $data['lang'],
                'ver'                      => $data['ver'],
                'state'                    => $state,
                'business_review'          => $data['business_review'] ?? ContractEnums::BUSINESS_REVIEW_NO,
                'template_activation_time' => $template_activation_time,
                'template_downtime'        => $template_downtime,
                'department_id'            => $data['department_id'],
                'is_combination_supplemental_agreement' => $base_info->is_combination_supplemental_agreement ?? 0,
                'subfile_info'             => json_encode($data['subfile_info'], JSON_UNESCAPED_UNICODE),
                'form_rule'                => $template_data['form_rule'],
                'file_url'                 => $template_data['file_url'],
                'pdf_url'                  => $template_data['pdf_url'],
                'created_at'               => date('Y-m-d H:i:s'),
                'updated_at'               => date('Y-m-d H:i:s'),
                'created_id'               => $user['id'],
                'updated_id'               => $user['id'],
                'is_show_return_person_signature' => $base_info->is_show_return_person_signature ?? 0,
            ];

            $model = new ContractTemplateVersionModel();
            if ($model->i_create($insert_data) === false) {
                throw new BusinessException('模版合同版本创建失败, 原因可能是: ' . get_data_object_error_msg($model) . '; 数据: ' . json_encode($insert_data, JSON_UNESCAPED_UNICODE), ErrCode::$CONTRACT_CREATE_ERROR);
            }

            $db->commit();
        } catch (ValidationException $e) {
            // 业务错误（数据错误等），不可对外抛出
            $code    = $e->getCode();
            $message = $e->getMessage();
        } catch (BusinessException $e) {
            // 业务错误（数据错误等），不可对外抛出
            $code         = $e->getCode();
            $message      = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        } catch (\Exception $e) {
            // 业务错误（数据错误等），不可对外抛出
            $code         = $e->getCode();
            $message      = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        }

        if (!empty($message)) {
            $db->rollback();
        }

        if (!empty($real_message)) {
            $this->logger->warning('contract-version-add-failed:' . $real_message);
        }

        return [
            'code'    => $code,
            'message' => $message
        ];
    }

    public function edit($data, $user)
    {
        $code    = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $db      = $this->getDI()->get('db_oa');
        $db->begin();
        try {
            $exists = ContractTemplateVersionModel::findFirst([
                'conditions' => 'id = :id:',
                'bind'       => ['id' => $data['id']],
            ]);

            // 模版不存在
            if (empty($exists)) {
                throw new ValidationException(static::$t->_('contract_template_version_get_fail'), ErrCode::$VALIDATE_ERROR);
            }

            //校验同一个模版分类且语言相同 使用时间不可重叠
            $template_data = ContractTemplateVersionModel::find([
                'conditions' => 'relate_id = :relate_id: and lang = :lang:',
                'bind'       => ['relate_id' => $data['relate_id'], 'lang' => $data['lang']],
            ])->toArray();

            $template_activation_time = empty($data['template_activation_time']) ? '' : $data['template_activation_time'] . ' 00:00:01';
            $template_downtime        = empty($data['template_downtime']) ? '' : $data['template_downtime'] . ' 23:59:59';

            if (!empty($template_data)) {
                foreach ($template_data as $item) {
                    if ($item['id'] == $data['id']) {
                        continue;
                    }
                    if (!empty($template_activation_time) && !($item['template_activation_time'] >= $template_downtime || $item['template_downtime'] <= $template_activation_time)) {
                        //时间不可重叠
                        throw new ValidationException(static::$t->_('contract_template_ver_time_repeat'), ErrCode::$VALIDATE_ERROR);
                    }
                }
            }

            $state = ContractEnums::CONTRACT_TEMPLATE_STATE_1;
            if (date('Y-m-d H:i:s') > $template_downtime) {
                $state = ContractEnums::CONTRACT_TEMPLATE_STATE_3;
            }

            if (date('Y-m-d H:i:s') < $template_activation_time) {
                $state = ContractEnums::CONTRACT_TEMPLATE_STATE_1;
            }

            if (date('Y-m-d H:i:s') < $template_downtime && date('Y-m-d H:i:s') > $template_activation_time) {
                $state = ContractEnums::CONTRACT_TEMPLATE_STATE_2;
            }

            // 校验新提交的子文件版本是否正确
            $new_sub_files = array_column($data['subfile_info'], 'file_type', 'unique_no');

            // 编辑前的子文件
            $sub_files = json_decode($exists->subfile_info, true);
            foreach ($sub_files as $sub_file_info) {
                // 同一个子文件的文件类型有过变更, 不可提交
                if (isset($new_sub_files[$sub_file_info['unique_no']]) && $new_sub_files[$sub_file_info['unique_no']] != $sub_file_info['file_type']) {
                    throw new ValidationException(static::$t->_('contract_template_sub_file_type_wrong，' . $sub_file_info['fie_name']), ErrCode::$VALIDATE_ERROR);
                }
            }

            // 模板处理
            if ($this->isFlashHomeOperationV2($data['department_id'])) {
                $template_data = $this->handleFhTemplateData($data);
            } else {
                if (isCountry('MY')) {
                    $service = reBuildCountryInstance(new ContractTemplateVersionService());
                    $template_data = $service->handleTemplateData($data);
                } else {
                    $template_data = $this->handleTemplateData($data); //模板版本编辑
                }
            }

            // 获取关联模板的额外字段
            $base_info = ContractTemplateBaseModel::findFirst([
                'conditions' => 'id = :id:',
                'bind'       => ['id' => $exists->relate_id],
                'columns'    => ['is_combination_supplemental_agreement', 'is_show_return_person_signature'],
            ]);

            $update_data   = [
                'template_activation_time' => $template_activation_time,
                'template_downtime'        => $template_downtime,
                'state'                    => $state,
                'department_id'            => $data['department_id'],
                'is_combination_supplemental_agreement' => $base_info->is_combination_supplemental_agreement ?? 0,
                'subfile_info'             => json_encode($data['subfile_info'], JSON_UNESCAPED_UNICODE),
                'form_rule'                => $template_data['form_rule'],
                'file_url'                 => $template_data['file_url'],
                'pdf_url'                  => $template_data['pdf_url'],
                'updated_at'               => date('Y-m-d H:i:s'),
                'updated_id'               => $user['id'],
                'is_show_return_person_signature' => $base_info->is_show_return_person_signature ?? 0,
            ];
            if ($exists->save($update_data) === false) {
                throw new BusinessException('合同版本模版修改失败, 原因可能是: ' . get_data_object_error_msg($exists) . '; 数据: ' . json_encode($update_data, JSON_UNESCAPED_UNICODE), ErrCode::$CONTRACT_CREATE_ERROR);
            }

            $db->commit();
        } catch (ValidationException $e) {
            // 校验错误，可对外抛出
            $code    = $e->getCode();
            $message = $e->getMessage();

        } catch (BusinessException $e) {
            // 业务错误（数据错误等），不可对外抛出
            $code         = $e->getCode();
            $message      = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        } catch (\Exception $e) {
            // 业务错误（数据错误等），不可对外抛出
            $code         = $e->getCode();
            $message      = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        }

        if (!empty($message)) {
            $db->rollback();
        }

        if (!empty($real_message)) {
            $this->logger->warning('contract-version-update-failed:' . $real_message);
        }

        return [
            'code'    => $code,
            'message' => $message
        ];
    }

    /**
     * @param $condition
     * @param string $from tpl_list-来自模板版本列表, use_list-来自制作列表
     * @return array
     */
    public function getList($condition, string $from = 'tpl_list')
    {
        $department_id = $condition['department_id'] ?? [];
        $department_id = array_filter($department_id);

        $relate_id  = $condition['relate_id'] ?? '';
        $state      = $condition['state'] ?? 0;
        $start_time = $condition['template_activation_time'] ?? '';
        $end_time   = $condition['template_downtime'] ?? '';

        $page_size = empty($condition['pageSize']) ? GlobalEnums::DEFAULT_PAGE_SIZE : $condition['pageSize'];
        $page_num  = empty($condition['pageNum']) ? GlobalEnums::DEFAULT_PAGE_NUM : $condition['pageNum'];
        $offset    = $page_size * ($page_num - 1);
        $builder   = $this->modelsManager->createBuilder();
        $builder->from(['c' => ContractTemplateVersionModel::class]);
        $builder->leftjoin(ContractTemplateBaseModel::class, 'c.relate_id = base.id', 'base');

        if (!empty($department_id)) {
            $builder->inWhere('c.department_id', $department_id);
        }

        if (!empty($relate_id)) {
            $builder->andWhere('c.relate_id = :relate_id:', ['relate_id' => $relate_id]);
        }

        if (!empty($condition['lang'])) {
            $builder->andWhere('c.lang = :lang:', ['lang' => $condition['lang']]);
        }

        if (!empty($state)) {
            $builder->andWhere('c.state = :state:', ['state' => $state]);
        }

        if (!empty($start_time)) {
            $builder->andWhere('c.template_activation_time <= :template_activation_time:', ['template_activation_time' => $start_time . '00:00:01']);
        }

        if (!empty($end_time)) {
            $builder->andWhere('c.template_downtime >= :template_downtime:', ['template_downtime' => $end_time . '23:59:59']);
        }

        $count = (int)$builder->columns('COUNT(c.id) AS total')->getQuery()->getSingleResult()->total;
        if ($count > 0) {
            // 使用中的版本所关联的合同模板
            $base_name_field_name = get_lang_field_name('template_name_', static::$language, 'th', 'zh');

            $columns = [
                'c.id',
                'c.template_no',
                'c.relate_id',
                'c.contract_type',
                'c.lang',
                'c.business_review',
                'c.ver',
                'c.template_activation_time',
                'c.template_downtime',
                'c.department_id',
                'c.state',
                'c.pdf_url',
                'c.file_url',
                "base.{$base_name_field_name} AS contract_name",
            ];

            $builder->columns($columns);
            $builder->limit($page_size, $offset);

            if ($from == 'tpl_list') {
                $builder->orderBy('c.template_no DESC');
            } else {
                $builder->orderBy('contract_name ASC');
            }

            $items = $builder->getQuery()->execute()->toArray();
            $items = $this->handelItems($items);
        }

        return [
            'items'      => $items ?? [],
            'pagination' => [
                'current_page' => $page_num,
                'per_page'     => $page_size,
                'total_count'  => $count,
            ]
        ];
    }

    public function getDetail($params, $is_form_rule = true)
    {
        $code    = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $id      = $params['id'];

        try {
            $contract = ContractTemplateVersionModel::findFirst([
                'conditions' => 'id = :id:',
                'bind'       => ['id' => $id]
            ]);
            if (empty($contract)) {
                throw new ValidationException(static::$t->_('contract_template_version_get_fail'), ErrCode::$CONTRACT_GET_INFO_ERROR);
            }

            $contract = $contract->toArray();

            $contract_type_enums      = ($this->getEnums())['lang'] ?? [];
            $contract_lang            = array_column($contract_type_enums, 'name', 'id');
            $contract['lang_text']    = $contract_lang[$contract['lang']] ?? '';
            $contract['form_rule']    = json_decode($contract['form_rule'], true);
            $contract['subfile_info'] = json_decode($contract['subfile_info'], true);
            $contract['pdf_url']      = gen_file_url(['object_key' => $contract['pdf_url']]);
            if (!$is_form_rule) {
                unset($contract['form_rule']);
            }

        } catch (ValidationException $e) {
            $code    = $e->getCode();
            $message = $e->getMessage();
        }

        return [
            'code'    => $code,
            'message' => $message,
            'data'    => $contract ?? []
        ];
    }


    public function handelItems($items)
    {
        $contract_type_enums = $this->getEnums();

        $department_info = array_column($contract_type_enums['use_department'], 'name', 'id');

        $contract_template_state = ContractEnums::$contract_template_state;

        foreach ($items as &$item) {
            $item['department_name'] = $department_info[$item['department_id']] ?? '';
            $item['state_text']      = static::$t->_($contract_template_state[$item['state']]);
            $path = !empty($item['pdf_url']) ? OssHelper::downloadFileHcm($item['pdf_url'], 600) : '';
            $item['pdf_url']         = $path['file_url'] ?? '';

            unset($item['form_rule']);
            unset($item['subfile_info']);

        }
        return $items;
    }

    /**
     * 处理PMD模版表单字段
     * 和文件内容
     *
     * @param $data
     * @return array
     * @throws BusinessException
     * @throws GuzzleException
     */
    public function handleTemplateData($data)
    {
        //查询子文件信息
        $unique_no = array_column($data['subfile_info'], 'unique_no');
        $sub_file_info = ContractSubFileModel::find([
            'conditions' => 'unique_no in ({unique_no:array})',
            'bind'       => ['unique_no' => $unique_no]
        ])->toArray();
        if (empty($sub_file_info)) {
            throw new BusinessException('模版合同版本创建子文件信息不存在 ' . json_encode($data, JSON_UNESCAPED_UNICODE), ErrCode::$CONTRACT_CREATE_ERROR);
        }

        $sub_file_info = array_column($sub_file_info, null, 'unique_no');
        $sub_file_infos = [];
        foreach ($unique_no as $k => $v) {
            $sub_file_infos[] = $sub_file_info[$v];
        }

        $field_arr         = [];
        $base_key          = [];
        $template_form_arr = [];

        // 拼接html模版
        $html_config = $this->getElectronicContractHtmlConfig($data['department_id']); //子文件更新
        $template_html = $html_config['header_html'];

        //子文件数量 最后一个子文件不需要分页
        $file_count = count($sub_file_infos);
        foreach ($sub_file_infos as $key => $item) {
            //拼接html模版
            if ($file_count - 1 == $key) {//最后一个子文件不需要分页
                $template_html .= $item['contract_content'];
            } else {
                $template_html .= $item['contract_content'] . '<div style="page-break-before: always"></div>';
            }

            // 相关子文件基础信息和其下的表单字段名提炼
            if (!empty($item['file_form_rule'])) {
                foreach (json_decode($item['file_form_rule'], true) as $k => $v) {
                    //每个子文件表单规则 提取 基础信息key
                    $base_key[] = $k;
                    foreach ($v as $v_2) {
                        //每个基础key 下字段放到一起
                        $field_arr[] = $v_2;
                    }
                }
            }
        }

        $template_html .= $html_config['footer_html'];

        //$field_arr是所有变量字段 $template_html是模版头+子文件+尾部 $base_key是按照模块的key

        $file_name = 'template_version_' . $data['ver'] . '.ftl';

        //模版上传到oss
        $data['file_url'] = $this->createFtlFile($template_html, $file_name);

        //字段 和基础key 分别去重
        $base_key = array_unique($base_key);

        // 查询基础的表单验证规则
        $res             = $html_config['base_form'];
        $base_field_lang = $html_config['base_form_rule_lang'];

        // 组装子文件拼成的表单[表单数据单元]
        foreach ($base_key as $value) {
            $template_form_arr[$value] = $res[$value];
        }

        // 合同模板的表单规则配置: 根据表单项字段取对应的配置规则
        $lang = explode('-', $data['lang']);
        foreach ($template_form_arr as $k1 => &$value) {

            foreach ($value as $k => &$v) {

                // 提取合同模板表单表字段在全局表单字段配置中的规则(仅处理模板中的表单字段与全局表单的交集字段)
                if (!in_array($v['field'], $field_arr)) {
                    unset($value[$k]);
                }

                // 多语言字段的映射处理
                // 公司名称
                if ($v['field'] == 'custom_company_name_th') {
                    $v = $base_field_lang['custom_company_name_' . $data['lang']][1];
                }

                if ($v['field'] == 'custom_company_name_en') {
                    $v = $base_field_lang['custom_company_name_' . $data['lang']][0];
                }

                // 公司法人
                if ($v['field'] == 'custom_legal_name_th') {
                    $v = $base_field_lang['custom_legal_name_' . $data['lang']][0];
                }

                if ($v['field'] == 'custom_legal_name_en') {
                    $v = $base_field_lang['custom_legal_name_' . $data['lang']][1];
                }

                // 兼容v1版本结构的授权人配置(旧版本编辑)
                if ($v['field'] == 'custom_authorized_person_th') {
                    $v = $base_field_lang['custom_authorized_person_' . $data['lang']][1];
                }

                if ($v['field'] == 'custom_authorized_person_en') {
                    $v = $base_field_lang['custom_authorized_person_' . $data['lang']][0];
                }

                // 甲方授权人信息: 多组
                if ($v['field'] == 'custom_authorized_person_info') {
                    foreach ($v['detail'][0] as $detail_k => &$detail_v) {
                        if ($detail_v['field'] == 'custom_authorized_person_en') {
                            $detail_v = $base_field_lang['custom_authorized_person_' . $data['lang']][0];
                        }

                        if ($detail_v['field'] == 'custom_authorized_person_th') {
                            $detail_v = $base_field_lang['custom_authorized_person_' . $data['lang']][1];
                        }
                    }
                }

                // 联系人
                if ($v['field'] == 'custom_contacts_name_th') {
                    $v = $base_field_lang['custom_contacts_name_' . $data['lang']][0];
                }

                if ($v['field'] == 'custom_contacts_name_en') {
                    $v = $base_field_lang['custom_contacts_name_' . $data['lang']][1];
                }

                //地址
                if ($v['field'] == 'custom_address_th') {
                    $v = $base_field_lang['custom_address_' . $data['lang']][0];
                }

                if ($v['field'] == 'custom_address_en') {
                    $v = $base_field_lang['custom_address_' . $data['lang']][1];
                }

                // 联系人
                if ($v['field'] == 'flash_contact_name_th') {
                    $v['field'] = 'flash_contact_name_' . $lang[1];
                    $v['lang']  = $lang[1];
                }

                if ($v['field'] == 'flash_contact_name_en') {
                    $v['field'] = 'flash_contact_name_' . $lang[0];
                    $v['lang']  = $lang[0];
                }

                // 销售代表
                if ($v['field'] == 'flash_sale_name_th') {
                    $v['field'] = 'flash_sale_name_' . $lang[1];
                    $v['lang']  = $lang[1];
                }

                if ($v['field'] == 'flash_sale_name_en') {
                    $v['field'] = 'flash_sale_name_' . $lang[0];
                    $v['lang']  = $lang[0];
                }

                // pdpa_custom_name
                // 客户名称
                if ($v['field'] == 'pdpa_custom_name_th') {
                    $v['field'] = 'pdpa_custom_name_' . $lang[1];
                    $v['lang']  = $lang[1];
                }

                if ($v['field'] == 'pdpa_custom_name_en') {
                    $v['field'] = 'pdpa_custom_name_' . $lang[0];
                    $v['lang']  = $lang[0];
                }

                // pdpa_custom_address
                // 客户地址
                if ($v['field'] == 'pdpa_custom_address_th') {
                    $v['field'] = 'pdpa_custom_address_' . $lang[1];
                    $v['lang']  = $lang[1];
                }

                if ($v['field'] == 'pdpa_custom_address_en') {
                    $v['field'] = 'pdpa_custom_address_' . $lang[0];
                    $v['lang']  = $lang[0];
                }

                // 补充协议甲方公司名称
                if ($v['field'] == 'agreement_company_name_th') {
                    $v = $base_field_lang['agreement_company_name_' . $data['lang']][1];
                }

                if ($v['field'] == 'agreement_company_name_en') {
                    $v = $base_field_lang['agreement_company_name_' . $data['lang']][0];
                }

                // 补充协议甲方公司地址
                if ($v['field'] == 'agreement_company_address_th') {
                    $v = $base_field_lang['agreement_company_address_' . $data['lang']][1];
                }

                if ($v['field'] == 'agreement_company_address_en') {
                    $v = $base_field_lang['agreement_company_address_' . $data['lang']][0];
                }

                // 补充协议修改条件内容
                if ($v['field'] == 'agreement_modified_text_th') {
                    $v = $base_field_lang['agreement_modified_text_' . $data['lang']][1];
                }

                if ($v['field'] == 'agreement_modified_text_en') {
                    $v = $base_field_lang['agreement_modified_text_' . $data['lang']][0];
                }
            }

            $template_form_arr[$k1] = array_values($value);
        }

        // 合同模板变量对应的表单项
        $form_data = $this->contractParams($template_form_arr);

        // 合同模板当前版本的pdf样例文件
        $pdf_info = $this->generateElectronicContractPdfFile($data['department_id'], $data['file_url'], $form_data);

        return [
            'form_rule' => json_encode($template_form_arr, JSON_UNESCAPED_UNICODE),
            'file_url'  => $data['file_url'],
            'pdf_url'   => $pdf_info['object_key']
        ];
    }


    /**
     * 处理Flash Home Operation模版表单字段
     * 和文件内容
     *
     * @param $data
     * @return array
     * @throws BusinessException
     * @throws GuzzleException
     */
    public function handleFhTemplateData($data)
    {
        //查询子文件信息
        $unique_no = array_column($data['subfile_info'], 'unique_no');

        $sub_file_info = ContractSubFileModel::find([
            'conditions' => 'unique_no in ({unique_no:array})',
            'bind'       => [
                'unique_no' => $unique_no]
        ])->toArray();
        if (empty($sub_file_info)) {
            throw new BusinessException('模版合同版本创建子文件信息不存在 ' . json_encode($data, JSON_UNESCAPED_UNICODE), ErrCode::$CONTRACT_CREATE_ERROR);
        }

        $sub_file_info = array_column($sub_file_info, null, 'unique_no');

        foreach ($unique_no as $k => $v) {
            $sub_file_infos[] = $sub_file_info[$v];
        }

        $field_arr         = [];
        $base_key          = [];
        $template_form_arr = [];

        //拼接html模版
        $html_config = $this->getElectronicContractHtmlConfig($data['department_id']);
        $template_html = $html_config['header_html'];

        //子文件数量 最后一个子文件不需要分页
        $file_count = count($sub_file_infos);

        foreach ($sub_file_infos as $key => $item) {
            //拼接html模版
            if ($file_count - 1 == $key) {//最后一个子文件不需要分页
                $template_html .= $item['contract_content'];
            } else {
                $template_html .= $item['contract_content'] . '<div style="page-break-before: always"></div>';
            }

            if (!empty($item['file_form_rule'])) {
                foreach (json_decode($item['file_form_rule'], true) as $k => $v) {//每个子文件表单规则 提取 基础信息key
                    $base_key[] = $k;
                    foreach ($v as $v_2) {//每个基础key 下字段放到一起
                        $field_arr[] = $v_2;
                    }
                }
            }
        }

        $template_html .= $html_config['footer_html'];

        $file_name = 'template_version_' . $data['ver'] . '.ftl';
        $data['file_url'] = $this->createFtlFile($template_html, $file_name);

        //字段 和基础key 分别去重
        $base_key = array_unique($base_key);

        //查询基础的表单验证规则
        $res = $html_config['base_form'];

        //组装子文件拼成的表单
        foreach ($base_key as $value) {
            $template_form_arr[$value] = $res[$value];
        }

        foreach ($template_form_arr as $k1 => &$value) {
            foreach ($value as $k => &$v) {
                if (!in_array($v['field'], $field_arr)) {
                    unset($value[$k]);
                }
            }

            $template_form_arr[$k1] = array_values($value);
        }

        $form_data = $this->fhContractParams($template_form_arr);

        // 生成PDF
        $pdf_info = $this->generateElectronicContractPdfFile($data['department_id'], $data['file_url'], $form_data);

        return [
            'form_rule' => json_encode($template_form_arr, JSON_UNESCAPED_UNICODE),
            'file_url'  => $data['file_url'],
            'pdf_url'   => $pdf_info['object_key']
        ];
    }


    /**
     * 合同模版下载
     *
     * @param $id
     * @return array
     */
    public function download($id)
    {
        $code    = ErrCode::$SUCCESS;
        $message = 'success';
        $object_url = '';

        try {
            $template_version = ContractTemplateVersionModel::findFirst([
                'conditions' => 'id =:id:',
                'bind'       => ['id' => $id]
            ]);
            if (empty($template_version)) {
                throw new ValidationException(static::$t->_('contract_template_version_get_fail'), ErrCode::$CONTRACT_GET_INFO_ERROR);
            }

            $object_url = gen_file_url(['object_key' => $template_version->pdf_url]);

        } catch (ValidationException $e) {
            // 业务错误（数据错误等），不可对外抛出
            $code    = $e->getCode();
            $message = $e->getMessage();
        } catch (\Exception $e) {
            // 业务错误（数据错误等），不可对外抛出
            $code         = $e->getCode();
            $message      = static::$t->_('retry_later');
            $this->logger->warning('contract-template-download-detail-failed:' . $e->getMessage());
        }

        return [
            'code'    => $code,
            'message' => $message,
            'data'    => $object_url
        ];
    }


    /**
     * 获取模版基础信息
     * @param $data
     * @return array
     */
    public function templateBaseInfo($data)
    {
        $code    = ErrCode::$SUCCESS;
        $message = $real_message = '';
        try {
            //查询基础模版信息
            $template_base_info = ContractTemplateBaseModel::findFirst([
                'conditions' => 'id = :id:',
                'bind'       => ['id' => $data['id']]
            ]);

            if (empty($template_base_info)) {
                throw new ValidationException(static::$t->_('contract_template_base_get_fail'), ErrCode::$VALIDATE_ERROR);
            }

            $template_base_info = $template_base_info->toArray();
            $sub_file_infos = json_decode($template_base_info['subfile_info'], true);

            $file_types = array_values(array_unique(array_column($sub_file_infos, 'file_type')));

            //查询相同子文件分类(增加使用中的状态过滤 v18933)
            $sub_files = ContractSubFileModel::find([
                'conditions' => 'file_type in ({file_type:array}) AND lang = :lang: AND state = :state:',
                'bind'       => ['file_type' => $file_types, 'lang' => $data['lang'], 'state' => ContractEnums::SUB_FILE_STATUS_1],
                'columns'    => ['ver_no', 'unique_no', 'file_type']
            ])->toArray();

            foreach ($sub_files as $k => $v) {
                foreach ($sub_file_infos as &$item) {
                    if ($v['file_type'] == $item['file_type']) {
                        $item['version_info'][] = $v;
                    }
                }
            }

            foreach ($sub_file_infos as $key => &$value) { //有子文件版本数据去默认第一个
                if (empty($value['version_info'])) {
                    $value['unique_no'] = '';
                    $value['version_info'] = [];
                } else {
                    $value['unique_no'] = $value['version_info'][0]['unique_no'];
                }
            }

        } catch (ValidationException $e) {
            // 校验错误，可对外抛出
            $code    = $e->getCode();
            $message = $e->getMessage();

        } catch (\Exception $e) {
            // 业务错误（数据错误等），不可对外抛出
            $code         = $e->getCode();
            $message      = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        }

        if (!empty($real_message)) {
            $this->logger->warning('contract-template-base-info-failed:' . $real_message);
        }

        return [
            'code'    => $code,
            'message' => $message,
            'data'    => $sub_file_infos ?? []
        ];
    }

    /**
     * 处理合同表单数据
     * @param $form_rule
     * @return array
     */
    public function contractParams($form_rule)
    {
        $field_data = [];

        foreach ($form_rule as $k => $v) {
            $i = 1;

            foreach ($v as $k1 => $v1) {
                if (!in_array($v1['field'], self::$special_field)) {
                    $field_data[$v1['field']] = $v1['value'];

                    // 授权人
                    if ($v1['field'] == 'flash_authorized_person') {
                        $field_data[$v1['field'] . '_' . $i] = $v1['value'];
                        $i++;
                    }

                    // 价格调整及条件
                    if ($v1['field'] == 'price_scheme') {
                        $field_data['price_scheme_description'] = '';
                        $field_data['price_scheme_description_0'] = '';
                        $field_data['price_scheme_description_1'] = '';
                    }

                } else {
                    // 标准件
                    if ($v1['field'] == 'standard_settlement_type') {
                        $field_data['standard_settlement_type_key'] = $v1['value'];
                        if ($v1['value'] == 1) {
                            $field_data['standard_settlement_type_item'] = array_column($v1['condition'][1], 'value', 'field');
                        }

                        if ($v1['value'] == 2) {
                            $field_data['standard_cod_pay_method'] = $v1['condition'][2][0]['value'];
                            foreach ($v1['condition'][2][1]['detail'] as $v2) {
                                $field_data['standard_settlement_type_item'][] = array_column($v2, 'value', 'field');
                            }
                        }
                    }

                    // 大件
                    if ($v1['field'] == 'bulky_item_settlement_type') {
                        $field_data['bulky_item_settlement_type_key'] = $v1['value'];
                        if ($v1['value'] == 1) {
                            $field_data['bulky_item_settlement_type_item'] = array_column($v1['condition'][1], 'value', 'field');
                        }

                        if ($v1['value'] == 2) {
                            $field_data['bulky_item_cod_pay_method'] = $v1['condition'][2][0]['value'];
                            foreach ($v1['condition'][2][1]['detail'] as $v2) {
                                $field_data['bulky_item_settlement_type_item'][] = array_column($v2, 'value', 'field');
                            }
                        }
                    }

                    // 水果件
                    if ($v1['field'] == 'fruit_item_settlement_type') {
                        $field_data['fruit_item_settlement_type_key'] = $v1['value'];
                        if ($v1['value'] == 1) {
                            $field_data['fruit_item_settlement_type_item'] = array_column($v1['condition'][1], 'value', 'field');
                        }

                        if ($v1['value'] == 2) {
                            $field_data['fruit_item_cod_pay_method'] = $v1['condition'][2][0]['value'];
                            foreach ($v1['condition'][2][1]['detail'] as $v2) {
                                $field_data['fruit_item_settlement_type_item'][] = array_column($v2, 'value', 'field');
                            }
                        }
                    }

                    // 授权人信息(多组)
                    if ($v1['field'] == 'custom_authorized_person_info') {
                        foreach ($v1['detail'] as $v2) {
                            $field_data['custom_authorized_person_info'][] = array_column($v2, 'value', 'field');
                        }
                    }
                }
            }
        }

        // 派生字段
        $field_data['settlement_type_1']            = '';
        $field_data['settlement_type_2']            = '';
        $field_data['settlement_circle_1']          = '';
        $field_data['settlement_circle_2']          = '';
        $field_data['standard_item_refund_refer_1'] = '';
        $field_data['standard_item_refund_refer_2'] = '';
        $field_data['bulky_item_refund_refer_1']    = '';
        $field_data['bulky_item_refund_refer_2']    = '';
        $field_data['cod_pay_currency_1']           = '';
        $field_data['cod_pay_currency_2']           = '';
        $field_data['custom_bank_currency_1']       = '';
        $field_data['custom_bank_currency_2']       = '';
        $field_data['flash_bank_currency_1']        = '';
        $field_data['flash_bank_currency_2']        = '';
        $field_data['bulky_item_refund_ratio']      = '';
        $field_data['bulky_item_refund_refer']      = '';
        $field_data['standard_item_refund_ratio']   = '';
        $field_data['pdpa_custom_country_1']        = '';
        $field_data['pdpa_custom_country_2']        = '';
        $field_data['contract_sign_date_0']         = '';
        $field_data['contract_sign_date_1']         = '';
        $field_data['contract_start_date_0']        = '';
        $field_data['contract_start_date_1']        = '';
        $field_data['contract_end_date_0']          = '';
        $field_data['contract_end_date_1']          = '';
        $field_data['pdpa_sign_date_0']             = '';
        $field_data['pdpa_sign_date_1']             = '';
        $field_data['agreement_start_date_0']       = '';
        $field_data['agreement_start_date_1']       = '';
        $field_data['agreement_end_date_0']         = '';
        $field_data['agreement_end_date_1']         = '';
        $field_data['agreement_sign_date_0']        = '';
        $field_data['agreement_sign_date_1']        = '';
        $field_data['standard_charge_type_desc_en']  = '';
        $field_data['standard_charge_type_desc_zh']  = '';
        $field_data['standard_charge_type_desc_th']  = '';
        $field_data['bulky_item_charge_type_value']  = '';
        $field_data['issuing_date_0']                = '';
        $field_data['issuing_date_1']                = '';
        $field_data['validity_date_0']               = '';
        $field_data['validity_date_1']               = '';

        // 甲乙方签字处 和 乙方BD页脚小签 变量
        return $this->getElectronicContractSignData($field_data, 0);
    }

    /**
     * 处理flash home 合同表单数据
     * @param $form_rule
     * @return array
     */
    public function fhContractParams($form_rule)
    {
        $field_data = [];

        foreach ($form_rule as $k => $v) {
            foreach ($v as $k1 => $v1) {
                $field_data[$v1['field']] = $v1['value'];
            }
        }

        $flash_sign_config               = EnumsService::getInstance()->getSettingEnvValueMap('flash_home_contract_sign_name_config');
        $field_data['fh_sign_name']      = $flash_sign_config['name'];
        $field_data['fh_sign_job_title'] = $flash_sign_config['job_title'];
        $field_data['fh_sign_img_url']   = $flash_sign_config['sign_name_url'];
        $field_data['sign_info'][]       = ['customer_name' => ''];
        return $field_data;
    }

    /**
     * 指定部门下数据权限
     * @param $params
     * @return array
     */
    public function useList($params)
    {
        $department_repository = new DepartmentRepository();
        //查当前员工部门信息
        $user_department   = $department_repository->getDepartmentDetail($params['department_id']);
        $ancestry_v3_array = explode('/', $user_department['ancestry_v3']);
        //查当前电子合同设置部门
        $department_info = EnumsService::getInstance()->getSettingEnvValueMap('contract_applicable_departments');

        //当前用户部门父链中存在哪个配置部门
        $params['department_id'] = [];
        foreach ($department_info as $item) {
            if (in_array($item['id'], $ancestry_v3_array)) {
                $template_enum_department_id = $this->getTemplateEnumIdByDepartmentId($item['id'], $department_info);
                if ($template_enum_department_id) {
                    $params['department_id'][] = $template_enum_department_id;
                }
            }
        }

        if (empty($params['department_id'])) {
            $page_size = empty($params['pageSize']) ? GlobalEnums::DEFAULT_PAGE_SIZE : $params['pageSize'];
            $page_num  = empty($params['pageNum']) ? GlobalEnums::DEFAULT_PAGE_NUM : $params['pageNum'];
            return [
                'items'      => $items ?? [],
                'pagination' => [
                    'current_page' => $page_num,
                    'per_page'     => $page_size,
                    'total_count'  => 0,
                ],
            ];
        }

        return $this->getList($params, 'use_list');
    }


    /**
     * 获取电子合同页搜索枚举列表
     * @param array $user
     * @return array
     */
    public function getListEnums(array $user)
    {
        $sys_department_id = !empty($user['sys_department_id']) ? $user['sys_department_id'] : $user['department_id'];
        $sys_department_id = $this->getTemplateEnumIdByDepartmentId($sys_department_id);

        // 使用中的版本所关联的合同模板
        $base_name_field_name = get_lang_field_name('template_name_', static::$language, 'th', 'zh');
        $columns = [
            "base.{$base_name_field_name} AS template_name",
            'base.id AS template_id'
        ];

        $builder   = $this->modelsManager->createBuilder();
        $builder->from(['ver' => ContractTemplateVersionModel::class]);
        $builder->leftjoin(ContractTemplateBaseModel::class, 'ver.relate_id = base.id', 'base');
        $builder->where('ver.department_id = :department_id:', ['department_id' => $sys_department_id]);
        $builder->andWhere('ver.state = :state:', ['state' => ContractEnums::CONTRACT_TEMPLATE_STATE_2]);
        $builder->columns($columns);
        $builder->groupBy("base.id");
        $template_list = $builder->getQuery()->execute()->toArray();
        $template_list = array_sort($template_list, 'template_name', SORT_ASC);

        return [
            'template_list' => $template_list,
        ];
    }

}