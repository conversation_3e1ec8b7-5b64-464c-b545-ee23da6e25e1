<?php

namespace App\Modules\Contract\Services;

use App\Library\Enums;
use App\Library\Enums\ContractEnums;
use App\Library\Enums\GlobalEnums;
use App\Library\ErrCode;
use App\Library\Exception\BusinessException;
use App\Library\OssHelper;
use App\Library\RedisClient;
use App\Library\Validation\ValidationException;
use App\Modules\Common\Services\CommonService;
use App\Modules\Common\Services\ContractService;
use App\Modules\Common\Services\EnumsService;
use App\Modules\Contract\Models\Contract;
use App\Repository\DepartmentRepository;
use App\Repository\HrJobTitleRepository;
use App\Repository\HrStaffRepository;
use App\Repository\oa\ContractCategoryRepository;
use App\Repository\oa\ContractElectronicRepository;
use App\Util\RedisKey;
use GuzzleHttp\Exception\GuzzleException;

class BaseService extends \App\Library\BaseService
{
    const LIST_TYPE_APPLY = 1;
    const LIST_TYPE_AUDIT = 2;
    const LIST_TYPE_FYR = 3;
    const LIST_TYPE_SEARCH = 4;

    public static $validate_detail = [
        'id' => 'Required|IntGe:1'                                  //合同ID
    ];

    public static $validate_fyr = [
        'fyr_id' => 'IntGe:0',// 征询ID
    ];

    public static $validate_update = [
        'id' => 'Required|IntGe:1'                                  //合同ID
    ];

    private static $validate_other = [
        '1' => [
            'payment_currency' => 'Required|IntIn:' . GlobalEnums::VALIDATE_CURRENCY_PARAMS,  //付款币种
            'amount' => 'Required|FloatGt:0.0',                         //付款金额
        ],
    ];

    public static $validate_consumer_id = [
        'cno' => 'Required|StrLenGeLe:1,30',//合同编号
        'quotation' => 'Required|Arr|ArrLenGeLe:1,20',//报价单信息
        'quotation[*].quotation_no' => 'Required|StrLenGeLe:1,16',//报价单号
        'quotation[*].consumer_id' => 'Required|StrLenGe:1',//客户id
    ];

    public static $validate_check_consumer_id = [
        'consumer_id' => 'Required|StrLenGe:1',//客户id
    ];

    // 获取数据提交时通用的校验规则
    public static function getCommonSaveValidateRules()
    {
        return [
            'cname' => 'Required|StrLenGeLe:1,100|>>>:' . static::$t->_('contract_name_length_is_overflow'),//合同名称
            //'cno' => 'Required|StrLenGeLe:11,12',                       //合同编号
            //'template_id' => 'Required|IntIn:11,12,13,14,15',           //合同模版
            'is_master' => 'Required|IntIn:1,2',                        //主从属性
            //'sub_cno' => 'IfIntEq:is_master,2|Required|StrLenGeLe:11,12',      //关联主合同编号
            'contract_file_arr' => 'Required|ArrLen:1',
            'contract_desc' => 'StrLenGeLe:0,1000|>>>:' . static::$t->_('contract_desc_length_is_overflow'),
            'sell_cno' => 'StrLenGeLe:0,20|>>>:' . static::$t->_('contract_param_error_0001'),
            'attachment_arr' => 'ArrLenGeLe:1,20',
            'is_group_contract' => 'Required|IntIn:0,1', //是否集团间合同
            'contract_storage_type' => 'Required|IntIn:1,2',  //1纸质合同2电子合同
            'is_agreement' => 'Required|IntIn:1,2',
            'effective_date' => 'Required|Date',
            'expiry_date' => 'IfIntEq:is_agreement,1|Required|Date',
        ];
    }

    public static $validate_archive = [
        'id' => 'Required|IntGe:1',                                        //合同归档ID
        'holder_name' => 'Required|StrLenGeLe:1,30',                       //纸质合同保管人
        'contract_file' => 'Required|ArrLen:1',
        'contract_file[*].file_name' => 'Required|StrLenGeLe:2,200',
        'contract_file[*].bucket_name' => 'Required|StrLenGeLe:2,300',
        'contract_file[*].object_key' => 'Required|StrLenGeLe:2,300',
    ];

    protected static $special_field = ['custom_authorized_person_info', 'standard_settlement_type', 'bulky_item_settlement_type', 'fruit_item_settlement_type'];

    public static function handleParams($params, $not_must)
    {
        if (empty($not_must) || !is_array($not_must)) {
            return $params;
        }
        foreach ($not_must as $value) {
            if (isset($params[$value]) && empty($params[$value])) {
                unset($params[$value]);
            }
        }
        return $params;
    }
    
    /**
     * @param $path
     * @param $is_return_object_key
     * @return array|string
     */
    public function getShowPath($path, $is_return_object_key = false)
    {
        $path_arr = $this->getRemotePath($path);
        $config = $this->getDI()->get('config');
        $img_prefix = $config->application->img_prefix ?? '';
        $path = $path_arr[0]['object_key'] ?? '';
        if (empty($is_return_object_key)) {
            return $img_prefix . $path;
        } else {
            return ['url' => $img_prefix . $path, 'object_key' => $path];
        }
    }

    /**
     * @param $oss_file_arr
     * @return string
     */
    public function handle_oss_file($oss_file_arr)
    {
        if (empty($oss_file_arr) || !is_array($oss_file_arr)) {
            return '';
        }
        $oss_key_arr = [];
        foreach ($oss_file_arr as $item) {
            $oss_key_arr[] = implode('@', $item);
        }
        return implode(',', $oss_key_arr);
    }
    
    /**
     * @param $path
     * @param $business_type
     * @param $id
     * @return array
     */
    public function getRemotePath($path,$business_type = '',$id = '')
    {
        if (empty($path)) {
            return [];
        }
        $path_arr = [];
        if (is_array($path)) {
            foreach ($path as $val) {
                $file_arr = $this->getOssFileName($val);
                if ($business_type){
                    $path_arr[] = [
                        'id' => $file_arr['object_key'],
                        'business_type' => $business_type,
                        'file_name' => $file_arr['file_name'],
                        'bucket_name' => $file_arr['bucket_name'],
                        'object_key' => $file_arr['object_key'],
                    ];
                }else{
                    $path_arr[] = [
                        'file_name' => $file_arr['file_name'],
                        'bucket_name' => $file_arr['bucket_name'],
                        'object_key' => $file_arr['object_key'],
                    ];
                }
            }
        } else {
            [$file_name, $bucket, $object_key] = explode('@', $path);

            if ($business_type){
                $path_arr[] = [
                    'id' => $object_key,
                    'business_type' => $business_type,
                    'file_name' => $file_name,
                    'bucket_name' => $bucket,
                    'object_key' => $object_key,
                ];
            }else{
                $path_arr[] = [
                    'file_name' => $file_name,
                    'bucket_name' => $bucket,
                    'object_key' => $object_key,
                ];
            }
        }

        return $path_arr;
    }

    /**
     * @param $oss_file
     * @return array|mixed
     */
    private function getOssFileName($oss_file)
    {
        return $oss_file;
        if (empty($oss_file) || strpos($oss_file, '@') == false) {
            return [
                'file_name' => '',
                'bucket_name' => '',
                'object_key' => '',
            ];
        }
        [$file_name, $bucket, $object_key] = explode('@', $oss_file);

        return [
            'file_name' => $file_name,
            'bucket_name' => $bucket,
            'object_key' => $object_key,
        ];
    }

    /**
     * @param array $params 参数
     * @param bool $is_update
     * @return array
     * @throws ValidationException
     */
    public function getValidateParams($params, $is_update = false)
    {
        $rules = [];
        //合同模板
        $type = !empty($params['template_id']) ? $params['template_id'] : 0;
        if (isset(self::$validate_other[$type])) {
            $rules = self::$validate_other[$type];
        }
        //更新操作增加id校验
        if ($is_update === true) {
            $rules = array_merge($rules, self::$validate_update);
        }
        //是否销售合同, 销售合同增加关联报价单校验
        if ($this->isSalesContract($type)) {
            $quotation_validate = [
                'is_quotation' => 'Required|IntIn:' . ContractEnums::VALIDATE_CONTRACT_RELATION_QUOTATION,  //是否关联报价单
                'quotation_no' => 'IfIntEq:is_quotation,' . ContractEnums::CONTRACT_RELATION_QUOTATION_YES . '|Required|Arr|ArrLenGeLe:1,20',
                'quotation_no[*]' => "StrLenGeLe:1,16",
            ];
            $rules = array_merge($rules, $quotation_validate);
        }
        //是否为集团公司间合同
        if (isset($params['is_group_contract']) && $params['is_group_contract'] == ContractEnums::IS_GROUP_CONTRACT_YES) {
            //是否集团间合同=是
            $rules['company_relation_code'] = 'Required|IntGt:0'; //必填合同关联公司
        } elseif (isset($params['is_group_contract']) && $params['is_group_contract'] == ContractEnums::IS_GROUP_CONTRACT_NO) {
            //是否集团间合同=否
            $rules['is_vendor'] = 'Required|IntIn:0,1'; //必填是否供应商
            if (isset($params['is_vendor']) && $params['is_vendor'] == ContractEnums::IS_VENDOR_YES) {
                //是否供应商=是
                $rules['vendor_id'] = 'Required|StrLenGeLe:1,32';//供应商编码
                $rules['vendor_name'] = 'Required|StrLenGeLe:1,128';//供应商名称
            } elseif (isset($params['is_vendor']) && $params['is_vendor'] == ContractEnums::IS_VENDOR_NO) {
                //是否供应商=否
                $rules['customer_company_name'] = 'Required|StrLenGeLe:1,50';//客户公司名称
            }
        }
        //合同关联公司不可与合同所属公司相同
        if (!empty($params['company_relation_code']) && !empty($params['company_code']) && $params['company_relation_code'] == $params['company_code']) {
            throw new ValidationException(static::$t->_('company_relation_code_cannot_equal_company_code'), ErrCode::$VALIDATE_ERROR);
        }
        //flash home Flash Home Operation
        //apply_staff_department=1 表示flashHome
        if ($params['apply_staff_department'] == 1) {
            $rules['franchisee_type'] = 'Required|IntIn:1,2';//加盟商类型
            $rules['franchisee_id'] = 'Required|StrLenGeLe:1,32';//加盟商id
            $rules['store_id'] = 'Required|StrLenGeLe:1,10';//网点code
            $rules['franchisee_name'] = 'Required|StrLenGeLe:1,50';//加盟商负责人
        }

        return array_merge(self::getCommonSaveValidateRules(), $rules);
    }

    /**
     * 获取合同编号
     *
     * @return string
     */
    public static function getContractNo()
    {
        if (self::getContractCounter()) {           //有计数器
            $cno = self::incrContractCounter();
        } else {                                    //没有计数器（一直都没有，有但是存在宕机）
            $cno = self::setContractCounter();
        }
        return 'FEX' . date('Ymd') . sprintf('%04s', $cno);
    }

    /**
     * 判断计数器是否存在
     *
     * @return bool|int
     */
    private static function getContractCounter()
    {
        return RedisClient::getInstance()->getClient()->exists(RedisKey::CONTRACT_CREATE_COUNTER);
    }

    /**
     * 计数器不存在的情况下
     *
     * @return bool|int
     */
    private static function setContractCounter()
    {
        $cno = 0;
        $today = date('Y-m-d');
        $expireTime = strtotime($today . ' 23:59:59');
        $contract = Contract::findFirst([
            'columns' => 'cno',
            "created_at > '{$today}'",
            'order' => 'id DESC'
        ]);
        if (!empty($contract->cno)) {   //数据库里有，取一下值
            $cno = substr($contract->cno, -4, 4);
        }
        ++$cno;
        RedisClient::getInstance()->getClient()->setex(RedisKey::CONTRACT_CREATE_COUNTER, $expireTime - time(), $cno);

        return $cno;
    }

    /**
     * 计数器存在的情况下
     *
     * @return int
     */
    private static function incrContractCounter()
    {
        return RedisClient::getInstance()->getClient()->incrBy(RedisKey::CONTRACT_CREATE_COUNTER, 1);
    }

    public function getPayer()
    {
        return [
            1 => self::$t->_('company_nature.2'),
            2 => self::$t->_('house_owner')
        ];
    }

    public function getLang()
    {
        $lang = self::$language;
        if (empty($lang) || !in_array($lang, ["th", "en", "zh-CN"], 1)) {
            $lang = "th";
        }
        return $lang;
    }

    public function isCanDownload($item, $uid)
    {
        if (empty($item)) {
            return '0';
        }

        if (empty($uid)) {
            return '1';
        }

        if ($item['status'] == Enums::CONTRACT_STATUS_APPROVAL) {
            return '1';
        }

        return '0';
    }

    /**
     * 是否是pmd部门提交的销售合同
     *
     * @param int $contract_template_id 合同分类
     * @param int $user_sys_department_id 当前用户一级部门id
     *
     * @return mixed
     *
     * @throws ValidationException
     */
    public function isPmdSubmitSalesContract(int $contract_template_id, int $user_sys_department_id)
    {
        $is_pmd_department = false;

        // PMD部门销售合同的校验: 启用国家: 泰国/菲律宾/马来
        $country_code = get_country_code();
        if (in_array($country_code, [GlobalEnums::MY_COUNTRY_CODE, GlobalEnums::PH_COUNTRY_CODE, GlobalEnums::TH_COUNTRY_CODE])) {
            // PMD部门配置
            $pmd_department_id_arr = EnumsService::getInstance()->getSettingEnvValueIds('pmd_department_ids');
            if (empty($pmd_department_id_arr)) {
                throw new ValidationException(static::$t->_('pmd_department_no_setting'), ErrCode::$VALIDATE_ERROR);
            }

            // 是否pmd部门
            $is_pmd_department = in_array($user_sys_department_id, $pmd_department_id_arr);

            // 马来/菲律宾: 只有PMD部门方可提销售合同
            if (in_array($country_code, [GlobalEnums::MY_COUNTRY_CODE, GlobalEnums::PH_COUNTRY_CODE]) && !$is_pmd_department && $this->isSalesContract($contract_template_id)
            ) {
                throw new ValidationException(static::$t->_('sales_department_no_setting_workflow'), ErrCode::$VALIDATE_ERROR);
            }
        }

        return $is_pmd_department;
    }

    /**
     * 是否是 属于 Flash Home Operation
     *
     * @param $department_id
     * @return bool
     */
    public function isFlashHomeOperation($department_id)
    {
        $department_info = EnumsService::getInstance()->getSettingEnvValueMap('contract_applicable_departments');
        $department_info = array_column($department_info, 'id', 'name');

        //获取fh下属部门
        $sub_department_data = (new DepartmentRepository())->getDepartmentSubListByIds($department_info['Flash Home Operation'] ?? 0);
        $sub_department_ids = array_column($sub_department_data, 'id');
        return in_array($department_id, $sub_department_ids);
    }

    /**
     * 是否是 属于 FFM
     *
     * @param $department_id
     * @return bool
     */
    public function isFFMFromOrganizational($department_id)
    {
        $department_info = EnumsService::getInstance()->getSettingEnvValueMap('contract_applicable_departments');
        $department_info = array_column($department_info, 'id', 'name');

        //获取fh下属部门
        $sub_department_data = (new DepartmentRepository())->getDepartmentSubListByIds($department_info['FFM'] ?? 0);
        $sub_department_ids = array_column($sub_department_data, 'id');
        return in_array($department_id, $sub_department_ids);
    }

    /**
     * 是否是 属于 Flash Home Operation
     *
     * @param int $department_id 一级部门
     * @return bool
     */
    public function isFlashHomeOperationV2(int $department_id)
    {
        $department_list = EnumsService::getInstance()->getSettingEnvValueMap('contract_applicable_departments');
        $department_list = array_column($department_list, 'id', 'name');
        return $department_id == $department_list['Flash Home Operation'];
    }

    /**
     * 21403 - PMD组织架构调整配套改造 ; 因PMD拆分部门，需要配置多个部门id
     * 跟产品讨论沿用合同原先配置 名称相同 id不同，按照名称分组
     * @return array
     */
    public function getContractApplicableDepartmentByName()
    {
        static $group_department_list = [];
        if (empty($group_department_list)) {
            $department_list = EnumsService::getInstance()->getSettingEnvValueMap('contract_applicable_departments');
            foreach ($department_list as $item) {
                $group_department_list[$item['name']][] = $item['id'];
            }
        }

        return $group_department_list;
    }

    /**
     * 是否是 属于 Group Project Management
     *
     * @param int $department_id 一级部门
     * @return bool
     */
    public function isGroupProjectManagement(int $department_id)
    {
        $department_list = $this->getContractApplicableDepartmentByName();
        return in_array($department_id, $department_list['Group Project Management']);
    }

    /**
     * 是否是 属于 Retail Management
     *
     * @param int $department_id 一级部门
     * @return bool
     */
    public function isRetailManagement(int $department_id)
    {
        $department_list = $this->getContractApplicableDepartmentByName();
        return in_array($department_id, $department_list['Retail Management']);
    }

    /**
     * 是否是销售合同
     *
     * @param string $contract_template_id
     * @return bool
     */
    public function isSalesContract(string $contract_template_id)
    {
        static $sales_contract_categorys = [];
        if (!empty($sales_contract_categorys)) {
            return in_array($contract_template_id, $sales_contract_categorys);
        }

        $sales_contract_categorys = ContractCategoryRepository::getInstance()->getSubCategoryIdsByAncestryId(Enums::CONTRACT_TEMPLATE_SALES);
        return in_array($contract_template_id, $sales_contract_categorys);
    }

    /**
     * 21403【TH MY PH|OA|其他合同&电子合同】PMD组织架构调整配套改造
     * 仍采用设置：电子合同设置部门- setting_env表code=‘contract_applicable_departments’可编辑（电子合同线上化适用部门）
     * 电子合同线上化适用部门，（ template_enum是否为合同模板部门枚举 1是，0否；相同name下部门id不同的此元素只可有一个为1的设置）
     * 需要根据传递的部门id 找到此id 所对应的部门名称；在根据名称找到是合同模板部门枚举中对应的部门id返回
     * @param integer $department_id 部门ID
     * @param array $department_info 电子合同线上化适用部门组 有的地方已经查过一次，避免重复查询；
     * @return int|mixed
     */
    public function getTemplateEnumIdByDepartmentId($department_id, $department_info = [])
    {
        //获取合同模版-模板适用部门-枚举列表（template_enum = 1）-按照名称分组
        $department_group_name = array_column($this->getTemplateDepartmentList(), 'id', 'name');

        //电子合同线上化适用部门 - 所有列表
        $department_info       = $department_info ? $department_info : EnumsService::getInstance()->getSettingEnvValueMap('contract_applicable_departments');
        $template_enum_department_id = 0;
        foreach ($department_info as $item) {
            if ($item['id'] == $department_id && !empty($department_group_name[$item['name']])) {
                $template_enum_department_id = $department_group_name[$item['name']];
                break;
            }
        }
        return $template_enum_department_id;
    }

    /**
     * 获取合同模版-模板适用部门-枚举列表
     * @return array
     */
    public function getTemplateDepartmentList()
    {
        $department_info = EnumsService::getInstance()->getSettingEnvValueMap('contract_applicable_departments');
        //21403 - PMD组织架构调整配套改造；新增一个元素 template_enum ： 是否为合同模板部门枚举 1是，0否；同一个部门名称下的配置只有一个元素的的template_enum = 1
        $template_enum_department_info = [];
        foreach ($department_info as $key => $item) {
            if ($item['template_enum'] != 1) {
                continue;
            }
            $template_enum_department_info[] = $item;
        }
        return $template_enum_department_info;
    }

    /**
     * 合同线上化枚举
     */
    public function getEnums()
    {
        $data = [];
        $lang = static::$t;
        $contract_storage_type = ContractEnums::$contract_storage_type;
        $contract_sub_file_status = ContractEnums::$contract_sub_file_status;
        $contract_sign_status = ContractEnums::$contract_sign_status;
        $contract_electronic_status = ContractEnums::$contract_electronic_status;
        $contract_template_state = ContractEnums::$contract_template_state;
        $contract_approval_status = Enums::$contract_status;
        $contract_status = ContractEnums::$contract_status;

        $contract_data = EnumsService::getInstance()->getSettingEnvValueMap('pmd_contract_enums');

        $data['use_department'] = $this->getTemplateDepartmentList();

        $contract_lang = ((new ContractService())->getContractEnums())['contract_lang'];

        foreach ($contract_approval_status as $key => $value) {
            $data['contract_approval_status'][] = [
                'id' => (string)$key,
                'name' => $lang->_($value)
            ];
        }

        foreach ($contract_status as $key => $value) {
            $data['contract_status'][] = [
                'id' => (string)$key,
                'name' => $lang->_($value)
            ];
        }

        foreach ($contract_storage_type as $key => $value) {
            $data['contract_storage_type'][] = [
                'id' => (string)$key,
                'name' => $lang->_($value)
            ];
        }

        foreach ($contract_sub_file_status as $key => $value) {
            $data['subfile_status'][] = [
                'id' => (string)$key,
                'name' => $lang->_($value)
            ];
        }

        foreach ($contract_sign_status as $key => $value) {
            $data['contract_sign_status'][] = [
                'id' => (string)$key,
                'name' => $lang->_($value)
            ];
        }


        foreach ($contract_electronic_status as $key => $value) {
            $data['contract_electronic_status'][] = [
                'id' => (string)$key,
                'name' => $lang->_($value)
            ];
        }


        foreach ($contract_template_state as $key => $value) {
            $data['template_state'][] = [
                'id' => (string)$key,
                'name' => $lang->_($value)
            ];
        }


        foreach ($contract_data['subfile_type'] as &$item) {
            $item['name'] = $lang->_($item['name']);
            $data['subfile_type'][] = $item;
        }

        foreach ($contract_lang as &$item) {
            $data['lang'][] = [
                'id' => $item['code'],
                'name' => $item['label']
            ];
        }

        foreach ($contract_data['contract_type'] as &$item) {
            $item['name'] = $lang->_($item['name']);
            $data['contract_type'][] = $item;
        }

        foreach (ContractEnums::$sign_customer_type as $key => $value) {
            $data['sign_customer_type'][] = [
                'id' => $key,
                'name' => static::$t->_($value),
            ];
        }

        foreach (ContractEnums::$sign_order_type as $key => $value) {
            $data['sign_order_type'][] = [
                'id' => (string)$key,
                'name' => static::$t->_($value),
            ];
        }

        return $data;

    }

    /**
     * 电子合同全部数据权限员工
     * */
    public function authorityStaffs()
    {
        return (EnumsService::getInstance()->getSettingEnvValueIds('contract_template_view_staffs'));
    }

    /***
     * 获取oss 文件
     *
     * @param $html
     * @param $file_name
     * @return mixed|string
     * @throws BusinessException
     * @throws GuzzleException
     */
    public function createFtlFile($html, $file_name)
    {
        $file_path = put_data_in_file($html, $file_name);
        $oss_result = OssHelper::uploadFile($file_path);
        if (empty($oss_result['object_key'])) {
            throw new BusinessException("电子合同-ftl文件创建失败, file_path={$file_path}, file_name={$file_name}", ErrCode::$BUSINESS_ERROR);
        }

        return $oss_result['object_key'];
    }

    /**
     * 其他合同新增/编辑更新的公共处理
     *
     * @param $data
     * @param $user
     * @param bool $is_add
     * @return array
     * @throws ValidationException
     */
    protected function handleSaveData($data, $user, bool $is_add = false)
    {
        // 获取币种与系统默认币种的汇率
        $exchange_rate = EnumsService::getInstance()->getCurrencyExchangeRate($data['payment_currency']);
        if ($exchange_rate === false) {
            throw new ValidationException(static::$t->_('exchange_rate_getting_error'), ErrCode::$VALIDATE_ERROR);
        }
        $data['exchange_rate'] = $exchange_rate;

        // 新增
        if ($is_add) {
            $data['create_id'] = $user['id'] ?? 0;
            $data['created_at'] = date('Y-m-d H:i:s');
        }

        $data['amount'] = bcmul($data['amount'], 1000);
        $data['status'] = Enums::CONTRACT_STATUS_PENDING;
        $data['create_name'] = $this->getNameAndNickName($user['name'] ?? '', $user['nick_name'] ?? '');
        $data['create_department'] = $user['department'] ?? '';
        $data['create_department_id'] = $user['department_id'] ?? 0;
        $data['updated_at'] = date('Y-m-d H:i:s');
        $data['last_update_id'] = $user['id'];
        $data['last_update_name'] = $user['name'];
        $data['last_update_department'] = $user['department'];
        $data['last_update_job_title'] = $user['job_title'];
        $data['last_update_at'] = date('Y-m-d H:i:s');

        if ($data['is_master'] == Enums::CONTRACT_IS_MASTER_YES) {
            $data['sub_cno'] = '';
        }

        if ($data['pay_method'] === '') {
            unset($data['pay_method']);
        }

        if (isset($data['effective_date']) && empty($data['effective_date'])) {
            $data['effective_date'] = null;
        }

        if (isset($data['expiry_date']) && empty($data['expiry_date'])) {
            $data['expiry_date'] = null;
        }

        //非销售合同is_quotation设置为2否,关联报价单设置为空 避免前端传0
        if (!$this->isSalesContract($data['template_id'])) {
            $data['is_quotation'] = ContractEnums::CONTRACT_RELATION_QUOTATION_NO;
            $data['quotation_no'] = [];
        }

        if (isset($data['contract_file']) && is_array($data['contract_file'])) {
            $data['contract_file'] = '';
        }

        if (isset($data['attachment']) && is_array($data['attachment'])) {
            $data['attachment'] = '';
        }

        return $data;
    }

    /**
     * 根据签约进度 或 获取电子合同pdf签章的默认值
     *
     * @param array $field_data
     * @param int $sign_step
     * @return array
     */
    public function getElectronicContractSignData(array $field_data, int $sign_step = 0)
    {
        // 甲乙方签字处甲方公司名称默认值处理
        if (!isset($field_data['custom_company_name_en']) && isset($field_data['agreement_company_name_en'])) {
            $field_data['custom_company_name_en'] = $field_data['agreement_company_name_en'];
        }

        if (!isset($field_data['custom_company_name_zh']) && isset($field_data['agreement_company_name_zh'])) {
            $field_data['custom_company_name_zh'] = $field_data['agreement_company_name_zh'];
        }

        if (!isset($field_data['custom_company_name_th']) && isset($field_data['agreement_company_name_th'])) {
            $field_data['custom_company_name_th'] = $field_data['agreement_company_name_th'];
        }

        $field_data['custom_company_name_en'] = $field_data['custom_company_name_en'] ?? '';
        $field_data['custom_company_name_zh'] = $field_data['custom_company_name_zh'] ?? '';
        $field_data['custom_company_name_th'] = $field_data['custom_company_name_th'] ?? '';

        // 甲乙方签字处变量
        $sign_field_data = [
            // 甲方签章处(对方)
            'custom_company_sign_img' => '',
            'custom_poa_item' => [['sign_img' => '', 'sign_name' => '', 'sign_job_title' => '']],
            'custom_company_sign_date' => '',

            // 乙方签章处(我方)
            'flash_company_sign_img' => '',
            'flash_authorized_person_sign_img' => '',
            'flash_authorized_person_name' => '',
            'flash_authorized_person_job_title' => '',
            'flash_company_sign_date' => '',

            // 乙方BD页脚小签
            'flash_bd_sign_img' => '',

            // 退件签字人签名
            'return_sign_picture' => '',
        ];

        return array_merge($field_data, $sign_field_data);
    }

    /**
     * 电子合同签约: 各场景邮件通知
     *
     * @param string $notice_scence 通知场景
     * @param array $email_var 邮件模板变量
     * @param bool $is_async_send 发送方式 true-异步发; false-实时发
     * @return bool
     * @throws BusinessException
     * @throws ValidationException
     */
    public function sendElectronicContractEmailNotice(string $notice_scence, array $email_var = [], bool $is_async_send = false)
    {
        $this->logger->info('电子合同-待发邮件, func_get_args' . json_encode(func_get_args(), JSON_UNESCAPED_UNICODE));

        if (empty($email_var['contract_lang'])) {
            throw new BusinessException("合同语言参数有误, 请检查[contract_lang={$email_var['contract_lang']}]", ErrCode::$BUSINESS_ERROR);
        }

        $emails = array_values(array_filter($email_var['emails']));
        if (empty($emails)) {
            throw new BusinessException('收件人邮箱为空, 请检查, emails=' . json_encode($emails), ErrCode::$BUSINESS_ERROR);
        }
        unset($email_var['emails']);

        // 获取适用部门所属公司名称
        $need_company_name_scence_list = [
            ContractEnums::ELECTRONIC_CONTRACT_EMIAL_SCENCE_WAITING_SIGN,
            ContractEnums::ELECTRONIC_CONTRACT_EMIAL_SCENCE_WAITING_POA_SIGN,
            ContractEnums::ELECTRONIC_CONTRACT_EMIAL_SCENCE_WAITING_RESIGN,
            ContractEnums::ELECTRONIC_CONTRACT_EMIAL_SCENCE_OTP_CODE,
            ContractEnums::ELECTRONIC_CONTRACT_EMIAL_SCENCE_SIGN_APPROVALED,
        ];
        if (in_array($notice_scence, $need_company_name_scence_list) && !isset($email_var['department_id'])) {
            throw new BusinessException("notice_scence: {$notice_scence}-获取公司名称时, 适用部门参数不存在, 请检查, email_var.department_id", ErrCode::$BUSINESS_ERROR);
        }

        $department_config = $this->getContractApplicableDepartmentConfig($email_var['department_id']);
        $email_var['company_name'] = $department_config['company_name'] ?? '';

        // 是否需要双语
        $is_need_multiple_languages = true;

        // 主题国家前缀
        $subject_prefix = '';

        switch ($notice_scence) {
            // 待甲方商务签约/待POA签字
            case ContractEnums::ELECTRONIC_CONTRACT_EMIAL_SCENCE_WAITING_SIGN:
            case ContractEnums::ELECTRONIC_CONTRACT_EMIAL_SCENCE_WAITING_POA_SIGN:
                $subject_key = '22228_email_waiting_sign_subject';
                $text_key = '22228_email_waiting_sign_text';
                break;

            // 待甲方商务重新签约
            case ContractEnums::ELECTRONIC_CONTRACT_EMIAL_SCENCE_WAITING_RESIGN:
                $subject_key = '22228_email_waiting_resign_subject';
                $text_key = '22228_email_waiting_resign_text';
                break;

            // POA完成签约提醒
            case ContractEnums::ELECTRONIC_CONTRACT_EMIAL_SCENC_POA_SIGNED:
                $subject_key = 'electronic_contract_email_poa_signed_subject';
                $text_key = 'electronic_contract_email_poa_signed_text';
                break;

            // 签约完成, 可下载合同提醒
            case ContractEnums::ELECTRONIC_CONTRACT_EMIAL_SCENCE_SIGN_APPROVALED:
                $subject_key = '22228_email_sign_approvaled_subject';
                $text_key = '22228_email_sign_approvaled_text';
                break;

            // 超时未签约提醒
            case ContractEnums::ELECTRONIC_CONTRACT_EMIAL_SCENCE_SIGN_TIMEOUT:
                $is_need_multiple_languages = false;
                $subject_prefix = '【' . get_country_code() . '】';

                $subject_key = 'electronic_contract_email_sign_timeout_subject';
                $text_key = 'electronic_contract_email_sign_timeout_text';
                break;

            // 客户完成签约提醒
            case ContractEnums::ELECTRONIC_CONTRACT_EMIAL_SCENCE_WAITING_REVIEW:
                $is_need_multiple_languages = false;
                $subject_prefix = '【' . get_country_code() . '】';

                $subject_key = 'electronic_contract_email_waiting_review_subject';
                $text_key = 'electronic_contract_email_waiting_review_text';
                break;

            // otp验证码
            case ContractEnums::ELECTRONIC_CONTRACT_EMIAL_SCENCE_OTP_CODE:
                $subject_key = '22228_email_otp_code_subject';
                $text_key = 'electronic_contract_email_otp_code_text';
                break;

            default:
                throw new BusinessException('邮箱提醒场景未配置, 请检查', ErrCode::$BUSINESS_ERROR);
        }

        // 合同语言
        $lang_item = explode('-', $email_var['contract_lang']);

        // 获取语言包
        $translation_1 = static::getTranslation($lang_item[0]);
        $subject = $subject_prefix . $translation_1->_($subject_key, $email_var);
        $text = $translation_1->_($text_key, $email_var);

        if ($is_need_multiple_languages && !empty($lang_item[1])) {
            $translation_2 = static::getTranslation($lang_item[1]);
            $subject .= ' ' . $translation_2->_($subject_key, $email_var);
            $text .= '<br /><br />' . $translation_2->_($text_key, $email_var);
        }

        if ($is_async_send) {
            // 实时发邮件
            $send_res = $this->mailer->sendAsync($emails, $subject, $text);
        } else {
            $send_res = $this->mailer->send($emails, $subject, $text);
        }

        if (!$send_res) {
            $this->logger->info('电子合同-待发邮件, 发送失败');
            throw new ValidationException(static::$t->_('sign_email_send_error'), ErrCode::$VALIDATE_ERROR);
        }

        $this->logger->info('电子合同-待发邮件, 发送成功');

        return true;
    }

    /**
     * 生成电子合同pdf文件
     *
     * @param int $department_id 电子合同所属一级部门
     * @param string $file_url 电子合同ftl文件地址
     * @param array $form_data 电子合同表单数据
     * @return array|mixed
     * @throws BusinessException
     */
    public function generateElectronicContractPdfFile(int $department_id, string $file_url, array $form_data)
    {
        // 电子合同所属部门
        $department_info = $this->getContractApplicableDepartmentByName();
        if (in_array($department_id, $department_info['Group Project Management'])) {
            // PMD 部门的合同
            // 页眉设置
            $template_header = EnumsService::getInstance()->getSettingEnvValue('contract_pdf_template_header');
            $template_header = str_replace('contract_name', $form_data['contract_name'], $template_header);

            // 页脚设置: pdf v2版本 页眉页脚暂不支持url方式(api/pdf/v2/createPdfByJvppeteer)
            // URL 需要转换 base64, 其他的维持原值
            $flash_bd_sign_img = is_valid_url($form_data['flash_bd_sign_img']) ? get_image_base64($form_data['flash_bd_sign_img']) : $form_data['flash_bd_sign_img'];
            $template_footer   = EnumsService::getInstance()->getSettingEnvValue('contract_pdf_template_footer');
            $template_footer   = str_replace('flash_bd_sign_img', $flash_bd_sign_img, $template_footer);
        } elseif (in_array($department_id, $department_info['Retail Management'])) {
            // Retail 部门的合同
            // 页眉设置
            $template_header = EnumsService::getInstance()->getSettingEnvValue('contract_pdf_template_retail_header');
            $template_header = str_replace('contract_name', $form_data['contract_name'], $template_header);

            // 页脚设置
            $template_footer = EnumsService::getInstance()->getSettingEnvValue('contract_pdf_template_retail_footer');
        } elseif (in_array($department_id, $department_info['Flash Home Operation'])) {
            // FH 部门的合同
            // 页眉设置
            $template_header = EnumsService::getInstance()->getSettingEnvValue('contract_pdf_template_flash_home_header');
            $template_header = str_replace('contract_name', $form_data['contract_name'], $template_header);

            // 页脚设置
            $template_footer = EnumsService::getInstance()->getSettingEnvValue('contract_pdf_template_flash_home_footer');
        } elseif (in_array($department_id, $department_info['FFM'])) {
            // FFM 部门的合同
            // 页眉设置
            $template_header = EnumsService::getInstance()->getSettingEnvValue('contract_pdf_template_header_for_ffm');
            $template_header = str_replace('contract_no', $form_data['contract_no'] ?? '', $template_header);
            $template_header = str_replace('warehouse_name_en', $form_data['warehouse_name_en'] ?? '', $template_header);

            // 页脚设置
            $template_footer = EnumsService::getInstance()->getSettingEnvValue('contract_pdf_template_footer_ffm');
        } else {
            throw new BusinessException('获取电子合同-页眉页脚配置-所属部门异常, 请检查', ErrCode::$BUSINESS_ERROR);
        }

        $post_data = [
            'pdfName'      => time(),
            'templateUrl'  => gen_file_url(['object_key' => $file_url]),
            'data'         => $form_data,
            'downLoadData' => [], // v2版本接口 不支持该参数
            "pdfOptions"   => [
                "format"              => 'A4',
                "displayHeaderFooter" => true,
                "printBackground"     => true,
                "headerTemplate"      => $template_header,
                "footerTemplate"      => $template_footer,
            ],
        ];

        // 原接口: /api/pdf/createPdfByJvppeteer
        // 新接口: /api/pdf/v2/createPdfByJvppeteer 图片参数支持 对应公网url或base64字符串, 建议优先url; 页眉页脚暂不支持url方式
        $api_path = '/api/pdf/v2/createPdfByJvppeteer';
        $response_data = CommonService::getInstance()->newPostRequest(env('pdf_rpc_endpoint') . $api_path, json_encode($post_data, JSON_UNESCAPED_UNICODE));

        if ($response_data['code'] != ErrCode::$SUCCESS) {
            throw new BusinessException("电子合同-调用pdf服务失败({$api_path})", ErrCode::$BUSINESS_ERROR);
        }

        $bucket_data = $response_data['data'] ?? [];
        if (empty($bucket_data['bucket_name']) || empty($bucket_data['object_key']) || !isset($bucket_data['content_type'])) {
            throw new BusinessException('电子合同-生成pdf文件格式错误', ErrCode::$BUSINESS_ERROR);
        }

        return $bucket_data;
    }

    /**
     * 获取电子合同html->PDF所需配置
     *
     * @param int $department_id 电子合同所属一级部门
     * @return array|mixed
     * @throws BusinessException
     */
    public function getElectronicContractHtmlConfig(int $department_id)
    {
        $html_config = [
            'header_html' => '',
            'footer_html' => '</body></html>',
            'base_form' => [],
            'base_form_rule_lang' => [],
        ];

        // 电子合同所属部门
        $department_info = $this->getContractApplicableDepartmentByName();

        if (in_array($department_id, $department_info['Group Project Management'])) {
            // PMD 部门
            // html头部结构+公共样式
            $html_config['header_html'] = EnumsService::getInstance()->getSettingEnvValue('contract_pdf_template_head_html');

            // 基础表单规则
            $html_config['base_form'] = EnumsService::getInstance()->getSettingEnvValueMap('contract_template_base_form');

            // 表单多语言字段配置
            $html_config['base_form_rule_lang'] = EnumsService::getInstance()->getSettingEnvValueMap('pmd_base_form_rule_lang');
        } elseif (in_array($department_id, $department_info['Retail Management'])) {
            // Retail 部门
            // html头部结构+公共样式
            $html_config['header_html'] = EnumsService::getInstance()->getSettingEnvValue('contract_pdf_template_retail_head_html');

            // 基础表单规则
            $html_config['base_form'] = EnumsService::getInstance()->getSettingEnvValueMap('contract_template_base_form_for_retail');

            // 表单多语言字段配置
            $html_config['base_form_rule_lang'] = EnumsService::getInstance()->getSettingEnvValueMap('pmd_base_form_rule_lang');
        } elseif (in_array($department_id, $department_info['Flash Home Operation'])) {
            // FH 部门
            // html头部结构+公共样式
            $html_config['header_html'] = EnumsService::getInstance()->getSettingEnvValue('contract_pdf_template_flash_home_head_html');

            // 基础表单规则
            $html_config['base_form'] = EnumsService::getInstance()->getSettingEnvValueMap('contract_template_base_flash_home_form');
        } elseif (in_array($department_id, $department_info['FFM'])) {
            // FFM 部门
            // html头部结构+公共样式
            $html_config['header_html'] = EnumsService::getInstance()->getSettingEnvValue('contract_template_pdf_head_html_for_ffm');

            // 基础表单规则
            $html_config['base_form'] = EnumsService::getInstance()->getSettingEnvValueMap('contract_template_base_form_for_ffm');

            // 表单多语言字段配置
            $html_config['base_form_rule_lang'] = EnumsService::getInstance()->getSettingEnvValueMap('pmd_base_form_rule_lang');
        } else {
            throw new BusinessException('获取电子合同-PDF配置-所属部门异常, 请检查', ErrCode::$BUSINESS_ERROR);
        }

        return $html_config;
    }

    /**
     * 获取电子合同所属公司的电子签章
     *
     * @param int $department_id
     * @return mixed
     */
    public function getElectronicContractCompanySignImg(int $department_id)
    {
        // 电子合同所属部门
        $department_list = EnumsService::getInstance()->getSettingEnvValueMap('contract_applicable_departments');
        $department_list = array_column($department_list, null, 'id');
        return $department_list[$department_id]['sign_img'] ?? '';
    }

    /**
     * 获取指定部门的授权人配置
     *
     * @param int $department_id
     * @return array
     */
    public function getFlashAuthorizedStaffList(int $department_id = 0)
    {
        // 电子合同所属部门
        $department_info = $this->getContractApplicableDepartmentByName();

        $setting_env_code = '';
        if (in_array($department_id, $department_info['Group Project Management'])) {
            // PMD 部门
            $setting_env_code = 'pmd_contract_electronic_staff_list';
        } elseif (in_array($department_id, $department_info['Retail Management'])) {
            // Retail 部门
            $setting_env_code = 'retail_contract_electronic_staff_list';
        } elseif (in_array($department_id, $department_info['FFM'])) {
            // FFM 部门
            $setting_env_code = 'electronic_contract_authorized_staff_list_for_ffm';
        }

        // 授权人配置
        return !empty($setting_env_code) ? EnumsService::getInstance()->getSettingEnvValueMap($setting_env_code) : [];
    }

    /**
     * 获取乙方授权人的电子签章 和 职位
     *
     * @param int $department_id 电子合同所属部门
     * @param int $staff_id
     * @return array
     */
    public function getElectronicContractAuthorizedPersonInfo(int $department_id, int $staff_id)
    {
        $staff_info = [];

        // 授权人配置
        $staff_list = $this->getFlashAuthorizedStaffList($department_id);
        $staff_list = array_column($staff_list, null, 'id');
        $staff_info['sign_img'] = $staff_list[$staff_id]['sign_img'] ?? '';

        // 授权人职位
        $staff_base_info = (new HrStaffRepository())->getStaffById($staff_id);
        $staff_job_info = (new HrJobTitleRepository())->getJobTitleInfo($staff_base_info['job_title'] ?? 0, false);
        $staff_info['job_title'] = $staff_job_info['job_name'] ?? '';

        return $staff_info;
    }


    /**
     * 生成邮箱相关链接
     *
     * @param array $sign_link_params
     * @param int $scence 链接场景(或otp认证的场景)
     * @return string
     * @throws BusinessException
     */
    public function generateEmailSignLink(array $sign_link_params, int $scence)
    {
        $electronic_key = $sign_link_params['electronic_key'];
        $email_key      = $sign_link_params['email_key'];
        $department_id  = $sign_link_params['department_id'];

        $sign_url = env('electronic_contract_client_sign_domain', '');
        switch ($scence) {
            // 商务签字/POA签字
            case ContractEnums::ELECTRONIC_CONTRACT_CLIENT_OTP_BUSINESS_SIGN_LOGIN:
            case ContractEnums::ELECTRONIC_CONTRACT_CLIENT_OTP_POA_SIGN_LOGIN:
                $sign_url .= '/sign/notice';
                break;
            // 商务下载签字版合同
            case ContractEnums::ELECTRONIC_CONTRACT_CLIENT_OTP_BUSINESS_DOWNLOAD_LOGIN:
                $sign_url .= '/download/login';
                break;
            default:
                throw new BusinessException('电子合同-邮箱链接生成失败, scence=' . $scence, ErrCode::$BUSINESS_ERROR);
        }

        // 获取公司
        $company_flag = ContractEnums::COMPANY_FLAG_FLASH_EXPRESS;
        $config_info = $this->getContractApplicableDepartmentConfig($department_id);
        if (strtoupper($config_info['name']) == 'FFM') {
            $company_flag = ContractEnums::COMPANY_FLAG_FFM;
        }

        $url_params_list = [
            "emsk={$electronic_key}-{$email_key}-{$scence}",
            "skey={$company_flag}"// 公司标识: 1-Flash express; 2-FFM
        ];

        return $sign_url . '?' . implode('&', $url_params_list);
    }

    /**
     * 重置电子合同表单变量的值
     *
     * @param array $form_data
     * @return array
     */
    public function resetFormDataValue(array $form_data)
    {
        $form_data = trim_array($form_data);

        // 将指定表单项 为空的值 重置为 NA
        $default_empty_value = ContractEnums::ELECTRONIC_CONTRACT_FORM_DATA_EMPTY_VALUE_DEFAULT_CHAR;
        $reset_na_fields = ContractEnums::$electronic_contract_form_data_reset_na_fields;
        foreach ($form_data as $key => &$value) {
            if (is_string($value) && in_array($key, $reset_na_fields['string']) && mb_strlen($value) < 1) {
                $value = $default_empty_value;
                continue;
            }

            if (is_array($value) && isset($reset_na_fields['array'][$key])) {
                $item_fields = $reset_na_fields['array'][$key];
                foreach ($value as &$item) {
                    foreach ($item as $sub_key => &$sub_value) {
                        if (in_array($sub_key, $item_fields) && mb_strlen($sub_value) < 1) {
                            $sub_value = $default_empty_value;
                        }
                    }
                }
                continue;
            }
        }

        return $form_data;
    }

    /**
     * 获取电子合同合同类型枚举配置
     */
    public function getElectronicContractEnums()
    {
        $contract_enums = EnumsService::getInstance()->getSettingEnvValueMap('pmd_contract_enums');
        return [
            'subfile_type' => array_column($contract_enums['subfile_type'], 'id'),
            'contract_type' => array_column($contract_enums['contract_type'], 'id'),
        ];
    }

    /**
     * 是否是 属于 FFM 部门
     *
     * @param int $department_id 一级部门
     * @return bool
     */
    public function isFFM(int $department_id)
    {
        $department_list = $this->getContractApplicableDepartmentByName();
        return in_array($department_id, $department_list['FFM']);
    }

    /**
     * 判断电子合同账期是否超期
     *
     * 来自FFM的电子合同
     * @param $contract_data
     * @return int
     * @throws ValidationException
     */
    protected function getContractAccountPeriodStatus($contract_data)
    {
        $is_expired_by_account_period = 0;

        if (get_country_code() != GlobalEnums::TH_COUNTRY_CODE) {
            return $is_expired_by_account_period;
        }

        if (!isset($contract_data['company_code']) || $contract_data['company_code'] != ContractEnums::CONTRACT_COMPANY_FLASH_FULLFILMENT) {
            return $is_expired_by_account_period;
        }

        if (!isset($contract_data['contract_storage_type']) || $contract_data['contract_storage_type'] != ContractEnums::CONTRACT_STORAGE_TYPE_2) {
            return $is_expired_by_account_period;
        }

        if (!isset($contract_data['electronic_id']) || empty($contract_data['electronic_id'])) {
            throw new ValidationException(static::$t->_('params_error', ['param' => 'electronic_id']), ErrCode::$VALIDATE_ERROR);
        }

        // 获取电子合同 和 配置的账期
        $ffm_contract_account_period = EnumsService::getInstance()->getSettingEnvValue('ffm_contract_account_period');
        if (!is_numeric($ffm_contract_account_period)) {
            throw new ValidationException(static::$t->_('ffm_contract_account_period_config_error'), ErrCode::$VALIDATE_ERROR);
        }

        $contract_electronic_model = ContractElectronicRepository::getInstance()->getContractElectronicById($contract_data['electronic_id']);
        $account_period            = !empty($contract_electronic_model->account_period) ? $contract_electronic_model->account_period : 0;

        // 1-超过账期; 2-未超账期
        $is_expired_by_account_period = $account_period > $ffm_contract_account_period ? 1 : 2;

        $this->logger->info([
            'getContractAccountPeriodStatus' => [
                'cno'                                  => $contract_data['cno'],
                'electronic_id'                        => $contract_data['electronic_id'],
                'electronic_account_period'            => $account_period,
                'ffm_contract_account_period_config'   => $ffm_contract_account_period,
                'result: is_expired_by_account_period' => $is_expired_by_account_period,
            ],
        ]);

        return $is_expired_by_account_period;
    }

    /**
     * 获取电子合同适用部门相关配置
     * @return array
     */
    public function getContractApplicableDepartmentConfig($department_id)
    {
        $config_item = EnumsService::getInstance()->getSettingEnvValueMap('contract_applicable_departments');

        $department_info = [];
        foreach ($config_item as $item) {
            if ($item['id'] == $department_id) {
                $department_info = $item;
                break;
            }
        }
        return $department_info;
    }

}
