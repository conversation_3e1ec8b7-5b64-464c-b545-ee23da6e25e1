<?php

namespace App\Modules\Contract\Controllers;

use App\Library\ErrCode;
use App\Library\Validation\Validation;
use App\Library\Validation\ValidationException;
use App\Modules\Contract\Services\ContractSubFileService;
use GuzzleHttp\Exception\GuzzleException;
use Phalcon\Http\Response;
use Phalcon\Http\ResponseInterface;

class ContractSubFileController extends BaseController
{
    /**
     * 合同枚举
     * @Token
     * @return Response|ResponseInterface
     */
    public function getEnumsAction()
    {
        $res = ContractSubFileService::getInstance()->getEnums();
        return $this->returnJson(ErrCode::$SUCCESS, 'ok', $res);
    }

    /**
     * 搜索列表
     * @Permission(action='contract.template.manage.contract_subfile')
     * @return Response|ResponseInterface
     * @throws ValidationException
     */
    public function listAction()
    {
        $params = $this->request->get();
        Validation::validate($params, ContractSubFileService::$validate_list_search);
        $staff_lists = ContractSubFileService::getInstance()->authorityStaffs();
        if (!in_array($this->user['id'], $staff_lists)) {
            $params['department_id'] = $this->user['sys_department_id'];
        }

        $res = ContractSubFileService::getInstance()->getList($params);
        return $this->returnJson(ErrCode::$SUCCESS, 'ok', $res);
    }

    /**
     * 子文件编辑
     * @Permission(action='contract.template.manage.contract_subfile')
     * @return Response|ResponseInterface
     * @throws ValidationException
     */
    public function editAction()
    {
        $data = $this->request->get();
        Validation::validate($data, ContractSubFileService::validateEdit());
        $res = ContractSubFileService::getInstance()->edit($data, $this->user);
        return $this->returnJson($res['code'], $res['message']);
    }

    /**
     * 查看
     * @Permission(action='contract.template.manage.contract_subfile')
     * @return Response|ResponseInterface
     * @throws ValidationException
     */
    public function detailAction()
    {
        $data = $this->request->get();
        Validation::validate($data, ['id' => 'Required|IntGe:1']);
        $res = ContractSubFileService::getInstance()->getDetail($data);
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }

    /**
     * 更新条款 - 查看
     * @Permission(action='contract.template.manage.contract_subfile')
     * @api https://yapi.flashexpress.pub/project/133/interface/api/87977
     * @return Response|ResponseInterface
     * @throws ValidationException
     */
    public function updateDetailAction()
    {
        $data = $this->request->get();
        Validation::validate($data, ['id' => 'Required|IntGe:1']);
        $res = ContractSubFileService::getInstance()->getUpdateDetail($data);
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }

    /**
     * 更新条款 - 保存
     * @Permission(action='contract.template.manage.contract_subfile')
     * @api https://yapi.flashexpress.pub/project/133/interface/api/87980
     * @return Response|ResponseInterface
     * @throws ValidationException
     * @throws GuzzleException
     */
    public function updateAction()
    {
        $data = $this->request->get();
        Validation::validate($data, ['id' => 'Required|IntGe:1', 'contract_content' => 'Required|StrLenGe:1']);
        $res = ContractSubFileService::getInstance()->update($data, $this->user);
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }
}
