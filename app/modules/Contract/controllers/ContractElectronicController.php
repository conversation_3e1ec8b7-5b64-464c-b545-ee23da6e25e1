<?php

namespace App\Modules\Contract\Controllers;

use App\Library\Enums\ContractEnums;
use App\Library\Enums\DownloadCenterEnum;
use App\Library\ErrCode;
use App\Library\Validation\Validation;
use App\Library\Validation\ValidationException;
use App\Modules\Common\Services\DownloadCenterService;
use App\Modules\Contract\Services\BaseService;
use App\Modules\Contract\Services\ContractElectronicFlowService;
use App\Modules\Contract\Services\ContractElectronicService;
use App\Modules\Contract\Services\ContractTemplateVersionService;
use App\Util\RedisKey;
use Exception;
use Phalcon\Http\Response;
use Phalcon\Http\ResponseInterface;

class ContractElectronicController extends BaseController
{
    /**
     * 合同枚举
     *
     * @Token
     * @return Response|ResponseInterface
     * @throws Exception
     */
    public function formEnumsAction()
    {
        $service = reBuildCountryInstance(new ContractElectronicService());
        $res     = $service->formSelectEnums($this->user['sys_department_id']);
        return $this->returnJson(ErrCode::$SUCCESS, 'ok', $res);
    }

    /**
     * @Token
     * 创建合同初始化数据
     */
    public function contractDefaultAction()
    {
        $data = $this->request->get();
        Validation::validate($data, ['id' => 'Required|IntGe:1']);
        $res = ContractElectronicService::getInstance()->contractDefault($data);
        return $this->returnJson(ErrCode::$SUCCESS, 'ok', $res);
    }

    /**
     * @Permission(action='contract.electronic.manage')
     * @api https://yapi.flashexpress.pub/project/133/interface/api/73367
     * 电子合同制作列表
     */
    public function listAction()
    {
        $data = $this->request->get();
        Validation::validate($data, ContractElectronicService::$validate_list_search);
        $res = ContractElectronicService::getInstance()->getList($data, $this->user, BaseService::LIST_TYPE_APPLY);
        return $this->returnJson(ErrCode::$SUCCESS, 'ok', $res);
    }

    /**
     * 电子合同制作列表 - 导出
     * @Permission(action='contract.electronic.manage')
     * @api https://yapi.flashexpress.pub/project/133/interface/api/81716
     * @return Response|ResponseInterface
     * @throws ValidationException
     */
    public function exportAction()
    {
        $params = $this->request->get();
        $validate = ContractElectronicService::$validate_list_search;
        unset($validate['pageSize'], $validate['pageNum']);
        Validation::validate($params, $validate);
        // 加锁处理
        $lock_key = md5(RedisKey::CONTRACT_ELECTRONIC_EXPORT_LOCK_PREFIX . $this->user['id']);
        $res = $this->atomicLock(function () use ($params) {
            $total = ContractElectronicService::getInstance()->getTotal($params, $this->user, BaseService::LIST_TYPE_APPLY);
            if ($total > ContractEnums::CONTRACT_ELECTRONIC_EXPORT_MAX) {
                $result = DownloadCenterService::getInstance()->addDownloadCenter($this->user['id'], DownloadCenterEnum::CONTRACT_ELECTRONIC_EXPORT, $params);
                $result['data'] = [
                    'export_method' => DownloadCenterEnum::EXPORT_METHOD_ASYNC,
                    'file_url' => ''
                ];
            } else {
                $result = ContractElectronicService::getInstance()->export($params, $this->user, BaseService::LIST_TYPE_APPLY);
                $result['data'] = [
                    'export_method' => DownloadCenterEnum::EXPORT_METHOD_SYNC,
                    'file_url' => is_string($result['data']) ? $result['data'] : ''
                ];
            }
            return $result;
        }, $lock_key, 10);
        return $this->returnJson($res['code'] ?? ErrCode::$FREQUENT_VISIT_ERROR, $res['message'] ?? $this->t['sys_processing'], $res['data'] ?? []);
    }

    /**
     * 电子合同审核-合同内容审核-列表
     * @Permission(action='contract.electronic.audit')
     */
    public function auditListAction()
    {
        $data = $this->request->get();
        Validation::validate($data, ContractElectronicService::$validate_list_search);

        $res = ContractElectronicService::getInstance()->getList($data, $this->user, BaseService::LIST_TYPE_AUDIT);

        return $this->returnJson(ErrCode::$SUCCESS, 'ok', $res);
    }

    /**
     * 数据查询-电子合同查询-查询
     * @Permission(action='contract.electronic.query')
     */
    public function queryListAction()
    {
        $data = $this->request->get();
        Validation::validate($data, ContractElectronicService::$validate_list_search);
        $res = ContractElectronicService::getInstance()->getList($data, $this->user, BaseService::LIST_TYPE_SEARCH);

        return $this->returnJson(ErrCode::$SUCCESS, 'ok', $res);
    }

    /**
     * 数据查询-电子合同查询-导出
     * @Permission(action='contract.electronic.export')
     * @api https://yapi.flashexpress.pub/project/133/interface/api/81725
     * @return Response|ResponseInterface
     * @throws ValidationException
     */
    public function exportAllAction()
    {
        $params = $this->request->get();
        $validate = ContractElectronicService::$validate_list_search;
        unset($validate['pageSize'], $validate['pageNum']);
        Validation::validate($params, $validate);
        // 加锁处理
        $lock_key = md5(RedisKey::CONTRACT_ELECTRONIC_EXPORT_ALL_LOCK_PREFIX . $this->user['id']);
        $res = $this->atomicLock(function () use ($params) {
            $total = ContractElectronicService::getInstance()->getTotal($params, $this->user, BaseService::LIST_TYPE_SEARCH);
            if ($total > ContractEnums::CONTRACT_ELECTRONIC_EXPORT_MAX) {
                $result = DownloadCenterService::getInstance()->addDownloadCenter($this->user['id'], DownloadCenterEnum::CONTRACT_ELECTRONIC_EXPORT_ALL, $params);
                $result['data'] = [
                    'export_method' => DownloadCenterEnum::EXPORT_METHOD_ASYNC,
                    'file_url' => ''
                ];
            } else {
                $result = ContractElectronicService::getInstance()->export($params, $this->user, BaseService::LIST_TYPE_SEARCH);
                $result['data'] = [
                    'export_method' => DownloadCenterEnum::EXPORT_METHOD_SYNC,
                    'file_url' => is_string($result['data']) ? $result['data'] : ''
                ];
            }
            return $result;
        }, $lock_key, 10);
        return $this->returnJson($res['code'] ?? ErrCode::$FREQUENT_VISIT_ERROR, $res['message'] ?? $this->t['sys_processing'], $res['data'] ?? []);
    }

    /**
     * @Permission(action='contract.electronic.manage')
     * 电子合同制作默认接口
     */
    public function templateDetailAction()
    {
        $data = $this->request->get();
        Validation::validate($data, ['id' => 'Required|IntGe:1']);
        $res = ContractTemplateVersionService::getInstance()->getDetail($data);
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }

    /**
     * @Permission(action='contract.electronic.manage')
     * 合同制作新增
     */
    public function addAction()
    {
        $data = $this->request->get();
        $this->logger->info('电子合同新增参数' . json_encode($data, JSON_UNESCAPED_UNICODE));

        ContractElectronicService::validateSaveParams($data);

        $lock_key = md5('contract_electronic_add' . $this->user['id']);
        $res      = $this->atomicLock(function () use ($data) {
            return ContractElectronicService::getInstance()->add($data, $this->user);
        }, $lock_key, 30);

        return $this->returnJson($res['code'] ?? ErrCode::$FREQUENT_VISIT_ERROR, $res['message'] ?? $this->t['sys_processing'], $res['data'] ?? []);
    }

    /**
     * @Permission(action='contract.electronic.manage')
     * 合同制作编辑
     */
    public function editAction()
    {
        $data = $this->request->get();
        $this->logger->info('电子合同编辑参数' . json_encode($data, JSON_UNESCAPED_UNICODE));

        ContractElectronicService::validateSaveParams($data, true);

        $lock_key = md5('contract_electronic_edit' . $this->user['id']);
        $res      = $this->atomicLock(function () use ($data) {
            return ContractElectronicService::getInstance()->edit($data, $this->user);
        }, $lock_key, 30);

        return $this->returnJson($res['code'] ?? ErrCode::$FREQUENT_VISIT_ERROR, $res['message'] ?? $this->t['sys_processing'], $res['data'] ?? []);
    }

    /**
     * @Token
     * 查看合同内容
     */
    public function detailAction()
    {
        $data = $this->request->get();
        Validation::validate($data, ['id' => 'Required|IntGe:1']);
        $res = ContractElectronicService::getInstance()->editDetail($data['id']);
        return $this->returnJson($res['code'], $res['message'], $res['data'] ?? []);
    }

    /**
     * 电子合同制作-查看-合同内容审核记录
     * @Permission(action='contract.electronic.manage')
     */
    public function businessDetailAction()
    {
        $data = $this->request->get();
        Validation::validate($data, ['id' => 'Required|IntGe:1']);

        $res = ContractElectronicService::getInstance()->getDetail($data['id'], true);

        return $this->returnJson($res['code'], $res['message'], $res['data'] ?? []);
    }

    /**
     * 电子合同审核-合同内容审核详情
     * @Permission(action='contract.electronic.audit')
     * @api https://yapi.flashexpress.pub/project/133/interface/api/87785
     */
    public function getAuditDetailByBusinessAction()
    {
        $data = $this->request->get();
        Validation::validate($data, ['id' => 'Required|IntGe:1']);

        $res = ContractElectronicService::getInstance()->getDetail($data['id'], true, true);

        return $this->returnJson($res['code'], $res['message'], $res['data'] ?? []);
    }

    /**
     * 电子合同审核-客户签字审核-签字审核详情
     * @Permission(action='contract.electronic.sign_audit')
     * @api https://yapi.flashexpress.pub/project/133/interface/api/87788
     */
    public function getAuditDetailBySignAction()
    {
        $data = $this->request->get();
        Validation::validate($data, ['id' => 'Required|IntGe:1']);
        $res = ContractElectronicService::getInstance()->getSignInfoAuditDetail($data['id'], true);
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }

    /**
     * 电子合同制作-查看-合同签字审核记录
     * @Permission(action='contract.electronic.manage')
     * @api https://yapi.flashexpress.pub/project/133/interface/api/87779
     */
    public function signAuditDetailAction()
    {
        $data = $this->request->get();
        Validation::validate($data, ['id' => 'Required|IntGe:1']);
        $res = ContractElectronicService::getInstance()->getSignAuditDetail($data['id']);
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }

    /**
     * 其他合同-数据查询-电子合同查询-查看-合同签字审核记录
     * @Permission(action='contract.export.electronic.view')
     * @api https://yapi.flashexpress.pub/project/133/interface/api/87782
     */
    public function viewSignAuditDetailAction()
    {
        $data = $this->request->get();
        Validation::validate($data, ['id' => 'Required|IntGe:1']);
        $res = ContractElectronicService::getInstance()->getSignAuditDetail($data['id']);
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }

    /**
     * @Permission(action='contract.electronic.manage')
     * 发起商务审核
     */
    public function createBusinessAction()
    {
        $data = $this->request->get();
        Validation::validate($data, ['id' => 'Required|IntGe:1']);

        $res = ContractElectronicService::getInstance()->createBusiness($data['id'], $this->user);

        return $this->returnJson($res['code'], $res['message'], $res['data'] ?? []);
    }

    /**
     * 撤回商务审核-合同内容审核
     * @Permission(action='contract.electronic.manage')
     * @api https://yapi.flashexpress.pub/project/133/interface/api/87776
     */
    public function cancelBusinessAuditAction()
    {
        $data = $this->request->get();
        Validation::validate($data, ['id' => 'Required|IntGe:1']);
        $res = ContractElectronicService::getInstance()->businessAuditCancel($data['id'], $this->user);
        return $this->returnJson($res['code'], $res['message']);
    }

    /**
     * 数据查询-电子合同查询-查看-合同内容审核记录
     * @Permission(action='contract.electronic.electronic_query')
     */
    public function auditBusinessDetailAction()
    {
        $data = $this->request->get();
        Validation::validate($data, ['id' => 'Required|IntGe:1']);
        $res = ContractElectronicService::getInstance()->getDetail($data['id'], true);
        return $this->returnJson($res['code'], $res['message'], $res['data'] ?? []);
    }

    /**
     * 电子合同审核-合同内容审核-通过
     * @Permission(action='contract.electronic.audit')
     */
    public function businessApproveAction()
    {
        $id   = $this->request->get('id', 'int', 0);
        $note = $this->request->get('note', 'trim', '');
        Validation::validate(['id' => $id, 'note' => $note], ContractElectronicService::$validate_approve);

        $res = ContractElectronicService::getInstance()->businessApprove($id, $this->user, $note);

        return $this->returnJson($res['code'], $res['message'], $res['data'] ?? []);
    }

    /**
     * @Permission(action='contract.electronic.manage')
     * 合同确认接口
     */
    public function contractConfirmAction()
    {
        $data = $this->request->get();
        Validation::validate($data, ['id' => 'Required|IntGe:1']);

        $res = ContractElectronicService::getInstance()->confirmCompleted($data['id'], $this->user['id']);

        return $this->returnJson($res['code'], $res['message'], $res['data'] ?? []);
    }

    /**
     * @Permission(action='contract.electronic.manage')
     * 合同签约接口
     */
    public function contractSignAction()
    {
        $data = $this->request->get();
        Validation::validate($data, ['id' => 'Required|IntGe:1']);

        $res = ContractElectronicService::getInstance()->contractSign($data['id']);

        return $this->returnJson($res['code'], $res['message'], $res['data'] ?? []);
    }

    /**
     * 电子合同审核-合同内容审核-驳回
     * @Permission(action='contract.electronic.audit')
     */
    public function businessRejectAction()
    {
        $id   = $this->request->get('id', 'int', 0);
        $note = $this->request->get('note', 'trim', '');
        Validation::validate(['id' => $id, 'note' => $note], ContractElectronicService::$validate_approve);

        $res = ContractElectronicService::getInstance()->businessReject($id, $note, $this->user);

        return $this->returnJson($res['code'], $res['message'], $res['data'] ?? []);
    }

    /**
     * @Permission(action='contract.export.electronic.download')
     * 下载电子合同
     */
    public function downloadAction()
    {
        $data = $this->request->get();
        Validation::validate($data, ['id' => 'Required|IntGe:1']);

        $res = ContractElectronicService::getInstance()->getDetail($data['id']);
        return $this->returnJson($res['code'], $res['message'], $res['data'] ?? []);
    }

    /**
     * 查询员工信息
     * @Token
     * @api https://yapi.flashexpress.pub/project/133/interface/api/74322
     */
    public function getStaffByIdAction()
    {
        $data = $this->request->get();
        Validation::validate($data, ['staff_info_id' => 'Required|IntGe:1', 'staff_type' => 'Required|IntIn:1,2']);

        $res = ContractElectronicService::getInstance()->getStaffById($data['staff_info_id'], $data['staff_type']);
        return $this->returnJson(ErrCode::$SUCCESS, 'ok', $res);
    }

    /**
     * @Token
     * 作废不签约
     */
    public function signInvalidAction()
    {
        $id = $this->request->get('id', 'int', 0);
        Validation::validate(['id' => $id], ['id' => 'Required|IntGe:1']);

        $res = ContractElectronicService::getInstance()->Invalid($id, $this->user);

        return $this->returnJson($res['code'], $res['message'], $res['data'] ?? []);
    }

    /**
     * @Token
     * 作废并作废-不签约
     */
    public function signCancelAction()
    {
        $id = $this->request->get('id', 'int', 0);
        Validation::validate(['id' => $id], ['id' => 'Required|IntGe:1']);

        $res = ContractElectronicService::getInstance()->signCancel($id);

        return $this->returnJson($res['code'], $res['message'], $res['data'] ?? []);
    }

    /**
     * @Token
     * 获取fh网点信息
     */
    public function getFranchiseeStoreAction()
    {
        $store_id = $this->request->get('store_id');
        Validation::validate(['store_id' => $store_id], ['store_id' => 'Required|StrLenGe:1']);

        $res = ContractElectronicService::getInstance()->getStoreById($store_id);
        return $this->returnJson($res['code'], $res['message'], $res['data'] ?? []);
    }

    /**
     * @Token
     * @api https://yapi.flashexpress.pub/project/133/interface/api/79387
     * 发起签约客户枚举
     */
    public function signInitAction()
    {
        $res = ContractElectronicService::getInstance()->getDefaultCustomerType($this->user);
        return $this->returnJson(ErrCode::$SUCCESS, 'ok', $res ?? []);
    }

    /**
     * 获取发起签约的默认数据
     * @Permission(action='contract.electronic.manage')
     * @api https://yapi.flashexpress.pub/project/133/interface/api/86120
     */
    public function getSignInitDataAction()
    {
        $params = $this->request->get();

        Validation::validate($params, [
            'electronic_no' => 'Required|StrLenGe:1',
            'customer_type' => 'Required|IntEq:' . ContractEnums::SIGN_CUSTOMER_TYPE_100 . '|>>>:' . $this->t['sign_customer_type_error'],
        ]);

        $data = ContractElectronicService::getInstance()->signInitData($params['electronic_no']);
        return $this->returnJson(ErrCode::$SUCCESS, '', $data);
    }

    /**
     * 签约提交
     * @Permission(action='contract.electronic.manage')
     * @api https://yapi.flashexpress.pub/project/133/interface/api/86168
     */
    public function signInitiateV2Action()
    {
        $params = $this->request->get();
        $sign_order_type = ContractEnums::SIGN_ORDER_TYPE_PARTY_A . ',' . ContractEnums::SIGN_ORDER_TYPE_PARTY_B;
        Validation::validate($params, [
            'electronic_no' => 'Required|StrLenGe:1',
            'customer_type' => 'Required|IntEq:' . ContractEnums::SIGN_CUSTOMER_TYPE_100 . '|>>>:' . $this->t['sign_customer_type_error'],
            'sign_order_type' => 'Required|IntIn:' . $sign_order_type . '|>>>:' . $this->t->_('params_error', ['param' => 'sign_order_type']),
            'custom_contact_email' => 'Required|Regexp:/^.{1,}@{1}.{1,}\.{1}.{1,}$/|>>>:' . $this->t['sign_init_submit_001'],
            'custom_poa_item' => 'Required|ArrLenGeLe:1,5',
            'custom_poa_item[*].sign_name' => 'Required|StrLenGeLe:1,1000',
            'custom_poa_item[*].sign_email' => 'Required|Regexp:/^.{1,}@{1}.{1,}\.{1}.{1,}$/|>>>:' . $this->t['sign_init_submit_002'],
        ]);

        $lock_key = md5('electronic_contract_signInitiate_v2_' . $this->user['id']);
        $res = $this->atomicLock(function () use ($params) {
            return ContractElectronicService::getInstance()->signInitiateV2($params, $this->user);
        }, $lock_key, 20);
        return $this->returnJson($res['code'] ?? ErrCode::$FREQUENT_VISIT_ERROR, $res['message'] ?? $this->t['sys_processing'], $res['data'] ?? []);
    }

    /**
     * 重新发起签约
     * @Permission(action='contract.electronic.manage')
     * @api https://yapi.flashexpress.pub/project/133/interface/api/86177
     */
    public function resignAction()
    {
        $params = $this->request->get();

        Validation::validate($params, [
            'electronic_no' => 'Required|StrLenGe:1'
        ]);

        $lock_key = md5('electronic_contract_resign_' . $this->user['id']);
        $res = $this->atomicLock(function () use ($params) {
            return ContractElectronicService::getInstance()->resign($params['electronic_no'], $this->user);
        }, $lock_key, 20);
        return $this->returnJson($res['code'] ?? ErrCode::$FREQUENT_VISIT_ERROR, $res['message'] ?? $this->t['sys_processing'], $res['data'] ?? []);
    }

    /**
     * @Token
     * 发起签约
     */
    public function signInitiateAction()
    {
        $params = $this->request->get();
        Validation::validate($params, ['id' => 'Required|IntGe:1', 'customer_type' => 'Required|IntIn:1,2,3,4', 'store_name' => 'Required|StrLenGeLe:1,100', 'store_id' => 'StrLenGeLe:1,100']);

        $res = ContractElectronicService::getInstance()->signInitiate($params);
        return $this->returnJson($res['code'], $res['message'], $res['data'] ?? []);
    }

    /**
     * 复核签字详情
     * @Permission(action='contract.electronic.manage')
     * @api https://yapi.flashexpress.pub/project/133/interface/api/86180
     */
    public function reviewSignInfoAction()
    {
        $params = $this->request->get();

        Validation::validate($params, [
            'electronic_no' => 'Required|StrLenGe:1'
        ]);

        $res = ContractElectronicService::getInstance()->getReviewSignInfo($params['electronic_no'], $this->user);
        return $this->returnJson(ErrCode::$SUCCESS, 'success', $res);
    }

    /**
     * 复核签字-获取复核人小签
     * @Permission(action='contract.electronic.manage')
     * @api https://yapi.flashexpress.pub/project/133/interface/api/86192
     */
    public function getReviewerSignImgAction()
    {
        $res = ContractElectronicService::getInstance()->getReviewerSignImg($this->user);
        return $this->returnJson(ErrCode::$SUCCESS, 'success', $res);
    }

    /**
     * 提交每页复核小签
     * @Permission(action='contract.electronic.manage')
     * @api https://yapi.flashexpress.pub/project/133/interface/api/86195
     */
    public function submitPageSignAction()
    {
        $params = $this->request->get();

        Validation::validate($params, [
            'electronic_no' => 'Required|StrLenGe:1',
            'pdf_page_total' => 'Required|IntGe:1',
            'review_page_number' => 'Required|IntGe:1',
            'sign_img' => 'Required|Url',
        ]);

        $res = ContractElectronicService::getInstance()->submitPageSign($params, $this->user);
        return $this->returnJson($res['code'], $res['message']);
    }

    /**
     * BD复核签字-退回
     * @Permission(action='contract.electronic.manage')
     * @api https://yapi.flashexpress.pub/project/133/interface/api/86213
     */
    public function reviewSignReturnAction()
    {
        $params = $this->request->get();

        Validation::validate($params, [
            'electronic_no' => 'Required|StrLenGe:1',
            'pdf_page_total' => 'Required|IntGe:1',
            'review_page_number' => 'Required|IntGe:1',
            'return_reason' => 'Required|StrLenGeLe:1,500',
        ]);

        $lock_key = md5('electronic_contract_review_sign_return_' . $this->user['id']);
        $res = $this->atomicLock(function () use ($params) {
            return ContractElectronicService::getInstance()->reviewSignReturn($params, $this->user);
        }, $lock_key, 20);
        return $this->returnJson($res['code'] ?? ErrCode::$FREQUENT_VISIT_ERROR, $res['message'] ?? $this->t['sys_processing'], $res['data'] ?? []);
    }

    /**
     * BD复核签字-完成
     * @Permission(action='contract.electronic.manage')
     * @api https://yapi.flashexpress.pub/project/133/interface/api/86222
     */
    public function reviewSignCompletedAction()
    {
        $params = $this->request->get();

        Validation::validate($params, [
            'electronic_no' => 'Required|StrLenGe:1',
            'password' => 'Required|StrLen:6',
        ]);

        $lock_key = md5('electronic_contract_review_sign_completed_' . $this->user['id']);
        $res = $this->atomicLock(function () use ($params) {
            return ContractElectronicService::getInstance()->reviewSignCompleted($params, $this->user);
        }, $lock_key, 20);
        return $this->returnJson($res['code'] ?? ErrCode::$FREQUENT_VISIT_ERROR, $res['message'] ?? $this->t['sys_processing'], $res['data'] ?? []);
    }

    /**
     * 签字复核审批列表
     * @Permission(action='contract.electronic.sign_audit')
     * @api https://yapi.flashexpress.pub/project/133/interface/api/86261
     */
    public function signAuditListAction()
    {
        $params = $this->request->get();

        Validation::validate($params, [
            'pageSize' => 'Required|IntGt:0',   // 每页条数
            'pageNum' => 'Required|IntGt:0',  // 页码
            'flag' => 'Required|IntIn:1,2',// 处理状态
            'electronic_no' => 'Required', // 电子合同编号
            'contract_name' => 'Required', // 合同名称
            'customer_name' => 'Required', // 客户名称
            'created_id' => 'Required', // 创建人工号
        ]);

        $res = ContractElectronicService::getInstance()->getReviewSignAuditList($params, $this->user);
        return $this->returnJson(ErrCode::$SUCCESS, 'success', $res);
    }

    /**
     * 签字复核审批-驳回
     * @Permission(action='contract.electronic.sign_audit')
     * @api https://yapi.flashexpress.pub/project/133/interface/api/86264
     */
    public function signAuditRejectAction()
    {
        $params = $this->request->get();

        Validation::validate($params, [
            'electronic_no' => 'Required|StrLenGe:1', // 电子合同编号
            'remark' => 'Required|StrLenGeLe:1,500', // 驳回原因
        ]);

        $lock_key = md5('electronic_contract_sign_audit_reject_' . $this->user['id']);
        $res = $this->atomicLock(function () use ($params) {
            return ContractElectronicFlowService::getInstance()->reject($params['electronic_no'], $params['remark'], $this->user);
        }, $lock_key, 20);
        return $this->returnJson($res['code'] ?? ErrCode::$FREQUENT_VISIT_ERROR, $res['message'] ?? $this->t['sys_processing']);
    }

    /**
     * 签字复核审批-通过
     * @Permission(action='contract.electronic.sign_audit')
     * @api https://yapi.flashexpress.pub/project/133/interface/api/86270
     */
    public function signAuditPassAction()
    {
        $params = $this->request->get();

        Validation::validate($params, [
            'electronic_no' => 'Required|StrLenGe:1', // 电子合同编号
            'remark' => 'Required|StrLenGeLe:0,500', // 同意备注
        ]);

        $lock_key = md5('electronic_contract_sign_audit_pass_' . $this->user['id']);
        $res = $this->atomicLock(function () use ($params) {
            return ContractElectronicFlowService::getInstance()->approve($params['electronic_no'], $params['remark'], $this->user);
        }, $lock_key, 20);
        return $this->returnJson($res['code'] ?? ErrCode::$FREQUENT_VISIT_ERROR, $res['message'] ?? $this->t['sys_processing']);
    }

    /**
     * 签字复核详情-相关资料下载列表
     * @Permission(action='contract.electronic.sign_audit')
     * @api https://yapi.flashexpress.pub/project/133/interface/api/86831
     */
    public function downloadFileListAction()
    {
        $params = $this->request->get();

        Validation::validate($params, [
            'electronic_no' => 'Required|StrLenGe:1', // 电子合同编号
        ]);

        $res = ContractElectronicService::getInstance()->getDownloadRelatedFileList($params['electronic_no']);
        return $this->returnJson(ErrCode::$SUCCESS, $this->t['success'], $res);
    }

    /**
     * 电子合同KA账号获取
     * @description: 根据KA账号获取相关信息
     * @author: AI
     * @date: 2025-07-18
     * @Token
     * 空接口后续对接后使用
     */
    public function getKaInfoAction()
    {
        $params = $this->request->get();
        Validation::validate($params, [
            'ka_account' => 'Required|StrLenGeLe:0,100'.'|>>>:'.$this->t->_('contract.ka_account_empty'),
        ]);

        return $this->returnJson(ErrCode::$SUCCESS, $this->t['success'], []);
    }

    /**
     * 原OA电子合同编号合同信息获取
     * @description: 根据原合同编号获取合同信息并赋值到新合同
     * @author: AI
     * @date: 2025-07-18
     * @Token
     */
    public function getOriginalContractInfoAction()
    {
        $params = $this->request->get();

        // 参数验证
        Validation::validate($params, [
            'original_no' => 'Required|StrLenGeLe:1,100|>>>:'.$this->t->_('contract.original_contract_no_empty'),
            //请先填写原合同编号
            'template_no'          => 'Required|StrLenGeLe:1,100|>>>:'.$this->t->_('contract.template_no_empty'),
            //模版不能为空
        ]);

        // 调用服务获取原合同信息
        $data = ContractElectronicService::getInstance()->getOriginalContractInfo($params, $this->user);

        return $this->returnJson(ErrCode::$SUCCESS, $this->t['success'], (object)$data);
    }

    /**
     * 电子合同制作-查看签字邮箱信息
     * @Permission(action='contract.electronic.manage')
     * @api https://yapi.flashexpress.pub/project/133/interface/api/87794
     */
    public function viewSignEmailInfoAction()
    {
        $params = $this->request->get();
        Validation::validate($params, [
            'electronic_no' => 'Required|StrLenGe:1',
        ]);
        $data = ContractElectronicService::getInstance()->getSignEmailInfo($params['electronic_no'], $this->user);
        return $this->returnJson(ErrCode::$SUCCESS, $this->t['success'], $data);
    }

    /**
     * 电子合同制作-保存签字邮箱信息
     * @Permission(action='contract.electronic.manage')
     * @api https://yapi.flashexpress.pub/project/133/interface/api/87797
     */
    public function saveSignEmailInfoAction()
    {
        $params = trim_array($this->request->get());
        Validation::validate($params, [
            'electronic_no' => 'Required|StrLenGe:1|>>>:' . $this->t->_('params_error', ['param' => 'electronic_no']),
            'sign_email' => 'Required|Regexp:/^.{1,}@{1}.{1,}\.{1}.{1,}$/|>>>:' . $this->t['contract_electronic_save_sign_email_error_001'],
            'sign_key' => 'Required|StrLen:32|>>>:' . $this->t->_('params_error', ['param' => 'sign_key']),
        ]);
        $res = ContractElectronicService::getInstance()->saveSignEmailInfo($params, $this->user);
        return $this->returnJson($res['code'] ?? ErrCode::$FREQUENT_VISIT_ERROR, $res['message'] ?? $this->t['sys_processing']);
    }

}
