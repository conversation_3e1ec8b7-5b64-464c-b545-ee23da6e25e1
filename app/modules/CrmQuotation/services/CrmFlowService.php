<?php

namespace App\Modules\CrmQuotation\Services;

use App\Library\ApiClient;
use App\Library\Enums\CrmQuotationEnums;
use App\Library\Enums\GlobalEnums;
use App\Library\ErrCode;
use App\Library\Exception\BusinessException;
use App\Library\Validation\ValidationException;
use App\Models\oa\ContractQuotationModel;
use App\Modules\Contract\Models\Contract;
use App\Modules\Contract\Services\ContractConsumerService;
use App\Modules\CrmQuotation\Models\CrmQuotationApplyModel;
use App\Modules\CrmQuotation\Services\Coupon as CrmCouponServices;
use App\Modules\User\Services\UserService;
use App\Modules\Workflow\Models\WorkflowRequestModel;
use App\Modules\Workflow\Services\AbstractFlowService;
use App\Library\Enums;
use App\Modules\Workflow\Services\WorkflowServiceV2;
use Exception;
use GuzzleHttp\Client;
use GuzzleHttp\Exception\GuzzleException;
use Phalcon\Mvc\Model;

class CrmFlowService extends AbstractFlowService
{

    const IS_BULKY_PRODUCTS = 2; //是大件产品
    const NO_BULKY_PRODUCTS = 1; //非大件产品

    const BULKY_PRO_FIX_DISCOUNT = 1; //固定折扣
    const BULKY_PRO_DISCOUNT = 2;     //特殊折扣
    const LARGE_REMOTE_AREA_FEE = 2;  //大件偏远费

    //折扣有效期类型 1 与主合同保持一致 2 固定有效期 3 自定义有效期 4 与出账日期保持一致
    const  DISCOUNT_VALID_TYPE_1 = 1;
    const  DISCOUNT_VALID_TYPE_2 = 2;
    const  DISCOUNT_VALID_TYPE_3 = 3;
    const  DISCOUNT_VALID_TYPE_4 = 4;

    private $biz_type = Enums::WF_CRM_QUOTATION;


    /**
     * @param $id
     * @param $note
     * @param $user
     * @return mixed
     * @throws BusinessException
     * @throws GuzzleException
     * @throws ValidationException
     */
    public function approve($id, $note, $user)
    {
        $db = $this->getDI()->get('db_oa');
        try {
            $db->begin();
            $item = CrmQuotationApplyModel::findFirst([
                'conditions' => ' id = :id: ',
                'bind'       => ['id' => $id],
                'for_update' => true,
            ]);
            if (empty($item)) {
                throw new Exception('不存在此申请单');
            }
            if ($item->state != Enums::CONTRACT_STATUS_PENDING) {
                throw new ValidationException(static::$t->_('contract_has_been_withdrawal'));
            }

            $request = $this->getRequest($id);
            $result = (new WorkflowServiceV2())->doApprove($request, $user, $this->getWorkflowParams($item, $user), $note);

            if (!empty($result->approved_at)) {
                /**
                 * 10326需求(泰国)
                 * 申请人部门是pmd
                 * 或者(申请人部门是sales && 结算类型:定期结算)
                 * 这两种不同步KA (将在合同审批同步)
                 * 13062 新增Bulky Business Development部门,到合同审批后同步
                 * 14479 增加Retail management部门定结 = sales部门定结+shop部门定结
                 * v21017 新增 JVB Operations 部门, 到合同审批后同步(与BulkyBD部门一致)
                 */
                $sync_status        = 2;
                $contract_condition =
                    $item->apply_user_department_type == Enums::DEPARTMENT_TYPE_PMD
                    || ($item->apply_user_department_type == Enums::DEPARTMENT_TYPE_BULKY_BUSINESS_DEVELOPMENT && $item->settlement_type == Enums::SETTLEMENT_TYPE_REGULAR)
                    || ($item->apply_user_department_type == Enums::DEPARTMENT_TYPE_SALES && $item->settlement_type == Enums::SETTLEMENT_TYPE_REGULAR)
                    || ($item->apply_user_department_type == Enums::DEPARTMENT_TYPE_SHOP && $item->settlement_type == Enums::SETTLEMENT_TYPE_REGULAR)
                    || ($item->apply_user_department_type == Enums::DEPARTMENT_TYPE_JVB && $item->settlement_type == Enums::SETTLEMENT_TYPE_REGULAR);

                $contract_customer = in_array($item->customer_type_category, [Enums::CONSUMER_TYPE_KA]);
                //C码 一律不在在报价单步MS 在销售合同同步
                if ($item->customer_type_category != Enums::CONSUMER_TYPE_CRM) {
                    if (get_country_code() == GlobalEnums::TH_COUNTRY_CODE) {
                        if (!($contract_condition && $contract_customer)) {
                            $sync_status = 1;
                        }else{
                            // 这块逻辑 产品要求$sync_status!=1 才同步并验证
                            if (!empty($item->quoted_price_list_sn)){
                                $conditions = 'cq.quotation_no = :quotation_no: and c.status = :c_status:';
                                $bind = ['quotation_no' => $item->quoted_price_list_sn,'c_status' => Enums::CONTRACT_STATUS_APPROVAL];
                                $builder = $this->modelsManager->createBuilder();
                                $builder->from(['c' => Contract::class]);
                                $builder->columns(['c.status']);
                                $builder->leftJoin(ContractQuotationModel::class, 'c.id = cq.contract_id', 'cq');
                                $builder->Where($conditions, $bind);
                                $exists = $builder->getQuery()->execute()->toArray();
                                if (!empty($exists)) {
                                    $sync_status = 1;
                                }
                            }
                        }
                    } else {
                        $sync_status = 1;
                    }
                }

                // 是最终审批通过
                $bool = $item->i_update([
                    'state'       => Enums::CONTRACT_STATUS_APPROVAL,
                    'updated_at'  => date('Y-m-d H:i:s'),
                    'sync_status' => $sync_status,
                ]);
                if ($bool === false) {
                    throw new Exception('申请单审批通过失败');
                }

                [$local_start_date, $local_end_date] = $this->dealTime($item);

                //
                // 同步审批状态
                //
                $apiClient = new ApiClient('crm', '', 'syncQuotedPriceAuditStatus');
                $apiClient->setParams([
                    [
                        'quoted_price_list_sn' => $item->quoted_price_list_sn,
                        "disc_start_date"      => strtotime($local_start_date),
                        "disc_end_date"        => strtotime($local_end_date),
                        'related_id'           => (int)$item->id,
                        'status'               => 3,
                        'is_sync_ms'           => $sync_status == 1 ? 1 : 0,
                    ],
                ]);
                $result = $apiClient->execute();
                if (!isset($result['result'])) {
                    throw new Exception('同步审批状态失败');
                }
                //
                // 同步折扣状态
                //
                if ($sync_status == 1) {
                    $sync_result = $this->syncMS($item);
                    if (!$sync_result) {
                        ContractConsumerService::getInstance()->retry($item->quoted_price_list_sn, 1);
                    }
                }
                //
                // 同步coupon
                //
                //跟Coupon同步
                $coupons     = (new CrmCouponServices())->getNormal($item->quoted_price_list_sn);
                $paramCoupon = [];
                foreach ($coupons as $k => $coupon) {
                    $paramCoupon[$k]['coupon_validity_time'] = $coupon['coupon_expire'];
                    $paramCoupon[$k]['coupon_category']      = $coupon['coupon_type'];
                    $paramCoupon[$k]['code_number']          = $coupon['coupon_num'];
                }
                if (!empty($paramCoupon)) {
                    $syncParams = [
                        'customer_id'   => $item->customer_id,
                        'customer_type' => $item->customer_type_category,
                        'apply_key'     => $item->crmno,
                        'coupon_apply'  => $paramCoupon,
                    ];

                    $rpc = new ApiClient('coupon_rpc', '', 'backyardApplyCoupon', 'th');
                    $rpc->setParams([$syncParams]);
                    $data = $rpc->execute();
                    if (isset($data['result'])) {
                        $this->logger->info('coupon_rpc_request :' . json_encode($syncParams) . ":coupon_rpc_response:" . json_encode($data));
                    } else {
                        $this->logger->error('coupon_rpc_request :' . json_encode($syncParams) . ":coupon_rpc_response:" . json_encode($data));
                    }
                }
            }
            $db->commit();
        } catch (Exception $exception) {
            $db->rollBack();
            throw $exception;
        }

        return true;
    }

    /**
     * @param $id
     * @param $note
     * @param $user
     * @return mixed
     * @throws BusinessException
     * @throws ValidationException
     */
    public function reject($id, $note, $user)
    {
        $db = $this->getDI()->get('db_oa');
        try {
            $db->begin();
            $item = CrmQuotationApplyModel::findFirst([
                'conditions' => ' id = :id: ',
                'bind'       => ['id' => $id],
                'for_update' => true,
            ]);
            if (empty($item)) {
                throw new Exception('不存在此申请单');
            }
            if ($item->state != Enums::CONTRACT_STATUS_PENDING) {
                throw new ValidationException(static::$t->_('contract_has_been_withdrawal'));
            }
            $request = $this->getRequest($id);
            $result  = (new WorkflowServiceV2())->doReject($request, $user, $this->getWorkflowParams($item, $user),
                $note);
            if ($result === false) {
                throw new Exception('申请单审批通过失败');
            }
            $bool = $item->i_update([
                'state'      => Enums::CONTRACT_STATUS_REJECTED,
                'updated_at' => date('Y-m-d H:i:s'),
            ]);
            if ($bool === false) {
                throw new Exception('撤销失败');
            }

            $apiClient = new ApiClient('crm', '', 'syncQuotedPriceAuditStatus');
            $apiClient->setParams([
                [
                    'quoted_price_list_sn' => $item->quoted_price_list_sn,
                    'status'               => 4,
                    'is_sync_ms'           => 0,
                ],
            ]);
            $result = $apiClient->execute();
            if (!isset($result['result'])) {
                throw new Exception('同步审批状态失败');
            }


            $db->commit();
        } catch (Exception $exception) {
            $db->rollBack();
            throw $exception;
        }

        return true;
    }

    /**
     * 找最新的request
     *
     * @param $id
     * @return Model
     */
    public function getRequest($id)
    {
        return WorkflowRequestModel::findFirst(
            [
                'biz_type = :type: AND biz_value= :id: AND is_abandon = :is_abandon:',
                'bind'  => [
                    'type'       => Enums::WF_CRM_QUOTATION,
                    'id'         => $id,
                    'is_abandon' => GlobalEnums::WORKFLOW_ABANDON_STATE_NO,
                ],
                'order' => 'id desc',
            ]
        );
    }


    /**
     * @param $id
     * @param $note
     * @param $user
     * @return mixed
     * @throws BusinessException
     * @throws ValidationException
     */
    public function cancel($id, $note, $user)
    {
        $item = CrmQuotationApplyModel::findFirst([
            'conditions' => ' id = :id: ',
            'bind'       => ['id' => $id],
            'for_update' => true,
        ]);
        if (empty($item)) {
            throw new Exception('不存在此申请单');
        }

        $bool = (new WorkflowServiceV2())->doCancel($this->getRequest($id), $user,
            $this->getWorkflowParams($item, $user), $note);
        if ($bool) {
            $bool = $item->i_update([
                'state'      => Enums::CONTRACT_STATUS_CANCEL,
                'updated_at' => date('Y-m-d H:i:s'),
            ]);
        }
        return $bool;
    }

    /**
     * @param $id
     * @param $user
     * @return mixed
     * @throws BusinessException
     * @throws ValidationException
     */
    public function createRequest($id, $user)
    {
        $crmQuotation     = CrmQuotationApplyModel::findFirst([
            'conditions' => 'id = :id: ',
            'bind'       => [
                'id' => $id,
            ],
        ]);
        $data['id']       = $crmQuotation->id;
        $data['name']     = $crmQuotation->crmno . '审批申请';
        $data['biz_type'] = $this->biz_type;
        $data['flow_id']  = $this->getFlowId($crmQuotation);

        $workflowParams = $this->getWorkflowParams($crmQuotation, $user);
        $requestId      = (new WorkflowServiceV2())->createRequest($data, $user, $workflowParams);

        // 判断 workflowrequest current_node_auditor_id字段是否为空
        if (empty($requestId)) {
            $this->logger->warning('申请流异常啦, workflowParams=' . json_encode($workflowParams, JSON_UNESCAPED_UNICODE));
            return false;
        }

        return true;
    }

    public function getWorkflowParams($item, $user)
    {
        $key4 = $item->lowest_discount_rate;
        if ($item->quoted_price_type && $item->quoted_price_type != 3) { // 如果存在主产品 标准产品 对比
            if ($item->total_discount_rate > $item->lowest_discount_rate) {
                $key4 = $item->total_discount_rate;
            }
        }

        //优惠券
        $coupons = (new CrmCouponServices())->getNormal($item->quoted_price_list_sn);

        //优惠券数量
        $coupon_amount = empty($coupons) ? 0 : count($coupons);
        //判断职位类型
        //Branch supervisor（职位id=16）,District Manager （职位id=269）,Area Manager （职位id=79）
        $job_title = '';
        if ($item->job_title == Enums::CRM_JOB_TITLE_BS) {
            $job_title = 'BS';
        } elseif ($item->job_title == Enums::CRM_JOB_TITLE_DM) {
            $job_title = 'DM';
        } elseif ($item->job_title == Enums::CRM_JOB_TITLE_AM) {
            $job_title = 'AM';
        }
        //产品类型
        // quoted_price_type 1固定折扣 3特殊折扣
        // lowest_discount_rate 非空就是标准产品
        $product_type = 0;
        if (!empty($item->lowest_discount_rate) && $item->quoted_price_type == 1) {
            $product_type = 1;//标准产品-固定折扣
        } elseif (!empty($item->lowest_discount_rate) && $item->quoted_price_type == 3) {
            $product_type = 2;//标准产品-特殊折扣
        }
        //v12175 菲律宾审批流增加产品类型 COD , 结算方式, 物料
        $product_type_cod = $product_type_settlement_category = $product_type_material = 0;
        if ($item->cod_fee_rate > 0) {
            $product_type_cod = 1;
        }
        if (!empty($item->settlement_category)) {
            $product_type_settlement_category = 1;
        }
        if (!empty($item->material_value)) {
            $product_type_material = 1;
        }
        /***
         * 【OA-TH】报价管理：CRM报价审批流优化
         * https://l8bx01gcjr.feishu.cn/docs/doccnVw1eAkfodCkOkHKHKdAOIh#
         * Sales 部门、Shop部门、Network部门在报价类型是特殊折扣时需要判断折扣价格表字段
         * 13062 增加 Bulky Business Development部门
         */
        $min_price = 0;//折扣表最小折扣价值（存储的是泰铢*100到分的）
        if ($item->quoted_price_type == CrmQuotationEnums::QUOTED_PRICE_TYPE_3 && !is_null($item->price_list)) {
            $price_list_arr = json_decode($item->price_list, true);
            //寻找list_rule列表数据中inside_province_price、outside_province_price、same_province_price三个折扣价格的最小值
            $min_price_arr = [];
            if (isset($price_list_arr['list_rule']) && !empty($price_list_arr['list_rule'])) {
                // @api 字段注释参考文档：https://yapi.flashexpress.pub/project/33/interface/api/21986
                foreach ($price_list_arr['list_rule'] as $rule) {
                    if (isset($rule['inside_province_price']) && !empty($rule['inside_province_price'])) {
                        //省内价格存在
                        $min_price_arr [] = $rule['inside_province_price'];
                    }
                    if (isset($rule['outside_province_price']) && !empty($rule['outside_province_price'])) {
                        //省外价格存在
                        $min_price_arr [] = $rule['outside_province_price'];
                    }
                    if (isset($rule['same_province_price']) && !empty($rule['same_province_price'])) {
                        //同省价格存在
                        $min_price_arr [] = $rule['same_province_price'];
                    }
                }
                $min_price = min(array_unique($min_price_arr));
            }
            //兼容新数据, 20230615crm价格表改成小驼峰
            if ($min_price === 0) {
                if (isset($price_list_arr['listRule']) && !empty($price_list_arr['listRule'])) {
                    foreach ($price_list_arr['listRule'] as $rule) {
                        if (isset($rule['insideProvincePrice']) && !empty($rule['insideProvincePrice'])) {
                            //省内价格存在
                            $min_price_arr [] = $rule['insideProvincePrice'];
                        }
                        if (isset($rule['outsideProvincePrice']) && !empty($rule['outsideProvincePrice'])) {
                            //省外价格存在
                            $min_price_arr [] = $rule['outsideProvincePrice'];
                        }
                        if (isset($rule['sameProvincePrice']) && !empty($rule['sameProvincePrice'])) {
                            //同省价格存在
                            $min_price_arr [] = $rule['sameProvincePrice'];
                        }
                    }
                    $min_price = min(array_unique($min_price_arr));
                }
            }
        }
        //TH 新增「特殊价格表网点计费价格」
        $is_special = 0;
        if ($item->special_branch_price == 1 || $item->special_branch_price == 2) {
            $is_special = 1;
        }
        //查询提交人信息
        $us          = new UserService();
        $create_user = $us->getUserById($item->create_id);
        //折扣天数
        $valid_days = 0;
        if ($item->discount_valid_type == self::DISCOUNT_VALID_TYPE_2) {
            $valid_days = $item->valid_days;
        }
        if (in_array($item->discount_valid_type, [self::DISCOUNT_VALID_TYPE_3, self::DISCOUNT_VALID_TYPE_4])) {
            $valid_days = round((strtotime($item->invalid_time) - strtotime($item->valid_time)) / 3600 / 24);
        }

        return [
            'store_id'                         => $create_user->organization_id,
            'submitter_id'                     => $create_user->id,
            'create_id'                        => $item->create_id,
            //创建人id
            'job_title_type'                   => $job_title,
            //职位
            'product_type'                     => $product_type,
            //产品类型
            'KEY'                              => $item->apply_user_department_type,
            // 部门类型
            'KEY1'                             => $item->credit_period,
            // 信用期限
            'KEY2'                             => $item->quoted_price_type,
            // 报价类型 1 固定折扣 2 阶梯折扣 3 特殊折扣 4 固定折扣80%off活动 5 标准产品逆向-退件免费策略
            'KEY3'                             => $item->return_discount_rate,
            // 退件折扣率(菲律宾标准产品逆向-固定折扣率)
            'KEY4'                             => $key4,
            // 运费折扣率
            'KEY5'                             => $item->cod_fee_rate,
            // cod 手续费率
            'KEY6'                             => $item->calculation_method,
            // 计费方式
            'KEY7'                             => $coupon_amount,
            // 优惠券数量
            'KEY8'                             => $item->return_fee_method,
            // 返利形式
            'KEY9'                             => $item->is_bulky_products,
            //大件产品
            'KEY10'                            => $item->bulky_products_discount,
            //大件折扣项
            'KEY11'                            => $item->bp_discount_rate,
            //大件折扣值
            'KEY12'                            => $item->large_remote_area_fee,
            //大件偏远地区费
            'KEY13'                            => $item->faraway_fee,
            //偏远地区费用  -1是没有,否则就是有
            'KEY14'                            => $min_price,
            //价格表（特殊和阶梯使用）最小折扣价值
            'KEY15'                            => $item->return_fee_val,
            //返利金额折扣
            'product_type_cod'                 => $product_type_cod,
            //产品: cod
            'cod_fee_min_value'                => $item->cod_fee_min_value,
            // cod手续费最小值, 单位分
            'settlement_category'              => $item->settlement_category,
            // 结算方式:1-现场结; 2-定期结
            'product_type_settlement_category' => $product_type_settlement_category,
            //产品: 结算方式
            'settlement_period'                => $item->settlement_period,
            //结算周期  1.周结 3.半月结 4.月结
            'product_type_material'            => $product_type_material,
            //产品: 物料
            //泰国,菲律宾 固定折扣:折扣值, 阶梯折扣:价格表中最高的折扣值 特殊折扣:传测算折扣率(综合折扣率)  可以用KEY14取最低折扣值
            //马来 固定折扣:折扣值, 阶梯折扣:价格表中最高的折扣值 特殊折扣:价格表中最低的折扣值
            'lowest_discount_rate'             => $item->lowest_discount_rate,
            //标准产品-固定折扣 折扣值  (马来 特殊折扣 最小折扣值)
            'is_special'                       => $is_special,
            //是否存在"特殊价格表网点计费价格"
            'fruit_discount_rate'              => $item->fruit_discount_rate,
            //水果件折扣值-固定折扣
            'valid_days'                       => $valid_days,
            //折扣有效天数
            'free_strategy_discount'           => $item->free_strategy_discount,
            //标准产品逆向-退件免费策略 1
            'fruit_special_id'                 => $item->fruit_special_id,
            //水果件特殊折扣-价格表id
            'fruit_with_package_id'            => $item->fruit_with_package_id
            //水果件带包材折扣-价格表id
        ];
    }

    /**
     * @param null $model
     * @return int
     * @throws BusinessException
     */
    public function getFlowId($model = null)
    {
        $country_code = get_country_code();
        if ($country_code == GlobalEnums::PH_COUNTRY_CODE) {
            // v18650 统一走原sales部门的审批流
            if ($model->job_title == Enums::CRM_JOB_TITLE_SALES || $model->job_title == Enums::CRM_JOB_TITLE_SALES_2) {
                $flowId = Enums::WF_CRM_QUOTATION_NEW_WF_ID_PH_SALES;
            } elseif ($model->job_title == Enums::CRM_JOB_TITLE_SALES_SUPERVISOR) {
                $flowId = Enums::WF_CRM_QUOTATION_NEW_WF_ID_PH_SALES_SUPERVISOR;
            } elseif ($model->job_title == Enums::CRM_JOB_TITLE_SALES_MANAGER || $model->create_id == Enums::CRM_SUBMITTER_LIKE_SALES_MANAGER) {
                $flowId = Enums::WF_CRM_QUOTATION_NEW_WF_ID_PH_SALES_MANAGER;
            } else {
                $flowId = Enums::WF_CRM_QUOTATION_NEW_WF_ID_PH_SALES_MANAGER;
            }
        } elseif ($country_code == GlobalEnums::TH_COUNTRY_CODE) {
            //23年1月需求把sales,shop,network审批流从之前的50里拆出来走单独审批流,大件产品sales,shop,network也都放到这个新的审批流
            //50里残留的sales,shop,network条件不动,没有符合的条件不会走进去
            //等全部部门独立审批流后且历史审批流全走完, 可以把50审批流删掉,大件产品审批流 sales=>Enums::WF_CRM_QUOTATION_SALES_ID,shop=>Enums::WF_CRM_QUOTATION_SHOP_ID,network=>Enums::WF_CRM_QUOTATION_NETWORK_ID也可以删掉
            $flowId = Enums::WF_CRM_QUOTATION_WF_ID;
            if ($model->apply_user_department_type == Enums::DEPARTMENT_TYPE_BULKY_BUSINESS_DEVELOPMENT) {
                //bulky business development部门走单独的审批流
                $flowId = Enums::WF_CRM_QUOTATION_BULKY_BUSINESS;
            } elseif ($model->apply_user_department_type == Enums::DEPARTMENT_TYPE_NETWORK_BULKY) {
                //network bulky部门走单独的审批流
                $flowId = Enums::WF_CRM_QUOTATION_NETWORK_BULKY_ID;
            } elseif ($model->apply_user_department_type == Enums::DEPARTMENT_TYPE_PMD) {
                //pmd部门走单独审批流
                $flowId = Enums::WF_CRM_QUOTATION_PMD_ID;
            } elseif ($model->apply_user_department_type == Enums::DEPARTMENT_TYPE_SALES) {
                //sales部门走单独审批流
                $flowId = Enums::WF_CRM_QUOTATION_SALES_ALL_ID;
            } elseif ($model->apply_user_department_type == Enums::DEPARTMENT_TYPE_SHOP) {
                //sales部门走单独审批流
                $flowId = Enums::WF_CRM_QUOTATION_SHOP_ALL_ID;
            } elseif ($model->apply_user_department_type == Enums::DEPARTMENT_TYPE_NETWORK) {
                //sales部门走单独审批流
                $flowId = Enums::WF_CRM_QUOTATION_NETWORK_ALL_ID;
            } elseif ($model->apply_user_department_type == Enums::DEPARTMENT_TYPE_FLASH_HOME) {
                $flowId = Enums::WF_CRM_QUOTATION_HOME_ALL_ID;
            } elseif ($model->apply_user_department_type == Enums::DEPARTMENT_TYPE_JVB) {
                // JVB Operations 部门
                $flowId = Enums::WF_CRM_QUOTATION_JVB_OPERATIONS;
            }

            //大件的都整合到部门审批流里了, 等历史的走完了就删掉
            //elseif (self::IS_BULKY_PRODUCTS == $model->is_bulky_products) {
            //    //大件产品不同部门走不同审批流
            //    switch ($model->apply_user_department_type) {
            //        case Enums::DEPARTMENT_TYPE_SALES:
            //            $flowId = Enums::WF_CRM_QUOTATION_SALES_ID;
            //            break;
            //        case Enums::DEPARTMENT_TYPE_SHOP:
            //            $flowId = Enums::WF_CRM_QUOTATION_SHOP_ID;
            //            break;
            //        case Enums::DEPARTMENT_TYPE_NETWORK:
            //            $flowId = Enums::WF_CRM_QUOTATION_NETWORK_ID;
            //            break;
            //    }
            //}
        } elseif ($country_code == GlobalEnums::MY_COUNTRY_CODE) {
            switch ($model->apply_user_department_type) {
                case Enums::DEPARTMENT_TYPE_NETWORK:
                case Enums::DEPARTMENT_TYPE_SALES:
                    $flowId = Enums::WF_CRM_QUOTATION_NETWORK_ALL_ID;
                    break;
                case Enums::DEPARTMENT_TYPE_SHOP:
                    $flowId = Enums::WF_CRM_QUOTATION_SHOP_ALL_ID;
                    break;
                case Enums::DEPARTMENT_TYPE_PMD:
                    $flowId = Enums::WF_CRM_QUOTATION_PMD_ID;
                    break;
            }
        }

        if (!isset($flowId) || empty($flowId)) {
            throw new BusinessException('crm报价审批单创建失败-获取flow_id失败:model=' . json_encode($model->toArray(),
                    JSON_UNESCAPED_UNICODE), ErrCode::$QUOTATION_CREATE_WORKFLOW_ERROR);
        }

        return $flowId;
    }


    /**
     * @param $item
     * @param $user
     * @return mixed
     * @throws BusinessException
     * @throws ValidationException
     */
    public function recommit($item, $user)
    {
        $req = $this->getRequest($item->id);
        if (empty($req)) {
            throw new BusinessException("没有找到req=" . $item->id);
        }

        //老的改成被遗弃
        $req->is_abandon = GlobalEnums::WORKFLOW_ABANDON_STATE_YES;
        if ($req->save() === false) {
            throw new BusinessException("is_abandon 状态变更失败, 原因可能是" . get_data_object_error_msg($req));
        }

        return $this->createRequest($item->id, $user);
    }

    /**
     * 是否同步ms（根据历史计算方式判断）
     *
     * @param $item
     *
     * @return bool
     */
    public function isSyncMSBySettlementType($item): bool
    {
        // 如果现场结算 并且 存在信用期限不能同步
        if ($item->settlement_type == 1 && !empty($item->credit_period)) {
            return false;
        }

        return true;
    }

    /**
     * 同步折扣信息到MS
     *
     * @param $crm_quotation_obj
     * @param int $need_sync_crm 是否需要同步CRM 1.是 0.否
     * @param string $contract_date_start 关联合同的生效时间
     * @param string $contract_date_end 关联合同的到期时间
     * @return bool
     * @throws GuzzleException
     * @date 2021/10/12
     */
    public function syncMS(
        $crm_quotation_obj,
        $need_sync_crm = CrmQuotationEnums::NEED_SYNC_CRM_NO,
        $contract_date_start = '',
        $contract_date_end = ''
    ) {
        $country_code = get_country_code();
        if (!in_array($country_code, [GlobalEnums::TH_COUNTRY_CODE, GlobalEnums::PH_COUNTRY_CODE])) {
            return true;
        }
        if ($crm_quotation_obj->discount_valid_type == self::DISCOUNT_VALID_TYPE_1) {
            if (empty($contract_date_start) || empty($contract_date_end)) {
                $this->logger->warning('syncMs-date-error: quoted_price_list_sn=' . $crm_quotation_obj->quoted_price_list_sn . ' contract_date_start=' . $contract_date_start . ' contract_date_start=' . $contract_date_end);
                return false;
            }
            $now_time_th = $contract_date_start . ' 00:00:00';
            $end_time_th = $contract_date_end . ' 00:00:00';
        } else {
            [$now_time_th, $end_time_th] = $this->dealTime($crm_quotation_obj);
        }
        //16264新增了加盟商,调用新的同步接口,其他的还是老逻辑
        if ($country_code == GlobalEnums::TH_COUNTRY_CODE && $crm_quotation_obj->apply_user_department_type == Enums::DEPARTMENT_TYPE_FLASH_HOME) {
            $sync_discount_result = $this->syncMsHomeDiscount($crm_quotation_obj, $now_time_th, $end_time_th);
            if ($sync_discount_result == false) {
                return false;
            }
        } else {
            $sync_discount_result = $this->syncMsDiscount($crm_quotation_obj, $now_time_th, $end_time_th);
            if ($sync_discount_result == false) {
                return false;
            }
        }
        //是否需要同步CRM(审批通过直接同步MS的情况不需要再次同步CRM,通过合同后同步MS的情况,需要再次同步CRM)
        if ($need_sync_crm == CrmQuotationEnums::NEED_SYNC_CRM_YES) {
            $apiClient  = new ApiClient('crm', '', 'syncMsNotice');
            $crm_params = [
                [
                    'quoted_price_list_sn' => $crm_quotation_obj->quoted_price_list_sn,
                    'disc_start_date'      => strtotime($now_time_th),
                    'disc_end_date'        => strtotime($end_time_th),
                ],
            ];
            $apiClient->setParams($crm_params);
            $crm_result = $apiClient->execute();

            $log_info = [
                'svc_url'      => 'crm - syncMsNotice',
                'crm_params'   => $crm_params,
                'crm_response' => $crm_result,
            ];

            if (isset($crm_result['result'])) {
                $this->logger->info(['syncMsNotice-success' => $log_info,]);
            } else {
                $this->logger->warning(['syncMsNotice-error' => $log_info,]);
            }
        }

        return true;
    }

    /**
     * crm折扣同步ms
     * (16264需求之前的逻辑,改成单独方法调用,16264针对flash home增加了新的同步接口)
     *
     * @param $crmQuotationObj
     * @param $now_time_th
     * @param $end_time_th
     * @return bool
     * @throws GuzzleException
     * @date 2023/4/8
     */
    public function syncMsDiscount($crmQuotationObj, $now_time_th, $end_time_th)
    {
        $country_code = get_country_code();

        $channel = 3;
        switch ($crmQuotationObj->apply_user_department_type) {
            case 1:
            case 2:
                $channel = 1;
                break;
            case 3:
                $channel = 2;
                break;
            case 4:
                $channel = 0;
                break;
            case 7:
                $channel = 0;//【CRM/OA-TH】BDC网点报价审批流优化（优化方案） OA报价单同步（仅后端）修改与network部门保持一致
                break;
            case 8:
            case Enums::DEPARTMENT_TYPE_JVB:
                $channel = 6;
                break;
        }

        $publicParam = [
            'relatedId'            => (int)$crmQuotationObj->id,
            'clientId'             => $crmQuotationObj->customer_id,
            'customerTypeCategory' => (int)$crmQuotationObj->customer_type_category,
            'customerName'         => $crmQuotationObj->customer_name,
            'customerMobile'       => $crmQuotationObj->customer_mobile,
            'priceType'            => (int)$crmQuotationObj->price_type,
            'discStartDate'        => strtotime($now_time_th),
            'discEndDate'          => strtotime($end_time_th) + 86399,
            'validDates'           => (int)$crmQuotationObj->valid_days,
            //"channel"             => (int) 3,
            'channel'              => $channel,
            'staffInfoId'          => (int)$crmQuotationObj->create_id,
        ];
        $this->logger->info('freightDiscountRequest:参数：' . json_encode($publicParam,
                JSON_UNESCAPED_UNICODE) . ' - 部门类型：' . $crmQuotationObj->apply_user_department_type . ': - channel:' . $channel);
        $params['discountApplyList'] = [];
        if ($crmQuotationObj->valid_promise_num) { //折扣期内承诺单量
            $params['discountApplyList'][] = array_merge($publicParam, [
                'validDates'        => 999 * 30,
                'discEndDate'       => strtotime(date('Y-m-d 00:00:00',strtotime($now_time_th) + 86400 * 999 * 30)) - 1,
                'priceRuleCategory' => 6,
                'currentDisc'       => 0,
                'requestDisc'       => (float)$crmQuotationObj->valid_promise_num,
                'currentDiscStr'    => (string)0,
                'requestDiscStr'    => (string)$crmQuotationObj->valid_promise_num,
            ]);
        }
        // 20210723 同步MS新增判断 现场结算并且存在信用期限不同步MS(信用期限不同步)
        if ($crmQuotationObj->credit_period && $this->isSyncMSBySettlementType($crmQuotationObj)) { //信用期限
            $params['discountApplyList'][] = array_merge($publicParam, [
                'priceRuleCategory' => 5,
                'currentDisc'       => 0,
                'requestDisc'       => (float)$crmQuotationObj->credit_period,
                'currentDiscStr'    => (string)0,
                'requestDiscStr'    => (string)$crmQuotationObj->credit_period,
            ]);
        }
        if (bccomp($crmQuotationObj->cod_fee_rate, 0.00, 2)) { // cod 手续费率
            $params['discountApplyList'][] = array_merge($publicParam, [
                'priceRuleCategory' => 4,
                'currentDisc'       => 0,
                'requestDisc'       => (float)$crmQuotationObj->cod_fee_rate,
                'currentDiscStr'    => (string)0,
                'requestDiscStr'    => (string)$crmQuotationObj->cod_fee_rate,
            ]);
        }
        if (bccomp($crmQuotationObj->return_discount_rate, 0.00, 2)) { //退件折扣率,
            $params['discountApplyList'][] = array_merge($publicParam, [
                'priceRuleCategory' => 3,
                'currentDisc'       => 0,
                'requestDisc'       => (int)$crmQuotationObj->return_discount_rate,
                'currentDiscStr'    => (string)0,
                'requestDiscStr'    => (string)(int)$crmQuotationObj->return_discount_rate,
            ]);
        }
        if ($crmQuotationObj->quoted_price_type == 1 && bccomp($crmQuotationObj->lowest_discount_rate, 0.00,
                2)) { //运费折扣率
            // 固定折扣
            $params['discountApplyList'][] = array_merge($publicParam, [
                'priceRuleCategory' => 1,
                'currentDisc'       => 0,
                'requestDisc'       => (int)$crmQuotationObj->lowest_discount_rate,
                'currentDiscStr'    => (string)0,
                'requestDiscStr'    => (string)(int)$crmQuotationObj->lowest_discount_rate,
            ]);
        } else {
            if ($crmQuotationObj->quoted_price_type == 2 && $crmQuotationObj->price_list) {
                // 阶梯折扣
                $params['discountApplyList'][] = array_merge($publicParam, [
                    'priceRuleCategory'  => 11,
                    'currentDisc'        => 0,
                    'requestDisc'        => null,
                    'currentDiscStr'     => (string)0,
                    'requestDiscStr'     => null,
                    'tieredDiscountFrom' => json_decode($crmQuotationObj->price_list), // 阶梯价格表
                ]);
            } else {
                if ($crmQuotationObj->quoted_price_type == 3 && $crmQuotationObj->price_list) {
                    // 特殊折扣
                    $params['discountApplyList'][] = array_merge($publicParam, [
                        'priceRuleCategory' => 12,
                        'currentDisc'       => 0,
                        'requestDisc'       => null,
                        'currentDiscStr'    => (string)0,
                        'requestDiscStr'    => null,
                        'tariffInfoForm'    => json_decode($crmQuotationObj->price_list), // 特殊价格表
                    ]);
                }
            }
        }

        if ($crmQuotationObj->calculation_method) { // 计费方式 按体积重
            //【CRM/OA/MS-TH】申请计费方式优化（一期）增加了计费方式枚举, 增加crm和ms的对应关系
            //crm计费方式枚举转成ms的
            $calculation_method_disc       = CrmQuotationEnums::$calculation_method_relation[$crmQuotationObj->calculation_method];
            $params['discountApplyList'][] = array_merge($publicParam, [
                'priceRuleCategory' => CrmQuotationEnums::PRICE_RULE_CATEGORY_7, //计费方式 7
                'currentDisc'       => 0,
                'requestDisc'       => (int)$calculation_method_disc,
                'currentDiscStr'    => '0',
                'requestDiscStr'    => (string)(int)$calculation_method_disc,
            ]);
        }

        if ($crmQuotationObj->calculation_method == CrmQuotationEnums::CALCULATION_METHOD_VOLUME_WEIGHT && $crmQuotationObj->special_config == CrmQuotationEnums::CALCULATION_SPECIAL_CONFIG_WEIGHT) {
            $params['discountApplyList'][] = array_merge($publicParam, [
                'priceRuleCategory' => CrmQuotationEnums::PRICE_RULE_CATEGORY_9, //计费方式 9体积重计费特殊配置
                'currentDisc'       => 0,
                'requestDisc'       => (int)$crmQuotationObj->calculation_method_value,
                'currentDiscStr'    => '0',
                'requestDiscStr'    => (string)(int)$crmQuotationObj->calculation_method_value,
            ]);
        }
        //【CRM/OA/MS-TH】申请计费方式优化（一期） 泰国增加计费方式 按重量/尺寸 , 别的国家calculation_method不会传来这个值,所以暂时不做国家区分
        if ($crmQuotationObj->calculation_method == CrmQuotationEnums::CALCULATION_METHOD_WEIGHT_SIZE) {
            if ($crmQuotationObj->special_config == CrmQuotationEnums::CALCULATION_SPECIAL_CONFIG_WEIGHT) {
                $params['discountApplyList'][] = array_merge($publicParam, [
                    'priceRuleCategory' => CrmQuotationEnums::PRICE_RULE_CATEGORY_8, //计费方式=重量/尺寸时 8是重量
                    'currentDisc'       => 0,
                    'requestDisc'       => (int)$crmQuotationObj->calculation_method_value,
                    'currentDiscStr'    => '0',
                    'requestDiscStr'    => (string)(int)$crmQuotationObj->calculation_method_value,
                ]);
            } else {
                if ($crmQuotationObj->special_config == CrmQuotationEnums::CALCULATION_SPECIAL_CONFIG_WEIGHT_SIZE) {
                    $params['discountApplyList'][] = array_merge($publicParam, [
                        'priceRuleCategory' => CrmQuotationEnums::PRICE_RULE_CATEGORY_8, //计费方式=重量/尺寸时 8是重量
                        'currentDisc'       => 0,
                        'requestDisc'       => (int)$crmQuotationObj->calculation_method_value,
                        'currentDiscStr'    => '0',
                        'requestDiscStr'    => (string)(int)$crmQuotationObj->calculation_method_value,
                    ]);
                    $params['discountApplyList'][] = array_merge($publicParam, [
                        'priceRuleCategory' => CrmQuotationEnums::PRICE_RULE_CATEGORY_17, //计费方式=重量/尺寸时 17是尺寸
                        'currentDisc'       => 0,
                        'requestDisc'       => (int)$crmQuotationObj->calculation_method_size_value,
                        'currentDiscStr'    => '0',
                        'requestDiscStr'    => (string)(int)$crmQuotationObj->calculation_method_size_value,
                    ]);
                }
            }
        }

        // 偏远地区是否免运费
        if ($crmQuotationObj->faraway_fee != -1) {
            $params['discountApplyList'][] = array_merge($publicParam, [
                'priceRuleCategory' => 10,
                'currentDisc'       => 0,
                'requestDisc'       => (int)$crmQuotationObj->faraway_fee ? 5000 : 0,
                'currentDiscStr'    => (string)0,
                'requestDiscStr'    => (string)$crmQuotationObj->faraway_fee ? 5000 : 0,
            ]);
        }

        //14大件折扣
        if ($crmQuotationObj->is_bulky_products == self::IS_BULKY_PRODUCTS && $crmQuotationObj->bulky_products_discount == self::BULKY_PRO_FIX_DISCOUNT) {
            $params['discountApplyList'][] = array_merge($publicParam, [
                'priceRuleCategory' => 14,
                'currentDisc'       => 0,
                'requestDisc'       => (int)$crmQuotationObj->bp_discount_rate,
                'currentDiscStr'    => (string)0,
                'requestDiscStr'    => (string)(int)$crmQuotationObj->bp_discount_rate,
            ]);
        }
        //15大件特殊价格表
        if ($crmQuotationObj->is_bulky_products == self::IS_BULKY_PRODUCTS && $crmQuotationObj->bulky_products_discount == self::BULKY_PRO_DISCOUNT) {
            $params['discountApplyList'][] = array_merge($publicParam, [
                'priceRuleCategory' => 15,
                'currentDisc'       => 0,
                'requestDisc'       => null,
                'currentDiscStr'    => (string)0,
                'requestDiscStr'    => null,
                'tariffInfoForm'    => json_decode($crmQuotationObj->bp_price_list), // 特殊价格表

            ]);
        }
        //16大件偏远地区费
        if ($crmQuotationObj->large_remote_area_fee == self::LARGE_REMOTE_AREA_FEE) {
            $params['discountApplyList'][] = array_merge($publicParam, [
                'priceRuleCategory' => 16,
                'currentDisc'       => 0,
                'requestDisc'       => (int)0,
                'currentDiscStr'    => (string)0,
                'requestDiscStr'    => (string)0,
            ]);
        }
        //泰国新增"特殊价格表网点计费价格"
        if ($country_code == GlobalEnums::TH_COUNTRY_CODE) {
            //结算方式
            if ($crmQuotationObj->special_branch_price == 1 || $crmQuotationObj->special_branch_price == 2) {
                //1=客户价格 2=网点价格  , MS接口  0网点价格 1客户价格
                if ($crmQuotationObj->special_branch_price == 1) {
                    $type_transition = 1;
                } else {
                    if ($crmQuotationObj->special_branch_price == 2) {
                        $type_transition = 0;
                    }
                }
                $params['discountApplyList'][] = array_merge($publicParam, [
                    'priceRuleCategory' => 13,
                    'currentDisc'       => 0,
                    'requestDisc'       => (int)$type_transition,
                    'currentDiscStr'    => (string)0,
                    'requestDiscStr'    => (string)$type_transition,
                ]);
            }
            //水果件-固定折扣
            if ($crmQuotationObj->fruit_discount_rate > 0) {
                $params['discountApplyList'][] = array_merge($publicParam, [
                    'priceRuleCategory' => 27,
                    'currentDisc'       => 0,
                    'requestDisc'       => (int)$crmQuotationObj->fruit_discount_rate,
                    'currentDiscStr'    => (string)0,
                    'requestDiscStr'    => (string)(int)$crmQuotationObj->fruit_discount_rate,
                ]);
            }

            //水果件-特殊折扣【CRM/OA/MS-TH】新增水果件报价类型
            if ($crmQuotationObj->fruit_special_id > 0) {
                $params['discountApplyList'][] = array_merge($publicParam, [
                    'priceRuleCategory' => CrmQuotationEnums::SYNC_FLASH_HOME_CATEGORY_FRUIT_SPECIAL,
                    'currentDisc'       => 0,
                    'requestDisc'       => (int)$crmQuotationObj->fruit_special_id,
                    'currentDiscStr'    => (string)0,
                    'requestDiscStr'    => (string)(int)$crmQuotationObj->fruit_special_id,
                ]);
            }
            //水果件-带包材折扣【CRM/OA/MS-TH】新增水果件报价类型
            if ($crmQuotationObj->fruit_with_package_id > 0) {
                $params['discountApplyList'][] = array_merge($publicParam, [
                    'priceRuleCategory' => CrmQuotationEnums::SYNC_FLASH_HOME_CATEGORY_FRUIT_PACKAGE,
                    'currentDisc'       => 0,
                    'requestDisc'       => (int)$crmQuotationObj->fruit_with_package_id,
                    'currentDiscStr'    => (string)0,
                    'requestDiscStr'    => (string)(int)$crmQuotationObj->fruit_with_package_id,
                ]);
            }
        }
        /**
         * v12175增加结算方式,结算周期,信用期限,信用额度
         * java提供:
         * 结算方式:
         * price_rule_category = 23,
         * 现场结算   LOCALE_BALANCE(1),
         * 定期结算   REGULAR_BALANCE(2);
         *
         * 结算周期:
         * price_rule_category = 24,
         * 周        WEEK(1),
         * 半月      FIFTEEN_DAYS(3),
         * 月        MONTH(4);
         *
         * 信用期限:
         * price_rule_category = 25,
         *
         * 信用额度:
         * price_rule_category = 26;
         *
         * 内容字段放在 request_disc 中，类型全部为 Integer。
         *
         */
        if ($country_code == GlobalEnums::PH_COUNTRY_CODE) {
            //结算方式
            if ($crmQuotationObj->settlement_category) {
                $params['discountApplyList'][] = array_merge($publicParam, [
                    'priceRuleCategory' => 23,
                    'currentDisc'       => 0,
                    'requestDisc'       => (int)$crmQuotationObj->settlement_category,
                    'currentDiscStr'    => (string)0,
                    'requestDiscStr'    => (string)$crmQuotationObj->settlement_category,
                ]);
            }
            //结算周期
            if ($crmQuotationObj->settlement_period) {
                $params['discountApplyList'][] = array_merge($publicParam, [
                    'priceRuleCategory' => 24,
                    'currentDisc'       => 0,
                    'requestDisc'       => (int)$crmQuotationObj->settlement_period,
                    'currentDiscStr'    => (string)0,
                    'requestDiscStr'    => (string)$crmQuotationObj->settlement_period,
                ]);
            }
            //信用期限
            if ($crmQuotationObj->credit_period) {
                $params['discountApplyList'][] = array_merge($publicParam, [
                    'priceRuleCategory' => 25,
                    'currentDisc'       => 0,
                    'requestDisc'       => (int)$crmQuotationObj->credit_period,
                    'currentDiscStr'    => (string)0,
                    'requestDiscStr'    => (string)$crmQuotationObj->credit_period,
                ]);
            }
            //信用额度
            if ($crmQuotationObj->credit_line) {
                $params['discountApplyList'][] = array_merge($publicParam, [
                    'priceRuleCategory' => 26,
                    'currentDisc'       => 0,
                    'requestDisc'       => (int)$crmQuotationObj->credit_line,
                    'currentDiscStr'    => (string)0,
                    'requestDiscStr'    => (string)$crmQuotationObj->credit_line,
                ]);
            }

            //菲律宾退件免费策略
            if ($crmQuotationObj->free_strategy_discount) {
                $params['discountApplyList'][] = array_merge($publicParam, [
                    'priceRuleCategory' => 33,
                    'currentDisc'       => 0,
                    'requestDisc'       => (int)(($crmQuotationObj->free_strategy_rate) * 100),
                    'currentDiscStr'    => (string)0,
                    'requestDiscStr'    => (string)(int)(($crmQuotationObj->free_strategy_rate) * 100),
                ]);
            }

            //菲律宾 cod 手续费最小值 单位分
            if ($crmQuotationObj->cod_fee_min_value) {
                $params['discountApplyList'][] = array_merge($publicParam, [
                    'priceRuleCategory' => 42,
                    'currentDisc'       => 0,
                    'requestDisc'       => (int)$crmQuotationObj->cod_fee_min_value,
                    'currentDiscStr'    => (string)0,
                    'requestDiscStr'    => (string)$crmQuotationObj->cod_fee_min_value,
                ]);
            }
        }

        try {
            // 待同步的请求参数
            $this->logger->info('freightDiscountRequest:' . json_encode($params, JSON_UNESCAPED_UNICODE));

            // ms api https://yapi.flashexpress.pub/project/628/interface/api/70867
            $url         = env('fee_url');
            $api_address = $url . 'svc/api/discount/apply';
            $client      = new Client();
            $response    = $client->request('POST', $api_address, [
                'headers'     => ['Content-Type' => 'application/json'],
                'body'        => json_encode($params),
                'http_errors' => false,
            ]);
            //返回值判断
            $fle_return = $response->getBody()->getContents();
            $fle_return = is_string($fle_return) ? json_decode($fle_return, true) : '';

            // 同步响应参数
            if (isset($fle_return['code']) && $fle_return['code'] == ErrCode::$SUCCESS) {
                $this->logger->info('freightDiscountresponse:' . json_encode($fle_return, JSON_UNESCAPED_UNICODE));
            } else {
                $this->logger->notice('freightDiscountresponse:' . json_encode($fle_return, JSON_UNESCAPED_UNICODE));
                throw new BusinessException("CRM报价单同步MS失败, 详见日志, 报价单ID={$crmQuotationObj->id}, 报价单号={$crmQuotationObj->quoted_price_list_sn}",
                    ErrCode::$BUSINESS_ERROR);
            }
        } catch (BusinessException $e) {
            $this->logger->notice('报价单同步MS异常, message:' . $e->getMessage() . '; traceAsString:' . $e->getTraceAsString());
            return false;
        } catch (Exception $e) {
            $this->logger->error('报价单同步MS异常, message:' . $e->getMessage() . '; traceAsString:' . $e->getTraceAsString());
            return false;
        }

        return true;
    }

    /**
     * crm折扣同步ms(flash home部门)
     *
     * @param $crm_quotation_obj
     * @param $now_time_th
     * @param $end_time_th
     * @return bool
     * @throws GuzzleException
     * @date 2023/4/8
     */
    public function syncMsHomeDiscount($crm_quotation_obj, $now_time_th, $end_time_th)
    {
        //当前只有flash home部门调用新接口, 所以固定channel, 后续如果有其他部门也要调用,再做判断
        $channel                     = CrmQuotationEnums::SYNC_CHANNEL_FLASH_HOME;
        $public_param                = [
            'relatedId'            => (int)$crm_quotation_obj->id,
            'storeId'              => $crm_quotation_obj->bind_store_id,
            'clientId'             => $crm_quotation_obj->customer_id,
            'customerTypeCategory' => (int)$crm_quotation_obj->customer_type_category,
            'customerName'         => $crm_quotation_obj->customer_name,
            'customerMobile'       => $crm_quotation_obj->customer_mobile,
            'priceType'            => (int)$crm_quotation_obj->price_type,
            'discStartDate'        => strtotime($now_time_th),
            'discEndDate'          => strtotime($end_time_th) + 86399,
            'validDates'           => (int)$crm_quotation_obj->valid_days,
            'channel'              => $channel,
            'staffInfoId'          => (int)$crm_quotation_obj->create_id,
            'rebateDisc'           => (int)$crm_quotation_obj->rebate_disc,//返点比例
        ];
        $params['discountApplyList'] = [];
        $is_need_sync                = false;
        // 固定折扣
        if ($crm_quotation_obj->quoted_price_type == 1 && bccomp($crm_quotation_obj->lowest_discount_rate, 0.00,
                2) == 1) { //运费折扣率
            $is_need_sync                  = true;
            $params['discountApplyList'][] = array_merge($public_param, [
                'priceRuleCategory' => CrmQuotationEnums::SYNC_FLASH_HOME_CATEGORY_FREIGHT,
                'currentDisc'       => 0,
                'requestDisc'       => (int)$crm_quotation_obj->lowest_discount_rate,
            ]);
        }
        //大件折扣
        if ($crm_quotation_obj->is_bulky_products == self::IS_BULKY_PRODUCTS && $crm_quotation_obj->bulky_products_discount == self::BULKY_PRO_FIX_DISCOUNT) {
            $is_need_sync                  = true;
            $params['discountApplyList'][] = array_merge($public_param, [
                'priceRuleCategory' => CrmQuotationEnums::SYNC_FLASH_HOME_CATEGORY_BULKY,
                'currentDisc'       => 0,
                'requestDisc'       => (int)$crm_quotation_obj->bp_discount_rate,
            ]);
        }
        //水果件固定折扣值
        if ($crm_quotation_obj->fruit_discount_rate > 0) {
            $is_need_sync                  = true;
            $params['discountApplyList'][] = array_merge($public_param, [
                'priceRuleCategory' => CrmQuotationEnums::SYNC_FLASH_HOME_CATEGORY_FRUIT,
                'currentDisc'       => 0,
                'requestDisc'       => (int)$crm_quotation_obj->fruit_discount_rate,
            ]);
        }
        //水果件特殊折扣【CRM/OA/MS-TH】新增水果件报价类型
        if ($crm_quotation_obj->fruit_special_id > 0) {
            $is_need_sync                  = true;
            $params['discountApplyList'][] = array_merge($public_param, [
                'priceRuleCategory' => CrmQuotationEnums::SYNC_FLASH_HOME_CATEGORY_FRUIT_SPECIAL,
                'currentDisc'       => 0,
                'requestDisc'       => (int)$crm_quotation_obj->fruit_special_id,
            ]);
        }
        //水果件带包材折扣【CRM/OA/MS-TH】新增水果件报价类型
        if ($crm_quotation_obj->fruit_with_package_id > 0) {
            $is_need_sync                  = true;
            $params['discountApplyList'][] = array_merge($public_param, [
                'priceRuleCategory' => CrmQuotationEnums::SYNC_FLASH_HOME_CATEGORY_FRUIT_PACKAGE,
                'currentDisc'       => 0,
                'requestDisc'       => (int)$crm_quotation_obj->fruit_with_package_id,
            ]);
        }
        //flash home 特殊价格表36
        if ($crm_quotation_obj->quoted_price_type == CrmQuotationEnums::QUOTED_PRICE_TYPE_3 && $crm_quotation_obj->price_list) {
            $is_need_sync                  = true;
            $params['discountApplyList'][] = array_merge($public_param, [
                'priceRuleCategory' => CrmQuotationEnums::SYNC_FLASH_HOME_FH_SPECIAL_PRICE_LIST,
                'currentDisc'       => 0,
                'requestDisc'       => 0,
                'tariffInfoForm'    => json_decode($crm_quotation_obj->price_list),
            ]);
        }
        //flash home 是否免偏远地区费37
        if ($crm_quotation_obj->faraway_fee != -1) {
            $is_need_sync                  = true;
            $params['discountApplyList'][] = array_merge($public_param, [
                'priceRuleCategory' => CrmQuotationEnums::SYNC_FLASH_HOME_FH_EXCLUDE_UPCOUNTRY_ENABLED,
                'currentDisc'       => 0,
                'requestDisc'       => (int)$crm_quotation_obj->faraway_fee,
            ]);
        }
        //flash home COD手续费38
        if (bccomp($crm_quotation_obj->cod_fee_rate, 0.00, 2)) {
            $is_need_sync = true;
            $params['discountApplyList'][] = array_merge($public_param, [
                'priceRuleCategory' => CrmQuotationEnums::SYNC_FLASH_HOME_FH_COD_POUNDAGE,
                'currentDisc' => 0,
                'requestDisc' => (float)$crm_quotation_obj->cod_fee_rate,
            ]);
        }
        //flash home 计费方式39
        if ($crm_quotation_obj->calculation_method) {
            $is_need_sync = true;
            //【CRM/OA/MS-TH】申请计费方式优化（一期）增加了计费方式枚举, 增加crm和ms的对应关系
            //crm计费方式枚举转成ms的
            $calculation_method_disc       = CrmQuotationEnums::$calculation_method_relation[$crm_quotation_obj->calculation_method];
            $params['discountApplyList'][] = array_merge($public_param, [
                'priceRuleCategory' => CrmQuotationEnums::SYNC_FLASH_HOME_FH_WEIGHING_CATEGORY,
                'currentDisc'       => 0,
                'requestDisc'       => (int)$calculation_method_disc,
            ]);
        }
        //flash home重量或尺寸计费特殊配置40
        if ($crm_quotation_obj->calculation_method == CrmQuotationEnums::CALCULATION_METHOD_WEIGHT_SIZE) {
            if ($crm_quotation_obj->special_config == CrmQuotationEnums::CALCULATION_SPECIAL_CONFIG_WEIGHT) {
                $is_need_sync                  = true;
                $params['discountApplyList'][] = array_merge($public_param, [
                    'priceRuleCategory' => CrmQuotationEnums::SYNC_FLASH_HOME_FH_WEIGHT_OR_LWH_RESTRICT,
                    'currentDisc'       => 0,
                    'requestDisc'       => (int)$crm_quotation_obj->calculation_method_value,
                ]);
            }
        }
        //flash home体积重计费特殊配置41
        if ($crm_quotation_obj->calculation_method == CrmQuotationEnums::CALCULATION_METHOD_VOLUME_WEIGHT && $crm_quotation_obj->special_config == CrmQuotationEnums::CALCULATION_SPECIAL_CONFIG_WEIGHT) {
            $is_need_sync                  = true;
            $params['discountApplyList'][] = array_merge($public_param, [
                'priceRuleCategory' => CrmQuotationEnums::SYNC_FLASH_HOME_FH_VOLUME_WEIGHT_RESTRICT,
                'currentDisc'       => 0,
                'requestDisc'       => (int)$crm_quotation_obj->calculation_method_value,
            ]);
        }

        //flash home退件折扣率43
        if (bccomp($crm_quotation_obj->return_discount_rate, 0.00, 2)) {
            $is_need_sync = true;
            $params['discountApplyList'][] = array_merge($public_param, [
                'priceRuleCategory' => CrmQuotationEnums::SYNC_FLASH_HOME_FH_RETURN_DISCOUNT_RATE,
                'currentDisc'       => 0,
                'requestDisc'       => (int)$crm_quotation_obj->return_discount_rate
            ]);
        }
        $this->logger->info('freightDiscountRequest(flashHome):参数：' . json_encode($public_param) . ' - 部门类型：' . $crm_quotation_obj->apply_user_department_type . ': - channel:' . $channel);
        if ($is_need_sync) {
            try {
                $url         = env('fee_url');
                $api_address = $url . 'svc/api/discount/franchisee/apply';
                $client      = new Client();
                $response    = $client->request('POST', $api_address, [
                    'headers'     => ['Content-Type' => 'application/json'],
                    'body'        => json_encode($params),
                    'http_errors' => false,
                ]);
                //返回值判断
                $fee_return = $response->getBody()->getContents();
                $fee_return = is_string($fee_return) ? json_decode($fee_return, true) : '';
                if (isset($fee_return['code']) && $fee_return['code'] == ErrCode::$SUCCESS) {
                    $this->logger->info('freightDiscountRequest(flashHome):' . json_encode($params,
                            JSON_UNESCAPED_UNICODE) . ':freightDiscountresponse(flashHome):' . json_encode($fee_return,
                            JSON_UNESCAPED_UNICODE));
                } else {
                    $this->logger->warning('freightDiscountRequest(flashHome):' . json_encode($params,
                            JSON_UNESCAPED_UNICODE) . ':freightDiscountresponse(flashHome):' . json_encode($fee_return,
                            JSON_UNESCAPED_UNICODE));
                    return false;
                }
            } catch (Exception $e) {
                $this->logger->error('freightDiscountRequest(flashHome):' . json_encode($params,
                        JSON_UNESCAPED_UNICODE) .
                    ':message:' . json_encode($e->getMessage(), JSON_UNESCAPED_UNICODE) .
                    ':traceAsString:' . json_encode($e->getTraceAsString(), JSON_UNESCAPED_UNICODE));
            }
        }
        return true;
    }

    public function dealTime($crmQuotationObj)
    {
        $local_now_time = date('Y-m-d 00:00:00');

        $apply_valid_time = date('Y-m-d 00:00:00', strtotime($crmQuotationObj->valid_time));

        $actual_valid_time = $local_now_time > $apply_valid_time ? $local_now_time : $apply_valid_time;

        $local_valid_time = $local_now_time > $actual_valid_time ? $local_now_time : $actual_valid_time;
        if ($crmQuotationObj->discount_valid_type == self::DISCOUNT_VALID_TYPE_2) {
            // 加天数
            $local_invalid_time = date('Y-m-d 00:00:00', strtotime($local_valid_time) + 86400 * ($crmQuotationObj->valid_days - 1));
        } else {
            // 固定时间
            $invalid_time       = !empty($crmQuotationObj->invalid_time) ? date('Y-m-d 00:00:00', strtotime($crmQuotationObj->invalid_time)) : '';
            $local_invalid_time = !empty($invalid_time) ? $invalid_time : $local_valid_time;
        }

        return [$local_valid_time, $local_invalid_time];
    }

}

