<?php


namespace App\Modules\Workflow\Services;


use App\Library\Enums;
use App\Library\Exception\BusinessException;
use App\Modules\User\Services\UserService;

class WorkflowNodeRelation
{

    /**
     * 获取申请人部门
     * @param $request
     * @return mixed
     */
    public function getSubmitterDepartment($request)
    {
        return $request['department_id'];
    }

    /**
     * 获取美元
     * @param $request
     * @return int|mixed
     */
    public function getUSD($request)
    {
        return $request['currency'] == Enums\GlobalEnums::CURRENCY_USD ? $request['amount'] : 0;
    }

    /**
     * 获取泰铢
     * @param $request
     * @return int|mixed
     */
    public function getTHB($request)
    {
        return $request['currency'] == Enums\GlobalEnums::CURRENCY_THB ? $request['amount'] : 0;
    }


    /**
     * 获取人民币
     * @param $request
     * @return int|mixed
     */
    public function getCNY($request)
    {
        return $request['currency'] == Enums\GlobalEnums::CURRENCY_CNY ? $request['amount'] : 0;
    }

    /**
     * 获取币种类型
     * @param $request
     * @return int
     */
    public function getCurrency($request)
    {
        return intval($request['currency']);
    }

    /**
     * @param $request
     * @return mixed
     */
    public function getAmount($request)
    {
        return $request['amount'];
    }

    /**
     * @param $request
     * @return mixed
     */
    public function getStoreCategory($request)
    {
        return $request['store_category'];
    }

    /**
     * @param $request
     * @return mixed
     */
    public function getTotalAmount($request)
    {
        return $request['total_amount'];
    }

    /**
     * 比较申请人跟指定工号的职等
     * @param array $request 传入参数
     * 如果 [申请人的职等] 比 [指定工号的职等] 高, 则返回 1;
     * 如果 [申请人的职等] 与 [指定工号的职等] 相等, 则返回 0;
     * 如果 [申请人的职等] 比 [指定工号的职等] 低, 则返回 -1;
     * @return int
     * @throws BusinessException
     */
    public function compareJobLevel($request)
    {
        $staffInfoId = $request['staff_id'];
        $jobLevel = $request['job_title_level'];

        if (!isset($staffInfoId) || empty($staffInfoId)) {
            throw new BusinessException('缺参数');
        }

        $staffInfo = (new UserService())->getUserByIdInRbi($staffInfoId);

        if (!isset($staffInfo->job_title_level) || empty($staffInfo->job_title_level)) {
            throw new BusinessException('指定工号 {$staffInfoId} 职级为NULL');
        }

        if ($jobLevel > $staffInfo->job_title_level) {
            return 1;
        } else if ($jobLevel > $staffInfo->job_title_level) {
            return 0;
        } else {
            return -1;
        }
    }

    /**
     * 采购申请类型是否是广告类
     * @param $request
     * @return int
     */
    public function isAd($request)
    {
        return $request['apply_type'] == 2 ? 1 : 0;
    }

    /**
     * 采购申请类型是否是固定资产类
     * @param $request
     * @return int
     */
    public function isAsset($request)
    {
        return $request['apply_type'] == 3 ? 1 : 0;
    }

    /**
     * 业务付款，是否是网点（0总部，1网点）
     * @param $request
     * @return int
     */
    public function isStore($request)
    {
        if (empty($request['cost_store'])) {
            return 0;
        }
        return 1;
    }

    /**
     * 租房合同，返回租房类型
     */
    public function getContractType($request){
        return $request['contract_lease_type'];
    }

    public function getLang($request){
        return isset($request['contract_lang']) ? $request['contract_lang'] : $request['lang'];
    }

    public function getManagerApprove($request){
        return $request['need_manager_approve'] ?? '';
    }

    /**
     * 报销，是否是探亲费或团建费，1是探亲费/福利费或团建费
     * @param $request
     * @return mixed
     */
    public function isFamilyOrMorale($request){
        return $request['type'];
    }

    /**
     * 获取报销类型(报销实质)
     * @param $request
     * @return mixed
     */
    public function getReimbursementType($request)
    {
        return $request['type'];
    }

    /**
     * 获取company_id
     *
     * @param $request
     * @return mixed
     */
    public function getCompanyId($request)
    {
        return $request['company_id'];
    }

    /**
     * submitter_id
     *
     * @param $request
     * @return mixed
     */
    public function getSubmitterId($request)
    {
        return $request['submitter_id'];
    }

    /**
     * 普通付款类型 7 劳动外协
     * @param $request
     * @return mixed
     */
    public function getOrdinaryPaymentType($request)
    {
        return $request['type'];
    }

    /**
     * 供应商审批 获取供应商应用模块
     * @param $request
     * @return mixed
     */
    public function getVendorModules($request)
    {
        return $request['vendor_modules'];
    }

    public function getOnlyPurchaseAudit($request){
        return $request['only_purchase_audit'];
    }

    /**
     * 供应商审批 获取供应商采购类型
     * @param $request
     * @return mixed
     */
    public function getPurchaseType($request)
    {
        return $request['purchase_type'];
    }

    /**
     * 合同审批 获取转换汇率后的amount
     * @param $request
     * @return mixed
     */
    public function getExchangeAmount($request){
        return $request['exchange_amount'];
    }

    /**
     * 合同审批 获取合同类型
     * @param $request
     * @return mixed
     */
    public function getTemplateId($request){
        return $request['template_id'];
    }

    /**
     * 合同审批 获取合同介质: 纸质合同 / 电子合同
     *
     * @param $request
     * @return mixed
     */
    public function getContractStorageType($request)
    {
        return $request['contract_storage_type'];
    }

    /**
     * 合同审批 获取是否主合同
     * @param $request
     * @return mixed
     */
    public function getIsMaster($request){
        return $request['is_master'];
    }

    /**
     * @param $request
     * @return mixed
     *
     */
    public function getKEY($request)
    {
        return $request['KEY'];
    }

    /**
     * @param $request
     * @return mixed
     *
     */
    public function getKEY1($request)
    {
        return $request['KEY1'];
    }

    /**
     * @param $request
     * @return mixed
     *
     */
    public function getKEY2($request)
    {
        return $request['KEY2'];
    }

    /**
     * @param $request
     * @return mixed
     *
     */
    public function getKEY3($request)
    {
        return $request['KEY3'];
    }

    /**
     * @param $request
     * @return mixed
     *
     */
    public function getKEY4($request)
    {
        return $request['KEY4'];
    }

    /**
     * @param $request
     * @return mixed
     *
     */
    public function getKEY5($request)
    {
        return $request['KEY5'];
    }

    /**
     * @param $request
     * @return mixed
     *
     */
    public function getKEY6($request)
    {
        return $request['KEY6'];
    }


    /**
     * @param $request
     * @return mixed
     *
     */
    public function getKEY7($request)
    {
        return $request['KEY7'];
    }

    /**
     * @param $request
     * @return mixed
     *
     */
    public function getKEY8($request)
    {
        return $request['KEY8'];
    }

    /**
     * @param $request
     * @return mixed
     *
     */
    public function getKEY9($request)
    {
        return $request['KEY9'];
    }

    /**
     * @param $request
     * @return mixed
     *
     */
    public function getKEY10($request)
    {
        return $request['KEY10'];
    }

    /**
     * @param $request
     * @return mixed
     *
     */
    public function getKEY11($request)
    {
        return $request['KEY11'];
    }

    /**
     * @param $request
     * @return mixed
     *
     */
    public function getKEY12($request)
    {
        return $request['KEY12'];
    }

    /**
     * crm 偏远地区费用审批节点参数
     * @param $request
     * @return mixed
     *
     */
    public function getKEY13($request)
    {
        return $request['KEY13'];
    }

    /**
     * 价格表（特殊和阶梯使用）审批节点参数
     * @param $request
     * @return mixed
     *
     */
    public function getKEY14($request)
    {
        return $request['KEY14'];
    }
	
	/**
	 * 返利金额折扣
	 * @param $request
	 * @return mixed
	 *
	 */
	public function getKEY15($request)
	{
		return $request['KEY15'];
	}


    /**
     * CRM报价审批 获取职位类型 1.Branch supervisor 2.District Manager  3.Area Manager
     * @param $request
     * @return mixed
     */
    public function getJobTitleType($request){
        return $request['job_title_type'];
    }

    /**
     * CRM报价审批 获取产品类型 1.标准产品-固定折扣 2.标准产品-特殊折扣
     * @param $request
     * @return mixed
     */
    public function getProductType($request){
        return $request['product_type'];
    }

    /**
     * CRM报价审批 获取创建人id(申请人)
     * @param $request
     * @return mixed
     */
    public function getCreateId($request){
        return $request['create_id'];
    }

    public function getIsHaveWelfare($request){
        return $request['is_have_welfare'];
    }

    /**
     * CRM报价审批 cod产品类型 1.存在cod 0.不存在cod
     * @param $request
     * @return mixed
     * @date 2022/3/14
     */
    public function getProductCod($request){
        return $request['product_type_cod'];
    }

    /**
     * CRM报价审批 "结算方式"产品类型 1.存在"结算方式" 0.不存在
     * @param $request
     * @return mixed
     * @date 2022/3/14
     */
    public function getProductSettlement($request){
        return $request['product_type_settlement_category'];
    }

    /**
     * CRM报价审批 结算方式: 1-现场结; 2-定期结
     * @param $request
     * @return mixed
     * @date 2022/3/14
     */
    public function getSettlementCategory($request){
        return $request['settlement_category'];
    }

    /**
     * CRM报价审批 获取cod手续费最小值，单位分
     * @param $request
     * @return mixed
     * @date 2022/3/14
     */
    public function getCodFeeMinValue($request){
        return $request['cod_fee_min_value'];
    }

    /**
     * CRM报价审批 结算周期 1.周结 3.半月结 4.月结
     * @param $request
     * @return mixed
     * @date 2022/9/13
     */
    public function getSettlementPeriod($request)
    {
        return $request['settlement_period'];
    }

    /**
     * CRM报价审批 "物料"产品类型 1.存在"物料" 0.不存在
     * @param $request
     * @return mixed
     * @date 2022/3/14
     */
    public function getProductMaterial($request){
        return $request['product_type_material'];
    }

    /**
     * CRM报价审批 标准产品-固定折扣 折扣值
     * @param $request
     * @return mixed
     * @date 2022/3/14
     */
    public function getLowestDiscountRate($request){
        return $request['lowest_discount_rate'];
    }

    /**
     * CRM报价审批 特殊价格表网点计费价格
     * @param $request
     * @return mixed
     * @date 2022/3/14
     */
    public function getIsSpecial($request){
        return $request['is_special'];
    }

    /**
     * CRM报价审批 水果件特殊折扣-固定折扣
     * @param $request
     * @return mixed
     * @date 2022/3/14
     */
    public function getFruitDiscountRate($request)
    {
        return $request['fruit_discount_rate'];
    }

    /**
     * CRM报价审批 水果件特殊折扣-价格表id
     * @param $request
     * @return mixed
     * @date 2022/3/14
     */
    public function getFruitSpecialId($request)
    {
        return $request['fruit_special_id'];
    }

    /**
     * CRM报价审批 水果件带包材折扣-价格表id
     * @param $request
     * @return mixed
     * @date 2022/3/14
     */
    public function getFruitWithPackageId($request)
    {
        return $request['fruit_with_package_id'];
    }

    /**
     * CRM报价审批 折扣有效期
     * @param $request
     * @return mixed
     * */
    public function getValidDays($request)
    {
        return $request['valid_days'];
    }
    
    /**
     * CRM报价审批 标准产品逆向-退件免费策略
     * @param $request
     * @return mixed
     * */
    public function getFreeStrategyDiscount($request)
    {
        return $request['free_strategy_discount'];
    }

    /**
     * 13663【OA-ALL】采购申请单优化
     * 1. 如果pr单的明细行里，填写的barcode，有任意一个是“资产”的barcode，则“资产经理”节点，必须审批。
     * 2. 如果pr单的明细行里，没有填写barcode，或者填写的barcode，没有任意一个是“资产”的barcode，则无需“资产经理”节点审批，自动跳过。
     * 3. “资产经理”审批节点为或签，如果配置了多个，则其中一个人审批通过之后，则该节点审批通过。
     * @param $request
     * @return mixed
     */
    public function getHasAssetsBarcode($request)
    {
        return $request['has_asset_barcode'];
    }


    /**
     * 获取报销实质(预算科目)外包或外协
     * @param $request
     * @return mixed
     * @date 2022/8/4
     */
    public function getOutWorker($request)
    {
        return $request['out_worker'];
    }
    /**
     * 验收单类别
     * v13620
     * */
    public function getAcceptanceCate($request){
        return $request['acceptance_cate'];
    }

    /**
     * 报销实质为水电是否有罚款
     * @param $request
     * @return mixed
     * @date 2022/10/26
     * */
    public function getFine($request)
    {
        return $request['is_fine'];
    }

    /**
     * V16911 检测部门是否是Group Asset Management部门及其子部门下
     * @param $request
     * @return mixed
     */
    public function inGroupAssetManagementDepartment($request)
    {
        return $request['in_group_asset_management_department'];
    }

    /**
     * V16911 检测部门是否是IT部门及其子部门
     * @param $request
     * @return mixed
     */
    public function inItManagementDepartment($request)
    {
        return $request['in_it_department'];
    }

    /**
     * v20322 电子合同 是否是 主合同组合补充协议
     * @param $request
     * @return mixed
     */
    public function isCombinationSupplementalAgreement($request)
    {
        return $request['is_combination_supplemental_agreement'] ?? 0;
    }

    /**
     * v21791 申请人直线上级等于配置的CMO工号时该节点需要审批
     * @param $request
     * @return mixed
     */
    public function isCMO($request)
    {
        return $request['is_cmo'];
    }

    /**
     * 合同账期是否已超期
     * @param $request
     * @return mixed
     */
    public function isExpiredByContractAccountPeriod($request)
    {
        return $request['is_expired_by_account_period'] ?? 0;
    }

}