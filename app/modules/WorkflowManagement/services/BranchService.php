<?php

namespace App\Modules\WorkflowManagement\Services;

use App\Library\ApiClient;
use App\Library\Enums;
use App\Library\Enums\WorkflowBranchEnums;
use App\Library\Enums\WorkflowBranchOptionsCnfEnums;
use App\Models\backyard\SysDepartmentModel;
use App\Models\backyard\SysStoreTypeModel;
use App\Modules\Organization\Services\JobService;
use App\Modules\Transfer\Models\SettingEnvModel;
use App\Modules\AgencyPayment\Services\AgencyPaymentService;
use App\Modules\WorkflowManagement\Models\ByWorkflowModel;
use App\Repository\oa\BudgetObjectRepository;

class BranchService extends BaseNodeService
{
    private static $instance;

    private function __construct()
    {
    }

    /**
     * 获取实例
     * @return BranchService
     */
    public static function getInstance(): BranchService
    {
        if (!isset(self::$instance) || !(self::$instance instanceof self)) {
            self::$instance = new self;
        }
        return self::$instance;
    }

    /**
     * 获取全部的条件类型
     * @param int $search
     * @return array
     */
    public function getAllCondition(int $search = 0): array
    {
        //获取全部分支条件，全部分支条件 = 公用审批条件 + 指定类型审批独有的条件
        $allType = array_merge($this->getPublicConditions(),
            $this->getIndividualConditions(Enums\WorkflowManageEnums::SCOPE_ALL));
        $allType = array_column($allType, null, 'type');

        if (empty($search)) {
            return $allType;
        } else {
            return $allType[$search] ?? [];
        }
    }

    /**
     * 获取审批流分支条件
     */
    public function getWorkflowBranchCond($params): array
    {
        //获取分支条件
        $condition = $this->getCommonCondition($params);

        //获取申请人相关条件
        //$submitterCond = $this->getSubmitterCommonCond($params);

        return [
            'cond' => $condition,
            //'submitter_cond' => $submitterCond,
        ];
    }

    /**
     * 公用条件
     * @param $params
     * @return array[]
     */
    public function getCommonCondition($params): array
    {
        $id = $params['id'];

        //获取全部分支条件，全部分支条件 = 公用审批条件 + 指定类型审批独有的条件
        $result = array_merge($this->getPublicConditions(),
            $this->getIndividualConditions(Enums\WorkflowManageEnums::SCOPE_PART, $id));

        return $this->getBranchResponse($result);
    }

    /**
     * 配置申请人节点范围，该范围数据结构与审批条件一致
     * @param $params
     * @return array[]
     */
    private function getSubmitterCommonCond($params): array
    {
        $branchArray = [
            $this->getBelongToDepartment(),
            $this->getJobTitle(),
            $this->getJobTitleGrade(),
            $this->getRole(),
            $this->getBelongToStore(),
            $this->getStoreCategory(),
        ];

        return $this->getBranchResponse($branchArray);
    }

    /**
     * @description 返回特定结构的审批条件数组
     * @param array $branch_conditions_array 条件数组
     * @return array
     */
    private function getBranchResponse(array $branch_conditions_array = []): array
    {
        $array = array_map(function ($v) {
            return [
                'condition'  => [
                    'type' => $v['type'],
                    'name' => $v['name'],
                ],
                'operator'   => $v['operator'],
                'option_cnf' => $v['option_cnf'],
                'option'     => $v['option'],
                'unique_key' => $v['type'],
            ];
        }, $branch_conditions_array);

        return [
            'condition'  => array_column($array, 'condition'),
            'operator'   => array_column($array, 'operator', 'unique_key'),
            'option_cnf' => array_column($array, 'option_cnf', 'unique_key'),
            'option'     => array_column($array, 'option', 'unique_key'),
        ];
    }

    /**
     * @description 获取共用审批条件，所有审批都可以使用
     * @return array
     */
    private function getPublicConditions(): array
    {
        return [
            $this->getBelongToDepartment(),
            $this->getBelongToRegion(),
            $this->getBelongToPiece(),
            $this->getJobTitle(),
            $this->getJobTitleGrade(),
            $this->getSex(),
            $this->getOnJobState(),
            $this->getProbation(),
            $this->getRole(),
            $this->getNationality(),
            $this->getStoreCategory(),
            $this->getBelongToStore(),
            $this->isStoreManger(),
            $this->isPieceManger(),
            $this->isRegionManger(),
            $this->isOrgManger(),
            $this->getHireType(),
            $this->getPositionType(),
        ];
    }

    /**
     * @description 获取各个审批自有条件，仅各个审批条件自用。
     * 获取全部自用条件，主要是用于解析，并不能把该条件适用全部审批。
     *
     * @param $workflow_type
     * @param int $scope
     * @return array[]
     */
    private function getIndividualConditions(int $scope, $workflow_type = null): array
    {
        $config     = $this->getIndividualConditionsConfig();
        $conditions = [];
        if ($scope == Enums\WorkflowManageEnums::SCOPE_PART && !is_null($workflow_type)) {
            if (isset($config[$workflow_type])) {
                $conditions = $config[$workflow_type];
            }
        } else {
            $conditions = array_merge(...$config);
            $conditions = array_column($conditions, null, 'type');
        }

        return $conditions;
    }

    /**
     * @description 获取获取各个审批自有条件配置，追加各个审批自用的审批条件，在这里配置！！！
     * @return array
     */
    private function getIndividualConditionsConfig(): array
    {
        return [
            ByWorkflowModel::APPROVAL_TYPE_SALARY                       => [
                $this->getAmount(),
                $this->getCurrency(),
                $this->getJobTitleType(),
                $this->getBusinessLineType(),
                $this->getSalaryRange(),
                $this->getBelongDepartment(),
            ],
            ByWorkflowModel::APPROVAL_TYPE_LE                           => [
                //获取请假类型
                $this->getLeaveLineType(),
                //假期开始日期是否在申请日之前
                $this->getLeaveStartDate(),
                //请假-天数 //请假-天数
                $this->getLeaveDays(),
            ],
            ByWorkflowModel::APPROVAL_TYPE_GO                           => [
                //出差-外出天数
                $this->getDayNumWFCondition(),
            ],
            ByWorkflowModel::APPROVAL_TYPE_RN                           => [
                //获取车辆类型
                $this->getResignEnumVehicle(),
            ],
            ByWorkflowModel::APPROVAL_TYPE_OS                           => [
                $this->getOSType(),
                $this->getOSJobTitle(),
            ],
            ByWorkflowModel::APPROVAL_TYPE_YCBT                         => [
                //获取黄牌出差申请条件
                $this->bpDayNum(),
            ],
            ByWorkflowModel::APPROVAL_TYPE_ASSET_V2                     => [
                //获取申请人所属公司
                $this->getCompany(),
            ],
            ByWorkflowModel::APPROVAL_TYPE_REPORT                       => [
                //违规原因
                $this->getReportReasonAll(),
                //举报类型
                $this->getReportTypeAll(),
                //表单员工-所属部门
                $this->getBelongDepartment(),
                //表单-举报-被举报人所属网点类型
                $this->getReportFormBelongStoreCategory(),
                //表单-举报-被举报人所属网点
                $this->getReportFormBelongStore(),
                //表单-举报-被举报人职位
                $this->getReportFormJobTitle(),
                //表单-举报-被举报人职级
                $this->getReportFormJobTitleGrade(),
                //表单-举报-被举报人角色
                $this->getReportFormReportedRoles(),
                //表单-举报-是否为组织负责人
                $this->getReportFormReportedIsOrgManger(),
                //表单-举报-是否为系统自动举报
                $this->getReportFormReportedIsAuto(),
            ],
            ByWorkflowModel::APPROVAL_TYPE_MILEAGE                      => [
                //获取申请人所属网点主管是否打卡
                $this->isStoreManagerToWork(),
                //获取申请人所属片区经理是否打卡
                $this->isMileagePieceMgrToWork(),
            ],
            ByWorkflowModel::APPROVAL_TYPE_HC                           => [
                $this->getBelongDepartment(),
                $this->getFormBelongStore(),
                $this->getSubmitterMaxLevel(),
                $this->getHcSubmitterPosition(),
                $this->getHcFormReasonType(),
            ],
            ByWorkflowModel::APPROVAL_TYPE_QUITCLAIM                    => [
                $this->getQuitclaimPaymentMethod(),
            ],
            ByWorkflowModel::APPROVAL_TYPE_REINSTATEMENT                => [
                //获取停职原因
                $this->getFormOfSuspensionReason(),
                //表单-恢复在职-申请恢复工号所属部门
                $this->getResumeJobFormSubmitDepartment(),
                //表单-恢复在职-申请恢复工号所属网点类型
                $this->getResumeJobFormSubmitStoreCategory(),
                //表单-恢复在职-申请恢复工号所属网点
                $this->getResumeJobFormSubmitStore(),
                //表单-恢复在职-申请恢复工号职位
                $this->getResumeJobFormSubmitJobTitle(),
                //表单-恢复在职-申请恢复工号职级
                $this->getResumeJobFormSubmitJobTitleGrade(),
                //表单-恢复在职-申请恢复工号角色
                $this->getResumeJobFormSubmitRole(),
                //表单-恢复在职-申请恢复在职工号雇佣类型
                $this->getResumeJobFormSubmitHireType(),
            ],
            ByWorkflowModel::APPROVAL_TYPE_FLEET                        => [
                // 加班车类型条件
                $this->getOvertimeBusType(),
                // 是否有测试标记
                $this->getSpecialMark(),
            ],
            ByWorkflowModel::APPROVAL_TYPE_OVERTIME                     => [
                // ot 审批类型
                $this->getOTApprovalType(),
                //当月累计小时
                $this->getOtDurationPH(),
                //当周累计小时
                $this->getOtDurationPercent(),
                //所在网点当天人效
                $this->getStaffEffectRate(),
            ],
            ByWorkflowModel::APPROVAL_TYPE_PA                           => [
                //kit 处罚类型
                $this->getPenaltyApprovalType(),
            ],
            ByWorkflowModel::APPROVAL_TYPE_REEMPLOYMENT                 => [
                $this->getBelongDepartment(WorkflowBranchEnums::BRANCH_CATEGORY_REEMPLOYMENT_FORM_DEPARTMENT,
                    '重新雇佣-部门'),
                $this->getJobTitle(WorkflowBranchEnums::BRANCH_CATEGORY_REEMPLOYMENT_FORM_JOB_TITLE, '重新雇佣-职位'),
                $this->getJobTitleGrade(WorkflowBranchEnums::BRANCH_CATEGORY_REEMPLOYMENT_FORM_JOB_TITLE_GRADE,
                    '重新雇佣-职级'),
            ],
            ByWorkflowModel::APPROVAL_TYPE_COMPANY_TERMINATION_CONTRACT => get_country_code() == Enums\GlobalEnums::MY_COUNTRY_CODE ? [
                //3天内同一网点解约个人代理人数
                $this->getForm3DayStoreTerminationNum(),
                //公司解约个人代理-员工问题解约类型
                $this->getFormTerminationTypePersonalAgency(),
            ] : [$this->getForm3DayStoreTerminationNum()],
            ByWorkflowModel::APPROVAL_TYPE_WMS_V2                       => [
                //获取申请人所属公司
                $this->getCompany(),
            ],
            ByWorkflowModel::APPROVAL_TYPE_JT                           => [
                $this->getFormOfBeforeDepartment(),
                $this->getFormOfBeforeJobTitle(),
                $this->getFormOfBeforeDepartmentLevel(),
                $this->getFormOfBeforeJobTitleGrade(),
                $this->getFormOfBeforePosition(),
                $this->getFormOfAfterPosition(),
                $this->getFormOfAfterHireType(),
            ],
            ByWorkflowModel::APPROVAL_TYPE_JT_STAGE_TWO                 => [
                $this->getFormOfAfterDepartment(),
                $this->getFormOfAfterPosition(),
                $this->getFormOfAfterJobTitle(),
                $this->getFormOfAfterDepartmentLevel(),
                $this->getFormOfAfterJobTitleGrade(),
                $this->getFormOfWeatherPieceConsistency(),
                $this->getFormOfWeatherRegionConsistency(),
                $this->getFormOfWeatherDepartmentConsistency(),
                $this->getFormOfWeatherTheJobTitleConsistency(),
            ],
            ByWorkflowModel::APPROVAL_TYPE_SUSPEND_WORK                 => [
                // 暂停接单原因
                $this->getSuspendWorkReasonType(WorkflowBranchEnums::BRANCH_CATEGORY_SUSPEND_WORK_FORM_REASON_TYPE,
                    self::$t->_('branch_category_' . WorkflowBranchEnums::BRANCH_CATEGORY_SUSPEND_WORK_FORM_REASON_TYPE)),
                // 所属部门
                $this->getBelongDepartment(WorkflowBranchEnums::BRANCH_CATEGORY_SUSPEND_WORK_FORM_DEPARTMENT,
                    self::$t->_('branch_category_' . WorkflowBranchEnums::BRANCH_CATEGORY_SUSPEND_WORK_FORM_DEPARTMENT)),
                // 所属网点类型
                $this->getStoreCategory(WorkflowBranchEnums::BRANCH_CATEGORY_SUSPEND_WORK_FORM_STORE_CATEGORY,
                    self::$t->_('branch_category_' . WorkflowBranchEnums::BRANCH_CATEGORY_SUSPEND_WORK_FORM_STORE_CATEGORY)),
                // 所属网点
                $this->getBelongToStore(WorkflowBranchEnums::BRANCH_CATEGORY_SUSPEND_WORK_FORM_STORE,
                    self::$t->_('branch_category_' . WorkflowBranchEnums::BRANCH_CATEGORY_SUSPEND_WORK_FORM_STORE)),
                // 职位
                $this->getJobTitle(WorkflowBranchEnums::BRANCH_CATEGORY_SUSPEND_WORK_FORM_JOB_TITLE,
                    self::$t->_('branch_category_' . WorkflowBranchEnums::BRANCH_CATEGORY_SUSPEND_WORK_FORM_JOB_TITLE)),
                // 同网点停职人数
                $this->getStoreSuspensionNum(WorkflowBranchEnums::BRANCH_CATEGORY_SUSPEND_WORK_FORM_STORE_SUSPEND_NUM,
                    self::$t->_('branch_category_' . WorkflowBranchEnums::BRANCH_CATEGORY_SUSPEND_WORK_FORM_STORE_SUSPEND_NUM)),
            ],
            ByWorkflowModel::APPROVAL_TYPE_OA_AGENCY_PAYMENT => [
                //表单-是否接口集成的数据
                $this->getIsIntegrated(),
                //表单-费用部门
                $this->getCostDepartment(),
                //表单-费用所属公司
                $this->getCostCompany(),
                //表单-费用类型
                $this->getCostType(),
                //表单-应付总金额（含VAT含WHT）
                $this->getPayableAmount(),
                //表单-实付总金额（含VAT不含WHT）
                $this->getAmountTotalActually(),
            ],
            ByWorkflowModel::APPROVAL_TYPE_HUB_OS_AT         => [
                //表单-工作网点对应的网点类型
                $this->getStoreCategory(WorkflowBranchEnums::BRANCH_CATEGORY_OS_AT_STORE_CATEGORY,
                    self::$t->_('branch_category_' . WorkflowBranchEnums::BRANCH_CATEGORY_OS_AT_STORE_CATEGORY)),
            ],
            //培训发布学习计划
            ByWorkflowModel::APPROVAL_TYPE_SCHOOL_PLAN_SEND => [
                $this->learningPlanType(),
            ],
            //停职申请-条件按 表单所属部门
            ByWorkflowModel::APPROVAL_TYPE_SUSPENSION_STATE   => [
                $this->getBelongDepartment(),
            ],
            //费用预提
            ByWorkflowModel::APPROVAL_TYPE_EXPENSE_WITHHOLDING => [
                //表单-费用所属公司
                $this->getCostCompany(),
                //表单-费用部门
                $this->getCostDepartment(),
                //表单-预算科目
                $this->getBudgetAccount(),
                //表单-预提金额
                $this->getPreWithdrawalAmount(),
            ],
        ];
    }

    /**
     * @description 获取全部带输入框的条件
     * @param $total_conditions
     * @return array
     */
    public function getTotalConditionsWithInput($total_conditions): array
    {
        $conditionsCnf = array_column($total_conditions, 'option_cnf', 'type');
        $conditions = array_map(function ($k, $v) {
            return isset($v['unit_type']) &&
                in_array($v['unit_type'], [WorkflowBranchOptionsCnfEnums::UNIT_TYPE_INPUT, WorkflowBranchOptionsCnfEnums::UNIT_TYPE_INPUT_NUMBER])
                ? $k
                : "";
        }, array_keys($conditionsCnf), array_values($conditionsCnf));
        return array_values(array_filter($conditions));
    }

    /**
     * 获取暂停接单原因
     * @param int $branch_type
     * @param string $branch_name
     * @return array
     */
    public function getSuspendWorkReasonType(
        int $branch_type = WorkflowBranchEnums::BRANCH_CATEGORY_SUSPEND_WORK_FORM_REASON_TYPE,
        string $branch_name = ''
    ): array {
        if (empty($branch_name)) {
            $branch_name = self::$t->_('branch_category_' . WorkflowBranchEnums::BRANCH_CATEGORY_SUSPEND_WORK_FORM_REASON_TYPE);
        }
        $suspend_work_reason_type = (new SettingEnvModel())->getSetVal('suspend_work_reason_type');
        $suspend_work_reason_type = $suspend_work_reason_type ? json_decode($suspend_work_reason_type, true) : [];
        $option                   = [];
        foreach ($suspend_work_reason_type as $k => $v) {
            $option[] = [
                'key'   => $this->getTranslation()->_($v),
                'value' => $k,
            ];
        }
        return [
            'type'       => $branch_type,
            'name'       => $branch_name,
            'operator'   => [
                self::getOperatorEqual(),
                self::getOperatorNotEqual(),
                self::getOperatorContain(),
                self::getOperatorNotContain(),
            ],
            'option_cnf' => BranchOptionsConfigService::getInstance()->getNormalOptionsCnf(),
            'option'     => $option,
        ];
    }

    /**
     * 获取同网点编制的停职数
     * @param int $branch_type
     * @param string $branch_name
     * @return array
     */
    public function getStoreSuspensionNum(
        int $branch_type = WorkflowBranchEnums::BRANCH_CATEGORY_SUSPEND_WORK_FORM_STORE_SUSPEND_NUM,
        string $branch_name = ''
    ): array {
        if (empty($branch_name)) {
            $branch_name = self::$t->_('branch_category_' . WorkflowBranchEnums::BRANCH_CATEGORY_SUSPEND_WORK_FORM_STORE_SUSPEND_NUM);
        }

        return [
            'type'       => $branch_type,
            'name'       => $branch_name,
            'operator'   => [
                self::getOperatorEqual(),
                self::getOperatorContain(),
                self::getOperatorGt(),
                self::getOperatorGe(),
                self::getOperatorLt(),
                self::getOperatorLe(),
            ],
            'option_cnf' => BranchOptionsConfigService::getInstance()->getInputNumberOptionsCnf(),
            'option'     => [],
        ];
    }


    /**
     * 获取所属部门
     * @return array
     */
    public function getBelongToDepartment(): array
    {
        return [
            'type'       => WorkflowBranchEnums::BRANCH_CATEGORY_BELONG_DEPARTMENT,
            'name'       => self::$t->_('applicant_organization'),//'申请人所属组织',
            'operator'   => [
                self::getOperatorEqual(),
                self::getOperatorContain(),
                self::getOperatorBelongTo(),
                self::getOperatorNotEqual(),
                self::getOperatorNotContain(),
                self::getOperatorNotBelongTo(),
            ],
            'option_cnf' => BranchOptionsConfigService::getInstance()->getDepartmentOptionsCnf(),
            'option'     => [],
        ];
    }

    /**
     * 获取职位
     * @param int $branch_type
     * @param string $branch_name
     * @return array
     */
    public function getJobTitle(
        int $branch_type = WorkflowBranchEnums::BRANCH_CATEGORY_JOB_TITLE,
        string $branch_name = ''
    ): array {
        if (empty($branch_name)) {
            $branch_name = self::$t->_('applicant_position');
        }

        return [
            'type'       => $branch_type,
            'name'       => $branch_name,
            'operator'   => [
                self::getOperatorEqual(),
                self::getOperatorContain(),
                self::getOperatorNotEqual(),
                self::getOperatorNotContain(),
            ],
            'option_cnf' => BranchOptionsConfigService::getInstance()->getJobTitleOptionsCnf(),
            'option'     => [],
        ];
    }

    /**
     * @description 获取职级
     * @param int $branch_type
     * @param string $branch_name
     * @return array
     */
    public function getJobTitleGrade(
        int $branch_type = WorkflowBranchEnums::BRANCH_CATEGORY_JOB_GRADE,
        string $branch_name = ''
    ): array {
        $grade = [];
        foreach (range(12, 24) as $v) {
            $grade[] = [
                'key'   => 'F' . $v,
                'value' => $v,
            ];
        }
        $grade = array_merge([['key' => 'F0', 'value' => 0]], $grade);
        if (empty($branch_name)) {
            $branch_name = self::$t->_('applicant_job_grade');
        }

        return [
            'type'       => $branch_type,
            'name'       => $branch_name,
            'operator'   => [
                self::getOperatorEqual(),
                self::getOperatorContain(),
                self::getOperatorGt(),
                self::getOperatorGe(),
                self::getOperatorLt(),
                self::getOperatorLe(),
            ],
            'option_cnf' => BranchOptionsConfigService::getInstance()->getNormalOptionsCnf(),
            'option'     => $grade,
        ];
    }

    /**
     * 获取性别
     * @return array
     */
    public function getSex(): array
    {
        return [
            'type'       => WorkflowBranchEnums::BRANCH_CATEGORY_SEX,
            'name'       => self::$t->_('view_staff_sex'), // '性别',
            'operator'   => [
                self::getOperatorEqual(),
            ],
            'option_cnf' => BranchOptionsConfigService::getInstance()->getNormalOptionsCnf(),
            'option'     => [
                [
                    'key'   => static::$t->_('staff_sex.1'), //男
                    'value' => Enums\StaffInfoEnums::STAFF_SEX_MALE,
                ],
                [
                    'key'   => static::$t->_('staff_sex.2'), //女
                    'value' => Enums\StaffInfoEnums::STAFF_SEX_FEMALE,
                ],
            ],
        ];
    }

    /**
     * 获取在职状态 1=在职 2=离职 3=停职 , 不包含待离职状态
     * @return array
     */
    public function getOnJobState(): array
    {
        return [
            'type'       => WorkflowBranchEnums::BRANCH_CATEGORY_ON_JOB_STATE,
            'name'       => self::$t->_('view_work_status'), //'在职状态',
            'operator'   => [
                self::getOperatorEqual(),
                self::getOperatorContain(),
                self::getOperatorNotEqual(),
                self::getOperatorNotContain(),
            ],
            'option_cnf' => BranchOptionsConfigService::getInstance()->getNormalOptionsCnf(),
            'option'     => [
                [
                    'key'   => self::$t->_('view_work_status.1'),//在职
                    'value' => Enums\StaffInfoEnums::STAFF_STATE_IN,
                ],
                [
                    'key'   => self::$t->_('view_work_status.2'),//离职
                    'value' => Enums\StaffInfoEnums::STAFF_STATE_LEAVE,
                ],
                [
                    'key'   => self::$t->_('view_work_status.3'), //停职
                    'value' => Enums\StaffInfoEnums::STAFF_STATE_STOP,
                ],
            ],
        ];
    }

    /**
     * 获取试用期
     * @return array
     */
    public function getProbation(): array
    {
        return [
            'type'       => WorkflowBranchEnums::BRANCH_CATEGORY_PROBATION,
            'name'       => self::$t->_('view_probation_status'),//'试用期',
            'operator'   => [
                self::getOperatorEqual(),
            ],
            'option_cnf' => BranchOptionsConfigService::getInstance()->getNormalOptionsCnf(),
            'option'     => [
                [
                    'key'   => self::$t->_('view_yes'),// '是',
                    'value' => WorkflowBranchEnums::OPTION_YES,
                ],
                [
                    'key'   => self::$t->_('view_no'),//'否',
                    'value' => WorkflowBranchEnums::OPTION_NO,
                ],
            ],
        ];
    }

    /**
     * @description 获取角色
     * @param int $branch_type
     * @param string $branch_name
     * @return array
     */
    public function getRole(
        int $branch_type = WorkflowBranchEnums::BRANCH_CATEGORY_ROLE,
        string $branch_name = ''
    ): array {
        if (empty($branch_name)) {
            $branch_name = self::$t->_('view_applicants_role'); //申请人角色
        }

        return [
            'type'       => $branch_type,
            'name'       => $branch_name,
            'operator'   => [
                self::getOperatorContain(),
                self::getOperatorNotContain(),
            ],
            'option_cnf' => BranchOptionsConfigService::getInstance()->getStaffRolesOptionsCnf(),
            'option'     => [],
        ];
    }

    /**
     * 获取国籍
     * @return array
     */
    public function getNationality(): array
    {
        $nationalityEnums = $this->getNationalityEnums();
        return [
            'type'       => WorkflowBranchEnums::BRANCH_CATEGORY_NATIONALITY,
            'name'       => self::$t->_('view_applicants_nationality'),//'国籍',
            'operator'   => [
                self::getOperatorEqual(),
                self::getOperatorContain(),
                self::getOperatorNotEqual(),
                self::getOperatorNotContain(),
            ],
            'option_cnf' => BranchOptionsConfigService::getInstance()->getNormalOptionsCnf(),
            'option'     => $nationalityEnums,
        ];
    }

    /**
     * 获取网点类型
     * @param int $branch_type
     * @param string $branch_name
     * @return array
     */
    public function getStoreCategory(
        int $branch_type = WorkflowBranchEnums::BRANCH_CATEGORY_STORE_CATEGORY,
        string $branch_name = ''
    ): array {
        $types     = SysStoreTypeModel::find([
            'columns' => 'id,code',
        ])->toArray();
        $typesList = array_map(function ($v) {
            return [
                'key'   => $v['code'],
                'value' => $v['id'],
            ];
        }, $types);
        usort($typesList, function ($a, $b) {
            return $a['value'] - $b['value'];
        });
        if (empty($branch_name)) {
            $branch_name = self::$t->_('view_branch_type'); //所属网点类型
        }

        return [
            'type'       => $branch_type,
            'name'       => $branch_name,
            'operator'   => [
                self::getOperatorEqual(),
                self::getOperatorContain(),
                self::getOperatorNotEqual(),
                self::getOperatorNotContain(),
            ],
            'option_cnf' => BranchOptionsConfigService::getInstance()->getNormalOptionsCnf(),
            'option'     => $typesList,
        ];
    }

    /**
     * 获取所属网点
     * @param int $branch_type
     * @param string $branch_name
     * @return array
     */
    public function getBelongToStore(
        int $branch_type = WorkflowBranchEnums::BRANCH_CATEGORY_BELONG_STORE,
        string $branch_name = ''
    ): array {
        if (empty($branch_name)) {
            $branch_name = self::$t->_('view_branch_store'); //所属网点
        }

        return [
            'type'       => $branch_type,
            'name'       => $branch_name,//'申请人所属网点',
            'operator'   => [
                self::getOperatorEqual(),
                self::getOperatorContain(),
                self::getOperatorNotEqual(),
                self::getOperatorNotContain(),
            ],
            'option_cnf' => BranchOptionsConfigService::getInstance()->getStoreOptionsCnf(),
            'option'     => [],
        ];
    }

    /**
     * 是否为网点负责人
     * @return array
     */
    public function isStoreManger(): array
    {
        return [
            'type'       => WorkflowBranchEnums::BRANCH_CATEGORY_IS_STORE_MGR,
            'name'       => self::$t->_('workflow_branch_11'), // '是否为网点负责人',
            'operator'   => [
                self::getOperatorEqual(),
            ],
            'option_cnf' => BranchOptionsConfigService::getInstance()->getNormalOptionsCnf(),
            'option'     => [
                [
                    'key'   => self::$t->_('view_yes'),// '是',
                    'value' => WorkflowBranchEnums::OPTION_YES,
                ],
                [
                    'key'   => self::$t->_('view_no'),//'否',
                    'value' => WorkflowBranchEnums::OPTION_NO,
                ],
            ],
        ];
    }

    /**
     * 是否为片区负责人
     * @return array
     */
    public function isPieceManger(): array
    {
        return [
            'type'       => WorkflowBranchEnums::BRANCH_CATEGORY_IS_PIECE_MGR,
            'name'       => self::$t->_('workflow_branch_12'), // '是否为片区负责人',
            'operator'   => [
                self::getOperatorEqual(),
            ],
            'option_cnf' => BranchOptionsConfigService::getInstance()->getNormalOptionsCnf(),
            'option'     => [
                [
                    'key'   => self::$t->_('view_yes'),// '是',
                    'value' => WorkflowBranchEnums::OPTION_YES,
                ],
                [
                    'key'   => self::$t->_('view_no'),//'否',
                    'value' => WorkflowBranchEnums::OPTION_NO,
                ],
            ],
        ];
    }

    /**
     * 是否为大区负责人
     * @return array
     */
    public function isRegionManger(): array
    {
        return [
            'type'       => WorkflowBranchEnums::BRANCH_CATEGORY_IS_REGION_MGR,
            'name'       => self::$t->_('workflow_branch_13'), // '是否为大区负责人',
            'operator'   => [
                self::getOperatorEqual(),
            ],
            'option_cnf' => BranchOptionsConfigService::getInstance()->getNormalOptionsCnf(),
            'option'     => [
                [
                    'key'   => self::$t->_('view_yes'),// '是',
                    'value' => WorkflowBranchEnums::OPTION_YES,
                ],
                [
                    'key'   => self::$t->_('view_no'),//'否',
                    'value' => WorkflowBranchEnums::OPTION_NO,
                ],
            ],
        ];
    }

    /**
     * @description 是否为组织负责人
     * @param int $branch_type
     * @param string $branch_name
     * @return array
     */
    public function isOrgManger(
        int $branch_type = WorkflowBranchEnums::BRANCH_CATEGORY_IS_ORG_MGR,
        string $branch_name = ''
    ): array {
        if (empty($branch_name)) {
            $branch_name = self::$t->_('workflow_branch_14'); //是否为组织负责人
        }

        return [
            'type'       => $branch_type,
            'name'       => $branch_name,
            'operator'   => [
                self::getOperatorEqual(),
            ],
            'option_cnf' => BranchOptionsConfigService::getInstance()->getNormalOptionsCnf(),
            'option'     => [
                [
                    'key'   => self::$t->_('view_yes'),// '是',
                    'value' => WorkflowBranchEnums::OPTION_YES,
                ],
                [
                    'key'   => self::$t->_('view_no'),//'否',
                    'value' => WorkflowBranchEnums::OPTION_NO,
                ],
            ],
        ];
    }

    /**
     * @description 获取雇佣类型
     * @param int $branch_type
     * @param string $branch_name
     * @return array
     */
    public function getHireType(
        int $branch_type = WorkflowBranchEnums::BRANCH_CATEGORY_HIRE_TYPE,
        string $branch_name = ''
    ): array {
        if (empty($branch_name)) {
            $branch_name = self::$t->_('workflow_branch_15'); //雇佣类型
        }

        $hireTypeEnums = SysService::getInstance()->getHireTypeEnums();
        return [
            'type'       => $branch_type,
            'name'       => $branch_name,
            'operator'   => [
                self::getOperatorEqual(),
                self::getOperatorContain(),
                self::getOperatorNotEqual(),
                self::getOperatorNotContain(),
            ],
            'option_cnf' => BranchOptionsConfigService::getInstance()->getNormalOptionsCnf(),
            'option'     => $hireTypeEnums,
        ];
    }

    /**
     * @description 获取职位性质
     * @param int $branch_type
     * @param string $branch_name
     * @return array
     */
    public function getPositionType(
        int $branch_type = WorkflowBranchEnums::BRANCH_CATEGORY_POSITION_TYPE,
        string $branch_name = ''
    ): array {
        if (empty($branch_name)) {
            $branch_name = self::$t->_('workflow_branch_16'); //职位性质
        }
        $position_type = JobService::getInstance()->getEffectivePositionType();
        return [
            'type'       => $branch_type,
            'name'       => $branch_name,
            'operator'   => [
                self::getOperatorEqual(),
                self::getOperatorContain(),
                self::getOperatorNotEqual(),
                self::getOperatorNotContain(),
            ],
            'option_cnf' => BranchOptionsConfigService::getInstance()->getNormalOptionsCnf(),
            'option'     => $position_type,
        ];
    }
    /**
     * 金额
     * @return array
     */
    public function getAmount(): array
    {
        return [
            'type'       => WorkflowBranchEnums::BRANCH_CATEGORY_AMOUNT,
            'name'       => self::$t->_('view_salary_probation'),//'试用期薪资',
            'operator'   => [
                self::getOperatorEqual(),
                self::getOperatorGt(),
                self::getOperatorGe(),
                self::getOperatorLt(),
                self::getOperatorLe(),
            ],
            'option_cnf' => BranchOptionsConfigService::getInstance()->getInputNumberOptionsCnf(),
            'option'     => [],
        ];
    }

    /**
     * 币种
     * @return array
     */
    public function getCurrency(): array
    {
        return [
            'type'       => WorkflowBranchEnums::BRANCH_CATEGORY_CURRENCY,
            'name'       => self::$t->_('view_currency'),//'币种',
            'operator'   => [
                self::getOperatorEqual(),
                self::getOperatorNotEqual(),
            ],
            'option_cnf' => BranchOptionsConfigService::getInstance()->getNormalOptionsCnf(),
            'option'     => [
                [
                    'key'   => 'THB',
                    'value' => 1,
                ],
                [
                    'key'   => 'PHP',
                    'value' => 2,
                ],
                [
                    'key'   => 'VND',
                    'value' => 3,
                ],
                [
                    'key'   => 'MYR',
                    'value' => 4,
                ],
                [
                    'key'   => 'LAK',
                    'value' => 5,
                ],
                [
                    'key'   => 'IN',
                    'value' => 6,
                ],
                [
                    'key'   => 'SGD',
                    'value' => 7,
                ],
            ],
        ];
    }

    /**
     * 薪资审批-职位类型
     * @return array
     */
    public function getJobTitleType(): array
    {
        return [
            'type'       => WorkflowBranchEnums::BRANCH_CATEGORY_SALARY_IS_FIRST_LINE_JOB_TITLE,
            'name'       => self::$t->_('view_is_front_line'),//'是否一线职位',
            'operator'   => [
                self::getOperatorEqual(),
            ],
            'option_cnf' => BranchOptionsConfigService::getInstance()->getNormalOptionsCnf(),
            'option'     => [
                [
                    'key'   => self::$t->_('view_yes'),// '是',
                    'value' => WorkflowBranchEnums::OPTION_YES,
                ],
                [
                    'key'   => self::$t->_('view_no'),//'否',
                    'value' => WorkflowBranchEnums::OPTION_NO,
                ],
            ],
        ];
    }

    /**
     * 薪资审批-业务线类型
     * @return array
     */
    public function getBusinessLineType(): array
    {
        return [
            'type'       => WorkflowBranchEnums::BRANCH_CATEGORY_BUSINESS_LINE_TYPE,
            'name'       => self::$t->_('view_business_line_type'),//'业务线类型',
            'operator'   => [
                self::getOperatorEqual(),
            ],
            'option_cnf' => BranchOptionsConfigService::getInstance()->getNormalOptionsCnf(),
            'option'     => [
                [
                    'key'   => self::$t->_('view_express'),//'快递线',
                    'value' => 1,
                ],
                [
                    'key'   => self::$t->_('view_non_express'),//'非快递线',
                    'value' => 2,
                ],
            ],
        ];
    }

    /**
     * 薪资审批-薪酬范围
     * @return array
     */
    public function getSalaryRange(): array
    {
        return [
            'type'       => WorkflowBranchEnums::BRANCH_CATEGORY_SALARY_RANGE,
            'name'       => self::$t->_('view_salary_range'),//'薪酬范围',
            'operator'   => [
                self::getOperatorEqual(),
            ],
            'option_cnf' => BranchOptionsConfigService::getInstance()->getNormalOptionsCnf(),
            'option'     => [
                [
                    'key'   => self::$t->_('view_in_salary_range'),//'范围内',
                    'value' => 1,
                ],
                [
                    'key'   => self::$t->_('view_no_in_salary_range'),//'范围外',
                    'value' => 2,
                ],
            ],
        ];
    }

    /**
     * 培训 学习计划
     * @return array
     */
    public function learningPlanType(): array
    {
        return [
            'type'        => WorkflowBranchEnums::BRANCH_CATEGORY_LEARNING_PLAN_TYPE,
            'name'        => self::$t->_('school.learning_plan_type'),
            'operator'    => [
                self::getOperatorEqual(),
            ],
            'option_cnf' => BranchOptionsConfigService::getInstance()->getNormalOptionsCnf(),
            'option'     => self::getLearningPlanType(),
        ];
    }
    /**
     * 表单提交-所属部门
     * @param int $branch_type
     * @param string $branch_name
     * @return array
     */
    public function getBelongDepartment(
        int $branch_type = WorkflowBranchEnums::BRANCH_CATEGORY_FORM_BELONG_DEPARTMENT,
        string $branch_name = ''
    ): array {
        if (empty($branch_name)) {
            $branch_name = self::$t->_('workflow.from_view_department'); //表单-部门(表单人的部门)
        }

        return [
            'type'       => $branch_type,
            'name'       => $branch_name,//'所属部门',
            'operator'   => [
                self::getOperatorEqual(),
                self::getOperatorContain(),
                self::getOperatorBelongTo(),
                self::getOperatorNotEqual(),
                self::getOperatorNotContain(),
                self::getOperatorNotBelongTo(),
            ],
            'option_cnf' => BranchOptionsConfigService::getInstance()->getDepartmentOptionsCnf(),
            'option'     => [],
        ];
    }

    /**
     * 请假-请假类型
     * @return array
     */
    public function getLeaveLineType(): array
    {
        return [
            'type'       => WorkflowBranchEnums::BRANCH_CATEGORY_LEAVE_TYPE,
            'name'       => static::$t->_('leave_line_type'),//请假类型
            'operator'   => [
                self::getOperatorEqual(),     //等于
                self::getOperatorNotEqual(),  //不包含
                self::getOperatorContain(),   //包含
                self::getOperatorNotContain(),//不包含

            ],
            'option_cnf' => BranchOptionsConfigService::getInstance()->getNormalOptionsCnf(),
            'option'     => [
                [
                    'key'   => static::$t->_('leave_line_type_1'), //年假
                    'value' => 1,
                ],
                [
                    'key'   => static::$t->_('leave_line_type_2'), //带薪事假
                    'value' => 2,
                ],
                [
                    'key'   => static::$t->_('leave_line_type_3'), //带薪病假
                    'value' => 3,
                ],
                [
                    'key'   => static::$t->_('leave_line_type_4'), //产假
                    'value' => 4,
                ],
                [
                    'key'   => static::$t->_('leave_line_type_5'), //陪产假
                    'value' => 5,
                ],
                [
                    'key'   => static::$t->_('leave_line_type_6'), //国家军训假
                    'value' => 6,
                ],
                [
                    'key'   => static::$t->_('leave_line_type_7'), //家人去世假
                    'value' => 7,
                ],
                [
                    'key'   => static::$t->_('leave_line_type_8'), //绝育手术假
                    'value' => 8,
                ],
                [
                    'key'   => static::$t->_('leave_line_type_9'), //个人受训假
                    'value' => 9,
                ],
                [
                    'key'   => static::$t->_('leave_line_type_10'), //婚假
                    'value' => 10,
                ],
                [
                    'key'   => static::$t->_('leave_line_type_11'), //出家假
                    'value' => 11,
                ],
                [
                    'key'   => static::$t->_('leave_line_type_12'), //不带薪事假
                    'value' => 12,
                ],
                [
                    'key'   => static::$t->_('leave_line_type_13'), //调休 （已废弃 马来启用）
                    'value' => 13,
                ],
                [
                    'key'   => static::$t->_('leave_line_type_14'), //其他（已废弃）
                    'value' => 14,
                ],
                [
                    'key'   => static::$t->_('leave_line_type_15'), //休息日
                    'value' => 15,
                ],
                [
                    'key'   => static::$t->_('leave_line_type_16'), //公司培训假
                    'value' => 16,
                ],
                [
                    'key'   => static::$t->_('leave_line_type_17'), //产检
                    'value' => 17,
                ],
                [
                    'key'   => static::$t->_('leave_line_type_18'), //无薪病假
                    'value' => 18,
                ],
                [
                    'key'   => static::$t->_('leave_line_type_19'), //跨国探亲假
                    'value' => 19,
                ],
                [
                    'key'   => static::$t->_('leave_line_type_20'), //单亲育儿假
                    'value' => 20,
                ],
                [
                    'key'   => static::$t->_('leave_line_type_21'), //女性特殊假
                    'value' => 21,
                ],
                [
                    'key'   => static::$t->_('leave_line_type_22'), //家庭暴力假
                    'value' => 22,
                ],
                [
                    'key'   => static::$t->_('leave_line_type_23'), //紧急假
                    'value' => 23,
                ],
                [
                    'key'   => static::$t->_('leave_line_type_24'), //无薪休假
                    'value' => 24,
                ],
                [
                    'key'   => static::$t->_('leave_line_type_25'), //隔离假
                    'value' => 25,
                ],
                [
                    'key'   => static::$t->_('leave_line_type_26'), //带薪病假（新冠治疗）
                    'value' => 26,
                ],
                [
                    'key'   => static::$t->_('leave_line_type_27'), //住院假（马来）
                    'value' => 27,
                ],
                [
                    'key'   => static::$t->_('leave_line_type_28'), //体恤假（马来）
                    'value' => 28,
                ],
                [
                    'key'   => static::$t->_('leave_line_type_29'), //考试假（马来）
                    'value' => 29,
                ],
                [
                    'key'   => static::$t->_('leave_line_type_30'), //孩子结婚假(越南)
                    'value' => 30,
                ],
                [
                    'key'   => static::$t->_('leave_line_type_31'), //丧假 leave_31
                    'value' => 31,
                ],
                [
                    'key'   => static::$t->_('leave_line_type_32'), //流产假
                    'value' => 32,
                ],
                [
                    'key'   => static::$t->_('leave_line_type_33'), //33其他家人丧家
                    'value' => 33,
                ],
                [
                    'key'   => static::$t->_('leave_line_type_34'), //34儿子割礼
                    'value' => 34,
                ],
                [
                    'key'   => static::$t->_('leave_line_type_35'), //35儿童洗礼
                    'value' => 35,
                ],
                [
                    'key'   => static::$t->_('leave_line_type_36'), //36朝觐假
                    'value' => 36,
                ],
                [
                    'key'   => static::$t->_('leave_line_type_37'), //37灾难假
                    'value' => 37,
                ],
                [
                    'key'   => static::$t->_('leave_line_type_38'), //泰国病假合并类型
                    'value' => 38,
                ],
                [
                    'key'   => static::$t->_('leave_line_type_39'), //个人代理员工申请类型 马来
                    'value' => 39,
                ],
                [
                    'key'   => static::$t->_('leave_line_type_40'), //个人代理员工申请类型 泰国
                    'value' => 40,
                ],
                [
                    'key'   => static::$t->_('leave_line_type_41'), //个人代理员工申请类型 菲律宾
                    'value' => 41,
                ],
                [
                    'key'   => static::$t->_('leave_line_type_42'), //马来国民假
                    'value' => 42,
                ],
            ],
        ];
    }

    /**
     * 请假-开始日期
     * @return array
     */
    public function getLeaveStartDate(): array
    {
        return [
            'type'       => WorkflowBranchEnums::BRANCH_CATEGORY_LEAVE_START_DATE,
            'name'       => static::$t->_('leave_line_start_date'),//假期开始日期是否在申请日之前
            'operator'   => [
                self::getOperatorEqual(), //等于
            ],
            'option_cnf' => BranchOptionsConfigService::getInstance()->getNormalOptionsCnf(),
            'option'     => [
                [
                    'key'   => self::$t->_('view_yes'),// '是',
                    'value' => WorkflowBranchEnums::OPTION_YES,
                ],
                [
                    'key'   => self::$t->_('view_no'),//'否',
                    'value' => WorkflowBranchEnums::OPTION_NO,
                ],
            ],
        ];
    }

    /**
     * 请假-天数
     * @return array
     */
    public function getLeaveDays(): array
    {
        return [
            'type'       => WorkflowBranchEnums::BRANCH_CATEGORY_LEAVE_DAYS,
            'name'       => static::$t->_('leave_line_days'),//请假天数
            'operator'   => [
                self::getOperatorEqual(),   //等于
                self::getOperatorNotEqual(),//不等于
                self::getOperatorGt(),      //大于
                self::getOperatorGe(),      //大于等于
                self::getOperatorLt(),      //小于
                self::getOperatorLe(),      //小于等于

            ],
            'option_cnf' => BranchOptionsConfigService::getInstance()->getInputNumberOptionsCnf(),
            'option'     => [],
        ];
    }

    /**
     * 出差-外出天数
     * @return array
     */
    public function getDayNumWFCondition(): array
    {
        return [
            'type'       => WorkflowBranchEnums::BRANCH_CATEGORY_BUSINESS_TRIP_DAYS,
            'name'       => static::$t->_('workflow_branch_112'),//出差-外出天数
            'operator'   => [
                self::getOperatorEqual(),   //等于
                self::getOperatorNotEqual(),//不等于
                self::getOperatorGt(),      //大于
                self::getOperatorGe(),      //大于等于
                self::getOperatorLt(),      //小于
                self::getOperatorLe(),      //小于等于

            ],
            'option_cnf' => BranchOptionsConfigService::getInstance()->getInputNumberOptionsCnf(),
            'option'     => [],
        ];
    }

    /**
     * @description: 离职申请-获取车辆来源
     *
     * @param null
     *
     * @return     :
     * <AUTHOR> L.J
     * @time       : 2022/3/16 11:19
     */
    public function getResignEnumVehicle()
    {
        $data = [];
        try {
            //获取来源
            $vehicle_enum_all = self::getSvcEnumVehicle();
            $vehicle_enum     = $vehicle_enum_all['vehicle_source_item'] ?? [];//车俩类型
            $data             = [
                'type'       => WorkflowBranchEnums::BRANCH_CATEGORY_RESIGN_VEHICLE_SOURCE,
                'name'       => static::$t->_('workflow_condition_key_113'),//离职=车辆类型
                'operator'   => [
                    self::getOperatorEqual(),     //等于
                    self::getOperatorNotEqual(),  //不等于
                    self::getOperatorContain(),   //包含
                    self::getOperatorNotContain(),//不包含
                ],
                'option_cnf' => BranchOptionsConfigService::getInstance()->getNormalOptionsCnf(),
                'option'     => [],
            ];
            //拼装车辆来源
            foreach ($vehicle_enum as $v) {
                $data['option'][] = [
                    'key'   => $v['label'] ?? '',
                    'value' => $v['value'] ?? 0,
                ];
            }
        } catch (\Exception $e) {
            $this->logger->error($e->getMessage());
        }
        return $data;
    }

    /**
     * 外协-外协类型
     * @return array
     */
    public function getOSType(): array
    {
        return [
            'type'       => WorkflowBranchEnums::BRANCH_CATEGORY_OS_TYPE,
            'name'       => static::$t->_('workflow_branch_114'),//外协类型
            'operator'   => [
                self::getOperatorEqual(),     //等于
                self::getOperatorNotEqual(),  //不等于
                self::getOperatorContain(),   //包含
                self::getOperatorNotContain(),//不包含
            ],
            'option_cnf' => BranchOptionsConfigService::getInstance()->getNormalOptionsCnf(),
            'option'     => [
                [
                    'key'   => static::$t->_('os_request_mode_1'), //短期外协
                    'value' => 1,
                ],
                [
                    'key'   => static::$t->_('os_request_mode_2'), //长期外协
                    'value' => 2,
                ],
                [
                    'key'   => static::$t->_('os_request_mode_3'), //车队外协
                    'value' => 3,
                ],
            ],
        ];
    }

    /**
     * 外协-外协职位
     * @return array
     */
    public function getOSJobTitle(): array
    {
        return [
            'type'       => WorkflowBranchEnums::BRANCH_CATEGORY_OS_JOB_TITLE,
            'name'       => static::$t->_('workflow_branch_115'),//外协职位
            'operator'   => [
                self::getOperatorEqual(),     //等于
                self::getOperatorNotEqual(),  //不等于
                self::getOperatorContain(),   //包含
                self::getOperatorNotContain(),//不包含
            ],
            'option_cnf' => BranchOptionsConfigService::getInstance()->getJobTitleOptionsCnf(),
            'option'     => [],
        ];
    }

    /**
     * 黄牌出差天数
     * @return array
     */
    public function bpDayNum(): array
    {
        return [
            'type'       => WorkflowBranchEnums::BRANCH_CATEGORY_TRIP_DAYS,
            'name'       => self::$t->_('workflow_branch_116'), // '黄牌出差天数',
            'operator'   => [
                self::getOperatorEqual(),
                self::getOperatorGt(),
                self::getOperatorGe(),
                self::getOperatorLt(),
                self::getOperatorLe(),
            ],
            'option_cnf' => BranchOptionsConfigService::getInstance()->getInputNumberOptionsCnf(),
            'option'     => [],
        ];
    }

    /**
     * 资产审批-所属公司
     * @param int $branch_type
     * @param string $branch_name
     * @return array
     */
    public function getCompany(int $branch_type = WorkflowBranchEnums::BRANCH_CATEGORY_ASSET_BELONG_COMPANY, string $branch_name = ''): array
    {
        $branch_name = empty($branch_name) ? self::$t->_('material_asset.company_name') : $branch_name;
        return [
            'type'        => $branch_type,
            'name'        => $branch_name,
            'operator'    => [
                self::getOperatorEqual(),
                self::getOperatorNotEqual(),
                self::getOperatorContain(),
                self::getOperatorNotContain(),
            ],
            'option_cnf' => BranchOptionsConfigService::getInstance()->getNormalOptionsCnf(),
            'option'     => self::getAllCompany(),
        ];
    }

    /**
     * @description:举报-违规原因
     * @return:[]
     * @author: L.J
     * @time: 2022/12/19 11:07
     */
    public function getReportReasonAll()
    {
        $getReportReasonAll = self::getSvcReportEnum();
        $data               = $getReportReasonAll['reason'] ?? [];
        $option             = [];
        //拼装数据
        foreach ($data as $k => $v) {
            $option[] = [
                'key'   => $v['reason'] ?? '',
                'value' => $v['id'] ?? '',
            ];
        }

        return [
            'type'       => WorkflowBranchEnums::BRANCH_CATEGORY_REPORT_REASON,
            'name'       => self::$t->_('workflow.report_reason'), // '举报-违规原因',
            'operator'   => [
                self::getOperatorEqual(),
                self::getOperatorNotEqual(),
                self::getOperatorContain(),
                self::getOperatorNotContain(),
            ],
            'option_cnf' => BranchOptionsConfigService::getInstance()->getNormalOptionsCnf(),
            'option'     => $option,
        ];
    }

    /**
     * @description:举报-举报类型
     * @return:[]
     * @author: L.J
     * @time: 2022/12/19 11:07
     */
    public function getReportTypeAll()
    {
        $getReportReasonAll = self::getSvcReportEnum();
        $data               = $getReportReasonAll['type'] ?? [];
        $option             = [];
        //拼装数 据
        foreach ($data as $k => $v) {
            $option[] = [
                'key'   => $v['type'] ?? '',
                'value' => $v['id'] ?? '',
            ];
        }

        return [
            'type'       => WorkflowBranchEnums::BRANCH_CATEGORY_REPORT_TYPE,
            'name'       => self::$t->_('workflow.report_type'), // '举报-举报类型',
            'operator'   => [
                self::getOperatorEqual(),
                self::getOperatorNotEqual(),
                self::getOperatorContain(),
                self::getOperatorNotContain(),
            ],
            'option_cnf' => BranchOptionsConfigService::getInstance()->getNormalOptionsCnf(),
            'option'     => $option,
        ];
    }

    /**
     * @description 表单-举报-是否为是否为系统自动举报
     * @return array
     */
    public function getReportFormReportedIsAuto(): array
    {
        return [
            'type'       => WorkflowBranchEnums::BRANCH_CATEGORY_REPORT_IS_AUTO,
            'name'       => self::$t->_('workflow.report_is_auto'), // '举报-举报类型',
            'operator'   => [
                self::getOperatorEqual(),
            ],
            'option_cnf' => BranchOptionsConfigService::getInstance()->getNormalOptionsCnf(),
            'option'     => [
                [
                    'key'   => self::$t->_('view_yes'),// '是',
                    'value' => WorkflowBranchEnums::OPTION_YES,
                ],
                [
                    'key'   => self::$t->_('view_no'),//'否',
                    'value' => WorkflowBranchEnums::OPTION_NO,
                ],
        ]];
    }

    /**
     * 虚假里程-网点主管否打卡
     * @return array
     */
    public function isStoreManagerToWork(): array
    {
        return [
            'type'       => WorkflowBranchEnums::BRANCH_CATEGORY_FAKE_MILEAGE_IS_STORE_MGR_ATTEND,
            'name'       => self::$t->_('store_manager_to_work'), // '网点主管是否打卡',
            'operator'   => [
                $this->getOperatorEqual(),
                $this->getOperatorNotEqual(),
            ],
            'option_cnf' => BranchOptionsConfigService::getInstance()->getNormalOptionsCnf(),
            'option'     => [
                [
                    'key'   => self::$t->_('view_yes'),// '是',
                    'value' => WorkflowBranchEnums::OPTION_YES,
                ],
                [
                    'key'   => self::$t->_('view_no'),//'否',
                    'value' => WorkflowBranchEnums::OPTION_NO,
                ],
            ],
        ];
    }

    /**
     * 虚假里程-片区经理是否打卡
     * @return array
     */
    public function isMileagePieceMgrToWork(): array
    {
        return [
            'type'       => WorkflowBranchEnums::BRANCH_CATEGORY_FAKE_MILEAGE_IS_PIECE_MGR_ATTEND,
            'name'       => self::$t->_('piece_mgr_to_work'), // 片区经理是否打卡
            'operator'   => [
                $this->getOperatorEqual(),
                $this->getOperatorNotEqual(),
            ],
            'option_cnf' => BranchOptionsConfigService::getInstance()->getNormalOptionsCnf(),
            'option'     => [
                [
                    'key'   => self::$t->_('view_yes'),// '是',
                    'value' => WorkflowBranchEnums::OPTION_YES,
                ],
                [
                    'key'   => self::$t->_('view_no'),//'否',
                    'value' => WorkflowBranchEnums::OPTION_NO,
                ],
            ],
        ];
    }

    /**
     * @description 【HC表单字段】工作地点
     * @return array
     */
    public function getFormBelongStore(): array
    {
        //表单字段-工作地点
        return $this->getFormBelongStoreEx(self::$t->_('hc_summary_store_text'));
    }

    /**
     * @description 表单-举报-被举报人所属网点
     * @return array
     */
    public function getReportFormBelongStore(): array
    {
        //表单字段-工作地点
        return $this->getFormBelongStoreEx(self::$t->_('workflow.report_store'));
    }

    /**
     * @description 分支条件表单-所属网点，可自定义条件名
     * @param $branch_name
     * @return array
     */
    public function getFormBelongStoreEx($branch_name): array
    {
        return [
            'type'       => WorkflowBranchEnums::BRANCH_CATEGORY_FORM_BELONG_STORE,
            'name'       => $branch_name, //条件名
            'operator'   => [
                $this->getOperatorEqual(),
                $this->getOperatorNotEqual(),
                $this->getOperatorContain(),
                $this->getOperatorNotContain(),
            ],
            'option_cnf' => BranchOptionsConfigService::getInstance()->getStoreOptionsCnf(),
            'option'     => [],
        ];
    }

    /**
     * 获取加班车类型条件
     * @return array
     */
    public function getOvertimeBusType(): array
    {
        return [
            'type'       => WorkflowBranchEnums::BRANCH_CATEGORY_OVERTIME_BUS_TYPE,
            'name'       => self::$t->_('branch_category_' . WorkflowBranchEnums::BRANCH_CATEGORY_OVERTIME_BUS_TYPE),
            'operator'   => [
                // 等于
                $this->getOperatorEqual(),
                // 不等于
                $this->getOperatorNotEqual(),
                // 包含
                $this->getOperatorContain(),
                // 不包含
                $this->getOperatorNotContain(),
            ],
            'option_cnf' => BranchOptionsConfigService::getInstance()->getNormalOptionsCnf(),
            //通过接口获取下拉
            'option'     => [
                [
                    'value' => WorkflowBranchEnums::ORDINARY_CAR,
                    'key'   => self::$t->_('ordinary_car'), // 普通加班车
                ],
                [
                    'value' => WorkflowBranchEnums::FD_COURIER_CAR,
                    'key'   => self::$t->_('fd_courier_car'), // FD 加班车
                ],
            ],
        ];
    }

    /**
     * 获取 OT 审批类型
     * @return array
     */
    public function getOTApprovalType(): array
    {
        $option    = [];
        $type_list = self::getSvcOTApprovalType();
        if (!empty($type_list)) {
            foreach ($type_list as $key => $val) {
                $option[] = [
                    'key'   => $val['msg'] . ' ' . $val['sub_msg'],
                    'value' => (int)$val['code'],
                ];
            }
        }

        return [
            'type'       => WorkflowBranchEnums::BRANCH_CATEGORY_OT_TYPE,
            'name'       => self::$t->_('branch_category_' . WorkflowBranchEnums::BRANCH_CATEGORY_OT_TYPE),
            //申请人负责部门的最高级别
            'operator'   => [
                // 等于
                $this->getOperatorEqual(),
                // 不等于
                $this->getOperatorNotEqual(),
                // 包含
                $this->getOperatorContain(),
                // 不包含
                $this->getOperatorNotContain(),
            ],
            'option_cnf' => BranchOptionsConfigService::getInstance()->getNormalOptionsCnf(),
            //通过接口获取下拉
            'option'     => $option,
        ];
    }

    //当月累计ot小时 减去 8*ph天数
    public function getOtDurationPH()
    {
        return [
            'type'       => WorkflowBranchEnums::BRANCH_CATEGORY_OT_MONTH_DURATION_PH,
            'name'       => self::$t->_('branch_category_' . WorkflowBranchEnums::BRANCH_CATEGORY_OT_MONTH_DURATION_PH),
            'operator'   => [
                // 等于
                $this->getOperatorEqual(),
                // 不等于
                $this->getOperatorNotEqual(),
                // 大于
                $this->getOperatorGt(),
                // 大于等于
                $this->getOperatorGe(),
                //小于
                $this->getOperatorLt(),
                //小于等于
                $this->getOperatorLe(),
            ],
            'option_cnf' => BranchOptionsConfigService::getInstance()->getInputNumberOptionsCnf(),
            //通过接口获取下拉
            'option'     => [],
        ];
    }

    //当周累计OT(h):当周可申请OT(h) / 可申请ot 小时数
    public function getOtDurationPercent()
    {
        return [
            'type'       => WorkflowBranchEnums::BRANCH_CATEGORY_OT_WEEK_DURATION_PERCENT,
            'name'       => self::$t->_('branch_category_' . WorkflowBranchEnums::BRANCH_CATEGORY_OT_WEEK_DURATION_PERCENT),
            'operator'   => [
                // 等于
                $this->getOperatorEqual(),
                // 不等于
                $this->getOperatorNotEqual(),
                // 大于
                $this->getOperatorGt(),
                // 大于等于
                $this->getOperatorGe(),
                //小于
                $this->getOperatorLt(),
                //小于等于
                $this->getOperatorLe(),
            ],
            'option_cnf' => BranchOptionsConfigService::getInstance()->getInputNumberOptionsCnf(),
            //通过接口获取下拉
            'option'     => [],
        ];
    }

    /**
     * 公司解约个人代理-3天内同一网点解约个人代理人数
     * @return array
     */
    public function getForm3DayStoreTerminationNum(): array
    {
        return [
            'type'       => WorkflowBranchEnums::BRANCH_CATEGORY_3DAY_STORE_TERMINATION_NUM,
            'name'       => self::$t->_('branch_category_' . WorkflowBranchEnums::BRANCH_CATEGORY_3DAY_STORE_TERMINATION_NUM),
            'operator'   => [
                // 等于
                $this->getOperatorEqual(),
            ],
            'option_cnf' => BranchOptionsConfigService::getInstance()->getNormalOptionsCnf(),
            //通过接口获取下拉
            'option'     => [
                [
                    'key'   => self::$t->_('view_yes'),// '是',
                    'value' => WorkflowBranchEnums::OPTION_YES,
                ],
                [
                    'key'   => self::$t->_('view_no'),//'否',
                    'value' => WorkflowBranchEnums::OPTION_NO,
                ],
            ],
        ];
    }

    /**
     * 公司解约个人代理-员工问题解约类型
     * @return array
     */
    public function getFormTerminationTypePersonalAgency(): array
    {
        return [
            'type'       => WorkflowBranchEnums::BRANCH_CATEGORY_TERMINATION_PERSONAL_AGENCY_STAFF_TYPE,
            'name'       => self::$t->_('branch_category_' . WorkflowBranchEnums::BRANCH_CATEGORY_TERMINATION_PERSONAL_AGENCY_STAFF_TYPE),
            'operator'   => [
                // 等于
                $this->getOperatorEqual(),
            ],
            'option_cnf' => BranchOptionsConfigService::getInstance()->getNormalOptionsCnf(),
            //通过接口获取下拉
            'option'     => [
                [
                    'key'   => self::$t->_('view_yes'),// '是',
                    'value' => WorkflowBranchEnums::OPTION_YES,
                ],
                [
                    'key'   => self::$t->_('view_no'),//'否',
                    'value' => WorkflowBranchEnums::OPTION_NO,
                ],
            ],
        ];
    }


    /**
     * 获取 kit 处罚类型
     * @return array
     */
    public function getPenaltyApprovalType(): array
    {
        return [
            'type'       => WorkflowBranchEnums::BRANCH_CATEGORY_PENALTY,
            'name'       => self::$t->_('branch_category_' . WorkflowBranchEnums::BRANCH_CATEGORY_PENALTY),
            'operator'   => [
                // 等于
                $this->getOperatorEqual(),
                // 不等于
                $this->getOperatorNotEqual(),
                // 包含
                $this->getOperatorContain(),
                // 不包含
                $this->getOperatorNotContain(),
            ],
            'option_cnf' => BranchOptionsConfigService::getInstance()->getNormalOptionsCnf(),
            //通过接口获取下拉
            'option'     => [
                [
                    'value' => WorkflowBranchEnums::PUNISH_CATEGORY,
                    'key'   => self::$t->_('punish_category_msg57'), //矿工处罚
                ],

            ],
        ];
    }

    /**
     * 是否有特殊标记
     * @return array
     */
    public function getSpecialMark(): array
    {
        return [
            'type'       => WorkflowBranchEnums::BRANCH_CATEGORY_SPECIAL_MARK,
            'name'       => self::$t->_('branch_category_' . WorkflowBranchEnums::BRANCH_CATEGORY_SPECIAL_MARK),
            'operator'   => [
                // 等于
                $this->getOperatorEqual(),
            ],
            'option_cnf' => BranchOptionsConfigService::getInstance()->getNormalOptionsCnf(),
            //通过接口获取下拉
            'option'     => [
                [
                    'key'   => self::$t->_('view_yes'),// '是',
                    'value' => WorkflowBranchEnums::OPTION_YES,
                ],
                [
                    'key'   => self::$t->_('view_no'),//'否',
                    'value' => WorkflowBranchEnums::OPTION_NO,
                ],
            ],
        ];
    }

    /**
     * 调用bu接口获取ot类型数据
     * @return array
     */
    protected function getSvcOTApprovalType(): array
    {
        //获取来源
        $data = [];
        try {
            $redis     = $this->getDI()->get('redis');
            $redis_key = 'by_get_svc_ot_approval_type_' . static::$language;
            //这里先读缓存
            $cache = $redis->get($redis_key);
            if ($cache) {
                $this->logger->info('getSvcOTApprovalType 返回参数: key' . $redis_key . ' 内容' . $cache);
                return json_decode($cache, true);
            }
            $ApiClient = new ApiClient('by', '', 'allOverTimeTypes', static::$language);
            $ApiClient->setParams([[]]);
            $res  = $ApiClient->execute();
            $data = $res['result'];
            if (!empty($data)) {
                // 放入缓存
                $redis->setex($redis_key, 60 * 5, json_encode($data, JSON_UNESCAPED_UNICODE));
            }
        } catch (\Exception $e) {
            $this->logger->error('getSvcOTApprovalType ' .
                ' code ' . $e->getCode() .
                ' file ' . $e->getFile() .
                ' line ' . $e->getLine() .
                ' message ' . $e->getMessage() .
                ' trace ' . $e->getTraceAsString()
            );
        }
        return $data;
    }

    /**
     * @description 获取HC申请-职位（表单填写的职位）的条件
     * @return array
     */
    public function getHcSubmitterPosition(): array
    {
        return [
            'type'       => WorkflowBranchEnums::BRANCH_CATEGORY_HC_SUBMITTER_POSITION,
            'name'       => self::$t->_('branch_category_' . WorkflowBranchEnums::BRANCH_CATEGORY_HC_SUBMITTER_POSITION),
            //申请人负责部门的最高级别
            'operator'   => [
                // 等于
                $this->getOperatorEqual(),
                // 不等于
                $this->getOperatorNotEqual(),
                // 包含
                $this->getOperatorContain(),
                // 不包含
                $this->getOperatorNotContain(),
            ],
            'option_cnf' => BranchOptionsConfigService::getInstance()->getJobTitleOptionsCnf(),
            //通过接口获取下拉
            'option'     => [],
        ];
    }

    /**
     * @description 申请人负责部门的最高级别
     * @return array
     */
    public function getSubmitterMaxLevel(): array
    {
        return [
            'type'       => WorkflowBranchEnums::BRANCH_CATEGORY_SUBMITTER_MAX_LEVEL,
            'name'       => self::$t->_('branch_category_127'), //申请人负责部门的最高级别
            'operator'   => [
                $this->getOperatorEqual(),
                $this->getOperatorNotEqual(),
                $this->getOperatorContain(),
            ],
            'option_cnf' => BranchOptionsConfigService::getInstance()->getNormalOptionsCnf(),
            'option'     => [
                [
                    'value' => 1,
                    'key'   => self::$t->_('principal_of_a_division'), //一级组织负责人
                ],
                [
                    'value' => 2,
                    'key'   => self::$t->_('principal_of_a_department'), // '二级组织负责人',
                ],
                [
                    'value' => 3,
                    'key'   => self::$t->_('principal_of_a_sub_department'),// '三级组织负责人',
                ],
                [
                    'value' => 4,
                    'key'   => self::$t->_('principal_of_a_sub_department2'), // '四级组织负责人',
                ],
                [
                    'value' => 101,
                    'key'   => self::$t->_('principal_of_a_bu'),//'所属BU负责人',
                ],
                [
                    'value' => 150,
                    'key'   => self::$t->_('principal_of_a_clevel'),//'所属C-level负责人',
                ],
            ],
        ];
    }

    /**
     * @description 申请人负责部门的最高级别
     * @return array
     */
    public function getQuitclaimPaymentMethod(): array
    {
        return [
            'type'       => WorkflowBranchEnums::BRANCH_CATEGORY_QUIT_CLAIM_PAYMENT,
            'name'       => self::$t->_('branch_category_128'), //申请人负责部门的最高级别
            'operator'   => [
                $this->getOperatorEqual(),
                $this->getOperatorNotEqual(),
            ],
            'option_cnf' => BranchOptionsConfigService::getInstance()->getNormalOptionsCnf(),
            'option'     => [
                [
                    'value' => 1,
                    'key'   => self::$t->_('payment_method_1'), //UB flash payroll
                ],
            ],
        ];
    }

    /**
     * @description 表单-举报-被举报人所属网点类型
     * @return array
     */
    public function getReportFormBelongStoreCategory(): array
    {
        return $this->getStoreCategory(WorkflowBranchEnums::BRANCH_CATEGORY_REPORT_STORE_CATEGORY,
            self::$t->_('branch_category_129'));
    }

    /**
     * @description 表单-举报-被举报人职位
     * @return array
     */
    public function getReportFormJobTitle(): array
    {
        return [
            'type'       => WorkflowBranchEnums::BRANCH_CATEGORY_REPORT_JOB_TITLE_OF_REPORTED,
            'name'       => self::$t->_('branch_category_130'),
            'operator'   => [
                $this->getOperatorEqual(),
                $this->getOperatorNotEqual(),
                $this->getOperatorContain(),
                $this->getOperatorNotContain(),
            ],
            'option_cnf' => BranchOptionsConfigService::getInstance()->getJobTitleOptionsCnf(),
            'option'     => [],
        ];
    }

    /**
     * @description 表单-举报-被举报人职级
     * @return array
     */
    public function getReportFormJobTitleGrade(): array
    {
        return $this->getJobTitleGrade(WorkflowBranchEnums::BRANCH_CATEGORY_REPORT_JOB_GRADE_OF_REPORTED,
            self::$t->_('branch_category_131'));
    }

    /**
     * @description 表单-举报-被举报人角色
     * @return array
     */
    public function getReportFormReportedRoles(): array
    {
        return $this->getRole(WorkflowBranchEnums::BRANCH_CATEGORY_REPORT_ROLES_OF_REPORTED,
            self::$t->_('branch_category_132'));
    }

    /**
     * @description 表单-举报-是否为组织负责人
     * @return array
     */
    public function getReportFormReportedIsOrgManger(): array
    {
        return $this->isOrgManger(WorkflowBranchEnums::BRANCH_CATEGORY_REPORT_IS_ORG_MGR_OF_REPORTED,
            self::$t->_('branch_category_133'));
    }

    /**
     * @description 表单-举报-被举报人职位
     * @return array
     */
    public function getFormOfSuspensionReason(): array
    {
        $optionsList = SysService::getInstance()->getSuspensionReason();

        $options = array_map(function ($v) {
            return [
                'key'   => $v['label'],
                'value' => $v['value'],
            ];
        }, $optionsList);

        return [
            'type'       => WorkflowBranchEnums::BRANCH_CATEGORY_SUSPENSION_REASON,
            'name'       => self::$t->_('branch_category_136'),
            'operator'   => [
                $this->getOperatorEqual(),
                $this->getOperatorNotEqual(),
                $this->getOperatorContain(),
                $this->getOperatorNotContain(),
            ],
            'option_cnf' => BranchOptionsConfigService::getInstance()->getNormalOptionsCnf(),
            'option'     => $options,
        ];
    }

    /**
     * 获取HC用人原因
     * @return array
     */
    public function getHcFormReasonType(): array
    {
        return [
            'type'       => WorkflowBranchEnums::BRANCH_CATEGORY_HC_REASON_TYPE,
            'name'       => self::$t->_('branch_category_137'),
            'operator'   => [
                $this->getOperatorEqual(),
                $this->getOperatorNotEqual(),
                $this->getOperatorContain(),
            ],
            'option_cnf' => BranchOptionsConfigService::getInstance()->getNormalOptionsCnf(),
            'option'     => [
                ['key' => self::$t->t('hc_reason_type_1'), 'value' => 1],
                ['key' => self::$t->t('hc_reason_type_2'), 'value' => 2],
                ['key' => self::$t->t('hc_reason_type_3'), 'value' => 3],
            ],
        ];
    }

    /**
     * 表单-恢复在职-申请恢复工号所属部门
     * @return array
     */
    public function getResumeJobFormSubmitDepartment(): array
    {
        return $this->getBelongDepartment(WorkflowBranchEnums::BRANCH_CATEGORY_SUSPENSION_FORM_SUBMIT_DEPARTMENT,
            self::$t->_('resume_job_form_submit_department'));
    }

    /**
     * 表单-恢复在职-申请恢复工号所属网点类型
     * @return array
     */
    public function getResumeJobFormSubmitStoreCategory(): array
    {
        return $this->getStoreCategory(WorkflowBranchEnums::BRANCH_CATEGORY_SUSPENSION_FORM_SUBMIT_STORE_CATEGORY,
            self::$t->_('resume_job_form_submit_store_category'));
    }

    /**
     * 表单-恢复在职-申请恢复工号所属网点
     * @return array
     */
    public function getResumeJobFormSubmitStore(): array
    {
        return $this->getBelongToStore(WorkflowBranchEnums::BRANCH_CATEGORY_SUSPENSION_FORM_SUBMIT_STORE,
            self::$t->_('resume_job_form_submit_store'));
    }

    /**
     * 表单-恢复在职-申请恢复工号职位
     * @return array
     */
    public function getResumeJobFormSubmitJobTitle(): array
    {
        return $this->getJobTitle(WorkflowBranchEnums::BRANCH_CATEGORY_SUSPENSION_FORM_SUBMIT_JOB_TITLE,
            self::$t->_('resume_job_form_submit_job_title'));
    }

    /**
     * 表单-恢复在职-申请恢复工号职级
     * @return array
     */
    public function getResumeJobFormSubmitJobTitleGrade(): array
    {
        return $this->getJobTitleGrade(WorkflowBranchEnums::BRANCH_CATEGORY_SUSPENSION_FORM_SUBMIT_JOB_TITLE_GRADE,
            self::$t->_('resume_job_form_submit_job_title_grade'));
    }

    /**
     * 表单-恢复在职-申请恢复工号角色
     * @return array
     */
    public function getResumeJobFormSubmitRole(): array
    {
        return $this->getRole(WorkflowBranchEnums::BRANCH_CATEGORY_SUSPENSION_FORM_SUBMIT_ROLE,
            '表单-恢复在职-申请恢复工号角色');
    }

    /**
     * 表单-恢复在职-申请恢复在职工号雇佣类型
     * @return array
     */
    public function getResumeJobFormSubmitHireType(): array
    {
        return $this->getHireType(WorkflowBranchEnums::BRANCH_CATEGORY_SUSPENSION_FORM_SUBMIT_HIRE_TYPE,
            self::$t->_('workflow_branch_163'));
    }

    /**
     * @description 表单-被转岗人转岗前所属部门
     * @return array
     */
    public function getFormOfBeforeDepartment(): array
    {
        return [
            'type'       => WorkflowBranchEnums::BRANCH_CATEGORY_TRANSFER_BEFORE_DEPARTMENT,
            'name'       => self::$t->_('branch_category_139'),
            'operator'   => [
                self::getOperatorEqual(),
                self::getOperatorContain(),
                self::getOperatorBelongTo(),
                self::getOperatorNotEqual(),
                self::getOperatorNotContain(),
                self::getOperatorNotBelongTo(),
            ],
            'option_cnf' => BranchOptionsConfigService::getInstance()->getDepartmentOptionsCnf(),
            'option'     => [],
        ];
    }

    /**
     * @description 表单-被转岗人转岗前职位是否为一线职位
     * @return array
     */
    public function getFormOfBeforeJobTitle(): array
    {
        return [
            'type'       => WorkflowBranchEnums::BRANCH_CATEGORY_TRANSFER_BEFORE_JOB_TITLE,
            'name'       => self::$t->_('branch_category_140'),
            'operator'   => [
                self::getOperatorEqual(),
            ],
            'option_cnf' => BranchOptionsConfigService::getInstance()->getNormalOptionsCnf(),
            'option'     => [
                [
                    'key'   => self::$t->_('view_yes'),// '是',
                    'value' => WorkflowBranchEnums::OPTION_YES,
                ],
                [
                    'key'   => self::$t->_('view_no'),//'否',
                    'value' => WorkflowBranchEnums::OPTION_NO,
                ],
            ],
        ];
    }

    /**
     * @description 表单-被转岗人转岗前所属部门级别
     * @return array
     */
    public function getFormOfBeforeDepartmentLevel(): array
    {
        return [
            'type'       => WorkflowBranchEnums::BRANCH_CATEGORY_TRANSFER_BEFORE_DEPARTMENT_LEVEL,
            'name'       => self::$t->_('branch_category_141'),
            'operator'   => [
                self::getOperatorEqual(),
                self::getOperatorNotEqual(),
            ],
            'option_cnf' => BranchOptionsConfigService::getInstance()->getNormalOptionsCnf(),
            'option'     => [
                [
                    'value' => 1,
                    'key'   => 1, //一级
                ],
                [
                    'value' => 2,
                    'key'   => 2, // 二级
                ],
                [
                    'value' => 3,
                    'key'   => 3,// 三级
                ],
                [
                    'value' => 4,
                    'key'   => 4, // 四级
                ],
            ],
        ];
    }

    /**
     * @description 表单-被转岗人转岗后所属部门
     * @return array
     */
    public function getFormOfAfterDepartment(): array
    {
        return [
            'type'       => WorkflowBranchEnums::BRANCH_CATEGORY_TRANSFER_AFTER_DEPARTMENT,
            'name'       => self::$t->_('branch_category_142'),
            'operator'   => [
                self::getOperatorEqual(),
                self::getOperatorContain(),
                self::getOperatorBelongTo(),
                self::getOperatorNotEqual(),
                self::getOperatorNotContain(),
                self::getOperatorNotBelongTo(),
            ],
            'option_cnf' => BranchOptionsConfigService::getInstance()->getDepartmentOptionsCnf(),
            'option'     => [],
        ];
    }

    /**
     * @description 表单-被转岗人转岗后职位是否为一线职位
     * @return array
     */
    public function getFormOfAfterJobTitle(): array
    {
        return [
            'type'       => WorkflowBranchEnums::BRANCH_CATEGORY_TRANSFER_AFTER_JOB_TITLE,
            'name'       => self::$t->_('branch_category_143'),
            'operator'   => [
                self::getOperatorEqual(),
            ],
            'option_cnf' => BranchOptionsConfigService::getInstance()->getNormalOptionsCnf(),
            'option'     => [
                [
                    'key'   => self::$t->_('view_yes'),// '是',
                    'value' => WorkflowBranchEnums::OPTION_YES,
                ],
                [
                    'key'   => self::$t->_('view_no'),//'否',
                    'value' => WorkflowBranchEnums::OPTION_NO,
                ],
            ],
        ];
    }

    /**
     * @description 表单-被转岗人转岗前职位
     * @return array
     */
    public function getFormOfBeforePosition(): array
    {
        return $this->getJobTitle(WorkflowBranchEnums::BRANCH_CATEGORY_TRANSFER_BEFORE_POSITION_ID, self::$t->_('branch_category_181'));
    }

    /**
     * @description 表单-被转岗人转岗后职位
     * @return array
     */
    public function getFormOfAfterPosition(): array
    {
        return $this->getJobTitle(WorkflowBranchEnums::BRANCH_CATEGORY_TRANSFER_AFTER_POSITION_ID, self::$t->_('branch_category_179'));
    }

    /**
     * @description 表单-被转岗人转岗后所属部门级别
     * @return array
     */
    public function getFormOfAfterDepartmentLevel(): array
    {
        return [
            'type'       => WorkflowBranchEnums::BRANCH_CATEGORY_TRANSFER_AFTER_DEPARTMENT_LEVEL,
            'name'       => self::$t->_('branch_category_144'),
            'operator'   => [
                self::getOperatorEqual(),
                self::getOperatorNotEqual(),
            ],
            'option_cnf' => BranchOptionsConfigService::getInstance()->getNormalOptionsCnf(),
            'option'     => [
                [
                    'value' => 1,
                    'key'   => 1, //一级
                ],
                [
                    'value' => 2,
                    'key'   => 2, // 二级
                ],
                [
                    'value' => 3,
                    'key'   => 3,// 三级
                ],
                [
                    'value' => 4,
                    'key'   => 4, // 四级
                ],
            ],
        ];
    }

    /**
     * @description 表单-转岗后所属大区与转岗前所属大区是否一致
     * @return array
     */
    public function getFormOfWeatherRegionConsistency(): array
    {
        return [
            'type'       => WorkflowBranchEnums::BRANCH_CATEGORY_TRANSFER_WEATHER_REGION_CONSISTENCY,
            'name'       => self::$t->_('branch_category_145'),
            'operator'   => [
                self::getOperatorEqual(),
            ],
            'option_cnf' => BranchOptionsConfigService::getInstance()->getNormalOptionsCnf(),
            'option'     => [
                [
                    'key'   => self::$t->_('view_yes'),// '是',
                    'value' => WorkflowBranchEnums::OPTION_YES,
                ],
                [
                    'key'   => self::$t->_('view_no'),//'否',
                    'value' => WorkflowBranchEnums::OPTION_NO,
                ],
            ],
        ];
    }

    /**
     * @description 表单-转岗后所属片区与转岗前所属片区是否一致
     * @return array
     */
    public function getFormOfWeatherPieceConsistency(): array
    {
        return [
            'type'       => WorkflowBranchEnums::BRANCH_CATEGORY_TRANSFER_WEATHER_PIECE_CONSISTENCY,
            'name'       => self::$t->_('branch_category_146'),
            'operator'   => [
                self::getOperatorEqual(),
            ],
            'option_cnf' => BranchOptionsConfigService::getInstance()->getNormalOptionsCnf(),
            'option'     => [
                [
                    'key'   => self::$t->_('view_yes'),// '是',
                    'value' => WorkflowBranchEnums::OPTION_YES,
                ],
                [
                    'key'   => self::$t->_('view_no'),//'否',
                    'value' => WorkflowBranchEnums::OPTION_NO,
                ],
            ],
        ];
    }

    /**
     * @description 表单-转岗后所属部门与转岗前所属部门是否一致
     * @return array
     */
    public function getFormOfWeatherDepartmentConsistency(): array
    {
        return [
            'type'       => WorkflowBranchEnums::BRANCH_CATEGORY_TRANSFER_WEATHER_DEPARTMENT_CONSISTENCY,
            'name'       => self::$t->_('branch_category_147'),
            'operator'   => [
                self::getOperatorEqual(),
            ],
            'option_cnf' => BranchOptionsConfigService::getInstance()->getNormalOptionsCnf(),
            'option'     => [
                [
                    'key'   => self::$t->_('view_yes'),// '是',
                    'value' => WorkflowBranchEnums::OPTION_YES,
                ],
                [
                    'key'   => self::$t->_('view_no'),//'否',
                    'value' => WorkflowBranchEnums::OPTION_NO,
                ],
            ],
        ];
    }

    /**
     * @description 表单-被转岗人转岗前职级
     * @return array
     */
    public function getFormOfBeforeJobTitleGrade(): array
    {
        return $this->getJobTitleGrade(WorkflowBranchEnums::BRANCH_CATEGORY_TRANSFER_BEFORE_JOB_TITLE_GRADE,
            self::$t->_('branch_category_157'));
    }

    /**
     * @description 表单-被转岗人转岗后职级
     * @return array
     */
    public function getFormOfAfterJobTitleGrade(): array
    {
        return $this->getJobTitleGrade(WorkflowBranchEnums::BRANCH_CATEGORY_TRANSFER_AFTER_JOB_TITLE_GRADE,
            self::$t->_('branch_category_158'));
    }

    /**
     * @description 被转岗人转岗前与转岗后职位是否一致
     * @return array
     */
    public function getFormOfWeatherTheJobTitleConsistency(): array
    {
        return [
            'type' => WorkflowBranchEnums::BRANCH_CATEGORY_TRANSFER_WEATHER_JOB_TITLE_CONSISTENCY,
            'name' => self::$t->_('branch_category_178'),
            'operator' => [
                self::getOperatorEqual(),
            ],
            'option_cnf' => BranchOptionsConfigService::getInstance()->getNormalOptionsCnf(),
            'option'      => [
                [
                    'key'   => self::$t->_('view_yes'),// '是',
                    'value' => WorkflowBranchEnums::OPTION_YES,
                ],
                [
                    'key'   => self::$t->_('view_no'),//'否',
                    'value' => WorkflowBranchEnums::OPTION_NO,
                ],
            ],
        ];
    }



    /**
     * 表单-是否接口集成的数据
     * @return array
     */
    public function getIsIntegrated(): array
    {
        return [
            'type'        => WorkflowBranchEnums::BRANCH_CATEGORY_INTEGRATED,
            'name'        => self::$t->_('branch_category_' . WorkflowBranchEnums::BRANCH_CATEGORY_INTEGRATED),
            'operator'    => [
                // 等于
                $this->getOperatorEqual(),
            ],
            'option_cnf' => BranchOptionsConfigService::getInstance()->getNormalOptionsCnf(),
            //通过接口获取下拉
            'option'      => [
                [
                    'key'   => self::$t->_('view_yes'),// '是',
                    'value' => WorkflowBranchEnums::OPTION_YES,
                ],
                [
                    'key'   => self::$t->_('view_no'),//'否',
                    'value' => WorkflowBranchEnums::OPTION_NO,
                ],
            ],
        ];
    }

    /**
     * 表单-应付总金额（含VAT含WHT）
     * @return array
     */
    protected function getPayableAmount(): array
    {
        return [
            'type'        => WorkflowBranchEnums::BRANCH_CATEGORY_PAYABLE_AMOUNT,
            'name'        => self::$t->_('branch_category_' . WorkflowBranchEnums::BRANCH_CATEGORY_PAYABLE_AMOUNT),
            'operator'    => [
                // 等于
                $this->getOperatorEqual(),
                //大于等于
                $this->getOperatorGe(),
                //小于等于
                $this->getOperatorLe(),
                //大于
                $this->getOperatorGt(),
                //小于
                $this->getOperatorLt(),
            ],
            'option_cnf' => BranchOptionsConfigService::getInstance()->getInputNumberOptionsCnf(),
            'option'     => [],
        ];
    }

    /**
     * 表单-实付总金额（含VAT不含WHT）
     * @return array
     */
    protected function getAmountTotalActually(): array
    {
        return [
            'type'        => WorkflowBranchEnums::BRANCH_CATEGORY_AMOUNT_TOTAL_ACTUALLY,
            'name'        => self::$t->_('branch_category_' . WorkflowBranchEnums::BRANCH_CATEGORY_AMOUNT_TOTAL_ACTUALLY),
            'operator'    => [
                // 等于
                $this->getOperatorEqual(),
                //大于等于
                $this->getOperatorGe(),
                //小于等于
                $this->getOperatorLe(),
                //大于
                $this->getOperatorGt(),
                //小于
                $this->getOperatorLt(),
            ],
            'option_cnf' => BranchOptionsConfigService::getInstance()->getInputNumberOptionsCnf(),
            'option'     => [],
        ];
    }


    /**
     * 表单-费用所属公司
     * @return array
     */
    protected function getCostDepartment(): array
    {
        return $this->getBelongDepartment(WorkflowBranchEnums::BRANCH_CATEGORY_COST_DEPARTMENT, self::$t->_('branch_category_169'));
    }

    /**
     * 表单-费用所属公司
     * @return array
     */
    protected function getCostCompany(): array
    {
        return $this->getCompany(WorkflowBranchEnums::BRANCH_CATEGORY_COST_COMPANY, self::$t->_('branch_category_170'));
    }

    /**
     * 表单-费用类型
     * @return array
     */
    protected function getCostType(): array
    {
        $costTypeEnum = AgencyPaymentService::getCostTypeEnumsList();
        return [
            'type'        => WorkflowBranchEnums::BRANCH_CATEGORY_COST_TYPE,
            'name'        => self::$t->_('branch_category_' . WorkflowBranchEnums::BRANCH_CATEGORY_COST_TYPE),
            //申请人负责部门的最高级别
            'operator'    => [
                // 等于
                $this->getOperatorEqual(),
                // 不等于
                $this->getOperatorNotEqual(),
                // 包含
                $this->getOperatorContain(),
                // 不包含
                $this->getOperatorNotContain(),
            ],
            'option_cnf' => BranchOptionsConfigService::getInstance()->getNormalOptionsCnf(),
            //通过接口获取下拉
            'option'      => $costTypeEnum,
        ];
    }

    /** ------------------------------- 下面是辅助函数，分支条件请写到分界线上面  -------------------------------- */

    /**
     * @description:获取车辆相关枚举
     *
     * @param null
     *
     * @return     :
     * <AUTHOR> L.J
     * @time       : 2022/3/16 11:23
     */
    protected function getSvcEnumVehicle()
    {
        //获取来源
        $data = [];
        try {
            $redis    = $this->getDI()->get('redis');
            $redisKey = 'wf_branch_getSvcEnumVehicle' . static::$language;
            $cache    = $redis->get($redisKey);
            if (!empty($cache)) {
                return json_decode($cache, true);
            }

            $ac = new ApiClient('by', '', 'getEnumVehicle', static::$language);
            $ac->setParams([[]]);
            $res = $ac->execute();

            $data = $res['result']['data'] ?? [];
            $redis->setex($redisKey, 60 * 5, json_encode($data, JSON_UNESCAPED_UNICODE));
        } catch (\Exception $e) {
            $this->logger->error($e->getMessage());
        }
        return $data;
    }

    /**
     * @description:获取举报枚举类型
     * @return     :
     * <AUTHOR> L.J
     * @time       : 2022/3/16 11:23
     */
    protected function getSvcReportEnum()
    {
        //获取来源
        $data = [];
        try {
            $redis     = $this->getDI()->get('redis');
            $redis_key = 'by_get_svc_report_enum_' . static::$language;
            //这里先读缓存
            $cache = $redis->get($redis_key);
            if ($cache) {
                $this->logger->info('getSvcReportEnum 返回参数: key' . $redis_key . ' 内容' . $cache);
                return json_decode($cache, true);
            }
            $ApiClient = new ApiClient('by', '', 'getSvcReportEnum', static::$language);
            $ApiClient->setParams([[]]);
            $res  = $ApiClient->execute();
            $data = $res['result']['data'] ?? [];

            $reasonId = array_keys($data["reason"]);
            array_multisort($reasonId, SORT_ASC, $data["reason"]);

            $redis->setex($redis_key, 60 * 5, json_encode($data, JSON_UNESCAPED_UNICODE));
        } catch (\Exception $e) {
            $this->logger->error('getSvcReportEnum ' .
                ' code ' . $e->getCode() .
                ' file ' . $e->getFile() .
                ' line ' . $e->getLine() .
                ' message ' . $e->getMessage() .
                ' trace ' . $e->getTraceAsString()
            );
        }
        return $data;
    }

    /**
     * 培训 学习计划-计划类型
     * @return array[]
     */
    protected function getLearningPlanType(): array
    {
        return [
            ['value' => 1, 'key' => self::$t->_('school.learning_plan_type_1')],
            ['value' => 2, 'key' => self::$t->_('school.learning_plan_type_2')],
            ['value' => 3, 'key' => self::$t->_('school.learning_plan_type_3')],
        ];
    }
    /**
     * 获取全部公司
     */
    protected function getAllCompany()
    {
        $builder = $this->modelsManager->createBuilder();
        $builder->columns('id as value, name as key');
        $builder->from(SysDepartmentModel::class);
        $builder->where('type = 1 and deleted = 0');
        $builder->orderBy('id ASC');
        return $builder->getQuery()->execute()->toArray();
    }

    /**
     * @description 获取国籍枚举
     * @return array
     */
    protected function getNationalityEnums(): array
    {
        //获取来源
        try {
            $redis    = $this->getDI()->get('redis');
            $redisKey = 'wf_branch_getNationalityEnums' . static::$language;
            $cache    = $redis->get($redisKey);
            if (!empty($cache)) {
                return json_decode($cache, true);
            }
            $ac = new ApiClient('hcm_rpc', '', 'getDictionaryByDictCode', static::$language);
            $ac->setParams([['dict_code' => 'nationality_region']]);
            $res = $ac->execute();

            $result = [];
            foreach ($res['result']['data'] as $v) {
                $result[] = [
                    'key'   => $v['label'],
                    'value' => $v['value'],
                ];
            }
            $redis->setex($redisKey, 60 * 5, json_encode($result, JSON_UNESCAPED_UNICODE));
        } catch (\Exception $e) {
            $this->logger->error($e->getMessage());
        }
        return $result;
    }

    /**
     * OT-所在网点当天人效
     * @return array
     */
    private function getStaffEffectRate(): array
    {
        return [
            'type'        => WorkflowBranchEnums::BRANCH_CATEGORY_OT_STAFF_EFFECT_RATE,
            'name'        => self::$t->_('branch_category_' . WorkflowBranchEnums::BRANCH_CATEGORY_OT_STAFF_EFFECT_RATE),
            'operator'    => [
                // 等于
                $this->getOperatorEqual(),
                // 不等于
                $this->getOperatorNotEqual(),
                // 大于
                $this->getOperatorGt(),
                // 大于等于
                $this->getOperatorGe(),
                //小于
                $this->getOperatorLt(),
                //小于等于
                $this->getOperatorLe(),
            ],
            'option_cnf' => BranchOptionsConfigService::getInstance()->getInputNumberOptionsCnf(),
            //通过接口获取下拉
            'option'      => [],
        ];
    }

    /**
     * 获取所属大区
     * @return array
     */
    private function getBelongToRegion(): array
    {
        return [
            'type'       => WorkflowBranchEnums::BRANCH_CATEGORY_BELONG_REGION,
            'name'       => self::$t->_('applicant_region'),
            'operator'   => [
                self::getOperatorEqual(),
                self::getOperatorContain(),
                self::getOperatorNotEqual(),
                self::getOperatorNotContain(),
            ],
            'option_cnf' => BranchOptionsConfigService::getInstance()->getRegionOptionsCnf(),
            'option'     => [],
        ];
    }

    /**
     * 获取所属片区
     * @return array
     */
    private function getBelongToPiece(): array
    {
        return [
            'type'       => WorkflowBranchEnums::BRANCH_CATEGORY_BELONG_PIECE,
            'name'       => self::$t->_('applicant_piece'),
            'operator'   => [
                self::getOperatorEqual(),
                self::getOperatorContain(),
                self::getOperatorNotEqual(),
                self::getOperatorNotContain(),
            ],
            'option_cnf' => BranchOptionsConfigService::getInstance()->getPieceOptionsCnf(),
            'option'     => [],
        ];
    }

    /**
     * 表单-被转岗人转岗前雇佣类型
     * @return array
     */
    private function getFormOfAfterHireType(): array
    {
        return $this->getHireType(WorkflowBranchEnums::BRANCH_CATEGORY_TRANSFER_AFTER_HIRE_TYPE,
            self::$t->_('workflow_branch_183'));
    }

    /**
     * 表单-预算科目
     * @return array
     */
    private function getBudgetAccount(): array
    {
        $budgetObject =  BudgetObjectRepository::getInstance(static::$language)->getListByName();
        $budgetObject = array_map(function ($item) {
            return [
                'value' => $item['id'],
                'key'   => $item['object_name'],
            ];
        }, $budgetObject);

        return [
            'type'       => WorkflowBranchEnums::BRANCH_CATEGORY_BUDGET_ACCOUNT,
            'name'       => self::$t->_('budget_adjust_subject'),
            'operator'   => [
                self::getOperatorEqual(),
                self::getOperatorContain(),
                self::getOperatorNotEqual(),
                self::getOperatorNotContain(),
            ],
            'option_cnf' => BranchOptionsConfigService::getInstance()->getNormalOptionsCnf(),
            'option'     => $budgetObject,
        ];
    }

    /**
     * 表单-预提金额
     * @return array
     */
    private function getPreWithdrawalAmount(): array
    {
        return [
            'type'       => WorkflowBranchEnums::BRANCH_CATEGORY_PRE_WITHDRAWAL_AMOUNT,
            'name'       => self::$t->_('budget_withholding.provision_amount'),
            'operator'   => [
                self::getOperatorEqual(),
                self::getOperatorNotEqual(),
            ],
            'option_cnf' => BranchOptionsConfigService::getInstance()->getInputNumberOptionsCnf(),
            'option'     => [],
        ];
    }
}