<?php

namespace app\modules\WorkflowManagement\services;

use App\Library\Enums;
use App\Library\Enums\WorkflowManageEnums;
use App\Library\Enums\WorkflowNodeEnums;
use App\Modules\WorkflowManagement\Models\ByWorkflowModel;

class NodeService extends BaseNodeService
{
    private static $instance;

    private function __construct()
    {
    }

    /**
     * 获取实例
     * @return NodeService
     */
    public static function getInstance(): NodeService
    {
        if (!isset(self::$instance) || !(self::$instance instanceof self)) {
            self::$instance = new self;
        }
        return self::$instance;
    }

    /**
     * 获取审批流审批人表单项
     */
    public function getWorkflowFormCond($params): array
    {
        //获取审批流id
        $flowId = $params['id'] ?? 0;

        //获取审批流请求
        $flowInfo = ByWorkflowModel::findFirst([
            'conditions' => 'id = :flow_id: and is_view = :is_view: ',
            'bind'       => [
                'flow_id'  => $flowId,
                'is_view' => ByWorkflowModel::IS_VIEW,
            ],
        ]);
        if (!empty($flowInfo)) {
            $flowInfo = $flowInfo->toArray();
        }

        $commonList             = $this->commonWorkflowFormCond();
        $individualFormItemList = $this->getIndividualFormItem(WorkflowManageEnums::SCOPE_PART, $flowInfo['relate_type']);
        $editFieldList          = FormService::getInstance()->getEditFieldByFlowId($flowInfo['relate_type']);
        $overtimeConfig         = ConfigService::getInstance()->getSingleNodeOvertimeConfig($flowInfo['relate_type']);

        return [
            'common'    => $commonList,
            'form'      => $individualFormItemList,
            'editField' => $editFieldList,
            'overtime'  => $overtimeConfig,
        ];
    }

    /**
     * @description 获取各个审批下独有的表单项，根据表单项查找审批人
     * @param $scope
     * @param $id
     * @return array|mixed
     */
    public function getIndividualFormItem($scope, $id = null)
    {
        $individualList = $this->getIndividualFormItemConfig();
        $list           = [];
        if ($scope == WorkflowManageEnums::SCOPE_PART && !is_null($id)) {
            if (isset($individualList[$id])) {
                $list = $individualList[$id];
            }
        } else {
            $list = array_values($individualList);
        }

        return $list;
    }

    /**
     * @description 获取各个审批下独有的表单项配置,配置表单项下的审批人查找逻辑，在这里配置！！！
     * @return array
     */
    public function getIndividualFormItemConfig(): array
    {
        return [
            ByWorkflowModel::APPROVAL_TYPE_SALARY            => $this->getSalaryDepartmentFormItem(),
            ByWorkflowModel::APPROVAL_TYPE_REPORT            => $this->getReportFromApprovalFormItem(),
            ByWorkflowModel::APPROVAL_TYPE_HC                => $this->getHcFormItem(),
            ByWorkflowModel::APPROVAL_TYPE_OS                => $this->getOsFormItem(),
            ByWorkflowModel::APPROVAL_TYPE_OUTSOURCING_OT    => $this->getOsOTFormItem(), //hub外协加班审批
            ByWorkflowModel::APPROVAL_TYPE_HUB_OS_AT         => $this->getOsATFormItem(),
            ByWorkflowModel::APPROVAL_TYPE_REINSTATEMENT     => $this->getReinstatementItem(),
            ByWorkflowModel::APPROVAL_TYPE_REEMPLOYMENT      => $this->getReemploymentFormItem(),
            ByWorkflowModel::APPROVAL_TYPE_JT                => $this->getTransferFormItem(),
            ByWorkflowModel::APPROVAL_TYPE_JT_STAGE_TWO      => $this->getTransferSecondFormItem(),
            ByWorkflowModel::APPROVAL_TYPE_SUSPEND_WORK      => $this->getSuspendWorkFormItem(),
            ByWorkflowModel::APPROVAL_TYPE_OA_AGENCY_PAYMENT => $this->getCostDepartment(),
            ByWorkflowModel::APPROVAL_TYPE_OVERTIME_OS       => $this->getOsOvertimeFormItem(), //外协延长工时
            ByWorkflowModel::APPROVAL_TYPE_SUSPENSION_STATE  => $this->getSuspensionFormItem(),
            ByWorkflowModel::APPROVAL_TYPE_WAREHOUSE_THREAD_PRICE=> $this->getWarehouseThreadPriceFormItem(),
        ];
    }

    /**
     * @description 薪资所属部门
     * @return array
     */
    public function getSalaryDepartmentFormItem(): array
    {
        //所属部门关联选项
        $deptConditions = $this->getDeptConditions();
        $deptCondList   = array_map(function ($v) {
            return [
                'key'   => $v['key'],
                'label' => $v['label'],
                'type'  => $v['type'],
            ];
        }, $deptConditions);

        //薪资审批
        return [
            'formList'     => [
                [
                    'key'   => WorkflowNodeEnums::APPROVAL_FROM_OPTIONS_SALARY_DEPARTMENT,
                    'label' => self::$t->_('view_department'),//'所属部门',
                ],
            ],
            'approvalType' => [
                WorkflowNodeEnums::APPROVAL_FROM_OPTIONS_SALARY_DEPARTMENT => $deptCondList,
            ],
            'options'      => array_column($deptConditions, 'options', 'key'),
        ];
    }

    /**
     * @description 获取举报
     * @return array
     */
    public function getReportFromApprovalFormItem(): array
    {
        //举报
        //获取举报的表单
        $deptConditions = $this->getReportFromApproval();
        $deptCondList   = array_map(function ($v) {
            return [
                'key'   => $v['key'],
                'label' => $v['label'],
                'type'  => $v['type'],
            ];
        }, $deptConditions);

        return [
            'formList'     => [
                [
                    'key'   => WorkflowNodeEnums::APPROVAL_FROM_OPTIONS_REPORT_STAFF,
                    'label' => self::$t->_('workflow.from_report_staff'),//'表单员工-被举报员工',
                ],
            ],
            'approvalType' => [
                WorkflowNodeEnums::APPROVAL_FROM_OPTIONS_REPORT_STAFF => $deptCondList,
            ],
            'options'      => array_column($deptConditions, 'options', 'key'),
        ];
    }

    //大区/片区/网点负责人
    public function getOrgAreaManager(): array
    {
        return [
            'key'     => Enums::NODE_AUDITOR_TYPE_ORG_AREA_MANAGER,
            'label'   => static::$t->_('view_org_manager'),//大区/片区/网点负责人
            'type'    => Enums::CONDITION_TYPE_OPTION,
            'options' => [
                [
                    'key'   => 3,
                    'label' => static::$t->_('view_org_region_mgr'), //大区负责人
                ],
                [
                    'key'   => 2,
                    'label' => static::$t->_('view_org_piece_mgr'), //片区负责人
                ],
                [
                    'key'   => 1,
                    'label' => static::$t->_('view_org_store_mgr'), //网点负责人
                ],
            ],
        ];
    }

    /**
     * @description 获取bu/CLevel负责人
     * @return array
     */
    public function getOrgBuOrClevelManager(): array
    {
        return [
            'key'     => Enums::NODE_AUDITOR_TYPE_BU_CL_MGR,
            'label'   => self::$t->_('view_bu_clevel_mgr'),//BU/C-level负责人
            'type'    => Enums::CONDITION_TYPE_API,
            'options' => [],
        ];
    }

    /**
     * @description 获取指定部门负责人
     * @return array
     */
    public function getSpecifyDepartmentManager(): array
    {
        return [
            'key'     => Enums::NODE_AUDITOR_TYPE_SPEC_DEP_ORG_MGR,
            'label'   => self::$t->_('view_spec_org_mgr'),//指定组织负责人
            'type'    => Enums::CONDITION_TYPE_API,
            'options' => [],
        ];
    }

    /**
     * 获取部门相关条件
     * @return array
     */
    public function getDeptConditions(): array
    {
        return [
            //部门
            $this->getOrgDepartmentManager(),
            $this->getSpecifyDepartmentManager(),
            $this->getOrgBuOrClevelManager(),
        ];
    }

    /**
     * @description:举报审批人表单项
     * @param null
     * @return:
     * @author: L.J
     * @time: 2022/12/19 11:41
     */
    public function getReportFromApproval(): array
    {
        return [
            //部门负责人
            $this->getOrgDepartmentManager(),
            //大区/片区/网点负责人
            $this->getOrgAreaManager(),
            //上级
            $this->getSpecifySuperior(),
            //角色
            $this->getSpecRoles(),
        ];
    }

    //部门负责人
    public function getOrgDepartmentManager(): array
    {
        return [
            'key'     => Enums::NODE_AUDITOR_TYPE_ORG_MANAGER,
            'label'   => self::$t->_('view_department_manager'),//部门负责人
            'type'    => Enums::CONDITION_TYPE_OPTION,
            'options' => [
                [
                    'key'   => 0,
                    'label' => self::$t->_('principal_of_the_organisation'), // 所属组织负责人
                ],
                [
                    'key'   => 1,
                    'label' => self::$t->_('principal_of_a_division'), //一级组织负责人
                ],
                [
                    'key'   => 2,
                    'label' => self::$t->_('principal_of_a_department'), // '二级组织负责人',
                ],
                [
                    'key'   => 3,
                    'label' => self::$t->_('principal_of_a_sub_department'),// '三级组织负责人',
                ],
                [
                    'key'   => 4,
                    'label' => self::$t->_('principal_of_a_sub_department2'), // '四级组织负责人',
                ],
                [
                    'key'   => 101,
                    'label' => self::$t->_('principal_of_a_bu'),//'所属BU负责人',
                ],
                [
                    'key'   => 150,
                    'label' => self::$t->_('principal_of_a_clevel'),//'所属C-level负责人',
                ],
            ],
        ];
    }

    //获取上级条件
    public function getSpecifySuperior(): array
    {
        return [
            'key'     => Enums::NODE_AUDITOR_TYPE_SPECIFY_SUPERIOR,
            'label'   => self::$t->_('view_specify_superior'),//指定上级
            'type'    => Enums::CONDITION_TYPE_OPTION,
            'options' => [
                [
                    'key'   => 1,
                    'label' => static::$t->_('view_staff_leader'),//直线上级（1级上级)
                ],
                [
                    'key'   => 2,
                    'label' => static::$t->_('view_staff_leader_n', ['level' => 2]),//2级上级
                ],
                [
                    'key'   => 3,
                    'label' => static::$t->_('view_staff_leader_n', ['level' => 3]),
                ],
                [
                    'key'   => 4,
                    'label' => static::$t->_('view_staff_leader_n', ['level' => 4]),
                ],
                [
                    'key'   => 5,
                    'label' => static::$t->_('view_staff_leader_n', ['level' => 5]),
                ],
                [
                    'key'   => 6,
                    'label' => static::$t->_('view_staff_leader_n', ['level' => 6]),
                ],
                [
                    'key'   => 7,
                    'label' => static::$t->_('view_staff_leader_n', ['level' => 7]),
                ],
                [
                    'key'   => 8,
                    'label' => static::$t->_('view_staff_leader_n', ['level' => 8]),
                ],
                [
                    'key'   => 9,
                    'label' => static::$t->_('view_staff_leader_n', ['level' => 9]),
                ],
                [
                    'key'   => 10,
                    'label' => static::$t->_('view_staff_leader_n', ['level' => 10]),
                ],
            ],
        ];
    }

    //获取暂停接单角色指定条件
    public function getSuspendWorkRoles(): array
    {
        return [
            'key'     => Enums::NODE_AUDITOR_TYPE_SPECIFY_ROLE,
            'label'   => static::$t->_('view_specify_role'),//指定上级
            'type'    => Enums::CONDITION_TYPE_OPTION,
            'options' => [
                [
                    'key'   => 68,
                    'label' => static::$t->_('role_hr_bp'),//HRBP
                ],
                [
                    'key'   => 77,
                    'label' => static::$t->_('role_hr_service'),//HR Service
                ],
            ],
        ];
    }

    //获取指定条件
    public function getSpecRoles(): array
    {
        return [
            'key'   => Enums::NODE_AUDITOR_TYPE_SPECIFY_ROLE,
            'label' => static::$t->_('view_specify_role'),//指定角色
            'type'  => Enums::CONDITION_TYPE_API,
        ];
    }

    /**
     * 新版分拨大区经理，网点 （3级部门）对应上级部门部门负责人
     * @return array
     */
    public function getHubAreaManager(): array
    {
        return [
            'key'   => Enums::NODE_AUDITOR_TYPE_HUB_AREA_MANAGER,
            'label' => self::$t->_('view_specify_staff_by_hashtable'),
            'type'  => Enums::CONDITION_TYPE_API,
        ];
    }

    /**
     * 新版分拨经理
     * @return array
     */
    public function getHubStoreManager(): array
    {
        return [
            'key'   => Enums::NODE_AUDITOR_TYPE_HUB_STORE_MANAGER,
            'label' => self::$t->_('role_60'),
            'type'  => Enums::CONDITION_TYPE_API,
        ];
    }

    //根据哈希表获取指定审批人
    public function getHubStandardizationOrAdmin(): array
    {
        return [
            'key'   => Enums::NODE_AUDITOR_TYPE_HUB_STANDARDIZATION_AND_ADMIN,
            'label' => self::$t->_('view_hub_standardization_and_admin'),
            'type'  => Enums::CONDITION_TYPE_API,
        ];
    }

    //指定成员
    public function getSpecifyStaff(): array
    {
        return [
            'key'   => Enums::NODE_AUDITOR_TYPE_SPECIFY_STAFF,
            'label' => self::$t->_('view_specify_staff'),//指定成员
            'type'  => Enums::CONDITION_TYPE_API,        //1=特殊类型(需前端调用接口处理) 2= options
        ];
    }

    //指定职能管理
    public function getSpecOrgManager(): array
    {
        return [
            'key'   => Enums::NODE_AUDITOR_TYPE_SPEC_ORG_MGR,
            'label' => static::$t->_('view_spe_func_mgt'),//指定职能管理
            'type'  => Enums::CONDITION_TYPE_API,
        ];
    }

    //员工本人
    public function getFormStaffSelf(): array
    {
        return [
            'key'   => Enums::NODE_AUDITOR_TYPE_STAFF_SELF,
            'label' => static::$t->_('view_staff_self'), //工号本人
            'type'  => Enums::CONDITION_TYPE_API,
        ];
    }

    //指定职位
    public function getSpecJobTitle(): array
    {
        return [
            'key'     => Enums::NODE_AUDITOR_TYPE_SPECIFY_POSITION,
            'label'   => static::$t->_('view_specify_job_title'),//指定职位
            'type'    => Enums::CONDITION_TYPE_API,
            'options' => [
                [
                    'key'   => WorkflowManageEnums::SPECIFY_POSITION_QUALIFY_COND_NO_LIMIT,
                    'label' => static::$t->_('view_not_limit'), //不限制
                ],
                [
                    'key'   => WorkflowManageEnums::SPECIFY_POSITION_QUALIFY_COND_SAME_DEPARTMENT,
                    'label' => static::$t->_('view_be_same_with_submitter_department'), //指定职位-与申请人所在部门相同
                ],
                [
                    'key'   => WorkflowManageEnums::SPECIFY_POSITION_QUALIFY_COND_SAME_LEVEL_DEPARTMENT,
                    'label' => static::$t->_('view_be_same_with_submitter_department_level_1'), //指定职位-与申请人所在一级部门相同
                ],
                [
                    'key'   => WorkflowManageEnums::SPECIFY_POSITION_QUALIFY_COND_SPEC_DEPARTMENT,
                    'label' => static::$t->_('plan_wms_department'), //获取指定部门指定职位的人
                ],
            ],
        ];
    }

    /**
     * 获取全部条件，用于反解析
     * @return array
     */
    public function getAllWorkflowNodeConditions(): array
    {
        //合并通用条件 & 各个审批定制条件
        return array_merge($this->commonWorkflowFormCond(), $this->getWorkflowNodeFormConditions());
    }

    /**
     * 获取全部条件，用于反解析
     * @return array
     */
    public function getWorkflowNodeFormConditions(): array
    {
        return $this->conversionStructure($this->getIndividualFormItem(WorkflowManageEnums::SCOPE_ALL));
    }

    public function getAllForm(): array
    {
        return $this->getIndividualFormItem(WorkflowManageEnums::SCOPE_ALL);
    }

    /**
     * @description 将Form的结构转换成通用结构
     * @param $form_data
     * @return array
     */
    private function conversionStructure($form_data): array
    {
        if (empty($form_data)) {
            return [];
        }

        $approvalList = [];
        $optionsList  = [];
        foreach ($form_data as $form) {
            if (empty($form['approvalType'])) {
                continue;
            }
            foreach ($form['approvalType'] as $item) {
                $approvalList = array_merge($approvalList, $item);
            }
            foreach ($form['options'] as $key => $item) {
                $optionsList[$key] = $item;
            }
        }
        foreach ($approvalList as $key => $approvalTypeItem) {
            if (empty($approvalTypeItem)) {
                continue;
            }
            $typeId = $approvalTypeItem['key'];
            $approvalList[$key]['options'] = $optionsList[$typeId] ?? [];
        }
        return $approvalList;
    }

    /**
     * 返回通用条件
     * @return array
     */
    public function commonWorkflowFormCond(): array
    {
        $commonList   = [
            $this->getSpecifyStaff(),
            //指定上级
            $this->getSpecifySuperior(),
            //获取指定角色
            $this->getSpecRoles(),
            //区域负责人
            $this->getOrgAreaManager(),
            //职能管理
            $this->getSpecOrgManager(),
            //指定职位
            $this->getSpecJobTitle(),
        ];
        $deptCondList = $this->getDeptConditions();

        return array_merge($commonList, $deptCondList);
    }

    /**
     * @description 处理返回结构
     * @param $conditions
     * @return array[]
     */
    private function handleStructure($conditions): array
    {
        $conditionsApprovalType = [];
        $conditionsOptions = [];
        foreach ($conditions as $formColumnKey => $condition) {
            $conditionsArr = array_map(function ($v) {
                return [
                    'key'   => $v['key'],
                    'label' => $v['label'],
                    'type'  => $v['type'],
                ];
            }, $condition);
            $conditionsApprovalType[$formColumnKey] = $conditionsArr;

            $optionsList = array_column($condition, 'options', 'key');
            foreach ($optionsList as $approvalType => $options) {
                $conditionsOptions[$approvalType] = $options;
            }
        }
        return [$conditionsApprovalType, $conditionsOptions];
    }

    /**
     * @description 获取HC表单配置
     * @return array
     */
    private function getHcFormItem(): array
    {
        $conditions    = [
            WorkflowNodeEnums::APPROVAL_FROM_OPTIONS_HC_DEPARTMENT => [
                $this->getOrgDepartmentManager(), //部门
                $this->getOrgBuOrClevelManager(), //BU/CLevel负责人
            ],
            WorkflowNodeEnums::APPROVAL_FROM_OPTIONS_HC_STORE => [
                $this->getOrgAreaManager(),       //大区/片区/网点负责人
            ],
            WorkflowNodeEnums::APPROVAL_FROM_OPTIONS_HC_DEPARTMENT_STORE => [
                $this->getSpecRoles(),            //获取指定角色
            ],
        ];
        [$approvalTypeArr, $optionsArr] = $this->handleStructure($conditions);
        return [
            'formList'     => [
                [
                    'key'   => WorkflowNodeEnums::APPROVAL_FROM_OPTIONS_HC_DEPARTMENT,
                    'label' => self::$t->_('workflow.from_hc_department'),//HC申请-表单项-所属部门,
                ],
                [
                    'key'   => WorkflowNodeEnums::APPROVAL_FROM_OPTIONS_HC_STORE,
                    'label' => self::$t->_('workflow.from_hc_store'),//HC申请-表单项-工作网点,
                ],
                [
                    'key'   => WorkflowNodeEnums::APPROVAL_FROM_OPTIONS_HC_DEPARTMENT_STORE,
                    'label' => self::$t->_('workflow.from_hc_department_and_store'),//HC申请-表单项-所属部门-工作网点,
                ],
            ],
            'approvalType' => $approvalTypeArr,
            'options'      => $optionsArr,
        ];
    }

    /**
     * 获取恢复在职申请表单配置
     * @return array
     */
    public function getReinstatementItem(): array
    {
        $conditions    = [
            WorkflowNodeEnums::APPROVAL_FROM_OPTIONS_REINSTATEMENT => [
                $this->getOrgDepartmentManager(), //部门
                $this->getOrgAreaManager(),       //区域负责人
                $this->getSpecifySuperior(),      //上级
                $this->getSpecRoles(),           //获取指定角色
            ],
        ];
        [$approvalTypeArr, $optionsArr] = $this->handleStructure($conditions);

        return [
            'formList'     => [
                [
                    'key'   => WorkflowNodeEnums::APPROVAL_FROM_OPTIONS_REINSTATEMENT,
                    'label' => self::$t->_('workflow.from_reinstatement_staff'),  //按照表单-表单字段：恢复在职员工
                ],
            ],
            'approvalType' => $approvalTypeArr,
            'options'      => $optionsArr,
        ];
    }

    /**
     * @description 获取转岗表单配置
     * @return array
     */
    public function getTransferFormItem(): array
    {
        $conditions    = [
            WorkflowNodeEnums::APPROVAL_FROM_OPTIONS_TRANSFER_BEFORE => [
                $this->getOrgDepartmentManager(), //部门
                $this->getOrgBuOrClevelManager(), //BU/CLevel负责人
                $this->getOrgAreaManager(),       //区域负责人
                $this->getSpecRoles(),            //获取指定角色
            ],
            WorkflowNodeEnums::APPROVAL_FROM_OPTIONS_TRANSFER_AFTER => [
                $this->getSpecRoles(),            //获取指定角色
            ],
        ];
        [$approvalTypeArr, $optionsArr] = $this->handleStructure($conditions);
        return [
            'formList'     => [
                [
                    'key'   => WorkflowNodeEnums::APPROVAL_FROM_OPTIONS_TRANSFER_BEFORE,
                    'label' => self::$t->_('workflow.from_transfer_before_department'),//转岗申请-转岗前部门
                ],
                [
                    'key'   => WorkflowNodeEnums::APPROVAL_FROM_OPTIONS_TRANSFER_AFTER,
                    'label' => self::$t->_('workflow.from_transfer_after_department'),//转岗申请-转岗后部门
                ],
            ],
            'approvalType' => $approvalTypeArr,
            'options'      => $optionsArr,
        ];
    }

    /**
     * @description 获取转岗表单配置
     * @return array
     */
    public function getTransferSecondFormItem(): array
    {
        $conditions    = [
            WorkflowNodeEnums::APPROVAL_FROM_OPTIONS_TRANSFER_AFTER => [
                $this->getOrgDepartmentManager(), //部门
                $this->getOrgBuOrClevelManager(), //BU/CLevel负责人
                $this->getOrgAreaManager(),       //区域负责人
                $this->getSpecRoles(),            //获取指定角色
            ],
        ];
        [$approvalTypeArr, $optionsArr] = $this->handleStructure($conditions);
        return [
            'formList'     => [
                [
                    'key'   => WorkflowNodeEnums::APPROVAL_FROM_OPTIONS_TRANSFER_AFTER,
                    'label' => self::$t->_('workflow.from_transfer_after_department'),//转岗申请-转岗后部门
                ],
            ],
            'approvalType' => $approvalTypeArr,
            'options'      => $optionsArr,
        ];
    }

    /**
     * @description 获取外协表单配置
     * @return array
     */
    public function getOsFormItem(): array
    {
        $conditions    = [
            WorkflowNodeEnums::APPROVAL_FROM_OPTIONS_OS_STORE => [
                //$this->getSpecifyStaffByHash(),
                $this->getHubAreaManager(),       //分拨大区经理
                $this->getHubStoreManager(),      //分拨经理
                $this->getOrgAreaManager(),       //区域负责人
            ],
        ];
        [$approvalTypeArr, $optionsArr] = $this->handleStructure($conditions);
        return [
            'formList'     => [
                [
                    'key'   => WorkflowNodeEnums::APPROVAL_FROM_OPTIONS_OS_STORE,
                    'label' => self::$t->_('workflow.from_hc_store'),//外协申请-表单项-网点
                ],
            ],
            'approvalType' => $approvalTypeArr,
            'options'      => $optionsArr,
        ];
    }

    /**
     * 停职申请-审批人表单
     * @return array
     */
    public function getSuspensionFormItem(): array
    {
        $conditions    = [
            WorkflowNodeEnums::APPROVAL_FROM_OPTIONS_OS_STORE => [
                $this->getHubStoreManager(),      //分拨经理
                $this->getOrgAreaManager(),       //区域负责人
            ],
        ];
        [$approvalTypeArr, $optionsArr] = $this->handleStructure($conditions);
        return [
            'formList'     => [
                [
                    'key'   => WorkflowNodeEnums::APPROVAL_FROM_OPTIONS_OS_STORE,
                    'label' => self::$t->_('workflow.from_hc_store'),//外协申请-表单项-网点
                ],
            ],
            'approvalType' => $approvalTypeArr,
            'options'      => $optionsArr,
        ];
    }

    /**
     * @description 获取外协表单配置
     * @return array
     */
    public function getOsOTFormItem(): array
    {
        $conditions    = [
            WorkflowNodeEnums::APPROVAL_FROM_OPTIONS_OS_OT_STORE => [
                $this->getHubAreaManager(),       //分拨大区经理
                $this->getHubStoreManager(),      //分拨经理
                $this->getOrgAreaManager(),       //区域负责人
            ],
        ];
        [$approvalTypeArr, $optionsArr] = $this->handleStructure($conditions);
        return [
            'formList'     => [
                [
                    'key'   => WorkflowNodeEnums::APPROVAL_FROM_OPTIONS_OS_OT_STORE,
                    'label' => self::$t->_('workflow.from_hc_store'),//外协申请-表单项-网点
                ],
            ],
            'approvalType' => $approvalTypeArr,
            'options'      => $optionsArr,
        ];
    }

    /**
     * @description 获取外协表单配置
     * @return array
     */
    public function getOsATFormItem(): array
    {
        $conditions    = [
            WorkflowNodeEnums::APPROVAL_FROM_OPTIONS_OS_AT_STORE => [
                $this->getHubAreaManager(),       //分拨大区经理
                $this->getHubStoreManager(),      //分拨经理
                $this->getOrgAreaManager(),       //区域负责人
                $this->getHubStandardizationOrAdmin(),
            ],
        ];
        [$approvalTypeArr, $optionsArr] = $this->handleStructure($conditions);
        return [
            'formList'     => [
                [
                    'key'   => WorkflowNodeEnums::APPROVAL_FROM_OPTIONS_OS_AT_STORE,
                    'label' => self::$t->_('workflow.from_hc_store'),
                ]
            ],
            'approvalType' => $approvalTypeArr,
            'options'      => $optionsArr,
        ];
    }

    /**
     * 重新雇佣申请审批人表单配置
     * @return array
     */
    public function getReemploymentFormItem()
    {
        $conditions    = [
            WorkflowNodeEnums::APPROVAL_FROM_OPTIONS_REEMPLOYMENT => [
                $this->getOrgDepartmentManager(), //部门
                $this->getOrgAreaManager(),       //区域负责人
                $this->getOrgBuOrClevelManager(),//
                $this->getSpecRoles(),           //获取指定角色
            ],
        ];
        [$approvalTypeArr, $optionsArr] = $this->handleStructure($conditions);

        return [
            'formList'     => [
                [
                    'key'   => WorkflowNodeEnums::APPROVAL_FROM_OPTIONS_REEMPLOYMENT,
                    'label' => self::$t->_('workflow.from_reemployment'),  //按照表单-表单字段：恢复在职员工
                ],
            ],
            'approvalType' => $approvalTypeArr,
            'options'      => $optionsArr,
        ];
    }

    /**
     * 暂停接单申请审批人表单配置
     * @return array
     */
    public function getSuspendWorkFormItem()
    {
        $conditions = [
            WorkflowNodeEnums::APPROVAL_FROM_OPTIONS_SUSPEND_WORK => [
                $this->getOrgDepartmentManager(), //部门
                $this->getOrgAreaManager(),       //区域负责人
                $this->getSpecifySuperior(),      //指定上级
                $this->getSuspendWorkRoles(),     //获取指定角色
            ],
        ];
        [$approvalTypeArr, $optionsArr] = $this->handleStructure($conditions);

        return [
            'formList'     => [
                [
                    'key'   => WorkflowNodeEnums::APPROVAL_FROM_OPTIONS_SUSPEND_WORK,
                    'label' => self::$t->_('workflow.from_suspend_work'),
                ],
            ],
            'approvalType' => $approvalTypeArr,
            'options'      => $optionsArr,
        ];
    }

    /**
     * 表单-费用所属部门
     * @return array
     */
    public function getCostDepartment()
    {
        $conditions    = [
            WorkflowNodeEnums::APPROVAL_FROM_OPTIONS_AGENCY_PAYMENT => [
                $this->getOrgDepartmentManager(), //部门
            ],
        ];
        [$approvalTypeArr, $optionsArr] = $this->handleStructure($conditions);
        return [
            'formList'     => [
                [
                    'key'   => WorkflowNodeEnums::APPROVAL_FROM_OPTIONS_AGENCY_PAYMENT,
                    'label' => self::$t->_('branch_category_169'),  //表单-费用所属部门
                ],
            ],
            'approvalType' => $approvalTypeArr,
            'options'      => $optionsArr,
        ];
    }

    /**
     * 表单 - 工号本人
     * @return array
     */
    public function getOsOvertimeFormItem()
    {
        $conditions    = [
            WorkflowNodeEnums::APPROVAL_FROM_OPTIONS_OS_OT_STAFF_INFO => [
                $this->getFormStaffSelf(), //员工本人
            ],
        ];
        [$approvalTypeArr, $optionsArr] = $this->handleStructure($conditions);
        return [
            'formList'     => [
                [
                    'key'   => WorkflowNodeEnums::APPROVAL_FROM_OPTIONS_OS_OT_STAFF_INFO,
                    'label' => self::$t->_('att_os_staff_no'),  //表单-外协工号
                ],
            ],
            'approvalType' => $approvalTypeArr,
            'options'      => $optionsArr,
        ];
    }

    /**
     * 仓库报价审核-审批人表单配置
     * @return array
     */
    private function getWarehouseThreadPriceFormItem(): array
    {
        $conditions    = [
            WorkflowNodeEnums::APPROVAL_FROM_OPTIONS_WAREHOUSE_THREAD_PRICE => [
                $this->getOrgAreaManager(),       //区域负责人
            ],
        ];
        [$approvalTypeArr, $optionsArr] = $this->handleStructure($conditions);
        return [
            'formList'     => [
                [
                    'key'   => WorkflowNodeEnums::APPROVAL_FROM_OPTIONS_WAREHOUSE_THREAD_PRICE,
                    'label' => self::$t->_('22456_891bf522'),  //表单-使用网点
                ],
            ],
            'approvalType' => $approvalTypeArr,
            'options'      => $optionsArr,
        ];
    }
}