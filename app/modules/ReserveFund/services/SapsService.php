<?php
/**
 * Created by PhpStorm.
 * Date: 2022/4/29
 * Time: 14:06
 */

namespace App\Modules\ReserveFund\Services;

use App\Modules\Purchase\Services\SapsService as SapService;
use App\Modules\Reimbursement\Models\RequestSapLog;


class SapsService extends BaseService
{
    private static $instance;

    private function __construct()
    {
    }

    /**
     * @return  SapsService
     */
    public static function getInstance()
    {
        if (!isset(self::$instance) || !(self::$instance instanceof self)) {
            self::$instance = new self;
        }
        return self::$instance;
    }

    public function ReserveApplySap($data)
    {
        $post_xml = '<soapenv:Envelope xmlns:soapenv="http://schemas.xmlsoap.org/soap/envelope/" xmlns:glob="http://sap.com/xi/SAPGlobal20/Global">									
   <soapenv:Header/>									
   <soapenv:Body>									
      <glob:Cust_ReceivalesPayableCreateRequest_sync>									
         <BasicMessageHeader>									
         </BasicMessageHeader>									
         <Cust_ReceivalesPayable>									
             <!--供应商:-->									
            <BusinessPartnerInternalID>' . $data['supplier'] . '</BusinessPartnerInternalID>									
            <!--公司:-->									
            <CompanyID>' . $data['company'] . '</CompanyID>									
            <!--Optional:-->									
            <ReceivablesPayablesEntryTypeCode>' . $data['credit'] . '</ReceivablesPayablesEntryTypeCode>									
            <!--外部参考号:-->									
            <PartnerBaseBusinessTransactionDocumentReference>' . $data['rfano'] . '</PartnerBaseBusinessTransactionDocumentReference>									
            <!--单据描述:-->									
            <Description>' . $data['description'] . '</Description>									
            <!--默认PHP:-->									
            <TransactionCurrencyCode>' . $data['currency'] . '</TransactionCurrencyCode>									
            <!--过账日期:-->									
            <PostingDate>' . $data['real_pay_at'] . '</PostingDate>									
            <!--单据日期:-->									
            <BusinessTransactionDocumentDate>' . $data['apply_at'] . '</BusinessTransactionDocumentDate>									
            <!--Optional:-->									
            <CountryCode>' . get_country_code() . '</CountryCode>									
            <!--Zero or more repetitions:-->									
            <item>									
               <!--默认值:-->									
               <PropertyMovementDirectionCode>1</PropertyMovementDirectionCode>									
               <!--总账科目:-->									
               <GeneralLedgerAccountAliasCode>' . $data['account_code'] . '</GeneralLedgerAccountAliasCode>									
               <TransactionCurrencyNetAmount ' . ' currencyCode="' . $data['currency'] . '">' . $data['amount'] . '</TransactionCurrencyNetAmount>									
    									
                <!--Optional:-->									
               <CostCentreID></CostCentreID>									
               <!--Optional:-->									
               <ProfitCentreID>' . $data['pc_code'] . '</ProfitCentreID>                         									
            </item>									
         </Cust_ReceivalesPayable>									
      </glob:Cust_ReceivalesPayableCreateRequest_sync>									
   </soapenv:Body>									
</soapenv:Envelope>';


        $this->logger->info('reservefound-sap-post-data:' . $data['rfano'] . '=========' . $post_xml);
        $xml = SapService::getInstance()->httpRequestXml($method = '/sap/yyd8h6xfdy_managecust_receival', $post_xml);
        $this->logger->info('reservefound-sap-return-data:' . $data['rfano'] . '=========' . $xml);

        preg_match_all("/\<Cust_ReceivalesPayable\>(.*?)\<\/Cust_ReceivalesPayable\>/s", $xml, $re_data);
        $return_data = [];
        if (isset($re_data[0][0]) && !empty($re_data[0][0])) {
            $return_data = SapService::getInstance()->xmlToArray($re_data[0][0]);
        }

        preg_match_all("/\<Log\>(.*?)\<\/Log\>/s", $xml, $log);
        $return_data['log'] = '';

        if (isset($log[0][0]) && !empty($log[0][0])) {
            $return_data['log'] = SapService::getInstance()->xmlToArray($log[0][0]);
        }
        $logModel = new RequestSapLog();

        $logModel->save([
            'uuid'          => $return_data['SAP_UUID'] ?? '',
            'order_code'    => $data['rfano'],
            'type'          => 4,
            'request_data'  => $post_xml,
            'response_data' => $xml ?? '',
            'create_at'     => date('Y-m-d H:i:s'),
        ]);

        return $return_data;
    }

    /**
     * 备用金归还发送sap
     * @param array $data 参数组
     * @param string $country_code 国家码
     * @return array|mixed
     */
    public function reserveBackSap($data, $country_code)
    {
        try {
            if (empty($data)) {
                return [];
            }

            $post_xml='<soapenv:Envelope xmlns:soapenv="http://schemas.xmlsoap.org/soap/envelope/" xmlns:glob="http://sap.com/xi/SAPGlobal20/Global">
               <soapenv:Header/>
               <soapenv:Body>
                  <glob:Cust_ReceivalesPayableCreateRequest_sync>
                     <BasicMessageHeader>
                     </BasicMessageHeader>
                     
                 <Cust_ReceivalesPayable>
                         <!--供应商:-->                                                                        
                        <BusinessPartnerInternalID>S00215</BusinessPartnerInternalID>                                                                        
                        <!--公司:-->                                                                        
                        <CompanyID>' . $data['cost_company_id'] . '</CompanyID>                                                                        
                        <!--供应商贷记4:-->                                                                        
                        <ReceivablesPayablesEntryTypeCode>3</ReceivablesPayablesEntryTypeCode>                                                                        
                        <!--外部参考号:-->                                                                        
                        <PartnerBaseBusinessTransactionDocumentReference>' . $data['rfano'] . '</PartnerBaseBusinessTransactionDocumentReference>                                                                        
                        <!--单据描述:-->                                                                        
                        <Description>' . $data['create_id'] . '</Description>                                                                        
                        <!--货币:-->                                                                        
                        <TransactionCurrencyCode>' . $data['currency'] . '</TransactionCurrencyCode>                                                                        
                        <!--过账日期:-->                                                                        
                        <PostingDate>' . $data['back_transaction_date'] . '</PostingDate>                                                                        
                        <!--单据日期:-->                                                                        
                        <BusinessTransactionDocumentDate>' . $data['create_date'] . '</BusinessTransactionDocumentDate>                                                                        
                        <!--Optional:-->                                                                        
                        <CountryCode>' . $country_code . '</CountryCode>                                                                        
                        <!--Zero or more repetitions:-->        
                                                                                        
                        <item>                                                                        
                           <!--默认值:-->                                                                        
                           <PropertyMovementDirectionCode></PropertyMovementDirectionCode>                                                                        
                           <!--总账科目:-->                                                                        
                           <GeneralLedgerAccountAliasCode>********</GeneralLedgerAccountAliasCode>                                                                        
                           <TransactionCurrencyNetAmount currencyCode="' . $data['currency'] . '">' . $data['back_amount'] . '</TransactionCurrencyNetAmount>                                                                        
                                                                                        
                            <!--成本中心:-->                                                                        
                           <CostCentreID></CostCentreID>                                                                        
                           <!--利润中心-->                                                                        
                           <ProfitCentreID></ProfitCentreID> 
                           <!--税务代码:-->
                           <TaxCode></TaxCode>
                           <!--行项目描述:-->
                           <ItemDescription>' . $data['create_id'] . '#Petty cash ' . $data['rfano'] . '</ItemDescription>                                                                                                
                        </item>
                                                                            
                     </Cust_ReceivalesPayable>        
            
                  </glob:Cust_ReceivalesPayableCreateRequest_sync>
               </soapenv:Body>
            </soapenv:Envelope>';

            $xml = SapService::getInstance()->httpRequestXml('/sap/yyd8h6xfdy_managecust_receival', $post_xml);

            preg_match_all("/\<Cust_ReceivalesPayable\>(.*?)\<\/Cust_ReceivalesPayable\>/s", $xml, $re_data);
            $return_data = [];
            if (isset($re_data[0][0]) && !empty($re_data[0][0])) {
                $return_data = SapService::getInstance()->xmlToArray($re_data[0][0]);
            }

            preg_match_all("/\<Log\>(.*?)\<\/Log\>/s", $xml, $log);
            $return_data['log'] = '';

            if (isset($log[0][0]) && !empty($log[0][0])) {
                $return_data['log'] = SapService::getInstance()->xmlToArray($log[0][0]);
            }
            $logModel = new RequestSapLog();
            $logModel->save(['uuid' => $return_data['SAP_UUID'] ?? '', 'order_code'=>$data['back_no'], 'type' => 12, 'request_data' => $post_xml, 'response_data' => $xml ?? '', 'create_at' => date('Y-m-d H:i:s')]);

            return $return_data;

        } catch (\Exception $e) {
            $this->logger->warning('reserve_back_sap_service_exception: ' . $e->getMessage());
            return [];
        }
    }
}
