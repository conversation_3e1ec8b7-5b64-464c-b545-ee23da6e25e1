<?php

namespace App\Modules\OrdinaryPayment\Services;

use App\Library\Enums\DepositEnums;
use App\Library\Enums\MaterialClassifyEnums;
use App\Library\Enums\OrdinaryPaymentEnums;
use App\Library\Enums\StaffInfoEnums;
use App\Library\Enums\BudgetWithholdingEnums;
use App\Models\oa\SettingEnvModel;
use App\Models\oa\VendorModel;
use App\Modules\Budget\Services\BaseService as BudgetBaseService;
use App\Modules\Budget\Services\BudgetWithholdingService;
use App\Modules\Common\Models\EnvModel;
use App\Modules\Contract\Models\ContractArchive;
use App\Modules\Hc\Services\SysService;
use App\Modules\Material\Services\ClassifyService;
use App\Modules\OrdinaryPayment\Models\OrdinaryPayment;
use App\Modules\OrdinaryPayment\Models\OrdinaryPaymentDetail;
use App\Modules\OrdinaryPayment\Models\OrdinaryPaymentExtend;
use App\Modules\Organization\Models\SysStoreModel;
use App\Modules\Reimbursement\Services\DetailService;
use App\Modules\Setting\Services\CostCategoryReimbursementDetailService;
use App\Modules\User\Models\HrStaffInfoModel;
use App\Modules\User\Models\HrStaffItemsModel;
use App\Repository\DepartmentRepository;
use App\Repository\HrStaffRepository;
use App\Repository\oa\PurchaseOrderRepository;
use App\Util\RedisKey;
use App\Library\Enums;
use App\Library\Enums\GlobalEnums;
use App\Library\Enums\ContractEnums;
use App\Library\Enums\BudgetObjectEnums;
use App\Library\ErrCode;
use App\Library\Exception\BusinessException;
use App\Library\Validation\ValidationException;
use App\Modules\User\Models\AttachModel;
use App\Modules\Budget\Models\BudgetObject;
use App\Modules\Budget\Services\BudgetService;
use App\Modules\Common\Services\EnumsService;
use App\Modules\OrdinaryPayment\Models\OrdinaryPaymentPersonal;
use App\Modules\Purchase\Services\BaseService as PurchaseService;
use App\Modules\Purchase\Services\OrderService;
use App\Library\Enums\KingDeeEnums;

class OrdinaryPaymentAddService extends BaseService
{

    private static $instance;

    private function __construct()
    {
    }


    public static function getInstance()
    {
        if (!isset(self::$instance) || !(self::$instance instanceof self)) {
            self::$instance = new self;
        }
        return self::$instance;
    }

    /**
     * 校验应付日期
     * @param $params
     * @return array
     */
    public function verifyShouldPayDate($params): array
    {
        if (empty($params['loan_time']) || empty($params['ticket_date']) || empty($params['should_pay_date'])) {
            return [];
        }
        $loanTimeList    = EnumsService::getInstance()->getSettingEnvValueMap('purchase_order_loan_time');
        $loanTimeListMap = array_column($loanTimeList, 'days', 'id');
        if (empty($loanTimeListMap[$params['loan_time']])) {
            return [];
        }
        $verifyDate = strtotime($params['ticket_date'] . ' +' . $loanTimeListMap[$params['loan_time']] . ' days');
        if (strtotime($params['should_pay_date']) < $verifyDate) {
            return ['verify_date' => date('Y-m-d', $verifyDate)];
        }
        return [];
    }
    /**
     * 普通付款 - 申请创建
     * @param array $data 付款申请数据
     * @param array $user
     * @return array
     */
    public function addApply(array $data, array $user)
    {
        $code    = ErrCode::$SUCCESS;
        $message = $real_message = '';

        $db = $this->getDI()->get('db_oa');
        $db->begin();

        try {
            // 该申请编号是否存在
            $exists = OrdinaryPayment::getFirst([
                'conditions' => 'apply_no = :apply_no:',
                'columns'    => 'id',
                'bind'       => ['apply_no' => $data['apply_no']],
            ]);

            if (!empty($exists)) {
                throw new ValidationException(static::$t->_('data_no_exist', ['no' => $data['apply_no']]), ErrCode::$VALIDATE_ERROR);
            }

            // 收款人类型为供应商时, 校验供应商下是否已提交过该发票号(同一供应商下的发票号不能重复)
            if ($data['payee_type'] == self::PAYEE_TYPE_1) {
                // 免校验的发票号配置
                $free_verification_invoice_numbers = EnumsService::getInstance()->getSettingEnvValueIds('free_verification_invoice_numbers');
                if (!in_array($data['ticket_number'], $free_verification_invoice_numbers)) {
                    $this->validateSupplierTicketNumber($data['supplier_id'], $data['ticket_number'], $data['factoring_apply_no'] ?? '');
                }
            }

            $amount_total_no_tax   = 0; //不含税金额总计
            $amount_total_vat      = 0; //vat金额总计(马来叫SST金额总计)
            $amount_total_wht      = 0; //wht金额总计
            $amount_total_have_tax = 0; //含税金额总计
            $amount_discount       = round($data['amount_discount'], 2); //折扣金额
            $main_data['sap_supplier_no'] = $data['sap_supplier_no'] ?? '';

            // 构建金额详情表数据&计算各个金额总计项
            $sap_cost_company_ids = EnumsService::getInstance()->getSettingEnvValueIds('purchase_sap_company_ids');
            $amount_detail_item = $this->buildAmountDetailData($data['amount_detail'], $amount_total_no_tax, $amount_total_vat, $amount_total_wht, $amount_total_have_tax, $data['cost_company_id'], $sap_cost_company_ids, $main_data);
            //验证所有采购订单
            $this->checkPurchaseOrder($amount_detail_item, $data['cost_company_id']);
            //15998需求，针对马来/17688以下验证开放所有国家
            //需要判断不含税金额总计、SST金额总计、WHT金额总计、含税金额总计都不可为负数
            if ($amount_total_no_tax < 0 || $amount_total_vat < 0 || $amount_total_wht < 0 || $amount_total_have_tax < 0 ) {
                throw new ValidationException(self::$t->_('amount_total_actually_value_negative_number'), ErrCode::$VALIDATE_ERROR);
            }
            $deposit_evn = EnumsService::getInstance()->getSettingEnvValueMap('deposit_business_budget_object_id_config');
            $deposit_ordinary_payment_set = $deposit_evn[DepositEnums::DEPOSIT_ORDINARY_PAYMENT] ?? [];
            //预算科目下的金额组
            $budget_amounts = [];
            foreach ($amount_detail_item as $item) {
                //核算科目id
                $budget_id = $item['budget_id'];
                $t_amount = bcmul($item['amount_have_tax'], 1000);

                //参与了押金管理归还设置的预算科目的含税金额不可输入负数
                if ($t_amount < 0 && $deposit_ordinary_payment_set && in_array($budget_id, $deposit_ordinary_payment_set)) {
                    throw new ValidationException(self::$t->_('amount_detail_budget_deposit_error', ['budget_name' => $item['budget_name']]), ErrCode::$VALIDATE_ERROR);
                }
                //不参与预算调整的核算科目无需参与含税金额累计
                if ($item['is_budget'] == BudgetBaseService::IS_NO_BUDGET) {
                    continue;
                }
                //按照预算科目累计含税金额
                if (isset($budget_amounts[$budget_id])) {
                    $budget_amounts[$budget_id]['amount'] += $t_amount;
                } else {
                    $budget_amounts[$budget_id]['budget_name'] = $item['budget_name'];
                    $budget_amounts[$budget_id]['amount']= $t_amount;
                }
            }
            //若付款分类（即预算科目）启用了预算管控，则金额详情栏里，该付款分类所有行的含税金额之和需要大于等于0
            if (!empty($budget_amounts)) {
                foreach ($budget_amounts as $budget) {
                    if ($budget['amount'] < 0) {
                        throw new ValidationException(self::$t->_('amount_detail_budget_have_tax_error', ['budget_name' => $budget['budget_name']]), ErrCode::$VALIDATE_ERROR);
                    }
                }
            }

            //实付金额 不能小于0
            $amount_total_actually = $amount_total_have_tax - $amount_total_wht - $amount_discount;
            if ($amount_total_actually <= 0) {
                throw new ValidationException(self::$t->_('amount_total_actually_value_error'), ErrCode::$VALIDATE_ERROR);
            }
            //应付日期校验
            $shouldPayDateError = $this->verifyShouldPayDate($data);
            if ($shouldPayDateError) {
                throw new ValidationException(self::$t->_('should_pay_date_error',['verify_date' => $shouldPayDateError['verify_date']]), ErrCode::$VALIDATE_ERROR);
            }
            // 普通付款申请 金额总计数据统计
            $data['amount_total_no_tax']   = round($amount_total_no_tax, 2);   // 不含税金额总计
            $data['amount_total_vat']      = round($amount_total_vat, 2);      // vat 金额总计
            $data['amount_total_wht']      = round($amount_total_wht, 2);      // wht 金额总计
            $data['amount_discount']       = round($amount_discount, 2);       // 折扣
            $data['amount_total_have_tax'] = round($amount_total_have_tax, 2); // 含税金额总计
            $data['amount_total_actually'] = round($amount_total_actually, 2); // 实付金额总计

            // [step1]:基本信息（主表）入库
            $apply_data           = $this->buildMainData($data, $user);

            $ordinaryPaymentModle = new OrdinaryPayment();
            $bool                 = $ordinaryPaymentModle->create($apply_data);
            if ($bool === false) {
                throw new BusinessException('普通付款申请-基本信息数据创建失败 = ' . json_encode(['apply_data' => $apply_data,'message' => get_data_object_error_msg($ordinaryPaymentModle)], JSON_UNESCAPED_UNICODE), ErrCode::$ORDINARY_PAYMENT_CREATE_MAIN_ERROR);
            }

            $attach = new AttachModel();
            $tmpList = [];
            if (!empty($data['attachments'])) {
                foreach ($data['attachments'] as $attachment) {
                    $tmp = [];
                    $tmp['oss_bucket_type'] = Enums::OSS_BUCKET_TYPE_ORDINARYPAYMENT_MAIN;
                    $tmp['oss_bucket_key'] = $ordinaryPaymentModle->id;
                    $tmp['sub_type']  = 0;
                    $tmp['bucket_name'] = $attachment['bucket_name'];
                    $tmp['object_key'] = $attachment['object_key'];
                    $tmp['file_name'] = $attachment['file_name'];
                    $tmpList[] = $tmp;
                }

                $attach_bool = $attach->batchInsert($tmpList);
                if ($attach_bool === false) {
                    throw new BusinessException('普通付款申请-主附件创建失败: ' . json_encode($tmpList, JSON_UNESCAPED_UNICODE) . ', 原因可能是: ' . get_data_object_error_msg($attach), ErrCode::$ORDINARY_PAYMENT_CREATE_DETAIL_FILE_ERROR);
                }
            }
            // 获取房租的科目id
            $rent_budget_id = (new SettingEnvModel())->getValByCode('ordinary_payment_rent_budget_id');
            // 查询被作废合同作废时间
            $contract_nos = array_values(array_unique(array_column($amount_detail_item, 'contract_no')));
            $contract_no_replace = [];
            if (!empty($contract_nos)) {
                $contract_no_arr = ContractArchive::find([
                    'columns' => 'cno, invalid_replace_cno, invalid_replace_begin, invalid_replace_end',
                    'conditions' => 'invalid_replace_cno in ({invalid_replace_cno:array}) and status in ({status:array}) and invalid_reason_type = :invalid_reason_type:',
                    'bind' => [
                        'invalid_replace_cno' => $contract_nos,
                        'status' => [ContractEnums::CONTRACT_ARCHIVE_STATUS_INVALID, ContractEnums::CONTRACT_ARCHIVE_STATUS_INVALID_ING],
                        'invalid_reason_type' => ContractEnums::INVALID_REASON_TYPE_RE_SIGN,
                    ],
                ])->toArray();
                foreach ($contract_no_arr as $contract_replace) {
                    $contract_no_replace[$contract_replace['invalid_replace_cno']][] = $contract_replace;
                }
            }
            // [step2]:入库金额详情数据
            foreach ($amount_detail_item as $item_detail) {
                //验证合同
                if (!empty($item_detail['contract_no'])) {
                    // 合同是否已作废
                    $exists = ContractArchive::findFirst([
                        'conditions' => 'cno = :cno:',
                        'bind'       => ['cno' => $item_detail['contract_no']],
                    ]);
                    //不允许关联的归档状态 待归档,待上传归档合同,待作废,待终止,已作废,已终止
                    $not_allowed_status = [ContractEnums::CONTRACT_ARCHIVE_STATUS_PENDING, ContractEnums::CONTRACT_ARCHIVE_STATUS_INVALID, ContractEnums::CONTRACT_ARCHIVE_STATUS_NO_UPLOAD, ContractEnums::CONTRACT_ARCHIVE_STATUS_TERMINAL, ContractEnums::CONTRACT_ARCHIVE_STATUS_INVALID_ING, ContractEnums::CONTRACT_ARCHIVE_STATUS_TERMINAL_ING];
                    $exists = $exists ? $exists->toArray() : [];

                    if (empty($exists) || in_array($exists['status'], $not_allowed_status)) {
                        throw new ValidationException(static::$t['archive_status_invalid'], ErrCode::$VALIDATE_ERROR);
                    }
                    // 如果付款分类=房租时, 验证日期不能包含被作废转移的时间

                    if (!empty($rent_budget_id) && $item_detail['budget_id'] == $rent_budget_id && !empty($contract_no_replace[$item_detail['contract_no']])) {
                        foreach ($contract_no_replace[$item_detail['contract_no']] as $contract_v) {
                            $transfer_params = ['contract_no' => $contract_v['invalid_replace_cno'], 'invalid_data' => $contract_v['invalid_replace_begin'] . ' - ' . $contract_v['invalid_replace_end']];
                            //开始时间或结束时间在禁用时间内
                            if ($item_detail['cost_start_date'] >= $contract_v['invalid_replace_begin'] && $item_detail['cost_start_date'] <= $contract_v['invalid_replace_end']) {
                                throw new ValidationException(self::$t->_('cost_date_invalid_error', $transfer_params), ErrCode::$VALIDATE_ERROR);
                            }
                            if ($item_detail['cost_end_date'] >= $contract_v['invalid_replace_begin'] && $item_detail['cost_end_date'] <= $contract_v['invalid_replace_end']) {
                                throw new ValidationException(self::$t->_('cost_date_invalid_error', $transfer_params), ErrCode::$VALIDATE_ERROR);
                            }
                            //开始时间大于禁用时间开始,结束时间大于禁用时间结束
                            if ($item_detail['cost_start_date'] <= $contract_v['invalid_replace_begin'] && $item_detail['cost_end_date'] >= $contract_v['invalid_replace_end']) {
                                throw new ValidationException(self::$t->_('cost_date_invalid_error', $transfer_params), ErrCode::$VALIDATE_ERROR);
                            }
                        }
                    }
                }
                //入库
                unset($item_detail['is_budget'], $item_detail['budget_name']);//过滤掉不需要入库字段信息
                $item_detail['ordinary_payment_id'] = $ordinaryPaymentModle->id;
                $detail_model                       = new OrdinaryPaymentDetail();
                $bool                               = $detail_model->create($item_detail);
                if ($bool === false) {
                    throw new BusinessException('普通付款申请-金额详情数据创建失败 = ' . json_encode($item_detail, JSON_UNESCAPED_UNICODE) . ', 原因可能是: ' . get_data_object_error_msg($detail_model), ErrCode::$ORDINARY_PAYMENT_CREATE_DETAIL_ERROR);
                }

                // [step2.1] 入库 金额详情-附件列表
                if (!empty($item_detail['attachment_list'])) {
                    $tmpList = [];
                    foreach ($item_detail['attachment_list'] as $k => $file) {
                        $tmp = [];
                        $tmp['oss_bucket_type'] = Enums::OSS_BUCKET_TYPE_ORDINARYPAYMENT;
                        $tmp['oss_bucket_key'] = $detail_model->id;
                        $tmp['sub_type'] = 0;
                        $tmp['bucket_name'] = $file['bucket_name'];
                        $tmp['object_key'] = $file['object_key'];
                        $tmp['file_name'] = $file['file_name'];
                        $tmpList[] = $tmp;
                    }
                    $attach_bool = $attach->batchInsert($tmpList);
                    if ($attach_bool === false) {
                        throw new BusinessException('普通付款申请-附件创建失败: ' . json_encode($data['attachment_list'], JSON_UNESCAPED_UNICODE) . ', 原因可能是: ' . get_data_object_error_msg($attach), ErrCode::$ORDINARY_PAYMENT_CREATE_DETAIL_FILE_ERROR);
                    }
                }
            }

            if (self::PAYEE_TYPE_1 == $data['payee_type']) {
                // [step3]:入库供应商信息
                $data_extend = [
                    'ordinary_payment_id'      => $ordinaryPaymentModle->id,
                    'supplier_id'              => $data['supplier_id'],
                    'supplier_name'            => $data['supplier_name'],
                    'supplier_address'         => $data['supplier_address'],
                    'supplier_contacts'        => $data['supplier_contacts'],
                    'supplier_tel'             => $data['supplier_tel'],
                    'supplier_email'           => $data['supplier_email'],
                    'supplier_bk_name'         => $data['supplier_bk_name'],
                    'supplier_bk_account'      => $data['supplier_bk_account'],
                    'supplier_bk_account_name' => $data['supplier_bk_account_name'],
                    'supplier_tax_number'      => $data['supplier_tax_number'] ?? '',//入库供应商税务号
                    'sap_supplier_no'          => $data['sap_supplier_no'] ?? '',    //SAP供应商号码
                    'bank_address'             => $data['bank_address'] ?? '',       //收款银行地址
                ];

                if (get_country_code() == GlobalEnums::MY_COUNTRY_CODE) {

                    //校验供应商的归属地
                    if (isset($data['is_clearance']) && in_array($data['is_clearance'], [
                            Enums\OrdinaryPaymentEnums::QUERY_CLEARANCE_QUEST_YES, Enums\OrdinaryPaymentEnums::QUERY_CLEARANCE_QUEST_NO])) {
                        $this->validateSupplierOwnership($data['supplier_id'], Enums::VENDOR_OWNERSHIP_MALAYSIA);

                        $data_extend_clearance = [
                            'is_clearance'          => $data['is_clearance'],
                            'clearance_no'          => $data['clearance_no'] ?? '',
                        ];
                        if ($data['is_clearance'] == OrdinaryPaymentEnums::QUERY_CLEARANCE_QUEST_YES && !empty($data_extend_clearance)) {
                            $data_extend_clearance['actual_clearance_date'] = $data['actual_clearance_date'];
                        }
                        $data_extend = array_merge($data_extend, $data_extend_clearance);
                    }
                }

                $extend_model = new OrdinaryPaymentExtend();
                $extend_res   = $extend_model->i_create($data_extend);
                if ($extend_res === false) {
                    throw new BusinessException('普通付款申请-供应商信息创建失败: ' . json_encode($data_extend, JSON_UNESCAPED_UNICODE) . ', 原因可能是: ' . get_data_object_error_msg($extend_model), ErrCode::$ORDINARY_PAYMENT_CREATE_EXTEND_ERROR);
                }
            } else {
                // 个人
                $staff_info_ids = array_unique(array_column($data['personal_detail']??'','staff_info_id'));
                if (isset($data['personal_detail']) && count($data['personal_detail']) > count($staff_info_ids)) {
                    throw new ValidationException(self::$t->_('pay_person_number_check'), ErrCode::$VALIDATE_ERROR);
                }

                $data_personal = [];
                $payee_aomunt_total = 0;
                foreach ($data['personal_detail'] as $item) {
                    $data_personal[] = [
                        'ordinary_payment_id' => $ordinaryPaymentModle->id,
                        'staff_info_id'       => $item['staff_info_id'],
                        'bank_name'           => $item['bank_name'],
                        'bank_no_name'        => $item['bank_no_name'],
                        'bank_no'             => $item['bank_no'],
                        'name'                => $item['name'],
                        'mobile'              => $item['mobile'],
                        'amount'              => $item['amount'],
                    ];

                    $payee_aomunt_total += $item['amount'];
                }

                $payee_aomunt_total = round($payee_aomunt_total, 2);

                // 含税金额总计 与 个人收款金额总计, 必须 一致 v18729
                if (bccomp($amount_total_have_tax, $payee_aomunt_total, 2) != 0) {
                    throw new ValidationException(static::$t->_('ordinary_payment_create_error_03'), ErrCode::$VALIDATE_ERROR);
                }

                $personal_model = new  OrdinaryPaymentPersonal();
                $personal_res   = $personal_model->batchInsert($data_personal);
                if ($personal_res === false) {
                    throw new BusinessException('普通付款申请-个人收款信息创建失败: ' . json_encode($data_personal, JSON_UNESCAPED_UNICODE) . ', 原因可能是: ' . get_data_object_error_msg($personal_model), ErrCode::$ORDINARY_PAYMENT_CREATE_EXTEND_ERROR);
                }
            }

            // 占用普通付款预算
            $amountInfo= [];

            // V21975预算校验拦截调整、预算占用逻辑调整 - 区分是否关联预提单
            if (!empty($apply_data['budget_withholding_id'])  && in_array(get_country_code(), [GlobalEnums::TH_COUNTRY_CODE, GlobalEnums::MY_COUNTRY_CODE, GlobalEnums::PH_COUNTRY_CODE])) {
                //获取预提信息
                $budget_withholding_info = BudgetWithholdingService::getInstance()->getBudgetWithholdingInfoById($apply_data['budget_withholding_id']);

                //预提单的剩余金额
                $remain_amount         = $budget_withholding_info->provision_amount - $budget_withholding_info->use_amount;

                //本次提交的含税金额总计(转换为本位币后)
                $amount_total_have_tax = EnumsService::getInstance()->amountExchangeRateCalculation($apply_data['amount_total_have_tax'], $apply_data['exchange_rate'], 2);
                $amount_total_have_tax = bcmul($amount_total_have_tax, 1000);

                if ($amount_total_have_tax > $remain_amount) {
                    //预算校验拦截 - 如果本次提交的含税金额总计(转换为本位币后)大于预提单的剩余金额，则校验当前费用部门当前预算科目提交当前季度的剩余预算是否大于本次提交的含税金额总计（转换为本位币）-剩余金额，如果大于等于则满足预算，不需要拦截，如果小于则提示“预算科目预算不足，请先调整预算！”
                    //预算占用逻辑 - 如果本次提交的含税金额总计(转换为本位币后)大于预提单的剩余金额，则本次普通付款占用预算，占用预算金额为本次提交的含税金额总计（转换为本位币）-剩余金额，预算科目为付款分类选择的预算科目，预算周期为提交单据当前月
                    //关联预提单-付款分类只能是一个，所以取任意一行即可
                    $amountInfo[] = ['budget_id' => $amount_detail_item[0]['budget_id'], 'amount' => $amount_total_have_tax - $remain_amount];
                }

                //V21975关联预提单则更新预提单的使用金额和剩余金额、记录普通付款是否需要红冲凭证等
                $this->saveBindBudgetWithholdingInfo($ordinaryPaymentModle, $budget_withholding_info, $sap_cost_company_ids);
            } else {
                foreach ($amount_detail_item as $detail) {
                    $t_amount = bcmul($detail['amount_have_tax'], 1000);
                    $t_amount = $apply_data['exchange_rate'] ? bcmul($t_amount, $apply_data['exchange_rate'], 0) : $t_amount;
                    $amountInfo[] = ['budget_id' => $detail['budget_id'], 'amount' => $t_amount];//含税金额
                }
            }

            // V21975 关联预提单 有不需要占用预算情况这里增加判断兼容
            if ($amountInfo) {
                //cost_store_type 含义：1-网点，2-总部，由于科目数据表中 网点和总部的value值正好和接口定义的相反，所以在此需要转换下
                $data['cost_store_type'] = $data['cost_store_type'] == 1 ? 2 : 1;
                $data['cost_department'] = $data['cost_department_id'];

                $budgetServer = new BudgetService();
                $budgetServer->checkBudgets($data['apply_no'], $amountInfo, BudgetService::ORDER_TYPE_3, $data, (integer)($data['is_submit']??0),$user['id']);
            }

            // [step4]: 注入审批流
            $flow_bool = (new OrdinaryPaymentFlowService())->createRequest($ordinaryPaymentModle, $user, $amount_detail_item);
            if ($flow_bool === false) {
                throw new BusinessException('普通付款申请-生成审批流失败', ErrCode::$ORDINARY_PAYMENT_CREATE_WORK_FLOW_ERROR);
            }

            // 提交事务
            $db->commit();

        } catch (ValidationException $e) {
            // 校验错误，可对外抛出
            if (in_array($e->getCode(), [ErrCode::$BUDGET_OVERAMOUNT_QUARTERLY, ErrCode::$BUDGET_OVERAMOUNT_MONTH])) {
                $code = ErrCode::$SUCCESS;
                $result = [
                    'message' => $e->getMessage(),
                    'can_apply' => $e->getCode() == ErrCode::$BUDGET_OVERAMOUNT_QUARTERLY ? 0 : 1,
                ];

            } else {
                $code = $e->getCode();
            }

            $message = $e->getMessage();

        } catch (BusinessException $e) {
            // 业务错误（数据错误等），不可对外抛出
            $code         = $e->getCode();
            $message      = static::$t->_('retry_later');
            $real_message = $e->getMessage();

        } catch (\Exception $e) {
            // 系统错误，不可对外抛出
            $code         = ErrCode::$SYSTEM_ERROR;
            $message      = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        }

        if (!empty($message)) {
            $db->rollback();
        }

        if (!empty($real_message)) {
            $this->logger->warning('ordinaryPayment-create-failed: ' . $real_message);
        }

        return [
            'code'    => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data' => $result ?? [],
        ];
    }


    /**
     * 处理类
     * @Token
     * @Date: 2022-06-10 10:53
     * @param $data
     * @param $apply_store_id
     * @return mixed :
     **@throws ValidationException
     * @throws ValidationException
     * @author: peak pan
     */
    public function addApplyCheck($data, $apply_store_id = '')
    {
        $country_code      = get_country_code();
        $subject_config    = json_decode((new EnvModel())->getEnvByCode('ordinary_payment_create_chek_config'), true);
        $amount_detail_arr = array_values(array_unique(array_column($data['amount_detail'], 'budget_id')));
        //排除随意混合科目id后再校验
        $random_mix_budget_ids = EnumsService::getInstance()->getSettingEnvValueIds('ordinary_payment_random_mix_budget_ids');
        if (!empty($random_mix_budget_ids)) {
            $amount_detail_arr = array_values(array_diff($amount_detail_arr, $random_mix_budget_ids));
        }
        $own_fleet_freight_arr = array_merge($subject_config['own_fleet'], $subject_config['freight']);
        if (in_array($country_code, [GlobalEnums::LA_COUNTRY_CODE, GlobalEnums::TH_COUNTRY_CODE]) && count(array_intersect($subject_config['water_and_electricity'], $amount_detail_arr)) == 1 && count($amount_detail_arr) > 1) {

            $subject_name = self::$t->_('expense_type.store_auto_type.1');
            throw new ValidationException(self::$t->_('ordinary_payment_create_water_and_electricity', ['subject_name' => $subject_name]), ErrCode::$VALIDATE_ERROR);
        }

        if ($country_code == GlobalEnums::TH_COUNTRY_CODE) {
            $amount_detail_child = array_values(array_unique(array_column($data['amount_detail'], 'product_id')));
            if (!empty($amount_detail_child) && count(array_intersect($subject_config['ad'], $amount_detail_child)) == 1 && count($amount_detail_child) > 1) {
                $subject_name = self::$t->_('contract_enums_cost_type_signbord_tax');
                throw new ValidationException(self::$t->_('ordinary_payment_create_water_and_electricity', ['subject_name' => $subject_name]), ErrCode::$VALIDATE_ERROR);
            }
        }

        //员工福利费 员工团建费
        if (!empty($subject_config['welfare_and_morale'])) {
            if ($this->combinationCheck($subject_config['welfare_and_morale'], $amount_detail_arr)) {
                $subject_name  = self::$t->_('ordinary_payment_category_18');
                $subject_title = self::$t->_('reimbursement_expense_19');
                throw new ValidationException(self::$t->_('ordinary_payment_create_own_fleet', ['subject_name' => $subject_name, 'subject_title' => $subject_title]), ErrCode::$VALIDATE_ERROR);
            }
        }

        //劳务成本-外协  劳务成本-外包
        if (!empty($subject_config['labour_services'])) {
            if ($this->combinationCheck($subject_config['labour_services'], $amount_detail_arr)) {
                $subject_name  = self::$t->_('bank_flow.ph.expense_type.66');
                $subject_title = self::$t->_('bank_flow.ph.expense_type.67');
                throw new ValidationException(self::$t->_('ordinary_payment_create_own_fleet', ['subject_name' => $subject_name, 'subject_title' => $subject_title]), ErrCode::$VALIDATE_ERROR);
            } else if ($apply_store_id != '' && $apply_store_id != Enums::PAYMENT_HEADER_STORE_ID && $country_code != GlobalEnums::MY_COUNTRY_CODE) {
                //仅泰国校验, 老挝没传$apply_store_id不校验
                //network 外包和外协不能一起提交
                $store_kv = OrdinaryPaymentEnums::FLOW_ID_ARRAY_KEY;
                if ($country_code == GlobalEnums::PH_COUNTRY_CODE) {
                    $store_kv = OrdinaryPaymentEnums::FLOW_ID_PH_ARRAY_KEY;
                }
                $network_category = $store_kv['network'];
                $store_data       = SysStoreModel::findFirst([
                    'conditions' => 'id = :id:',
                    'bind' => ['id' => $apply_store_id],
                ]);
                if (isset($store_data->category) && in_array($store_data->category, $network_category) && in_array($subject_config['labour_services'][0], $amount_detail_arr) && in_array($subject_config['labour_services'][1], $amount_detail_arr)) {
                    throw new ValidationException(self::$t->_('reimbursement_validation_network_labour_services'), ErrCode::$VALIDATE_ERROR);
                }
            }
        }

        //第三方运费-支干线 第三方运费-加盟商 只有马来使用此校验16844
        if (!empty($subject_config['freight']) && $country_code == GlobalEnums::MY_COUNTRY_CODE) {
            if ($this->combinationCheck($subject_config['freight'], $amount_detail_arr)) {
                $subject_name  = self::$t->_('ordinary_payment_category_63');
                $subject_title = self::$t->_('reimbursement_expense_114');
                throw new ValidationException(self::$t->_('ordinary_payment_create_own_fleet', ['subject_name' => $subject_name, 'subject_title' => $subject_title]), ErrCode::$VALIDATE_ERROR);
            }
        }

        if (count(array_intersect($amount_detail_arr, $own_fleet_freight_arr)) > OrdinaryPaymentEnums::ID_DEFAULT_ZERO && count(array_diff($amount_detail_arr, $own_fleet_freight_arr)) > OrdinaryPaymentEnums::ID_DEFAULT_ZERO && $country_code != GlobalEnums::MY_COUNTRY_CODE) {

            $subject_name  = self::$t->_('ordinary_payment_category_all');//自有车队
            $subject_title = self::$t->_('bank_flow_th_expense_cost');//第三方车队

            throw new ValidationException(self::$t->_('ordinary_payment_create_own_fleet', ['subject_name' => $subject_name, 'subject_title' => $subject_title]), ErrCode::$VALIDATE_ERROR);
        }

        if ($country_code == GlobalEnums::TH_COUNTRY_CODE) {
            if (count(array_diff($amount_detail_arr, $subject_config['welfare_and_morale'])) == OrdinaryPaymentEnums::ID_DEFAULT_ZERO) {
                $hrbp_id = SysService::getInstance()->getBpHeadByStaff($data['apply_id']);
                if (empty($hrbp_id)) {
                    throw new ValidationException(static::$t->_('ordinary_payment_not_hrbp'), ErrCode::$VALIDATE_ERROR);
                }
            }
        }

        return $subject_config;
    }

    /**
     * v18028、v18960 校验付款分类是否允许混合提交
     *
     * @param array $budget_ids
     * @return mixed
     * @throws ValidationException
     */
    public function addApplyCheckNew(array $budget_ids)
    {
        $amount_detail_arr = $budget_ids;
        //排除随意混合科目id后再校验
        $random_mix_budget_ids = EnumsService::getInstance()->getSettingEnvValueIds('ordinary_payment_random_mix_budget_ids');
        if (!empty($random_mix_budget_ids)) {
            $amount_detail_arr = array_diff($budget_ids, $random_mix_budget_ids);
        }
        $mixed_config = EnumsService::getInstance()->getSettingEnvValueMap('ordinary_payment_mixed_check_config');
        foreach ($amount_detail_arr as $budget_id) {
            foreach ($mixed_config as $one_config) {
                if (in_array($budget_id, $one_config)) {
                    //科目id中是否存在不在分组中的id
                    $diff_budget_arr = array_values(array_diff($amount_detail_arr, $one_config));
                    if (!empty($diff_budget_arr)) {
                        $budget_name_data = (new BudgetService())->getBudgetByIds([$budget_id, $diff_budget_arr[0]]);
                        throw new ValidationException(self::$t->_('ordinary_payment_mixed_check_error', ['budget_1' => $budget_name_data[$budget_id] ?? '', 'budget_2' => $budget_name_data[$diff_budget_arr[0]] ?? '']), ErrCode::$VALIDATE_ERROR);
                    }
                }
            }
        }
        return true;
    }

    /**
     * 获取工号基本信息
     * @param $id 工号
     * @param $type 1:根据部门ID获取，2：根据网点ID获取
     * @return array
     */
    public function getPcCode($id, $type)
    {
        $code    = ErrCode::$SUCCESS;
        $message = $real_message = '';

        $data = [];
        try {
            if ($type == 1) {
                $data['pc_code'] = $this->getPcCodeByDepartmentId($id);
            } else {
                $data['pc_code'] = $this->getPcCodeByStoreId($id);
            }

        } catch (\Exception $e) {
            $code         = ErrCode::$SYSTEM_ERROR;
            $message      = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        }

        if (!empty($real_message)) {
            $logger = $this->getDI()->get('logger');
            $logger->warning('普通付款申请创建页 - 基本信息 - 获取pc code 信息异常: ' . $real_message);
        }

        return [
            'code'    => $code,
            'message' => $message,
            'data'    => $data,
        ];

    }


    /**
     * 获取申请创建页 - 基本信息 - 默认数据
     *
     * @param array $user
     * @param $data_type
     * @return array
     */
    public function getOptionsCostCompanyData(array $user, $data_type)
    {
        $data=$this->getCreatePageBaseInfoDefaultData($user, $data_type);
        return [
            'code'    => $data['code'],
            'message' => $data['message'],
            'data'    => $data['data'],
        ];
    }

    /**
     * 获取申请创建页 - 基本信息 - 默认数据
     * @param array $user
     * @param int $data_type
     * @return array
     */
    public function getCreatePageBaseInfoDefaultData(array $user, int $data_type)
    {
        $code    = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $data = [];
        try {
            if ($data_type == 1) {
                // 提交申请的员工基本信息
                $user_base_info = $this->getUserMetaFromBi($user['id'], 0);

                $data['apply_no']                    = static::genSerialNo(Enums::ORDINARY_PAYMENT_APPLY_NO_PREFIX, RedisKey::ORDINARY_PAYMENT_APPLY_COUNTER);
                $data['create_id']                   = $user['id'];
                $data['create_name']                 = $this->getNameAndNickName($user['name'], $user['nick_name']);
                $data['create_node_department_name'] = $user_base_info['create_node_department_name'];
                $data['create_company_name']         = $user_base_info['create_company_name'];

            } else if ($data_type == 2) {
                // WHT 类别及对应WHT税率
                $data['wht_category'] = EnumsService::getInstance()->getFormatWhtRateConfig();

                // VAT7税率
                $data['vat7_rate_list'] = EnumsService::getInstance()->getFormatVatRateConfig();

                //付款分类&费用类型
               // $data['cost_category'] = $this->getCostCategory();

                //网点类型选择总部时使用的总部网点ID和网点名称
                $data['header_office'] = ['id' => Enums::PAYMENT_HEADER_STORE_ID, 'name' => Enums::PAYMENT_HEADER_STORE_NAME];
                //可抵扣vat税率
                $data['deductible_vat_tax'] = EnumsService::getInstance()->getFormatDeductibleVatTaxRateConfig();
                //COO下的公司名称和id
                $data['cost_company'] =  (new PurchaseService())->getCooCostCompany();
                //进外支付列表
                $data['pay_where_list'] = [
                    ['id' => (string)Enums\PayEnums::PAY_WHERE_IN, 'name' => static::$t->_('pay_where.1')],
                    ['id' => (string)Enums\PayEnums::PAY_WHERE_OUT, 'name' => static::$t->_('pay_where.2')],
                ];
                // 发票类型
                $data['ticket_type'] = $this->ticketTypeConfig();
                //业务类型
                $amity_data = [];
                foreach (GlobalEnums::$business_type_item as $k => $translation_k) {
                    if ($k == 0) {
                        continue;
                    }
                    $amity_data[] = [
                        'label' => self::$t[$translation_k],
                        'value' => (string)$k,
                    ];
                }
                $data['business_type'] = $amity_data;

                //17764返回SAP公司ID组、金蝶公司ID组
                $data['sap_company_ids'] = EnumsService::getInstance()->getSettingEnvValueIds('purchase_sap_company_ids');
                $data['kingdee_company_ids'] = EnumsService::getInstance()->getSettingEnvValueIds(KingDeeEnums::KINGDEE_BU_SETTING_CODE);
                $payee_type_config = static::payeeConfig();
                $data['payee_type_config'] =  $payee_type_config ? $payee_type_config : (object)[];

                //汽运保理费用类型
                $transportation_factoring_config = static::transportationFactoringConfig();
                $data['transportation_factoring_config'] = $transportation_factoring_config ? $transportation_factoring_config : (object)[];
            }

            $data['load_time'] = EnumsService::getInstance()->getSettingEnvValueMap('purchase_order_loan_time');

            //财务分类
            $finance_category_enums['type'] = MaterialClassifyEnums::MATERIAL_FINANCE_CATEGORY_ENUMS;
            $data['finance_map'] = ClassifyService::getInstance()->getClassifyArr($finance_category_enums, 'search')['data'];

            // 可编辑供应商银行账户号的第三方支付方配置
            $data['can_edit_supplier_bk_account_by_third_payer'] = EnumsService::getInstance()->getSettingEnvValueIds('can_edit_vendor_bank_account_by_third_payer');

            // 免校验的发票号配置清单
            $data['free_verification_invoice_numbers'] = EnumsService::getInstance()->getSettingEnvValueIds('free_verification_invoice_numbers');

            //是否采购使用
            foreach (BudgetObjectEnums::$is_purchase as $is_purchase => $item) {
                $data['budget_is_purchase'][] = [
                    'label' => static::$t->_($item),
                    'value' => $is_purchase,
                ];
            }
            // 报销模块-预算科目明细中餐费的ID，可以配置多个，多个ID使用,分隔
            $data['reimbursement_meal_expense_product_ids'] = EnumsService::getInstance()->getSettingEnvValueIds('reimbursement_meal_expense_product_ids');

            // 报销支付方式
            $data['reimbursement_payment_method_item'] = EnumsService::getInstance()->getPaymentMethodItem();

            //报销模块-可拆分费用部门工号，支持配置多个工号
            $data['reimbursement_split_department_staff_ids'] = EnumsService::getInstance()->getSettingEnvValueIds('reimbursement_split_department_staff_ids');

            //普通付款模块-可拆分费用部门工号，支持配置多个工号
            $data['ordinary_payment_split_department_staff_ids'] = EnumsService::getInstance()->getSettingEnvValueIds('ordinary_payment_split_department_staff_ids');

            //可以编辑费用总部/网点配置的用户
            $data['change_cost_all_type_by_staff_ids'] = EnumsService::getInstance()->getSettingEnvValueIds('change_cost_all_type_by_staff_ids');
        } catch (ValidationException $e) {
            $code    = ErrCode::$SYSTEM_ERROR;
            $message = $e->getMessage();
        } catch (\Exception $e) {
            $code         = ErrCode::$SYSTEM_ERROR;
            $message      = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        }

        if (!empty($real_message)) {
            $this->logger->warning('普通付款申请创建页 - 基本信息 - 默认值获取异常: ' . $real_message);
        }

        return [
            'code'    => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data'    => $data,
        ];
    }

    /**
     * 获取工号基本信息
     * @param $staff_id
     * @return array
     */
    public function getUser($staff_id)
    {
        $code    = ErrCode::$SUCCESS;
        $message = $real_message = '';

        $data = [];
        try {
            $data = $this->getUserMetaFromBi($staff_id, 1);

            // 申请人可选的费用所属网点/总部枚举值
            $store_id = $data['apply_store_id'];
            if ($store_id == Enums::PAYMENT_HEADER_STORE_ID) {
                $store_id = Enums::HEAD_OFFICE_STORE_FLAG;
            }

            // 费用所属网点/总部枚举值的获取
            $costStoreType = DetailService::getInstance()->costStoreType($data['apply_node_department_id'], $store_id, $staff_id);

            // 因上述枚举值获取公用的是报销模块的实现, 但其枚举值与付款模块的枚举值正好相反, 故此处需要将网点/总部的枚举值互调下
            foreach ($costStoreType['items'] as &$item) {
                if ($item['id'] == 1) {
                    $item['id'] = Enums::PAYMENT_COST_STORE_TYPE_02;
                } elseif ($item['id'] == 2) {
                    $item['id'] = Enums::PAYMENT_COST_STORE_TYPE_01;
                }

                $data['cost_store_type_list'] = $costStoreType['items'];
            }

        } catch (ValidationException $e) {
            $code    = ErrCode::$SYSTEM_ERROR;
            $message = $e->getMessage();
        } catch (\Exception $e) {
            $code         = ErrCode::$SYSTEM_ERROR;
            $message      = static::$t->_('retry_later');
            $real_message = $e->getMessage();
            $this->logger->warning('普通付款申请创建页 - 基本信息 - 获取申请人信息异常: ' . $real_message);
        }

        return [
            'code'    => $code,
            'message' => $message,
            'data'    => $data,
        ];

    }


    /**
     * 获取申请创建页 - 合同列表
     * 获取归档合同中状态为已归档的所有合同列表
     * @param array $params
     * @param array $user
     * @return array
     */
    public function getContractList(array $params)
    {
        $code    = ErrCode::$SUCCESS;
        $message = $real_message = '';

        $data = [];

        try {
            $from_purchase = !empty($params['from_purchase']) ? true : false;
            $data = $this->getArchiveContract($params['cno'], $from_purchase);

        } catch (\Exception $e) {
            $code         = ErrCode::$SYSTEM_ERROR;
            $message      = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        }

        if (!empty($real_message)) {
            $logger = $this->getDI()->get('logger');
            $logger->warning('普通付款 - 申请创建页-相关合同 获取异常: ' . $real_message);
        }

        return [
            'code'    => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data'    => $data,
        ];
    }

    /**
     * 获取申请创建页 - 系统网点列表
     * @param array $params
     * @param array $user
     * @return array
     */
    public function getStoreList(array $params, array $user)
    {
        $code    = ErrCode::$SUCCESS;
        $message = $real_message = '';

        $data = [];

        try {

            // 获取网点
            $data = $this->getSysStoreList($params, $user);

        } catch (\Exception $e) {
            $code         = ErrCode::$SYSTEM_ERROR;
            $message      = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        }

        if (!empty($real_message)) {
            $logger = $this->getDI()->get('logger');
            $logger->warning('普通付款申请创建页 - 系统网点列表 获取异常: ' . $real_message);
        }

        return [
            'code'    => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data'    => $data,
        ];
    }

    /**
     * TODO 方法的参数太多了，后续考虑优化传参方式
     * 构建金额详情表数据
     *
     * @param $amount_detail // 金额详情数据
     * @param $amount_total_no_tax
     * @param $amount_total_vat
     * @param $amount_total_wht
     * @param $amount_total_have_tax
     * @param $cost_company_id // 提交的费用公司id(基本信息)
     * @param $sap_cost_company_ids // 配置的sap相关的费用公司ids
     * @param $main_data
     * @return mixed
     * @throws ValidationException
     */
    private function buildAmountDetailData($amount_detail, &$amount_total_no_tax, &$amount_total_vat, &$amount_total_wht, &$amount_total_have_tax, $cost_company_id = 0,  $sap_cost_company_ids = [], $main_data)
    {

        $amount_detail_item = []; //金额明细表数据
        $limit_level_code = true;
        //V22269 - 金额详情需存储费用一级部门ID
        $amount_detail = $this->getAmountDetailSysDepartmentId($amount_detail);
        foreach ($amount_detail as $index => $item) {
            // [1]金额单项计算
            // 不含税金额
            $amount_no_tax = round($item['amount_no_tax'], 2);
            // vat 7% 金额
            $amount_vat = $item['amount_vat'];

            // 如果配置了sap相关的费用公司, 则 进行费用公司校验
            if(!empty($sap_cost_company_ids) && in_array($cost_company_id, $sap_cost_company_ids) && empty($item['cost_center_name'])){
                throw new ValidationException(self::$t['cost_center_not_empty_string'], ErrCode::$VALIDATE_ERROR);
            }

            //含税金额 (不含税金额 + vat金额）
            $amount_have_tax = $amount_no_tax + $amount_vat;
            $amount_wht = $item['wht_amount'];

            //不含税金额总计
            $amount_total_no_tax += $amount_no_tax;

            //VAT 金额总计[之前+累计有问题，改成函数保留3位小数是因为入库要四舍五入]
            $amount_total_vat = bcadd($amount_total_vat, $item['amount_vat'], 3);

            //WHT 金额总计[之前+累计有问题，改成函数保留3位小数是因为入库要四舍五入]
            $amount_total_wht = bcadd($amount_total_wht, is_numeric($item['wht_amount']) ? $item['wht_amount'] : 0, 3);

            //含税金额总计
            $amount_total_have_tax += $amount_have_tax;

            if (in_array($item['level_code'], self::$level_code_arr)) {
                $limit_level_code = false;
            }

            // 构建 金额明细表数据
            $amount_detail_item[] = [
                'budget_id'           => $item['budget_id'], //付款分类（科目ID）
                'level_code'          => $item['level_code'],//付款分类（科目code）
                'is_budget'           => $item['isbudget'],//是否参与预算管控0不1是，15998需求新增用于马来逻辑判断
                'budget_name'         => $item['budget_name'],//核算科目名称，15998需求新增用于马来逻辑判断
                'product_id'          => $item['product_id'],//费用类型（商品ID）
                'ledger_account_id'   => $item['ledger_account_id'],//核算科目id
                'cost_start_date'     => $item['cost_start_date'], //费用发生日期
                'cost_end_date'       => $item['cost_end_date'], //费用结束日期
                'amount_no_tax'       => $amount_no_tax, //不含税金额
                'amount_vat'          => $amount_vat,//vat 金额 （不含税金额的7%）
                'amount_have_tax'     => $amount_have_tax,//含税金额
                'wht_category'        => $item['wht_category'],//WHT类别
                'wht_rate'            => $item['wht_rate'],//WHT税率
                'vat_rate'            => $item['vat7_rate'],//VAT税率
                'amount_wht'          => $amount_wht,//WHT金额
                'cost_store_id'       => $item['cost_store_id'],//费用所属网点ID（若基本信息中费用所属网点类型为：总部，则该字段值存储TH00000001）
                'cost_store_name'     => $item['cost_store_name'],//费用所属网点名称（若cost_store_id = TH00000001,则该字段职位 Header office）
                'cost_center_name'    => $item['cost_center_name'] ?? '',//费用-所属中心(pc_code)
                'cost_department_id'  => $item['cost_department_id'] ?? 0,//费用部门id,总部必填
                'cost_department_name'=> $item['cost_department_name'] ?? '',//费用部门名称,总部必填
                'cost_sys_department' => $item['cost_sys_department'] ?? 0,//费用一级部门ID
                'contract_no'         => $item['contract_no'] ?? '',//合同编号
                'attachment_list'     => $item['attachment_list'] ?? [],//附件
                'voucher_description' => $item['voucher_description'] ?? '',//凭证描述
                'deductible_vat_tax' => $item['deductible_vat_tax'] ?? '',//可抵扣VAT税率
                'deductible_tax_amount' => $item['deductible_tax_amount'] * 100 ?? '',//可抵扣税额
                'pono'                  => $item['pono'] ?? '',//采购订单编号
                'finance_category_id' => $item['finance_category_id'] ?? 0,//财务分类id
            ];
        }
        if (get_country_code() == GlobalEnums::TH_COUNTRY_CODE && $limit_level_code && empty($main_data['sap_supplier_no']) && isset($main_data['cost_company_id']) && in_array($main_data['cost_company_id'], $sap_cost_company_ids)) {
            throw new ValidationException(self::$t['sap_supplier_no_required_error'], ErrCode::$VALIDATE_ERROR);
        }

        return $amount_detail_item;
    }

    /**
     * V22269 - 金额详情需存储费用一级部门ID
     * @param array $amount_detail 金额详情明细行
     * @return array
     */
    private function getAmountDetailSysDepartmentId(array $amount_detail): array
    {
        $department_repository = new DepartmentRepository();
        $department_list = $department_repository->getDepartmentByIds(array_column($amount_detail, 'cost_department_id'));
        foreach ($amount_detail as &$item) {
            if (empty($item['cost_department_id']) || empty($department_list[$item['cost_department_id']])) {
                continue;
            }
            $one_department_info = $department_list[$item['cost_department_id']];
            $first_department_info = $department_repository->getFirstLevelDepartmentByAncestryV3($one_department_info['ancestry_v3']);
            $item['cost_sys_department'] = $first_department_info['id'] ?? 0;
        }
        return $amount_detail;
    }

    /**
     * 组装普通付款的申请入库数据
     *
     * @param array $data
     * @param array $user
     * @return array
     * @throws ValidationException
     */
    private function buildMainData(array $data, array $user)
    {
        if (empty($data)) {
            return [];
        }

        // 获取用户基本信息
        $create_user_info = $this->getUserMetaFromBi($user['id'], 0);
        $apply_user_info  = $this->getUserMetaFromBi($data['apply_staff_id'], 1);

        // 获取币种与系统默认币种的汇率
        $exchange_rate = EnumsService::getInstance()->getCurrencyExchangeRate($data['currency']);
        $data['exchange_rate'] = $exchange_rate ? $exchange_rate : 1;

        //19856需求，关联保理付款单号不是空的增加如下拦截逻辑
        $data = $this->validateFactoringAmountAndPeroid($data);
        
        $country_code = get_country_code();
        $kingdee_company_ids = EnumsService::getInstance()->getSettingEnvValueIds(KingDeeEnums::KINGDEE_BU_SETTING_CODE);
        $account_type = in_array($data['cost_company_id'], $kingdee_company_ids) ? KingDeeEnums::ACCOUNT_TYPE_SUB_COMPANY : KingDeeEnums::ACCOUNT_TYPE_FLASH_EXPRESS_COMPANY;

        return [
            'apply_no'                     => $data['apply_no'],                               //编号
            'create_id'                    => $create_user_info['create_id'],                  //发起人工号
            'create_name'                  => $create_user_info['create_name'],                //发起人姓名
            'create_node_department_id'    => $create_user_info['create_node_department_id'],  //发起人部门id
            'create_node_department_name'  => $create_user_info['create_node_department_name'],//发起人部门名称
            'create_company_id'            => $create_user_info['create_company_id'],          //发起人公司ID
            'create_company_name'          => $create_user_info['create_company_name'],        //发起人公司名称
            'apply_id'                     => $apply_user_info['apply_id'],                    //申请人id
            'apply_name'                   => $apply_user_info['apply_name'],                  //申请人姓名
            'apply_sys_department_id'      => $apply_user_info['apply_sys_department_id'],     //申请人一级部门id
            'apply_node_department_id'     => $apply_user_info['apply_node_department_id'],    //申请人直属部门id
            'apply_node_department_name'   => $apply_user_info['apply_node_department_name'],  //申请人部门名称
            'apply_company_id'             => $apply_user_info['apply_company_id'],            //申请人公司ID
            'apply_company_name'           => $apply_user_info['apply_company_name'] ?? null,  //申请人公司名称
            'apply_store_id'               => $apply_user_info['apply_store_id'],              //申请人所属网点id
            'apply_store_name'             => $apply_user_info['apply_store_name'],            //申请人所属网点名称
            'apply_mobile'                 => $apply_user_info['apply_mobile'],                //申请人电话
            'apply_email'                  => $apply_user_info['apply_email'],                 //申请人邮箱
            'cost_department_id'           => $data['cost_department_id'],                     //费用所属部门ID
            'cost_department_name'         => $data['cost_department_name'],                   //费用所属部门名称
            'cost_store_type'              => $data['cost_store_type'],                        //费用所属网点（类型）：1 :总部,2:网点
            'payment_method'               => $data['payment_method'],                         //付款方式：1-银行转账(默认); 2-支票
            'currency'                     => $data['currency'],                               //付款币种: 1-泰铢(默认),
            'exchange_rate'                => $data['exchange_rate'],
            'should_pay_date'              => $data['should_pay_date'],      //应付日期
            'remark'                       => $data['remark'] ?? '',         //费用信息-备注
            'amount_total_no_tax'          => $data['amount_total_no_tax'],  //不含税金额总计
            'amount_total_vat'             => $data['amount_total_vat'],     //vat金额总计
            'amount_total_wht'             => $data['amount_total_wht'],     //wht金额总计
            'amount_discount'              => $data['amount_discount'],      //折扣
            'amount_total_have_tax'        => $data['amount_total_have_tax'],//含税金额总计
            'amount_total_actually'        => $data['amount_total_actually'],//实付金额总计
            'attachments'                  => $data['attachments'],
            'invoice_no'                   => $data['invoice_no'] ?? '',//增值税发票号
            'invoice_type'                 => $country_code == GlobalEnums::PH_COUNTRY_CODE ? $data['invoice_type'] : GlobalEnums::FINANCIAL_INVOICE_TYPE_0,
            'pay_type'                     => Enums::$budget_object_reimbursement_type_map[$data['amount_detail'][0]['level_code']] ?? Enums::REIMBURSEMENT_TYPE_OTHER,
            'payee_type'                   => $data['payee_type'],
            'amount_total'                 => $data['amount_total'] ?? '0.00',
            'cost_company_id'              => $data['cost_company_id'] ?? '',
            'extra_message'                => $data['extra_message'] ?? '',
            'voucher_abstract'             => $data['voucher_abstract'] ?? '',
            'swift_code'                   => $data['swift_code'] ?? '',
            'pay_where'                    => $data['pay_where'] ?? 1,
            'ticket_number'                => $data['ticket_number'] ?? '',
            'ticket_date'                  => $data['ticket_date'] ?? '',
            'loan_time'                    => $data['loan_time'] ?? '',
            'ver'                          => 1,
            'business_type'                => $data['business_type'] ?? '',
            'is_with_vat_invoice'          => $country_code == GlobalEnums::ID_COUNTRY_CODE ? $data['is_with_vat_invoice'] : GlobalEnums::IS_WITH_VAT_INVOICE_DEFAULT,
            'account_type'                 => $account_type,
            'is_factoring'                 => $data['is_factoring'] ?? 0,                //是否保理付款
            'factoring_vendor_id'          => $data['factoring_vendor_id'] ?? '',        //保理对象(供应商编码)
            'factoring_vendor_name'        => $data['factoring_vendor_name'] ?? '',      //保理对象(供应商名称)
            'factoring_apply_no'           => $data['factoring_apply_no'] ?? '',         //关联保理付款单号
            'factoring_payment_peroid_num' => $data['factoring_payment_peroid_num'] ?? 0,//保理付款期次
            'budget_withholding_id'        => $data['budget_withholding_id'] ?? 0,       //预算管理-费用预提-预提单ID
            'budget_withholding_no'        => $data['budget_withholding_no'] ?? '',      //预算管理-费用预提-预提单号
        ];
    }

    /**
     *
     *
     * @param $costDepartment
     * @param $costStoreType
     * @return array
     *
     */
    public function getBudgetList($costDepartment, $costStoreType)
    {
        return $this->budgetList([
            'cost_department' => $costDepartment,
            'cost_store_type' => $costStoreType,
        ]);
    }

    /**
     * 获取科目树状列表
     *
     * @param $data
     * cost_department 费用部门
     * cost_store 费用网点
     * @return array
     *
     */
    protected function budgetList($data)
    {
        $budgetService = new BudgetService();
        $budgetTrees = $budgetService->objectList([
            'department_id' => $data['cost_department'], //
            'organization_type' => $data['cost_store_type'],
        ], BudgetService::ORDER_TYPE_3);
        $endBudgetIds = $budgetService->endBudgetIds($budgetTrees);
        if ($endBudgetIds) {
            $budgets = BudgetObject::find([
                'conditions' => ' id in ({ids:array}) ',
                'bind' => ['ids' => $endBudgetIds],
            ])->toArray();
            $products = $budgetService->purchaseProducts(array_column($budgets, 'level_code'), BudgetService::ORDER_TYPE_3);
            $budgetTrees = $this->bindProduct($budgetTrees, $products);
        }

        return $budgetTrees;
    }

    private function bindProduct($budgetTrees, $products)
    {
        $tipsMap = CostCategoryReimbursementDetailService::getInstance()->getMapByBizType(CostCategoryReimbursementDetailService::SUPPORT_BIZ_TYPE_ORDINARY_PAYMENT);
        foreach ($budgetTrees as $k => $budgetTree) {
            $budgetTrees[$k]['tips'] = $tipsMap[$budgetTree['id']] ?? '';
            if (isset($budgetTree['list'])) {
                $budgetTrees[$k]['list'] = $this->bindProduct($budgetTree['list'], $products);
            } else {
                if (isset($products[$budgetTree['level_code']])) {
                    $budgetTrees[$k]['products'] = isset($products[$budgetTree['level_code']]) ? $products[$budgetTree['level_code']] : [];
                }
            }
        }
        return $budgetTrees;
    }

    public function check_payment($amount_detail_item)
    {
        if(empty($amount_detail_item)) return [];

        foreach ($amount_detail_item as $level_code_item) {
            $type_item[] = Enums::$budget_object_reimbursement_type_map[$level_code_item['level_code']] ?? Enums::REIMBURSEMENT_TYPE_OTHER;
        }
        // 校验多条普通付款是否符合既定规则, 不符规则时，给出相应提示
        $type_item = array_unique($type_item);

        // 劳务成功-外协 与 福利费/团建费 不可同时提交
        if (in_array(Enums::REIMBURSEMENT_TYPE_EXTERNAL_COURIER, $type_item) && in_array(Enums::REIMBURSEMENT_TYPE_WELFARE_OR_MORALE, $type_item)) {

            return static::$t->_('ordinarypayment_type_error_001');
        } else if (in_array(Enums::REIMBURSEMENT_TYPE_EXTERNAL_COURIER, $type_item) && in_array(Enums::REIMBURSEMENT_TYPE_OTHER, $type_item)) {
            // 劳务成功-外协 与 其他 不可同时提交

            return static::$t->_('ordinarypayment_type_error_003');
        } else if (in_array(Enums::REIMBURSEMENT_TYPE_WELFARE_OR_MORALE, $type_item) && in_array(Enums::REIMBURSEMENT_TYPE_OTHER, $type_item)) {
            // 福利费/团建费 与 其他 不可同时提交

            return static::$t->_('ordinarypayment_type_error_002');
        }
        return [];
    }

    /**
     * staff info
     *
     * @param $uid
     * @param int $staff_type 员工类型 1-内部员工; 2-外协员工; 3-个人代理  (普通付款个人收款类型场景)
     * @return array
     */
    public function getStaffInfo($uid, int $staff_type = 0)
    {
        $code    = ErrCode::$SUCCESS;
        $message = 'success';
        $data    = [];

        try {
            $staff_list = (new HrStaffRepository())->getStaffInfoListByStaffIds([$uid], StaffInfoEnums::HR_STAFF_ITEMS_BANK_NO_NAME);
            $data = $staff_list[0] ?? [];
            if (!empty($data)) {
                $data['bank_no'] = $data['bank_no'] ?? '';

                // 有员工类型, 则校验员工属性 是否 与 员工类型相匹配, 不匹配则返回空信息
                if ($staff_type) {
                    // 内部员工 的 属性校验
                    $is_innser_staff = $staff_type == StaffInfoEnums::STAFF_TYPE_INNER && $data['formal'] == StaffInfoEnums::FORMAL_IN && $data['hire_type'] != StaffInfoEnums::HIRE_TYPE_PERSONAL_AGENCY;

                    // 外协员工 的 属性校验
                    $is_outsourcing_staff = $staff_type == StaffInfoEnums::STAFF_TYPE_OUTSOURCING && $data['formal'] == StaffInfoEnums::FORMAL_NOT_IN;

                    // 个人代理 的 属性校验
                    $is_personal_agent_staff = $staff_type == StaffInfoEnums::STAFF_TYPE_PERSONAL_AGENT && $data['formal'] == StaffInfoEnums::FORMAL_IN && $data['hire_type'] == StaffInfoEnums::HIRE_TYPE_PERSONAL_AGENCY;

                    if (!$is_innser_staff && !$is_outsourcing_staff && !$is_personal_agent_staff) {
                        $data = [];
                    }
                }

                unset($data['bank_type'], $data['formal'], $data['hire_type']);
            }

        } catch (\Exception $e) {
            $code = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage();
            $this->logger->warning('普通付款申请创建页 - 用户信息获取异常: ' . $real_message);
        }

        return [
            'code'    => $code,
            'message' => $message,
            'data'    => $data ? $data : null,
        ];
    }

    /**
     * 添加文件附件
     * @param $data
     * @param integer $type 1付款申请 4数据查询
     * @return array
     */
    public function addFile($data, $type = self::LIST_TYPE_APPLY){
        $code = ErrCode::$SUCCESS;
        $message = 'ok';
        $real_message = '';

        try {
            // 普通付款是否存在
            $exists = OrdinaryPayment::getFirst([
                'conditions' => 'id = :id:',
                'bind'       => ['id' => $data['id']],
            ]);
            if (empty($exists)) {
                throw new BusinessException('普通付款申请单不存在，ID为:' . $data['apply_no'], ErrCode::$VALIDATE_ERROR);
            }

            if (!empty($data['required_supplement_file'])) {
                $attach_arr = $oss_bucket_key = [];
                foreach ($data['required_supplement_file'] as $k => $file) {
                    $tmp = [];
                    $tmp['oss_bucket_type'] =  OrdinaryPaymentEnums::OSS_BUCKET_TYPE_ORDINARY_PAYMENT_ATTACHMENT_FILE;
                    $tmp['oss_bucket_key'] =  $data['id'];
                    $tmp['sub_type'] = 0;
                    $tmp['bucket_name'] = $file['bucket_name'];
                    $tmp['object_key'] = $file['object_key'];
                    $tmp['file_name'] = $file['file_name'];
                    $tmp['created_at'] = date('Y-m-d H:i:s');
                    $attach_arr[] = $tmp;
                    $oss_bucket_key[] = $data['id'];
                }
                if (!empty($attach_arr) && !empty($oss_bucket_key)) {
                    // 删除历史关联附件
                    $old_model = AttachModel::find([
                        'conditions' => 'oss_bucket_type = :oss_bucket_type: AND oss_bucket_key IN({oss_bucket_key:array})',
                        'bind' => ['oss_bucket_type' => OrdinaryPaymentEnums::OSS_BUCKET_TYPE_ORDINARY_PAYMENT_ATTACHMENT_FILE,
                            'oss_bucket_key' => array_values(array_unique($oss_bucket_key))],
                    ]);
                    if (!empty($old_model)) {
                        $old_model->delete();
                    }
                    $attach = new AttachModel();
                    $attach_bool = $attach->batchInsert($attach_arr);
                    if ($attach_bool === false) {
                        throw new BusinessException('普通付款-附件添加失败，attachment data => ' .
                            json_encode($data['required_supplement_file'],JSON_UNESCAPED_UNICODE),
                            ErrCode::$CONTRACT_CREATE_ERROR);
                    }
                }
            }

            //应付日期
            if (!empty($data['should_pay_date']) && $type == self::LIST_TYPE_DATA) {
                $exists->should_pay_date = $data['should_pay_date'];
                $exists->updated_at = date('Y-m-d H:i:s');
                $bool = $exists->save();
                if ($bool === false) {
                    throw new BusinessException('普通付款-补充附件-更新应付日期失败 data => ' . json_encode($data['should_pay_date'],JSON_UNESCAPED_UNICODE) . '；可能的原因是：' . get_data_object_error_msg($exists), ErrCode::$BUSINESS_ERROR);
                }
            }
        } catch (ValidationException $e) {
            $code = $e->getCode();
            $message = $e->getMessage();
        } catch (BusinessException $e) {
            $code = $e->getCode();
            $message = $e->getMessage();
            $real_message = $e->getMessage().'---'.$e->getLine().'---'.$e->getTraceAsString();
        } catch (\Exception $e) {
            //系统错误，不可对外抛出
            $code = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage().'---'.$e->getLine().'---'.$e->getTraceAsString();
        }

        if (!empty($real_message)) {
            $this->logger->error('ordinary-payment-add-attachment-failed:' . $real_message);
        }

        return [
            'code' => $code,
            'message' => $message,
            'data' => $result ?? [],
        ];
    }

    /**
     * 普通付款科目组合校验【eg ：[1,2] 校验只能[1],[2],[1,2]三种组合】
     * @param array $config_arr 数据组合的配置
     * @param array $products_arr 详情数据里面提交的科目数据
     * @return bool 返回值为真就校验不通过
     */
    public function combinationCheck(array $config_arr, array $products_arr)
    {
        $check  = false;
        $first  = $config_arr[0] ?? '';//配置项第一个科目
        $second = $config_arr[1] ?? '' ;//配置项第二个科目
        if ((in_array($first, $products_arr) && !in_array($second, $products_arr) && count($products_arr) > 1)
            ||
            (!in_array($first, $products_arr) && in_array($second, $products_arr) && count($products_arr) > 1)
            ||
            (in_array($first, $products_arr) && in_array($second, $products_arr) && count($products_arr) > 2)
        ) {
            $check = true;
        }
        return $check;
    }

    /**
     * 验证关联的采购订单是否符合条件
     * @param $detail
     * @param $cost_company_id
     * @throws ValidationException
     * @date 2023/9/13
     */
    public function checkPurchaseOrder($detail, $cost_company_id)
    {
        $purchase_order_nos = array_values(array_filter(array_unique(array_column($detail, 'pono'))));
        if (!empty($purchase_order_nos)) {
            $purchase_order_data = PurchaseOrderRepository::getInstance()->getOrderDataByNos($purchase_order_nos);
            $purchase_order_data = array_column($purchase_order_data, null, 'pono');
            foreach ($purchase_order_nos as $no) {
                if (!key_exists($no, $purchase_order_data)) {
                    throw new ValidationException(self::$t['ordinary_import_pono_not_exist_error'] . $no, ErrCode::$VALIDATE_ERROR);
                }
                if ($purchase_order_data[$no]['cost_company'] != $cost_company_id) {
                    throw new ValidationException(self::$t['ordinary_import_po_company_error'] . $no, ErrCode::$VALIDATE_ERROR);
                }
                if ($purchase_order_data[$no]['status'] != Enums::WF_STATE_APPROVED) {
                    throw new ValidationException(self::$t['ordinary_import_po_status_error'] . $no, ErrCode::$VALIDATE_ERROR);
                }
            }
        }
    }

    /**
     * 判断指定供应商的发票号码是否重复
     *
     * @param string $supplier_id 供应商编码
     * @param string $ticket_number 发票号码
     * @param string $factoring_apply_no 关联保理付款单号
     * @return bool
     * @throws ValidationException
     */
    private function validateSupplierTicketNumber(string $supplier_id, string $ticket_number, string $factoring_apply_no)
    {
        if (empty($supplier_id) || empty($ticket_number)) {
            return true;
        }

        $builder = $this->modelsManager->createBuilder();
        $builder->columns('main.apply_no, main.factoring_apply_no');
        $builder->from(['main' => OrdinaryPayment::class]);
        $builder->leftJoin(OrdinaryPaymentExtend::class, 'main.id = extend.ordinary_payment_id', 'extend');
        $builder->where('main.ticket_number = :ticket_number: AND extend.supplier_id = :supplier_id:', ['ticket_number' => $ticket_number, 'supplier_id' => $supplier_id]);
        $builder->inWhere('main.approval_status', [Enums::WF_STATE_PENDING, Enums::WF_STATE_APPROVED]);
        $builder->inWhere('main.pay_status', [Enums::PAYMENT_PAY_STATUS_PENDING, Enums::PAYMENT_PAY_STATUS_PAY]);
        //19856需求：发票号码-重复校验规则调整
        if ($factoring_apply_no) {
            //关联保理付款单号不为空,已经提交的普通付款单据号所关联保理付款单号与当前提交关联保理付款单号不一致则拦截
            $data = $builder->getQuery()->execute()->toArray();
            if (!empty($data)) {
                foreach ($data as $item) {
                    if ($item['factoring_apply_no'] != $factoring_apply_no) {
                        throw new ValidationException(static::$t->_('ticket_number_already_used_hint', ['apply_no' => $item['apply_no']]), ErrCode::$VALIDATE_ERROR);
                    }
                }
            }
        } else {
            //关联保理付款单号为空，则校验逻辑不变
            $data = $builder->getQuery()->getSingleResult();
            if (!empty($data)) {
                throw new ValidationException(static::$t->_('ticket_number_already_used_hint', ['apply_no' => $data->apply_no]), ErrCode::$VALIDATE_ERROR);
            }
        }

        return true;
    }

    /**
     * 校验供应商归属是否不归属指定国家
     * @return void
     * @throws ValidationException
     */
    private function validateSupplierOwnership($supply_id, $supply_belong_country)
    {
        if (empty($supply_id) || empty($supply_belong_country)) {
            return;
        }

        $vendorDetail = VendorModel::findFirst([
            'conditions' => 'vendor_id = :vendor_id:',
            'bind' => [
                'vendor_id' => $supply_id,
            ],
        ]);
        if (empty($vendorDetail)) { //供应商ID不存在
            throw new ValidationException(static::$t->_('supply_not_exists'), ErrCode::$VALIDATE_ERROR);
        }
        if ($vendorDetail->ownership == $supply_belong_country) { //供应商归属于指定国家
            throw new ValidationException(static::$t->_('supply_belongs_this_county'), ErrCode::$VALIDATE_ERROR);
        }
    }

    /**
     * 判断关联保理付款单号下不含税金额、期次是否超出限制
     * @param $data
     * @return mixed
     * @throws ValidationException
     */
    private function validateFactoringAmountAndPeroid($data)
    {
        if (isset($data['factoring_apply_no']) && !empty($data['factoring_apply_no'])) {
            $factoring_apply_info = OrdinaryPayment::findFirst([
                'conditions' => 'apply_no = :apply_no:',
                'columns'    => 'amount_total_actually,exchange_rate',
                'bind'       => ['apply_no' => $data['factoring_apply_no']],
            ]);
            //关联保理付款单号查询不到
            if (!$factoring_apply_info) {
                throw new ValidationException(self::$t['factoring_apply_no_not_exists'], ErrCode::$VALIDATE_ERROR);
            }

            // 金额详情-实付金额总计 根据汇率 转为当前国家币种的金额值
            $factoring_apply_info->amount_total_actually = EnumsService::getInstance()->amountExchangeRateCalculation($factoring_apply_info->amount_total_actually, $factoring_apply_info->exchange_rate, 2);

            //获取保理付款单下金额详情-实付金额总计累计、期数累计
            $factoring_apply_list = OrdinaryPaymentListService::getInstance()->getFactoringList([$data['factoring_apply_no']]);
            $one_factoring_apply_info = $factoring_apply_list[$data['factoring_apply_no']];

            //当前提交的金额详情-实付金额总计 + 取保理付款单下金额详情-实付金额总计累计 > 关联保理付款单号单据自身的金额详情-实付金额总计
            $params_amount_total_actually = EnumsService::getInstance()->amountExchangeRateCalculation($data['amount_total_actually'], $data['exchange_rate'], 2);
            $amount_total_actually = bcadd($params_amount_total_actually, $one_factoring_apply_info['amount_total_actually'], 2);

            if (bccomp($amount_total_actually, $factoring_apply_info->amount_total_actually, 2) === 1) {
                throw new ValidationException(self::$t['factoring_amount_total_no_tax_pass'], ErrCode::$VALIDATE_ERROR);
            }
            $ordinary_payment_factoring_nums = EnumsService::getInstance()->getSettingEnvValue('ordinary_payment_factoring_nums', 0);
            if ($one_factoring_apply_info['factoring_payment_peroid_num'] > $ordinary_payment_factoring_nums) {
                throw new ValidationException(self::$t['factoring_payment_peroid_num_pass'], ErrCode::$VALIDATE_ERROR);
            }
            $data['factoring_payment_peroid_num'] = $one_factoring_apply_info['factoring_payment_peroid_num'];
        }
        return $data;
    }

    /**
     * V21975 - 普通付款- 提交关联预提单 - 更新预提单的使用金额和剩余金额、记录普通付款是否需要红冲凭证等
     * @param object $ordinary_payment 普通付款对象
     * @param object $budget_withholding_info 关联预提单对象
     * @param array $sap_cost_company_ids sap公司ID组
     * @return bool
     * @throws BusinessException
     * @throws ValidationException
     */
    private function saveBindBudgetWithholdingInfo(object $ordinary_payment, object $budget_withholding_info, array $sap_cost_company_ids)
    {
        //普通付款 - 关联预提单 - 公司主体属于同步SAP配置 - 普通付款并主表（预提单无明细）或普通付款明细行（预提单有明细）- 记录是否需要红冲预提并且记录红冲金额
        if (in_array($ordinary_payment->cost_company_id, $sap_cost_company_ids)) {
            $budget_withholding_detail = $budget_withholding_info->getDetails()->toArray();
            if ($budget_withholding_detail) {
                //提交成功后如果关联的预提单有明细行且公司主体属于同步SAP配置则普通付款明细行记录是否需要红冲预提并且记录红冲金额
                $budget_withholding_detail = array_column($budget_withholding_detail, null, 'cost_center_code');
                $ordinary_payment_detail   = $ordinary_payment->getDetails();
                foreach ($ordinary_payment_detail as $item) {
                    //成本中心为空，跳过不处理
                    if (empty($item->cost_center_name)) {
                        continue;
                    }
                    //与预提单的中心不一致，跳过不处理
                    if (!isset($budget_withholding_detail[$item->cost_center_name])) {
                        continue;
                    }

                    //如果提交时(当前单据未占用使用金额)预提单明细行预提金额小于等于预提单明细行的使用金额，则红冲金额为0，需要按照付款明细行中的成本中心与预提单中的成本中心比对
                    $one_budget_withholding_detail = $budget_withholding_detail[$item->cost_center_name];
                    if ($one_budget_withholding_detail['provision_amount'] <= $one_budget_withholding_detail['use_amount']) {
                        $red_voucher_amount = 0;
                    } else {
                        //如果提交时明细行本次含税金额(转换本位币后)<=明细行剩余预提金额，则红冲金额取本次提交明细行的含税金额(转换本位币后)
                        //如果提交时明细行本次含税金额(转换本位币后)>明细行剩余预提金额，则红冲金额取剩余金额的相反数
                        $remain_amount      = bcdiv($one_budget_withholding_detail['provision_amount'] - $one_budget_withholding_detail['use_amount'], 1000, 2);
                        $amount_have_tax    = EnumsService::getInstance()->amountExchangeRateCalculation($item->amount_have_tax, $ordinary_payment->exchange_rate, 2);
                        $red_voucher_amount = (bccomp($amount_have_tax, $remain_amount) === 1) ? $remain_amount : $amount_have_tax;
                    }

                    //是否需要红冲预提：如果红冲金额大于0则需要预提，如果小于0则不需要
                    $ordinary_payment_detail_update = [
                        'is_send_red_voucher' => $red_voucher_amount > 0 ? BudgetWithholdingEnums::IS_SEND_RED_VOUCHER_YES : BudgetWithholdingEnums::IS_SEND_RED_VOUCHER_NO,
                        'red_voucher_amount'  => $red_voucher_amount,
                    ];
                    $bool = $item->i_update($ordinary_payment_detail_update);
                    if ($bool === false) {
                        throw new BusinessException('普通付款申请-关联预提单-有明细行-普通付款明细行记录是否需要红冲预提并且记录红冲金额，失败 = ' . json_encode($ordinary_payment_detail_update, JSON_UNESCAPED_UNICODE) . '; 可能的原因是：' . get_data_object_error_msg($item), ErrCode::$ORDINARY_PAYMENT_CREATE_MAIN_ERROR);
                    }
                }
            } else {
                //提交成功后如果关联的预提单无明细行且公司主体属于同步SAP配置普通付款并主表记录是否需要红冲预提并且记录红冲金额
                if ($budget_withholding_info->provision_amount <= $budget_withholding_info->use_amount) {
                    //如果提交时(当前单据未占用使用金额)预提单预提金额小于等于预提单的使用金额，则红冲金额为0
                    $red_voucher_amount = 0;
                } else {
                    //如果提交时本次含税金额总计(转换本位币后)<=剩余金额，则红冲金额取本次含税金额总计(转换本位币后)
                    //如果提交时本次含税金额总计(转换本位币后)>剩余金额，则红冲金额取剩余金额
                    $remain_amount         = bcdiv($budget_withholding_info->provision_amount - $budget_withholding_info->use_amount, 1000, 2);
                    $amount_total_have_tax = EnumsService::getInstance()->amountExchangeRateCalculation($ordinary_payment->amount_total_have_tax, $ordinary_payment->exchange_rate, 2);
                    $red_voucher_amount    = (bccomp($amount_total_have_tax, $remain_amount) === 1) ? $remain_amount : $amount_total_have_tax;
                }

                //是否需要红冲预提：如果红冲金额大于0则需要预提，如果小于0则不需要
                $ordinary_payment_update = [
                    'is_send_red_voucher' => $red_voucher_amount > 0 ? BudgetWithholdingEnums::IS_SEND_RED_VOUCHER_YES : BudgetWithholdingEnums::IS_SEND_RED_VOUCHER_NO,
                    'red_voucher_amount'  => $red_voucher_amount,
                ];
                $bool = $ordinary_payment->i_update($ordinary_payment_update);
                if ($bool === false) {
                    throw new BusinessException('普通付款申请-关联预提单-无明细行-普通付款并主表记录是否需要红冲预提并且记录红冲金额，失败 = ' . json_encode($ordinary_payment_update, JSON_UNESCAPED_UNICODE) . '; 可能的原因是：' . get_data_object_error_msg($ordinary_payment), ErrCode::$ORDINARY_PAYMENT_CREATE_MAIN_ERROR);
                }
            }
        }

        //普通付款 - 关联预提单 - 变更预提单使用金额、使用状态、预提单-费用明细-按照成本中心变更使用金额
        BudgetWithholdingService::getInstance()->updateAmount($ordinary_payment, $budget_withholding_info);
        return true;
    }
}
