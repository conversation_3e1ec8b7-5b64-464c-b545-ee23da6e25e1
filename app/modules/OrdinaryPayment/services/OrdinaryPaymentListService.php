<?php

namespace App\Modules\OrdinaryPayment\Services;

use App\Library\Enums;
use App\Library\Enums\GlobalEnums;
use App\Library\Enums\BankFlowEnums;
use App\Library\Enums\PayEnums;
use App\Library\Enums\KingDeeEnums;
use App\Library\Enums\OrdinaryPaymentEnums;
use App\Library\Enums\SysConfigEnums;
use App\Library\Enums\BudgetWithholdingEnums;
use App\Library\ErrCode;
use App\Library\Exception\BusinessException;
use App\Library\Validation\ValidationException;
use App\Models\oa\OrdinaryPaymentModel;
use App\Modules\Budget\Models\BudgetObject;
use App\Modules\Budget\Models\BudgetObjectDepartmentAmount;
use App\Modules\Budget\Services\BudgetService;
use App\Modules\Budget\Services\LedgerAccountService;
use App\Modules\Common\Services\EnumsService;
use App\Modules\Hc\Models\HrStaffInfoModel;
use App\Modules\Material\Models\MaterialFinanceCategoryModel;
use App\Modules\OrdinaryPayment\Models\OrdinaryPayment;
use App\Modules\OrdinaryPayment\Models\OrdinaryPaymentDetail;
use App\Modules\OrdinaryPayment\Models\OrdinaryPaymentPersonal;
use App\Modules\Organization\Services\DepartmentService;
use App\Modules\Pay\Services\PayService;
use App\Modules\Purchase\Services\OrderService;
use App\Modules\Setting\Services\CommonDataPermissionService;
use App\Modules\Setting\Services\DataPermissionModuleConfigService;
use App\Modules\User\Models\AttachModel;
use App\Modules\OrdinaryPayment\Models\OrdinaryPaymentExtend;
use App\Modules\Vendor\Services\ListService;
use App\Modules\Workflow\Services\FYRService;
use App\Modules\Workflow\Services\WorkflowServiceV2;
use App\Repository\DepartmentRepository;
use App\Repository\oa\AccountingSubjectsRepository;
use App\Repository\HrStaffRepository;
use App\Repository\oa\BudgetObjectProductRepository;
use App\Repository\oa\BudgetObjectRepository;
use App\Modules\Budget\Services\BaseService as BudgetBaseService;
use App\Repository\oa\BudgetWithholdingRepository;

class OrdinaryPaymentListService extends BaseService
{
    // 列表页 - 搜索条件
    public static $validate_apply_list_search = [
        'apply_no'         => 'StrLenGeLe:12,20|>>>:apply no error',
        'approval_status'  => 'IntIn:1,2,3,4|>>>:apply status error', //申请（审核）状态
        'pay_status'       => 'IntIn:1,2,3|>>>:pay status error',
        'apply_start_date' => 'Date|>>>:apply start date error',
        'apply_end_date'   => 'Date|>>>:apply end date error',
    ];

    // 列表页 - 搜索条件
    public static $validate_other_list_search = [
        'apply_no'            => 'StrLenGeLe:12,20|>>>:apply no error',
        'approval_status'     => 'IntIn:1,2,3,4|>>>:apply status error', //申请（审核）状态
        'pay_status'          => 'IntIn:1,2,3|>>>:pay status error',
        'apply_start_date'    => 'date|>>>:apply start date error',
        'apply_end_date'      => 'date|>>>:apply end date error',
        'apply_id_name'       => 'StrLenGeLe:1,20|>>>:apply staff_id or staff_name  error',//申请人姓名或工号
        'apply_department_id' => 'IntGt:0|>>>:apply department id error',//申请人部门ID
        'pay_date_start'      => 'Date|>>>:pay_date_start error',
        'pay_date_end'        => 'Date|>>>:pay_date_end error',
        'pay_operate_start_date' => 'Date|>>>:pay_operate_start_date error',
        'pay_operate_end_date' => 'Date|>>>:pay_operate_end_date error',
        'pono'                 => 'StrLenGeLe:0,20|>>>:pono error',
    ];

    public static $validate_reply_list = [
        'is_reply'            => 'Required|IntIn:0,1|>>>:is_reply is_reply error'
    ];

    // 数据下载 最大条数
    const DOWNLOAD_LIMIT = 10000;

    private static $instance;

    private function __construct()
    {
    }


    public static function getInstance()
    {
        if (!isset(self::$instance) || !(self::$instance instanceof self)) {
            self::$instance = new self;
        }
        return self::$instance;
    }

    /**
     * 获取列表页搜索条件的支付状态 和 申请状态列表
     *
     * @return array
     */
    public static function getSearchDefault()
    {
        $pay_status_item   = Enums::$payment_pay_status;
        $apply_status_item = Enums::$payment_apply_status;

        $pay_status_item_tmp = [];
        foreach ($pay_status_item as $index => $t_key) {
            $pay_status_item_tmp[] = [
                'id'    => $index,
                'label' => self::$t[$t_key],
            ];
        }

        $apply_status_item_tmp = [];
        foreach ($apply_status_item as $index => $t_key) {
            $apply_status_item_tmp[] = [
                'id'    => $index,
                'label' => self::$t[$t_key],
            ];
        }

        return [
            'apply_status_item' => $apply_status_item_tmp,
            'pay_status_item'   => $pay_status_item_tmp,
        ];
    }

    /**
     * 获取普通付款 分页列表 数据
     *
     * @param array $condition
     * @param array $user
     * @param int $type
     * @return array
     */
    public function getList(array $condition, array $user = [], int $type = 0)
    {
        $page_size = empty($condition['page_size']) ? GlobalEnums::DEFAULT_PAGE_SIZE : $condition['page_size'];
        $page_num  = empty($condition['page']) ? GlobalEnums::DEFAULT_PAGE_NUM : $condition['page'];
        $offset    = $page_size * ($page_num - 1);

        $uid = $user['id'] ?? 0;
        $condition['uid'] = $uid;

        $code    = ErrCode::$SUCCESS;
        $message = 'success';
        $real_message = '';

        $data = [
            'items'      => [],
            'pagination' => [
                'current_page' => $page_num,
                'per_page'     => $page_size,
                'total_count'  => 0,
            ],
        ];

        try {
            if (isset($condition['apply_start_date]']) && isset($condition['apply_end_date]']) && $condition['apply_start_date]'] > $condition['apply_end_date]']) {
                throw new ValidationException('start date or end date error', ErrCode::$VALIDATE_ERROR);
            }

            if (isset($condition['approved_start_date']) && isset($condition['approved_end_date']) && $condition['approved_start_date'] > $condition['approved_end_date']) {
                throw new ValidationException('approved start date or end date error', ErrCode::$VALIDATE_ERROR);
            }

            // 如果是付款支付列表, 且当前用户不在支付权限工号列表, 则返回空
            if ($type == self::LIST_TYPE_PAY) {
                $pay_staff_id = $this->getPayAuthStaffIdItem();
                if (!in_array($uid, $pay_staff_id)) {
                    throw new ValidationException(static::$t->_('no_pay_auth_access_msg'), ErrCode::$ORDINARY_PAYMENT_PAY_AUTH_ERROR);
                }
            }

            $builder = $this->modelsManager->createBuilder();
            $builder->from(['main' => OrdinaryPayment::class]);

            //组合搜索条件
            $builder = $this->getCondition($builder, $condition, $type, $user);
            $count = (int) $builder->columns('COUNT(DISTINCT main.id) AS total')->getQuery()->getSingleResult()->total;

            $items = [];
            if ($count) {
                $columns = [
                    'main.id',
                    'main.apply_no',
                    'main.apply_id',
                    'main.apply_name',
                    'main.apply_node_department_name',
                    'main.created_at',
                    'main.amount_total_have_tax',
                    'main.amount_total_actually',
                    'main.approval_status',
                    'main.pay_status',
                    'main.is_after_ap_th',
                    'main.currency',
                ];

                // 审核模块的已处理列表, 展示处理时间
                if ($type == self::LIST_TYPE_AUDIT && isset($condition['flag']) && $condition['flag'] == GlobalEnums::AUDIT_TAB_PROCESSED) {
                    $columns[] = 'log.audit_at';
                }

                $builder->columns($columns);
                $builder->groupBy('main.id');

                if(!in_array($type, [self::LIST_TYPE_AUDIT, self::LIST_TYPE_CONSULTED_REPLY])) {
                    $builder->orderBy('main.id DESC');
                }

                $builder->limit($page_size, $offset);
                $items = $builder->getQuery()->execute()->toArray();
                $items = $this->handleItems($items,$uid);
            }

            $data['items']                     = $items;
            $data['pagination']['total_count'] = $count;

        } catch (ValidationException $e) {
            $code         = $e->getCode();
            $message      = $e->getMessage();

        } catch (BusinessException $e) {
            // 业务错误（数据错误等），不可对外抛出
            $code         = $e->getCode();
            $message      = static::$t->_('retry_later');
            $real_message = $e->getMessage() . $e->getTraceAsString();

        } catch (\Exception $e) {
            $code         = ErrCode::$SYSTEM_ERROR;
            $message      = static::$t->_('retry_later');
            $real_message = $e->getMessage() . $e->getTraceAsString();
        }

        if (!empty($real_message)) {
            $this->logger->warning('普通付款列表异常信息:' . $real_message);
        }

        // 无权限的情况,返回正常结构的空数据,按成功处理
        if ($code == ErrCode::$ORDINARY_PAYMENT_PAY_AUTH_ERROR) {
            $code    = ErrCode::$SUCCESS;
            $message = 'success';
        }

        return [
            'code'    => $code,
            'message' => $message,
            'data'    => $data,
        ];
    }

    /**
     * 列表复合搜索条件
     *
     * @param $builder
     * @param $condition
     * @param $type
     * @param array $user
     * @return mixed
     * @throws BusinessException
     */
    private function getCondition($builder, $condition, $type, $user = [])
    {
        $apply_no                   = $condition['apply_no'] ?? '';
        $approval_status            = $condition['approval_status'] ?? '';
        $pay_status                 = $condition['pay_status'] ?? '';
        $apply_start_date           = $condition['apply_start_date'] ?? '';
        $apply_end_date             = $condition['apply_end_date'] ?? '';
        $apply_id_name              = $condition['apply_id_name'] ?? '';
        $apply_department_id        = $condition['apply_department_id'] ?? '';
        $approved_start_date        = $condition['approved_start_date'] ?? '';
        $approved_end_date          = $condition['approved_end_date'] ?? '';
        $cost_company               = $condition['cost_company'] ?? [];
        $cost_department_id         = $condition['cost_department_id'] ?? 0;
        $apply_store_id             = $condition['apply_store_id'] ?? 0;
        $supplier_id                = $condition['supplier_id'] ?? '';//供应商筛选
        $pono                       = $condition['pono'] ?? '';
        $budget_is_purchase         = $condition['budget_is_purchase'] ?? 0;        //预算科目是否采购使用1否，2是
        $budget_product_is_purchase = $condition['budget_product_is_purchase'] ?? 0;//预算科目明细是否采购使用1否，2是
        $is_clearance               = $condition['is_clearance'] ?? null; //默认为空

        // 征询回复进度
        $is_reply = $condition['is_reply'] ?? GlobalEnums::CONSULTED_REPLY_STATE_PENDING;
        $is_reply = in_array($is_reply, GlobalEnums::CONSULTED_REPLY_STATE_ITEM) ? $is_reply : GlobalEnums::CONSULTED_REPLY_STATE_PENDING;

        // 审批处理进度
        $flag = $condition['flag'] ?? GlobalEnums::AUDIT_TAB_PENDING;
        $flag = in_array($flag, GlobalEnums::AUDIT_TAB_STATE_ITEM) ? $flag : GlobalEnums::AUDIT_TAB_PENDING;

        /**
         * 付款申请列表
         * 当前登录用户提交的所有申请数据
         */
        if ($type == self::LIST_TYPE_APPLY) {
            $builder->andWhere('main.create_id = :uid:', ['uid' => $condition['uid']]);

        } else if ($type == self::LIST_TYPE_AUDIT) {
            /**
             * 付款审核列表
             * 需要当前登录用户审批或审批处理过的申请数据
             */
            $builder = (new WorkflowServiceV2())->getBizWorkflowOrderListByProcessState($builder, $flag, [Enums::ORDINARY_PAYMENT_BIZ_TYPE], $condition['uid'], 'main');

        } else if ($type == self::LIST_TYPE_PAY) {
            /**
             * 付款支付列表
             * 付款申请表中 审核状态为"已通过"的所有数据
             * 待处理：获取 支付状态为"待支付"的数据
             * 已处理：获取 支付状态为"已支付" 和 "未支付"的数据
             */
            $builder->andWhere('main.approval_status = :approval_status:', ['approval_status' => Enums::PAYMENT_APPLY_STATUS_APPROVAL]);

            // 待处理
            if ($flag == GlobalEnums::AUDIT_TAB_PENDING) {
                $pay_status_item = [
                    Enums::PAYMENT_PAY_STATUS_PENDING,
                ];
                $builder->andWhere('main.is_pay_module = :is_pay_module:', ['is_pay_module' => PayEnums::BIZ_DATA_IS_PAY_MODULE_NO]);
                $builder->inWhere('main.pay_status', $pay_status_item);
            }

            // 已处理
            if ($flag == GlobalEnums::AUDIT_TAB_PROCESSED) {
                $pay_status_item = [
                    Enums::PAYMENT_PAY_STATUS_PAY,
                    Enums::PAYMENT_PAY_STATUS_NOTPAY,
                ];

                $builder->inWhere('main.pay_status', $pay_status_item);
            }

            // 数据权限对接: 支付人仅可取 费用所属公司 是其管辖范围内的
            $permission_dept_ids = DataPermissionModuleConfigService::getInstance()->getDataConfigPermission(SysConfigEnums::SYS_MODULE_ORDINARY_PAYMENT, $user['id']);
            $builder->inWhere('main.cost_company_id', $permission_dept_ids);

        } else if ($type == self::LIST_TYPE_CONSULTED_REPLY) {
            // 意见征询回复列表 v18276: 待回复的单据无需取终审通过 且 待支付的
            $biz_table_info = ['table_alias' => 'main', 'pay_status_field_name' => ''];
            $builder = (new FYRService())->getBizOrderConsultationReplyListByProcessState($builder, $is_reply, [Enums::ORDINARY_PAYMENT_BIZ_TYPE], $condition['uid'], $biz_table_info);

        } else if ($type == self::LIST_TYPE_DATA) {
            // 数据查询 及 导出
            $builder->leftjoin(OrdinaryPaymentDetail::class, 'detail.ordinary_payment_id = main.id', 'detail');
            $builder->leftjoin(OrdinaryPaymentExtend::class, 'main.id = ope.ordinary_payment_id', 'ope');
//            $builder->andWhere('detail.ordinary_payment_id is not null');

            if (!empty($condition['pay_date_start'])) {
                $builder->andWhere('ope.pay_bk_flow_date >= :pay_date_start:', ['pay_date_start' => $condition['pay_date_start'] . ' 0:0:0']);
            }

            if (!empty($condition['pay_date_end'])) {
                $builder->andWhere('ope.pay_bk_flow_date <= :pay_date_end:', ['pay_date_end' => $condition['pay_date_end'] . ' 23:59:59']);
            }

            // 付款分类(预算科目ID)
            if (!empty($condition['budget_id'])) {
                $builder->andWhere('detail.budget_id = :detail_budget_id:', ['detail_budget_id' => $condition['budget_id']]);
            }
            //预算科目是否采购使用1否，2是-18394需求
            if (!empty($budget_is_purchase)) {
                $budget_ids = BudgetObjectRepository::getInstance()->getListByParams(['is_purchase' => $budget_is_purchase], true);
                $builder->inWhere('detail.budget_id', $budget_ids);
            }

            // 费用类型(预算科目明细ID)
            if (!empty($condition['product_id'])) {
                $builder->andWhere('detail.product_id = :detail_product_id:', ['detail_product_id' => $condition['product_id']]);
            }
            //预算科目明细是否采购使用1否，2是-18394需求
            if (!empty($budget_product_is_purchase)) {
                $budget_product_ids = BudgetObjectProductRepository::getInstance()->getListByParams(['is_purchase' => $budget_product_is_purchase], true);
                $builder->inWhere('detail.product_id', $budget_product_ids);
            }

            // 支付操作开始/结束日期搜索
            if (!empty($condition['pay_operate_start_date']) && !empty($condition['pay_operate_end_date'])) {
                $builder->betweenWhere('ope.pay_at', $condition['pay_operate_start_date'] . ' 00:00:00', $condition['pay_operate_end_date'] . ' 23:59:59');
            }


            // 供应商筛选
            if (!empty($supplier_id)) {
                $builder->andWhere('ope.supplier_id = :supplier_id:', ['supplier_id' => $supplier_id]);
            }
            // 采购订单编号
            if (!empty($pono)) {
                $builder->andWhere('detail.pono = :pono:', ['pono' => $pono]);
            }

            //是否清关
            if (!empty($is_clearance)) {
                $builder->inWhere('ope.is_clearance', $is_clearance);
            }

            // 对接通用数据权限
            // 业务表参数
            $table_params = [
                'table_alias_name' => 'main',
                'create_id_field' => 'apply_id',
                'create_node_department_id_filed' => 'cost_department_id',
            ];

            $builder = CommonDataPermissionService::getInstance()->getCommonDataPermission($user, $builder, Enums\SysConfigEnums::SYS_MODULE_ORDINARY_PAYMENT, $table_params);

        }

        //申请编号
        if (!empty($apply_no)) {
            $builder->andWhere('main.apply_no = :apply_no:', ['apply_no' => $apply_no]);
        }

        //申请时间-起始日期
        if (!empty($apply_start_date)) {
            $apply_start_date .= ' 00:00:00';
            $builder->andWhere('main.created_at >= :apply_start_date:', ['apply_start_date' => $apply_start_date]);
        }

        //申请时间-截止日期
        if (!empty($apply_end_date)) {
            $apply_end_date .= ' 23:59:59';
            $builder->andWhere('main.created_at <= :apply_end_date:', ['apply_end_date' => $apply_end_date]);
        }

        //申请时间-起始日期
        if (!empty($approved_start_date)) {
            $approved_start_date .= ' 00:00:00';
            $builder->andWhere('main.approved_at >= :apply_start_date:', ['apply_start_date' => $approved_start_date]);
        }

        //申请时间-截止日期
        if (!empty($approved_end_date)) {
            $approved_end_date .= ' 23:59:59';
            $builder->andWhere('main.approved_at <= :apply_end_date:', ['apply_end_date' => $approved_end_date]);
        }

        //申请状态
        if (!empty($approval_status)) {
            $builder->andWhere('main.approval_status = :approval_status1:', ['approval_status1' => $approval_status]);
        }

        //支付状态
        if (!empty($pay_status)) {
            $builder->andWhere('main.pay_status = :pay_state:', ['pay_state' => $pay_status]);
        }

        //申请人ID或姓名
        if (!empty($apply_id_name)) {
            $builder->andWhere('(main.apply_id LIKE :apply_id: OR main.apply_name LIKE :apply_name:)', ['apply_id' => "$apply_id_name", 'apply_name' => "$apply_id_name%"]);
        }

        //申请人部门
        if (!empty($apply_department_id)) {
            $builder->andWhere('main.apply_node_department_id = :apply_department_id:', ['apply_department_id' => $apply_department_id]);
        }

        // 费用所属公司
        if (!empty($cost_company)) {
            if (is_array($cost_company)) {
                $builder->andWhere('main.cost_company_id IN ({cost_company_id:array})', ['cost_company_id' => array_values($cost_company)]);
            } else {
                $builder->andWhere('main.cost_company_id = :cost_company_id:', ['cost_company_id' => $cost_company]);
            }
        }

        //费用所属部门
        if (!empty($cost_department_id)) {
            $builder->andWhere('main.cost_department_id = :cost_department_id:', ['cost_department_id' => $cost_department_id]);
        }
        //申请人所属网点
        if (!empty($apply_store_id)) {
            //普通付款新增时入库的总部转成了********** 这里也要转一下
            if ($apply_store_id == Enums::HEAD_OFFICE_STORE_FLAG) {
                $apply_store_id = Enums::PAYMENT_HEADER_STORE_ID;
            }
            $builder->andWhere('main.apply_store_id = :apply_store_id:', ['apply_store_id' => $apply_store_id]);
        }
        return $builder;
    }

    /**
     * 列表数据格式处理
     * @param array $items
     * @param $uid
     * @return array
     */
    private function handleItems(array $items, $uid)
    {
        if (empty($items)) {
            return [];
        }

        foreach ($items as &$item) {
            // 币种
            $item['currency_text'] = static::$t[GlobalEnums::$currency_item[$item['currency']]];

            $item['approval_status_text'] = static::$t[Enums::$payment_apply_status[$item['approval_status']]];
            $item['pay_status_text']      = static::$t[Enums::$payment_pay_status[$item['pay_status']]];
            $item['created_at']           = gmdate_customize_by_datetime($item['created_at'], 'Y-m-d');

            $item['is_can_download'] = $this->isCanDownload($item, $uid);
        }

        return $items;
    }

    /**
     * 获取导出数据的总量
     *
     * @param $condition
     * @param $user
     * @return mixed
     */
    public function getDataExportTotal($condition, $user)
    {
        $total_count = 0;
        try {
            $builder = $this->modelsManager->createBuilder();
            $builder->from(['main' => OrdinaryPayment::class]);
            $builder = $this->getCondition($builder, $condition, self::LIST_TYPE_DATA, $user);
            $total_count = (int) $builder->columns('COUNT(main.id) AS t_count')->getQuery()->getSingleResult()->t_count;
        } catch (\Exception $e) {
            $this->logger->error('普通付款-数据查询-获取导出数据的总量异常:' . $e->getMessage());
        }

        return $total_count;
    }

    /**
     * 普通付款数据同步导出
     *
     * @param $condition
     * @param $user
     * @return mixed
     */
    public function getSyncExportData($condition, $user)
    {
        $code = ErrCode::$SUCCESS;
        $message = 'success';
        $data = '';

        try {
            // 同步下载, 最多1w条
            $condition['pageNum'] = GlobalEnums::DEFAULT_PAGE_NUM;
            $condition['pageSize'] = self::DOWNLOAD_LIMIT;

            // 获取数据
            $excel_data = $this->getExportData($condition, $user);

            // 获取表头
            $header = $this->getExportExcelHeaderFields();

            // 生成Excel
            $file_name = "Ordinary_payment_" . date("YmdHis") . '.xlsx';
            $result = $this->exportExcel($header, $excel_data, $file_name);
            if (isset($result['code']) && $result['code'] == ErrCode::$SUCCESS && !empty($result['data'])) {
                $data = $result['data'];
            } else {
                throw new ValidationException(static::$t->_('excel_file_gen_fail'), ErrCode::$VALIDATE_ERROR);
            }

        } catch (ValidationException $e) {
            $code = $e->getCode();
            $message = $e->getMessage();

        } catch (\Exception $e) {
            $code    = ErrCode::$SYSTEM_ERROR;
            $message = $e->getMessage() . $e->getTraceAsString();
            $this->logger->error('普通付款-数据查询-数据同步导出异常:' . $message);
        }

        return [
            'code'    => $code,
            'message' => $message,
            'data'    => $data,
        ];
    }

    /**
     * 获取普通付款-数据
     * @param $condition
     * @param $user
     * @return array
     */
    public function getExportData($condition, $user)
    {
        $export_data = [];

        try {
            $page_num  = empty($condition['pageNum']) ? GlobalEnums::DEFAULT_PAGE_NUM : $condition['pageNum'];
            $page_size = empty($condition['pageSize']) ? GlobalEnums::DEFAULT_PAGE_SIZE : $condition['pageSize'];
            $offset    = $page_size * ($page_num - 1);

            $builder = $this->modelsManager->createBuilder();
            $builder->from(['main' => OrdinaryPayment::class]);
            $builder = $this->getCondition($builder, $condition, self::LIST_TYPE_DATA, $user);

            //导出的基本数据
            $builder->columns([
                'main.id',
                'main.apply_no',
                'main.create_name',
                'main.create_id',
                'main.apply_name',
                'main.apply_id',
                'main.apply_company_name',
                'main.apply_node_department_name',
                'main.apply_store_name',
                'main.cost_store_type',
                'main.cost_department_id',
                'main.cost_department_name',
                'main.payee_type',
                'main.approval_status',
                'main.pay_status',
                'main.created_at',
                'main.currency',
                'main.ticket_date',
                'main.payment_method',
                'main.pay_where',
                'main.ticket_number',
                'main.remark',
                'main.loan_time',
                'main.should_pay_date',
                'main.extra_message',
                'main.voucher_abstract',
                'main.cost_company_id',
                'detail.id as detail_id',
                'detail.voucher_description',
                'detail.wht_category',
                'detail.wht_rate',
                'detail.vat_rate',
                'detail.budget_id',
                'detail.ledger_account_id',
                'detail.product_id',
                'detail.cost_start_date',
                'detail.cost_end_date',
                'detail.amount_no_tax',
                'detail.amount_have_tax',
                'detail.amount_wht',
                'detail.cost_store_name',
                'detail.cost_center_name',
                'detail.amount_vat',
                'detail.deductible_vat_tax',
                'detail.deductible_tax_amount',
                'detail.finance_category_id',
                'detail.pono',
                'detail.cost_department_name as d_cost_department_name',
                'main.amount_discount',
                'main.amount_total_actually',
                'main.factoring_apply_no',
                'ope.pay_bk_flow_date',
                'ope.is_pay',
                'ope.supplier_name',
                'ope.supplier_bk_name',
                'ope.supplier_bk_account',
                'ope.supplier_bk_account_name',
                'ope.sap_supplier_no',
                'ope.supplier_tax_number',
                'ope.supplier_contacts',
                'ope.supplier_tel',
                'ope.supplier_address',
                'ope.supplier_email',
                'ope.pay_at',
                'main.apply_email',
                'main.invoice_no',
                'main.approved_at',
                'ope.pay_bk_flow_date',
                'main.is_supplement_invoice',
                'main.business_type',//业务类型
                'main.amount_total_no_tax',//不含税金额总计
                'main.amount_total_vat',//vat金额总计
                'main.amount_total_wht',//wht金额总计
                'main.amount_total_have_tax',//含税金额总计
                'main.account_type',
                'ope.is_clearance', //是否涉及清关
                'ope.clearance_no', //清关编号
                'ope.actual_clearance_date', //清关日期
            ]);
            $builder->orderBy('main.id DESC');
            $builder->limit($page_size, $offset);
            $export_data_list = $builder->getQuery()->execute()->toArray();
            // 取在最后一个可以修改的财务节点确认需要上传发票开始，之后是否上传附件
            $builder = $this->modelsManager->createBuilder();
            $builder->columns(['distinct op.id']);
            $builder->from(['attach' => AttachModel::class]);
            $builder->leftjoin(OrdinaryPayment::class, 'op.id = attach.oss_bucket_key', 'op');
            $builder->leftjoin(OrdinaryPaymentExtend::class, 'ope.ordinary_payment_id = op.id', 'ope');
            $builder->andWhere('attach.oss_bucket_type = :type: and attach.deleted = :deleted:', ['type' => OrdinaryPaymentEnums::OSS_BUCKET_TYPE_ORDINARY_PAYMENT_ATTACHMENT_FILE, 'deleted' => 0]);
            $builder->andWhere('op.is_supplement_invoice = :is_sup:', ['is_sup' => OrdinaryPaymentEnums::IS_SUPPLEMENT_INVOICE_YES]);
            $builder->andWhere('attach.created_at >= op.supplement_file_change_date');
            $sup_list = $builder->getQuery()->execute()->toArray();
            $sup_list = !empty($sup_list) ? array_unique(array_column($sup_list,'id')) : [];

            //公司列表
            $company_list        = (new DepartmentService())->getCompanyList();
            $company_list_map      = array_column($company_list, 'name', 'id');

            //财务分类
            $finance_category = MaterialFinanceCategoryModel::find(['columns' => 'id, name'])->toArray();
            $finance_category= array_column($finance_category,'name','id');

            //费用类型&付款分类
            $budgetIds =  array_column($export_data_list,'budget_id'); //科目IDs（付款分类）
            $productIds =  array_column($export_data_list,'product_id');//产品IDs(费用类型）
            $budgetService = new BudgetService();
            $budgets = $budgetService->budgetObjectList($budgetIds);
            $products = $budgetService->budgetObjectProductList($productIds);
            $payee_type_config =['1' => 'ordinary_payment_supplier', '2' => 'ordinary_payment_personal'];

            // 核算科目
            $ledger_account_list = LedgerAccountService::getInstance()->getList();
            $ledger_account_list = !empty($ledger_account_list['data']) ? array_column($ledger_account_list['data'], 'name', 'id') : [];
            // 会计科目
            $account_subjects_list = AccountingSubjectsRepository::getInstance(static::$language)->getListByIds();

            //付款条款
            $loan_time_data = OrderService::getInstance()->getEnvByCode('purchase_order_loan_time');
            $loan_time_map = array_column($loan_time_data,'description', 'id');

            // wht类别
            $wht_cat_map = EnumsService::getInstance()->getWhtRateCategoryMap(0);

            // 费用所属部门id 与 一级部门名称的映射关系
            $department_and_first_level_name_map = self::getDepartmentAndFirstLevelDepartmentMap();
            //处理amount 相关只展示一次数据处理
            $amountSpecialMap = [];
            $amountSpecialOPIdsMap = array_fill_keys(array_column($export_data_list,'id'),true);
            // 导出数据分段处理
            $export_data_list = array_chunk($export_data_list, 5000);
            foreach ($export_data_list as $chunk_list) {
                // 普通付款单据在支付模块的支付状态
                $pay_status_map_list = PayService::getInstance()->batchGetOrdersPayStatusMap(array_column($chunk_list, 'apply_no'), BankFlowEnums::BANK_FLOW_OA_TYPE_ORDINARY_PAYMENT);

                foreach ($chunk_list as $key => $data) {
                    // 付款分类
                    $lang = strtolower(substr(self::$language, -2));
                    $budget_name  = isset($budgets[$data['budget_id']]) ? $budgets[$data['budget_id']]['name_' . $lang] : '-';
                    $product_name = isset($products[$data['product_id']]) ? $products[$data['product_id']]['name_' . $lang] : '-';

                    $pay_bk_flow_date = '';
                    if ($data['is_pay'] == Enums::ORDINARY_PAYMENT_STATUS_PAY) {
                        $pay_bk_flow_date = $data['pay_bk_flow_date'];
                    }
                    if (!isset($amountSpecialMap[$data['id']]) && isset($amountSpecialOPIdsMap[$data['id']])) {
                        $amountSpecialMap[$data['id']] = [
                            'amount_total_no_tax'   => $data['amount_total_no_tax'],  //不含税金额总计
                            'amount_total_vat'      => $data['amount_total_vat'],     //vat总计
                            'amount_total_wht'      => $data['amount_total_wht'],     //wht总计
                            'amount_total_have_tax' => $data['amount_total_have_tax'],//含税金额总计
                            'amount_discount'       => $data['amount_discount'],      //折扣
                            'amount_total_actually' => $data['amount_total_actually'],//实付金额总计
                        ];
                    }

                    $_row_data = [
                        // 普通付款基本信息
                        $data['apply_no'], // 编号
                        $data['create_name'],//发起人姓名
                        $data['create_id'], //发起人工号
                        $data['apply_name'],//申请人姓名
                        $data['apply_id'], // 申请人工号
                        $data['apply_company_name'], // 申请人公司
                        $data['apply_node_department_name'], // 申请人部门
                        $data['apply_store_name'],//申请人所属网点名字
                        $data['cost_department_name'], //费用所属部门
                        $department_and_first_level_name_map[$data['cost_department_id']] ?? '', //费用所属一级部门
                        $company_list_map[$data['cost_company_id']] ?? '',//费用所属公司
                        static::$t->_(GlobalEnums::$business_type_item[$data['business_type']]),//业务类型
                        date('Y-m-d', strtotime($data['created_at'])),//申请日期
                        static::$t->_(GlobalEnums::$currency_item[$data['currency']]),//币种
                        $data['ticket_date'],//发票日期
                        static::$t->_(Enums::$payment_method[$data['payment_method']]),//付款方式
                        $data['pay_where'] == PayEnums::PAY_WHERE_IN ? static::$t->_('pay_where.1') : static::$t->_('pay_where.2'),//境内外支付
                        $data['ticket_number'],//发票号码
                        $data['remark'],//备注
                        $loan_time_map[$data['loan_time']] ?? '',//付款条款
                        $data['should_pay_date'],//应付日期

                        'amount_total_no_tax'   => 0,  //不含税金额总计
                        'amount_total_vat'      => 0,     //vat总计
                        'amount_total_wht'      => 0,     //wht总计
                        'amount_total_have_tax' => 0,//含税金额总计
                        'amount_discount'       => 0,      //折扣
                        'amount_total_actually' => 0,//实付金额总计

                        $data['extra_message'],//额外参考信息
                        $data['voucher_abstract'], //凭证摘要
                        $pay_bk_flow_date,//银行流水日期
                        $data['detail_id'],//普通付款行id
                        $data['cost_store_type'] == 2 ? $data['cost_store_name'] : $data['d_cost_department_name'], //1 :总部,2:网点;费用所属网点-网点，费用属部门名称-总部
                        $data['cost_center_name'], //费用所属中心
                        $budget_name, //付款分类
                        $product_name, //费用类型
                        $finance_category[$data['finance_category_id']] ?? '',//财务分类
                        ($data['account_type'] == KingDeeEnums::ACCOUNT_TYPE_FLASH_EXPRESS_COMPANY) ? ($ledger_account_list[$data['ledger_account_id']] ?? '') : ($account_subjects_list[$data['ledger_account_id']]['subjects_name'] ?? ''), // 核算科目/会计科目
                        $data['voucher_description'],//凭证描述
                        $data['cost_start_date'] . ' - ' . $data['cost_end_date'], //费用发生期间
                        $data['amount_no_tax'], //不含税金额
                        $data['amount_have_tax'], //含税金额
                        $wht_cat_map[$data['wht_category']] ?? 0,//wht 类别
                        $data['wht_rate'] . '%',//wht税率
                        $data['amount_wht'], //WHT金额
                        $data['vat_rate'] . '%',//vat税率
                        $data['amount_vat'], //VAT金额
                        bcdiv($data['deductible_tax_amount'], 100, 2),//可抵扣税额
                        $data['deductible_vat_tax'] == '' ? '' : $data['deductible_vat_tax'] . '%', //可抵扣VAT税率
                        static::$t->_($payee_type_config[$data['payee_type']]), // 收款人类型
                        static::$t->_(Enums::$ordinary_payment_approval_status[$data['approval_status']]),// 申请状态
                        static::$t->_(Enums::$ordinary_payment_pay_status[$data['pay_status']]),// 支付状态
                        $data['pay_at'],//支付操作日期
                    ];

                    // 是否上传补充附件
                    if ($data['is_supplement_invoice'] == OrdinaryPaymentEnums::IS_SUPPLEMENT_INVOICE_YES) {
                        if (in_array($data['id'], $sup_list)) {
                            $sup_status = OrdinaryPaymentEnums::$supplement_invoice_status[OrdinaryPaymentEnums::IS_SUPPLEMENT_INVOICE_YES];
                        } else {
                            $sup_status = OrdinaryPaymentEnums::$supplement_invoice_status[OrdinaryPaymentEnums::IS_SUPPLEMENT_INVOICE_NO];
                        }
                    } else {
                        $sup_status = '';
                    }

                    // 是否添加附件
                    $is_supplement_invoice = empty($data['is_supplement_invoice']) ? '' : (OrdinaryPaymentEnums::IS_SUPPLEMENT_INVOICE_YES == $data['is_supplement_invoice'] ? static::$t->_('view_yes') : static::$t->_('view_no'));

                    // 供应商
                    if (Enums::PAYEE_TYPE_VENDOR == $data['payee_type']) {
                        if (isset($amountSpecialMap[$data['id']], $amountSpecialOPIdsMap[$data['id']])) {
                            $supplierData                          = $_row_data;
                            $supplierData['amount_total_no_tax']   = $amountSpecialMap[$data['id']]['amount_total_no_tax'];
                            $supplierData['amount_total_vat']      = $amountSpecialMap[$data['id']]['amount_total_vat'];
                            $supplierData['amount_total_wht']      = $amountSpecialMap[$data['id']]['amount_total_wht'];
                            $supplierData['amount_total_have_tax'] = $amountSpecialMap[$data['id']]['amount_total_have_tax'];
                            $supplierData['amount_discount']       = $amountSpecialMap[$data['id']]['amount_discount'];
                            $supplierData['amount_total_actually'] = $amountSpecialMap[$data['id']]['amount_total_actually'];
                            unset($amountSpecialOPIdsMap[$data['id']]);
                            unset($amountSpecialMap[$data['id']]);
                        }
                        $export_data[] = array_merge(array_values($supplierData??$_row_data), [
                            '',
                            '',
                            '',
                            '',
                            '',
                            $data['pono'],
                            $data['factoring_apply_no'],
                            $data['supplier_name'],
                            static::$t->_(OrdinaryPaymentEnums::$clearance_state[$data['is_clearance']]), //是否涉及清关
                            $data['clearance_no'], //清关编号
                            !empty($data['actual_clearance_date'])
                                ? date('Y-m-d', strtotime($data['actual_clearance_date']))
                                : '', //清关日期
                            $data['sap_supplier_no'],
                            $data['supplier_bk_name'],
                            $data['supplier_bk_account'],
                            $data['supplier_bk_account_name'],
                            $data['supplier_tax_number'],
                            $data['supplier_contacts'],
                            $data['supplier_tel'],
                            $data['supplier_address'],
                            $data['supplier_email'],

                            $data['apply_email'],
                            $data['invoice_no'],
                            $data['approved_at'],
                            $data['pay_bk_flow_date'],
                            $is_supplement_invoice,
                            !empty($sup_status) ? static::$t->_($sup_status) : '',

                            // 普通付款单据在支付模块的支付状态
                            $pay_status_map_list[$data['apply_no']] ?? ''
                        ]);
                        if (isset($supplierData)) {
                            unset($supplierData);
                        }

                        // 个人
                    } else if (Enums::PAYEE_TYPE_PERSONAL == $data['payee_type']) {
                        // TODO 老代码, 后续需优化
                        $personal_data = OrdinaryPaymentPersonal::find([
                            'conditions' => 'ordinary_payment_id = :ordinary_payment_id:',
                            'bind'       => ['ordinary_payment_id' => $data['id']],
                        ])->toArray();

                        foreach ($personal_data as $personal_item) {
                            if (isset($amountSpecialMap[$data['id']], $amountSpecialOPIdsMap[$data['id']])) {
                                $individualData                          = $_row_data;
                                $individualData['amount_total_no_tax']   = $amountSpecialMap[$data['id']]['amount_total_no_tax'];
                                $individualData['amount_total_vat']      = $amountSpecialMap[$data['id']]['amount_total_vat'];
                                $individualData['amount_total_wht']      = $amountSpecialMap[$data['id']]['amount_total_wht'];
                                $individualData['amount_total_have_tax'] = $amountSpecialMap[$data['id']]['amount_total_have_tax'];
                                $individualData['amount_discount']       = $amountSpecialMap[$data['id']]['amount_discount'];
                                $individualData['amount_total_actually'] = $amountSpecialMap[$data['id']]['amount_total_actually'];
                                unset($amountSpecialOPIdsMap[$data['id']]);
                                unset($amountSpecialMap[$data['id']]);
                            }
                            $export_data[] = array_merge(array_values($individualData ?? $_row_data), [
                                $personal_item['staff_info_id'],
                                $personal_item['bank_name'],
                                $personal_item['bank_no_name'],
                                $personal_item['bank_no'],
                                $personal_item['amount'],
                                $data['pono'],
                                '',
                                '',
                                '',
                                '',
                                '',
                                '',
                                '',
                                '',
                                '',
                                '',
                                '',
                                '',
                                '',
                                '',
                                $data['apply_email'],
                                $data['invoice_no'],
                                $data['approved_at'],
                                $data['pay_bk_flow_date'],
                                $is_supplement_invoice,
                                !empty($sup_status) ? static::$t->_($sup_status) : '',

                                // 普通付款单据在支付模块的支付状态
                                $pay_status_map_list[$data['apply_no']] ?? ''
                            ]);
                            if (isset($individualData)) {
                                unset($individualData);
                            }
                        }
                    }
                }
            }

        } catch (\Exception $e) {
            $this->logger->warning('普通付款-数据查询-导出数据获取异常: ' . $e->getMessage() . $e->getTraceAsString());
        }

        return $export_data;
    }

    /**
     * 获取普通付款导出的Excel表头
     */
    public function getExportExcelHeaderFields()
    {
        return [
            static::$t->_('global.number'), // 编号
            static::$t->_('re_field_created_name'), // 发起人姓名
            static::$t->_('re_field_created_id'), // 发起人工号
            static::$t->_('global.applicant.name'), // 申请人姓名
            static::$t->_('global.applicant.id'), // 申请人工号
            static::$t->_('payment_store_renting_apply_company'),   // 申请人公司
            static::$t->_('re_field_apply_department_name'), // 申请人部门
            static::$t->_('re_field_apply_store_name'), // 申请人所属网点
            static::$t->_('re_filed_apply_cost_department'), // 费用所属部门
            static::$t->_('re_filed_apply_cost_first_department'), // 费用所属一级部门

            //费用所属公司 业务类型 申请日期 币种 发票日期 付款方式 境内/境外支付 发票号 备注  付款条款 应付日期
            static::$t->_('re_field_cost_company_name'),
            static::$t->_('re_filed_business_type'), // 业务类型
            static::$t->_('re_filed_apply_date'),
            static::$t->_('re_filed_currency_text'),
            static::$t->_('re_filed_ticket_date'),
            static::$t->_('re_filed_payment_method'),
            static::$t->_('re_filed_pay_where'),
            static::$t->_('re_filed_ticket_number'),
            static::$t->_('re_filed_remark'),
            static::$t->_('re_filed_loan_time'),
            static::$t->_('re_filed_should_pay_date'),

            static::$t->_('ordinarypayment.amount_total_no_tax'), // 不含税金额总计
            static::$t->_('ordinarypayment.amount_total_vat'), // vat金额总计
            static::$t->_('ordinarypayment.amount_total_wht'), // wht金额总计
            static::$t->_('ordinarypayment.amount_total_have_tax'), // 含税金额总计
            static::$t->_('purchase.discount'), // 折扣
            static::$t->_('total.amount.actually.paid'), // 实付金额总计

            static::$t->_('re_filed_extra_message'),//额外参考信息 凭证摘要extra_message
            static::$t->_('re_filed_voucher_abstract'),
            static::$t->_('ordinarypayment.bankjournaldate'), // 银行流水日期
            static::$t->_('ordinarypayment.detail_id'),//普通付款行id
            static::$t->_('re_filed_apply_cost_store'), // 费用所属网点
            static::$t->_('re_filed_apply_cost_center'), // 费用所属中心
            static::$t->_('cost_category'), // 付款分类
            static::$t->_('payment_store_renting_cost_type'), // 费用类型
            static::$t->_('ordinary_payment_finance_category_id'), // 财务分类
            static::$t->_('purchase_apply_ledger_account'), // 核算科目
            static::$t->_('re_filed_voucher_description'),//凭证描述

            static::$t->_('cost_date_between'), // 费用发生期间
            static::$t->_('purchase_order_product_field_total_price'), // 不含税金额
            static::$t->_('purchase_order_product_field_all_total'), // 含税金额
            static::$t->_('re_filed_payment_wht_category'), //wht 类别
            static::$t->_('re_filed_payment_wht_rate'), //wht 税率

            static::$t->_('wages_wht_amount'), // WHT含税金额
            static::$t->_('re_filed_payment_vat_rate'), //vat 税率

            static::$t->_('vat_amount'), // VAT金额
            static::$t->_('re_field_deductible_tax_amount'), // 可抵扣税额
            static::$t->_('re_field_deductible_vat_tax'), // 可抵扣VAT税率
            static::$t->_('ordinary_payment_type'), // 收款人类型
            static::$t->_('ordinary_payment_approval_status'),// 申请状态
            static::$t->_('ordinary_payment_pay_status'), // 支付状态
            static::$t->_('re_field_pay_operate_date'), // 支付操作日期
            static::$t->_('ordinary_payment_number'), // 收款人工号
            static::$t->_('ordinary_payment_bank_name'), // 银行名称
            static::$t->_('ordinary_payment_account_name'), // 银行账户名称
            static::$t->_('ordinary_payment_account_number'), // 银行账号
            static::$t->_('ordinary_payment_amount'), // 金额
            static::$t->_('ordinary_payment_pono'), // 采购订单编号
            static::$t->_('ordinary_payment_factoring_apply_no'), // 关联保理付款单号
            static::$t->_('ordinary_payment_supplier_name'), // 供应商名称
            static::$t->_('ordinary_payment_is_clearance'), // 是否涉及清关
            static::$t->_('ordinary_payment_clearance_no'), // 清关编号
            static::$t->_('ordinary_payment_clearance_date'), // 清关日期
            static::$t->_('ordinary_payment_sap_supplier_no'), //sap 供应商编号

            static::$t->_('ordinary_payment_bank_name'), // 银行名称
            static::$t->_('ordinary_payment_bank_account_number'), // 银行账户号
            static::$t->_('ordinary_payment_account_name'), // 银行账户名称
            static::$t->_('ordinary_payment_supplier_tax_number'), // 供应商税务号
            static::$t->_('ordinary_payment_supplier_contacts'), // 供应商联系人
            static::$t->_('ordinary_payment_supplier_tel'), // 供应商联系方式
            static::$t->_('ordinary_payment_supplier_address'), // 供应商地址
            static::$t->_('ordinary_payment_supplier_email'), // 供应商邮箱

            static::$t->_('ordinary_payment_apply_email'), // 申请人邮箱
            static::$t->_('ordinary_payment_invoice_no'), // 增值税发票
            static::$t->_('ordinary_payment_approve_at'), // 审批通过日期
            static::$t->_('ordinary_payment_pay_at'), // 银行流水日期
            static::$t->_('ordinary_payment_is_supplement_invoice'), // 是否需要补充发票
            static::$t->_('ordinary_payment_supplement_invoice_status'), // 补充附件状态
            static::$t->_('pay_module_pay_status'), // 支付模块支付状态
        ];
    }

    /**
     * 获取待支付的数量
     * @param int $user_id
     * @return int
     */
    public function getWaitingPayCount(int $user_id)
    {
        if (empty($user_id)) {
            return 0;
        }

        $pay_staff_ids = $this->getPayAuthStaffIdItem();
        if (!in_array($user_id, $pay_staff_ids)) {
            return 0;
        }

        // 数据权限对接: 支付待办 仅可取 费用所属公司 是 支付人 管辖范围内的
        $dept_ids = DataPermissionModuleConfigService::getInstance()->getDataConfigPermission(SysConfigEnums::SYS_MODULE_ORDINARY_PAYMENT, $user_id);
        if (empty($dept_ids)) {
            return 0;
        }

        return OrdinaryPayment::count([
            'conditions' => 'cost_company_id IN ({dept_ids:array}) AND approval_status = :approval_status: AND pay_status = :pay_status: AND is_pay_module = :is_pay_module:',
            'bind'       => [
                'dept_ids' => $dept_ids,
                'approval_status' => Enums::PAYMENT_APPLY_STATUS_APPROVAL,
                'pay_status' => Enums::PAYMENT_PAY_STATUS_PENDING,
                'is_pay_module' => PayEnums::BIZ_DATA_IS_PAY_MODULE_NO
            ],
        ]);
    }

    /**
     * 获取指定时间段上传过附件的单号
     * @param array $condition
     * @return array
     */
    public function getAttachmentNoList($condition = []){
        $start = $condition['start'] ?? '';
        $end = $condition['end'] ?? '';

        $builder = $this->modelsManager->createBuilder();
        $builder->columns([
            'distinct op.apply_no'
        ]);
        $builder->from(['attach' => AttachModel::class]);
        $builder->leftjoin(OrdinaryPayment::class, 'op.id = attach.oss_bucket_key', 'op');

        //组合搜索条件
        $builder->andWhere('attach.oss_bucket_type = :type: and attach.deleted = :deleted:',
            ['type' => OrdinaryPaymentEnums::OSS_BUCKET_TYPE_ORDINARY_PAYMENT_ATTACHMENT_FILE,'deleted' => 0]);
        $builder->andWhere('op.approval_status in({status:array}) and op.pay_status in({pay_status:array})',
            ['status' => [Enums::CONTRACT_STATUS_PENDING,Enums::CONTRACT_STATUS_APPROVAL],
                'pay_status' => [Enums::PAYMENT_PAY_STATUS_PENDING,Enums::PAYMENT_PAY_STATUS_PAY]]);
        if (!empty($start)) {
            $builder->andWhere('attach.created_at >= :start:',
                ['start' => $start]);
        }
        if (!empty($end)) {
            $builder->andWhere('attach.created_at <= :end:',
                ['end' => $end]);
        }

        $list = $builder->getQuery()->execute()->toArray();

        return !empty($list) ? array_unique(array_column($list,'apply_no')) : [];
    }

    /**
     * 获取支付截止时间超过7天没有补充附件的单号
     * @param array $condition
     * @return array
     */
    public function getSupplementAttachments($condition = []){
        $pay_at = $condition['pay_at'] ?? null;

        $builder = $this->modelsManager->createBuilder();
        $builder->columns([
            'distinct op.apply_no, op.create_id, op.apply_email, op.supplement_file_change_date, attach.created_at'
        ]);
        $builder->from(['op' => OrdinaryPayment::class]);
        $builder->leftjoin(OrdinaryPaymentExtend::class, 'ope.ordinary_payment_id = op.id', 'ope');
        $builder->leftjoin(AttachModel::class, 'op.id = attach.oss_bucket_key and attach.oss_bucket_type = 31', 'attach');

        // 组合搜索条件
        $builder->andWhere('op.approval_status = :status: and op.pay_status = :pay_status: and op.is_supplement_invoice = :is_sup:',
            ['status' => Enums::CONTRACT_STATUS_APPROVAL,'pay_status' => Enums::PAYMENT_PAY_STATUS_PAY,
                'is_sup' => OrdinaryPaymentEnums::IS_SUPPLEMENT_INVOICE_YES]);
        $builder->andWhere('attach.created_at is null');

        // 支付时间超过7天
        if (!empty($pay_at)) {
            $builder->andWhere('ope.pay_at < :pat_at:', ['pat_at' => $pay_at]);
        }
        $list = $builder->getQuery()->execute()->toArray();

        $res = $emails = [];
        $id_list = !empty($list) ? array_values(array_unique(array_filter(array_column($list,'create_id')))) : [];
        // 申请邮箱
        if (!empty($id_list)) {
            $emails = HrStaffInfoModel::find([
                'columns' => 'staff_info_id, email',
                'conditions' => 'staff_info_id in({ids:array})',
                'bind'       => [
                    'ids' => $id_list,
                ]
            ])->toArray();
        }
        foreach ($list as $item) {
            $res[$item['create_id']][] = $item['apply_no'];
        }

        return [
            'no_list' => $res,
            'email_list' => !empty($emails) ? array_column($emails,'email','staff_info_id') : []
        ];
    }

    /**
     * 普通付款-数据导出-含附件
     * 说明: 给财务人员用
     *
     * @param $condition
     * @return array
     */
    public function dataExportAllByTask($condition)
    {
        parent::setLanguage($condition['lang'] ?? 'zh-CN');

        try {
            $builder = $this->modelsManager->createBuilder();
            $builder->from(['main' => OrdinaryPayment::class]);
            $builder->leftjoin(OrdinaryPaymentDetail::class, 'detail.ordinary_payment_id=main.id', 'detail');
            $builder->leftjoin(OrdinaryPaymentExtend::class, 'main.id=ope.ordinary_payment_id', 'ope');
            $builder->where("detail.ordinary_payment_id is not null");

            // 指定科目
            if (!empty($condition['budget_ids'])) {
                $builder->inWhere('detail.budget_id', $condition['budget_ids']);
            }

            $builder = $this->getCondition($builder, $condition, 0);
            $count_info = $builder->columns('COUNT(main.id) AS t_count')->getQuery()->getSingleResult();
            $count = $count_info->t_count ?? 0;
            if (empty($count)) {
                throw new ValidationException('data empty');
            }

            // 最大条数限制
            if ($count > self::DOWNLOAD_LIMIT) {
                throw new ValidationException('Exceeded maximum number limit '. self::DOWNLOAD_LIMIT .', please change your filter conditions');
            }

            //导出的基本数据
            $builder->columns([
                'main.id',
                'main.apply_no',
                'main.create_name',
                'main.create_id',
                'main.apply_name',
                'main.apply_id',
                'main.apply_company_name',
                'main.apply_node_department_name',
                'main.cost_department_id',
                'main.cost_department_name',
                'main.payee_type',
                'main.approval_status',
                'main.pay_status',
                'main.created_at',
                'main.currency',
                'main.ticket_date',
                'main.payment_method',
                'main.pay_where',
                'main.ticket_number',
                'main.remark',
                'main.loan_time',
                'main.should_pay_date',
                'main.extra_message',
                'main.voucher_abstract',
                'main.cost_company_id',
                'detail.id AS detail_id',
                'detail.voucher_description',
                'detail.wht_category',
                'detail.wht_rate',
                'detail.vat_rate',
                'detail.budget_id',
                'detail.ledger_account_id',
                'detail.product_id',
                'detail.cost_start_date',
                'detail.cost_end_date',
                'detail.amount_no_tax',
                'detail.amount_have_tax',
                'detail.amount_wht',
                'detail.cost_store_name',
                'detail.cost_center_name',
                'detail.amount_vat',
                'detail.deductible_vat_tax',
                'detail.deductible_tax_amount',
                'main.amount_discount',
                'main.amount_total_actually',
                'ope.pay_bk_flow_date',
                'ope.is_pay',
                'ope.supplier_name',
                'ope.supplier_bk_name',
                'ope.supplier_bk_account',
                'ope.supplier_bk_account_name',
                'ope.sap_supplier_no',
                'ope.supplier_tax_number',
                'ope.supplier_contacts',
                'ope.supplier_tel',
                'ope.supplier_address',
                'ope.supplier_email',
                'main.apply_email',
                'main.invoice_no',
                'main.approved_at',
                'ope.pay_bk_flow_date',
                'main.is_supplement_invoice',
                'main.business_type',//业务类型
                'main.amount_total_no_tax',//不含税金额总计
                'main.amount_total_vat',//vat金额总计
                'main.amount_total_wht',//wht金额总计
                'main.amount_total_have_tax',//含税金额总计
            ]);
            $builder->orderBy('main.id DESC');
            $export_data_list = $builder->getQuery()->execute()->toArray();

            // 取在最后一个可以修改的财务节点确认需要上传发票开始，之后是否上传附件
            $builder = $this->modelsManager->createBuilder();
            $builder->columns([
                'distinct op.id'
            ]);
            $builder->from(['attach' => AttachModel::class]);
            $builder->leftjoin(OrdinaryPayment::class, 'op.id = attach.oss_bucket_key', 'op');
            $builder->leftjoin(OrdinaryPaymentExtend::class, 'ope.ordinary_payment_id = op.id', 'ope');
            $builder->andWhere('attach.oss_bucket_type = :type: and attach.deleted = :deleted:',
                ['type' => OrdinaryPaymentEnums::OSS_BUCKET_TYPE_ORDINARY_PAYMENT_ATTACHMENT_FILE,'deleted' => 0]);
            $builder->andWhere('op.is_supplement_invoice = :is_sup:',
                ['is_sup' => OrdinaryPaymentEnums::IS_SUPPLEMENT_INVOICE_YES]);
            $builder->andWhere('attach.created_at >= op.supplement_file_change_date');
            $sup_list = $builder->getQuery()->execute()->toArray();
            $sup_list = !empty($sup_list) ? array_unique(array_column($sup_list,'id')) : [];

            // 公司列表
            $company_list        = (new DepartmentService())->getCompanyList();
            $company_list_map      = array_column($company_list, 'name', 'id');

            //费用类型&付款分类
            $budgetIds =  array_column($export_data_list,'budget_id'); //科目IDs（付款分类）
            $productIds =  array_column($export_data_list,'product_id');//产品IDs(费用类型）
            $budgetService = new BudgetService();
            $budgets = $budgetService->budgetObjectList($budgetIds);
            $products = $budgetService->budgetObjectProductList($productIds);
            $payee_type_config =["1"=>'ordinary_payment_supplier','2'=>'ordinary_payment_personal'];
            //核算科目名字
            $ledgerIdToName = [] ;
            $ledger_res = LedgerAccountService::getInstance()->getList();
            if ($ledger_res['code'] == ErrCode::$SUCCESS) {
                $ledgerIdToName = array_column($ledger_res['data'],'name','id');
            }

            // 获取主数据费用信息 - 附件
            $main_ids = array_values(array_unique(array_column($export_data_list, 'id')));
            $main_attachment = [];
            $main_append_attachment = [];
            if (!empty($main_ids)) {
                $main_attachment = AttachModel::find([
                    'conditions' => 'oss_bucket_type = :oss_bucket_type: AND oss_bucket_key IN ({keys:array}) AND deleted=0',
                    'bind' => [
                        'oss_bucket_type' => Enums::OSS_BUCKET_TYPE_ORDINARYPAYMENT_MAIN,
                        'keys' => $main_ids
                    ]
                ])->toArray();
                // 合并多个附件
                $main_attachment = merge_attachments($main_attachment);

                // 获取主数据费用信息 - 补充附件
                $main_append_attachment = AttachModel::find([
                    'conditions' => 'oss_bucket_type = :oss_bucket_type: AND oss_bucket_key IN ({keys:array}) AND deleted=0',
                    'bind' => [
                        'oss_bucket_type' => Enums\OrdinaryPaymentEnums::OSS_BUCKET_TYPE_ORDINARY_PAYMENT_ATTACHMENT_FILE,
                        'keys' => $main_ids
                    ]
                ])->toArray();
                // 合并多个附件
                $main_append_attachment = merge_attachments($main_append_attachment);
            }

            // 获取详情数据 - 附件
            $detail_ids = array_values(array_unique(array_column($export_data_list, 'detail_id')));
            $detail_attachment = [];
            if (!empty($detail_ids)) {
                $detail_attachment = AttachModel::find([
                    'conditions' => 'oss_bucket_type = :oss_bucket_type: AND oss_bucket_key IN ({keys:array}) AND deleted=0',
                    'bind' => [
                        'oss_bucket_type' => Enums::OSS_BUCKET_TYPE_ORDINARYPAYMENT,
                        'keys' => $detail_ids
                    ]
                ])->toArray();
                $detail_attachment = merge_attachments($detail_attachment);
            }
            //付款条款
            $loan_time_data = OrderService::getInstance()->getEnvByCode('purchase_order_loan_time');
            $loan_time_map = array_column($loan_time_data,'description','id');

            $wht_cat_map = EnumsService::getInstance()->getWhtRateCategoryMap(0);

            // 费用所属部门id 与 一级部门名称的映射关系
            $department_and_first_level_name_map = self::getDepartmentAndFirstLevelDepartmentMap();

            // Excel列表数据
            $row_values = [];

            // 导出数据分段处理
            $export_data_list = array_chunk($export_data_list, 5000);
            foreach ($export_data_list as $chunk_list) {
                // 普通付款单据在支付模块的支付状态
                $pay_status_map_list = PayService::getInstance()->batchGetOrdersPayStatusMap(array_column($chunk_list, 'apply_no'), BankFlowEnums::BANK_FLOW_OA_TYPE_ORDINARY_PAYMENT);

                foreach ($chunk_list as $key => $data) {
                    // 付款分类
                    $lang = strtolower(substr(self::$language, -2));
                    $budget_name  = isset($budgets[$data['budget_id']]) ? $budgets[$data['budget_id']]['name_' . $lang] : '-';
                    $product_name = isset($products[$data['product_id']]) ? $products[$data['product_id']]['name_' . $lang] : '-';
                    $pay_bk_flow_date = '';
                    if ($data['is_pay'] == Enums::ORDINARY_PAYMENT_STATUS_PAY) {
                        $pay_bk_flow_date = $data['pay_bk_flow_date'];
                    }

                    // 普通付款基本信息
                    $row_value = [
                        $data['id'], // 主数据ID
                        $data['apply_no'], // 编号
                        $data['create_name'],//发起人姓名
                        $data['create_id'], //发起人工号
                        $data['apply_name'],//申请人姓名
                        $data['apply_id'], // 申请人工号
                        $data['apply_company_name'], // 申请人公司
                        $data['apply_node_department_name'], // 申请人部门
                        $data['cost_department_name'], //费用所属部门
                        $department_and_first_level_name_map[$data['cost_department_id']] ?? '', //费用所属一级部门
                        $company_list_map[$data['cost_company_id']] ?? '',//费用所属公司
                        self::$t->_(GlobalEnums::$business_type_item[$data['business_type']]),//业务类型
                        date('Y-m-d', strtotime($data['created_at'])),//申请日期
                        self::$t->_(GlobalEnums::$currency_item[$data['currency']]),//币种
                        $data['ticket_date'],//发票日期
                        self::$t->_(Enums::$payment_method[$data['payment_method']]),//付款方式
                        ($data['pay_where'] == PayEnums::PAY_WHERE_IN) ? static::$t->_('pay_where.1') : static::$t->_('pay_where.2'),//境内外支付
                        $data['ticket_number'],//发票号码
                        $data['remark'],//备注
                        $loan_time_map[$data['loan_time']] ?? '',//付款条款
                        $data['should_pay_date'],//应付日期

                        $data['amount_total_no_tax'],//不含税金额总计
                        $data['amount_total_vat'],//vat总计
                        $data['amount_total_wht'],//wht总计
                        $data['amount_total_have_tax'],//含税金额总计
                        $data['amount_discount'],//折扣
                        $data['amount_total_actually'],//实付金额总计
                        $data['extra_message'],//额外参考信息
                        $data['voucher_abstract'], //凭证摘要
                        $pay_bk_flow_date,//银行流水日期
                        $data['cost_store_name'], //费用所属网点
                        $data['cost_center_name'], //费用所属中心
                        $budget_name, //付款分类
                        $product_name, //费用类型
                        $ledgerIdToName[$data['ledger_account_id']] ?? '',
                        $data['voucher_description'],//凭证描述
                        $data['cost_start_date'] . ' - ' . $data['cost_end_date'], //费用发生期间
                        $data['amount_no_tax'], //不含税金额
                        $data['amount_have_tax'], //含税金额
                        $wht_cat_map[$data['wht_category']] ?? 0,//wht 类别
                        $data['wht_rate'] . '%',//wht税率
                        $data['amount_wht'], //WHT金额
                        $data['vat_rate'] . '%',//vat税率
                        $data['amount_vat'], //VAT金额
                        bcdiv($data['deductible_tax_amount'], 100, 2),//可抵扣税额
                        $data['deductible_vat_tax'] == '' ? '' : $data['deductible_vat_tax'] . '%', //可抵扣VAT税率
                        static::$t->_($payee_type_config[$data['payee_type']]), // 收款人类型
                        static::$t->_(Enums::$ordinary_payment_approval_status[$data['approval_status']]),// 申请状态
                        static::$t->_(Enums::$ordinary_payment_pay_status[$data['pay_status']]),// 支付状态
                    ];
                    // 是否上传补充附件
                    if ($data['is_supplement_invoice'] == OrdinaryPaymentEnums::IS_SUPPLEMENT_INVOICE_YES) {
                        if (in_array($data['id'], $sup_list)) {
                            $sup_status = OrdinaryPaymentEnums::$supplement_invoice_status[OrdinaryPaymentEnums::IS_SUPPLEMENT_INVOICE_YES];
                        } else {
                            $sup_status = OrdinaryPaymentEnums::$supplement_invoice_status[OrdinaryPaymentEnums::IS_SUPPLEMENT_INVOICE_NO];
                        }
                    } else {
                        $sup_status = '';
                    }
                    // 是否添加附件
                    $is_supplement_invoice = empty($data['is_supplement_invoice']) ? '' : (OrdinaryPaymentEnums::IS_SUPPLEMENT_INVOICE_YES == $data['is_supplement_invoice'] ? static::$t->_('view_yes') : static::$t->_('view_no'));

                    // 费用信息/金额详情 相关附件信息
                    $_attachment_data = [
                        $main_attachment[$data['id']] ?? '', // 费用信息-附件
                        $main_append_attachment[$data['id']] ?? '', // 费用信息-补充附件
                        $detail_attachment[$data['detail_id']] ?? '', // 金额详情-附件
                        $data['detail_id'], // 金额详情-id
                    ];

                    if (Enums::PAYEE_TYPE_VENDOR == $data['payee_type']) {
                        $row_values[] = array_merge($row_value, [
                            '',
                            '',
                            '',
                            '',
                            '',
                            $data['supplier_name'],
                            $data['sap_supplier_no'],
                            $data['supplier_bk_name'],
                            $data['supplier_bk_account'],
                            $data['supplier_bk_account_name'],
                            $data['supplier_tax_number'],
                            $data['supplier_contacts'],
                            $data['supplier_tel'],
                            $data['supplier_address'],
                            $data['supplier_email'],

                            $data['apply_email'],
                            $data['invoice_no'],
                            $data['approved_at'],
                            $data['pay_bk_flow_date'],
                            $is_supplement_invoice,
                            !empty($sup_status) ? static::$t->_($sup_status) : '',

                            // 支付模块支付状态
                            $pay_status_map_list[$data['apply_no']] ?? ''

                        ], $_attachment_data);
                    } elseif (Enums::PAYEE_TYPE_PERSONAL == $data['payee_type']) {
                        $personal_data           = OrdinaryPaymentPersonal::Find([
                            'conditions' => 'ordinary_payment_id = :ordinary_payment_id:',
                            'bind'       => ['ordinary_payment_id' => $data['id']],
                        ])->toArray();

                        foreach ($personal_data as $personal_item) {
                            $row_values[] = array_merge($row_value, [
                                $personal_item['staff_info_id'],
                                $personal_item['bank_name'],
                                $personal_item['bank_no_name'],
                                $personal_item['bank_no'],
                                $personal_item['amount'],
                                '',
                                '',
                                '',
                                '',
                                '',
                                '',
                                '',
                                '',
                                '',
                                '',
                                $data['apply_email'],
                                $data['invoice_no'],
                                $data['approved_at'],
                                $data['pay_bk_flow_date'],
                                $is_supplement_invoice,
                                !empty($sup_status) ? static::$t->_($sup_status) : '',

                                // 支付模块支付状态
                                $pay_status_map_list[$data['apply_no']] ?? ''

                            ], $_attachment_data);
                        }
                    }
                }
            }

            $header = [
                static::$t->_('payment_store_renting_apply_id'), // 主数据ID
                static::$t->_('global.number'), // 编号
                static::$t->_('re_field_created_name'), // 发起人姓名
                static::$t->_('re_field_created_id'), // 发起人工号
                static::$t->_('global.applicant.name'), // 申请人姓名
                static::$t->_('global.applicant.id'), // 申请人工号
                static::$t->_('payment_store_renting_apply_company'),   // 申请人公司
                static::$t->_('re_field_apply_department_name'), // 申请人部门
                static::$t->_('re_filed_apply_cost_department'), // 费用所属部门
                static::$t->_('re_filed_apply_cost_first_department'), // 费用所属一级部门
                //费用所属公司 申请日期 币种 发票日期 付款方式 境内/境外支付 发票号 备注  付款条款 应付日期
                static::$t->_('re_field_cost_company_name'),
                static::$t->_('re_filed_business_type'), // 业务类型
                static::$t->_('re_filed_apply_date'),
                static::$t->_('re_filed_currency_text'),
                static::$t->_('re_filed_ticket_date'),
                static::$t->_('re_filed_payment_method'),
                static::$t->_('re_filed_pay_where'),
                static::$t->_('re_filed_ticket_number'),
                static::$t->_('re_filed_remark'),
                static::$t->_('re_filed_loan_time'),
                static::$t->_('re_filed_should_pay_date'),

                static::$t->_('ordinarypayment.amount_total_no_tax'), // 不含税金额总计
                static::$t->_('ordinarypayment.amount_total_vat'), // vat金额总计
                static::$t->_('ordinarypayment.amount_total_wht'), // wht金额总计
                static::$t->_('ordinarypayment.amount_total_have_tax'), // 含税金额总计
                static::$t->_('purchase.discount'), // 折扣
                static::$t->_('total.amount.actually.paid'), // 实付金额总计
                //额外参考信息 凭证摘要extra_message
                static::$t->_('re_filed_extra_message'),
                static::$t->_('re_filed_voucher_abstract'),
                static::$t->_('ordinarypayment.bankjournaldate'), // 银行流水日期
                static::$t->_('re_filed_apply_cost_store'), // 费用所属网点
                static::$t->_('re_filed_apply_cost_center'), // 费用所属中心
                static::$t->_('cost_category'), // 付款分类
                static::$t->_('payment_store_renting_cost_type'), // 费用类型
                static::$t->_('purchase_apply_ledger_account'), // 核算科目

                static::$t->_('re_filed_voucher_description'),//凭证描述


                static::$t->_('cost_date_between'), // 费用发生期间
                static::$t->_('purchase_order_product_field_total_price'), // 不含税金额
                static::$t->_('purchase_order_product_field_all_total'), // 含税金额
                static::$t->_('re_filed_payment_wht_category'), //wht 类别
                static::$t->_('re_filed_payment_wht_rate'), //wht 税率


                static::$t->_('wages_wht_amount'), // WHT含税金额
                static::$t->_('re_filed_payment_vat_rate'), //vat 税率

                static::$t->_('vat_amount'), // VAT金额
                static::$t->_('re_field_deductible_tax_amount'), // 可抵扣税额
                static::$t->_('re_field_deductible_vat_tax'), // 可抵扣VAT税率
                static::$t->_('ordinary_payment_type'), // 收款人类型
                static::$t->_('ordinary_payment_approval_status'),// 申请状态
                static::$t->_('ordinary_payment_pay_status'), // 支付状态
                static::$t->_('ordinary_payment_number'), // 收款人工号
                static::$t->_('ordinary_payment_bank_name'), // 银行名称
                static::$t->_('ordinary_payment_account_name'), // 银行账户名称
                static::$t->_('ordinary_payment_account_number'), // 银行账号
                static::$t->_('ordinary_payment_amount'), // 金额
                static::$t->_('ordinary_payment_supplier_name'), // 供应商名称
                static::$t->_('ordinary_payment_sap_supplier_no'), //sap 供应商编号


                static::$t->_('ordinary_payment_bank_name'), // 银行名称
                static::$t->_('ordinary_payment_bank_account_number'), // 银行账户号
                static::$t->_('ordinary_payment_account_name'), // 银行账户名称
                static::$t->_('ordinary_payment_supplier_tax_number'), // 供应商税务号
                static::$t->_('ordinary_payment_supplier_contacts'), // 供应商联系人
                static::$t->_('ordinary_payment_supplier_tel'), // 供应商联系方式
                static::$t->_('ordinary_payment_supplier_address'), // 供应商地址
                static::$t->_('ordinary_payment_supplier_email'), // 供应商邮箱

                static::$t->_('ordinary_payment_apply_email'), // 申请人邮箱
                static::$t->_('ordinary_payment_invoice_no'), // 增值税发票
                static::$t->_('ordinary_payment_approve_at'), // 审批通过日期
                static::$t->_('ordinary_payment_pay_at'), // 银行流水日期
                static::$t->_('ordinary_payment_is_supplement_invoice'), // 是否需要补充发票
                static::$t->_('ordinary_payment_supplement_invoice_status'), // 补充附件状态
                static::$t->_('pay_module_pay_status'), // 支付模块支付状态

                static::$t->_('ordinary_payment_expense_info_attachment'), // 费用信息-附件
                static::$t->_('ordinary_payment_expense_info_append_attachment'), // 费用信息-补充附件
                static::$t->_('ordinary_payment_expense_info_detail_attachment'), // 金额详情-附件
                static::$t->_('ordinary_payment_expense_info_detail_id'), // 金额详情-ID
            ];
            $file_name         = "Ordinary_payment_" . date("YmdHis");
            $result            = $this->exportExcel($header, $row_values, $file_name);
            $result['message'] = $result['code'] == ErrCode::$SUCCESS ? 'success' : $result['data'];

            return $result;

        } catch (ValidationException $e) {
            $code    = ErrCode::$VALIDATE_ERROR;
            $message = $e->getMessage();
        } catch (\Exception $e) {
            $code    = ErrCode::$SYSTEM_ERROR;
            $message = $e->getMessage() . $e->getTraceAsString();
            $this->logger->warning('普通付款-数据查询-导出数据异常:' . $message . $e->getTraceAsString());
        }

        return [
            'code'    => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data'    => [],
        ];
    }

    /**
     * 获取部门与其一级部门的映射关系
     */
    private static function getDepartmentAndFirstLevelDepartmentMap()
    {
        // 费用所属部门id 与 一级部门名称的映射关系
        $all_department = (new DepartmentRepository())->getAllDepartmentList();
        $department_and_first_level_name_map = [];
        foreach ($all_department as $value) {
            if (in_array($value['type'], [2, 3]) && $value['level'] == 1) {
                // 遍历所有部门
                foreach ($all_department as $_value) {
                    if (in_array($_value['type'], [2, 3])) {
                        $ancestry_v3 = explode('/', $_value['ancestry_v3']);
                        // 当前一级部门id 是否 在当前部门链中
                        if (in_array($value['id'], $ancestry_v3)) {
                            $department_and_first_level_name_map[$_value['id']] = $value['name'];
                        }
                    }
                }
            }
        }

        unset($all_department);
        return $department_and_first_level_name_map;
    }

    /**
     * 返回可同步至金蝶的普通付款列表
     * @param array $params ['kingdee_company_ids' => '金蝶BU公司ID组', 'date_start' => '数据范围起始日期', 'date_end' => '数据范围截止日期', 'pay_type' => '1应付,2付款', 'is_cancel'=>'是否取消付款，0否，1是', 'max_id' => '最大id']
     * @return mixed
     */
    public function getSendKingDeeList($params)
    {
        $page_size = 1;
        $kingdee_company_ids = $params['kingdee_company_ids'];
        $date_start = $params['date_start'];
        $date_end = $params['date_end'];
        $pay_type = $params['pay_type'] ?? 0;
        $is_cancel = $params['is_cancel'] ?? 0;
        $max_id = $params['max_id'] ?? 0;
        $columns = [
            'main.id',
            'main.apply_no',
            'main.create_id',
            'main.amount_total_have_tax',
            'main.cost_company_id',
            'main.currency',
            'main.approved_at',
            'main.cost_department_id',
            'main.amount_total_wht',
            'main.remark',
            'main.amount_total_actually',
            'main.amount_discount',
            'main.payee_type',
            'main.should_pay_date',
            'main.is_pay_module',
            'ope.is_pay',
            'ope.pay_at',
            'ope.pay_bk_account',
            'ope.pay_bk_flow_date',
            'ope.sap_supplier_no',
        ];
        $builder = $this->modelsManager->createBuilder();
        $builder->from(['main' => OrdinaryPayment::class]);
        $builder->leftjoin(OrdinaryPaymentExtend::class, 'main.id = ope.ordinary_payment_id', 'ope');
        $builder->where('approval_status = :status: and main.payment_method = :payment_method:', ['status' => Enums::WF_STATE_APPROVED, 'payment_method' => Enums::PAYMENT_METHOD_BANK_TRANSFER]);
        $builder->inWhere('cost_company_id', $kingdee_company_ids);
        $builder->andWhere('main.id > :max_id:', ['max_id' => $max_id]);
        if ($pay_type == KingDeeEnums::PAY_TYPE_PAYABLE && $is_cancel == KingDeeEnums::IS_CANCEL_PAY_NO) {
            //应付正向
            $builder->andWhere('main.payable_positive_is_send_kingdee = :is_send: and main.approved_at >= :date_start: and approved_at < :date_end:', ['is_send' => KingDeeEnums::PAY_IS_SEND_KINGDEE_NO, 'date_start' => $date_start, 'date_end' => $date_end]);
        } else if ($pay_type == KingDeeEnums::PAY_TYPE_PAYABLE && $is_cancel == KingDeeEnums::IS_CANCEL_PAY_YES) {
            //应付反向
            $builder->andWhere('main.pay_status = :pay_status: and main.payable_negative_is_send_kingdee = :is_send: and ope.pay_at >= :date_start: and ope.pay_at < :date_end:', ['pay_status' => Enums::PAYMENT_PAY_STATUS_NOTPAY, 'is_send' => KingDeeEnums::PAY_IS_SEND_KINGDEE_NO, 'date_start' => $date_start, 'date_end' => $date_end]);
        } else if ($pay_type == KingDeeEnums::PAY_TYPE_PAYBILL) {
            //付款
            $builder->andWhere('main.pay_status = :pay_status: and main.paybill_is_send_kingdee = :is_send: and ope.pay_bk_flow_date >= :date_start: and ope.pay_bk_flow_date < :date_end:', ['pay_status' => Enums::PAYMENT_PAY_STATUS_PAY, 'is_send' => KingDeeEnums::PAY_IS_SEND_KINGDEE_NO, 'date_start' => $date_start, 'date_end' => $date_end]);
        }

        $builder->columns($columns);
        $builder->limit($page_size);
        $builder->orderBy('main.id ASC');
        return $builder->getQuery()->execute()->toArray();

    }


    /**
     * 获取普通付款应付参数
     * @param object $item 报销单据对象信息
     * @param array $params['is_cancel' => '应付（付款）是否取消支付：0否（正向），1是（反向）', 'expire_date' => '到期日', 'vendor_id' => '供应商编码', 'sap_company_list' => 'sap公司ID组', 'account_subjects_list' => '会计科目组']
     * @return array
     */
    public function getPayableParams($item, $params)
    {
        $is_cancel      = $params['is_cancel'];
        $details        = OrdinaryPaymentDetail::find([
            'conditions' => 'ordinary_payment_id = :ordinary_payment_id:',
            'bind'       => ['ordinary_payment_id' => $item['id']],
        ])->toArray();
        $payable_params = [
            'bill_no'         => ($is_cancel == KingDeeEnums::IS_CANCEL_PAY_YES ? 'CN' : '') . get_country_code() . $item['apply_no'],
            'apply_date'      => ($item['is_pay'] == Enums::PAYMENT_PAY_STATUS_NOTPAY) ? date('Y-m-d', strtotime($item['pay_at'])) : date('Y-m-d', strtotime($item['approved_at'])),
            'expire_date'     => $params['expire_date'],
            'vendor_id'       => $params['vendor_id'],
            'business_type_no'=> $params['business_type_no'] ?? '',
            'currency'        => $item['currency'],
            'sap_company_id'  => $params['sap_company_list'][$item['cost_company_id']]['sap_company_id'] ?? '',
            'no'              => $item['apply_no'],
            'amount'          => $item['amount_total_have_tax'],
            'cost_store_name' => $details[0]['cost_store_name'],
            'is_offset_loan'  => '否',
            'loan_amount'     => 0
        ];
        $budgetIds =  array_column($details,'budget_id'); //科目IDs（付款分类）
        $budgetService = new BudgetService();
        $budgets = $budgetService->budgetObjectList($budgetIds);
        foreach ($details as $detail) {
            $budget_name  = isset($budgets[$detail['budget_id']]) ? $budgets[$detail['budget_id']]['name_' . 'en'] : '';
            $payable_params['details'][] = [
                'company_project' => $params['company_project_enum'][$params['sap_company_list'][$item['cost_company_id']]['sap_company_id']] ?? '',
                'abstract'         => $item['apply_no'] . '_' . $budget_name . '(' . $detail['voucher_description'] . ')',
                'cost_center_code' => $detail['cost_center_name'],
                'quantity'         => ($detail['amount_no_tax'] >= 0) ? ($is_cancel == KingDeeEnums::IS_CANCEL_PAY_YES ? -1 : 1) : ($is_cancel == KingDeeEnums::IS_CANCEL_PAY_YES ? 1 : -1),
                'tax_not_price'    => abs($detail['amount_no_tax']),
                'tax_not'          => $detail['amount_no_tax'],
                'amount_price'     => abs($detail['amount_have_tax']),
                'amount'           => $detail['amount_have_tax'],
                'rate'             => $detail['vat_rate'],
                'tax'              => $detail['amount_vat'],
                'subjects_code'    => $params['account_subjects_list'][$detail['ledger_account_id']]['subjects_code'] ?? '',
            ];
        }

        return $payable_params;
    }


    /**
     * 关联保理付款单号筛选
     * @param array $params 请求参数组
     * @return array
     */
    public function searchFactoringList($params)
    {
        $code = ErrCode::$SUCCESS;
        $message = $real_message = 'success';
        $list = [];
        try {
            //先找到待审核、已通过、待支付、已支付 && 关联保理付款单号非空 的 关联保理付款单号组
            $ordinary_payment_factoring_nums = EnumsService::getInstance()->getSettingEnvValue('ordinary_payment_factoring_nums', 0);
            $factoring_apply_no_list = OrdinaryPaymentModel::find([
                'columns' => 'factoring_apply_no, count(id) as num',
                'conditions' => 'approval_status in ({approval_status:array}) and pay_status in ({pay_status:array}) and factoring_apply_no != :factoring_apply_no:',
                'bind' => ['approval_status' => [Enums::WF_STATE_PENDING, Enums::WF_STATE_APPROVED], 'pay_status' => [PayEnums::PAYMENT_MODULE_PAY_STATUS_PENDING, PayEnums::PAYMENT_MODULE_PAY_STATUS_PAY], 'factoring_apply_no' => ''],
                'group' => 'factoring_apply_no',
                'having' => 'num >= ' . $ordinary_payment_factoring_nums
            ])->toArray();
            $factoring_apply_nos = array_column($factoring_apply_no_list, 'factoring_apply_no');

            //再找待审核、已通过、待支付、已支付 && 是否保理付款 = 是 && 关联保理单号是空 && 单号不在关联保理付款单号组的单据号
            $builder = $this->modelsManager->createBuilder();
            $builder->from(OrdinaryPayment::class);
            $builder->columns('apply_no, factoring_vendor_id, factoring_vendor_name');
            $builder->where('approval_status in ({approval_status:array}) and pay_status in ({pay_status:array}) and is_factoring = :is_factoring: and factoring_apply_no = :factoring_apply_no:', [
                'approval_status' => [Enums::WF_STATE_PENDING, Enums::WF_STATE_APPROVED],
                'pay_status' => [PayEnums::PAYMENT_MODULE_PAY_STATUS_PENDING, PayEnums::PAYMENT_MODULE_PAY_STATUS_PAY],
                'is_factoring' => OrdinaryPaymentEnums::IS_FACTORING_YES,
                'factoring_apply_no' => '',
            ]);
            // 按照的单号模糊搜索
            if (!empty($params['apply_no'])) {
                $builder->andWhere('apply_no like :apply_no:', ['apply_no' => '%' . $params['apply_no'] . '%']);
            }
            if (!empty($factoring_apply_nos)) {
                $builder->notInWhere('apply_no', $factoring_apply_nos);
            }
            $builder->orderBy('apply_no');
            $builder->limit(GlobalEnums::DEFAULT_PAGE_SIZE);
            $list = $builder->getQuery()->execute()->toArray();

            //根据保理对象的供应商编号组获取供应商的信息和账号组
            $factoring_vendor_ids = array_values(array_unique(array_column($list, 'factoring_vendor_id')));
            if ($factoring_vendor_ids) {
                $vendor_list = ListService::getInstance()->getOrdinaryPaymentFactoringVendorList($factoring_vendor_ids);
                $factoring_apply_list = $this->getFactoringList(array_column($list, 'apply_no'));
                foreach ($list as &$item) {
                    $item['factoring_payment_peroid_num'] = $factoring_apply_list[$item['apply_no']]['factoring_payment_peroid_num'];
                    $item['factoring_vendor_info'] = $vendor_list[$item['factoring_vendor_id']] ?? [];
                }
            }
        } catch (\Exception $e) {
            $code = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage();
            $this->logger->warning('关联保理付款单号筛选失败: ' . $real_message);
        }
        return [
            'code' => $code,
            'message' => $message,
            'data' => $list,
        ];
    }

    /**
     * 获取每个已被关联的保理付款单下金额详情-实付金额总计累计、期数累计
     * @param array $factoring_apply_nos 关联保理付款单号组
     * @return array
     */
    public function getFactoringList($factoring_apply_nos)
    {
        if (!$factoring_apply_nos) {
            return [];
        }

        //获取本位币
        $default_currency = EnumsService::getInstance()->getSysCurrencyInfo();

        $list = OrdinaryPayment::find([
            'columns' => 'SUM(if(currency=' . $default_currency['code'] . ', amount_total_actually, amount_total_actually * exchange_rate)) as amount_total_actually, factoring_apply_no, COUNT(id) as factoring_payment_peroid_num',
            'conditions' => 'approval_status in ({approval_status:array}) and pay_status in ({pay_status:array}) and factoring_apply_no in ({factoring_apply_no:array})',
            'bind' => ['approval_status' => [Enums::WF_STATE_PENDING, Enums::WF_STATE_APPROVED], 'pay_status' => [PayEnums::PAYMENT_MODULE_PAY_STATUS_PENDING, PayEnums::PAYMENT_MODULE_PAY_STATUS_PAY], 'factoring_apply_no' => $factoring_apply_nos],
            'group' => 'factoring_apply_no'
        ])->toArray();
        $list = array_column($list, null, 'factoring_apply_no');
        foreach ($factoring_apply_nos as $factoring_apply_no) {
            if (!isset($list[$factoring_apply_no])) {
                $list[$factoring_apply_no] = [
                    'amount_total_actually' => '0.00',
                    'factoring_payment_peroid_num' => 1,
                ];
            } else {
                $list[$factoring_apply_no]['factoring_payment_peroid_num'] += 1;
            }
        }
        return $list;
    }

    /**
     * 获取核算科目对应的未删除的财务分类
     * @param integer $ledger_account_id 核算科目id
     * @return array
     */
    public function getFinanceCategoryList($ledger_account_id)
    {
        $code = ErrCode::$SUCCESS;
        $message = $real_message = 'success';
        $data = [];
        try {
            $data = $this->findFinanceCategory($ledger_account_id);
        } catch (\Exception $e) {
            $code = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage();
            $this->logger->warning('获取核算科目对应的未删除的财务分类失败: ' . $real_message);
        }
        return [
            'code' => $code,
            'message' => $message,
            'data' => $data,
        ];
    }

    /**
     * 获取核算科目对应的未删除的财务分类
     * @param integer $ledger_account_id 核算科目id
     * @return array
     */
    public function findFinanceCategory($ledger_account_id)
    {
        $data = [];
        if (empty($ledger_account_id)) {
            return $data;
        }
        $finance_category = MaterialFinanceCategoryModel::find([
            'columns' => 'id, name',
            'conditions' => 'ledger_account_id = :ledger_account_id: and is_deleted = :is_deleted:',
            'bind' => ['ledger_account_id' => $ledger_account_id, 'is_deleted' => GlobalEnums::IS_NO_DELETED]
        ])->toArray();
        if (count($finance_category) == 1) {
            $data['finance_category_id'] = $finance_category[0]['id'];
            $data['finance_category_name'] = $finance_category[0]['name'];
        }
        return $data;
    }



    /**
     * 获取普通付款模块审批通过日期在上月1号（含）到本月1号（不含）、费用所属网点/总部=总部、明细行的费用部门 != 单据头上的费用部门的所有明细行数据
     * @param string $last_month_start 上月1号
     * @param string $current_month_start 本月1号
     * @return array
     */
    public function getPublicExpenseSplitData(string $last_month_start, string $current_month_start): array
    {
        $builder = $this->modelsManager->createBuilder();
        $builder->from(['main' => OrdinaryPayment::class]);
        $builder->innerJoin(OrdinaryPaymentDetail::class, 'detail.ordinary_payment_id = main.id', 'detail');
        //审批通过 && 审批通过日期在上月1号（含）到本月1号（不含）
        $builder->where('main.approval_status = :status: and main.approved_at >= :date_start: and main.approved_at < :date_end:', ['status' => Enums::WF_STATE_APPROVED, 'date_start' => $last_month_start, 'date_end' => $current_month_start]);
        //费用所属网点/总部 (1 :总部,2:网点) = 总部
        $builder->andWhere('main.cost_store_type = :cost_store_type:', ['cost_store_type' => Enums::PAYMENT_COST_STORE_TYPE_01]);
        //明细行的费用部门 != 单据头上的费用部门
        $builder->andWhere('detail.cost_department_id != main.cost_department_id');
        $builder->columns('main.apply_no,main.create_id,main.create_node_department_name,main.created_at,main.cost_department_name as main_cost_department_name,detail.cost_department_name,detail.budget_id,detail.product_id,main.currency,detail.amount_have_tax,detail.cost_sys_department');
        $items = $builder->getQuery()->execute()->toArray();

        if (empty($items)) {
            return [];
        }

        $budget_service = new BudgetService();
        //付款分类
        $budget_list = $budget_service->budgetObjectList(array_column($items, 'budget_id'));

        //费用类型
        $product_list = $budget_service->budgetObjectProductList(array_column($items, 'product_id'));

        //发起人列表
        $staff_list = (new HrStaffRepository())->getStaffSysDepartmentInfoByIds(array_column($items, 'create_id'));
        //英文
        $en_language = self::getTranslation('en');
        $list        = [];
        foreach ($items as $item) {
            $list[] = [
                'module_zh'                   => static::$t->_('public_expense_split_notice_module.2'),
                'module_en'                   => $en_language['public_expense_split_notice_module.2'],
                'no'                          => $item['apply_no'], //普通付款单号
                'created_id'                  => $item['create_id'], //单据创建人工号 = 单据发起人工号
                'created_department_name'     => $item['create_node_department_name'], //单据创建人所属部门名称 = 单据发起人所属部门名称
                'created_sys_department_name' => $staff_list[$item['create_id']]['sys_department_name'] ?? '', //单据创建人所属一级部门名称 = 单据发起人所属一级部门名称
                'apply_date'                  => date('Y-m-d', strtotime($item['created_at'])), //申请日期 = 单据申请日期格式YYYY-MM-DD
                'main_cost_department_name'   => $item['main_cost_department_name'],//预算部门 = 表头中的费用所属部门
                'cost_department_name'        => $item['cost_department_name'], //明细行中费用网点/总部 = 明细行中费用网点/总部
                'cost_classify_zh'            => $budget_list[$item['budget_id']]['name_cn'] ?? '', //费用分类 = 明细行中的付款分类-中文
                'cost_classify_en'            => $budget_list[$item['budget_id']]['name_en'] ?? '', //费用分类 = 明细行中的付款分类-英文
                'cost_detail_zh'              => $product_list[$item['product_id']]['name_cn'] ?? '', //费用明细 = 明细行中的费用类型-中文
                'cost_detail_en'              => $product_list[$item['product_id']]['name_en'] ?? '', //费用明细 = 明细行中的费用类型-英文
                'currency'                    => static::$t->_(GlobalEnums::$currency_item[$item['currency']]), //币种 = 表头中的币种
                'amount'                      => $item['amount_have_tax'],//含税金额 = 明细行中的含税金额
                'cost_sys_department'         => $item['cost_sys_department'],//费用一级部门ID
            ];
        }
        return $list;
    }

    /**
     * 普通付款 - 申请创建 - 预提编号 - 是否显示
     * @param array $params 请求参数组
     * @return array
     */
    public function showBudgetWithholding(array $params)
    {
        $code        = ErrCode::$SUCCESS;
        $message     = $real_message = 'success';
        $is_show     = false;
        $amount_type = 0;
        try {
            //上线日期
            $budget_withholding_online_date = EnumsService::getInstance()->getSettingEnvValue('budget_withholding_online_date');

            //遍历明细行，分别判断费用发生日期的开始日期和结束日期，如果任意一个大于上线日期就显示
            foreach ($params['amount_detail'] as $detail) {
                if ($detail['cost_start_date'] > $budget_withholding_online_date || $detail['cost_end_date'] > $budget_withholding_online_date) {
                    $is_show = true;
                    break;
                }
            }

            //如果有在上线日期之后的显示预提，则需要再循环判断所有的付款分类，只要有任意一行付款分类满足预提条件，则显示预提编号，显示以后必填
            if ($is_show) {
                $current_month = date('Y-m');
                //获取部门下每个预算科目下的预算情况
                $budget_ids = array_values(array_unique(array_column($params['amount_detail'], 'budget_id')));
                $builder = $this->modelsManager->createBuilder();
                $builder->columns('o.id, d.department_id, d.amount_type');
                $builder->from(['d' => BudgetObjectDepartmentAmount::class]);
                $builder->Join(BudgetObject::class, 'd.object_code = o.level_code', 'o');
                $builder->where('d.is_delete = :is_delete:', ['is_delete' => GlobalEnums::IS_NO_DELETED]);
                //费用所属网点/总部；预算占用组织架构类型：1-网点；2-总部相反需要特殊处理
                $builder->andWhere('d.organization_type = :organization_type:', ['organization_type' => $params['cost_store_type'] == Enums::PAYMENT_COST_STORE_TYPE_01 ? 2 : 1]);
                $builder->inWhere('o.id', $budget_ids);
                $builder->andWhere('d.month = :month:', ['month' => $current_month]);
                $builder->andWhere('d.department_id = :department_id:', ['department_id' => $params['cost_department_id']]);
                $items = $builder->getQuery()->execute()->toArray();

                //默认为不显示、查询不到不显示预提编号
                $is_show = false;
                if ($items) {
                    //预提单-配置哪些部门的哪些科目必须关联预提单，同时标记是否允许当月费用不关联预提单，key为部门ID_预算科目ID，值为1允许，2不允许
                    $budget_withholding_department_budget_allow = EnumsService::getInstance()->getSettingEnvValueMap('budget_withholding_department_budget_allow');

                    //获取指定部门的一级部门信息
                    $department_repository = new DepartmentRepository();
                    $department_info = $department_repository->getDepartmentDetail($params['cost_department_id']);
                    $first_department_info = $department_repository->getFirstLevelDepartmentByAncestryV3($department_info['ancestry_v3']);

                    //通过费用部门+付款分类查询当前预算是共享预算还是非共享预算
                    foreach ($items as $item) {
                        //如果非共享预算，则查询费用部门；如果为共享预算，则查询费用部门的一级部门
                        $cost_department_id = $item['amount_type'] == BudgetBaseService::IS_NO_COM_USE ? $params['cost_department_id'] : $first_department_info['id'];

                        //查询费用部门+付款分类是否属于配置的必须预提的预算科目, 不属于直接跳过
                        if (!isset($budget_withholding_department_budget_allow[$cost_department_id . '_' . $item['id']])) {
                            continue;
                        }

                        //如果属于则显示预提编号
                        $is_show = true;

                        //还需要判断费用发生日期的开始日期和结束日期都属于当前月 && 否允许当月费用不关联预提单：1允许，2不允许；不允许时需要显示预提编号
                        $allow = $budget_withholding_department_budget_allow[$cost_department_id . '_' . $item['id']];
                        foreach ($params['amount_detail'] as $detail) {
                            if ($detail['budget_id'] != $item['id']) {
                                continue;
                            }
                            
                            if (date('Y-m', strtotime($detail['cost_start_date'])) == $current_month && date('Y-m', strtotime($detail['cost_end_date'])) == $current_month) {
                                $is_show = $allow == BudgetWithholdingEnums::ALLOW_CURRENT_MONTH_YES ? false : true;
                            } else {
                                $is_show = true;
                            }

                            if ($is_show) {
                                break;
                            }
                        }

                        if ($is_show) {
                            //是否共享预算
                            $amount_type = $item['amount_type'];
                            break;
                        }
                    }
                }
            }
        } catch (\Exception $e) {
            $code    = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $this->logger->warning('获取预提编号是否显示失败: ' . $e->getMessage());
        }
        return [
            'code'    => $code,
            'message' => $message,
            'data'    => [
                'is_show' => $is_show,
                'amount_type' => $amount_type
            ],
        ];
    }

    /**
     * 普通付款 - 申请创建 - 预提编号 - 搜索、选择
     * @param array $params 请求参数组
     * @return array
     */
    public function searchBudgetWithholding(array $params)
    {
        $code    = ErrCode::$SUCCESS;
        $message = $real_message = 'success';
        $data    = [];
        try {
            //  1. 如果费用部门+付款分类为非共享预算，则查询所有预算单中费用部门等于普通付款的费用部门，并且付款分类等于预提单中预算科目，并且费用所属网点/总部等于预提单中的费用所属网点/总部，并且预提单状态等于已通过的所有预提单
            //  2. 如果费用部门+付款分类为共享预算，则查询所有预算单中费用部门等于普通付款的费用部门或一级部门，并且费用所属网点/总部等于预提单中的费用所属网点/总部，并且付款分类等于预提单中预算科目，并且预提单状态等于已通过的所有预提单
            if ($params['amount_type'] == BudgetBaseService::IS_COM_USE) {
                //获取指定部门的一级部门信息
                $department_repository = new DepartmentRepository();
                $department_info = $department_repository->getDepartmentDetail($params['cost_department_id']);
                $first_department_info = $department_repository->getFirstLevelDepartmentByAncestryV3($department_info['ancestry_v3']);
                $params['cost_department_id'] = [
                    $params['cost_department_id'],
                    $first_department_info['id']
                ];
            }
            $items = BudgetWithholdingRepository::getInstance()->getBudgetWithholdingListByParams($params);
            $budget_list = (new BudgetService())->getBudgetByIds([$params['budget_id']]);
            foreach ($items as $item) {
                $provision_amount = bcdiv($item['provision_amount'], 1000, 2);
                $use_amount       = bcdiv($item['use_amount'], 1000, 2);
                $data[] = [
                    'id'                   => $item['id'],
                    'no'                   => $item['no'],
                    'cost_department_name' => $item['cost_department_name'],
                    'cost_store_type_text' => static::$t->_(Enums::$payment_cost_store_type[$item['cost_store_type']]),
                    'ownership_period'     => $item['ownership_period'],
                    'budget_name'          => $budget_list[$item['budget_id']] ?? '',
                    'provision_amount'     => $provision_amount,
                    'use_amount'           => $use_amount,
                    'remain_amount'        => bcsub($provision_amount, $use_amount, 2),
                ];
            }
        } catch (\Exception $e) {
            $code    = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $this->logger->warning('获取预提编号 - 搜索、选择失败: ' . $e->getMessage());
        }
        return [
            'code'    => $code,
            'message' => $message,
            'data'    => $data,
        ];
    }

    /**
     * 校验普通付款中预提单号等于当前预提单并且申请状态等于待审核或已通过的数据是否状态都为已支付或未支付，只要有任意一条数据不是，则提示“存在未完成支付的单据，不允许关闭预提单”，如果没有关联的普通付款单，则无效校验普通付款状态
     * @param int $budget_withholding_id 预提单id
     * @return bool
     * @throws ValidationException
     */
    public function checkBudgetWithholding(int $budget_withholding_id)
    {
        $count = OrdinaryPayment::count([
            'conditions' => 'budget_withholding_id = :budget_withholding_id: and approval_status in ({approval_status:array}) and pay_status not in ({pay_status:array})',
            'bind'       => [
                'budget_withholding_id' => $budget_withholding_id,
                'approval_status'       => [Enums::WF_STATE_PENDING, Enums::WF_STATE_APPROVED],
                'pay_status'            => [PayEnums::PAYMENT_MODULE_PAY_STATUS_PAY, PayEnums::PAYMENT_MODULE_PAY_STATUS_NOTPAY,],
            ],
        ]);
        if ($count) {
            //存在未完成支付的单据，不允许关闭预提单
            throw new ValidationException(static::$t->_('budget_withholding_close_exist_un_payment'), ErrCode::$VALIDATE_ERROR);
        }
        return true;
    }
}
