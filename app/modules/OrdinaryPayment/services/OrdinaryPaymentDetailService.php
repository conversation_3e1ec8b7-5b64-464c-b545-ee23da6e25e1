<?php

namespace App\Modules\OrdinaryPayment\Services;

use App\Library\Enums;
use App\Library\Enums\ContractEnums;
use App\Library\Enums\DepositEnums;
use App\Library\Enums\OrdinaryPaymentEnums;
use App\Library\Enums\GlobalEnums;
use App\Library\Enums\PayEnums;
use App\Library\Enums\KingDeeEnums;
use App\Library\Enums\SettingEnums;
use App\Library\Enums\StaffInfoEnums;
use App\Library\Enums\SysConfigEnums;
use App\Library\ErrCode;
use App\Library\Exception\BusinessException;
use App\Library\Validation\ValidationException;
use App\Models\oa\SettingEnvModel;
use App\Modules\Budget\Services\BudgetService;
use App\Modules\Budget\Services\LedgerAccountService;
use App\Modules\Common\Services\EnumsService;
use App\Modules\Common\Services\StoreService;
use App\Modules\Common\Services\WaterMarkerService;
use App\Modules\Contract\Models\Contract;
use App\Modules\Contract\Models\ContractArchive;
use App\Modules\Deposit\Models\DepositLossModel;
use App\Modules\Deposit\Models\DepositModel;
use App\Modules\Deposit\Models\DepositReturnModel;
use App\Modules\Hc\Models\SysStoreModel;
use App\Modules\OrdinaryPayment\Models\OrdinaryPayment;
use App\Modules\OrdinaryPayment\Models\OrdinaryPaymentDetail;
use App\Modules\OrdinaryPayment\Models\OrdinaryPaymentExtend;
use App\Modules\Organization\Services\DepartmentService;
use App\Modules\Pay\Services\PayFlowService;
use App\Modules\Pay\Services\PayService;
use App\Modules\Purchase\Services\BaseService as PurchaseService;
use App\Modules\Reimbursement\Services\ReimbursementFlowService;
use App\Modules\Setting\Services\AccountingRuleService;
use App\Modules\Setting\Services\CommonDataPermissionService;
use App\Modules\User\Models\AttachModel;
use App\Modules\User\Services\UserService;
use App\Modules\Workflow\Services\FYRService;
use App\Modules\Workflow\Services\WorkflowServiceV2;
use App\Repository\oa\AccountingSubjectsRepository;
use App\Repository\DepartmentRepository;
use App\Repository\HrStaffRepository;
use App\Repository\oa\PurchaseOrderRepository;
use Exception;
use GuzzleHttp\Exception\GuzzleException;
use Mpdf\Mpdf;
use App\Library\OssHelper;
use App\Modules\Material\Models\MaterialFinanceCategoryModel;
use App\Modules\Deposit\Services\DepositService;
use App\Modules\Common\Services\DepartmentService as CommonDepartmentService;

class OrdinaryPaymentDetailService extends BaseService
{
    private static $instance;

    private function __construct()
    {
    }


    public static function getInstance()
    {
        if (!isset(self::$instance) || !(self::$instance instanceof self)) {
            self::$instance = new self;
        }
        return self::$instance;
    }

    /**
     * 获取普通付款详情
     *
     * @param int $id
     * @param int $uid
     * @param bool $is_audit
     * @param boolean $if_download
     * @return array|mixed
     * @throws BusinessException
     * @throws ValidationException
     */
    public function getDetail(int $id, int $uid = 0, bool $is_audit = false, bool $if_download = false)
    {
        //获取付款申请主表信息
        $main_model = OrdinaryPayment::findFirst([
            'id = :id:',
            'bind' => ['id' => $id],
        ]);
        if (empty($main_model)) {
            throw new ValidationException(static::$t->_('data_not_exist_by_id', ['id' => $id]), ErrCode::$VALIDATE_ERROR);
        }

        $attachments = $main_model->getFile()->toArray();
        $data = $main_model->toArray();
        $data['attachments'] = $attachments;

        // 获取付款申请拓展表信息
        $extend_conditions = [
            'conditions' => 'ordinary_payment_id = :ordinary_payment_id:',
            'bind'       => ['ordinary_payment_id' => $id],
        ];
        $extend_obj        = OrdinaryPaymentExtend::getFirst($extend_conditions);

        switch ($main_model->payee_type) {
            case self::PAYEE_TYPE_1;
                //供应商信息
                $data['supplier_info'] = $this->getSupplierInfo($extend_obj);
                break;

            case self::PAYEE_TYPE_2;
                $personal_data = $main_model->getPersons()->toArray();
                $data['personal_detail'] = !empty($personal_data) ? $personal_data : null;
                break;
            default;
        }

        //支付信息
        $data['pay_info'] = $this->getPayInfo($extend_obj);

        //金额详情
        $data['amount_detail_item'] = OrdinaryPaymentDetail::find([
            'conditions' => 'ordinary_payment_id = :ordinary_payment_id:',
            'bind'       => ['ordinary_payment_id' => $id],
        ])->toArray();

        foreach ($data['amount_detail_item'] as &$item) {
            $item['deductible_tax_amount'] = bcdiv($item['deductible_tax_amount'],100,2);
            $item['vat7_rate'] = $item['vat_rate'].'%';
        }

        //金额详情-附件
        $data['attachment_list'] = [];
        if ($data['amount_detail_item'] && !$if_download) {
            //取出金额明细数据中ID字段
            $amount_detail_ids       = array_column($data['amount_detail_item'], 'id');
            $data['attachment_list'] = $this->getAttachmentListByDetailIds($amount_detail_ids);
        }

        // 上传的补充附件
        $data['required_supplement_file'] = AttachModel::find([
            'conditions' => 'oss_bucket_key = :oss_bucket_key: AND oss_bucket_type = :oss_bucket_type:',
            'bind'       => ['oss_bucket_key' => $id, 'oss_bucket_type' => OrdinaryPaymentEnums::OSS_BUCKET_TYPE_ORDINARY_PAYMENT_ATTACHMENT_FILE],
            'columns'    => ['bucket_name', 'object_key', 'file_name', 'oss_bucket_key'],
        ])->toArray();

        // 审批日志
        //审批流信息
        $req = (new OrdinaryPaymentFlowService())->getRequest($id);
        if (empty($req->id)) {
            throw new BusinessException('普通付款获取工作流批次失败', ErrCode::$ORDINARY_PAYMENT_GET_WORK_REQUEST_ERROR);
        }

        $data['auth_logs'] = $this->getAuditLogs($req, $data, $if_download);

        // 审批节点: 可编辑的字段: AP, APS 泰, APS 北 可编辑WHT类别/WHT税率
        $data['can_edit'] = false;
        $data['can_edit_fields']  = (object)[];
        if ($is_audit) {
            $can_edit_data = (new OrdinaryPaymentFlowService())->getCanEditFieldByReq($req, $uid);
            $data['can_edit'] =  $can_edit_data=== false ? false : true;
            $data['can_edit_fields'] = $can_edit_data=== false ? (object)[] :$can_edit_data;
        }

        // 审批节点: 可编辑的字段: AP, APS 泰, APS 北 可编辑WHT类别/WHT税率
        $data['node_tag'] = (new ReimbursementFlowService())->getPendingNodeTag($req, $uid);

        return $this->handleData($data);
    }

    /**
     * 普通付款申请详情数据获取
     * @param $id
     * @param $uid
     * @param $is_audit
     * @return array
     */
    public function getOrdinaryPaymentDetail($id, $uid, $is_audit = false)
    {
        $code    = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $data    = [];

        try {
            //获取申请详情数据
            $data = $this->getDetail($id, $uid, $is_audit);
            if (empty($data['id'])) {
                throw new BusinessException('获取普通付款信息失败', ErrCode::$ORDINARY_PAYMENT_GET_DETAIL_INFO_ERROR);
            }

        } catch (ValidationException $e) {
            $code    = $e->getCode();
            $message = $e->getMessage();

        } catch (BusinessException $e) {
            $code         = $e->getCode();
            $message      = static::$t->_('retry_later');
            $real_message = $e->getMessage();

        }

        if (!empty($real_message)) {
            $this->logger->warning('loan-get-audit-detail-failed:' . $real_message);
        }

        return [
            'code'    => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data'    => $data,
        ];
    }

    /**
     * 生成普通付款pdf文件
     * @param int $id
     * @param int $uid
     * @return array
     */
    public function download(int $id, int $uid)
    {
        $code    = ErrCode::$SUCCESS;
        $message = $real_message = '';

        $lang = $this->getLang();

        $return_data = [];

        try {
            $data = $this->getDetail($id, $uid, false, true);
            if (empty($data['id'])) {
                throw new BusinessException('获取普通付款信息失败', ErrCode::$ORDINARY_PAYMENT_GET_INFO_ERROR);
            }

            if (!$this->isCanDownload($data,$uid)) {
                throw new ValidationException('unpaid status cannot be downloaded1', ErrCode::$ORDINARY_PAYMENT_PAY_STATUS_DOWNLOAD_ERROR);
            }

            // 文件临时目录
            $sys_tmp_dir = sys_get_temp_dir();
            $file_dir    = $sys_tmp_dir . '/';
            $file_name   = 'ordinary_payment_' . md5($id) . "_{$lang}.pdf";
            $file_path   = $file_dir . $file_name;
            $view = new \Phalcon\Mvc\View();
            $view->setViewsDir(APP_PATH . '/views');
            $view->setVars($data);
            $view->start();
            $view->disableLevel([
                \Phalcon\Mvc\View::LEVEL_LAYOUT      => false,
                \Phalcon\Mvc\View::LEVEL_MAIN_LAYOUT => false,
            ]);

            // 代码里审批日志用的倒序
            $view->render("ordinaryPayment", "ordinary_payment_" . $lang);
            $view->finish();
            $content = $view->getContent();
            //echo $content;

            $mpdf = new Mpdf([
                'format' => 'A4',
                'mode'   => 'zh-CN',
            ]);

            $mpdf->useAdobeCJK = true;
            $mpdf->SetDisplayMode('fullpage');
            $mpdf->SetHTMLHeader("");
            $mpdf->SetHTMLFooter("");
            $mpdf->WriteHTML($content);
            $mpdf->Output($file_path, "f");

            // 加水印
            WaterMarkerService::getInstance()->addWaterMarkerToPdfFile($file_path, $file_path);

            // 生成成功, 上传OSS
            $upload_res = OssHelper::uploadFile($file_path);
            if (!empty($upload_res['object_url'])) {
                $return_data['file_name'] = self::$t->_('ordinary_payment_pdf_file_name') . '.pdf';
                $return_data['file_url']  = $upload_res['object_url'];
            } else {
                throw new BusinessException('普通付款下载失败，请重试', ErrCode::$ORDINARY_PAYMENT_DOWNLOAD_UPLOAD_OSS_ERROR);
            }

        } catch (ValidationException $e) {
            $code    = $e->getCode();
            $message = $e->getMessage();

        } catch (BusinessException $e) {
            $code         = $e->getCode();
            $message      = static::$t->_('retry_later');
            $real_message = $e->getMessage();

        } catch (\Mpdf\MpdfException $e) {
            $code         = $e->getCode();
            $message      = static::$t->_('retry_later');
            $real_message = $e->getMessage();

        }

        if (!empty($real_message)) {
            $this->logger->warning('ordinary_payment-detail-download-failed:' . $real_message);
        }

        return [
            'code'    => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data'    => $return_data,
        ];
    }

    /**
     * 文件流输出
     * @param string $file_path
     * @param string $file_name
     * @return mixed
     */
    public function outputPdfFile(string $file_path, string $file_name = '')
    {
        if (empty($file_path)) {
            return [
                'code'    => ErrCode::$VALIDATE_ERROR,
                'message' => 'access error',
                'data'    => [],
            ];
        }

        $file_name = $file_name ? $file_name : 'File_' . date('YmdHis') . '.pdf';

        header('Content-Description: File Transfer');
        header('Content-Transfer-Encoding: binary');
        header('Cache-Control: public, must-revalidate, max-age=0');
        header('Pragma: public');
        header('Expires: Sat, 26 Jul 1997 05:00:00 GMT');
        header('Last-Modified: ' . gmdate('D, d M Y H:i:s') . ' GMT');
        header('Content-Type: application/pdf');

        if (!isset($_SERVER['HTTP_ACCEPT_ENCODING']) || empty($_SERVER['HTTP_ACCEPT_ENCODING'])) {
            // don't use length if server using compression
            header('Content-Length: ' . get_headers($file_path, true)['Content-Length']);
        }

        header('Content-Disposition: attachment; filename="' . $file_name . '"');

        exit(file_get_contents($file_path));
    }

    /**
     * 处理详情数据格式
     * @param $data
     * @return array
     */
    protected function handleData($data)
    {
        if (empty($data)) {
            return [];
        }

        if (!empty($data['amount_detail_item'])) {
            $budgetIds =  array_column($data['amount_detail_item'],'budget_id'); //科目IDs（付款分类）
            $productIds =  array_column($data['amount_detail_item'],'product_id');//产品IDs(费用类型）
            $budgetService = new BudgetService();
            $budgets = $budgetService->budgetObjectList($budgetIds);
            $products = $budgetService->budgetObjectProductList($productIds);

            //核算科目名字
            $ledger_id_to_name           = [];
            $accounting_subjects_to_name = [];
            //17822需求，需要按照科目类型区分1快递公司科目、2子公司科目来查询不同的科目表
            $ledger_account_ids = array_values(array_filter(array_column($data['amount_detail_item'], 'ledger_account_id')));
            //核算科目名字
            if (!empty($ledger_account_ids)) {
                //快递公司科目
                if ($data['account_type'] == KingDeeEnums::ACCOUNT_TYPE_FLASH_EXPRESS_COMPANY) {
                    $res = LedgerAccountService::getInstance()->getList($ledger_account_ids);
                    if ($res['code'] == ErrCode::$SUCCESS) {
                        $ledger_id_to_name = array_column($res['data'], 'name', 'id');
                    }
                } else {
                    //子公司科目
                    $accounting_subjects_to_name = AccountingSubjectsRepository::getInstance(static::$language)->getListByIds($ledger_account_ids);
                }
            }

            $lang = strtolower(substr(self::$language, -2));
            $lang = in_array($lang, ['th','en','cn']) ? $lang : 'en';

            $finance_category = MaterialFinanceCategoryModel::find([
                'columns' => 'id,name',
            ])->toArray();
            $finance_category= array_column($finance_category,'name','id');

            $wht_cat_map = EnumsService::getInstance()->getWhtRateCategoryMap(0);
            foreach ($data['amount_detail_item'] as $key => &$value) {
                $value['budget_name']  = isset($budgets[$value['budget_id']]) ? $budgets[$value['budget_id']]['name_' . $lang] : '';
                $value['budget_name_en']  = isset($budgets[$value['budget_id']]) ? $budgets[$value['budget_id']]['name_en'] : '';
                $value['product_name'] = isset($products[$value['product_id']]) ? $products[$value['product_id']]['name_' . $lang] : '';
                $value['product_name_en'] = isset($products[$value['product_id']]) ? $products[$value['product_id']]['name_en'] : '';
                $value['wht_category_name']        = $wht_cat_map[$value['wht_category']] ?? 0;
                $value['wht_rate_name']            = $value['wht_rate'] . '%';
                $value['attachment_list']          = $data['attachment_list'][$value['id']] ?? [];
                //核算科目
                $value['ledger_account_name'] = $ledger_id_to_name[$value['ledger_account_id']] ?? ($accounting_subjects_to_name[$value['ledger_account_id']]['subjects_name'] ?? '');

                $value['finance_category_name']    =$finance_category[$value['finance_category_id']]??'';
            }
        }

        // 发票类型
        if (!empty($data['invoice_type'])) {
            $data['invoice_type'] = $data['invoice_type'];
            $data['invoice_type_label'] = static::$t->_(GlobalEnums::$financial_invoice_type_item[$data['invoice_type']]) ?? '';
        } else {
            $data['invoice_type'] = '';
            $data['invoice_type_label'] = '';
        }

        // 费用所属公司实时获取
        $coo_cost_company_list = (new PurchaseService())->getCooCostCompany();
        $coo_cost_company_list = array_column($coo_cost_company_list, 'cost_company_name', 'cost_company_id');

        return [
            'id'                           => $data['id'],
            'apply_no'                     => $data['apply_no'],
            'create_id'                    => $data['create_id'],
            'create_name'                  => $data['create_name'],
            'create_department_name'       => $data['create_node_department_name'],
            'create_company_name'          => $data['create_company_name'],
            'apply_id'                     => $data['apply_id'],
            'apply_name'                   => $data['apply_name'],
            'apply_node_department_name'   => $data['apply_node_department_name'],
            'apply_company_name'           => $data['apply_company_name'],
            'apply_mobile'                 => $data['apply_mobile'],
            'apply_store_name'             => $data['apply_store_name'],
            'cost_department_id'           => $data['cost_department_id'],
            'cost_department_name'         => $data['cost_department_name'],
            'cost_company_name'            => $coo_cost_company_list[$data['cost_company_id']] ?? '',
            'cost_store_type'              => (int)$data['cost_store_type'],
            'cost_store_type_text'         => self::$t->_(Enums::$payment_cost_store_type[$data['cost_store_type']]),
            'payment_method'               => (int)$data['payment_method'],
            'payment_method_text'          => self::$t->_(Enums::$payment_method[$data['payment_method']]),
            'currency'                     => (int)$data['currency'],
            'currency_text'                => self::$t->_(GlobalEnums::$currency_item[$data['currency']]),
            'should_pay_date'              => $data['should_pay_date'],
            'remark'                       => $data['remark'],
            'amount_total_no_tax'          => $data['amount_total_no_tax'],
            'amount_total_vat'             => $data['amount_total_vat'],
            'amount_total_wht'             => $data['amount_total_wht'],
            'amount_discount'              => $data['amount_discount'],
            'amount_total_have_tax'        => $data['amount_total_have_tax'],
            'amount_total_actually'        => $data['amount_total_actually'],
            'approval_status'              => $data['approval_status'],
            'pay_status'                   => $data['pay_status'],
            'cancel_reason'                => $data['cancel_reason'],
            'refuse_reason'                => $data['refuse_reason'],
            'cancel_at'                    => $data['cancel_at'],
            'rejected_at'                  => $data['rejected_at'],
            'approved_at'                  => $data['approved_at'],
            'cost_company_id'              => $data['cost_company_id'],
            'extra_message'                => $data['extra_message'],
            'voucher_abstract'             => $data['voucher_abstract'],
            'swift_code'                   => $data['swift_code'],
            'pay_where'                    => $data['pay_where'],
            'ticket_number'                => $data['ticket_number'],
            'ticket_date'                  => $data['ticket_date'] ? date('Y-m-d',
                strtotime($data['ticket_date'])) : '',
            'loan_time'                    => $data['loan_time'],
            'ver'                          => $data['ver'],
            'supplier_info'                => $data['supplier_info'] ?? null, //拓展表字段- 供应商信息
            'personal_detail'              => $data['personal_detail'] ?? null,
            'pay_info'                     => $data['pay_info'],           //拓展表字段- 支付信息
            'amount_detail_item'           => $data['amount_detail_item'], // 金额详情
            'auth_logs'                    => $data['auth_logs'],          //审批流
            'can_edit'                     => $data['can_edit'],
            'can_edit_fields'              => $data['can_edit_fields'],
            'node_tag'                     => $data['node_tag'],
            'is_after_ap_th'               => $data['is_after_ap_th'],
            'attachments'                  => $data['attachments'],
            'payee_type'                   => $data['payee_type'],
            'amount_total'                 => $data['amount_total'],
            'is_supplement_invoice'        => intval($data['is_supplement_invoice']),
            'apply_email'                  => $data['apply_email'],
            'invoice_no'                   => $data['invoice_no'],
            'invoice_type'                 => $data['invoice_type'],
            'invoice_type_label'           => $data['invoice_type_label'],
            'required_supplement_file'     => $data['required_supplement_file'],
            'business_type'                => $data['business_type'],
            'is_with_vat_invoice'          => $data['is_with_vat_invoice'] ? $data['is_with_vat_invoice'] : '',
            'is_factoring'                 => $data['is_factoring'],
            'factoring_vendor_id'          => $data['factoring_vendor_id'],
            'factoring_vendor_name'        => $data['factoring_vendor_name'],
            'factoring_apply_no'           => $data['factoring_apply_no'],
            'factoring_payment_peroid_num' => $data['factoring_payment_peroid_num'],
            'budget_withholding_id'        => $data['budget_withholding_id'],
            'budget_withholding_no'        => $data['budget_withholding_no'],
        ];
    }

    /**
     * 获取审批日志
     *
     * @param $req
     * @param $detail_data
     * @param bool $if_download
     * @return array
     * @throws BusinessException
     */
    private function getAuditLogs($req, $detail_data, $if_download = false)
    {
        $auth_logs = (new WorkflowServiceV2())->getAuditLogs($req);

        // 下载的时候不要申请
        if ($if_download) {
            $temp = [];
            foreach ($auth_logs as $k => $v) {
                // 如果申请的就跳过
                if ($v['action'] == 0) {
                    continue;
                }

                $temp[] = $v;
            }

            $auth_logs = $temp;
        }

        //查询支付模块的审批流
        if ($detail_data['is_pay_module'] == PayEnums::BIZ_DATA_IS_PAY_MODULE_YES) {
            $payment_data = PayService::getInstance()->getPaymentByBusinessNo(Enums\BankFlowEnums::BANK_FLOW_OA_TYPE_ORDINARY_PAYMENT, $detail_data['apply_no']);
            if (!empty($payment_data)) {
                $pay_flow_service = new PayFlowService();
                $payment_audit_logs = $pay_flow_service->getAuditLogs($payment_data, true);
                //上下文必须保证两个数组是索引数组,且$payment_audit_logs排在$auth_logs之前
                $auth_logs = array_merge($payment_audit_logs, $auth_logs);
                //查到支付模块数据直接返回, 没查到的继续走下边的拼接支付人逻辑(兼容开启支付模块后历史数据审批通过未支付完成的)
                return $auth_logs;
            }
        }
        $us = new UserService();
        // 审核通过 -> 待付款: 如果非下载操作, 审批流中追加一条待付款的节点数据展示
        if (!$if_download && ($detail_data['approval_status'] == Enums::PAYMENT_APPLY_STATUS_APPROVAL) && ($detail_data['pay_status'] == Enums::PAYMENT_PAY_STATUS_PENDING)) {
            $payPendingLogs = [
                'action_name'       => self::$t->_(Enums::$payment_pay_status[$detail_data['pay_status']]),
                'audit_at'          => $detail_data['approved_at'],
                'audit_at_datetime' => $detail_data['approved_at'],
                'action'            => 5,
                "info"              => '',
            ];

            $pay_staff_id = $this->getPayAuthStaffIdItem();
            if ($pay_staff_id) {
                foreach ($pay_staff_id as $staff_id) {
                    $current = $us->getUserById($staff_id);
                    if (!empty($current) && !is_string($current)) {
                        // 待支付
                        $payPendingLogs['list'][] = [
                            'staff_id'         => $staff_id,
                            'staff_name'       => $this->getNameAndNickName($current->name, $current->nick_name ?? ''),
                            'staff_department' => $current->getDepartment()->name ?? '',
                            'job_title'        => $current->getJobTitle()->name ?? '',
                        ];
                    }
                }

                array_unshift($auth_logs, $payPendingLogs);
            }

        }
        // 审核通过 -> 已付款 / 未付款
        if (($detail_data['approval_status'] == Enums::PAYMENT_APPLY_STATUS_APPROVAL) && in_array($detail_data['pay_status'], [Enums::PAYMENT_PAY_STATUS_PAY, Enums::PAYMENT_PAY_STATUS_NOTPAY])) {
            // 获取申请支付人信息
            $current = $us->getUserById($detail_data['pay_info']['pay_staff_id']);

            // 审批流 追加一条 付款节点数据
            if ($current) {
                $payLogs = [
                    'staff_id'          => $detail_data['pay_info']['pay_staff_id'], //实际支付人工号
                    'staff_name'        => $this->getNameAndNickName($current->name, $current->nick_name ?? ''),
                    'staff_department'  => $current->getDepartment()->name ?? '',
                    'job_title'         => $current->getJobTitle()->name ?? '',
                    'action_name'       => self::$t->_(Enums::$payment_pay_status[$detail_data['pay_status']]),
                    'audit_at'          => $detail_data['pay_info']['pay_at'],
                    'audit_at_datetime' => $detail_data['pay_info']['pay_at'],
                    'action'            => 5,
                    "info"              => '',
                ];

                array_unshift($auth_logs, $payLogs);
            }
        }

        return $auth_logs;
    }

    private function getLang()
    {
        $lang = self::$language;
        if (empty($lang) || !in_array($lang, ["th", "en", "zh-CN"], 1)) {
            $lang = "th";
        }
        return $lang;
    }

    /**
     * 获取供应商字段信息
     * @param $extend_data_obj
     * @return array
     */
    private function getSupplierInfo($extend_data_obj)
    {
        // 应前端要求，数据为空时返回 null，不要 []
        if (empty($extend_data_obj)) {
            return null;
        }
        return [
            'supplier_name'            => $extend_data_obj->supplier_name,
            'supplier_address'         => $extend_data_obj->supplier_address,
            'supplier_contacts'        => $extend_data_obj->supplier_contacts,
            'supplier_tel'             => $extend_data_obj->supplier_tel,
            'supplier_email'           => $extend_data_obj->supplier_email,
            'supplier_bk_name'         => $extend_data_obj->supplier_bk_name,
            'supplier_bk_account'      => $extend_data_obj->supplier_bk_account,
            'supplier_bk_account_name' => $extend_data_obj->supplier_bk_account_name,
            'supplier_tax_number'      => $extend_data_obj->supplier_tax_number,
            'sap_supplier_no'          => $extend_data_obj->sap_supplier_no,
            'is_clearance'             => $extend_data_obj->is_clearance == OrdinaryPaymentEnums::QUERY_CLEARANCE_QUEST_NULL ? '' : $extend_data_obj->is_clearance,
            'actual_clearance_date'    => !empty($extend_data_obj->actual_clearance_date)
                ? date('Y-m-d', strtotime($extend_data_obj->actual_clearance_date))
                : '',
            'expect_clearance_date'    => !empty($extend_data_obj->expect_clearance_date)
                ? date('Y-m-d', strtotime($extend_data_obj->expect_clearance_date))
                : '',
            'clearance_no'             => $extend_data_obj->clearance_no,
        ];
    }

    /**
     * 获取支付字段信息
     * @param $extend_data_obj
     * @return array|null
     */
    private function getPayInfo($extend_data_obj)
    {
        // 应前端要求，数据为空时返回 null，不要 []
        if (empty($extend_data_obj) || $extend_data_obj->is_pay == 0) {
            return null;
        }
        return [
            'is_pay'           => $extend_data_obj->is_pay,
            'pay_staff_id'     => $extend_data_obj->pay_staff_id,
            'pay_bk_name'      => $extend_data_obj->pay_bk_name,
            'pay_bk_account'   => $extend_data_obj->pay_bk_account,
            'pay_signer_name'  => $extend_data_obj->pay_signer_name,
            'pay_bk_flow_date' => $extend_data_obj->pay_bk_flow_date,
            'remark'           => $extend_data_obj->remark,
            'pay_at'           => $extend_data_obj->pay_at,
        ];
    }

    /**
     * 获取金额详情-附件列表数据
     * @param array $amount_detail_ids
     * @return array
     */
    private function getAttachmentListByDetailIds(array $amount_detail_ids)
    {


        // 根据金额明细ID关联获取附件数据
        $attachment_list_detail_id_map = [];
        if (is_array($amount_detail_ids) && !empty($amount_detail_ids)) {
            $file = AttachModel::find([
                'conditions' => 'oss_bucket_key IN ({oss_bucket_key:array}) AND oss_bucket_type = :oss_bucket_type:',
                'bind'       => ['oss_bucket_key' => $amount_detail_ids, 'oss_bucket_type' => Enums::OSS_BUCKET_TYPE_ORDINARYPAYMENT],
                'columns'    => ['bucket_name', 'object_key', 'file_name', 'oss_bucket_key'],
            ]);

            $attach_list = $file ? $file->toArray() : [];
            if ($attach_list) {
                foreach ($attach_list as $attach_item) {
                    $attachment_list_detail_id_map[$attach_item['oss_bucket_key']][] = $attach_item;
                }
            }


        }
        return $attachment_list_detail_id_map;

    }

    /**
     * @param $id
     * @param $uid
     * @return array
     */
    public function getAuditDetail($id, $uid = 0)
    {
        $code = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $ordinary_detail = [];
        try {
            $ordinary_detail = $this->getDetail($id, $uid, $is_audit = false);
            if (empty($ordinary_detail['id'])) {
                throw new BusinessException('获取合同信息失败', ErrCode::$CONTRACT_GET_INFO_ERROR);
            }

            $ordinary_detail_re= (new OrdinaryPaymentFlowService())->getRequest($id);
            if (empty($ordinary_detail_re->id)) {
                throw new BusinessException('获取工作流批次失败', ErrCode::$CONTRACT_GET_WORK_REQUEST_ERROR);
            }

            //待回复征询ID
            $ask = (new FYRService())->getRequestToByReplyAsk($ordinary_detail_re,$uid);

            $ordinary_detail['ask_id'] = $ask ? $ask->id : '';

        } catch (ValidationException $e) {
            $code = $e->getCode();
            $message = $e->getMessage();

        } catch (BusinessException $e) {
            $code = $e->getCode();
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        }

        if (!empty($real_message)) {
            $this->logger->warning('contract-get-audit-detail-failed:' . $real_message);
        }

        return [
            'code' => $code,
            'message' => $message,
            'data' => $ordinary_detail,
        ];
    }


    /**
     * 获得导出模板的头，及字段
     * @return array
     */
    public function getImportTplAndField(){
        $field = [
            'budget_name',    //付款分类名字
            'product_name',   //费用类型名字
            'cost_start_date',  //费用发生开始时间
            'cost_end_date',    //费用发生结束时间
            'amount_no_tax',    //不含税金额
            'vat7_rate',        //VAT税率
            //'amount_vat',       //VAT税额
            //'amount_have_tax',  //含税金额，自动计算。
            'wht_category_name',     //wht类别
            'wht_rate',          //wht税率
            //'amount_wht',        //wht金额,自动计算
            'cost_store_name',   //费用所属网点,
            'contract_no',        //合同编号
            'pono', //采购订单编号
        ];

        $header = [
            self::$t->_('cost_category'),
            self::$t->_('payment_store_renting_cost_type'),
            self::$t->_('re_filed_start_at'),
            self::$t->_('re_filed_end_at'),
            self::$t->_('purchase_order_product_field_total_price'),
            self::$t->_('payment_store_renting_vat_rate'),
            //static::$t->_('payment_store_renting_vat_amount'),
            self::$t->_('payment_store_renting_wht_category'),
            self::$t->_('payment_store_renting_wht_category_tax'),
            //self::$t->_('payment_store_renting_wht_amount'),
            self::$t->_('re_field_cost_store_name'),
            self::$t->_('payment_store_renting_contract_no'),
            self::$t->_('ordinary_import_pono'),
        ];
        return ['header'=>$header,'field'=>$field];
    }




    public function getImportTpl(){
        $header = $this->getImportTplAndField()['header'];
        $code = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $data = [];

        try {
            $res = $this->exportExcel($header,[]);
            $data = $res['data'];
        }catch (Exception $e){
            $this->logger->error('getImportTpl error==='.$e->getMessage());
            $code = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
        }

        return [
            'code' => $code,
            'message' => $message,
            'data' => $data,
        ];
    }


    /**
     * 导入模板，要根据人的属性，判断一些数据
     *
     * @param array $params
     * @param $data
     * @return array
     * @throws BusinessException
     * @throws GuzzleException
     */
    public function import($params = [], $data)
    {
        ini_set('memory_limit', '1024M');

        $code = ErrCode::$SUCCESS;
        $message = '';

        $staff_id = $params['apply_staff_id'];
        $cost_store_type = $params['cost_store_type'];
        $cost_department_id = $params['cost_department_id'];
        $account_type = $params['account_type'];
        $cost_company_id = $params['cost_company_id'];

        $tmp = $this->getImportTplAndField();

        $header = $tmp['header'];
        $field = $tmp['field'];

        $whtTax = EnumsService::getInstance()->getWhtRateMap();
        $whtNameToId = EnumsService::getInstance()->getWhtRateCategoryMap(1, true);

        $errorNum = 0;
        $is_department_store_flag = 0;  //0都可以，只要网点存在就行。1是总部，2是网点

        try {
            if (count($data) > self::AMOUNT_DETAIL_MAX_UPLOAD_TOTAL) {
                throw new ValidationException(self::$t['ordinary_payment_max_upload_error']);
            }

            $user = OrdinaryPaymentAddService::getInstance()->getUser($staff_id);
            if ($user['code'] != 1) {
                throw new ValidationException(self::$t->_("ordinary_payment_not_found_user"));
            }
            $user = $user['data'];
            $apply_store_name = $user['apply_store_name'];      //申请人所属网点名字
            $apply_store_id = $user['apply_store_id'];          //申请人所属网点id
            $apply_center_code = $user['apply_center_code'];    //盛情人对应pc_code，是网点的时候用
            $cost_store_type_list = $user['cost_store_type_list'];
            if (count($cost_store_type_list) == 2) {
            } else {
                $is_department_store_flag = $cost_store_type_list[0]['id'];
                if ($is_department_store_flag != $cost_store_type) {
                    throw new ValidationException(self::$t->_("ordinary_payment_cost_store_type_error"));
                }
            }

            //V22269 总部时-根据申请人可选择范围为总部或者前端用户自己选择的总部-获取申请人权限范围下的部门清单
            $staff_can_select_department_list = [];
            //是否为金蝶公司
            $is_kingdee                       = false;
            if ($is_department_store_flag == 1 || ($is_department_store_flag == 0 && $cost_store_type == 1)) {
                $staff_can_select_department_list = $this->getStaffCanSelectDepartmentList($user, $cost_department_id);

                //总部 - 费用所属公司属于同步金蝶BU，则取部门在OA组织架构中维护的金蝶成本中心
                $kingdee_company_ids = EnumsService::getInstance()->getSettingEnvValueIds(KingDeeEnums::KINGDEE_BU_SETTING_CODE);
                $is_kingdee = in_array($cost_company_id, $kingdee_company_ids);
            }

            $budgetList = OrdinaryPaymentAddService::getInstance()->getBudgetList(
                $cost_department_id,
                ($cost_store_type == 1 ? 2 : 1)
            );
            $budgetNameToItem = array_column($budgetList, null, 'name');
            foreach ($budgetNameToItem as $k => $v) {
                if (isset($v['products'])) {
                    $budgetNameToItem[$k]['products'] = array_column($v['products'], null, 'name');
                } else {
                    $budgetNameToItem[$k]['products'] = [];
                }
            }

            //合并成字段，顺便判断模板每行没有问题
            foreach ($data as $k => $v) {
                if (count($field) != count($v)) {
                    throw new ValidationException(self::$t->_("ordinary_payment_template_error"));
                }
                $data[$k] = array_combine($field, $v);
            }

            // vat配置动态获取, 与 手动添加明细里的vat保持取值一致
            $vat7_rate = EnumsService::getInstance()->getVatRateValueItem();
            // 获取房租的科目id
            $rent_budget_id = (new SettingEnvModel())->getValByCode('ordinary_payment_rent_budget_id');
            // 查询所有合同的作废信息
            $contract_nos = array_values(array_unique(array_column($data, 'contract_no')));
            $contract_no_replace = [];
            if (!empty($contract_nos)) {
                $contract_no_arr = ContractArchive::find([
                    'columns' => 'cno, invalid_replace_cno, invalid_replace_begin, invalid_replace_end',
                    'conditions' => 'invalid_replace_cno in ({invalid_replace_cno:array}) and status in ({status:array}) and invalid_reason_type = :invalid_reason_type:',
                    'bind' => [
                        'invalid_replace_cno' => $contract_nos,
                        'status' => [ContractEnums::CONTRACT_ARCHIVE_STATUS_INVALID, ContractEnums::CONTRACT_ARCHIVE_STATUS_INVALID_ING],
                        'invalid_reason_type' => ContractEnums::INVALID_REASON_TYPE_RE_SIGN,
                    ],
                ])->toArray();
                foreach ($contract_no_arr as $contract_replace) {
                    $contract_no_replace[$contract_replace['invalid_replace_cno']][] = $contract_replace;
                }
            }
            // 查询所有采购订单信息
            $purchase_order_nos = array_values(array_filter(array_unique(array_column($data, 'pono'))));
            $purchase_order_data = [];
            if (!empty($purchase_order_nos)) {
                $purchase_order_data = PurchaseOrderRepository::getInstance()->getOrderDataByNos($purchase_order_nos);
                $purchase_order_data = array_column($purchase_order_data, null, 'pono');
            }

            $country_code = get_country_code();//国家码
            foreach ($data as $k => $v) {
                $msg = [];
                $data[$k]['ledger_account_id'] = 0;
                $data[$k]['ledger_account_name'] = '';

                $budgetFlag = true;
                $budgetName = $data[$k]['budget_name'];
                if (empty($budgetName) || empty($budgetNameToItem[$budgetName])) {
                    $msg[] = self::$t->_("ordinary_payment_bucket_name_error");
                    $budgetFlag = false;
                } else {
                    $data[$k]['isbudget'] = $budgetNameToItem[$budgetName]['is_budget'];
                    $data[$k]['budget_id'] = $budgetNameToItem[$budgetName]['id'];
                    $data[$k]['level_code'] = $budgetNameToItem[$budgetName]['level_code'];
                }
                //如果付款分类存在，才判断，费用类型。
                if ($budgetFlag) {
                    $budget_id = $data[$k]['budget_id'];
                    $products = $budgetNameToItem[$budgetName]['products'];
                    if (empty($products)) {
                        //如果为空是对的
                        if (empty($data[$k]['product_name'])) {
                            $data[$k]['product_id'] = 0;
                            $data[$k]['product_name'] = '';
                            $data[$k]['pno'] = '';

                        } else {
                            //否则是错误的
                            $msg[] = self::$t->_("ordinary_payment_product_name_error");
                        }
                    } else {
                        $product_name = $data[$k]['product_name'];
                        if (empty($product_name) || empty($products[$product_name])) {
                            $msg[] = self::$t->_("ordinary_payment_product_name_error");
                        } else {
                            $data[$k]['product_id'] = $products[$product_name]['id'];
                            $data[$k]['pno'] = $products[$product_name]['pno'];

                        }
                    }
                    
                    //根据是否是快递公司 核算科目/会计科目 进行取值
                    if ($account_type == KingDeeEnums::ACCOUNT_TYPE_SUB_COMPANY) {
                        $account_subjects_info = AccountingRuleService::getInstance()->getAccountingSubjectsInfo(['budget_id' => $budget_id, 'product_no' => $data[$k]['pno'], 'organization_type' => $cost_store_type, 'cost_department_id' => $cost_department_id, 'organization_type_is_common' => 1]);

                        $data[$k]['ledger_account_id']   = $account_subjects_info['subjects_id'] ?? '';
                        $data[$k]['ledger_account_name'] = $account_subjects_info['subjects_name'] ?? '';

                    } else {
                        $temp                            = LedgerAccountService::getInstance()->getLedgerAccountByBudgetIdOrProdcutId(
                            $budget_id,
                            $data[$k]['product_id']
                        );
                        $data[$k]['ledger_account_id']   = $temp['data']['ledger_account_id'];
                        $data[$k]['ledger_account_name'] = $temp['data']['ledger_account_name'];
                    }

                    //财务分类
                    $finance_category = OrdinaryPaymentListService::getInstance()->findFinanceCategory($data[$k]['ledger_account_id']);
                    $data[$k]['finance_category_id'] = $finance_category['finance_category_id'] ?? 0;
                    $data[$k]['finance_category_name'] = $finance_category['finance_category_name'] ?? '';
                }

                $start_date_flag = 1;
                $tmp = $this->checkDate($data[$k]['cost_start_date']);
                if ($tmp === false) {
                    $msg[] = self::$t->_('ordinary_payment_cost_start_date_error');
                    $start_date_flag = 0;
                } else {
                    $data[$k]['cost_start_date'] = $tmp;
                }

                $tmp = $this->checkDate($data[$k]['cost_end_date']);
                if ($tmp === false) {
                    $msg[] = self::$t->_('ordinary_payment_cost_end_date_error');
                } else {
                    $data[$k]['cost_end_date'] = $tmp;
                    //结束时间 >= 开时间
                    if ($start_date_flag && $data[$k]['cost_start_date'] > $data[$k]['cost_end_date']) {
                        $msg[] = self::$t->_('ordinary_payment_cost_end_date_error');
                    }
                }

                /**
                 * 15998需求马来/17688泰国菲律宾国家不含税金额可以导入负数;
                 * 20240815邮件需求不含税金额范围输入值介于-9999999999.99至0或0,9999999999.99,不可为0
                 * 非马来国家不含税金额不可导入负数；不含税金额范围输入值介于1,9999999999.99
                 */
                $can_input_negative = in_array($country_code, [GlobalEnums::MY_COUNTRY_CODE, GlobalEnums::TH_COUNTRY_CODE, GlobalEnums::PH_COUNTRY_CODE]);
                if (!is_numeric($data[$k]['amount_no_tax']) || ($can_input_negative && !preg_match(self::AMOUNT_NOT_TAX, $data[$k]['amount_no_tax'])) || (!$can_input_negative && $data[$k]['amount_no_tax'] <= 0 || $data[$k]['amount_no_tax'] > 9999999999.99)) {
                    $msg[] = self::$t->_('ordinary_payment_amount_no_tax_error');
                }

                $data[$k]['vat7_rate'] = is_numeric($data[$k]['vat7_rate']) ? (float) sprintf("%.3f", $data[$k]['vat7_rate'] * 100) : $data[$k]['vat7_rate'];
                if (!is_numeric($data[$k]['vat7_rate']) || !in_array($data[$k]['vat7_rate'], $vat7_rate)) {
                    $msg[] = self::$t->_('ordinary_payment_vat7_rate_error');
                }

                //不含税金额
                $amount_no_tax = round($data[$k]['amount_no_tax'], 2);
                $amount_vat_rate = $data[$k]['vat7_rate'] / 100;

                //vat7%金额
                $amount_vat = round($amount_no_tax * $amount_vat_rate, 2);
                $data[$k]['amount_vat'] = $amount_vat;

                $amount_have_tax = $amount_no_tax + $amount_vat;
                $data[$k]['amount_have_tax'] = $amount_have_tax;

                if (!array_key_exists($data[$k]['wht_category_name'], $whtNameToId)) {
                    $msg[] = self::$t->_('ordinary_payment_wht_category_error');
                } else {
                    $data[$k]['wht_category'] = $whtNameToId[$data[$k]['wht_category_name']];
                    if(!is_numeric($data[$k]['wht_rate'])){
                        $msg[] = self::$t->_('ordinary_payment_wht_rate_error');
                    }else{
                        $data[$k]['wht_rate'] = (float) sprintf("%.3f", $data[$k]['wht_rate'] * 100);
                        if (!array_key_exists((string) $data[$k]['wht_rate'], $whtTax[$data[$k]['wht_category']]['rate_list'])) {
                            $msg[] = self::$t->_('ordinary_payment_wht_rate_error');
                        } else {
                            $amount_wht = round($amount_no_tax * ($data[$k]['wht_rate'] / 100), 2);
                            $data[$k]['amount_wht'] = $amount_wht;
                        }
                    }
                }

                //总部
                if ($is_department_store_flag == 1) {
                    //V22269 依据输入的名称去找对应申请员工权限下的部门范围；映射到部门信息，存储费用部门id、费用部门名称、成本中心；若找不到部门信息拦截
                    $excel_cost_store_name = $data[$k]['cost_store_name'];
                    $one_department_info   = $staff_can_select_department_list ? ($staff_can_select_department_list[$excel_cost_store_name] ?? []) : [];
                   if (empty($one_department_info)) {
                       $data[$k]['cost_store_name'] = $excel_cost_store_name;
                       //V22269 若找不到部门信息拦截
                       $msg[] = self::$t->_('ordinary_payment_cost_store_name_error_1');
                   } else {
                       //V22269 映射到部门信息，存储费用部门id、费用部门名称、成本中心
                       $data[$k]['cost_department_id']   = $one_department_info['id'];
                       $data[$k]['cost_department_name'] = $one_department_info['name'];
                       $data[$k]['cost_center_name']     = $this->getDepartmentPcCode($data[$k]['cost_department_id'], $is_kingdee);

                       //V22269 以下逻辑网点按照原先存储即可
                       $data[$k]['cost_store_name'] = $apply_store_name;
                       $data[$k]['cost_store_id'] = $apply_store_id;
                   }
                } //网点
                elseif ($is_department_store_flag == 2) {
                    if ($apply_store_name != $data[$k]['cost_store_name']) {
                        $msg[] = self::$t->_("ordinary_payment_cost_store_name_error_2");
                    } else {
                        $data[$k]['cost_store_name'] = $apply_store_name;
                        $data[$k]['cost_store_id'] = $apply_store_id;
                        $data[$k]['cost_center_name'] = $apply_center_code;
                    }
                } else {
                    //如果传过来的是总部
                    if($cost_store_type == 1){
                        //V22269 依据输入的名称去找对应申请员工权限下的部门范围；映射到部门信息，存储费用部门id、费用部门名称、成本中心；若找不到部门信息拦截
                        $excel_cost_store_name = $data[$k]['cost_store_name'];
                        $one_department_info   = $staff_can_select_department_list ? ($staff_can_select_department_list[$excel_cost_store_name] ?? []) : [];
                        if (empty($one_department_info)) {
                            $data[$k]['cost_store_name'] = $excel_cost_store_name;
                            //V22269 若找不到部门信息拦截
                            $msg[] = self::$t->_('ordinary_payment_cost_store_name_error_1');
                        } else {
                            //V22269 映射到部门信息，存储费用部门id、费用部门名称、成本中心
                            $data[$k]['cost_department_id']   = $one_department_info['id'];
                            $data[$k]['cost_department_name'] = $one_department_info['name'];
                            $data[$k]['cost_center_name']     = $this->getDepartmentPcCode($data[$k]['cost_department_id'], $is_kingdee);

                            //V22269 以下逻辑网点按照原先存储即可
                            $data[$k]['cost_store_name'] = $apply_store_name;
                            $data[$k]['cost_store_id'] = $apply_store_id;
                        }
                    }else{
                        $store = SysStoreModel::findFirst(
                            [
                                'conditions' => 'name = :store_name: and state=1',
                                'bind' => ['store_name' => $data[$k]['cost_store_name']],
                            ]
                        );
                        if (empty($store)) {
                            $msg[] = self::$t->_("ordinary_payment_cost_store_name_error_3");
                        } else {
                            $data[$k]['cost_store_name'] = $store->name;
                            $data[$k]['cost_store_id'] = $store->id;
                            $data[$k]['cost_center_name'] = $store->sap_pc_code;
                        }

                    }
                }

                //合同编号
                if (!empty($data[$k]['contract_no'])) {
                    $arr = $this->getArchiveContractByCno($data[$k]['contract_no']);
                    if (empty($arr)) {
                        $msg[] = self::$t->_("ordinary_payment_contract_no_error");
                    }
                    //校验合同被作废时间
                    if (!empty($rent_budget_id) && $budgetFlag && $data[$k]['budget_id'] == $rent_budget_id && $start_date_flag) {
                        // 查询被作废的时间段
                        if (isset($contract_no_replace[$data[$k]['contract_no']])) {
                            foreach ($contract_no_replace[$data[$k]['contract_no']] as $contract_v) {
                                $transfer_params = ['contract_no' => $contract_v['invalid_replace_cno'], 'invalid_data' => $contract_v['invalid_replace_begin'] . ' - ' . $contract_v['invalid_replace_end']];
                                //开始时间或结束时间在禁用时间内
                                if ($data[$k]['cost_start_date'] >= $contract_v['invalid_replace_begin'] && $data[$k]['cost_start_date'] <= $contract_v['invalid_replace_end']) {
                                    $msg[] = self::$t->_('cost_date_invalid_error', $transfer_params);
                                    continue;
                                }
                                if ($data[$k]['cost_end_date'] >= $contract_v['invalid_replace_begin'] && $data[$k]['cost_end_date'] <= $contract_v['invalid_replace_end']) {
                                    $msg[] = self::$t->_('cost_date_invalid_error', $transfer_params);
                                    continue;
                                }
                                //开始时间大于禁用时间开始,结束时间大于禁用时间结束
                                if ($data[$k]['cost_start_date'] <= $contract_v['invalid_replace_begin'] && $data[$k]['cost_end_date'] >= $contract_v['invalid_replace_end']) {
                                    $msg[] = self::$t->_('cost_date_invalid_error', $transfer_params);
                                    continue;
                                }
                            }
                        }
                    }
                }
                //采购订单校验
                if (!empty($data[$k]['pono'])) {
                    if (!key_exists($data[$k]['pono'], $purchase_order_data)) {
                        //采购订单编号不存在
                        $msg[] = self::$t->_('ordinary_import_pono_not_exist_error');
                    } else {
                        if ($purchase_order_data[$data[$k]['pono']]['cost_company'] != $cost_company_id) {
                            //采购订单费用所属公司与付款费用所属公司不一致
                            $msg[] = self::$t->_('ordinary_import_po_company_error');
                        }
                        if ($purchase_order_data[$data[$k]['pono']]['status'] != Enums::CONTRACT_STATUS_APPROVAL) {
                            //采购订单未审核通过
                            $msg[] = self::$t->_('ordinary_import_po_status_error');
                        }
                    }

                }

                if (!empty($msg)) {
                    $errorNum++;
                }
                $data[$k]['msg'] = implode(";", $msg);
            }

        } catch (ValidationException $e) {
            $code = ErrCode::$VALIDATE_ERROR;
            $message = $e->getMessage();
        }

        $res = [];
        $count = count($data);
        $success = $count - $errorNum;
        $res['count'] = $count;
        $res['success_num'] = $success;
        $res['error_num'] = $errorNum;
        $res['error_file'] = '';
        $res['data'] = $data;

        if (!empty($errorNum)) {
            $field[] = 'msg';
            $header[] = self::$t->_('payment_upload_result_hint');
            $excelData = [];
            foreach ($data as $item) {
                $tmpData = array_only($item, $field);
                $tmpData['vat7_rate'] .='%';
                $tmpData['wht_rate'].='%';
                //总部的话，此字段会被赋予为网点名称，但是excel是部门名称，这里转下
                $tmpData['cost_store_name'] = (($is_department_store_flag == 1 || ($is_department_store_flag == 0 && $cost_store_type == 1)) && !empty($item['cost_department_id'])) ? $item['cost_department_name'] : $tmpData['cost_store_name'];
                $excelData[] = array_values($tmpData);
            }

            $res['error_file'] = $this->exportExcel($header, $excelData)['data'];
        }
        return ['code' => $code, 'message' => $message, 'data' => $res];
    }


    /**
     * 将Excel上传后日期变数字的转换为日期
     * @param integer $digital excel上传后日期变成的数字
     * @param string $date_format 日期转换后的格式  date 2020-01-02(默认)   month  2020-01
     * @return $result string 转换后的日期
     */
    public function excelDigitalToDate($digital, $date_format = 'date')
    {
        if ($date_format == 'month') {
            return $digital > 25569 ? date('Y-m', ($digital - 25569) * 24 * 3600) : '';
        } else {
            return $digital > 25569 ? date('Y-m-d', ($digital - 25569) * 24 * 3600) : '';
        }
    }


    public function checkDate($date)
    {
        if (empty($date)) {
            return false;
        }
        if (is_numeric($date)) {
            $date = $this->excelDigitalToDate($date);
        }

        $tmp = strtotime($date);
        if ($tmp === false) {
            return $tmp;
        }
        return date("Y-m-d", $tmp);
    }

    /**
     * 押金单条数据详情处理
     * @Date: 9/27/22 3:24 PM
     * @param array $params
     * @return array
     * @author: peak pan
     **/
    public function depositDetail(array $params)
    {
        $lang = strtolower(substr(self::$language, -2));
        if (!in_array($lang, ['th', 'en', 'cn'])) {
            $lang = 'en';
        }
        $code    = ErrCode::$SUCCESS;
        $message = '';
        //获取付款申请主表信息
        $data = [];
        try {
            $main_model = OrdinaryPaymentDetail::findFirst([
                'id = :id:',
                'bind' => ['id' => $params['id']],
            ]);
            if (empty($main_model)) {
                throw new ValidationException(self::$t['cheque_account_empty_data'], ErrCode::$VALIDATE_ERROR);
            }

            $ordinary_payment = $main_model->getOrdinaryPayment();
            $data             = DepositService::getInstance()->getDepositInfo($main_model, $ordinary_payment, $params);

            $cost_company_name = '';
            if ($ordinary_payment->cost_company_id) {
                $company_info      = (new DepartmentService())->getCostCompanyByDepartmentId($ordinary_payment->cost_company_id);
                $cost_company_name = $company_info['name'] ?? '';
            }

            $data['head'] = [
                'type'                       => static::$t[DepositEnums::$deposit_modules[$params['type']]],
                'id'                         => $main_model->id,
                'apply_no'                   => $ordinary_payment->apply_no,
                'apply_id'                   => $ordinary_payment->apply_id,
                'apply_name'                 => $ordinary_payment->apply_name,
                'apply_email'                => $ordinary_payment->apply_email,
                'cost_department_id'         => $ordinary_payment->cost_department_id,
                'cost_department_name'       => $ordinary_payment->cost_department_name,
                'apply_node_department_name' => $ordinary_payment->apply_node_department_name,
                'create_company_name'        => $cost_company_name,
                'cost_store_type'            => static::$t[Enums::$payment_cost_store_type[$ordinary_payment->cost_store_type]],
                'currency'                   => static::$t[GlobalEnums::$currency_item[$ordinary_payment->currency]],//币种
            ];
        } catch (ValidationException $e) {
            $code    = $e->getCode();
            $message = $e->getMessage();

        } catch (Exception $e) {
            $code         = ErrCode::$SYSTEM_ERROR;
            $message      = static::$t->_('retry_later');
            $real_message = $e->getMessage() . $e->getTraceAsString();
            $this->logger->warning('押金管理-普通付款-获取数据详情信息:' . $real_message);
        }

        return [
            'code'    => $code,
            'message' => $message,
            'data'    => $data,
        ];

    }

    /**
     * 押金列表分页列表 数据
     *
     * @Date: 8/6/22 3:28 PM
     * @param array $condition 查询条件
     * @param array $user
     * @param int $type 类型
     * @return array
     * @author: peak pan
     */
    public function getDepositList(array $condition, array $user, int $type = 0)
    {
        $page_size = empty($condition['pageSize']) ? DepositEnums::PAGE_SIZE : $condition['pageSize'];
        $page_num  = empty($condition['pageNum']) ? DepositEnums::PAGE_NUM : $condition['pageNum'];
        $offset    = $page_size * ($page_num - DepositEnums::PAGE_NUM);

        $code         = ErrCode::$SUCCESS;
        $message      = 'success';

        $data = [
            'items'      => [],
            'pagination' => [
                'current_page' => $page_num,
                'per_page'     => $page_size,
                'total_count'  => 0,
            ],
        ];

        try {
            if (isset($condition['sta_date']) && isset($condition['end_date']) && $condition['sta_date'] > $condition['end_date']) {
                throw new ValidationException(self::$t['start_and_date_error'], ErrCode::$VALIDATE_ERROR);
            }

            if (isset($condition['sta_return_date']) && isset($condition['end_return_date']) && $condition['sta_return_date'] > $condition['end_return_date']) {
                throw new ValidationException(self::$t['start_and_date_error'], ErrCode::$VALIDATE_ERROR);
            }

            $condition['uid'] = $user['id'];
            // 如果是付款支付列表, 且当前用户不在支付权限工号列表, 则返回空
            if ($type == self::LIST_TYPE_PAY) {
                $pay_staff_id = $this->getPayAuthStaffIdItem();
                if (!in_array($user['id'], $pay_staff_id)) {
                    throw new ValidationException('no auth access', ErrCode::$ORDINARY_PAYMENT_PAY_AUTH_ERROR);
                }
            }
            $builder = $this->modelsManager->createBuilder();
            if (isset($condition['source']) && $condition['source'] == DepositEnums::IS_EXPORT) {
                $columns = [
                    'op.apply_no',//申请单号
                    'op.apply_id',//申请人工号
                    'op.apply_name',//申请人姓名
                    'op.created_at',//申请日期
                    'op.cost_company_id',//费用所属公司
                    'op.cost_department_name',//费用所属部门
                    'op.cost_store_type',//费用所属网点/总部
                    'op.apply_name',//押金负责人
                    'opd.contract_no',//相关合同
                    'ca.status as contract_status',//合同押金状态
                    'ca.terminal_at as expiry_date',
                    'de.contract_no as contract_no_b', 'op.currency', 'opd.budget_id', 'opd.product_id',
                    'opd.id AS detail_id', // 明细ID
                    'opd.cost_store_name',//费用所属网点/总部
                    'opd.cost_center_name',//费用所属中心
                    'opd.cost_start_date',//费用发生期间
                    'opd.cost_start_date',//费用发生期间
                    'opd.cost_end_date',//费用结束期间
                    'opd.amount_no_tax',//不含税金额（含WHT）
                    'opd.vat_rate',//SST税率
                    'opd.amount_vat',//SST税额
                    'opd.amount_have_tax',//含税金额
                    'opd.wht_category',//WHT类
                    'opd.wht_rate',//WHT税率
                    'opd.amount_wht', '"" as sum_money',//实付金额
                    'de.deposit_money',//押金总金额
                    'dr.status',//押金归还状态
                    'de.return_money',//归还金额
                    'dr.loss_money as loss_money_return',//损失总金额
                    'dr.other_return_money',// 其他退款金额
                    'dr.other_return_info',// 其他退款说明
                    'dr.bank_flow_date',//银行流水日期
                    'dr.return_info', '"" as return_attachment',//归还详情附加
                    'dl.loss_bear_id',//损失承担方
                    'dl.loss_budget_id',//损失类型
                    'dl.loss_department_id',//损失部门名称 网点/总部
                    'dl.loss_money', '"" as loss_attachment',//损失附加
                    'de.id as deposit_id', 'dr.id as deposit_return_id', 'dl.id as deposit_loss_id',
                    'de.apply_id as deposit_create_name',
                    'de.return_status', 'dl.loss_organization_id',
                    'op.apply_node_department_name AS biz_apply_department_name',
                    'de.apply_node_department_name AS deposit_node_department_name',
                ];
            } else {
                $columns = [
                    'op.apply_no', 'opd.id', 'op.apply_id',
                    'op.apply_name', 'op.created_at',
                    'op.cost_company_id',//费用所属公司
                    'op.cost_department_name', 'op.cost_store_type',
                    'op.apply_node_department_name AS biz_apply_department_name',
                    'opd.cost_store_id',//详情的网点和总部id
                    'opd.cost_store_name',//详情的网点和总部
                    'op.create_id', 'op.create_name', 'op.cost_company_id',
                    'opd.amount_no_tax', 'opd.amount_vat', 'opd.amount_wht',
                    'op.currency', 'ca.status',
                    'de.return_money',//归还总金额
                    'de.loss_money ',//损失总结金额
                    'de.deposit_money',//押金总金额
                    'opd.contract_no', 'de.contract_no as contract_no_b', 'de.return_status',
                    'ca.terminal_at as expiry_date', 'de.apply_id as deposit_create_id',
                    'de.apply_name as deposit_create_name',
                    'de.apply_node_department_name AS deposit_node_department_name',
                ];
            }
            $builder->from(['opd' => OrdinaryPaymentDetail::class]);
            //组合搜索条件
            $builder = $this->getDepositCondition($builder, $condition, $type, $user);
            //如果是导出数据需要分组处理
            if (isset($condition['source']) && $condition['source'] == DepositEnums::IS_EXPORT) {
                $builder->orderBy('opd.id DESC');
            }
            $count = (int)$builder->columns('COUNT(DISTINCT opd.id) AS total')->getQuery()->getSingleResult()->total;
            if ($count) {
                $builder->columns($columns);
                if ($type == DepositEnums::LIST_TYPE_DATA_EXPORT) {
                    $builder->groupBy('dl.id,opd.id');
                } else {
                    $builder->groupBy('opd.id');
                }
                $builder->orderBy('opd.id DESC');

                $builder->limit($page_size, $offset);
                $items = $builder->getQuery()->execute()->toArray();
                $items = $this->handleDepositItems($items, $condition);
            }
            $data['items']                     = $items ?? [];
            $data['pagination']['total_count'] = $count;

        } catch (ValidationException $e) {
            $code    = $e->getCode();
            $message = $e->getMessage();

        } catch (Exception $e) {
            $code         = ErrCode::$SYSTEM_ERROR;
            $message      = static::$t->_('retry_later');
            $real_message = $e->getMessage() . $e->getTraceAsString();
            $this->logger->warning('押金管理-普通付款-列表数据:' . $real_message);
        }

        return [
            'code'    => $code,
            'message' => $message,
            'data'    => $data,
        ];
    }

    /**
     *  押金列表数据处理
     * @Date: 9/27/22 3:27 PM
     * @param array $items 数据
     * @param array $condition 条件
     * @return  array
     * @author: peak pan
     */
    private function handleDepositItems($items, $condition = [])
    {
        if (empty($items)) {
            return [];
        }

        $wht_category_arr   = array_column(EnumsService::getInstance()->getFormatWhtRateConfig(), 'label', 'value');
        $cost_company_list  = [];
        $cost_company_ids   = array_values(array_unique(array_filter(array_column($items, 'cost_company_id'))));
        if ($cost_company_ids) {
            $cost_company_arr  = (new DepartmentService())->getDepartmentInfoByIds($cost_company_ids);
            $cost_company_list = array_column($cost_company_arr, 'name', 'id');
        }

        if (isset($condition['source']) && $condition['source'] == DepositEnums::IS_EXPORT) {
            $budget_ids    = array_values(array_unique(array_filter(array_column($items, 'budget_id'))));
            $budget_id_arr = empty($budget_ids) ? [] : DepositService::getInstance()->getBudgetObjectIdByName($budget_ids);

            $product_ids    = array_values(array_unique(array_filter(array_column($items, 'product_id'))));
            $product_id_arr = empty($product_ids) ? [] : DepositService::getInstance()->getBudgetObjectProductIdByName($product_ids);

            $deposit_return_ids    = array_values(array_unique(array_filter(array_column($items, 'deposit_id'))));
            $return_attachment     = empty($deposit_return_ids) ? [] : DepositService::getInstance()->getIdByUrlAttach($deposit_return_ids, Enums::OSS_ORDINARY_PAYMENT_TYPE_DEPOSIT_ADD);
            $loss_bear_name_arr    = array_column(array_merge(DepositEnums::$vendor_arr, (new PurchaseService())->getCooCostCompany()), 'cost_company_name', 'cost_company_id');
            $loss_budget_ids       = array_values(array_unique(array_filter(array_column($items, 'loss_budget_id'))));
            $loss_budget_arr       = empty($loss_budget_ids) ? [] : DepositService::getInstance()->getBudgetIdsByname($loss_budget_ids); //损失类型
            $loss_organization_arr = DepositEnums::$organization_type;

            foreach ($items as &$item) {
                //正常
                $item['contract_no']         = $item['contract_no_b'] ? $item['contract_no_b'] : $item['contract_no'];
                $item['contract_status_id']  = $item['contract_status'];
                $item['contract_status']     = empty($item['contract_no']) ? '' : static::$t['contract_status_name_normal'];
                $item['currency']            = static::$t[GlobalEnums::$currency_item[$item['currency']]];//币种
                $item['sum_money']           = bcsub(bcadd($item['amount_no_tax'], $item['amount_vat'], 3), $item['amount_wht'], 2);//实付金额
                $item['deposit_money']       = $item['sum_money'];//押金总金额
                $item['cost_store_type']     = !empty($item['cost_store_type']) ? static::$t[Enums::$payment_cost_store_type[$item['cost_store_type']]] : '';
                $item['create_company_name'] = !empty($item['cost_company_id']) ? $cost_company_list[$item['cost_company_id']] : '';
                $item['wht_category']        = $wht_category_arr[$item['wht_category']] ?? '';
                $item['vat_rate']            = !empty($item['vat_rate']) ? $item['vat_rate'] . '%' : '0%';
                $item['wht_rate']            = $item['wht_rate'] . '%';
                $item['budget_id']           = !empty($item['budget_id']) ? $budget_id_arr[$item['budget_id']] : '';//预算分类
                $item['product_id']          = !empty($item['product_id']) && !empty($product_id_arr[$item['product_id']]) ? $product_id_arr[$item['product_id']] : '';//明细分类

                $item['status']             = empty($item['return_status']) ? DepositEnums::DEPOSIT_RETURN_STATUS_NOT : $item['return_status'];
                $item['return_status_id']   = $item['return_status'] ?? '1';
                $item['return_status']      = empty($item['return_status_id']) ? '' : static::$t[DepositEnums::$contract_return_list[$item['return_status_id']]];
                $item['deposit_apply_name'] = !empty($item['deposit_create_name']) ? $item['deposit_create_name'] : $item['apply_id'];
                $item['return_money']       = $item['return_money'] != '' ? bcdiv($item['return_money'], 1000, 2) : '';//归还金额
                $item['other_return_money'] = bcdiv($item['other_return_money'], 1000, 2); // 其他退款金额
                $item['return_attachment']  = !empty($return_attachment[$item['deposit_id']]) ?
                    implode(',', $return_attachment[$item['deposit_id']]) : '';//归还详情附加
                $item['loss_bear_id']       = !empty($item['loss_bear_id']) ? $loss_bear_name_arr[$item['loss_bear_id']] : '';//损失承担方
                $item['loss_budget_id']     = !empty($item['loss_budget_id']) ? $loss_budget_arr[$item['loss_budget_id']] : '';//损失类型
                $item['loss_department_id'] = !empty($item['loss_organization_id']) ? static::$t[$loss_organization_arr[$item['loss_organization_id']]] : '';//损失部门名称 网点/总部

                $item['loss_money']        = $item['loss_money'] != '' ? bcdiv($item['loss_money'], 1000, 2) : '';//损失金额
                $item['loss_money_return'] = $item['loss_money_return'] != '' ? bcdiv($item['loss_money_return'], 1000, 2) : ''; //损失总金额

                $item['created_at']      = show_time_zone($item['created_at'], 'Y-m-d');
                $item['cost_start_date'] = $item['cost_start_date'] . ' - ' . $item['cost_end_date'];//费用发生日期
                $item['deposit_node_department_name'] = $item['deposit_node_department_name'] ?? $item['biz_apply_department_name'];
            }
        } else {
            foreach ($items as &$item) {
                $item['contract_no']          = $item['contract_no_b'] ? $item['contract_no_b'] : $item['contract_no'];
                $item['type']                 = DepositEnums::DEPOSIT_ORDINARY_PAYMENT;
                $item['return_money']         = $item['return_money'] != '' ? bcdiv($item['return_money'], 1000, 2) : '';
                $item['deposit_money']        = $item['loss_money'] != '' ? bcdiv($item['loss_money'], 1000, 2) : '';
                $item['sum_deposit_money']    = bcsub(bcadd($item['amount_no_tax'], $item['amount_vat'], 3), $item['amount_wht'], 2);
                $item['contract_status']      = $item['status'] ?? '';
                $item['contract_status_name'] = empty($item['contract_no']) ? '' : static::$t['contract_status_name_normal'];//正常
                $item['expiry_date']          = '';
                $item['currency_text']        = static::$t[GlobalEnums::$currency_item[$item['currency']]];
                $item['cost_store_type_text'] = $item['cost_store_name'];
                $item['return_status_id']     = $item['return_status'] ?? '1';
                $item['return_status']        = empty($item['return_status_id']) ? '' : static::$t[DepositEnums::$contract_return_list[$item['return_status_id']]];
                $item['created_at']           = show_time_zone($item['created_at'], 'Y-m-d');
                $item['create_id']            = empty($item['deposit_create_id']) ? $item['apply_id'] : $item['deposit_create_id'];
                $item['create_name']          = empty($item['deposit_create_id']) ? $item['apply_name'] : $item['deposit_create_name'];
                $item['create_company_name']  = !empty($item['cost_company_id']) ? $cost_company_list[$item['cost_company_id']] : '';
                $item['deposit_node_department_name'] = $item['deposit_node_department_name'] ?? $item['biz_apply_department_name'];

                unset($item['cost_store_n_name'], $item['cost_company_id']);
            }
        }
        return $items;
    }

    /**
     *  列表复合搜索条件  押金
     *
     * @Date: 9/27/22 3:27 PM
     * @param object $builder 对象
     * @param array $condition 条件
     * @param int $type 模块类型
     * @param array $user
     * @return object
     * @throws BusinessException
     * @author: peak pan
     */
    private function getDepositCondition(object $builder, array $condition, int $type, array $user = [])
    {
        $apply_no          = $condition['apply_no'] ?? '';
        $create_company_id = $condition['create_company_id'] ?? '';
        $cost_store_type   = $condition['cost_store_type'] ?? '';
        $contract_no       = $condition['contract_no'] ?? '';
        $sta_date          = $condition['sta_date'] ?? '';
        $end_date          = $condition['end_date'] ?? '';
        $create_id         = $condition['create_name'] ?? '';
        $sta_return_date   = $condition['sta_return_date'] ?? '';
        $end_return_date   = $condition['end_return_date'] ?? '';
        $return_status     = $condition['return_status'] ?? '';

        // 是否接入通用数据权限
        $is_access_common_data_permission = $condition['is_access_common_data_permission'] ?? false;

        //付款申请列表当前登录用户提交的所有申请数据
        $builder->leftjoin(OrdinaryPayment::class, 'op.id = opd.ordinary_payment_id', 'op');
        $builder->leftjoin(DepositModel::class, 'de.detail_id = opd.id', 'de');
        $builder->leftjoin(DepositReturnModel::class, 'dr.deposit_id = de.id', 'dr');
        $builder->leftjoin(Contract::class, 'co.cno = opd.contract_no', 'co');
        $builder->leftjoin(ContractArchive::class, 'ca.cno=co.cno', 'ca');

        $builder->andWhere('de.id IS NULL OR (de.id IS NOT NULL AND de.deposit_type = :deposit_type:)', ['deposit_type' => DepositEnums::DEPOSIT_ORDINARY_PAYMENT]);

        if (in_array($type, [self::LIST_TYPE_APPLY, self::LIST_TYPE_DATA, DepositEnums::LIST_TYPE_DATA_EXPORT]) && get_country_code() == GlobalEnums::TH_COUNTRY_CODE) {
            $builder->leftjoin(OrdinaryPaymentExtend::class, 'op.id = ope.ordinary_payment_id', 'ope');
            $builder->andWhere('ope.pay_bk_flow_date >= :pay_bk_flow_date:', ['pay_bk_flow_date' => DepositEnums::DEPOSIT_PAY_AT . ' 00:00:00']);
        }

        if ($type == self::LIST_TYPE_APPLY) {
            $builder->inWhere('opd.budget_id', $condition['deposit_budget_ids']);
            $builder->andWhere('op.apply_id = :uid: or de.apply_id = :uid: ', ['uid' => $condition['uid']]);
        } else if ($type == self::LIST_TYPE_DATA) {
            $builder->inWhere('opd.budget_id', $condition['deposit_budget_ids']);
        } else if ($type == DepositEnums::LIST_TYPE_DATA_EXPORT) {
            //数据导出  和列表不一致
            $builder->leftjoin(DepositLossModel::class, 'dl.deposit_return_id = dr.id', 'dl');
            if (!empty($condition['export_type'])) {
                $builder->inWhere('opd.budget_id', $condition['deposit_budget_ids']);
                $builder->andWhere('op.apply_id = :uid:  or de.apply_id = :uid: ', ['uid' => $condition['uid']]);
            } else {
                $builder->inWhere(' opd.budget_id', $condition['deposit_budget_ids']);
            }
        }

        // 对接通用数据权限
        if ($is_access_common_data_permission === true) {
            // 业务表参数
            $table_params = [
                'type' => SettingEnums::DATA_PERMISSION_TYPE_MULTI_ENTIEY,
                'entity_item' => [
                    'biz' => [
                        'table_alias_name' => 'op',
                        'create_id_field' => 'apply_id',
                        'create_node_department_id_filed' => 'apply_node_department_id',
                        'extra_condtions' => 'de.apply_id IS NULL', // 预留, 暂时不用
                    ],
                    'deposit' => [
                        'table_alias_name' => 'de',
                        'create_id_field' => 'apply_id',
                        'create_node_department_id_filed' => 'apply_node_department_id',
                        'extra_condtions' => 'de.apply_id IS NOT NULL', // 预留, 暂时不用
                    ],
                ]
            ];
            $builder = CommonDataPermissionService::getInstance()->getCommonDataPermission($user, $builder, SysConfigEnums::SYS_MODULE_DEPOSIT_ORDINARY_PAYMENT, $table_params);
        }

        //申请编号
        if (!empty($apply_no)) {
            if (is_array($apply_no)) {
                $builder->inWhere('op.apply_no', $apply_no);
            } else {
                $builder->andWhere('op.apply_no = :apply_no:', ['apply_no' => $apply_no]);
            }
        }

        //申请时间-截止日期
        if (!empty($end_date) && !empty($sta_date)) {
            $sta_date .= ' 00:00:00';
            $end_date .= ' 23:59:59';
            $builder->betweenWhere('op.created_at', $sta_date, $end_date);
        }

        //归还-截止日期
        if (!empty($end_return_date) && !empty($sta_return_date)) {
            $sta_return_date .= ' 00:00:00';
            $end_return_date .= ' 23:59:59';
            $builder->betweenWhere('dr.return_date', $sta_return_date, $end_return_date);

            //如果有归还时间 且没有归还状态的时候 默认为已归还
            if (empty($return_status)) {
                $builder->andWhere('de.return_status = :return_status:', ['return_status' => DepositEnums::DEPOSIT_RETURN_STATUS_LAST_FILE]);
            } else if (in_array($return_status, [DepositEnums::DEPOSIT_RETURN_STATUS_NOT, DepositEnums::DEPOSIT_RETURN_STATUS_INTERVENTION, DepositEnums::DEPOSIT_RETURN_STATUS_DETERMINE])) {
                //如果有归还时间 且归还状态为 未归还、法务介入中、法务已确定的时候 为默认状态0
                $builder->andWhere('de.return_status = :return_status:', ['return_status' => DepositEnums::DEPOSIT_RETURN_STATUS_DEFAULT]);
            } else {
                //如果有归还时间 且归还状态不为未归还、法务介入中、法务已确定，空的时候 状态为当前选择的状态
                $builder->andWhere('de.return_status = :return_status:', ['return_status' => $return_status]);
            }
        } else {
            //如果有归还时间为空 且归还状态不为空按照选择的归还状态查询
            if (!empty($return_status)) {
                if ($return_status == DepositEnums::DEPOSIT_RETURN_STATUS_NOT) {
                    $builder->andWhere('(de.return_status = :return_status:) or (de.return_status is null)', ['return_status' => $return_status]);
                } else {
                    $builder->andWhere('de.return_status = :return_status:', ['return_status' => $return_status]);
                }
            }
        }

        //押金负责人
        if (!empty($create_id)) {
            $builder->andWhere('(((de.apply_id ="" or de.apply_id is null ) and (op.apply_id = :create_id: or op.apply_name = :create_id: ) ) or ((de.apply_id = :create_id: or de.apply_name = :create_id:) and  (de.apply_id !="" or de.apply_name !="")))', ['create_id' => $create_id]);
        }

        //费用所属公司
        if (!empty($create_company_id)) {
            $builder->andWhere('op.cost_company_id = :cost_company_id:', ['cost_company_id' => $create_company_id]);
        }
        if (!empty($cost_store_type)) {
            if ($cost_store_type == Enums::HEAD_OFFICE_STORE_FLAG) {
                $builder->andWhere('opd.cost_store_name = :cost_store_name:', ['cost_store_name' => DepositEnums::STORE_HEADER_OFFICE_NAME]);
            } else {
                if (isset($condition['source']) && ($condition['source'] == DepositEnums::IS_EXPORT && empty($condition['type_all']))) {
                    $cost_store_model = SysStoreModel::findFirst([
                        'id = :id:',
                        'bind' => ['id' => $cost_store_type],
                    ]);
                    if (!empty($cost_store_model->name)) {
                        $builder->andWhere('opd.cost_store_name = :cost_store_name:', ['cost_store_name' => $cost_store_model->name]);
                    }
                } else {
                    $builder->andWhere('opd.cost_store_name = :cost_store_name:', ['cost_store_name' => $cost_store_type]);
                }
            }
        }
        if (!empty($contract_no)) {

            if (is_array($contract_no)) {
                $builder->andWhere('(opd.contract_no in ({contract_no:array}) and (de.contract_no ="" or de.contract_no is null)) or (de.contract_no in ({contract_no:array}))', ['contract_no' => $contract_no]);

            } else {
                $builder->andWhere('(( (de.contract_no ="" or de.contract_no is null ) and opd.contract_no = :contract_no:) or (de.contract_no = :contract_no:))', ['contract_no' => $contract_no]);
            }
        }

        // 费用所属公司
        if (!empty($cost_company)) {
            $builder->andWhere('opd.cost_company_id = :cost_company_id:', ['cost_company_id' => $cost_company]);
        }
        $builder->andWhere('op.pay_status = :pay_status:', ['pay_status' => Enums::PAYMENT_PAY_STATUS_PAY]);

        return $builder;
    }

    /**
     * 个人收款时, 收款人信息批量导入
     *
     * @param int $staff_type
     * @param array $excel_data
     * @return array
     * @throws GuzzleException
     */
    public function importPayeeInfo(int $staff_type, array $excel_data)
    {
        $code = ErrCode::$SUCCESS;
        $message = 'success';
        $data = [
            'result_data' => [],
            'total_count' => 0,
            'success_count' => 0,
            'error_count' => 0,
            'result_file_url' => '',
        ];

        try {
            // Excel 列名
            $excel_header = array_shift($excel_data);

            // 去除空白行
            foreach ($excel_data as $key => $val) {
                if (empty(implode('', trim_array(array_slice($val, 0, 6))))) {
                    unset($excel_data[$key]);
                }
            }

            $excel_data = array_values($excel_data);
            if (empty($excel_data)) {
                throw new ValidationException(static::$t->_('bank_flow_data_empty'), ErrCode::$VALIDATE_ERROR);
            }

            $data_total = count($excel_data);
            if ($data_total > self::PAYEE_INFO_MAX_UPLOAD_TOTAL) {
                throw new ValidationException(static::$t->_('payee_info_max_upload_count', ['max_count' => self::PAYEE_INFO_MAX_UPLOAD_TOTAL]), ErrCode::$VALIDATE_ERROR);
            }

            // 文件格式基本校验
            // 个人代理 + 内部员工: 收款人工号/姓名/金额
            if (in_array($staff_type, [StaffInfoEnums::STAFF_TYPE_INNER, StaffInfoEnums::STAFF_TYPE_PERSONAL_AGENT])) {
                $file_tpl_is_error = count($excel_header) > 3;

            } else {
                // 外协: 收款人工号/姓名/银行名称/银行账户名称/银行账号/金额
                $file_tpl_is_error = count($excel_header) > 6;
            }

            if ($file_tpl_is_error) {
                throw new ValidationException(static::$t->_('payee_info_upload_tpl_error', ['staff_type' => $staff_type]), ErrCode::$VALIDATE_ERROR);
            }

            // 批量获取收款人信息
            $payee_ids = array_column($excel_data, 0);
            $payee_info_list = (new HrStaffRepository())->getStaffInfoListByStaffIds($payee_ids, StaffInfoEnums::HR_STAFF_ITEMS_BANK_NO_NAME);
            $payee_info_list = array_column($payee_info_list, null, 'staff_info_id');

            // 收款人数据 与 员工类型校验
            $staff_id_repeat_count = [];
            $result_data = [];
            $success_count = 0;
            foreach ($excel_data as $index => $row) {
                // 工号信息
                if (empty($row[0])) {
                    $excel_data[$index][] = static::$t->_('payee_info_upload_data_error_1');
                    continue;
                }

                $staff_info = $payee_info_list[$row[0]] ?? [];
                if (empty($staff_info)) {
                    $excel_data[$index][] = static::$t->_('payee_info_upload_data_error_2');
                    continue;
                }

                // 工号是否重复
                if (isset($staff_id_repeat_count[$row[0]])) {
                    $excel_data[$index][] = static::$t->_('payee_info_upload_data_error_3');
                    continue;
                }
                $staff_id_repeat_count[$row[0]]++;


                // 个人代理 + 内部员工
                if (in_array($staff_type, [StaffInfoEnums::STAFF_TYPE_INNER, StaffInfoEnums::STAFF_TYPE_PERSONAL_AGENT])) {
                    // 员工类型 与 员工属性校验
                    // 内部员工
                    if ($staff_type == StaffInfoEnums::STAFF_TYPE_INNER && !($staff_info['formal'] == StaffInfoEnums::FORMAL_IN && $staff_info['hire_type'] != StaffInfoEnums::HIRE_TYPE_PERSONAL_AGENCY)) {
                        $excel_data[$index][] = static::$t->_('payee_info_upload_data_error_12');
                        continue;
                    }

                    // 个人代理
                    if ($staff_type == StaffInfoEnums::STAFF_TYPE_PERSONAL_AGENT && !($staff_info['formal'] == StaffInfoEnums::FORMAL_IN && $staff_info['hire_type'] == StaffInfoEnums::HIRE_TYPE_PERSONAL_AGENCY)) {
                        $excel_data[$index][] = static::$t->_('payee_info_upload_data_error_13');
                        continue;
                    }

                    // 银行信息, 必填校验
                    if (empty($staff_info['bank_name'])) {
                        $excel_data[$index][] = static::$t->_('payee_info_upload_data_error_6');
                        continue;
                    }

                    if (empty($staff_info['bank_no_name'])) {
                        $excel_data[$index][] = static::$t->_('payee_info_upload_data_error_7');
                        continue;
                    }

                    if (empty($staff_info['bank_no'])) {
                        $excel_data[$index][] = static::$t->_('payee_info_upload_data_error_8');
                        continue;
                    }

                    if (empty($staff_info['name'])) {
                        $excel_data[$index][] = static::$t->_('payee_info_upload_data_error_9');
                        continue;
                    }

                    if (empty($staff_info['mobile'])) {
                        $excel_data[$index][] = static::$t->_('payee_info_upload_data_error_10');
                        continue;
                    }

                    $bank_name = $staff_info['bank_name'];
                    $bank_no_name = $staff_info['bank_no_name'];
                    $bank_no = $staff_info['bank_no'];
                    $name = $staff_info['name'];
                    $mobile = $staff_info['mobile'];

                    $amount = $row[2];

                } else {
                    // 外协
                    // 员工类型 与 员工属性校验
                    if ($staff_info['formal'] != StaffInfoEnums::FORMAL_NOT_IN) {
                        $excel_data[$index][] = static::$t->_('payee_info_upload_data_error_11');
                        continue;
                    }

                    if (empty($staff_info['name'])) {
                        $excel_data[$index][] = static::$t->_('payee_info_upload_data_error_9');
                        continue;
                    }

                    if (empty($staff_info['mobile'])) {
                        $excel_data[$index][] = static::$t->_('payee_info_upload_data_error_10');
                        continue;
                    }

                    $bank_name = !empty($row[2]) ? $row[2] : $staff_info['bank_name'];
                    $bank_no_name = !empty($row[3]) ? $row[3] : $staff_info['bank_no_name'];
                    $bank_no = !empty($row[4]) ? $row[4] : $staff_info['bank_no'];
                    $name = $staff_info['name'];
                    $mobile = $staff_info['mobile'];

                    $amount = $row[5];
                }

                // 金额格式
                if (empty($amount)) {
                    $excel_data[$index][] = static::$t->_('payee_info_upload_data_error_4');
                    continue;
                }

                if (!preg_match('/^([1-9][0-9]{0,7})?(\.{1}[0-9]{1,2})?$/', $amount)) {
                    $excel_data[$index][] = static::$t->_('payee_info_upload_data_error_5');
                    continue;
                }

                $success_count++;
                $excel_data[$index][] = static::$t->_('excel_result_success');

                $result_data[] = [
                    'staff_info_id' => $row[0],
                    'bank_name' => $bank_name,
                    'bank_no' => $bank_no,
                    'bank_no_name' => $bank_no_name,
                    'mobile' => $mobile,
                    'name' => $name,
                    'amount' => (string)$amount,
                ];
            }

            // 创建返回文件
            $excel_header[] = static::$t->_('re_field_deal_res');
            $result_file_name = 'BatchUploadPayeeInfoResult_' . date('YmdHis') . '.xlsx';
            $result_file_url = $this->exportExcel($excel_header, $excel_data, $result_file_name)['data'];
            if (empty($result_file_url)) {
                throw new BusinessException('结果文件生成失败', ErrCode::$BUSINESS_ERROR);
            }

            $data['result_data'] = $result_data;
            $data['total_count'] = $data_total;
            $data['success_count'] = $success_count;
            $data['error_count'] = $data_total - $success_count;
            $data['result_file_url'] = $result_file_url;

        } catch (ValidationException $e) {
            $code = $e->getCode();
            $message = $e->getMessage();

        } catch (BusinessException $e) {
            $code = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $this->logger->warning('普通付款-批量导入收款人信息异常, 原因可能是: ' . $e->getMessage());
        } catch (Exception $e) {
            $code = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $this->logger->error('普通付款-批量导入收款人信息异常, 原因可能是: ' . $e->getMessage());
        }

        return [
            'code' => $code,
            'message' => $message,
            'data' => $data,
        ];

    }

    /**
     * 导出某单据的收款人信息
     *
     * @param int $id
     * @return array
     */
    public function exportPayeeInfo(int $id)
    {
        $code = ErrCode::$SUCCESS;
        $message = 'success';
        $data = [
            'file_url' => '',
        ];

        try {
            // 获取单据信息
            $model = OrdinaryPayment::getFirst($id);
            if (empty($model)) {
                throw new ValidationException(static::$t->_('data_not_exist_by_id', ['id' => $id]), ErrCode::$VALIDATE_ERROR);
            }

            // 获取收款人信息
            $payee_list = $model->getPersons()->toArray();

            // 按工号升序
            $payee_list = array_sort($payee_list, 'staff_info_id', SORT_ASC);

            $excel_data = [];
            foreach ($payee_list as $payee) {
                $excel_data[] = [
                    $payee['staff_info_id'],
                    $payee['bank_name'],
                    $payee['bank_no_name'],
                    $payee['bank_no'],
                    $payee['name'],
                    $payee['mobile'],
                    $payee['amount'],
                ];
            }

            // 生成Excel
            $excel_header = [
                static::$t->_('ordinary_payment_number'),// 收款人工号
                static::$t->_('ordinary_payment_bank_name'),// 银行名称
                static::$t->_('ordinary_payment_account_name'),// 银行账户名
                static::$t->_('ordinary_payment_account_number'),// 银行账号
                static::$t->_('ordinary_payment_payee_name'),// 联系人
                static::$t->_('ordinary_payment_payee_tel'),// 联系方式
                static::$t->_('ordinary_payment_payee_amount'),// 金额
            ];

            $file_name = 'DownloadPayeeInfoResult_' . date('YmdHis') . '.xlsx';
            $file_url = $this->exportExcel($excel_header, $excel_data, $file_name)['data'];
            if (empty($file_url)) {
                throw new ValidationException(static::$t->_('download_fail_error'), ErrCode::$VALIDATE_ERROR);
            }

            $data['file_url'] = $file_url;

        } catch (ValidationException $e) {
            $code = $e->getCode();
            $message = $e->getMessage();

        } catch (Exception $e) {
            $code = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $this->logger->error('普通付款-导出收款人信息异常, 原因可能是: ' . $e->getMessage());
        }

        return [
            'code' => $code,
            'message' => $message,
            'data' => $data,
        ];
    }

    /**
     * 补历史CostStoreId字段数据
     * @return void
     */
    public function dealHistoryCostStoreId()
    {
        $builder = $this->modelsManager->createBuilder();
        $builder->columns('id,sap_pc_code');
        $builder->from(\App\Modules\Organization\Models\SysStoreModel::class);
        $builder->where('state = 1');
        $builder->groupBy('sap_pc_code');
        $builder->having('count(*)=1');
        $storeFind  = $builder->getQuery()->execute()->toArray();
        $storeData  = array_column($storeFind, 'id', 'sap_pc_code');
        $pageSize   = 2000;
        $modelName  = (new OrdinaryPaymentDetail)->getSource();
        $pageNumber = 1;
        while (true) {
            $list = $this->getCostForDealHistoryCostStoreId($pageSize, $pageNumber);
            if (empty($list)) {
                break;
            }
            echo '当前处理总数' . count($list) . PHP_EOL;
            foreach ($list as $item) {
                if (empty($storeData[$item['cost_center_name']])) {
                    continue;
                }
                $this->db_oa->updateAsDict($modelName, [
                    'cost_store_id' => $storeData[$item['cost_center_name']],
                ], [
                    'conditions' => 'id = ' . $item['id'],
                ]);
            }
            ++$pageNumber;
        }
    }

    /**
     * 补历史CostStoreId字段数据查询
     * @param $pageSize
     * @param $pageNumber
     * @return mixed
     */
    private function getCostForDealHistoryCostStoreId($pageSize, $pageNumber)
    {
        return OrdinaryPaymentDetail::find([
            'columns'    => 'id,cost_center_name',
            'conditions' => 'cost_store_id=""',
            'limit'      => $pageSize,
            'offset'     => $pageSize * ($pageNumber - 1),
        ])->toArray();
    }

    /**
     * V22269 总部时-根据申请人可选择范围为总部或者前端用户自己选择的总部-获取申请人权限范围下的部门清单
     * @param array $user 申请人信息组
     * @param int $cost_department_id 单据头上用户选择的费用所属部门ID
     * @return array
     */
    private function getStaffCanSelectDepartmentList(array $user, int $cost_department_id): array
    {
        //可以编辑费用总部/网点配置的用户
        $change_cost_all_type_by_staff_ids = EnumsService::getInstance()->getSettingEnvValueIds('change_cost_all_type_by_staff_ids');

        //普通付款模块-可拆分费用部门工号，支持配置多个工号
        $ordinary_payment_split_department_staff_ids = EnumsService::getInstance()->getSettingEnvValueIds('ordinary_payment_split_department_staff_ids');

        $apply_id = $user['apply_id'];
        $staff_can_select_department_list = [];
        if (!in_array($apply_id, $change_cost_all_type_by_staff_ids) && !in_array($apply_id, $ordinary_payment_split_department_staff_ids)) {
            //用户不属于配置的可以编辑费用总部/网点配置的用户并且不属于普通付款可拆分费用部门工号;只允许为表头中的费用部门
            $staff_can_select_department_list = (new DepartmentRepository())->getDepartmentDetail($cost_department_id);
            $staff_can_select_department_list = $staff_can_select_department_list ? [$staff_can_select_department_list] : $staff_can_select_department_list;
        } elseif (in_array($apply_id, $change_cost_all_type_by_staff_ids) && !in_array($apply_id, $ordinary_payment_split_department_staff_ids)) {
            //用户属于配置的可编辑费用总部/网点配置的用户并且不属于普通付款可拆分费用部门工号;只允许为表头中的费用部门
            $staff_can_select_department_list = (new DepartmentRepository())->getDepartmentDetail($cost_department_id);
            $staff_can_select_department_list = $staff_can_select_department_list ? [$staff_can_select_department_list] : $staff_can_select_department_list;
        } elseif (!in_array($apply_id, $change_cost_all_type_by_staff_ids) && in_array($apply_id, $ordinary_payment_split_department_staff_ids)) {
            //用户不属于配置的可编辑费用总部/网点配置的用户并且属于普通付款可拆分费用部门工号;只允许为组织架构中所有未删除的部门名称
            $staff_can_select_department_list = (new DepartmentRepository())->getDepartmentList();
        } elseif (in_array($apply_id, $change_cost_all_type_by_staff_ids) && in_array($apply_id, $ordinary_payment_split_department_staff_ids)) {
            //用户属于配置的可编辑费用总部/网点配置的用户并且属于普通付款可拆分费用部门工号;只允许为组织架构中所有未删除的部门名称
            $staff_can_select_department_list = (new DepartmentRepository())->getDepartmentList();
        }
        return $staff_can_select_department_list ? array_column($staff_can_select_department_list, null, 'name') : $staff_can_select_department_list;
    }

    /**
     * V22269 总部时-费用所属中心取值逻辑
     * @param int $cost_department_id 费用部门id
     * @param bool $is_kingdee true金蝶公司，false否
     * @return string
     */
    private function getDepartmentPcCode(int $cost_department_id, bool $is_kingdee): string
    {
        if ($is_kingdee) {
            //费用所属公司属于同步金蝶BU;取总部中部门在OA组织架构中维护的金蝶成本中心
            $cost_center_code = (new CommonDepartmentService())->getDepartmentKingDeePcCode($cost_department_id);
        } else {
            //费用所属公司属于sap同步公司;取总部中部门在OA组织架构中维护的SAP成本中心
            //费用所属公司不属于上述场景;取总部中部门在OA组织架构中维护的SAP成本中心
            $cost_center_code = OrdinaryPaymentAddService::getInstance()->getPcCode($cost_department_id, 1)['data']['pc_code'] ?? '';
        }
        return $cost_center_code;
    }


    /**
     * 处理附件地址
     * @param $attachment_list
     * @return string|null
     */
    private function dealAttachmentList($attachment_list): ?string
    {
        if (empty($attachment_list)) {
            return null;
        }
        $config     = $this->getDI()->get('config');
        $img_prefix = $config->application->img_prefix ?? '';

        $result = [];
        foreach ($attachment_list as $attachment) {
            $result[] = $img_prefix . $attachment['object_key'];
        }
        return implode(PHP_EOL, $result);
    }


    /**
     * 导出金额详情
     * @param $id
     * @return string[]
     * @throws BusinessException
     * @throws GuzzleException
     * @throws ValidationException
     */
    public function downloadAmountDetail($id,$uid): array
    {

        $data = $this->getDetail($id, $uid);

        $list = $data['amount_detail_item'];
        $excel_data = [];
        $i = 0;

        foreach ($list as $item) {
            $i ++;
            $attachment_list =$this->dealAttachmentList($item['attachment_list']);
            $excel_data[]                  = [
                $i,
                $item['budget_name'],
                $item['product_name'],
                $item['finance_category_name'],
                $item['ledger_account_name'],
                $item['voucher_description'],
                $item['cost_start_date'].' ～ '.$item['cost_end_date'],
                $item['amount_no_tax'],
                $item['vat_rate'].'%',
                $item['amount_vat'],
                $item['amount_have_tax'],
                $item['wht_category_name'],
                $item['wht_rate_name'],
                $item['amount_wht'],
                $item['cost_store_name'],
                $item['cost_center_name'],
                $item['deductible_vat_tax'],//可抵扣VAT税率
                bcdiv($item['deductible_tax_amount'], 100, 2),//可抵扣税额
                $item['contract_no'],// oa相关合同 ???
                $item['pono'],//关联采购订单 ???
                $attachment_list,//附件
            ];
        }

        $vat_sst_rate_key   = 'deposit_vat_rate';
        $vat_sst_amount_key = 'vat_amount';
        if (EnumsService::getInstance()->getVatSStRateName() == GlobalEnums::SST_RATE_NAME) {
            $vat_sst_rate_key   = 'deposit_sst_rate';
            $vat_sst_amount_key = 'deposit_sst_amount';
        }
        $excel_header = [
            static::$t->_('global.no'),        // 序号
            static::$t->_('payment_classification'),     // 付款分类
            static::$t->_('types_of_expense'),  // 费用类型
            static::$t->_('finance_category'),//财务分类
            static::$t->_('gl_account'),    //
            static::$t->_('document_description'),    //
            static::$t->_('period_of_expense'),    //
            static::$t->_('total_amount_excluding_tax'),    //不含税金额
            static::$t->_($vat_sst_rate_key),    // VAT税率 SST税率
            static::$t->_($vat_sst_amount_key),    // VAT金额 SST税额
            static::$t->_('deposit_amount_have_tax'),    //含税金额
            static::$t->_('deposit_wht_category'),    //WHT类别
            static::$t->_('deposit_wht_rate'),    //WHT税率
            static::$t->_('deposit_wht_amount'),    //WHT金额
            static::$t->_('deposit_cost_store_type'),    //费用所属网点/总部
            static::$t->_('deposit_cost_center_name'),    //费用所属中心
            static::$t->_('purchase_payment_invoice_head_24'),    //可抵扣VAT税率
            static::$t->_('purchase_payment_field_deductible_vat_amount'),    //可抵扣税额
            static::$t->_('purchase_order_field_cno'),    //OA相关合同
            static::$t->_('ordinary_import_pono'),    //关联采购订单
            static::$t->_('attachment'),    //附件

        ];

        $file_name = 'DownloadPayeeInfoAmountDetailResult_' . date('YmdHis') . '.xlsx';
        $file_url  = $this->exportExcel($excel_header, $excel_data, $file_name)['data'];
        if (empty($file_url)) {
            throw new ValidationException(static::$t->_('download_fail_error'), ErrCode::$VALIDATE_ERROR);
        }
        return ['file_url' => $file_url];
    }



}
