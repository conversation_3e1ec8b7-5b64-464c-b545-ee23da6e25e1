<?php

namespace App\Modules\OrdinaryPayment\Services;

use App\Library\Enums;
use App\Library\Enums\GlobalEnums;
use App\Library\Enums\KingDeeEnums;
use App\Library\Enums\OrdinaryPaymentEnums;
use App\Library\ErrCode;
use App\Library\Validation\ValidationException;
use App\Modules\Budget\Services\BudgetWithholdingService;
use App\Modules\Common\Models\EnvModel;
use App\Modules\Common\Services\EnumsService;
use App\Modules\Contract\Models\Contract;
use App\Modules\Contract\Models\ContractArchive;
use App\Modules\Organization\Models\SysStoreModel;
use App\Modules\User\Models\DepartmentModel;
use App\Modules\User\Services\UserService;
use App\Modules\Reimbursement\Models\Pccode;
use App\Modules\Common\Services\StoreService;
use App\Modules\Budget\Services\BaseService as BudgetBaseService;

use App\Modules\Store\Models\SysStore;


class BaseService extends \App\Library\BaseService
{
    // 金额详情明细每次最大允许上传的数量
    const AMOUNT_DETAIL_MAX_UPLOAD_TOTAL = 500;

    // 收款人信息每次最多导入数据
    const PAYEE_INFO_MAX_UPLOAD_TOTAL = 500;

    // 列表类型
    const LIST_TYPE_APPLY = 1; //付款申请
    const LIST_TYPE_AUDIT = 2; //付款审核
    const LIST_TYPE_PAY = 3; //付款支付
    const LIST_TYPE_DATA = 4; //数据查询
    const LIST_TYPE_CONSULTED_REPLY = 5; //征询回复

    const PAYEE_TYPE_1 = 1;//收款人类型1供应商
    const PAYEE_TYPE_2 = 2;//个人
    const AMOUNT_NOT_TAX = '/^(?:[-+]?[1-9]\d{0,9}(?:\.\d{1,2})?|[-+]?0\.\d{1,2})$/';//不含税金额值集范围-9999999999.99至0,或者是0至9999999999.99,不可为空0

    public static $validate_department = [
        'department_id' => 'Required|IntGe:1',
    ];
    public static $validate_apply_staff_id = [
        'apply_staff_id' => 'Required|IntGe:1',
    ];

    // 支付信息校验规则
    public static $validate_pay_param = [
        'id'               => 'Required|IntGe:1|>>>:id error',
        'is_pay'           => 'Required|IntIn:2,3|>>>:whether paid error',
        'pay_bk_name'      => 'IfIntEq:is_pay,2|Required|StrLenGeLe:1,200|>>>:payment bank error',
        'pay_bk_account'   => 'IfIntEq:is_pay,2|Required|StrLenGeLe:1,200|>>>:payment bank account error',
        'pay_signer_name'  => 'IfIntEq:is_pay,2|Required|StrLenGeLe:1,500|>>>:signer name error',
        'pay_bk_flow_date' => 'IfIntEq:is_pay,2|Required|Date|>>>:bank flow date error',
        'remark'           => 'IfIntEq:is_pay,3|Required|StrLenGeLe:1,1000|>>>:remark error',
    ];

    // 详情查看校验
    public static $validate_detail_param = [
        'id' => 'Required|IntGe:1|>>>:id error',
    ];

    // url校验
    public static $validate_url_param = [
        'name' => 'Required|StrLenGeLe:1,256|>>>:error',
        'url'  => 'Required|StrLenGeLe:64,1024|>>>:error',
    ];

    // 合同编号搜索验证规则
    public static $validate_contract_search_param = [
        'cno' => 'Required|StrLenGeLe:1,20|>>>:cno error',
    ];

    // 网点搜索验证规则
    public static $validate_store_search_param = [
        'store_name' => 'Required|StrLenGeLe:1,255|>>>:store_name error',
    ];

    public static $validate_budget = [
        'cost_department_id' => 'Required|IntGe:1',
        'cost_store_type' => 'Required|IntIn:1,2', // 1 :总部,2:网点
    ];

    public static $validate_update = [
        'id' => 'Required|IntGe:1|>>>:id error',
    ];

    public static $validate_supplement = [
        'id' => 'Required|IntGe:1',
        'required_supplement_file'=>'Required|Arr'
    ];

    //审核 通过字段 数据校验
    public static $validate_approve_amount_detail_param = [
        'amount_detail'                 => 'Required|Arr|ArrLenGe:1|>>>:amount detail error',//金额详情
        'amount_detail[*].detail_id'    => 'Required|IntGt:0|>>>: amount detail id error',
        'amount_detail[*].wht_category' => 'Required|IntGe:0|>>>:WHT category error', //WHT 类别
        'amount_detail[*].wht_rate'     => 'Required|FloatGe:0|>>>:WHT rate error', //WHT 税率
        'amount_detail[*].ledger_account_id' => 'IntGe:0',                                //核算科目id
    ];

    //供应商字段参数校验
    public static $validate_vendor_param = [
        'supplier_id'              => 'Required|StrLenGeLe:1,32|>>>:supplier id length error',//供应商编号
        'supplier_name'            => 'Required|StrLenGeLe:1,200|>>>:supplier name length error',//供应商名称
        'supplier_address'         => 'Required|StrLenGeLe:1,1000|>>>:supplier address length error',//供应商地址
        'supplier_contacts'        => 'Required|StrLenGeLe:1,200|>>>:supplier contacts length error',//供应商联系人
        'supplier_tel'             => 'Required|StrLenGeLe:1,200|>>>:supplier tel length error',//供应商号码
        'supplier_email'           => 'Required|StrLenGeLe:1,200|>>>:supplier email error',//供应商邮箱
        'supplier_bk_name'         => 'Required|StrLenGeLe:1,200|>>>:supplier bank name length error',//银行名称
        'supplier_bk_account'      => 'Required|StrLenGeLe:1,200|>>>:supplier bank account length error',//银行账户号
        'supplier_bk_account_name' => 'Required|StrLenGeLe:1,200|>>>:supplier bank account name length error',//收款人银行账户名称
        'supplier_tax_number'       => 'StrLenGeLe:0,30|>>>:supplier tax number length error',//供应商税务号
    ];

    //收款类型 个人字段验证
    public static $validate_person_param = [
        'personal_detail' => 'Required|Arr|ArrLenGe:1|>>>:personal detail error',//收款人信息
        'personal_detail[*]' => 'Required|Obj',
        'personal_detail[*].staff_info_id' => 'Required|StrLenGeLe:1,32|>>>:staff_info_id error',//收款人工号
        'personal_detail[*].bank_name' => 'StrLenGeLe:1,128|>>>:bank_name length error',//收款人银行名称
        'personal_detail[*].bank_no_name' => 'Required|StrLenGeLe:1,128|>>>:bank_no_name length error',//收款人银行账号名称
        'personal_detail[*].bank_no' => 'Required|StrLenGeLe:1,32|>>>:bank_no error',//收款人银行账号
        'personal_detail[*].name' => 'Required|StrLenGeLe:1,128|>>>:name  error',//联系人
        'personal_detail[*].mobile' => 'Required|StrLenGeLe:1,20|>>>:mobile length error',//联系方式
        'personal_detail[*].amount' => 'Required|StrLenGeLe:1,32|>>>:amount length error',//金额
    ];

    //普通付款 - 申请创建 - 预提编号
    public static $validate_show_budget_withholding_param = [
        'cost_store_type'                  => 'Required|IntIn:1,2|>>>: cost_store_type error',  //费用所属网点类型：1：总部，2：网点
        'cost_department_id'               => 'Required|IntGt:0|>>>: department id error',      //费用所属部门ID
        'amount_detail'                    => 'Required|Arr|ArrLenGe:1|>>>:amount detail error',//金额详情
        'amount_detail[*].budget_id'       => 'Required|IntGe:1|>>>:budget_id error',           //付款分类
        'amount_detail[*].cost_start_date' => 'Required|Date|>>>:cost start date error',        //费用发生期间-开始日期
        'amount_detail[*].cost_end_date'   => 'Required|Date|>>>:cost end date error',          //费用发生期间-结束日期
    ];

    //普通付款 - 申请创建 - 预提编号 - 搜索、选择
    public static $validate_search_budget_withholding_param = [
        'no'                 => 'StrLenGeLe:0,20',
        'cost_department_id' => 'Required|IntGt:0|>>>: department id error',      //费用所属部门ID
        'cost_store_type'    => 'Required|IntIn:1,2|>>>: cost_store_type error',  //费用所属网点类型：1：总部，2：网点
        'amount_type'        => 'Required|IntIn:' . BudgetBaseService::IS_NO_COM_USE . ',' . BudgetBaseService::IS_COM_USE,
        'budget_id'          => 'Required|IntGe:0',
    ];

    public static $level_code_arr = [
        '098',
        '047',
        '008',
        '115',
    ];


    /**
     * 普通付款申请验证规则
     * @param array $params 请求参数组
     * @return array
     */
    public static function validateCreate($params)
    {
        // 普通付款申请验证规则
        $validate_create = [
            'apply_no'                             => 'Required|StrLenGeLe:12,20|>>>:apply no error',//编号
            'apply_staff_id'                       => 'Required|IntGt:0|>>>:apply staffId error',//申请人工号
            'cost_department_id'                   => 'Required|IntGt:0|>>>: department id error',//费用所属部门ID
            'cost_department_name'                 => 'Required|StrLenGeLe:1,100|>>>: department name error',//费用所属部门名称
            'cost_store_type'                      => 'Required|IntIn:1,2|>>>: expense network error',//费用所属网点类型：1：总部，2：网点
            'currency'                             => 'Required|IntIn:'.GlobalEnums::VALIDATE_CURRENCY_PARAMS.'|>>>:payment currency error', //付款币种
            'payment_method'                       => 'Required|IntIn:2,3|>>>:payee_type error',//付款方式（2-银行转账(默认)， 3-支票）
            'should_pay_date'                      => 'Required|Date|>>>:pay date error',//应付日期
            'remark'                               => 'Required|>>>:remark error', //备注,
            'payee_type'                           => 'Required|IntIn:1,2|>>>:payment method error',//收款类型 1供应商 2 个人
            'attachments'                          => 'Arr|ArrLenGeLe:0,20|>>>:main attachment list  error[Upload up to 20]',     //附件列表，必填
            'amount_detail'                        => 'Required|Arr|ArrLenGe:1|>>>:amount detail error',//金额详情
            'amount_detail[*]'                     => 'Required|Obj',
            'amount_detail[*].budget_id'           => 'Required|IntGe:1|>>>:budget_id error', //付款分类
            'amount_detail[*].level_code'          => 'Required|StrLenGeLe:1,50|>>>:level code error', //付款分类
            'amount_detail[*].product_id'          => 'IntGe:0|>>>:product_id error', //费用类型(付款分类子类）
            'amount_detail[*].ledger_account_id'   => 'IntGe:0|>>>:ledger_account_id error', //核算科目id
            'amount_detail[*].cost_start_date'     => 'Required|Date|>>>:cost start date error', //费用发生期间-开始日期
            'amount_detail[*].cost_end_date'       => 'Required|Date|>>>:cost end date error', //费用发生期间-结束日期
            'amount_detail[*].vat7_rate'           => 'Required|FloatGe:0|>>>:VAT rate field is blank, please check!',
            'amount_detail[*].wht_category'        => 'Required|IntGe:0|>>>:WHT category error', //WHT 类别
            'amount_detail[*].wht_rate'            => 'Required|FloatGe:0|>>>:WHT rate error', //WHT 税率
            'amount_detail[*].cost_center_name'    => 'StrLenGeLe:0,100|>>>:cost center error', //费用所属中心（pccode)
            'amount_detail[*].contract_no'         => 'StrLenGeLe:0,100|>>>:contract no error', //合同编号
            'amount_detail[*].attachment_list'     => 'Arr|ArrLenGeLe:0,5|>>>:attachment list  error', //附件列表（数组）不是必填了=v9046
            'amount_detail[*].pono'                => 'StrLenGeLe:0,20|>>>:pono error', //采购订单编号
            'amount_discount' => 'Required|FloatGe:0|>>>:amount discount error',//折扣金额
            'cost_company_id' => 'Required|IntGt:0',//费用所属公司
            'extra_message' => 'StrLenGeLe:0,35',//额外参考消息
            'voucher_abstract' => 'StrLenGeLe:0,40',//凭证摘要
            'ticket_number' => 'Required|StrLenGeLe:1,100',//发票号码
            'ticket_date' => 'Required|Date',//发票日期
            'loan_time' => 'Str',//付款条款
            'pay_where' => 'Required|StrIn:1,2',//1 境内 2 境外
            'swift_code' => 'IfIntEq:pay_where,2|Required|StrLenGeLe:1,30',//Swift Code
            'amount_detail[*].voucher_description'                       => 'StrLenGeLe:0,40',//凭证描述
            'bank_address' => 'IfIntEq:pay_where,2|Required|StrLenGeLe:1,200|>>>: bank_address error',//收款银行地址
            'amount_detail[*].deductible_vat_tax' => 'Str',//可抵扣VAT税率
            'invoice_no' => 'StrLenGeLe:0,100|>>>:invoice no error', //增值税发票号长度限制
            'business_type' => 'IntIn:1,2|>>>:business_type error', //业务类型
        ];

        // V22269 金额详情 - 网点/总部必填拦截 - 1：总部，2：网点;
        if (!empty($params['cost_store_type']) ) {
            if ($params['cost_store_type'] == Enums::PAYMENT_COST_STORE_TYPE_01) {
                $validate_create['amount_detail[*].cost_department_id']   = 'Required|IntGt:0';
                $validate_create['amount_detail[*].cost_department_name'] = 'Required|StrLenGeLe:1,255';
            } else if ($params['cost_store_type'] == Enums::PAYMENT_COST_STORE_TYPE_02) {
                $validate_create['amount_detail[*].cost_store_id']   = 'Required|StrLenGeLe:1,12|>>>:store id error';    //费用所属网点id
                $validate_create['amount_detail[*].cost_store_name'] = 'Required|StrLenGeLe:1,255|>>>:store name error'; //费用所属网点名称
            }
        }

        $country_code = get_country_code();

        // 马来15988需求/泰国菲律宾菲律宾7688需求，20240815邮件需求不含税金额（范围输入值介于-9999999999.99至0或0,9999999999.99不可为0）和SST或VAT税额/WHT金额允许输入负数（范围输入值介于-9999999999.99,9999999999.99）
        if (in_array($country_code, [GlobalEnums::MY_COUNTRY_CODE, GlobalEnums::TH_COUNTRY_CODE, GlobalEnums::PH_COUNTRY_CODE])) {
            $validate_create['amount_detail[*].amount_no_tax'] = 'Required|Numeric|Regexp:' . self::AMOUNT_NOT_TAX . '|>>>:amount no tax value error'; //不含税金额
            $validate_create['amount_detail[*].amount_vat'] = 'Required|FloatGeLe:-9999999999.99,9999999999.99|>>>:amount vat 7% value error'; //VAT7%金额
            $validate_create['amount_detail[*].wht_amount'] = 'Required|FloatGeLe:-9999999999.99,9999999999.99|>>>:amount wht value error'; //WHT金额
        } else {
            $validate_create['amount_detail[*].amount_no_tax'] = 'Required|FloatGtLe:0,9999999999.99|>>>:amount no tax value error'; //不含税金额
            $validate_create['amount_detail[*].amount_vat'] = 'Required|FloatGeLe:0,9999999999.99|>>>:amount vat 7% value error'; //VAT7%金额
            $validate_create['amount_detail[*].wht_amount'] = 'Required|FloatGeLe:0,9999999999.99|>>>:amount wht value error'; //WHT金额
        }

        // 菲律宾: 校验明细行的发票类型
        if ($country_code == GlobalEnums::PH_COUNTRY_CODE) {
            $invoice_type_enums = implode(',', array_keys(GlobalEnums::$financial_invoice_type_item));
            $validate_create['invoice_type'] = "Required|IntIn:{$invoice_type_enums}|>>>:" . static::$t->_('financial_invoice_type_params_error');
        }

        // 印尼的必填校验
        if ($country_code == GlobalEnums::ID_COUNTRY_CODE) {
            $validate_create['is_with_vat_invoice'] = 'Required|IntIn:' . implode(',', GlobalEnums::$is_with_vat_invoice_item) . '|>>>:' . static::$t->_('is_with_vat_invoice_params_error');
            $validate_create['invoice_no'] = 'IfIntEq:is_with_vat_invoice,' . GlobalEnums::IS_WITH_VAT_INVOICE_YES . '|Required|StrLenGeLe:1,100' . '|>>>:' . static::$t->_('vat_invoice_params_error');
        }

        //根据不同的情况，关联额外的参数验证
        if (isset($params['payee_type']) && $params['payee_type'] == self::PAYEE_TYPE_1) {
            $validate_create = array_merge($validate_create, self::$validate_vendor_param);
        } else if (isset($params['payee_type']) && $params['payee_type'] == self::PAYEE_TYPE_2) {
            $validate_create = array_merge($validate_create, self::$validate_person_param);
        }
        // 判断swift_code 字段长度,超过30个字符
        if (isset($params['swift_code'])) {
            $validate_create['swift_code'] = 'StrLenLe:30';
        }

        //21112 马来 付款条款字段调整为必填
        if ($country_code == GlobalEnums::MY_COUNTRY_CODE) {
            $validate_create['loan_time'] = 'Required|StrLenGeLe:1,500';
        }
        return $validate_create;
    }

    /**
     * 普通付款新增-参数验证通过后校验其他额外关联项信息验证
     * @param array $params 请求参数组
     * @throws ValidationException
     */
    public static function validationOther(array $params)
    {
        $amount_detail_count = count($params['amount_detail']);
        //明细行总数拦截
        if ($amount_detail_count > self::AMOUNT_DETAIL_MAX_UPLOAD_TOTAL) {
            throw new ValidationException(static::$t->_('ordinary_payment_max_upload_error'), ErrCode::$VALIDATE_ERROR);
        }

        // [供应商]费用所属公司如果在同步SAP的那个配置里面，就需要必填成本中心,不用区分国家
        $sap_cost_company_ids = EnumsService::getInstance()->getSettingEnvValueIds('purchase_sap_company_ids');
        //金蝶公司
        $kingdee_company_ids = EnumsService::getInstance()->getSettingEnvValueIds(KingDeeEnums::KINGDEE_BU_SETTING_CODE);
        if (isset($params['payee_type']) && $params['payee_type'] == self::PAYEE_TYPE_1) {
            //17822需求当费用所属公司属于SAP同步公司或者属于同步金蝶BU时财务编码必填
            if (empty($params['sap_supplier_no']) && (in_array($params['cost_company_id'], $sap_cost_company_ids) || in_array($params['cost_company_id'], $kingdee_company_ids))) {
                throw new ValidationException(static::$t->_('sap_supplier_no_required_error'), ErrCode::$VALIDATE_ERROR);
            }
        }
        //17687需要对金额详情的每一行的（|amount_no_tax不含税金额*vat_rate表字段，参数名是：vat7_rateVAT税率-amount_vatVAT税额| 或 |amount_no_tax不含税金额*wht_rateWHT税率-amount_wht表字段，参数名是：wht_amountWHT税额|）与系统配置的税额允差值进行比较
        $finance_tax_allowance_lines = [];
        //获取vat/sst税名称
        $vat_sst_name = EnumsService::getInstance()->getVatSStRateName();
        //获取税额允差值
        $finance_tax_allowance_val = EnumsService::getInstance()->getSettingEnvValue('finance_tax_allowance', 0);
        foreach ($params['amount_detail'] as $key => $item) {
            //17822需求当费用所属公司属于SAP同步公司或者属于同步金蝶BU时费用所属中心必填
            if (empty($item['cost_center_name']) && (in_array($params['cost_company_id'], $sap_cost_company_ids) || in_array($params['cost_company_id'], $kingdee_company_ids))) {
                throw new ValidationException(static::$t->_('cost_center_code_is_must'), ErrCode::$VALIDATE_ERROR);
            }
            if (!empty($finance_tax_allowance_val) && $finance_tax_allowance_val > 0) {
                $vat_diff = abs(bcsub(round($item['amount_no_tax'] * $item['vat7_rate'] / 100, 2), $item['amount_vat'], 2));
                $wht_diff = abs(bcsub(round($item['amount_no_tax'] * $item['wht_rate'] / 100, 2), $item['wht_amount'], 2));
                //超过允差值需要拦截提交
                if (bccomp($vat_diff, $finance_tax_allowance_val, 2) === 1 || bccomp($wht_diff, $finance_tax_allowance_val, 2) === 1) {
                    $finance_tax_allowance_lines[] = ($key + 1);
                }
            }
        }

        if (!empty($finance_tax_allowance_lines)) {
            throw new ValidationException(static::$t->_('ordinary_payment_tax_allowance_validate_error', ['LINES' => implode('、', $finance_tax_allowance_lines), 'VAT_SST' => $vat_sst_name]), ErrCode::$VALIDATE_ERROR);
        }

        // 校验付款分类
        // 不同员工类型的付款分类配置
        $payee_config = static::payeeConfig();
        if (empty($payee_config) && $params['payee_type'] == self::PAYEE_TYPE_2) {
            throw new ValidationException(static::$t->_('ordinary_payment_create_error_01'), ErrCode::$VALIDATE_ERROR);
        }

        if (!empty($payee_config)) {
            // 提交的付款分类
            $detail_level_code_list = array_values(array_unique(array_column($params['amount_detail'], 'level_code')));

            // 是否提交了不同员工类型的付款分类
            $intersect_count = 0;
            foreach ($payee_config as $object_codes) {
                if (!empty(array_intersect($detail_level_code_list, $object_codes))) {
                    $intersect_count++;
                }
            }

            if ($intersect_count >= 2) {
                throw new ValidationException(static::$t->_('ordinary_payment_create_error_02'), ErrCode::$VALIDATE_ERROR);
            }
        }

        // 19138需求，汽运保理验证规则
        $transportation_factoring_config = static::transportationFactoringConfig();
        if (!empty($transportation_factoring_config)) {
            //汽运费用的预算科目ID配置存在 && 供应商
            if (!empty($transportation_factoring_config['Transportation']) && $params['payee_type'] == self::PAYEE_TYPE_1) {
                //付款分类
                $budget_ids = array_values(array_unique(array_column($params['amount_detail'], 'budget_id')));
                $in_transportation_set = false;
                foreach ($budget_ids as $budget_id) {
                    if (in_array($budget_id, $transportation_factoring_config['Transportation'])) {
                        $in_transportation_set = true;
                        break;
                    }
                }
                //是否保理付款：当选择的付款分类其中的任意一行属于配置的汽运费用 && 收款人类型为供应商 && 选择项为保理、非保理才可提交
                if ($in_transportation_set && (empty($params['is_factoring']) || !in_array($params['is_factoring'], [OrdinaryPaymentEnums::IS_FACTORING_NO, OrdinaryPaymentEnums::IS_FACTORING_YES]))) {
                    throw new ValidationException(static::$t->_('ordinary_payment_create_error_03'), ErrCode::$VALIDATE_ERROR);
                }
                //保理对象：是否保理付款 = 是，必须选择才可提交
                if (isset($params['is_factoring']) && $params['is_factoring'] == OrdinaryPaymentEnums::IS_FACTORING_YES && (empty($params['factoring_vendor_id']) || empty($params['factoring_vendor_name']))) {
                    throw new ValidationException(static::$t->_('ordinary_payment_create_error_04'), ErrCode::$VALIDATE_ERROR);
                }
            }

            //保理费用的预算明细科目ID配置存在 && 供应商
            if (!empty($transportation_factoring_config['Factoring']) && $params['payee_type'] == self::PAYEE_TYPE_1) {
                //费用类型
                $product_ids = array_values(array_unique(array_column($params['amount_detail'], 'product_id')));
                $in_factoring_set = false;
                foreach ($product_ids as $product_id) {
                    if (in_array($product_id, $transportation_factoring_config['Factoring'])) {
                        $in_factoring_set = true;
                        break;
                    }
                }
                //关联保理付款单号：费用类型中任意一行属于配置的保理费用 && 收款人类型为供应商，必填
                if ($in_factoring_set && empty($params['factoring_apply_no'])) {
                    throw new ValidationException(static::$t->_('ordinary_payment_create_error_05'), ErrCode::$VALIDATE_ERROR);
                }
            }
        }

        // 21885需求，如果金额详情大于100行-增加如下逻辑
        $country_code = get_country_code();
        if ($amount_detail_count > 100 && in_array($country_code, [GlobalEnums::TH_COUNTRY_CODE, GlobalEnums::MY_COUNTRY_CODE, GlobalEnums::PH_COUNTRY_CODE])) {
            //则校验折扣金额是否等于0，如果不等于0，则拦截提交并提示“金额详情超过100行时总折扣必须为0，如果存在折扣，需要将折扣计算到具体的明细行！”
            if ($country_code != GlobalEnums::TH_COUNTRY_CODE && bccomp($params['amount_discount'], 0, 2) !== 0) {
                throw new ValidationException(static::$t->_('ordinary_payment_create_error_06'), ErrCode::$VALIDATE_ERROR);
            }

            //则校验所有明细行的付款分类以及费用类型(空等空)必须相等，如果不相等则拦截提交并提示“金额详情超过100行时付款分类以及费用类型必须相同！”
            $budget_id = array_unique(array_column($params['amount_detail'], 'budget_id'));
            if (count($budget_id) >= 2) {
                throw new ValidationException(static::$t->_('ordinary_payment_create_error_07'), ErrCode::$VALIDATE_ERROR);
            }
            $product_id = array_unique(array_column($params['amount_detail'], 'product_id'));
            if (count($product_id) >= 2) {
                throw new ValidationException(static::$t->_('ordinary_payment_create_error_07'), ErrCode::$VALIDATE_ERROR);
            }

            $ledger_account_group = [];
            foreach ($params['amount_detail'] as $key => $item) {
                //则校验所有明细行中不含税金额是否大于0，如果存在不大于0的数据，则拦截提交并提示“金额详情超过100行时不含税金额不允许为负数！”
                //********邮件需求需要放开普通付款明细行超过100行时不允许明细行存在负数的校验
                /*if (bccomp($item['amount_no_tax'], 0, 2) !== 1) {
                    throw new ValidationException(static::$t->_('ordinary_payment_create_error_08'), ErrCode::$VALIDATE_ERROR);
                }*/

                //则按照核算科目分组(空为单独一组)，组内的VAT税率和WHT税率必须全部相等(VAT和WHT分开比较)，如果不相等则拦截提交并提示“同一个费用类型的VAT税率必须相等”或者“同一个费用类型的WHT税率必须相等”，分开校验，分开提示
                if (!isset($ledger_account_group[$item['ledger_account_id']]['vat_rate'])) {
                    $ledger_account_group[$item['ledger_account_id']]['vat_rate'] = $item['vat7_rate'];
                } elseif ($item['vat7_rate'] != $ledger_account_group[$item['ledger_account_id']]['vat_rate']) {
                    throw new ValidationException(static::$t->_('ordinary_payment_create_error_09'), ErrCode::$VALIDATE_ERROR);
                }

                if (!isset($ledger_account_group[$item['ledger_account_id']]['wht_rate'])) {
                    $ledger_account_group[$item['ledger_account_id']]['wht_rate'] = $item['wht_rate'];
                } elseif ($item['wht_rate'] != $ledger_account_group[$item['ledger_account_id']]['wht_rate']) {
                    throw new ValidationException(static::$t->_('ordinary_payment_create_error_10'), ErrCode::$VALIDATE_ERROR);
                }

            }
        }

        // v21975 - 预提编号 - 相关校验逻辑
        if (in_array(get_country_code(), [GlobalEnums::TH_COUNTRY_CODE, GlobalEnums::MY_COUNTRY_CODE, GlobalEnums::PH_COUNTRY_CODE])) {
            //如果费用部门+付款分类命中了预提校验，则预提单必填，如果预提单未填写，则提示“预提单必须填写！”
            $res = OrdinaryPaymentListService::getInstance()->showBudgetWithholding($params);
            if ($res['data']['is_show'] && (empty($params['budget_withholding_id']) || empty($params['budget_withholding_no']))) {
                throw new ValidationException(static::$t->_('ordinary_payment_create_error_11'), ErrCode::$VALIDATE_ERROR);
            }

            //填写了预提编号
            if (!empty($params['budget_withholding_no'])) {
                //校验金额详情中的付款分类是否存在多个,如果存在多个，则提示“需要预提的费用付款时仅允许选择同一付款分类！”
                $budget_id = array_unique(array_column($params['amount_detail'], 'budget_id'));
                if (count($budget_id) > 1) {
                    throw new ValidationException(static::$t->_('ordinary_payment_create_error_12'), ErrCode::$VALIDATE_ERROR);
                }

                //金额详情中的费用日期与预提单中的预算归属日期不一致！
                $budget_withholding_info = BudgetWithholdingService::getInstance()->getBudgetWithholdingInfoById($params['budget_withholding_id']);
                foreach ($params['amount_detail'] as $key => $item) {
                    if (date('Y-m', strtotime($item['cost_start_date'])) != $budget_withholding_info->ownership_period || date('Y-m', strtotime($item['cost_end_date'])) != $budget_withholding_info->ownership_period) {
                        throw new ValidationException(static::$t->_('ordinary_payment_create_error_13'), ErrCode::$VALIDATE_ERROR);
                    }
                }

                //如果预提编号有值，并且关联的预提单有明细行，则校验本次提交的普通付款明细行中的成本中心是否重复，如果重复则拦截提交并提示“关联预提单的付款不允许一个网点提交多行数据，请核对！”
                $budget_withholding_detail = $budget_withholding_info->getDetails()->toArray();
                if ($budget_withholding_detail) {
                    $cost_center_name = array_count_values(array_filter(array_column($params['amount_detail'], 'cost_center_name')));
                    foreach ($cost_center_name as $num) {
                        if ($num > 1) {
                            throw new ValidationException(static::$t->_('ordinary_payment_create_error_14'), ErrCode::$VALIDATE_ERROR);
                        }
                    }
                }

                //校验预提编号是否属于可选择范围-与选择预提单列表的范围一致，如果不属于则提示“预提编号不可用！”
                $res = OrdinaryPaymentListService::getInstance()->searchBudgetWithholding([
                    'no'                 => $params['budget_withholding_no'],
                    'cost_department_id' => $params['cost_department_id'],
                    'amount_type'        => $res['data']['amount_type'],
                    'cost_store_type'    => $params['cost_store_type'],
                    'budget_id'          => $budget_id[0],
                ]);
                if (empty($res['data'])) {
                    throw new ValidationException(static::$t->_('ordinary_payment_create_error_15'), ErrCode::$VALIDATE_ERROR);
                }
            }
        }
    }

    /**
     * 过滤空值 和 非必要参数
     * @param array $params
     * @param array $not_must
     * @return mixed
     */
    public static function handleParams(array $params, array $not_must = [])
    {
        $params = array_filter($params);

        if (empty($not_must) || !is_array($not_must)) {
            return $params;
        }

        foreach ($not_must as $value) {
            if (isset($params[$value])) {
                unset($params[$value]);
            }
        }

        return $params;
    }

    /**
     * 过滤空值 和 非必要参数
     * @param array $params
     * @param array $not_must
     * @return mixed
     */
    public static function handleParamsFilter(array $params, array $not_must = [])
    {
        $params = array_filter($params, "keep_zero_and_non_empty");

        if (empty($not_must) || !is_array($not_must)) {
            return $params;
        }

        foreach ($not_must as $value) {
            if (isset($params[$value])) {
                unset($params[$value]);
            }
        }

        return $params;
    }

    /**
     * 去bi里面取相关数据
     *
     * @param $userId
     * @param int $flag 0发起人，1申请人
     * @return array|string
     * @throws ValidationException
     */
    public function getUserMetaFromBi($userId, $flag = 0)
    {
        $model = (new UserService())->getUserByIdInRbi($userId);
        if (empty($model)) {
            throw new ValidationException(static::$t->_('re_staff_info_id_not_exist'), ErrCode::$VALIDATE_ERROR);
        }

        if ($model->state == 2) {
            throw new ValidationException(static::$t->_('re_staff_info_id_left'), ErrCode::$VALIDATE_ERROR);
        }

        if (get_country_code() == GlobalEnums::MY_COUNTRY_CODE) {
            // v21199 发起人须是正式员工 或 实习生
            if ($flag == 0 && !in_array($model->formal, [1, 4])) {
                throw new ValidationException(static::$t->_('staff_not_formal_or_trainee'), ErrCode::$VALIDATE_ERROR);
            }

            if ($flag == 1 && $model->formal != 1) {
                throw new ValidationException(static::$t->_('re_staff_info_id_not_formal'), ErrCode::$VALIDATE_ERROR);
            }

        } else {
            if ($model->formal != 1) {
                throw new ValidationException(static::$t->_('re_staff_info_id_not_formal'), ErrCode::$VALIDATE_ERROR);
            }
        }

        $data               = [];
        $id                 = $model->staff_info_id ?? "";
        $name               = $model->name ?? "";
        $nick_name          = $model->nick_name ?? "";
        $sys_department_id  = $model->sys_department_id;
        $node_department_id = $model->node_department_id;
        $email              = $model->email;

        $node_department_name = "";
        $company_id           = 0;
        $company_name         = "";
        //根据所属部门获取公司信息
        $departmentObj = DepartmentModel::findFirst([
            "conditions" => "id = :id:",
            "bind"       => [
                "id" => $node_department_id,
            ],
        ]);

        if (!empty($departmentObj)) {
            $node_department_name = $departmentObj->name;
            if ($departmentObj->type == 2) {
                $company_id   = $departmentObj->company_id;
                $company_name = $departmentObj->company_name;
            }

        }

        if (empty($flag)) {
            $data['create_id']                   = $id;
            $data['create_name']                 = $this->getNameAndNickName($name, $nick_name);
            $data['create_node_department_id']   = $node_department_id;
            $data['create_node_department_name'] = $node_department_name;
            $data['create_company_id']           = $company_id;
            $data['create_company_name']         = $company_name;
            $data['apply_email']                 = $email;

            $data['create_job_title_id'] = $model->job_title;
            $data['create_job_title_name'] = '';
            $title_model = $model->getJobTitle();
            if(!empty($title_model)){
                $data['create_job_title_name'] = $title_model->job_name;
            }

        } else {
            $data['apply_id']                   = $id;
            $data['apply_name']                 = $this->getNameAndNickName($name, $nick_name);
            $data['apply_sys_department_id']    = $sys_department_id;
            $data['apply_node_department_id']   = $node_department_id;
            $data['apply_node_department_name'] = $node_department_name;
            $data['apply_company_id']           = (string)$company_id;
            $data['apply_company_name']         = $company_name;
            $data['apply_mobile']               = $model->mobile;
            $data['apply_email']                = $email;

            // 是否允许自由选择费用所属部门
            $data['is_free_change'] = false;

            // 可自由选择全量费用所属部门的部门配置
            $change_cost_department_ids = EnumsService::getInstance()->getSettingEnvValueIds('change_cost_department_ids');

            // 可自由选择全量费用所属部门的工号配置
            $change_cost_staff_ids = EnumsService::getInstance()->getSettingEnvValueIds('change_cost_department_by_staff_ids');

            // 申请人所属部门 或 申请人 隶属 上述配置时, 可自由选择费用所属部门 v18029
            if (in_array($sys_department_id, $change_cost_department_ids) || in_array($id, $change_cost_staff_ids)) {
                $data['is_free_change'] = true;
            }
            $data['is_need_email_attachment'] = false;//是否需要上传审批邮件附件
            //总部员工，找部门对应code
            if ($model->sys_store_id == -1) {
                $data['apply_store_id']    = Enums::PAYMENT_HEADER_STORE_ID;
                $data['apply_store_name']  = Enums::PAYMENT_HEADER_STORE_NAME;
                $data['apply_center_code'] = $this->getPcCodeByDepartmentId($node_department_id);
            } else {
                $department_config                = EnumsService::getInstance()->getSettingEnvValueMap('appoint_store_by_department_id');
                $tmp                              = SysStoreModel::findFirst("id = '" . $model->sys_store_id . "'");
                $data['apply_store_id']           = $model->sys_store_id;
                $data['apply_store_name']         = $tmp ? $tmp->name : '';
                $data['apply_center_code']        = $this->getPcCodeByStoreId($model->sys_store_id);
                $data['is_need_email_attachment'] = get_country_code() == GlobalEnums::PH_COUNTRY_CODE && $sys_department_id == $department_config['network'];//是否需要上传审批邮件附件
            }
        }
        $data['department_v2'] = $this->getMyDeptListV2(['id' => $id, 'department_id' => $node_department_id]);
        return $data;
    }

    /**
     * 根据部门id 获取 对应的pc_code信息
     * @param int $department_id
     * @return
     */
    public function getPcCodeByDepartmentId($department_id)
    {

        $data = Pccode::findFirst(["conditions" => "department_id = :department_id:", "bind" => ["department_id" => $department_id]]);
        if (!empty($data)) {
            return $data->pc_code;
        }
        return "";
    }

    /**
     * 获取对应网点的pc_code
     * @param $store_id
     * @return string
     */
    public function getPcCodeByStoreId($store_id)
    {
        $data = SysStoreModel::findFirst(["conditions" => "id = :id:", "bind" => ["id" => $store_id]]);
        if (!empty($data)) {
            return $data->sap_pc_code;
        }
        return "";
    }

    /**
     * 获取获取状态为已归档的其他归档合同
     * @param string $cno
     * @param bool $from_purchase 是否来自采购付款申请单
     * @return array
     */
    protected function getArchiveContract(string $cno = '', $from_purchase = false)
    {
        try {
            $builder = $this->modelsManager->createBuilder();
            $builder->from(['ca' => ContractArchive::class]);
            $builder->where('ca.status = :status:', ['status' => Enums\ContractEnums::CONTRACT_ARCHIVE_STATUS_APPROVAL]);
            $builder->columns('ca.id, ca.cno');
            // 采购付款申请单不查询网点合同
            if ($from_purchase) {
                $builder->leftjoin(Contract::class, 'ca.cno = c.cno', 'c');
                $builder->andWhere('c.contract_type != :contract_type:', ['contract_type' => Enums\ContractEnums::CONTRACT_TYPE_RENTING]);
            }
            if (!empty($cno)) {
                $builder->andWhere('ca.cno LIKE :cno:', ['cno' => "$cno%"]);
            }
            $obj = $builder->getQuery()->execute();

            if ($obj) {
                return $obj->toArray();
            }


        } catch (\Exception $e) {
            $logger = $this->getDI()->get('logger');
            $logger->warning('普通付款 - 申请创建页- 相关合同获取异常: ' . $e->getMessage());
        }

        return [];
    }

    /**
     * 获取获取状态为已归档的其他归档合同
     * @param string $contract_no
     * @return array
     */
    protected function getArchiveContractByCno(string $cno = '')
    {
        try {
            $builder = $this->modelsManager->createBuilder();
            $builder->from(ContractArchive::class);
            $builder->where('status = 2');
            $builder->columns('id,cno');
            if (!empty($cno)) {
                $builder->andWhere('cno = :cno:', ['cno' => $cno]);
            }
            $obj = $builder->getQuery()->execute();
            if ($obj) {
                return $obj->toArray();
            }
        } catch (\Exception $e) {
            $logger = $this->getDI()->get('logger');
            $logger->warning('普通付款 - 申请创建页- 相关合同获取异常: ' . $e->getMessage());
        }
        return [];
    }

    /**
     * 获取系统所有网点: 总部 与 网点合并
     * @param array $params
     * @param array $user
     * @param bool $is_append_header_office
     * @return array
     */
    public function getSysStoreList(array $params = [], array $user = [], bool $is_append_header_office = false)
    {
        $data = [];

        if ($is_append_header_office) {
            $data[] = [
                'id'          => Enums::PAYMENT_HEADER_STORE_ID,
                'name'        => Enums::PAYMENT_HEADER_STORE_NAME,
                'sap_pc_code' => '',
            ];
        }

        $store = (new StoreService())->getSysStoreListByCondition($params);
        return $store ? trim_array(array_merge($data, $store)) : $data;
    }

    /**
     * 付款分类&费用类型 结构返回
     * @return array
     */
    public function getCostCategory()
    {
        $category = Enums::$ordinary_payment_category;
        foreach ($category as &$item) {
            $item['label'] = self::$t[$item['label']];
            if ($item['tax_item']) {
                foreach ($item['tax_item'] as &$child_item) {
                    $child_item['label'] = self::$t[$child_item['label']];
                }
            }
        }

        return $category;
    }

    /**
     * 付款分类&费用类型 对应翻译key 关系结构
     */
    public function getCostCategoryTransKey()
    {
        $category                = Enums::$ordinary_payment_category;
        $cost_category_trans_key = [];
        foreach ($category as $item) {
            $cost_category_trans_key[$item['id']] = [
                'label'    => $item['label'],
                'tax_item' => [],
            ];
            if ($item['tax_item']) {
                foreach ($item['tax_item'] as $child_item) {
                    $cost_category_trans_key[$item['id']]['tax_item'][$child_item['id']] = ['label' => $child_item['label']];
                }
            }
        }
        return $cost_category_trans_key;
    }

    /**
     * 获取付款支付权限的工号清单
     * @return array
     */
    public function getPayAuthStaffIdItem()
    {
        $staff_ids     = EnvModel::getEnvByCode(Enums::ORDINARY_PAYMENT_PAY_STAFF_IDS_KEY);
        $staff_id_item = explode(',', $staff_ids);
        return $staff_id_item ? $staff_id_item : [];
    }


    public function isCanDownload($item,$uid){
        if(empty($item)){
            return '0';
        }

        if(empty($uid)){
            return '1';
        }

        if($item['approval_status'] == Enums::CONTRACT_STATUS_REJECTED || $item['approval_status'] == Enums::CONTRACT_STATUS_CANCEL || $item['pay_status'] == Enums::LOAN_PAY_STATUS_NOTPAY){
            return '0';
        }
        /**
         * 线上问题修复，改is_after_ap_th字段返回值
         * 再一期审批流修改需求后不再维护变更，故而导致无法正常下载pdf
         * 跟万琳沟通后，直接改为不满足以上提交就可以下载
         */
        return '1';
    }

    public function getMyDeptListV2($user){
        return \App\Modules\Reimbursement\Models\DepartmentModel::find([
            'columns' => 'id, name',
            'conditions' => 'level = 1 and type in (2, 3) and deleted = 0 and manager_id = ?1 or id = ?2',
            'bind'       => [
                1 => $user['id'],
                2 => $user['department_id']
            ]
        ])->toArray();
    }

    public static function payeeConfig()
    {
        $val = EnumsService::getInstance()->getSettingEnvValue('payee_budget_config');
        return !empty($val) ? json_decode($val, true) : [];
    }

    /**
     * 获取汽运保理费用类型,汽运费用是科目ID,保利费用是科目明细ID
     * @return array|mixed
     */
    public function transportationFactoringConfig()
    {
        $val = EnumsService::getInstance()->getSettingEnvValueMap('ordinary_payment_transportation_factoring_id');
        foreach ($val as $k => $v) {
            $val[$k] = explode(',', $v);
        }
        return $val;
    }

    public function ticketTypeConfig(){

        $ticket_type = json_decode(EnvModel::getEnvByCode('ticket_type'),true)??[];

        foreach ($ticket_type as $k => $type) {
            $ticket_type[$k]['value'] = static::$t->_($type['value']);
        }

        return $ticket_type;
    }



    /**
     *    日期-获取当月最后一天
     *  @return int
     */
    public function getLastDay($month,$year)
    {

        if ($month == 2) {

            $last_day = is_leap_year($year) ? 29 : 28;

        } else if ($month == 4 || $month == 6 || $month == 9 || $month == 11) {

            $last_day = 30;

        } else {

            $last_day = 31;

        }

        return $last_day;
    }
}
