<?php
/**
 * 普通付款 模块
 */

namespace App\Modules\OrdinaryPayment\Controllers;

use App\Library\Enums;
use App\Library\Enums\BudgetObjectEnums;
use App\Library\Enums\DownloadCenterEnum;
use App\Library\ErrCode;
use App\Library\Exception\BusinessException;
use App\Library\Validation\Validation;
use App\Library\Validation\ValidationException;
use App\Modules\Budget\Services\BudgetWithholdingService;
use App\Modules\Common\Services\DownloadCenterService;
use App\Modules\Common\Services\EnumsService;
use App\Modules\OrdinaryPayment\Services\OrdinaryPaymentAddService;
use App\Modules\OrdinaryPayment\Services\BaseService;
use App\Modules\OrdinaryPayment\Services\OrdinaryPaymentDetailService;
use App\Modules\OrdinaryPayment\Services\OrdinaryPaymentFlowService;
use App\Modules\OrdinaryPayment\Services\OrdinaryPaymentListService;
use App\Modules\OrdinaryPayment\Services\OrdinaryPaymentUpdateService;
use App\Modules\Purchase\Services\OrderService;
use Exception;
use GuzzleHttp\Exception\GuzzleException;
use Phalcon\Http\Response;
use Phalcon\Http\ResponseInterface;

class OrdinaryPaymentController extends BaseController
{


    /**
     * 普通付款申请 - 创建页 - 基本信息 默认值
     *
     * @Token
     * @return Response|ResponseInterface
     */

    public function getCreaterInfoAction()
    {
        $res = OrdinaryPaymentAddService::getInstance()->getCreatePageBaseInfoDefaultData($this->user, 1);
        if ($res['code'] == ErrCode::$SUCCESS) {
            return $this->returnJson(ErrCode::$SUCCESS, 'ok', $res['data']);
        }
        return $this->returnJson($res['code'], $res['message']);
    }

    /**
     * 普通付款申请 - 创建页 - 基本信息 默认值
     * @Token
     * @api https://yapi.flashexpress.pub/project/133/interface/api/22154
     * @return \Phalcon\Http\Response|ResponseInterface
     */
    public function getOptionsDefaultAction()
    {
        $res = OrdinaryPaymentAddService::getInstance()->getCreatePageBaseInfoDefaultData($this->user, 2);
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }

    /**
     * 获取指定工号基础信息
     *
     * @Token
     * @return Response|ResponseInterface
     * @throws ValidationException
     */
    public function getApplyUserAction()
    {
        $param = $this->request->get();
        Validation::validate($param, BaseService::$validate_apply_staff_id);

        $res = OrdinaryPaymentAddService::getInstance()->getUser($param['apply_staff_id']);
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }

    /**
     * 根据部门ID获得PcCode
     *
     * @Token
     * @return Response|ResponseInterface
     * @throws ValidationException
     */
    public function getPcCodeAction()
    {
        $param = $this->request->get();
        Validation::validate($param, BaseService::$validate_department);
        $res = OrdinaryPaymentAddService::getInstance()->getPcCode($param['department_id'], 1);

        if ($res['code'] == ErrCode::$SUCCESS) {
            return $this->returnJson(ErrCode::$SUCCESS, 'ok', $res['data']);
        }

        return $this->returnJson($res['code'], $res['message']);
    }

    /**
     * 普通付款申请 - 创建页 - 合同列表搜索
     *
     * @Token
     * @return Response|ResponseInterface
     * @throws ValidationException
     */
    public function getContractListAction()
    {
        $param = $this->request->get();
        Validation::validate($param, BaseService::$validate_contract_search_param);

        $res = OrdinaryPaymentAddService::getInstance()->getContractList($param);

        if ($res['code'] == ErrCode::$SUCCESS) {
            return $this->returnJson(ErrCode::$SUCCESS, $res['message'], $res['data']);
        }

        return $this->returnJson($res['code'], $res['message'], []);
    }

    /**
     * 普通付款申请 - 创建页 - 网点列表搜索
     *
     * @Token
     * @return Response|ResponseInterface
     * @throws ValidationException
     */
    public function getStoreListAction()
    {
        $param = $this->request->get();
        Validation::validate($param, BaseService::$validate_store_search_param);

        $res = OrdinaryPaymentAddService::getInstance()->getStoreList($param, $this->user);
        if ($res['code'] == ErrCode::$SUCCESS) {
            return $this->returnJson(ErrCode::$SUCCESS, $res['message'], $res['data']);
        }

        return $this->returnJson($res['code'], $res['message'], []);
    }

    /**
     * 获取科目列表
     *
     * @Token
     * @return mixed
     */
    public function getBudgetsAction()
    {
        try {
            $data = $this->request->get();
            Validation::validate($data, BaseService::$validate_budget);
            //cost_store_type 含义：1-网点，2-总部，由于科目数据表中 网点和总部的value值正好和接口定义的相反，所以在此需要转换下
            $data['cost_store_type'] = $data['cost_store_type'] == 1 ? 2 : 1;
            $data = OrdinaryPaymentAddService::getInstance()->getBudgetList($data['cost_department_id'], $data['cost_store_type']);

            return $this->returnJson(ErrCode::$SUCCESS, '', $data);
        } catch (ValidationException $e) {

            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage(), []);
        } catch (Exception $e) {
            $this->logger->error(
                'file ' . $e->getFile() .
                ' line ' . $e->getLine() .
                ' message ' . $e->getMessage() .
                ' trace ' . $e->getTraceAsString()
            );
            return $this->returnJson(ErrCode::$SYSTEM_ERROR, $this->t['retry_later'], []);
        }

    }


    /**
     * 普通付款 - 申请创建
     * @Permission(action='ordinary_payment.apply.add')
     *
     * @api https://yapi.flashexpress.pub/project/133/interface/api/26298
     * @return Response|ResponseInterface
     * @throws ValidationException
     */
    public function createAction()
    {
        $params = trim_array($this->request->get());
        $params = BaseService::handleParamsFilter($params, []);

        $params['supplier_email'] = $params['supplier_email'] ?? '';
        Validation::validate($params, BaseService::validateCreate($params));

        //参数验证通过后校验其他额外关联项信息验证
        BaseService::validationOther($params);

        // 创建申请
        $lock_key = md5('ordinary_payment_create_' . $params['apply_no']);
        $res = $this->atomicLock(function () use ($params) {
            return OrdinaryPaymentAddService::getInstance()->addApply($params, $this->user);
        }, $lock_key, 10);

        if (isset($res['code'])) {
            $code = $res['code'];
            $message = $res['message'];
            $data = $res['data'];
        } else {
            $code = ErrCode::$FREQUENT_VISIT_ERROR;
            $message = $this->t['sys_processing'];
            $data = [];
        }

        return $this->returnJson($code, $message, $data);
    }

    /**
     * 付款申请列表
     * @Permission(action='ordinary_payment.apply.query')
     *
     * @return mixed
     * @throws ValidationException
     */
    public function applyListAction()
    {
        $params = $this->request->get();
        $params = BaseService::handleParams($params, []);

        Validation::validate($params, OrdinaryPaymentListService::$validate_apply_list_search);

        $res = OrdinaryPaymentListService::getInstance()->getList($params, $this->user, OrdinaryPaymentListService::LIST_TYPE_APPLY);
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }

    /**
     * 付款审核列表
     * @Permission(action='ordinary_payment.audit.query')
     *
     * @return mixed
     * @throws ValidationException
     */
    public function auditListAction()
    {
        $params = $this->request->get();
        $params = BaseService::handleParams($params, []);
        Validation::validate($params, OrdinaryPaymentListService::$validate_other_list_search);

        $res = OrdinaryPaymentListService::getInstance()->getList($params, $this->user, OrdinaryPaymentListService::LIST_TYPE_AUDIT);
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }

    /**
     * 付款支付列表
     * @Permission(action='ordinary_payment.pay.query')
     *
     * @return mixed
     * @throws ValidationException
     */
    public function payListAction()
    {
        $params = $this->request->get();
        $params = BaseService::handleParams($params, []);
        Validation::validate($params, OrdinaryPaymentListService::$validate_other_list_search);

        $res = OrdinaryPaymentListService::getInstance()->getList($params, $this->user, OrdinaryPaymentListService::LIST_TYPE_PAY);
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }

    /**
     * 数据查询列表
     * @Permission(action='ordinary_payment.data.query')
     *
     * @api https://yapi.flashexpress.pub/project/133/interface/api/22050
     * @return mixed
     * @throws ValidationException
     */
    public function dataListAction()
    {
        $params = $this->request->get();
        $params = BaseService::handleParams($params, []);

        Validation::validate($params, OrdinaryPaymentListService::$validate_other_list_search);

        $res = OrdinaryPaymentListService::getInstance()->getList($params, $this->user, OrdinaryPaymentListService::LIST_TYPE_DATA);
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }


    /**
     * 普通付款 - 付款申请 - 详情
     * @Permission(action='ordinary_payment.apply.view')
     *
     * @return mixed
     * @throws ValidationException
     */
    public function applyDetailAction()
    {
        $param = $this->request->get();
        $id = $this->request->get('id');
        Validation::validate($param, BaseService::$validate_detail_param);

        $res = OrdinaryPaymentDetailService::getInstance()->getOrdinaryPaymentDetail($id, $this->user['id']);
        if ($res['code'] == ErrCode::$SUCCESS) {
            return $this->returnJson(ErrCode::$SUCCESS, $res['message'], $res['data']);
        }
        return $this->returnJson($res['code'], $res['message']);
    }

    /**
     * 普通付款 - 付款审核 - 详情
     * @Permission(action='ordinary_payment.audit.view')
     *
     * @return Response|ResponseInterface
     * @throws ValidationException
     */
    public function auditDetailAction()
    {
        $param = $this->request->get();
        $id = $this->request->get('id');

        Validation::validate($param, BaseService::$validate_detail_param);

        $res = OrdinaryPaymentDetailService::getInstance()->getOrdinaryPaymentDetail($id, $this->user['id'], true);
        if ($res['code'] == ErrCode::$SUCCESS) {
            return $this->returnJson(ErrCode::$SUCCESS, $res['message'], $res['data']);
        }

        return $this->returnJson($res['code'], $res['message']);
    }

    /**
     * 普通付款 - 付款支付 - 详情
     * @Permission(action='ordinary_payment.pay.view')
     *
     * @return mixed
     * @throws ValidationException
     */
    public function payDetailAction()
    {
        $param = $this->request->get();
        $id = $this->request->get('id');

        Validation::validate($param, BaseService::$validate_detail_param);

        $res = OrdinaryPaymentDetailService::getInstance()->getOrdinaryPaymentDetail($id, $this->user['id']);
        if ($res['code'] == ErrCode::$SUCCESS) {
            return $this->returnJson(ErrCode::$SUCCESS, $res['message'], $res['data']);
        }
        return $this->returnJson($res['code'], $res['message']);
    }

    /**
     * 普通付款 - 数据查询 - 详情
     * @Permission(action='ordinary_payment.data.view')
     *
     * @return mixed
     * @throws ValidationException
     */
    public function dataDetailAction()
    {
        $param = $this->request->get();
        $id = $this->request->get('id');

        Validation::validate($param, BaseService::$validate_detail_param);

        $res = OrdinaryPaymentDetailService::getInstance()->getOrdinaryPaymentDetail($id, $this->user['id']);
        if ($res['code'] == ErrCode::$SUCCESS) {
            return $this->returnJson(ErrCode::$SUCCESS, $res['message'], $res['data']);
        }

        return $this->returnJson($res['code'], $res['message']);
    }

    /**
     * 数据查询-导出
     * @Permission(action='ordinary_payment.data.export')
     * @api https://yapi.flashexpress.pub/project/133/interface/api/22078
     * @return mixed
     * @throws ValidationException
     */
    public function dataExportAction()
    {
        set_time_limit(0);
        ini_set('memory_limit', '512M');

        $params = $this->request->get();
        $params = BaseService::handleParams($params, []);
        Validation::validate($params, OrdinaryPaymentListService::$validate_other_list_search);

        // 加锁处理
        $lock_key = md5('ordinary_payment_data_export_' . $this->user['id']);
        $res = $this->atomicLock(function () use ($params) {
            // 大于指定数量, 添加异步任务 导出
            if (OrdinaryPaymentListService::getInstance()->getDataExportTotal($params, $this->user) > OrdinaryPaymentListService::getInstance()::DOWNLOAD_LIMIT) {
                $result = DownloadCenterService::getInstance()->addDownloadCenter($this->user['id'], DownloadCenterEnum::FINANCE_ORDINARY_PAYMENT_DATA, $params);
                $result['data'] = [
                    'export_method' => DownloadCenterEnum::EXPORT_METHOD_ASYNC,
                    'file_url' => '',
                ];
            } else {
                // 小于等于指定数量, 同步导出
                $result = OrdinaryPaymentListService::getInstance()->getSyncExportData($params, $this->user);
                $result['data'] = [
                    'export_method' => DownloadCenterEnum::EXPORT_METHOD_SYNC,
                    'file_url' => $result['data'],
                ];
            }

            return $result;

        }, $lock_key, 30);

        if (isset($res['code'])) {
            $code = $res['code'];
            $message = $res['message'];
            $data = $res['data'];
        } else {
            $code = ErrCode::$FREQUENT_VISIT_ERROR;
            $message = $this->t['sys_processing'];
            $data = [];
        }

        return $this->returnJson($code, $message, $data);
    }

    /**
     * 普通付款申请 - 撤销
     * @Permission(action='ordinary_payment.apply.revoke')
     *
     * @return Response|ResponseInterface
     * @throws ValidationException
     */
    public function cancelAction()
    {
        $params = $this->request->get();
        $params = BaseService::handleParams($params, []);

        Validation::validate($params, OrdinaryPaymentFlowService::$validate_cancel);

        $lock_key = md5('ordinary_payment_cancel_' . $params['id']);
        $res = $this->atomicLock(function () use ($params) {
            return (new OrdinaryPaymentFlowService())->cancel($params['id'], $params['note'], $this->user);
        }, $lock_key, 20);

        if (isset($res['code'])) {
            $code = $res['code'];
            $message = $res['message'];
        } else {
            $code = ErrCode::$FREQUENT_VISIT_ERROR;
            $message = $this->t['sys_processing'];
        }

        return $this->returnJson($code, $message);
    }

    /**
     * 普通付款 - 付款审核-审核通过
     * @Permission(action='ordinary_payment.audit.audit')
     */
    public function approveAction()
    {
        $params = $this->request->get();
        $params = BaseService::handleParams($params, []);

        // 请求参数
        $this->logger->info('付款管理 - 普通付款 - 审批通过请求参数: ' . json_encode($this->request->get(), JSON_UNESCAPED_UNICODE));

        Validation::validate($params, OrdinaryPaymentFlowService::$validate_approve);

        $main_data = $params['main'] ?? [];

        $amount_detail = $params['amount_detail'] ?? [];

        // 金额详情参数
        $this->logger->info('普通付款- 审批通过 - 金额详情参数: ' . json_encode($amount_detail, JSON_UNESCAPED_UNICODE));

        //如果有修改金额详情的字段信息，则验证参数正确性
        if ($amount_detail) {
            Validation::validate($params, BaseService::$validate_approve_amount_detail_param);
        }

        $lock_key = md5('ordinary_payment_approve_' . $params['id']);
        $res = $this->atomicLock(function () use ($params, $amount_detail, $main_data) {
            return (new OrdinaryPaymentFlowService())->approve($params['id'], $params['note'] ?? '', $this->user, $amount_detail, $main_data);
        }, $lock_key, 20);

        if (isset($res['code'])) {
            $code = $res['code'];
            $message = $res['message'];
        } else {
            $code = ErrCode::$FREQUENT_VISIT_ERROR;
            $message = $this->t['sys_processing'];
        }

        return $this->returnJson($code, $message);
    }

    /**
     * 普通付款申请 - 审核驳回
     * @Permission(action='ordinary_payment.audit.audit')
     */
    public function rejectAction()
    {
        $params = $this->request->get();
        $params = BaseService::handleParams($params, []);

        Validation::validate($params, OrdinaryPaymentFlowService::$validate_reject);

        $lock_key = md5('ordinary_payment_reject_' . $params['id']);
        $res = $this->atomicLock(function () use ($params) {
            return (new OrdinaryPaymentFlowService())->reject($params['id'], $params['note'], $this->user);
        }, $lock_key, 20);

        if (isset($res['code'])) {
            $code = $res['code'];
            $message = $res['message'];
        } else {
            $code = ErrCode::$FREQUENT_VISIT_ERROR;
            $message = $this->t['sys_processing'];
        }

        return $this->returnJson($code, $message);
    }


    /**
     * 普通付款申请 - 支付
     * @Permission(action='ordinary_payment.pay.edit')
     *
     * @return mixed
     * @throws ValidationException
     */
    public function payAction()
    {
        $params = $this->request->get();
        $params = BaseService::handleParams($params, []);

        Validation::validate($params, BaseService::$validate_pay_param);

        $res = OrdinaryPaymentUpdateService::getInstance()->pay($params, $this->user);

        if ($res['code'] == ErrCode::$SUCCESS) {
            return $this->returnJson(ErrCode::$SUCCESS, $res['message'], $res['data']);
        }

        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }


    /**
     * 普通付款 - 付款申请 - 下载
     * @Permission(action='ordinary_payment.apply.download')
     *
     * @return mixed
     * @throws ValidationException
     */
    public function applyDownloadAction()
    {
        $param = $this->request->get();
        $id = $this->request->get('id', 'int');

        Validation::validate($param, BaseService::$validate_detail_param);

        // 加锁处理
        $lock_key = md5('ordinary_payment_apply_download_' . $id . '_' . $this->user['id']);
        $res = $this->atomicLock(function () use ($id) {
            return OrdinaryPaymentDetailService::getInstance()->download($id, $this->user['id']);
        }, $lock_key, 30);

        if (isset($res['code'])) {
            $code = $res['code'];
            $message = $res['message'];
            $data = $res['data'];
        } else {
            $code = ErrCode::$FREQUENT_VISIT_ERROR;
            $message = $this->t['sys_processing'];
            $data = [];
        }

        return $this->returnJson($code, $message, $data);
    }

    /**
     * 普通付款 - 数据查询 - 下载
     * @Permission(action='ordinary_payment.data.download')
     *
     * @return mixed
     * @throws ValidationException
     */
    public function dataDownloadAction()
    {
        $param = $this->request->get();
        $id = $this->request->get('id', 'int');

        Validation::validate($param, BaseService::$validate_detail_param);

        // 加锁处理
        $lock_key = md5('ordinary_payment_data_download_' . $id . '_' . $this->user['id']);
        $res = $this->atomicLock(function () use ($id) {
            return OrdinaryPaymentDetailService::getInstance()->download($id, $this->user['id']);
        }, $lock_key, 30);

        if (isset($res['code'])) {
            $code = $res['code'];
            $message = $res['message'];
            $data = $res['data'];
        } else {
            $code = ErrCode::$FREQUENT_VISIT_ERROR;
            $message = $this->t['sys_processing'];
            $data = [];
        }

        return $this->returnJson($code, $message, $data);
    }


    /**
     * pdf文件流输出
     *
     * @return Response|ResponseInterface
     * @throws ValidationException
     */
    public function outputAction()
    {
        $param = $this->request->get();
        $url = $this->request->get('url', 'trim');
        $name = $this->request->get('name', 'trim');

        Validation::validate($param, BaseService::$validate_url_param);

        $res = OrdinaryPaymentDetailService::getInstance()->outputPdfFile($url, $name);
        return $this->returnJson($res['code'], $res['message']);
    }


    /**
     * 获得批量上传模板
     *
     * @Token
     */
    public function getImportTplAction()
    {
        $res = OrdinaryPaymentDetailService::getInstance()->getImportTpl();
        if ($res['code'] == ErrCode::$SUCCESS) {
            return $this->returnJson($res['code'], $res['message'], $res['data']);
        }
        return $this->returnJson($res['code'], $res['message']);
    }


    /**
     * 导入模板
     *
     * @Token
     */
    public function importAction()
    {
        $validate = [
            'apply_staff_id' => 'Required|IntGt:0|>>>:apply staffId error',//申请人工号
            'cost_department_id' => 'Required|IntGt:0|>>>: department id error',//费用所属部门ID
            'cost_store_type' => 'Required|IntIn:1,2|>>>: expense network error',//费用所属网点类型：1：总部，2：网点
            'account_type' => 'Required|IntIn:1,2|>>>: account_type error',//科目类型类型：1：快递公司核算科目，2：子公司会计科目
            'cost_company_id' => 'Required|IntGt:0|>>>: cost_company_id error',//费用公司id
        ];


        $params = $this->request->get();
        $excelData = [];
        try {
            Validation::validate($params, $validate);
            $params = array_only($params, array_keys($validate));


            if (!$this->request->hasFiles()) {
                throw new ValidationException('not found file or staff_id');
            }

            $file = $this->request->getUploadedFiles()[0];
            $extension = $file->getExtension();
            if (!in_array($extension, ['xlsx'])) {
                throw new ValidationException('the file extension error');
            }

            $config = ['path' => ''];
            $excel = new \Vtiful\Kernel\Excel($config);
            // 读取测试文件
            $excelData = $excel->openFile($file->getTempName())
                ->openSheet()
                ->setSkipRows(1)
                ->getSheetData();
            if (empty($excelData) || empty($excelData[0])) {
                throw new ValidationException('data empty');
            }
        } catch (ValidationException $e) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
        }

        $lock_key = md5('ordinary_payment_batch_import_' . $this->user['id']);
        $res = $this->atomicLock(function () use ($params, $excelData) {
            return OrdinaryPaymentDetailService::getInstance()->import($params, $excelData);
        }, $lock_key, 30);

        if (isset($res['code'])) {
            $code = $res['code'];
            $message = $res['message'];
            $data = $res['data'];
        } else {
            $code = ErrCode::$FREQUENT_VISIT_ERROR;
            $message = $this->t['sys_processing'];
            $data = [];
        }

        return $this->returnJson($code, $message, $data);
    }

    /**
     * 公共用户信息
     *
     * @Token
     */
    public function getStaffInfoAction()
    {
        $params = $this->request->get();

        $validate = [
            'uid' => 'Required|StrLenGeLe:1,32|>>>:params error[uid]', // 收款人工号
            'staff_type' => 'Required|IntIn:1,2,3|>>>: params error[staff_type]',// 收款人员工类型
        ];

        Validation::validate($params, $validate);

        $res = OrdinaryPaymentAddService::getInstance()->getStaffInfo($params['uid'], $params['staff_type']);
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }

    /**
     * 处理类
     *
     * @Token
     * @Date: 2021-09-09 15:03
     * @return Response|ResponseInterface :
     * @author: peak pan
     */
    public function getOptionsCostCompanyAction()
    {
        $res = OrdinaryPaymentAddService::getInstance()->getOptionsCostCompanyData($this->user, 2);
        if ($res['code'] == ErrCode::$SUCCESS) {
            return $this->returnJson(ErrCode::$SUCCESS, 'ok', $res['data']);
        }
        return $this->returnJson($res['code'], $res['message']);
    }

    /**
     * 普通付款申请-添加补充附件
     * @Token
     */
    public function applySupplementInvoiceAction()
    {
        $data = $this->request->get();

        Validation::validate($data, BaseService::$validate_supplement);

        $res = OrdinaryPaymentAddService::getInstance()->addFile($data);
        if ($res['code'] == ErrCode::$SUCCESS) {
            return $this->returnJson(ErrCode::$SUCCESS, $res['message'], isset($res['data']) ? $res['data'] : []);
        }

        return $this->returnJson($res['code'], $res['message']);
    }

    /**
     * 普通付款-数据查询-添加补充附件
     * @Permission(action='ordinary_payment.data.upload')
     * @api https://yapi.flashexpress.pub/project/133/interface/api/47947
     * @return mixed
     * @throws ValidationException
     */
    public function addSupplementInvoiceAction()
    {
        $data = $this->request->get();

        Validation::validate($data, array_merge(BaseService::$validate_supplement, ['should_pay_date' => 'Required|Date']));

        $res = OrdinaryPaymentAddService::getInstance()->addFile($data, BaseService::LIST_TYPE_DATA);
        if ($res['code'] == ErrCode::$SUCCESS) {
            return $this->returnJson(ErrCode::$SUCCESS, $res['message'], isset($res['data']) ? $res['data'] : []);
        }

        return $this->returnJson($res['code'], $res['message']);
    }

    /**
     * 获取列表筛选项中费用相关的字段配置
     *
     * @Token
     */
    public function getCostSearchFieldsAction()
    {
        try {
            $data = EnumsService::getInstance()->getCostSearchFields(BudgetObjectEnums::BUDGET_OBJECT_ORDER_TYPE_3);
            return $this->returnJson(ErrCode::$SUCCESS, 'success', $data);

        } catch (Exception $e) {
            $this->logger->error(
                'file ' . $e->getFile() .
                ' line ' . $e->getLine() .
                ' message ' . $e->getMessage() .
                ' trace ' . $e->getTraceAsString()
            );

            return $this->returnJson(ErrCode::$SYSTEM_ERROR, $this->t['retry_later'], []);
        }
    }

    /**
     * 获取符合条件的采购订单
     *
     * @Token
     * @api https://yapi.flashexpress.pub/project/133/interface/api/76512
     * @return Response|ResponseInterface
     * @throws ValidationException
     */
    public function getPurchaseOrderAction()
    {
        $param = $this->request->get();
        Validation::validate($param, ['company_id' => 'Required|IntGt:0', 'pono' => 'StrLenGeLe:0,20']);
        $res = OrderService::getInstance()->getPurchaseOrderOptions($param['company_id'], $param['pono'] ?? '');

        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }

    /**
     * 导入收款人信息
     * @Permission(action='ordinary_payment.apply.add')
     *
     * @api https://yapi.flashexpress.pub/project/133/interface/api/82040
     */
    public function importPayeeInfoAction()
    {
        $params = $this->request->get();

        $validate = [
            'staff_type' => 'Required|IntIn:1,2,3|>>>:params error[staff_type]',//员工类型
        ];

        Validation::validate($params, $validate);

        if (!$this->request->hasFiles()) {
            throw new ValidationException($this->t['bank_flow_not_found_file'], ErrCode::$VALIDATE_ERROR);
        }

        $file = $this->request->getUploadedFiles()[0];
        $extension = $file->getExtension();
        if (!in_array($extension, ['xlsx'])) {
            throw new ValidationException($this->t['file_format_error'], ErrCode::$VALIDATE_ERROR);
        }

        // 读取文件
        $config = ['path' => ''];
        $excel = new \Vtiful\Kernel\Excel($config);
        $excelData = $excel->openFile($file->getTempName())->openSheet()->getSheetData();

        $lock_key = md5('ordinary_payment_batch_import_payee_info_' . $this->user['id']);
        $res = $this->atomicLock(function () use ($params, $excelData) {
            return OrdinaryPaymentDetailService::getInstance()->importPayeeInfo($params['staff_type'], $excelData);
        }, $lock_key, 30);

        if (isset($res['code'])) {
            $code = $res['code'];
            $message = $res['message'];
            $data = $res['data'];
        } else {
            $code = ErrCode::$FREQUENT_VISIT_ERROR;
            $message = $this->t['sys_processing'];
            $data = [];
        }

        return $this->returnJson($code, $message, $data);
    }

    /**
     * 导出收款人信息
     * @Permission(menu='ordinary_payment')
     *
     * @api https://yapi.flashexpress.pub/project/133/interface/api/82049
     */
    public function exportPayeeInfoAction()
    {
        $params = $this->request->get();

        $validate = [
            'id' => 'Required|IntGt:0|>>>:params error[id]',//单据ID
        ];

        Validation::validate($params, $validate);

        $lock_key = md5('ordinary_payment_export_payee_info_' . $this->user['id']);
        $res = $this->atomicLock(function () use ($params) {
            return OrdinaryPaymentDetailService::getInstance()->exportPayeeInfo($params['id']);
        }, $lock_key, 20);

        if (isset($res['code'])) {
            $code = $res['code'];
            $message = $res['message'];
            $data = $res['data'];
        } else {
            $code = ErrCode::$FREQUENT_VISIT_ERROR;
            $message = $this->t['sys_processing'];
            $data = [];
        }

        return $this->returnJson($code, $message, $data);
    }

    /**
     * 关联保理付款单号筛选
     *
     * @Token
     * @api https://yapi.flashexpress.pub/project/133/interface/api/84134
     * @return Response|ResponseInterface
     */
    public function searchFactoringListAction()
    {
        $params = trim_array($this->request->get());
        $res = OrdinaryPaymentListService::getInstance()->searchFactoringList($params);
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }

    /**
     * 获取核算科目对应的未删除的财务分类
     * @Token
     * @api https://yapi.flashexpress.pub/project/133/interface/api/88013
     * @return Response|ResponseInterface
     * @throws ValidationException
     */
    public function getFinanceCategoryListAction()
    {
        $params = trim_array($this->request->get());
        Validation::validate($params, ['ledger_account_id' => 'Required|IntGt:0']);
        $res = OrdinaryPaymentListService::getInstance()->getFinanceCategoryList($params['ledger_account_id']);
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }

    /**
     * 普通付款 - 申请创建 - 预提编号 - 显示与否
     * @Permission(action='ordinary_payment.apply.add')
     * @api https://yapi.flashexpress.pub/project/133/interface/api/90629
     * @return Response|ResponseInterface
     * @throws ValidationException
     */
    public function showBudgetWithholdingAction()
    {
        $params = trim_array($this->request->get());
        Validation::validate($params, BaseService::$validate_show_budget_withholding_param);
        $res = OrdinaryPaymentListService::getInstance()->showBudgetWithholding($params);
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }

    /**
     * 普通付款 - 申请创建 - 预提编号 - 搜索、选择
     * @Permission(action='ordinary_payment.apply.add')
     * @api https://yapi.flashexpress.pub/project/133/interface/api/90644
     * @return Response|ResponseInterface
     * @throws ValidationException
     */
    public function searchBudgetWithholdingAction()
    {
        $params = trim_array($this->request->get());
        Validation::validate($params, BaseService::$validate_search_budget_withholding_param);
        $res = OrdinaryPaymentListService::getInstance()->searchBudgetWithholding($params);
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }

    /**
     * 费用预提-查看
     * @Token
     * @api https://yapi.flashexpress.pub/project/133/interface/api/91121
     * @return Response|ResponseInterface
     * @throws ValidationException
     */
    public function getBudgetWithholdingInfoAction()
    {
        $params = trim_array($this->request->get());
        Validation::validate($params, BudgetWithholdingService::$validate_id);
        $res = BudgetWithholdingService::getInstance()->detail($params, $this->user);
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }

    /**
     * 下载金额详情
     * @Permission(action='ordinary_payment.apply.view')
     * @return Response|ResponseInterface
     * @throws ValidationException
     * @throws BusinessException
     * @throws GuzzleException
     */
    public function downloadAmountDetailAction()
    {
        $params = trim_array($this->request->get());
        $validate = [
            'id' => 'Required|IntGt:0|>>>:params error[id]',
        ];

        Validation::validate($params, $validate);

        $res = OrdinaryPaymentDetailService::getInstance()->downloadAmountDetail($params['id'],$this->user['id']);
        return $this->returnJson(ErrCode::$SUCCESS,'ok', $res);
    }

}
