<?php


namespace App\Modules\JobTransfer\Services;


use App\Library\ApiClient;
use App\Library\Enums;
use App\Library\Enums\GlobalEnums;
use app\library\Enums\JobTransferEnums;
use App\Library\Enums\VehicleInfoEnums;
use App\Library\ErrCode;
use App\Library\Exception\BusinessException;
use App\Library\OssHelper;
use App\Library\Validation\ValidationException;
use App\Models\backyard\HrJobTitleModel;
use App\Models\backyard\HrStaffInfoModel;
use App\Models\backyard\JobTransferModel;
use App\Models\backyard\SettingEnvModel;
use App\Models\backyard\SysDepartmentModel;
use App\Modules\Common\Models\EnvModel;
use app\modules\Hc\models\HrJobDepartmentRelationModel;
use App\Modules\Hc\Models\SysStoreModel;
use App\Repository\HrStaffRepository;
use App\Modules\Organization\Services\DepartmentService;
use App\Traits\TokenTrait;

class SysService extends BaseService
{
    use TokenTrait;

    private const DEFAULT_LIST_SIZE = 10;

    public static $not_must_params = [
    ];

    public static $validate_currency = [
        'id'                   => 'Required|IntGe:1',                                             //审核ID
        //'organization_name' => 'Required|StrLenGeLe:2,300',                     //网点名称
        'audit_id'             => 'Required',                                                     //ID
        'status'               => 'Required|IntIn:2,3',                                           //审批状态
        'approval_arr'         => 'IfIntEq:status,2|Required|ArrLenGe:1',                         //审批同意修改资产数量
        'approval_arr[*].id'   => 'Required|StrLenGeLe:1,300',                                    //资产ID
        'approval_arr[*].nums' => 'Required|IntGe:1',                                             //数量
        'reject_reason'        => 'IfIntEq:status,3|Required|StrLenGeLe:1,300',                   //拒绝原因
    ];

    private static $instance;

    private function __construct()
    {
    }

    /**
     * @return SysService
     */
    public static function getInstance(): SysService
    {
        if (!isset(self::$instance) || !(self::$instance instanceof self)) {
            self::$instance = new self;
        }
        return self::$instance;
    }

    /**
     * 转岗-部门下拉
     * @param array $paramIn
     * @return array|false
     */
    public function getDepartmentList($paramIn = [])
    {
        $staffId       = $paramIn["staff_id"] ?? "";
        $sysDepartment = HrStaffInfoModel::findFirst([
            "conditions" => "staff_info_id = :staff_info_id:",
            "columns"    => "sys_department_id,node_department_id",
            "bind"       => ["staff_info_id" => $staffId],
        ]);
        if (empty($sysDepartment)) {
            return false;
        }
        $getAncestry = SysDepartmentModel::findFirst([
            'conditions' => 'id = :str: and deleted = 0',
            'bind'       => [
                'str' => $sysDepartment->sys_department_id,
            ],
            'columns'    => 'id,ancestry_v3',
        ]);
        if (empty($getAncestry)) {
            return false;
        }
        $getAncestry = $getAncestry->toArray();
        return SysDepartmentModel::find([
            'conditions' => '( ancestry_v3 like :chain: or id = :department_id: ) and deleted = 0',
            'bind'       => [
                'chain'         => $getAncestry['ancestry_v3']."/%",
                'department_id' => $getAncestry['id'],
            ],
            'columns'    => 'id,name',
            'order'      => 'name asc',
        ])->toArray();
    }

    /**
     * @description 转岗获取职位下拉列表
     * @param $paramIn
     * @return array
     */
    public function getPositionList($paramIn = []): array
    {
        $departmentId = $paramIn["department_id"] ?? "";

        if (empty($departmentId)) {
            return [];
        }

        $job_title = [];
        $countryCode = get_country_code();
        if ($countryCode == GlobalEnums::MY_COUNTRY_CODE && HrStaffRepository::isAgentStaff($paramIn['staff_id'])) {
            $individualContractorJobTitleConfig = (new SettingEnvModel())->getSetVal('individual_contractor_job_title', ',');
            $job_title = !empty($individualContractorJobTitleConfig) ? $individualContractorJobTitleConfig : [];
        } else if(HrStaffRepository::isLntCompany($paramIn['staff_id'])) { //如果是LNT公司
            $lnt_recruitment_job_ids = (new SettingEnvModel())->getSetVal('lnt_recruitment_job_ids', ',');
            $job_title = !empty($lnt_recruitment_job_ids) ? $lnt_recruitment_job_ids : [];
        }

        $builder = $this->modelsManager->createBuilder()
            ->columns('hr_job_department_relation.job_id as id, hr_job_title.job_name,hr_job_department_relation.working_day_rest_type')
            ->addFrom(HrJobDepartmentRelationModel::class, 'hr_job_department_relation')
            ->leftJoin(HrJobTitleModel::class, 'hr_job_department_relation.job_id = hr_job_title.id', 'hr_job_title')
            ->where('hr_job_title.status = 1');
        if (is_array($departmentId)) {
            $builder->inWhere('hr_job_department_relation.department_id', $departmentId);
        } else {
            $builder->andWhere('hr_job_department_relation.department_id = :department_id:',
                ['department_id' => $departmentId]);
        }

        if(!empty($job_title)) {
            $builder->inWhere('hr_job_title.id', $job_title);
        }

        $builder->orderby('CONVERT(hr_job_title.job_name USING gbk) ASC');
        $data = $builder->getQuery()->execute()->toArray();

        if (!empty($data)) {
            foreach ($data as $key => $value) {
                if (!empty($value['working_day_rest_type'])) {
                    $working_day_rest_type_arr = explode(',', $value['working_day_rest_type']);
                    foreach ($working_day_rest_type_arr as $w_v) {
                        $data[$key]['working_day_rest_type_items'][] = [
                            'key'   => $w_v,
                            'value' => self::$t->_('working_day_rest_type_'.$w_v),
                        ];
                    }
                } else {
                    $data[$key]['working_day_rest_type'] = [];
                }
            }
        }
        return $data;
    }

    /**
     * @description 转岗获取工作天数和轮休规则
     * @param $paramIn
     * @return array
     */
    public function getWorkingDayRestType($paramIn = []): array
    {
        $departmentId = $paramIn['department_id'] ?? '';
        $jobTitleId   = $paramIn['job_title_id'] ?? '';

        if (empty($departmentId) || empty($jobTitleId)) {
            return [];
        }

        $builder = $this->modelsManager->createBuilder()
            ->columns('h.working_day_rest_type')
            ->addFrom(HrJobDepartmentRelationModel::class, 'h');
        $builder->andWhere('h.department_id = :department_id:',
            ['department_id' => $departmentId]);
        $builder->andWhere('h.job_id = :job_id:',
            ['job_id' => $jobTitleId]);
        $data = $builder->getQuery()->getSingleResult();

        if (empty($data)) {
            return [];
        }
        $data                      = $data->toArray();
        $working_day_rest_type_arr = explode(',', $data['working_day_rest_type']);
        $countryCode               = get_country_code();

        foreach ($working_day_rest_type_arr as $item) {
            //排除掉自由轮休
            if (in_array($countryCode, [Enums\GlobalEnums::TH_COUNTRY_CODE, Enums\GlobalEnums::MY_COUNTRY_CODE]) && $item == Enums::WORKING_DAY_REST_TYPE_91) {
                continue;
            }
            $data['working_day_rest_type_items'][] = [
                'key'   => $item,
                'value' => self::$t->_('working_day_rest_type_' . $item),
            ];
        }
        $items                         = array_column($data['working_day_rest_type_items'], 'key');
        $data['working_day_rest_type'] = !empty($items) ? implode(',', $items) : '';
        return $data;
    }

    /**
     * 获取网点
     * @param array $paramIn
     * @return array
     */
    public function getStoreList($paramIn = []): array
    {
        $departmentId = $paramIn['department_id'] ?? '';
        $storeName    = $paramIn['store_name'] ?? '';

        //校验参数
        if (empty($departmentId)) {
            return [];
        }

        //获取指定部门下的网点
        $manageStoreIds = ManageOrganizationService::getInstance()->getManageStoresByDepartmentId($departmentId);
        if (empty($manageStoreIds)) {
            $manageStoreIds = $this->getRelevanceStoreIds($departmentId);
        }

        //获取搜索的网点
        $storeList   = $this->searchStoreListByName($storeName);
        $storeList   = array_column($storeList, null, 'id');
        $storeIdList = array_column($storeList, 'id');

        //取交集
        $intersection = array_values(array_intersect($manageStoreIds, $storeIdList));
        if (is_numeric(stripos(Enums::PAYMENT_HEADER_STORE_NAME, $storeName))) {
            $intersection[] = Enums::HEAD_OFFICE_STORE_FLAG;
        }
        $intersection = array_values(array_slice($intersection, 0, self::DEFAULT_LIST_SIZE));

        $result = array_map(function ($v) use ($storeList) {
            if ($v == Enums::HEAD_OFFICE_STORE_FLAG) {
                return [
                    'key'   => Enums::PAYMENT_HEADER_STORE_NAME,
                    'value' => $v,
                ];
            } else {
                return [
                    'key'   => $storeList[$v]['name'],
                    'value' => $v,
                ];
            }
        }, array_values($intersection));

        $result = array_values($result);
        return array_sort($result, 'key', SORT_ASC);
    }

    /**
     * @description 搜索 - 营业中的网点（注意目前只有th转岗在用这个逻辑）
     * @param $storeName
     * @return array
     */
    public function searchStoreListByName($storeName): array
    {
        if (get_country_code() == Enums\GlobalEnums::TH_COUNTRY_CODE){
            return SysStoreModel::find([
                'conditions' => 'use_state = :use_state: and name like :store_name:',
                'bind'       => [
                    'store_name' => $storeName.'%',
                    'use_state' => SysStoreModel::USE_STATE_YES,
                ],
                'columns'    => 'id,name',
            ])->toArray();
        }else{
            return SysStoreModel::find([
                'conditions' => 'name like :store_name:',
                'bind'       => [
                    'store_name' => $storeName.'%',
                ],
                'columns'    => 'id,name',
            ])->toArray();
        }

    }

    /**
     * 转岗获取hc下拉列表
     * @Access  public
     * @Param   array
     * @return array
     */
    public function getHcList($paramIn = []): array
    {
        //[1]获取参数
        $departmentId = $paramIn["department_id"] ?? "";
        $storeId      = $paramIn["store_id"] ?? "";
        $jobTitle     = $paramIn["job_title_id"] ?? "";

        if (empty($paramIn["department_id"]) || empty($paramIn["store_id"]) || empty($paramIn['job_title_id'])) {
            return [];
        }

        $ac = new ApiClient('by', '', 'get_job_transfer_hc_list_v2', static::$language);
        $ac->setParams([
            [
                'department_id' => $departmentId,
                'store_id'      => $storeId,
                'job_title_id'  => $jobTitle,
            ],
        ]);
        $res = $ac->execute();
        return $res['result']['data'] ?? [];
    }

    /**
     * 将数组打包成key => value
     * @param $data
     * @param bool $needTranslate
     * @return array
     */
    public function package($data, $needTranslate = true): array
    {
        $list = [];
        if (is_array($data)) {
            foreach ($data as $key => $value) {
                $list[] = [
                    'key'   => $key,
                    'value' => $needTranslate == true ? self::$t->_($value) : $value,
                ];
            }
        }
        return $list;
    }

    /**
     * 获取转岗类型下拉
     * @return array
     */
    public function getJobTransferTypes(): array
    {
        $type = $this->getTransferType();

        return $this->package($type);
    }

    /**
     * 获取转岗状态下拉
     * @return array
     */
    public function getJobTransferState(): array
    {
        $type = $this->getTransferState();

        return $this->package($type);
    }

    /**
     * 获取审批状态下拉
     * @param array $params
     * @return array
     */
    public function getJobTransferAuditState(array $params = []): array
    {
        $type = $params['state_type'] ?? JobTransferEnums::STATE_TYPE_APPLY;
        $list = $this->getAuditState($type);
        return $this->package($list);
    }

    /**
     * 获取转岗状态下拉
     * @return array
     */
    public function getJobTransferCarOwner(): array
    {
        $type = $this->getVehicleSource();

        return $this->package($type);
    }

    /**
     * 获取审批状态下拉
     * @return array
     */
    public function getJobTransferDataSource(): array
    {
        $type = $this->getDataSource();

        return $this->package($type);
    }

    /**
     * 获取审批状态下拉
     * @return array
     */
    public function getTransferReason(): array
    {
        $type = $this->getTransferReasonList();

        return $this->package($type);
    }

    /**
     * 获取审批状态下拉
     * @return array
     */
    public function getTransferSalaryType(): array
    {
        $type = $this->getSalaryType();

        return $this->package($type);
    }

    /**
     * 获取状态下拉列表
     * @Return array
     */
    public function getAuditState($type = JobTransferEnums::STATE_TYPE_APPLY): array
    {
        $stateList = [
            Enums::BY_APPROVAL_STATUS_PENDING  => self::$t->_('wms_audit_status.1'),
            Enums::BY_APPROVAL_STATUS_APPROVAL => self::$t->_('wms_audit_status.2'),
            Enums::BY_APPROVAL_STATUS_REJECT   => self::$t->_('wms_audit_status.3'),
            Enums::BY_APPROVAL_STATUS_CANCEL   => self::$t->_('wms_audit_status.4'),
        ];
        if ($type == JobTransferEnums::STATE_TYPE_TOTAL) {
            $stateList[Enums::BY_APPROVAL_STATUS_OVER_TIME] = self::$t->_('wms_audit_status.5');
        }
        return $stateList;
    }

    /**
     * 获取原因下拉列表
     * @Return array
     */
    public function getTransferReasonList(): array
    {
        return [
            JobTransferModel::JOB_TRANSFER_REASON_STORE_INTEGRATION   => self::$t->_('job_transfer_reason.1'),
            JobTransferModel::JOB_TRANSFER_REASON_STORE_NEWLY_OPENED  => self::$t->_('job_transfer_reason.2'),
            JobTransferModel::JOB_TRANSFER_REASON_ADJUST_SERVICE_AREA => self::$t->_('job_transfer_reason.3'),
            JobTransferModel::JOB_TRANSFER_REASON_STORE_UPGRADE       => self::$t->_('job_transfer_reason.4'),
            JobTransferModel::JOB_TRANSFER_REASON_INSUFFICIENT_STAFF  => self::$t->_('job_transfer_reason.5'),
            JobTransferModel::JOB_TRANSFER_REASON_OTHERS              => self::$t->_('job_transfer_reason.99'),
        ];
    }

    /**
     * 获取转岗类型下拉列表
     * @Return array
     */
    public function getTransferType(): array
    {
        return [
            Enums::JOB_TRANSFER_TYPE_IN_DEP => self::$t->_('job_transfer_type.1'),
        ];
    }

    /**
     * 获取转岗状态下拉列表
     * @Return array
     */
    public function getTransferState(): array
    {
        return [
            Enums::JOB_TRANSFER_STATE_PENDING => self::$t->_('job_transfer_state.1'), //待转岗
            Enums::JOB_TRANSFER_STATE_NO      => self::$t->_('job_transfer_state.2'), //未转岗
            Enums::JOB_TRANSFER_STATE_SUCCESS => self::$t->_('job_transfer_state.3'), //转岗成功
            Enums::JOB_TRANSFER_STATE_FAIL    => self::$t->_('job_transfer_state.4'), //转岗失败
        ];
    }

    /**
     * 获取状态下拉列表
     * @Return array
     */
    public function getTransferDataSource(): array
    {
        return [
            Enums::DATA_FROM_BY => 'Backyard',
            Enums::DATA_FROM_OA => 'OA',
        ];
    }

    /**
     * 获取车辆归属下拉列表
     * @Return array
     */
    public function getVehicleSource(): array
    {
        return [
            Enums::CAR_OWNER_PERSONAL_STATUS => self::$t->_('car_owner.1'),
            Enums::CAR_OWNER_COMPANY_STATUS  => self::$t->_('car_owner.2'),
            Enums::CAR_OWNER_BORROW_STATUS   => self::$t->_('car_owner.3'),
        ];
    }

    /**
     * 获取数据来源下拉列表
     * @Return array
     */
    private function getDataSource(): array
    {
        return [
            Enums::DATA_FROM_BY => 'Backyard',
            Enums::DATA_FROM_OA => 'OA',
        ];
    }

    /**
     * 获取转岗角色下拉
     * @param array $paramIn
     * @return array
     */
    public function getRoleList($paramIn = []): array
    {
        $transferInfo = JobTransferModel::findFirst($paramIn['id']);
        if (empty($transferInfo)) {
            return [];
        }

        //指定部门、职位关联的角色
        $ac = new ApiClient('hris', '', 'department_job_title_role_by_store', static::$language);
        $ac->setParams([[
            'department_id' => $paramIn['department_id'],
            'job_title_id'  => $paramIn['job_title_id'],
            'sys_store_id'  => $transferInfo->after_store_id,
        ]]);
        $return = $ac->execute();


        return $return["result"]['data'] ?? [];
    }

    /**
     * 获取全部角色下拉列表
     */
    public function getAllRoleList(): array
    {
        $ac = new ApiClient('hris', '', 'role_list', static::$language);
        $ac->setParams(['']);
        $ret = $ac->execute();
        return $ret['result'] ? array_column($ret['result'], null, 'role_id') : [];
    }

    /**
     * 获取批号
     * @throws \Exception
     */
    public function getBatchNumber(): array
    {
        $key = $this->getTokenId();

        return [
            'code' => ErrCode::$SUCCESS,
            'data' => $key,
        ];
    }

    /**
     * @description 下载一线职位清单
     */
    public function downloadFrontLineJobList()
    {
        //转岗一线职位（部门|职位,部门|职位）, key: job_transfer_front_line_position
        $positionList = (new SettingEnvModel())->listByCode([
            'job_transfer_front_line_position',
            'job_transfer_front_line_position_output',
        ]);
        $positionList = array_column($positionList, 'set_val', 'code');

        if (empty($positionList['job_transfer_front_line_position'])) {
            throw new ValidationException('尚未配置一线职位清单，请联系产品处理');
        }

        $positionConfig = $positionList['job_transfer_front_line_position'];
        if (empty($positionList['job_transfer_front_line_position_output'])) {
            return $this->genFrontLinePositionExcel($positionConfig);
        }

        $result = json_decode($positionList['job_transfer_front_line_position_output'], true);
        if (md5($positionConfig) != $result['version']) {
            return $this->genFrontLinePositionExcel($positionConfig);
        }
        return $result['path'] ?? '';
    }

    public function getFrontLinePositionData($positionConfig)
    {
        if (empty($positionConfig)) {
            return [];
        }
        $result      = [];
        $jobTitleIds = explode(',', $positionConfig);

        $jobTitleInfo = HrJobTitleModel::find([
            'conditions' => 'id in ({ids:array})',
            'bind'       => ['ids' => $jobTitleIds],
            'columns'    => 'id,job_name',
        ])->toArray();
        $jobTitleInfo = array_column($jobTitleInfo, 'job_name', 'id');

        foreach ($jobTitleIds as $item) {
            $result[] = [
                $jobTitleInfo[$item] ?? '',
            ];
        }
        return $result;
    }

    /**
     * @description 获取一线职位配置
     * @return array
     *
     * 返回数据结构
     * 【key = 部门ID】 = 【value = 职位ID】
     */
    public function getFrontLineConfig(): array
    {
        $settingEnv     = new SettingEnvModel();
        return $settingEnv->getSetVal('job_transfer_front_line_position', ',');
    }

    public function getExcelHeader(): array
    {
        return [
            static::$t->_('department.manager_job_title'), //职位
        ];
    }

    private function genFrontLinePositionExcel($positionConfig)
    {
        $data = $this->getFrontLinePositionData($positionConfig);

        $excel_extra_config = [
            'file_name'       => 'FirstLineList.xlsx',
            'end_column_char' => 'C',
            'column_width'    => 15,
        ];
        $path               = self::customizeExcelToFile($this->getExcelHeader(), $data, $excel_extra_config);
        $oss_result         = OssHelper::uploadFile($path);

        $configModel          = SettingEnvModel::findFirst([
            'conditions' => "code = :code:",
            'bind'       => [
                'code' => 'job_transfer_front_line_position_output',
            ],
        ]);
        $configModel->set_val = json_encode([
            'version' => md5($positionConfig),
            'path'    => $oss_result['object_url'] ?? '',
        ]);
        $configModel->save();

        return $oss_result['object_url'] ?? '';
    }

    /**
     * 获取批量导入特殊转岗模版
     * @return mixed
     */
    public function downloadSpecialImportTemplate()
    {
        $settingEnv   = new SettingEnvModel();
        $template     = $settingEnv->getValByCode('download_import_special_transfer_template');
        $templateList = json_decode($template, true);
        $language     = strtolower(substr(static::$language, 0, 2));
        return $templateList[$language] ?? $templateList['en'];
    }

    /**
     * 转岗管理-数据查询-导出payroll 权限员工
     * @return array|false|string[]
     */
    public function getExportForPayrollPermissionStaffIds()
    {
        $staffIdsStr = EnvModel::getEnvByCode('transfer_export_salary');
        return empty($staffIdsStr) ? [] : explode(',', $staffIdsStr);
    }

    /**
     * @return array[]
     */
    public function getSalaryType(): array
    {
        return [
            JobTransferEnums::SALARY_TYPE_NOT_CHANGE       => self::$t->_('job_transfer.salary_type_1'),
            JobTransferEnums::SALARY_TYPE_SALARY_STRUCTURE => self::$t->_('job_transfer.salary_type_2'),
        ];
    }

    /**
     * 获取项目期数
     * @return array
     */
    public function getTransferProjectNum($params = []): array
    {
        switch ($params['job_title_id']) {
            case JobTransferEnums::JOB_EV_TITLE_ID:
                $type = $this->getProjectNum();
                break;
            case JobTransferEnums::JOB_VAN_PROJECT_TITLE_ID:
                $type = $this->getVanProjectNum();
                break;
            default:
                $type = [];
        }

        return $this->package($type);
    }

    /**
     * @return array
     */
    private function getProjectNum(): array
    {
        return [
            JobTransferEnums::PROJECT_NUM_1 => self::$t->_('project_num_1'),
            JobTransferEnums::PROJECT_NUM_2 => self::$t->_('project_num_2'),
        ];
    }

    public function getTransferCarType($audit_id)
    {
        $transferInfo = JobTransferModel::findFirst($audit_id);
        if (!$transferInfo) {
            return [];
        }
        $lntVehicleType = $vehicleType = [];

        //若所选转岗员工的合同公司=LNT，转岗后职位=Van Courier/Car Courier时，车类型字段可选：
        //  1. HCM-设置中心-lnt_vehicle_type_ids, 示例：110/4,110/9,1199/7,1199/8
        if (HrStaffRepository::isAgentStaff($transferInfo->staff_id)) {
            return $this->getCarTypeByJobTitle($transferInfo->after_position_id);
        } else if(HrStaffRepository::isLntCompany($transferInfo->staff_id)) {
            $lnt_vehicle_type_ids = (new SettingEnvModel())->getSetVal('lnt_vehicle_type_ids', ',');
            foreach ($lnt_vehicle_type_ids as $oneType) {
                $typeInfo = explode('/', $oneType);
                $lntVehicleType[$typeInfo[0]][] = $typeInfo[1];
            }

            if(!empty($transferInfo->after_position_id) && isset($lntVehicleType[$transferInfo->after_position_id])) {
                foreach ($lntVehicleType[$transferInfo->after_position_id] as $oneTypes) {
                    if(!isset(JobTransferEnums::VEHICLE_TYPE_CATEGORY_LIST[$oneTypes])) {
                        continue;
                    }
                    $vehicleType[] = ['value' => intval($oneTypes), 'label' => JobTransferEnums::VEHICLE_TYPE_CATEGORY_LIST[$oneTypes]];
                }
                return $vehicleType;
            }

            return [];
        }

        $vehicleType = JobTransferEnums::JOB_TITLE_VEHICLE_CATEGORY_ITEM[(int)$transferInfo->after_position_id] ?? [];
        if (empty($vehicleType)) {
            return [];
        }
        return $vehicleType;
    }

    /**
     * @param $job_title_id
     * @return array
     */
    public function getCarTypeByJobTitle($job_title_id): array
    {
        if (empty($job_title_id)) {
            return [];
        }

        // 获取配置
        $envModel = (new SettingEnvModel());
        $icVehicleTypeData = $envModel->getSetVal('ic_vehicle_type_ids', ',');

        // 初始化结果数组
        $vehicleTypeList = $result = [];

        foreach ($icVehicleTypeData as $item) {
            // 分割字符串
            [$position, $carType] = explode('/', $item);

            // 如果职位不存在于结果数组中，则初始化一个空数组
            if (!isset($result[$position])) {
                $result[$position] = [];
            }

            // 将车类型ID添加到对应职位的数组中
            $result[$position][] = (int)$carType;
        }

        $vehicleTypes = $result[intval($job_title_id)] ?? [];
        if (empty($vehicleTypes)) {
            return [];
        }

        foreach ($vehicleTypes as $oneType) {
            if(!isset(VehicleInfoEnums::VEHICLE_TYPE_CATEGORY_LIST[$oneType])) {
                continue;
            }
            $vehicleTypeList[] = ['value' => $oneType, 'label' => VehicleInfoEnums::VEHICLE_TYPE_CATEGORY_LIST[$oneType]];
        }
        return $vehicleTypeList;
    }

    /**
     * 获取转岗雇佣类型下拉列表
     * @param $params
     * @return array
     * @throws BusinessException
     */
    public function getTransferHireType($params = [])
    {
        if (get_country_code() == Enums\GlobalEnums::TH_COUNTRY_CODE) {
            if (!empty($params['staff_id'])) {
                $ac = new ApiClient('by', '', 'get_transfer_hire_type_list', static::$language);
                $ac->setParams(
                    [
                        [
                            'staff_id'      => $params['staff_id'],
                            'department_id' => $params['department_id'],
                            'position_id'   => $params['position_id'],
                            'store_id'      => $params['store_id'],
                        ]
                    ]
                );
                $res = $ac->execute();
                if ($res['result']['code'] != ErrCode::$SUCCESS) {
                    throw new BusinessException($res['result']['msg']);
                }
                return $res['result']['data'];
            } else {
                return [
                    [
                        'label' => static::$t->_('hire_type_1'),
                        'value' => 1,
                    ],
                    [
                        'label' => static::$t->_('hire_type_2'),
                        'value' => 2,
                    ],
                    [
                        'label' => static::$t->_('hire_type_3'),
                        'value' => 3,
                    ],
                    [
                        'label' => static::$t->_('hire_type_4'),
                        'value' => 4,
                    ],
                    [
                        'label' => static::$t->_('hire_type_13'),
                        'value' => 13,
                    ],
                ];
            }
        } else {
            return [
                [
                    'label' => static::$t->_('hire_type_1'),
                    'value' => 1,
                ],
                [
                    'label' => static::$t->_('hire_type_2'),
                    'value' => 2,
                ],
            ];
        }
    }

    private function getVanProjectNum(): array
    {
        return [
            JobTransferEnums::PROJECT_NUM_1 => self::$t->_('van_courier_project_num_1'),
            JobTransferEnums::PROJECT_NUM_2 => self::$t->_('van_courier_project_num_2'),
            JobTransferEnums::PROJECT_NUM_3 => self::$t->_('van_courier_project_num_3'),
            JobTransferEnums::PROJECT_NUM_4 => self::$t->_('van_courier_project_num_4'),
        ];
    }

    /**
     * 获取薪资范围
     * @param $params
     * @return array
     */
    public function getSalaryRange($params): array
    {
        $queryParams = [
            'job_transfer_id' => $params['id'],
        ];
        $ac = new ApiClient('hcm_rpc', '', 'get_job_transfer_salary_range');
        $ac->setParams([$queryParams]);
        $ac_result = $ac->execute();

        if (!isset($ac_result['result']['code']) || $ac_result['result']['code'] != ErrCode::$SUCCESS) {
            $this->logger->error(sprintf('get getTransferSalary err, params: %s', json_encode($params)));
        }
        if (empty($ac_result['result']['data'])) {
            return [];
        }
        return $ac_result['result']['data'];
    }

    /**
     * 获取关联网点
     * @param $departmentId
     * @return array
     */
    private function getRelevanceStoreIds($departmentId): array
    {
        $storeIds = [];
        switch (get_country_code()) {
            case Enums\GlobalEnums::TH_COUNTRY_CODE:
                $storeIds = $this->getSpecDepartmentRelevanceStoreIds([25, 20001], $departmentId);
                break;
            case Enums\GlobalEnums::PH_COUNTRY_CODE:
                $storeIds = $this->getSpecDepartmentRelevanceStoreIds([126, 26], $departmentId);
                break;
            default:
                break;
        }
        return $storeIds;
    }

    /**
     * 获取指定部门的关联网点 id
     * @param array $department_ids
     * @param $origin_department_id
     * @return array
     */
    public function getSpecDepartmentRelevanceStoreIds($department_ids = [], $origin_department_id): array
    {
        if (empty($department_ids)) {
            return [];
        }
        $manageStoreIds     = [];
        $department_service = new DepartmentService();
        foreach ($department_ids as $department_id) {
            $departmentHubInfo   = $department_service->getChildrenListByDepartmentIdV2($department_id, true);
            $departmentHubInfo[] = $department_id;
            if (in_array($origin_department_id, $departmentHubInfo)) {
                $manageStoreIds = array_merge($manageStoreIds,
                    $department_service->getRelevanceStoreByDepartmentId($departmentHubInfo));
            }
        }
        return $manageStoreIds;
    }


}