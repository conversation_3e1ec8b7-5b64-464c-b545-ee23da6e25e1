<?php
/**
 * pdf加水印
 */

namespace App\Modules\Common\Services;

use App\Library\BaseService;
use App\Library\ErrCode;
use App\Library\Exception\BusinessException;
use App\Library\OssHelper;
use App\Library\PdfWatermark\FpdiPdfWatermarker as PDFWatermarker;
use App\Library\PdfWatermark\Pdf;
use App\Library\PdfWatermark\Watermark;
use App\Library\Validation\ValidationException;
use Exception;
use GuzzleHttp\Exception\GuzzleException;

class WaterMarkerService extends BaseService
{
    private static $instance;
    public static  $waterMarkUrl = '';

    public static function getInstance()
    {
        if (!isset(self::$instance) || !(self::$instance instanceof self)) {
            self::$instance = new self;
        }

        return self::$instance;
    }

    /**
     * pdf文件加水印
     * @param string $file_url
     * @param string $file_name
     * @param string $watermark 水印
     * @return mixed
     */
    public function addWaterMarkerToPdfFile(string $file_url, string $file_name, string $watermark = BASE_PATH . '/public/images/watermark_logo.png')
    {
        try {
            // 判断文件是否是pdf
            $file_info = pathinfo($file_url);
            if ($file_info['extension'] != 'pdf') {
                throw new ValidationException('非pdf文件, file_info: ' . json_encode($file_info, JSON_UNESCAPED_UNICODE), ErrCode::$VALIDATE_ERROR);
            }

            $pdf = new Pdf($file_url);
            $watermark = new Watermark($watermark);
            $pdf_water_marker = new PDFWatermarker($pdf, $watermark);
            return $pdf_water_marker->savePdf($file_name);
        } catch (ValidationException $e) {
            $this->logger->notice('pdf watermarking abnormal:' . $e->getMessage() . "params: [file_url=$file_url; file_name=$file_name]");
        } catch (Exception $e) {
            $this->logger->warning('pdf watermarking abnormal:' . $e->getMessage() . "params: [file_url=$file_url; file_name=$file_name]");
        }

        return false;
    }

    /**
     * pdf文件加水印(Java服务实现)
     *
     * @param string $file_url 原pdf文件路径
     * @param string $file_name 原文件名, 带后缀(test.pdf)
     * @param boolean $is_act_private 是否私有化 true是，false否
     * @return mixed
     * @throws GuzzleException
     */
    public function addWaterMarkerToPdfFileV2(string $file_url, string $file_name, bool $is_act_private = false)
    {
        $upload_res = [];

        try {
            // 判断文件是否是pdf
            $file_info = pathinfo($file_url);
            if (($file_info['extension'] != 'pdf' && !$is_act_private) || ($is_act_private && strrpos($file_name, '.pdf') === false)) {
                throw new ValidationException('非pdf文件, file_info: ' . json_encode($file_info, JSON_UNESCAPED_UNICODE), ErrCode::$VALIDATE_ERROR);
            }

            // 调用Java加水印接口
            if (!empty(self::$waterMarkUrl)) {
                $water_mark_url = self::$waterMarkUrl;
            } else {
                $water_mark_url = EnumsService::getInstance()->getSettingEnvValue('flash_company_water_marker_logo_url');
            }

            $paf_params = [
                'nonStrokingAlphaConstant' => 0.55,            //选填 水印透明度   不传默认0.55,
                'pdfUrl'                   => $file_url,       // 必填 pdf文件URL
                'waterMarkUrl'             => $water_mark_url, // 必填 水印图片URL
            ];

            $response_data = CommonService::getInstance()->newPostRequest(env('pdf_rpc_endpoint') . '/api/pdf/addWaterMarkToPdf', json_encode($paf_params, JSON_UNESCAPED_UNICODE));

            $object_url = $response_data['data']['object_url'] ?? '';
            if (empty($object_url)) {
                throw new BusinessException('pdf加水印失败, result=' . json_encode($response_data, JSON_UNESCAPED_UNICODE), ErrCode::$BUSINESS_ERROR);
            }

            // 文件名加后缀
            if (mb_substr($file_name, -4) != '.pdf') {
                $file_name .= '.' . $file_info['extension'];
            }

            // 写入本地文件
            $local_file_path = sys_get_temp_dir() . '/' . mt_rand(100, 999) . $file_name;
            $put_res = file_put_contents($local_file_path, file_get_contents($object_url));

            if ($put_res === false) {
                throw new BusinessException('pdf加水印成功, 写入本地临时文件失败, file_put_contents result=' . $put_res, ErrCode::$BUSINESS_ERROR);
            }

            // 生成成功, 上传OSS
            $upload_res = OssHelper::uploadFile($local_file_path, '', $file_name, $is_act_private);

        } catch (ValidationException $e) {
            $this->logger->notice('pdf watermarking abnormal:' . $e->getMessage() . "params: [file_url=$file_url; file_name=$file_name]");
        } catch (Exception $e) {
            $this->logger->warning('pdf watermarking abnormal:' . $e->getMessage() . "params: [file_url=$file_url; file_name=$file_name]");
        }

        return $upload_res;
    }

}
