<?php
/**
 * 全局通用的静态枚举配置 V1.0.0 2021.04
 */

namespace App\Modules\Common\Services;

use App\Library\ApiClient;
use App\Library\BaseService;
use App\Library\Enums;
use App\Library\Enums\ContractEnums;
use App\Library\Enums\GlobalEnums;
use App\Library\Enums\PayEnums;
use App\Library\Enums\ReimbursementEnums;
use App\Library\Enums\StaffInfoEnums;
use App\Library\Enums\ReserveFundReturnEnums;
use App\Library\Enums\PurchaseEnums;
use App\Library\Enums\SettingEnums;
use App\Library\ErrCode;
use App\Library\Exception\BusinessException;
use App\Library\Validation\ValidationException;
use app\models\backyard\StaffPayrollCompanyInfoModel;
use App\Models\oa\SettingEnvModel;
use App\Models\oa\SysModuleModel;
use App\Modules\Common\Models\ContractCompanyModel;
use App\Modules\Common\Models\CurrencyModel;
use App\Modules\Common\Models\EnvModel;
use App\Modules\Payment\Models\PaymentCostType;
use App\Modules\Purchase\Services\BaseService as PurchaseService;
use App\Modules\Reimbursement\Models\BudgetObject;
use App\Modules\Reimbursement\Models\BudgetObjectOrder;
use App\Modules\Reimbursement\Models\BudgetObjectProduct;
use App\Models\oa\ContractCategoryModel;
use App\Models\oa\SupplierBankModel;
use App\Repository\DepartmentRepository;
use App\Repository\oa\ReimbursementTripTimeLimitRepository;
use App\Repository\oa\SettingInvoiceHeaderRepository;
use App\Util\RedisKey;
use Exception;

class EnumsService extends BaseService
{
    private static $instance;
    private $sys_department_company_ids;
    private $pay_module_payer;
    /**
     *
     */
    public static function getInstance()
    {
        if (!isset(self::$instance) || !(self::$instance instanceof self)) {
            self::$instance = new self;
        }

        return self::$instance;
    }
    /**
     * 获取全局枚举配置
     * @param array $enums_types 枚举配置的类型: 比如 币种:currency; 付款方式:payment_method; 合同所述公司:contract_company
     * @return mixed
     */
    public function getGlobalEnumsSetting(array $enums_types = [])
    {
        $enums_types = $enums_types ? array_filter(array_unique($enums_types)) : [];
        $enums_item = [
            'currency' => GlobalEnums::$currency_item,
            'payment_method' => GlobalEnums::$payment_method_item,
            'contract_company' => $this->getContractCompanyItem(),
            'loan_status' => Enums\LoanEnums::$loan_status,
            'pay_status' => Enums::$loan_pay_status,
            'apply_status' => Enums::$payment_apply_status,
            'staff_status' => StaffInfoEnums::$staff_state,
            'reserve_return_status' => ReserveFundReturnEnums::$back_status,//备用金～归还状态
            'purchase_apply_business_type' => PurchaseEnums::$apply_business_type,//采购申请单-业务类型枚举
            'payroll_company_info' => $this->getPayrollCompanyInfo(),
        ];

        $enums_keys = !empty($enums_types) ? $enums_types : array_keys($enums_item);

        $data = [];
        foreach ($enums_keys as $key) {
            $_tmp_enums = $enums_item[$key] ?? [];
            if (empty($_tmp_enums)) {
                continue;
            }

            $_tmp_data = [];
            foreach ($_tmp_enums as $code => $t_key) {
                // 公司的label不分语言
                $label = $key == 'contract_company' ? $t_key : self::$t[$t_key];
                $_tmp_data[] = [
                    'code' => $code,
                    'label' => $label,
                ];
            }

            $data[$key] = $_tmp_data;
        }

        return $data;
    }

    /**
     * 根据枚举code获取对应的翻译项
     * @param string $enums_type
     * @param string $code
     * @return mixed
     */
    public function getCodeValue(string $enums_type = '', string $code = '')
    {
        if (empty($enums_type)) {
            return '';
        }

        $enums = [];
        switch ($enums_type) {
            case 'currency':
                $enums = GlobalEnums::$currency_item;
                break;
            case 'payment_method':
                $enums = GlobalEnums::$payment_method_item;
                break;
        }

        return !empty($enums[$code]) ? self::$t[$enums[$code]] : '';
    }

    /**
     * 获取指定币种与系统默认币种间的汇率
     *
     * @param int $currency
     * @return mixed
     */
    public function getCurrencyExchangeRate(int $currency)
    {
        GlobalEnums::init();

        // 币种参数有误 或 未配置
        if (!array_key_exists($currency, GlobalEnums::$currency_item)) {
            $logger_content = '传入的币种 - ' . $currency;
            $logger_content .= '; 币种清单配置 - '.json_encode(array_keys(GlobalEnums::$currency_item), JSON_UNESCAPED_UNICODE);
            $this->logger->error('币种汇率转换异常: ' . $logger_content);
            return false;
        }

        $exchange_rate_key = GlobalEnums::$currency_symbol_map[$currency] . '_TO_' . GlobalEnums::$sys_default_currency_symbol;
        $exchange_rate = GlobalEnums::$exchange_rate_map[$exchange_rate_key] ?? false;
        if ($exchange_rate === false) {
            $this->logger->error("币种汇率转换异常: {$exchange_rate_key}; 币种汇率配置=" . json_encode(GlobalEnums::$exchange_rate_map, JSON_UNESCAPED_UNICODE));
        }

        return $exchange_rate;
    }

    /**
     * 获取系统默认币种
     * @return mixed
     */
    public function getSysCurrencyInfo()
    {
        GlobalEnums::init();

        return [
            'code' => GlobalEnums::$sys_default_currency,
            'symbol' => GlobalEnums::$sys_default_currency_symbol,
            'symbol_simple' => GlobalEnums::$sys_default_currency_symbol_simple,
        ];
    }

    /**
     * 币种金额转换: 金额转换为系统默认币种的金额, By 汇率
     *
     * @param string $curr_currency 当前币种
     * @param string $curr_amount 当前金额
     * @param int $scale 转换后金额保留的小数位
     *
     * @return mixed 返回转换后币种对应的金额C
     */
    public function currencyAmountConversion(string $curr_currency, string $curr_amount, $scale = 2)
    {
        // 确定币种转换的汇率
        $exchange_rate = $this->getCurrencyExchangeRate($curr_currency);
        if (!$exchange_rate) {
            $logger_content = '传入参数 - ' . json_encode(func_get_args(), JSON_UNESCAPED_UNICODE);
            $this->logger->error('币种金额转换异常: ' . $logger_content);
            return false;
        }

        return sprintf("%.{$scale}f", bcmul($curr_amount, $exchange_rate, $scale));
    }

    /**
     * 金额按汇率计算, By 指定汇率
     *
     * @param string $amount 当前金额
     * @param string $exchange_rate 当前币种
     * @param int $scale 转换后金额保留的小数位
     *
     * @return mixed 返回转换后币种对应的金额C
     */
    public function amountExchangeRateCalculation(string $amount, string $exchange_rate, $scale = 2)
    {
        // 确定币种转换的汇率
        $exchange_rate = $exchange_rate ? $exchange_rate : 1;
        return sprintf("%.{$scale}f", bcmul($amount, $exchange_rate, $scale));
    }

    /**
     * 获取预算开关状态
     * @return string
     */
    public function getBudgetStatus()
    {
        static $budget_status = null;
        if (!is_null($budget_status)) {
            return $budget_status;
        }

        return EnvModel::getEnvByCode('budget_status', 0);
    }

    /**
     * 获取合同邮件地址
     * @return string
     */
    public function getContractAttachmentAddress(){
        return EnvModel::getEnvByCode('contract_attachment_address','');
    }

    /**
     * 获得系统支付模块是否开启
     * @return integer 1.开启, 0.未开启
     */
    public function getSystemPayModuleStatus()
    {
        static $status = null;
        if (!isset($status)) {
            //默认值:未开启
            $default_status = 0;
            //查询系统是否开启支付模块
            $status = EnvModel::getEnvByCode('pay_module_status', $default_status);
        }
        return $status;
    }

    /**
     * 获得单个模块的支付模块是否开启
     * @param string $module_key 模块key
     * @param int $company_id 公司id
     * @return integer 1.开启, 0.未开启
     */
    public function getPayModuleStatus($module_key, $company_id = 0)
    {
        static $module_status = null;
        if (!isset($module_status)) {
            //默认值:未开启
            $default_status = 0;
            if (empty($module_key)) {
                return $default_status;
            }
            //查询系统是否开启支付模块
            $module_status = EnvModel::getEnvByCode('pay_module_status', $default_status);
            //查询模块是否开启支付模块
            if ($module_status == PayEnums::PAY_MODULE_STATUS_OPEN) {
                //如果系统开启了支付,再查模块是否开启
                $sys_module_info = SysModuleModel::findFirst([
                    'conditions' => 'name_key = :name_key:',
                    'bind' => ['name_key' => $module_key],
                    'columns' => 'pay_module_status'
                ]);
                $module_status = $sys_module_info ? $sys_module_info->pay_module_status : $default_status;
                //查询当前公司是否不启用, 如果不启用, 开启状态为0
                if (!empty($company_id) && $module_status == PayEnums::PAY_MODULE_STATUS_OPEN) {
                    $unusable_company = $this->getSettingEnvValueIds('payment_unusable_company_ids');
                    if (!empty($unusable_company) && in_array($company_id, $unusable_company)) {
                        $module_status = 0;
                    }
                }
            }
        }
        return $module_status;
    }

    /**
     * 获得支票模块是否开启
     * @return string|null
     */
    public function getChequeModuleStatus()
    {
        static $status = null;
        if (!isset($status)) {
            $status = EnvModel::getEnvByCode('cheque_module_status', 0);
        }
        return $status;
    }

    /**
     * 获得支付模块支付人
     * @return array
     */
    public function getPayModulePayer()
    {
        if (!isset($this->pay_module_payer)) {
            $payer = EnvModel::getEnvByCode('pay_module_payer', []);
            if (!empty($payer)) {
                $payer = trim($payer);
                $payer = explode(',', $payer);
            }
            $this->pay_module_payer = $payer;
        }
        return $this->pay_module_payer;
    }

    /**
     * 获取支付信息 - 付款银行/账号配置
     */
    public function getPaymentBankAndAccountList()
    {
        $res = EnvModel::getEnvByCode('reimbursement_bank', []);

        if (!empty($res)) {
            $res = json_decode($res,true);
        }

        return $res;
    }

    /**
     * 获取费用类型列表
     * @return array
     */
    public function getStoreRentPaymentCostTypeItem()
    {
        //9888【OA|网点租房付款】功能优化 需要将此块由配置修改为读取表数据
        $list = PaymentCostType::find([
            'conditions' => "is_deleted = :is_deleted:",
            'bind' => ['is_deleted' => GlobalEnums::IS_NO_DELETED],
            'columns' => ["id", "name"]
        ])->toArray();
        $item = [];
        if (!empty($list)) {
            foreach ($list as $key => $val) {
                $item[$val['id']] = 'payment_cost_type_' . strtolower(get_country_code()) . "_{$val['id']}";
            }
        }
        return $item;
    }

    /**
     * 获取支付信息 - 付款银行字符串
     */
    public function getPaymentBankString()
    {
        $res = EnvModel::getEnvByCode('reimbursement_bank', []);

        if (empty($res)) {
            return ['bank_name'=>'','bank_account'=>''];
        }
        $array_res = json_decode($res,true);
        $bank_name = array_column($array_res,'pay_bank_name');
        $bank_account = array_column($array_res,'pay_bank_account');
        $all_bank_name = implode(',',$bank_name);
        $all_bank_account = implode(',',$bank_account);
        return ['bank_name'=>$all_bank_name,'bank_account'=>$all_bank_account];
    }
    /**
     * 获取网点租房付款合同支付类型
     * @return mixed|string
     */
    public function getStoreRentPaymentContractPayTypeItem()
    {
        $item = EnvModel::getEnvByCode('store_rent_payment_contract_pay_type', []);
        if (!empty($item)) {
            $item = json_decode($item, true);
            foreach ($item as $key => $val) {
                $item[$key] = "rent_contract_pay_type_{$key}";
            }
        }
        return $item;
    }
    // 初始化合同语言
    public static function getContractLang(){
        $langStr = EnvModel::getEnvByCode('contract_store_renting_lang');
        $langArr = !empty($langStr) ? json_decode($langStr,true) : [];
        $langRes = [];
        foreach ($langArr as $lang) {
            $langRes[$lang['id']] = $lang['name'];
        }
        return $langRes;
    }

    /**
     * 获取组织架构中公司id
     * 单例模式获取,本次进程只读取一次setting_env表,如果需要查询最新数据$refresh传true
     * 逐步取代Enums::$company_types,代码中全部替换完毕后,可以删除Enums中的$company_types
     * @param bool $refresh
     * @return array
     * @date 2021/9/13
     */
    public function getSysDepartmentCompanyIds($refresh = false):array
    {
        if (!isset($this->sys_department_company_ids)) {
            $this->sys_department_company_ids =$this->getSettingEnvValueMap('sys_department_company_ids');
        }
        return $this->sys_department_company_ids;
    }

    /**
     * 国家处理
     * @Date: 2021-09-22 10:13
     * @author: peak pan
     * @return:
     **/
    public function getCountryList($code)
    {
        $res = EnvModel::getEnvByCode($code, []);
        if (!empty($res)) {
            $res = json_decode($res,true);
        }
        $country=[];
        foreach ($res as $key=>$data){
            $country[$key] = self::$t[$data];
        }
        $country_arr[$code]=$country;
        return $country_arr;
    }

    /**
     * @description 获取国家简称 统一从HCM获取
     * @return array
     **/
    public function getCountryListV2(): array
    {
        $ac = new ApiClient('hcm_rpc', '', 'getDictionaryByDictCode', static::$language);
        $ac->setParams([['dict_code' => 'abbreviation_country_region']]);
        $res  = $ac->execute();

        $countryList = [];
        $data = $res['result']['data'] ?? [];
        foreach ($data as $v) {
            $countryList[$v['value']] = $v['label'] ?? '';
        }
        return [
            'country_list' => $countryList
        ];
    }

    /**
     * @description 获取国家简称 统一从HCM获取
     * @return array
     **/
    public function getCountryListByWorking(): array
    {
        $ac = new ApiClient('hcm_rpc', '', 'getDictionaryByDictCode', static::$language);
        $ac->setParams([['dict_code' => 'working_country']]);
        $res  = $ac->execute();

        $countryList = [];
        $data = $res['result']['data'] ?? [];
        foreach ($data as $v) {
            $countryList[$v['value']] = $v['label'] ?? '';
        }
        return [
            'country_list' => $countryList
        ];
    }

    /**
     * 供应商管理-第三方支付方
     * @return mixed|string
     */
    public function getVendorThirdPayer()
    {
        $res = EnvModel::getEnvByCode('vendor_third_payer', []);
        return !empty($res) ? json_decode($res, true) : [];
    }

    /**
     * 获取setting_env中配置的values是ids类的枚举
     * @param string $code code
     * @return array
     */
    public function getSettingEnvValueIds(string $code)
    {
        $res = EnvModel::getEnvByCode($code, []);
        return  !empty($res) ? explode(',', $res) : [];
    }

    /**
     * 获取setting_env中配置的values是ids类的枚举
     * @param string $code code
     * @return array
     */
    public function getSettingEnvValueJson(string $code)
    {
        $res = EnvModel::getEnvByCode($code, '{}');
        return !empty($res) ? json_decode($res, true) : [];
    }

    /**
     * 获取 vat 配置 [sst == vat], 仅返回value列表
     *
     * @return mixed
     */
    public function getVatRateValueItem()
    {
        $string = EnvModel::getEnvByCode('common_vat_tax_rate', '');
        return !empty($string) ? explode(',', $string) : [];
    }

    /**
     * 获取 可抵扣vat税 配置 [sst == vat], 仅返回value列表
     *
     * @return mixed
     */
    public function getDeductibleRateValueItem()
    {
        return json_decode(EnvModel::getEnvByCode('deductible_vat_tax', '{}'), true);
    }

    /**
     * 获取 vat 配置 [sst == vat]
     *
     * @return mixed
     */
    public function getFormatVatRateConfig()
    {
        $string = EnvModel::getEnvByCode('common_vat_tax_rate', '');
        $array = !empty($string) ? explode(',', $string) : [];

        $result = [];
        foreach ($array as $v) {
            $result[] = [
                'label' => $v . '%',
                'value' => $v
            ];
        }

        return $result;
    }

    /**
     * 获取 wht 类别值与名称的映射关系
     * @param int $enabled_type 0: 返回所有的类别; 1: 返回仅启用的类别
     * @param  bool $is_flip true: cat_label => cat_value; false: cat_value => cat_label
     * @return mixed
     */
    public function getWhtRateCategoryMap(int $enabled_type = 1, bool $is_flip = false)
    {
        $string = EnvModel::getEnvByCode('common_wht_tax_rate', '{}');
        $array = json_decode($string,true);
        $result = [];
        foreach ($array as $cat){
            // 返回所有的
            if (!$enabled_type) {
                $result[$cat['value']] = $cat['label'];
                continue;
            }

            // 仅返回已启用的
            if ($cat['is_enabled'] == 1) {
                $result[$cat['value']] = $cat['label'];
            }
        }

        return $is_flip ? array_flip($result) : $result;
    }

    /**
     * 获取 wht 配置的value和label映射关系
     *
     * cat_value => [
     *      cat_value,
     *      cat_label,
     *      cat_is_enabled,
     *      rate_list = [
     *          rate_value = [
     *              rate_value,
     *              rate_label,
     *              rate_is_enabled
     *          ]
     *     ]
     * ]
     *
     * @param int $enabled_type 0: 返回所有的类别和税率; 1: 返回仅启用的类别和税率
     *
     * @return mixed
     */
    public function getWhtRateMap(int $enabled_type = 1)
    {
        $string = EnvModel::getEnvByCode('common_wht_tax_rate', '{}');
        $result = json_decode($string,true);

        $result = array_column($result, null, 'value');
        foreach ($result as $_k => $_val) {
            // 返回所有的
            if (!$enabled_type) {
                $result[$_k]['rate_list'] = array_column($_val['rate_list'], null, 'value');
                continue;
            }

            // 仅返回已启用的
            if ($_val['is_enabled'] == 1) {
                $_rate_list = [];
                foreach ($_val['rate_list'] as $_rate) {
                    if ($_rate['is_enabled'] == 1) {
                        $_rate_list[$_rate['value']] = $_rate;
                    }
                }

                $result[$_k]['rate_list'] = $_rate_list;
            } else {
                // 删除未启用的类别
                unset($result[$_k]);
            }
        }

        return $result;
    }

    /**
     * 获取 wht 所有税率配置的value和label映射关系
     * [
     *  value => label
     * ]
     *
     * @param int $enabled_type 0: 返回所有税率; 1: 返回仅启用税率
     *
     * @return mixed
     */
    public function getAllWhtRateMap(int $enabled_type = 1)
    {
        $rate = $this->getFormatWhtRateConfig();

        $result = [];
        foreach ($rate as $v) {
            // 该分类无税率: 略
            if (empty($v['rate_list'])) {
                continue;
            }

            foreach ($v['rate_list'] as $r) {
                // 返回所有
                if (!$enabled_type) {
                    $result[$r['value']] = $r;
                    continue;
                }

                // 返回仅启用的
                if ($r['is_enabled'] == 1) {
                    $result[$r['value']] = $r;
                }
            }
        }

        asort($result);
        return $result;
    }

    /**
     * 获取 wht 格式化配置
     * value => ''
     * label => ''
     *
     * 返回所有分类和税率(未启用和已启用)
     *
     * @return mixed
     */
    public function getFormatWhtRateConfig()
    {
        $string = EnvModel::getEnvByCode('common_wht_tax_rate', '{}');
        return json_decode($string,true);
    }

    /**
     * 获取 wht 所有税率 格式化配置[不含分类]
     * 取已启用分类下的已启用税率，去重
     * value => ''
     * label => ''
     *
     * @return mixed
     */
    public function getFormatAllWhtRateConfig()
    {
        $rate = $this->getFormatWhtRateConfig();

        $result = $_tmp = [];
        foreach ($rate as $v) {
            // 该分类无税率 或 该分类未启用: 略过
            if (empty($v['rate_list']) || $v['is_enabled'] != 1) {
                continue;
            }

            foreach ($v['rate_list'] as $r) {
                if ($r['is_enabled'] == 1 && empty($_tmp[$r['value']])) {
                    $_tmp[$r['value']] = 1;
                    $result[] = [
                        'value' => $r['value'],
                        'label' => $r['label'],
                    ];
                }
            }
        }

        return array_sort($result, 'value', SORT_ASC);
    }

    /**
     * 获取 可抵扣vat税率 格式化配置 [可抵扣sst同此]
     * value => ''
     * label => ''
     *
     * @return mixed
     */
    public function getFormatDeductibleVatTaxRateConfig()
    {
        $string = EnvModel::getEnvByCode('deductible_vat_tax', '{}');
        $array = json_decode($string,true);

        $result = [];
        foreach ($array as $v) {
            $result[] = [
                'label' => $v . '%',
                'value' => $v
            ];
        }

        return $result;
    }

    /**
     * 获取 vat/sst|wht|可抵扣税
     *
     * @return mixed
     */
    public function getTaxRateConfig()
    {
        return  [
            'wht_tax_rate' => $this->getFormatWhtRateConfig(),
            'vat_tax_rate' => $this->getFormatVatRateConfig(),
            'deductible_vat_tax_rate' => $this->getFormatDeductibleVatTaxRateConfig(),
            'all_wht_tax_rate' => $this->getFormatAllWhtRateConfig(),
        ];
    }

    // 获取合同所属公司
    public function getContractCompanyItem()
    {
        $currencyList = ContractCompanyModel::getCompanyByCode();

        $contract_company_map = [];
        foreach ($currencyList as $item) {
            $contract_company_map[$item['company_code']] = $item['company_name'];
        }

        return $contract_company_map;
    }

    // 获取vat/sst税名称
    public function getVatSStRateName()
    {
        return in_array(get_country_code(), [GlobalEnums::MY_COUNTRY_CODE]) ? GlobalEnums::SST_RATE_NAME : GlobalEnums::VAT_RATE_NAME;
    }

    /**
     * 获取 发票类型 格式化配置
     * public 里是公用的发票类型,目前维护在枚举文件中
     * reimbursement 是报销模块用的发票类型,目前维护在setting_env中
     * value => ''
     * label => ''
     *
     * @return mixed
     */
    public function getInvoiceTypeItem()
    {
        $result = [
            'public' => [],
            'reimbursement' => [],
        ];
        foreach (GlobalEnums::$financial_invoice_type_item as $code => $code_key) {
            $result['public'][] = [
                'label' => static::$t->_($code_key),
                'value' => (string) $code
            ];
        }
        //获取是否必填:增值税发票号、发票税务号、企业名称 的配置
        $required_tax_no = $this->getSettingEnvValueIds('sys_module_reimbursement_invoice_type_required');
        //获取报销配置
        $reimbursement_invoice_type_enums = $this->getSettingEnvValueMap('sys_module_reimbursement_invoice_type_enums');
        foreach ($reimbursement_invoice_type_enums as $k => $v) {
            $result['reimbursement'][] = [
                'label' => static::$t->_($v),
                'value' => (string) $k,
                'is_required_tax_no' => in_array($k, $required_tax_no) ? '1' : '0'
            ];
        }
        return $result;
    }

    /**
     * 获取财务模块费用相关的检索项配置
     *
     * 费用科目
     * 费用明细
     * 费用所属机构类型: 网点 / 总部
     * 费用所属公司
     *
     * @param int $cost_biz_type 费用所属业务类型 1-报销; 2-采购; 3-普通付款
     * @return mixed
     * @throws Exception
     */
    public function getCostSearchFields(int $cost_biz_type)
    {
        $lang = static::$language;
        $lang = substr($lang, 0, 2);
        $lang = $lang == 'zh' ? 'cn' : $lang;
        $lang = in_array($lang, static::$allow_language) ? $lang : static::$default_language;

        // 1.费用科目(预算科目)
        $builder = $this->modelsManager->createBuilder();
        $builder->columns("bo.id, bo.name_{$lang} AS name");
        $builder->from(['bo' => BudgetObject::class]);
        $builder->leftjoin(BudgetObjectOrder::class, 'bo.level_code = boo.level_code', 'boo');
        $builder->where('bo.is_delete = :bo_is_delete:', ['bo_is_delete' => GlobalEnums::IS_NO_DELETED]);
        $builder->andWhere('boo.is_delete = :boo_is_delete:', ['boo_is_delete' => GlobalEnums::IS_NO_DELETED]);
        $builder->andWhere('boo.type = :biz_type:', ['biz_type' => $cost_biz_type]);
        $data['budget_object'] = $builder->getQuery()->execute()->toArray();

        // 2.费用明细
        $data['budget_object_product'] = BudgetObjectProduct::find([
            'columns' => "id, name_{$lang} AS name",
            'conditions' => 'is_delete = :is_delete: AND type = :biz_type:',
            'bind' => ['is_delete' => GlobalEnums::IS_NO_DELETED, 'biz_type' => $cost_biz_type]
        ])->toArray();

        // 3.费用所属机构类型
        $data['cost_store_type'] = [
            ['id' => 1, 'name' => static::$t->_('global.branch')],
            ['id' => 2, 'name' => static::$t->_('payment_cost_store_type_1')]
        ];

        // 4.费用公司列表
        $data['cost_company'] =  (new PurchaseService())->getCooCostCompany();

        return $data;
    }

    /**
     * 判断工号是否在特权工号配置清单中
     * @param $staff_id
     * @return bool
     */
    public function isPrivilegeStaffId($staff_id)
    {
        if (empty($staff_id)) {
            return false;
        }

        $privilege_staff_id_item = $this->getSettingEnvValueIds('store_access_staff_id');
        return in_array($staff_id, $privilege_staff_id_item);
    }

    /**
     * 获取setting_env json
     * @param string $code code
     * @return array
     * */
    public function getSettingEnvValueMap(string $code)
    {
        $res = EnvModel::getEnvByCode($code, []);
        return !empty($res) ? json_decode($res, true) : [];
    }

    /**
     * 获取setting_env value 值
     *
     * @param string $code code
     * @param string $default_val
     * @return mixed
     */
    public function getSettingEnvValue(string $code, string $default_val = '')
    {
        return trim(EnvModel::getEnvByCode($code, $default_val));
    }
    /**
     * 获取其他合同的合同分类列表
     *
     * @param int $company_code 合同所属公司的枚举标识
     * @return array
     */
    public function getContractCategoryTreeList($company_code = 0)
    {
        static $contract_category_list = [];

        if (!empty($contract_category_list)) {
            return $contract_category_list;
        }

        $contract_category_list = ContractCategoryModel::find([
            'conditions' => 'company_code IN ({company_codes:array}) AND is_deleted = :is_deleted:',
            'bind' => [
                'company_codes' => empty($company_code) ? [ContractEnums::CONTRACT_COMPANY_DEFAULT] : [ContractEnums::CONTRACT_COMPANY_DEFAULT, $company_code],
                'is_deleted' => GlobalEnums::IS_NO_DELETED
            ],
            'columns' => 'translation_key AS label, id AS value, ancestry_id AS p, sort',
        ])->toArray();

        foreach ($contract_category_list as $k => &$v) {
            $v['label'] = static::$t->_($v['label']);
        }

        return array_sort(list_to_tree($contract_category_list, 'value', 'p', 'children'), 'sort', SORT_ASC);
    }

    /**
     * 获取其他合同的合同分类Map
     *
     * id => [
     *     id,
     *     ancestry_id,
     *     level,
     *     label
     * ]
     *
     * @return array
     */
    public function getContractCategorysMap()
    {
        static $contract_category_map = [];

        if (!empty($contract_category_map)) {
            return $contract_category_map;
        }

        $contract_category_map = ContractCategoryModel::find([
            'columns' => 'translation_key, id, ancestry_id, level',
        ])->toArray();
        $contract_category_map = array_column($contract_category_map, null, 'id');
        foreach ($contract_category_map as $k => &$v) {
            $v['label'] = static::$t->_($v['translation_key']);
        }

        return $contract_category_map;
    }

    /**
     * 银行列表
     * @param $module
     * @return array
     */
    public function getBankList($module)
    {
        if ($module == 'contract_store_renting') {
            BaseService::setLanguage(static::$default_language);
        }
        $list = SupplierBankModel::find([
            'condition' => 'is_del = :is_del:',
            'bind'      => ['is_del' => GlobalEnums::IS_NO_DELETED],
            'columns'   => ['id', 'bank_code'],
            'order'     => 'id ASC'
        ])->toArray();

        $data = [];

        $t = static::$t;
        foreach ($list as $value) {
            $data[] = [
                'value' => $value['id'],
                'label' => $t[$value['bank_code']],
            ];
        }

        return $data;
    }

    /**
     * 公司列表
     * @return array
     */
    public function getCompanyList()
    {
        // 默认含有"不限公司"的选项
        $data[] = [
            'value' => (string) SettingEnums::INVOICE_HEADER_RELATED_COMPANY_TYPE_ALL,
            'label' => static::$t->_('invoice_header_associated_company_all')
        ];

        $list = (new DepartmentRepository())->getCompanyList();
        foreach ($list as $value) {
            $data[] = [
                'value' => $value['id'],
                'label' => $value['name']
            ];
        }

        return $data;
    }

    /**
     * 发票抬头列表
     * @return array
     */
    public function getInvoiceHeaderList()
    {
        $data = [];
        $list = SettingInvoiceHeaderRepository::getInstance()->getList();
        foreach ($list as $value) {
            $related_company_ids = explode(',', $value['related_company_ids']);
            $data[] = [
                'value' => $value['id'],
                'label' => $value['header_name'],
                'is_related_all_company' => in_array(SettingEnums::INVOICE_HEADER_RELATED_COMPANY_TYPE_ALL, $related_company_ids),
                'related_company_ids' => $related_company_ids
            ];
        }

        return $data;
    }

    /**
     * 获取多个配置项
     * @param array $codes code组
     * @return array
     */
    public function getMultiEnvByCode(array $codes = [])
    {
        if (empty($codes) || !is_array($codes)) {
            return [];
        }

        $result = SettingEnvModel::find([
            'conditions' => 'code in ({codes:array})',
            'bind' => ['codes' => $codes],
            'columns' => ['val', 'code'],
        ])->toArray();
        return array_column($result, 'val', 'code');
    }

    /**
     * 获取员工雇佣类型-通过HCM接口
     * 缓存时间-24小时
     * @param bool $web_format 是否返回前端格式  true: [["value": "1", "label": "Permanent Employee"]]   false: ["1":"Permanent Employee"]
     * @return mixed
     */
    public function getHireTypeEnum($web_format = false)
    {
        $redis_key = RedisKey::STAFF_HIRE_TYPE_ENUM_CACHE . ':' . static::$language;
        $hire_type_enum = $this->getCache($redis_key);
        if ($hire_type_enum) {
            //有缓存,使用缓存
            $hire_type_enum = json_decode($hire_type_enum, true);
        } else {
            //没有缓存,调用接口查询,并缓存一天
            $api_client = new ApiClient('hcm_rpc', '', 'getHireTypeList', static::$language);
            $api_client->setParams(['']);
            $result = $api_client->execute();
            if (!empty($result['result']['data'])) {
                $hire_type_enum = $result['result']['data'];
                //缓存枚举时间一天
                $this->setCache($redis_key, json_encode($hire_type_enum, JSON_UNESCAPED_UNICODE), 60*60*24);
            }
        }
        if (!$hire_type_enum) {
            return [];
        }
        //处理成前端格式
        if (!$web_format) {
            $hire_type_enum = array_column($hire_type_enum, 'label', 'value');
        }
        return $hire_type_enum;
    }

    /**
     * 获取 发票类型 格式化配置
     * public 里是公用的发票类型,目前维护在枚举文件中
     * reimbursement 是报销模块用的发票类型,目前维护在setting_env中
     * value => ''
     * label => ''
     *
     * @return mixed
     */
    public function getPaymentMethodItem()
    {
        $result = [];
        foreach (ReimbursementEnums::$payment_method_item as $code => $lang_key) {
            $result[] = [
                'label' => static::$t->_($lang_key),
                'value' => (string) $code
            ];
        }

        return $result;
    }

    public function getPayrollCompanyInfo(){
        $data = StaffPayrollCompanyInfoModel::find([
            'columns' => 'company_id,company_short_name'
        ])->toArray();
        $data = array_column($data, 'company_short_name', 'company_id');
        return $data;
    }

    /**
     * 根据公司id获取某公司信息配置（组织架构树id串多个,分割、公司全名、公司注册号、公司logo、公司水印）
     * @param integer $company_id 公司id
     * @return array|mixed
     */
    public function getCompanyInfo($company_id)
    {
        $company_info = [];
        $company_list = $this->getSettingEnvValueMap('company_info');
        foreach ($company_list as $item) {
            if (empty($item['ids'])) {
                continue;
            }
            if (in_array($company_id, explode(',', $item['ids']))) {
                $company_info = $item;
                break;
            }
        }
        return $company_info;
    }

    /**
     * 获取系统币种配置
     * @return array
     */
    public function getSysCurrencyList()
    {
        $data = [];
        $list = CurrencyModel::getAllCurrency();
        foreach ($list as $value) {
            $data[] = [
                'value' => $value['id'],
                'label' => $value['currency_symbol'],
            ];
        }

        return $data;
    }

    /**
     * 获取油费额度配置枚举
     */
    public function getReimbursementFuelQuotaEnums()
    {
        $data = [];
        foreach (ReimbursementEnums::$expenses_type_item as $k => $v) {
            $data['expenses_type_item'][] = [
                'value' => (string)$k,
                'label' => static::$t->_($v),
            ];
        }

        foreach (ReimbursementEnums::$oil_type_item as $k => $v) {
            $data['oil_type_item'][] = [
                'value' => (string)$k,
                'label' => static::$t->_($v),
            ];
        }

        foreach (ReimbursementEnums::$position_type_item as $k => $v) {
            $data['position_type_item'][] = [
                'value' => (string)$k,
                'label' => static::$t->_($v),
            ];
        }

        return $data;
    }

    /**
     * 境内住宿区域配置枚举
     */
    public function getReimbursementDomesticAccommodationAreaEnums()
    {
        $data = [];
        foreach (ReimbursementEnums::$domestic_accommodation_area_type_item as $k => $v) {
            $data['area_type_item'][] = [
                'value' => (string)$k,
                'label' => static::$t->_($v),
            ];
        }

        return $data;
    }

    /**
     * 境内住宿额度配置枚举
     */
    public function getReimbursementDomesticAccommodationQuotaEnums()
    {
        $data = [];
        foreach (ReimbursementEnums::$domestic_accommodation_quota_type_item as $k => $v) {
            $data['area_type_item'][] = [
                'value' => (string)$k,
                'label' => static::$t->_($v),
            ];
        }

        foreach (ReimbursementEnums::$position_type_item as $k => $v) {
            $data['position_type_item'][] = [
                'value' => (string)$k,
                'label' => static::$t->_($v),
            ];
        }

        foreach (Enums::$job_level as $v) {
            $data['job_level_item'][] = [
                'value' => (string)$v['id'],
                'label' => $v['name'],
            ];
        }

        return $data;
    }

    /**
     * 境外住宿区域配置枚举
     */
    public function getReimbursementOverseasAccommodationAreaEnums()
    {
        $data = [];
        foreach (ReimbursementEnums::$overseas_accommodation_type_item as $k => $v) {
            $data['accommodation_type_item'][] = [
                'value' => (string)$k,
                'label' => $v,
            ];
        }

        $country_list = $this->getCountryListByWorking()['country_list'] ?? [];
        foreach ($country_list as $k => $v) {
            $data['country_item'][] = [
                'value' => (string)$k,
                'label' => $v,
            ];
        }

        return $data;
    }


    /**
     * 境外住宿额度配置相关枚举
     */
    public function getReimbursementOverseasAccommodationQuotaEnums()
    {
        $data = [];
        foreach (ReimbursementEnums::$overseas_accommodation_type_item as $k => $v) {
            $data['accommodation_type_item'][] = [
                'value' => (string)$k,
                'label' => $v,
            ];
        }

        foreach (ReimbursementEnums::$position_type_item as $k => $v) {
            $data['position_type_item'][] = [
                'value' => (string)$k,
                'label' => static::$t->_($v),
            ];
        }

        return $data;
    }

    /**
     * 租车费额度配置相关枚举
     */
    public function getReimbursementCarRentalQuotaEnums()
    {
        $data = [];
        foreach (ReimbursementEnums::$position_type_item as $k => $v) {
            $data['position_type_item'][] = [
                'value' => (string)$k,
                'label' => static::$t->_($v),
            ];
        }

        foreach (Enums::$job_level as $v) {
            $data['job_level_item'][] = [
                'value' => (string)$v['id'],
                'label' => $v['name'],
            ];
        }

        foreach (ReimbursementEnums::$is_required_email_approval_no_item as $k => $v) {
            $data['is_required_email_approval_item'][] = [
                'value' => (string)$k,
                'label' => static::$t->_($v),
            ];
        }

        return $data;
    }

    /**
     * 获取报销相关枚举配置
     */
    public function getReimbursementEnums()
    {
        $data = [];

        // 特殊报销费用项
        $data['special_expense_type'] = $this->getSettingEnvValueMap('reimbursement_special_expense_type');

        // 特殊报销费用明细
        $data['special_expense_details_type'] = $this->getSettingEnvValueMap('reimbursement_special_expense_details_type');

        // 报销时间限制规则
        $trip_time_limit              = ReimbursementTripTimeLimitRepository::getInstance()->getOneById(1);
        $data['trip_time_limit_rule'] = [
            'is_limit'                  => $trip_time_limit->is_limit ?? '0',
            'next_month_specified_date' => $trip_time_limit->next_month_specified_date ?? '1',
        ];

        return $data;
    }
    
}
