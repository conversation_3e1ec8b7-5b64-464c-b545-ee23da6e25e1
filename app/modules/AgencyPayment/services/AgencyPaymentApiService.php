<?php
namespace App\Modules\AgencyPayment\Services;

use App\Library\Enums;
use App\Library\Enums\AgencyPaymentEnums;
use App\Library\Enums\ByWorkflowEnums;
use App\Library\Enums\GlobalEnums;
use App\Library\Enums\PayEnums;
use App\Library\Enums\StaffInfoEnums;
use App\Library\ErrCode;
use App\Library\Exception\BusinessException;
use App\Library\Validation\Validation;
use App\Library\Validation\ValidationException;
use App\Models\oa\AgencyPaymentDetailModel;
use App\Models\oa\AgencyPaymentDetailSummaryModel;
use App\Models\oa\AgencyPaymentModel;
use App\Models\oa\ByWorkflowAuditLogModel;
use App\Modules\Common\Services\EnumsService;
use App\Modules\Pay\Models\Payment;
use App\Modules\Third\Services\ByWorkflowService;
use App\Repository\DepartmentRepository;
use App\Repository\HrStaffRepository;
use App\Repository\StoreRepository;
use App\Util\RedisKey;
use Exception;

class AgencyPaymentApiService extends BaseService
{
    private static $instance;

    private function __construct()
    {
    }

    private function __clone()
    {
    }

    public static function getInstance()
    {
        if (!isset(self::$instance) || !(self::$instance instanceof self)) {
            self::$instance = new self;
        }
        return self::$instance;
    }

    //代付结果查询参数
    public static $get_payment_result_validate = [
        'no' => 'Required|StrLenGeLe:1,100',
        'out_no' => 'Required|StrLenGeLe:1,50',
    ];

    /**
     * 数据集成到代理支付模块
     * @param array $params 请求参数组
     * @return array
     */
    public function add($params)
    {
        $code = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $data = [];
        $this->logger->info('oa_external_api_agency_payment_add ' . json_encode($params, JSON_UNESCAPED_UNICODE));
        //开启事务
        $db = $this->getDI()->get('db_oa');
        $db->begin();
        try {
            if (empty($params['lang']) || !in_array($params['lang'], self::$allow_language)) {
                throw new ValidationException(static::$t->_('agency_payment_add_api.lang'), ErrCode::$VALIDATE_ERROR);
            }
            self::setLanguage($params['lang']);
            Validation::validate($params, [
                'task_batch_no' => 'Required|StrLenGeLe:1,50|>>>:' . static::$t->_('agency_payment_add_api.task_batch_no'),//任务批次号
                'payment_batch_no' => 'Required|StrLenGeLe:1,50|>>>:' . static::$t->_('agency_payment_add_api.payment_batch_no'), //支付批次号
                'mark' => 'StrLenGeLe:0,500|>>>:' . static::$t->_('agency_payment_add_api.mark'),//备注
                'cost_type' => 'Required|IntIn:' . implode(',', array_keys(self::getCostType())), //费用类型
                'currency' => 'Required|IntIn:' . GlobalEnums::VALIDATE_CURRENCY_PARAMS, //币种
                'is_final' => 'Required|IntIn:0,1',//是否终态0否1是
                'notify_url' => 'StrLenGeLe:0,500',//异步回调url
                'attachment_url' => 'StrLenGeLe:0,500',//附件
                'detail' => 'Required|ArrLenGeLe:1,500|>>>:' . static::$t->_('agency_payment_add_api.detail'),
                'detail[*]' => 'Obj',
                'detail[*].out_no' => 'Required|StrLenGeLe:1,50|>>>:' . static::$t->_('agency_payment_add_api.detail_out_no'),//外部系统编号
                'detail[*].payee_staff_id' => 'StrLenGeLe:0,50|>>>:' . static::$t->_('agency_payment_add_api.detail_payee_staff_id'),//收款人工号
                'detail[*].bank_account' => 'Required|StrLenGeLe:1,50|>>>:' . static::$t->_('agency_payment_add_api.detail_bank_account'),//收款人账号
                'detail[*].bank_account_name' => 'Required|StrLenGeLe:1,100|>>>:' . static::$t->_('agency_payment_add_api.detail_bank_account_name'),//收款人姓名
                'detail[*].bank_name' => 'Required|StrLenGeLe:1,50|>>>:' . static::$t->_('agency_payment_add_api.detail_bank_name'),//收款人开户银行
                'detail[*].amount_no_tax' => 'Required|FloatGtLt:0,************.99',//不含税金额
                'detail[*].vat_rate' => 'Required|FloatGe:0',//vat税率
                'detail[*].amount_total_vat' => 'Required|FloatGeLt:0,************.99',//VAT税额
                'detail[*].wht_category' => 'Required|StrLenGeLe:1,50',//WHT类别
                'detail[*].wht_rate' => 'Required|FloatGe:0',//WHT税率
                'detail[*].amount_total_wht' => 'Required|FloatGeLt:0,************.99',//WHT税额
                'detail[*].amount_total_actually' => 'Required|FloatGtLt:0,************.99',//实付金额
                'detail[*].cost_start_date' => 'Required|Date',//费用开始日期
                'detail[*].cost_end_date' => 'Required|Date',//费用结束日期
                'detail[*].due_date' => 'Required|Date',//应付日期
                'detail[*].remark' => 'StrLenGeLe:0,500|>>>:' . static::$t->_('agency_payment_add_api.detail_remark'),//行备注
                'detail[*].created_at' => 'Required|Datetime',//创建时间
                'detail[*].payee_id_no' => 'StrLenGeLe:0,100',//收款人证件号码
                'detail[*].payee_mobile' => 'StrLenGeLe:0,100',//收款人联系电话
                'detail[*].payee_address' => 'StrLenGeLe:0,500',//收款人居住地址
            ]);

            //提交过来的明细行外部系统编号有重复
            $params_out_nos = array_values(array_unique(array_column($params['detail'], 'out_no')));
            if (count($params_out_nos) != count($params['detail'])) {
                throw new ValidationException(static::$t->_('agency_payment_add_api.detail_out_no_repeat'), ErrCode::$VALIDATE_ERROR);
            }

            //获取费用类型配置
            $agency_payment_cost_type_config = EnumsService::getInstance()->getSettingEnvValueMap('agency_payment_cost_type_config');
            $one_cost_type_config = $agency_payment_cost_type_config[$params['cost_type']];
            if (!$one_cost_type_config || empty($one_cost_type_config['apply_id']) || empty($one_cost_type_config['cost_company_id']) || empty($one_cost_type_config['cost_sys_department_id']) || empty($one_cost_type_config['cost_store_type'])) {
                throw new ValidationException(static::$t->_('agency_payment_add_api.cost_type_config_invalid'), ErrCode::$VALIDATE_ERROR);
            }

            //获取申请人信息
            $user = (new HrStaffRepository())->getStaffById($one_cost_type_config['apply_id']);
            //员工不存在 || 非在职
            if (!$user || $user['state'] != StaffInfoEnums::STAFF_STATE_IN) {
                throw new ValidationException(static::$t->_('crowd_sourcing_apply_user_null'), ErrCode::$VALIDATE_ERROR);
            }

            $now = date('Y-m-d H:i:s');
            //支付明细行
            $detail_data = [];
            $wht_category_item = EnumsService::getInstance()->getWhtRateCategoryMap(1, true);
            $vat_config = EnumsService::getInstance()->getVatRateValueItem();
            $wht_config = EnumsService::getInstance()->getWhtRateMap();
            $vat_sst_name = EnumsService::getInstance()->getVatSStRateName();
            $agency_payment_detail_list = AgencyPaymentDetailModel::find([
                'columns' => 'out_no',
                'conditions' => 'out_no in ({out_no:array}) and pay_status in ({pay_status:array})',
                'bind' => ['out_no' => $params_out_nos, 'pay_status' => [AgencyPaymentEnums::AGENCY_PAYMENT_PAY_STATUS_PENDING, AgencyPaymentEnums::AGENCY_PAYMENT_PAY_STATUS_PAY]]
            ])->toArray();
            $db_out_nos = array_unique(array_column($agency_payment_detail_list, 'out_no'));
            //明细行的费用部门和费用网点是否必须传输
            $detail_is_required = $this->checkDetailIsRequired($one_cost_type_config['cost_company_id'], $params['cost_type']);

            //费用部门
            $cost_department_name = array_column($params['detail'], 'cost_department_name');
            $department_list = (new DepartmentRepository())->getDepartmentByNames($cost_department_name);

            //费用网点
            $cost_store_name = array_column($params['detail'], 'cost_store_name');
            $store_list = (new StoreRepository())->getStoreByNames($cost_store_name);

            foreach ($params['detail'] as $item) {
                //当接口传输的费用类型以及费用类型配置的默认提单公司，在系统配置的代理支付同步sap的公司ID和费用类型，那么该批次的明细行的费用部门和费用网点必须传输
                if ($detail_is_required && (empty($item['cost_department_name']) || empty($item['cost_store_name']))) {
                    throw new ValidationException(static::$t->_('agency_payment_add_api.detail_cost_department_store_invalid'), ErrCode::$VALIDATE_ERROR);
                }

                //费用部门名称-必填时费用部门必须有值、填写了费用部门需是（非删除的部门）
                $one_department_info = [];
                if (isset($item['cost_department_name'])) {
                    $one_department_info = $department_list[$item['cost_department_name']] ?? [];
                    if ($item['cost_department_name'] && empty($one_department_info)) {
                        throw new ValidationException(static::$t->_('agency_payment_add_api.detail_cost_department_invalid'), ErrCode::$VALIDATE_ERROR);
                    }
                }

                //费用网点名称-必填时费用网点必须有值、填写了费用网点（激活的网点）或 Head office
                $one_store_info = [];
                if (isset($item['cost_store_name'])) {
                    if (strtolower($item['cost_store_name']) == strtolower(GlobalEnums::STORE_HEADER_OFFICE_NAME)) {
                        $one_store_info = ['id' => GlobalEnums::STORE_HEADER_OFFICE_ID, 'name' => $item['cost_store_name'], 'sap_pc_code' => $one_department_info ? $one_department_info['sap_cost_center'] : ''];
                    } else {
                        $one_store_info = $store_list[$item['cost_store_name']] ?? [];
                    }
                    if ($item['cost_store_name'] && empty($one_store_info)) {
                        throw new ValidationException(static::$t->_('agency_payment_add_api.detail_cost_store_invalid'), ErrCode::$VALIDATE_ERROR);
                    }

                    //必填时-找不到成本中心，请联系财务维护网点上的成本中心
                    if ($detail_is_required && empty($one_store_info['sap_pc_code'])) {
                        throw new ValidationException(static::$t->_('agency_payment_detail_import_error_018'), ErrCode::$VALIDATE_ERROR);
                    }
                }

                // 提取WHT类别
                if (!array_key_exists($item['wht_category'], $wht_category_item)) {
                    // WHT类别错误，请填写：PND3或PND53或/
                    throw new ValidationException(static::$t->_('payment_upload_error_007'), ErrCode::$VALIDATE_ERROR);
                }
                if (!in_array($item['vat_rate'], $vat_config)) {
                    // VAT税率错误，类别为PND3，请填写：2%或3%或5%；类别为PND53，请填写：1%或2%或3%或5%；类别为/，请填写：0%
                    throw new ValidationException(static::$t->_('vat_sst_rate_error_hint', ['VAT_SST' => $vat_sst_name]), ErrCode::$VALIDATE_ERROR);
                }
                if (empty($wht_config[$wht_category_item[$item['wht_category']]]['rate_list'][$item['wht_rate']])) {
                    // WHT税率错误，类别为PND3，请填写：2%或3%或5%；类别为PND53，请填写：1%或2%或3%或5%；类别为/，请填写：0%
                    throw new ValidationException(static::$t->_('payment_upload_error_008'), ErrCode::$VALIDATE_ERROR);
                }
                //外部系统编号待支付、已支付单据已存在明细行里
                if (in_array($item['out_no'], $db_out_nos)) {
                    throw new ValidationException(static::$t->_('agency_payment_add_api.detail_out_no_existed', ['out_no' => $item['out_no']]), ErrCode::$VALIDATE_ERROR);
                }

                //应付金额 = 不含税金额 + vat金额
                //费用一级部门
                $first_level_department_info = $one_department_info ? (new DepartmentRepository())->getFirstLevelDepartmentByAncestryV3($one_department_info['ancestry_v3']) : [];
                $detail_data[] = [
                    'out_no' => $item['out_no'],
                    'payee_staff_id' => $item['payee_staff_id'] ?? '',
                    'bank_account_name' => $item['bank_account_name'],
                    'bank_name' => $item['bank_name'],
                    'bank_account' => $item['bank_account'],
                    'vat_rate' => $item['vat_rate'],
                    'amount_total_vat' => $item['amount_total_vat'],
                    'wht_category' => $wht_category_item[$item['wht_category']],
                    'wht_rate' => $item['wht_rate'],
                    'amount_total_wht' => $item['amount_total_wht'],
                    'amount_no_tax' => $item['amount_no_tax'],
                    'payable_amount' => bcadd($item['amount_no_tax'], $item['amount_total_vat'], 2),
                    'amount_total_actually' => $item['amount_total_actually'],
                    'cost_start_date' => $item['cost_start_date'],
                    'cost_end_date' => $item['cost_end_date'],
                    'due_date' => $item['due_date'],
                    'remark' => $item['remark'] ?? '',
                    'cost_type' => $params['cost_type'],
                    'cost_department_id' => $one_department_info ? $one_department_info['id'] : 0,//费用部门ID
                    'cost_department_name' => $item['cost_department_name'] ?? '',//费用部门名称
                    'cost_sys_department_id' => $first_level_department_info ? $first_level_department_info['id'] : 0,//费用一级部门id
                    'cost_store_id' => $one_store_info ? $one_store_info['id'] : '',//费用网点ID
                    'cost_store_name' => $item['cost_store_name'] ?? '',//费用网点名称
                    'cost_center_code' => $one_store_info ? $one_store_info['sap_pc_code'] : '',//费用成本中心
                    'payee_id_no' => $item['payee_id_no'] ?? '',//收款人证件号码
                    'payee_mobile' => $item['payee_mobile'] ?? '',//收款人联系电话
                    'payee_address' => $item['payee_address'] ?? '',//收款人居住地址
                    'created_at' => $item['created_at'],
                    'updated_at' => $now
                ];
            }

            //检测任务批次号在系统中是否已存在，不存在创建
            $agency_payment_info = AgencyPaymentModel::findFirst([
                'conditions' => 'task_batch_no = :task_batch_no:',
                'bind' => ['task_batch_no' => $params['task_batch_no']]
            ]);
            //已存在非待提交
            if ($agency_payment_info && $agency_payment_info->status != AgencyPaymentEnums::AGENCY_PAYMENT_STATUS_WAIT_SUBMIT) {
                throw new ValidationException(static::$t->_('agency_payment_add_api_invalid'), ErrCode::$VALIDATE_ERROR);
            }
            //任务批次号不存在，则创建
            if (!$agency_payment_info) {
                //获取部门、公司信息
                $department_list = (new DepartmentRepository())->getDepartmentByIds([$user['node_department_id'], $one_cost_type_config['cost_sys_department_id'], $one_cost_type_config['cost_company_id']], 2);

                $agency_payment_info = new AgencyPaymentModel();
                $agency_payment_info->workflow_no = '';
                $agency_payment_info->batch_no = static::genSerialNo('OAIC', RedisKey::AGENCY_PAYMENT_CREATE_COUNTER, 4, date('ymd'));
                $agency_payment_info->apply_date = date('Y-m-d');
                $agency_payment_info->apply_id = $one_cost_type_config['apply_id'];
                $agency_payment_info->apply_name = $this->getNameAndNickName($user['name'] ?? '', $user['nick_name'] ?? '');
                $agency_payment_info->apply_department_id = $user['node_department_id'];
                $agency_payment_info->apply_department_name = $department_list[$user['node_department_id']]['name'] ?? '';
                $agency_payment_info->cost_company_id = $one_cost_type_config['cost_company_id'];
                $agency_payment_info->cost_company_name = $department_list[$one_cost_type_config['cost_company_id']]['name'] ?? '';
                $agency_payment_info->cost_sys_department_id = $one_cost_type_config['cost_sys_department_id'];
                $agency_payment_info->cost_department_name = $department_list[$one_cost_type_config['cost_sys_department_id']]['name'] ?? '';
                $agency_payment_info->cost_store_type = $one_cost_type_config['cost_store_type'];
                $agency_payment_info->pay_method = GlobalEnums::PAYMENT_METHOD_BANK_TRANSFER;
                $agency_payment_info->currency = $params['currency'];
                $agency_payment_info->mark = $params['mark'] ?? '';
                $agency_payment_info->cost_type = $params['cost_type'];
                $agency_payment_info->status = AgencyPaymentEnums::AGENCY_PAYMENT_STATUS_WAIT_SUBMIT;
                $agency_payment_info->pay_status = AgencyPaymentEnums::AGENCY_PAYMENT_PAY_STATUS_PENDING;
                $agency_payment_info->is_pay_module = PayEnums::BIZ_DATA_IS_PAY_MODULE_NO;
                $agency_payment_info->is_push_pay_module = AgencyPaymentEnums::AGENCY_PAYMENT_IS_PUSH_PAY_MODULE_DEFAULT;
                $agency_payment_info->source_type = AgencyPaymentEnums::SOURCE_TYPE_API;
                $agency_payment_info->task_batch_no = $params['task_batch_no'];
                $agency_payment_info->payment_batch_no = $params['payment_batch_no'];
                $agency_payment_info->reason = '';
                $agency_payment_info->pay_staff_id = 0;
                $agency_payment_info->sync_sap = 0;
                $agency_payment_info->sap_uuid = '';
                $agency_payment_info->sap_note = '';
                $agency_payment_info->notify_url = $params['notify_url'] ?? '';//异步回调url
                $agency_payment_info->attachment_url = $params['attachment_url'] ?? '';//附件
                $agency_payment_info->created_at = $now;
                $bool = $agency_payment_info->save();
                if ($bool === false) {
                    throw new BusinessException('数据集成到代理支付模块 - 支付批次记录失败= ' . json_encode($params, JSON_UNESCAPED_UNICODE) . '; 可能存在的原因：' . get_data_object_error_msg($agency_payment_info), ErrCode::$BUSINESS_ERROR);
                }
            }

            //支付明细入库
            foreach ($detail_data as &$detail) {
                $detail['agency_payment_id'] = $agency_payment_info->id;
                $detail['no'] = str_replace('_', '', static::genSerialNo($agency_payment_info->batch_no, RedisKey::AGENCY_PAYMENT_CREATE_DETAIL_COUNTER,  5, '_'));
                $detail['payment_batch_no'] = $params['payment_batch_no'];

                $data[] = [
                    'out_no'                => $detail['out_no'],
                    'no'                    => $detail['no'],
                    'amount_total_actually' => $detail['amount_total_actually'],
                ];
            }
            $agency_payment_detail_model = new AgencyPaymentDetailModel();
            $bool = $agency_payment_detail_model->batch_insert($detail_data);
            if ($bool === false) {
                throw new BusinessException('数据集成到代理支付模块 - 支付明细记录失败 = ' . json_encode($detail_data, JSON_UNESCAPED_UNICODE), ErrCode::$BUSINESS_ERROR);
            }

            //计算批次下所有汇总金额信息
            $amount_data = AgencyPaymentService::getInstance()->calculateTotalAmount($agency_payment_info->id);
            //批次终态操作需要创建审批流
            $agency_payment_detail_summary_data = [];
            if ($params['is_final'] == AgencyPaymentEnums::IS_FINAL_YES && empty($agency_payment_info->workflow_no)) {
                $by_add_result = (new ByWorkflowService())->add([
                    'submitter_id' => $agency_payment_info->apply_id,
                    'summary_data' => [],
                    'biz_type' => ByWorkflowEnums::BY_BIZ_TYPE_AGENCY_PAYMENT,
                    'audit_params' => [
                        'company_id' => $agency_payment_info->cost_company_id,
                        'department_id' => $agency_payment_info->cost_sys_department_id,
                        'source_type' => 1,//接口传输1是，2否
                        'cost_type' => $agency_payment_info->cost_type,
                        'payable_amount' => $amount_data['payable_amount'],//应付总金额（含VAT含WHT）
                        'amount_total_actually' => $amount_data['amount_total_actually'],//实付总金额（含VAT不含WHT）
                    ],
                    'form_params' => [
                        'department_id' => $agency_payment_info->cost_sys_department_id,
                    ]
                ]);
                $agency_payment_info->workflow_no = $by_add_result['serial_no'];
                $agency_payment_info->status = Enums::WF_STATE_PENDING;

                //代理支付明细表-付款汇总行
                $agency_payment_detail = $agency_payment_info->getDetails()->toArray();
                foreach ($agency_payment_detail as $key => $item) {
                    $agency_payment_detail_summary_key = $item['cost_department_id'] . '_' . $item['cost_store_id'] . '_' . $item['cost_center_code'];
                    if (isset($agency_payment_detail_summary_data[$agency_payment_detail_summary_key])) {
                        $agency_payment_detail_summary_data[$agency_payment_detail_summary_key]['amount_total_vat'] = bcadd($agency_payment_detail_summary_data[$agency_payment_detail_summary_key]['amount_total_vat'], $item['amount_total_vat'], 2);
                        $agency_payment_detail_summary_data[$agency_payment_detail_summary_key]['amount_total_wht'] = bcadd($agency_payment_detail_summary_data[$agency_payment_detail_summary_key]['amount_total_wht'], $item['amount_total_wht'], 2);
                        $agency_payment_detail_summary_data[$agency_payment_detail_summary_key]['amount_total_no_tax'] = bcadd($agency_payment_detail_summary_data[$agency_payment_detail_summary_key]['amount_total_no_tax'], $item['amount_no_tax'], 2);
                        $agency_payment_detail_summary_data[$agency_payment_detail_summary_key]['payable_amount'] = bcadd($agency_payment_detail_summary_data[$agency_payment_detail_summary_key]['payable_amount'], $item['payable_amount'], 2);
                        $agency_payment_detail_summary_data[$agency_payment_detail_summary_key]['amount_total_actually'] = bcadd($agency_payment_detail_summary_data[$agency_payment_detail_summary_key]['amount_total_actually'], $item['amount_total_actually'], 2);
                        $agency_payment_detail_summary_data[$agency_payment_detail_summary_key]['num'] += 1;
                    } else {
                        $agency_payment_detail_summary_data[$agency_payment_detail_summary_key] = [
                            'agency_payment_id' => $agency_payment_info->id,
                            'amount_total_vat' => $item['amount_total_vat'],//vat金额总计
                            'amount_total_wht' => $item['amount_total_wht'],//wht金额总计
                            'amount_total_no_tax' => $item['amount_no_tax'],//不含税总金额
                            'payable_amount' => $item['payable_amount'],//应付总金额
                            'amount_total_actually' => $item['amount_total_actually'],//实付总金额
                            'num' => 1,//数量
                            'cost_department_id' => $item['cost_department_id'],//费用部门ID
                            'cost_department_name' => $item['cost_department_name'],//费用部门名称
                            'cost_sys_department_id' => $item['cost_sys_department_id'],//费用一级部门ID
                            'cost_store_id' => $item['cost_store_id'],//费用网点ID
                            'cost_store_name' => $item['cost_store_name'],//费用网点名称
                            'cost_center_code' => $item['cost_center_code'],//费用成本中心
                            'created_at' => $now
                        ];
                    }
                }
            }

            //更新批次上金额汇总信息
            $agency_payment_info->amount_total_no_tax = $amount_data['amount_total_no_tax'];//不含税总金额
            $agency_payment_info->amount_total_vat = $amount_data['amount_total_vat'];//VAT总金额
            $agency_payment_info->amount_total_wht = $amount_data['amount_total_wht'];//WHT总金额
            $agency_payment_info->amount_total_actually = $amount_data['amount_total_actually'];//实付总金额
            $agency_payment_info->payable_amount = $amount_data['payable_amount'];//应付总金额
            $agency_payment_info->updated_at = $now;
            $bool = $agency_payment_info->save();
            if ($bool === false) {
                throw new BusinessException('数据集成到代理支付模块 - 支付批次-更新汇总金额等信息失败= ' . json_encode($amount_data, JSON_UNESCAPED_UNICODE) . '; 可能存在的原因：' . get_data_object_error_msg($agency_payment_info), ErrCode::$BUSINESS_ERROR);
            }

            //代理支付明细表-付款汇总行入库
            if (!empty($agency_payment_detail_summary_data)) {
                $agency_payment_detail_summary = new AgencyPaymentDetailSummaryModel();
                $bool = $agency_payment_detail_summary->batch_insert($agency_payment_detail_summary_data);
                if ($bool === false) {
                    throw new BusinessException('数据集成到代理支付模块 - 支付明细-付款汇总行记录失败 = ' . json_encode($agency_payment_detail_summary_data, JSON_UNESCAPED_UNICODE) . '; 可能存在的原因：' . get_data_object_error_msg($agency_payment_detail_summary), ErrCode::$BUSINESS_ERROR);
                }
            }

            //事物提交
            $db->commit();
        } catch (ValidationException $e) {
            $code    = $e->getCode();
            $message = $e->getMessage();
        } catch (BusinessException $e) {
            $code = $e->getCode();
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        } catch (Exception $e) {
            $code = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        }
        if (!empty($real_message)) {
            $this->logger->error('oa_external_api_agency_payment_add failed:' . $real_message);
        }
        if (!empty($message)) {
            //事务回滚
            $db->rollback();
        }
        return [
            'code' => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data' => $data,
        ];
    }

    /**
     * by自动通过审批回调
     * @param array $params 请求参数
     * @return array
     */
    public function pass($params)
    {
        $code = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $db = $this->getDI()->get('db_oa');
        $db->begin();
        try {
            $agency_payment_info = AgencyPaymentModel::findFirst([
                'conditions' => 'workflow_no = :workflow_no:',
                'bind' => ['workflow_no' => $params['workflow_no']]
            ]);
            if (empty($agency_payment_info)) {
                throw new ValidationException(static::$t->_('agency_payment_not_exists'), ErrCode::$VALIDATE_ERROR);
            }
            //由于by哪里异步通知无法区分是系统自动审批还是人为审批，一律回调，非待审核默认为异步同步成功
            if ($agency_payment_info->status == Enums::WF_STATE_PENDING) {
                //代理支付审核通过
                $now_time = date('Y-m-d H:i:s');
                $pass_params = [
                    'status' => Enums::WF_STATE_APPROVED,
                    'is_pay_module' => PayEnums::BIZ_DATA_IS_PAY_MODULE_YES,
                    'is_push_pay_module' => AgencyPaymentEnums::AGENCY_PAYMENT_IS_PUSH_PAY_MODULE_DEFAULT,
                    'approved_at' => show_time_zone($params['approval_time']),
                    'reason' => $params['reason'] ?? '',
                    'updated_at' => $now_time
                ];
                $bool = $agency_payment_info->i_update($pass_params);
                if ($bool === false) {
                    throw new BusinessException('代理支付-通过失败: 待处理数据: '. json_encode($pass_params, JSON_UNESCAPED_UNICODE) . '; 可能存在的问题: ' . get_data_object_error_msg($agency_payment_info), ErrCode::$BUSINESS_ERROR);
                }
                //记录审批操作记录
                $by_workflow_audit_log = new ByWorkflowAuditLogModel();
                $pass_log = [
                    'biz_type' => Enums::WF_AGENCY_PAYMENT_BIZ_TYPE,
                    'biz_value' => $agency_payment_info->id,
                    'staff_id' => $agency_payment_info->apply_id,
                    'approval_id' => $params['approval_id'],
                    'status' => Enums::WF_STATE_APPROVED,
                    'approval_time' => $now_time,
                    'created_at' => $now_time,
                    'updated_at' => $now_time
                ];
                $bool = $by_workflow_audit_log->i_create($pass_log);
                if ($bool === false) {
                    throw new BusinessException('代理支付-通过-记录by审批日志失败: 待处理数据: '. json_encode($pass_log, JSON_UNESCAPED_UNICODE) . '; 可能存在的问题: ' . get_data_object_error_msg($by_workflow_audit_log), ErrCode::$BUSINESS_ERROR);
                }
            }
            $db->commit();
        } catch (ValidationException $e) {
            $code = $e->getCode();
            $message = $e->getMessage();
        } catch (BusinessException $e) {
            // 业务错误（数据错误等），不可对外抛出
            $code = $e->getCode();
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        } catch (\Exception $e) {
            // 系统错误，不可对外抛出
            $code = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        }
        if (!empty($message)) {
            $db->rollback();
        }
        if (!empty($real_message)) {
            $this->logger->error('agency-payment-audit-pass failed:' . $real_message);
        }
        return [
            'code' => $code,
            'message' => $message,
            'data' => $bool ?? true
        ];
    }


    /**
     * 数据集成到代理支付模块 - 查询代付结果
     * @param array $params 请求参数
     * @return array
     */
    public function getPaymentResult($params)
    {
        $code = ErrCode::$SUCCESS;
        $message = '';
        $data = [];
        $this->logger->info('oa_external_api_getPaymentResult' . json_encode($params, JSON_UNESCAPED_UNICODE));
        try {
            Validation::validate($params, self::$get_payment_result_validate);
            $agency_payment_detail_info = AgencyPaymentDetailModel::findFirst([
                'conditions' => 'no = :no: and out_no = :out_no:',
                'bind' => [
                    'no' => $params['no'],
                    'out_no' => $params['out_no']
                ]
            ]);
            if (empty($agency_payment_detail_info)) {
                throw new ValidationException(static::$t->_('agency_payment_detail_not_found'), ErrCode::$VALIDATE_ERROR);
            }

            //支付批次
            $agency_payment = $agency_payment_detail_info->getAgencyPayment();
            if (empty($agency_payment)) {
                throw new ValidationException(static::$t->_('agency_payment_not_exists'), ErrCode::$VALIDATE_ERROR);
            }

            $data = $this->getPayResult($agency_payment_detail_info, $agency_payment);
        } catch (ValidationException $e) {
            $code = $e->getCode();
            $message = $e->getMessage();
        } catch (Exception $e) {
            $code = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $this->logger->error('oa_external_api_getPaymentResult failed:' .  $e->getMessage() . json_encode($params, JSON_UNESCAPED_UNICODE));
        }
        return [
            'code' => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data' => $data,
        ];
    }

    /**
     * 主动查询/异步通知-告知外部系统支付结果
     * @param object $agency_payment_detail_info 代理支付明细对象
     * @param object $agency_payment 代理支付批次
     * @return mixed
     * @throws ValidationException
     */
    protected function getPayResult($agency_payment_detail_info, $agency_payment)
    {
        $reason = $payer_date = $out_trade_at = '';
        if ($agency_payment->status == Enums::WF_STATE_REJECTED) {
            //如果整个单据的审批状态为“已驳回”，整个单据审批流的驳回原因
            $reason = $agency_payment->reason;

            //如果整个单据的审批状态为“已驳回”，整个单据审批流的驳回时间
            $payer_date = (string)strtotime($agency_payment->rejected_at);
        } elseif ($agency_payment->status == Enums::WF_STATE_APPROVED) {
            //支付模块
            $payment = $agency_payment_detail_info->getPayment();
            if (empty($payment)) {
                throw new ValidationException(self::$t['crowd_sourcing_data_being_processed'], ErrCode::$CROWD_SOURCING_NO_SEARCH_DATA_NULL_ERROR);
            }

            //如果整个单据的审批状态为“已通过”，明细行的支付状态为未支付，传输该单据明细行在支付模块里的未付款原因，未付款原因为空，则取撤回原因。
            $reason = $payment->not_pay_reason ? $payment->not_pay_reason : $payment->cancel_reason;

            //如果整个单据的审批状态为“已通过”，取明细行在支付模块里的的操作日期时分秒，即状态变成“已支付”或者“未支付”的日期，取后台表的操作时间，当地的操作时间
            if ($agency_payment_detail_info->pay_status == Enums::LOAN_PAY_STATUS_PAY) {
                $payer_date = (string)strtotime(show_time_zone($payment->payer_date));
            } elseif ($agency_payment_detail_info->pay_status == Enums::LOAN_PAY_STATUS_NOTPAY) {
                $payer_date = (string)strtotime($payment->updated_at);
            } else {
                $payer_date = '';
            }

            //明细行的支付状态为已支付，回传。取明细行在支付模块里的pay回传的交易时间，年月日时分秒，当地的操作时间。如果不是通过pay支付，取支付模块里的银行流水日期，银行流水日期为空，则该字段默认为空
            $out_trade_at = $payment->out_trade_at ? (string)strtotime($payment->out_trade_at) : ($payment->pay_bank_flow_date ? (string)strtotime($payment->pay_bank_flow_date) : '');
        } elseif ($agency_payment->status == Enums::WF_STATE_CANCEL) {
            //如果整个单据的审批状态为“已撤回”，整个单据审批流的撤回原因
            $reason = $agency_payment->reason;

            //如果整个单据的审批状态为“已撤回”，整个单据审批流的撤回时间
            $payer_date = (string)strtotime($agency_payment->canceled_at);
        }
        $not_pay_reason_category = null;
        //  1. 如果未进入支付模块，在代理支付直接操作的未支付，并且支付状态为未支付，则值固定为其他
        if ($agency_payment->is_pay_module == 0 && $agency_payment_detail_info->pay_status == Enums::LOAN_PAY_STATUS_NOTPAY) {
            $not_pay_reason_category = PayEnums::AGENCY_NOT_PAY_REASON_CATEGORY_OTHER;
        }
        //  2. 如果已进入支付模块，并且支付状态为未支付，则取用户选择的，未付款原因分类，如果用户未选择，则固定值为其他
        if ($agency_payment->is_pay_module == 1 && $agency_payment_detail_info->pay_status == Enums::LOAN_PAY_STATUS_NOTPAY) {
            $payment                 = $agency_payment_detail_info->getPayment();
            $not_pay_reason_category = $payment->not_pay_reason_category ? : PayEnums::AGENCY_NOT_PAY_REASON_CATEGORY_OTHER;
        }

        $data['no']                      = $agency_payment_detail_info->no;        //代理支付的明细行编号
        $data['pay_status']              = $agency_payment_detail_info->pay_status;//代理支付明细行在代理支付模块上的支付状态
        $data['not_pay_reason']          = $agency_payment_detail_info->pay_status == Enums::LOAN_PAY_STATUS_NOTPAY ? $reason : '';
        $data['not_pay_reason_category'] = $not_pay_reason_category ? intval($not_pay_reason_category) : null;
        $data['payer_date']              = $payer_date;
        $data['out_trade_at']            = $out_trade_at;
        $data['out_no']                  = $agency_payment_detail_info->out_no;//外部系统编号
        $data['task_batch_no']           = $agency_payment->task_batch_no;     //任务批次ID
        return $data;
    }

    /**
     * 推送到支付模块的单据 - 将支付结果推送至外部系统（异步通知）
     * @param object $agency_payment_detail 代理支付明细对象
     * @return bool
     */
    public function callNotifyUrl($agency_payment_detail)
    {
        try {
            //支付批次
            $agency_payment = $agency_payment_detail->getAgencyPayment();
            //关联的支付批次信息 - 接口传输 && 有异步回到url && 已成功推送至支付模块的才进行回调
            if ($agency_payment->source_type != AgencyPaymentEnums::SOURCE_TYPE_API || empty($agency_payment->notify_url)
                || $agency_payment->is_pay_module != PayEnums::BIZ_DATA_IS_PAY_MODULE_YES || $agency_payment->is_push_pay_module != AgencyPaymentEnums::AGENCY_PAYMENT_IS_PUSH_PAY_MODULE_SUCCESS) {
                return false;
            }

            //三级审批人- 已支付 || 申请人在支付模块撤回 - 未支付 || 一级支付人拒绝支付（仅针对泰国）- 未支付
            if (!in_array($agency_payment_detail->pay_status, [PayEnums::PAYMENT_MODULE_PAY_STATUS_PAY, PayEnums::PAYMENT_MODULE_PAY_STATUS_NOTPAY])) {
                return false;
            }

            $data = $this->getPayResult($agency_payment_detail, $agency_payment);

            //curl发起请求
            $header[]    = 'Content-type: application/json;charset=utf-8';
            $header[]    = 'Accept: application/json';
            $header[]    = 'Accept-Language: en';
            $json_params = json_encode($data, JSON_UNESCAPED_UNICODE);
            $this->logger->info('agency_payment callNotifyUrl request_params :【 ' . $agency_payment->notify_url . ' ' . $json_params . ' 】');
            $res_data     = curl_request($agency_payment->notify_url, $json_params, 'POST', $header);
            $res_data_arr = json_decode($res_data, true);
            $this->logger->info('agency_payment callNotifyUrl response_result:【 ' . json_encode($res_data_arr, JSON_UNESCAPED_UNICODE) . ' 】');
        } catch (\Exception $e) {
            $this->logger->error('代理支付-将支付结果推送至外部系统（异步通知）- 失败原因是:' . $e->getMessage() . '; 对应的代理支付明细数据是: ' . $agency_payment_detail->no);
            return false;
        }
        return true;
    }
}
