<?php

namespace App\Modules\Purchase\Models;

use App\Library\CInterface\BankFlowModelInterface;
use App\Library\CInterface\PayModelInterface;
use App\Library\Enums;
use App\Library\Enums\PayEnums;
use App\Library\ErrCode;
use App\Library\Exception\BusinessException;
use App\Models\Base;
use App\Modules\User\Models\AttachModel;
use App\Modules\Vendor\Services\ListService;

class PurchasePayment extends Base implements BankFlowModelInterface,PayModelInterface
{
    // initialize 在请求期间仅会被自动调用一次，目的是为模型实例进行初始化
    public function initialize()
    {
        $this->setConnectionService('db_oa');
        // setSource()方法指定数据库表
        $this->setSource('purchase_payment');

        $this->hasMany(
            'id',
            PurchasePaymentReceipt::class,
            'ppid', [
                "alias" => "Receipts",
            ]
        );

        $this->hasMany(
            'id',
            AttachModel::class,
            'oss_bucket_key', [
                'params' => [
                    'conditions' => "oss_bucket_type = ".Enums::OSS_BUCKET_TYPE_PURCHASE_PAYMENT." and deleted=0"
                ],
                "alias" => "Attachments",
            ]
        );
    }

    /**
     * @param $condition
     * @return mixed
     */
    public static function getFirst($condition)
    {
        return parent::findFirst($condition);
    }

    /**
     * @param null $parameters
     * @return mixed
     */
    public static function find($parameters = null)
    {
        return parent::find($parameters);
    }

    public function getModelByNo(string $no)
    {
        return self::findFirst(
            [
                'conditions' => 'ppno = :no:',
                'bind' => ['no' => $no]
            ]
        );
    }


    public function getModelByNos(array $no, bool $has_pay = false, bool $is_row = false)
    {
        if (!is_array($no) || empty($no)) {
            return [];
        }
        //默认条件
        $conditions = 'ppno in ({nos:array}) and status = :status: and pay_status = :pay_status:';
        $bind = [
            'nos' => $no,
            'status' => Enums::CONTRACT_STATUS_APPROVAL,
            'pay_status' => Enums::LOAN_PAY_STATUS_PENDING
        ];
        //是否需要包含已支付数据
        if ($has_pay == true) {
            $conditions = 'ppno in ({nos:array}) and status = :status: and pay_status in ({pay_status:array})';
            $bind['pay_status'] = [Enums::LOAN_PAY_STATUS_PENDING, Enums::LOAN_PAY_STATUS_PAY];
        }
        return self::find(
            [
                'conditions' => $conditions,
                'bind' => $bind
            ]
        );
    }


    public function getFormatData()
    {
        return [
            'oa_value' => $this->id,
            'oa_type' => Enums\BankFlowEnums::BANK_FLOW_OA_TYPE_PURCHASE_PAYMENT,
            'no' => $this->ppno,
            'amount' => bcdiv($this->cur_amount, 1000, 2),
            'currency' => $this->currency,
            'status' => $this->status,
            'pay_status' => $this->pay_status
        ];
    }

    public function link(array $data)
    {
        //判断现有的状态
        if (empty($this) || $this->status != Enums::CONTRACT_STATUS_APPROVAL || $this->pay_status != Enums::LOAN_PAY_STATUS_PENDING) {
            throw new BusinessException('not found purchase_payment or purchase_payment pay_status is error');
        }

        $item = [];
        $item['pay_status'] = Enums::LOAN_PAY_STATUS_PAY;    //是否已付款
        $item['operation_remark'] = $data['bank_name'].";".$data['bank_account'].";".$data['ticket_no'];
        $item['real_pay_at'] = $data['date'];
        $item['pay_id'] = $data['create_id'];
        $item['pay_at'] = date("Y-m-d H:i:s");
        $item['updated_at'] = date("Y-m-d H:i:s");
        $item['pay_from'] = 2;

        $bool = $this->i_update($item);
        if ($bool === false) {
            throw new BusinessException("采购付款申请单-支付失败", ErrCode::$CONTRACT_UPDATE_ERROR);
        }
        return true;
    }


    /** @noinspection PhpUnhandledExceptionInspection */
    public function batch_link($ids, $data)
    {
        $sql = 'update purchase_payment set 
                         pay_status=' . Enums::LOAN_PAY_STATUS_PAY . ',
                         operation_remark="' . ($data['bank_name'] . ';' . $data['bank_account'] . ';' . $data['ticket_no']) . '",
                         real_pay_at="' . $data['date'] . '",
                         pay_id="' . $data['create_id'] . '",
                         pay_bank_name="' . $data['bank_name'] . '",
                         pay_bank_account="' . $data['bank_account'] . '",
                         pay_at="' . date("Y-m-d H:i:s") . '",
                         updated_at="' . date("Y-m-d H:i:s") . '",
                         pay_from=2 where id in (' . implode(',', $ids).')';
        $this->getDI()->get('logger')->info("purchase_payment sql=".$sql);
        $bool = $this->getDI()->get('db_oa')->execute($sql);
        if ($bool === false) {
            throw new BusinessException('采购付款申请单付款-批量更新失败==' . $sql);
        }
        return true;
    }



    public function cancel($user)
    {
        //判断现有的状态
        if (empty($this) || $this->status != Enums::CONTRACT_STATUS_APPROVAL || $this->pay_status != Enums::LOAN_PAY_STATUS_PAY) {
            throw new BusinessException('not purchase_payment or purchase_payment pay_status is error');
        }

        $item = [];
        $item['pay_status'] = Enums::LOAN_PAY_STATUS_PENDING;    //是否已付款
        $item['operation_remark'] = '';
        $item['real_pay_at'] = null;
        $item['pay_id'] = null;
        $item['pay_at'] = null;
        $item['pay_from'] = 1;

        $bool = $this->i_update($item);
        if ($bool === false) {
            throw new BusinessException("采购付款申请单-撤销支付失败", ErrCode::$CONTRACT_UPDATE_ERROR);
        }
        return true;
    }

    public function batch_confirm($ids, $data)
    {
        $sql = 'update purchase_payment_receipt set 
                        is_deduct = '.intval($data['is_deduct']).'        
                        where id in (' . implode(',', $ids).')';
        $bool = $this->getDI()->get('db_oa')->execute($sql);
        if ($bool === false) {
            throw new BusinessException('采购付款申请单-批量确认失败==' . $sql);
        }
        return true;
    }

    public function getPayData()
    {
        $arr = [
            'oa_type' => Enums\BankFlowEnums::BANK_FLOW_OA_TYPE_PURCHASE_PAYMENT,
            'no' => $this->ppno,
            'apply_staff_id' => $this->create_id,
            'apply_staff_name' => $this->create_name,
            'cost_department_id' => $this->cost_department,
            'cost_department_name' => $this->cost_department_name,
            'cost_company_id' => $this->cost_company_id,
            'cost_company_name' => $this->cost_company_name,
            'apply_date' => $this->apply_date,
            'pay_method' => $this->payment_method,
            'pay_where' => $this->pay_where,
            'currency' => $this->currency,
            'amount_total_no_tax' => bcdiv($this->not_tax_amount, 1000, 2),           //不含税金额
            'amount_total_vat' => bcdiv($this->vat7_amount, 1000, 2),                 //税额
            'amount_total_have_tax' => 0,                                                       //含税金额（含VAT含WHT）
            'amount_total_wht' => bcdiv($this->wht_amount, 1000, 2),                  //wht总计
            //'amount_total_have_tax_no_wht' => bcdiv($this->real_amount, 1000, 2),//含税金额总计（含VAT不含WHT）
            'amount_total_have_tax_no_wht' => bcdiv(bcsub(bcadd($this->not_tax_amount, $this->vat7_amount),$this->wht_amount),1000,2),//含税金额总计（含VAT不含WHT）
            'amount_loan' => 0,             //冲减借款金额,
            'amount_reserve' => 0,
            'amount_discount' => 0,                    //折扣
            'amount_total_actually' => bcdiv($this->cur_amount, 1000, 2),            //实付金额
            'amount_remark' => $this->remark,                                                    //备注
            'default_planned_pay_date'     => empty($this->due_date) ? date('Y-m-d') : $this->due_date,//应付日期
            'planned_pay_date'             => empty($this->due_date) ? date('Y-m-d') : $this->due_date,//计划支付日期
        ];

        //含税金额（含VAT含WHT）
        $arr['amount_total_have_tax'] = bcadd($arr['amount_total_no_tax'], $arr['amount_total_vat'], 2);


        $arr['pays'] = [];

        $tmp = [];
        $tmp['bank_name'] = $this->bank_name;
        $tmp['bank_account'] = $this->bank_no;
        $tmp['bank_account_name'] = $this->bank_account_name;
        $tmp['amount'] = $arr['amount_total_actually'];
        $tmp['bank_address'] = '';
        $tmp['swift_code'] = $this->swift_code??'';
        $arr['pays'][] = $tmp;

        return $arr;
    }

    public function getPayCallBackData($data)
    {
        /*
         'id'=>'Required|IntGe:1',
        'pass_or_not'=>Required|IntIn:1,2',
        'real_pay_at'=>'IfIntEq:pass_or_not,1|Required|Date'*/
        $pay_status         = $data['pay_status'];
        $new                = [];
        $new['pass_or_not'] = 2;
        if ($pay_status == Enums::LOAN_PAY_STATUS_PAY) {
            $new['pass_or_not'] = 1;
            $new['real_pay_at'] = date("Y-m-d");
            if ($data['pay_method'] == Enums::PAYMENT_METHOD_CASH) {
                $new['real_pay_at'] = $data['pay_date'] ?? date("Y-m-d");
            } elseif ($data['pay_method'] == Enums::PAYMENT_METHOD_BANK_TRANSFER) {
                $new['real_pay_at']      = $data['pay_bank_flow_date'] ?? date("Y-m-d");
                $new['pay_bank_name']    = $data['pay_bank_name'];
                $new['pay_bank_account'] = $data['pay_bank_account'];
            }
        } else {
            $new['note']        = $data['not_pay_reason'];
            $new['real_pay_at'] = null;
        }
        return $new;
    }

    /**
     * 从添加时的来源获取银行账号, 用于支付模块更新银行账号
     * @param $no
     * @param $pay_id
     * @return array
     * @date 2022/3/4
     */
    public function getBankInfo($no,$pay_id){
        $self_data = self::findFirst(
            [
                'conditions' => 'ppno=:ppno: and status = :status: and pay_status = :pay_status:',
                'bind' => [
                    'ppno' => $no,
                    'status' => Enums::CONTRACT_STATUS_APPROVAL,
                    'pay_status' => Enums::LOAN_PAY_STATUS_PENDING
                ]
            ]
        );
        if (!isset($self_data->vendor_id) || empty($self_data->vendor_id)){
            return [];
        }
        $vendor_data = ListService::getInstance()->searchVendorTypeList($self_data->vendor, 1, $self_data->vendor_id);
        if (!isset($vendor_data['code']) || $vendor_data['code'] != ErrCode::$SUCCESS || empty($vendor_data['data'])){
            return [];
        }
        $vendor_info = $vendor_data['data'];
        if (!isset($vendor_info[0])){
            return [];
        }
        return [
            'type'=>3,
            'items'=>$vendor_info[0]
        ];
    }

    //打上支付模块标记
    public function updatePayTag():bool
    {
        //修改是否进入支付模块标记
        if ($this->i_update(['is_pay_module'=>1]) === false){
            return false;
        }
        return true;
    }

    /**
     * 支付模块修改收款方银行信息 -> 同步更新业务侧收款方银行信息
     *
     * @param array $data
     * @param array $user
     * @return bool
     * @throws BusinessException
     */
    public function syncUpdatePayeeInfo(array $data, array $user = [])
    {
        //判断现有的状态
        $main_model = self::findFirst([
            'conditions' => 'ppno = :no: AND status = :status: AND pay_status = :pay_status: AND is_pay_module = :is_pay_module:',
            'bind' => [
                'no' => $data['payment_no'],
                'status' => Enums::WF_STATE_APPROVED,
                'pay_status' => Enums::PAYMENT_PAY_STATUS_PENDING,
                'is_pay_module' => PayEnums::BIZ_DATA_IS_PAY_MODULE_YES,
            ],
        ]);

        // 主数据为空 或 本模块的单据, 不可变更收款人信息
        if (empty($main_model)) {
            return true;
        }

        // 变更前数据
        $this->getLogger()->info('sync_update_pyeeinfo_before_data=' . json_encode($main_model->toArray(), JSON_UNESCAPED_UNICODE));

        // 要变更的数据
        $pay_info = $data['pay'][0] ?? [];
        $sync_data = [
            'bank_account_name' => $pay_info['bank_account_name'],
            'bank_name' => $pay_info['bank_name'],
            'bank_no' => $pay_info['bank_account'],
            'swift_code' => $pay_info['swift_code'],
            'updated_at' => date('Y-m-d H:i:s')
        ];

        if ($main_model->i_update($sync_data) === false) {
            throw new BusinessException('采购付款单支付-回更收款人信息失败, 原因可能是:' . get_data_object_error_msg($main_model), ErrCode::$BUSINESS_ERROR);
        }

        // 变更后数据
        $this->getLogger()->info('sync_update_pyeeinfo_after_data=' . json_encode($main_model->toArray(), JSON_UNESCAPED_UNICODE));

        return true;
    }
}
