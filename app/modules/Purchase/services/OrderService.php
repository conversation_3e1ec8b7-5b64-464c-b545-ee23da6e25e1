<?php

namespace App\Modules\Purchase\Services;

use App\Library\Enums;
use App\Library\Enums\GlobalEnums;
use App\Library\Enums\KingDeeEnums;
use App\Library\Enums\VendorEnums;
use App\Library\Enums\PurchaseEnums;
use App\Library\Enums\MaterialClassifyEnums;
use App\Library\ErrCode;
use App\Library\Exception\BusinessException;
use App\Library\OssHelper;
use App\Library\Validation\Validation;
use App\Library\Validation\ValidationException;
use App\Models\backyard\SettingEnvModel;
use App\Models\oa\PurchaseOrderProductDeliveryModel;
use App\Modules\Budget\Services\BudgetService;
use App\Modules\Budget\Services\LedgerAccountService;
use App\Modules\Common\Models\EnvModel;
use App\Modules\Common\Services\EnumsService;
use App\Modules\Common\Services\WaterMarkerService;
use App\Modules\Contract\Models\Contract;
use App\Modules\Material\Models\MaterialFinanceCategoryModel;
use App\Modules\Organization\Models\HrJobTitleModel;
use App\Modules\Organization\Models\HrStaffInfoModel;
use App\Modules\Organization\Models\SysDepartmentModel;
use App\Modules\Purchase\Models\PurchaseApply;
use App\Modules\Purchase\Models\PurchaseApplyProduct;
use App\Modules\Purchase\Models\PurchaseOrder;
use App\Modules\Purchase\Models\PurchaseOrderProduct;
use App\Modules\Purchase\Models\PurchasePayment;
use App\Modules\Purchase\Models\PurchaseOrderPaymentMode;
use App\Modules\Purchase\Models\PurchasePaymentReceipt;
use App\Modules\Purchase\Models\PurchaseProductCategory;
use App\Modules\Purchase\Models\PurchaseProductList;
use App\Modules\Purchase\Models\PurchaseStorage;
use App\Modules\Purchase\Models\PurchaseStorageProduct;
use App\Modules\Purchase\Models\PurchaseType;
use App\Modules\Purchase\Models\Vendor;
use App\Modules\Setting\Services\CommonDataPermissionService;
use App\Modules\Setting\Services\PaymentModeService;
use App\Modules\User\Models\AttachModel;
use App\Modules\User\Models\DepartmentModel;
use App\Modules\User\Services\UserService;
use App\Modules\Workflow\Services\FYRService;
use App\Modules\Workflow\Services\WorkflowServiceV2;
use App\Repository\BySettingEnvRepository;
use App\Repository\DepartmentRepository;
use App\Repository\oa\AccountingSubjectsRepository;
use App\Repository\oa\ContractCategoryRepository;
use App\Util\RedisKey;
use Exception;
use GuzzleHttp\Exception\GuzzleException;
use Mpdf\Mpdf;

class OrderService extends BaseService
{
    const DATA_TYPE_APPLY = 1;            // 采购申请
    const DATA_TYPE_AUDIT = 2;            // 采购审核
    const DATA_TYPE_PAYMENT = 3;          // 采购支付
    const DATA_TYPE_DATA = 4;             // 数据查询
    const DATA_TYPE_REPLY = 5;            // 采购回复
    const DATA_TYPE_PURCHASE_PAYMENT = 6; // 采购付款单关联PO单

    // 采购订单号前缀
    const PURCHASE_ORDER_NO_PREFIX = 'PO';

    const NOTREQUIRED_LONG_TEXT_LEN = 'StrLenGeLe:0,100';
    const REQUIRED_LONG_TEXT_LEN = 'Required|StrLenGeLe:1,100';
    public $digits = 1000000;//原来4位 扩大为6位 需刷数据库
    public $digits_num = 6;
    public static $validate_param = [
        'pono' => 'Required|StrLenGeLe:10,20',
        'create_id' => 'Required|Int',
        'create_name' => 'Required',
        'create_department_id' => 'Required|Int',
        'pa_id' => 'Required|Int',
        'department_id' => 'Required',
        'vendor_id' => self::REQUIRED_LONG_TEXT_LEN,
        //'vendor'=>self::REQUIRED_LONG_TEXT_LEN,
        //'vendor_addr'=>self::NOTREQUIRED_LONG_TEXT_LEN,
        //'vendor_contact'=>self::REQUIRED_LONG_TEXT_LEN,
        //'vendor_phone'=>self::REQUIRED_LONG_TEXT_LEN,
        'payment_to' => self::REQUIRED_LONG_TEXT_LEN,
        'loan_time' => 'Required|Int',
        'payment_method' => 'Required|IntIn:' . Enums::PURCHASE_PAY_TYPE_BANK . ',' . Enums::PURCHASE_PAY_TYPE_CASH . "," . Enums::PURCHASE_PAY_TYPE_CHEQUE,
        'delivery_addr' => self::REQUIRED_LONG_TEXT_LEN,
        'remark' => 'StrLenGeLe:0,5000',
        'currency' => 'Required|IntIn:' . GlobalEnums::VALIDATE_CURRENCY_PARAMS,
        'attachment' => 'Arr|ArrLenGeLe:0,10',
        // 'attachment[*].bucket_name'=>'Required',
        // 'attachment[*].object_key'=>'Required',
        // 'attachment[*].file_name'=>'Required',
        'product' => 'Required|Arr|ArrLenGe:1',
        'product[*].category_a' => 'Required|Int',
        'product[*].category_b' => 'Required|Int',
        'product[*].product_id' => 'Required',
        'product[*].product_name' => self::REQUIRED_LONG_TEXT_LEN,
        // 'product[*].product_option'=>'Required|StrLenGeLe:1,30',
        // 'product[*].product_option_code'=>self::NOTREQUIRED_LONG_TEXT_LEN,
        'product[*].finance_code' => 'Required',
        'product[*].desc' => self::REQUIRED_LONG_TEXT_LEN,
        'product[*].total' => 'Required|IntGt:0',
        'product[*].unit' => 'Required|StrLenGeLe:1,30',
        'product[*].price' => 'Required|FloatGt:0',
        'product[*].total_price' => 'Required|FloatGt:0',
        'product[*].remark' => self::NOTREQUIRED_LONG_TEXT_LEN,
        'product[*].vat7' => 'Required|FloatGe:0',
        'product[*].vat7_rate' => 'Required|FloatGe:0',
        'subtotal_amount' => 'Required|FloatGt:0',
        //'discount'=>'Required|FloatGe:0',
        //'freight'=>'Required|FloatGe:0',
        //'installation_cost'=>'Required|FloatGe:0',
        'taxation' => 'Required|FloatGe:0',
    ];

    public static $validate_products_param = [
        'product' => 'Required|Arr|ArrLenGe:1',
        'product[*]' => 'Required|Obj',
        'product[*].total' => 'Required|IntGe:0',
        'product[*].price' => 'Required|FloatGe:0',
        'product[*].vat7_rate' => 'Required|FloatGe:0',
    ];

    public static $validate_update_param = [
        'id' => 'Required|IntGe:1',
        'is_free_budget' => 'IntIn:0,1',
        'product' => 'Required|Arr|ArrLenGe:1',
        'product[*]' => 'Required|Obj',
        'product[*].total' => 'Required|IntGe:0',
        'product[*].remark' => 'Required|StrLenGeLe:0,255'
    ];


    public static $validate_update_product_param = [
        'id' => 'Required|IntGe:1',
        'total' => 'Required|IntGe:0',
        'remark' => 'Required|StrLenGeLe:0,255',
        'vat7' => 'Required|FloatGe:0',
        'wht_amount' => 'Required|FloatGeLe:0,9999999999|>>>: amount wht  error'
    ];

    public static $validate_wht_product_param = [
        'product[*].wht_cate' => 'Required|IntGe:0|>>>:WHT category error', //WHT 类别
        'product[*].wht_rate' => 'Required|FloatGe:0|>>>:WHT rate error2', //WHT 税率
        'product[*].wht_amount' => 'Required|FloatGeLe:0,9999999999|>>>: amount wht  error', //WHT 金额
    ];

    public static $validate_update_delivery_date_param = [
        'id' => 'Required|IntGe:1',
        'product' => 'Required|Arr|ArrLenGe:1',
        'product[*]' => 'Required|Obj',
        'product[*].id' => 'Required|Int',
        'product[*].delivery_date' => 'Required|Date',
        'product[*].dispatch_date' => 'Required',//发货日期
        'product[*].dispatch_total' => 'Required',//发货数量
        'product[*].sailing_date' => 'Required',//开船日期
        'product[*].sailing_arrive_date' => 'Required',//到港日期
        'product[*].clearance_date' => 'Required',//清关日期
    ];
    //关闭申请单
    public static $validate_close = [
        'id' => 'Required|IntGe:1'
    ];
    //添加发货记录非必填
    public static $not_must_delivery_add = [
        'delivery[*].storage_date',
        'delivery[*].dispatch_date',
        'delivery[*].sailing_arrive_date',
        'delivery[*].sailing_date',
        'delivery[*].clearance_date',
    ];
    //添加发货记录
    public static $validate_delivery_add = [
        'po_id' => 'Required|IntGt:0',
        'product_id' => 'Required|IntGt:0',
        'delivery' => 'Required|Arr',
        'delivery[*].storage_date' => 'Date',
        'delivery[*].dispatch_date' => 'Date',
        'delivery[*].sailing_arrive_date' => 'Date',
        'delivery[*].sailing_date' => 'Date',
        'delivery[*].clearance_date' => 'Date',
        'delivery[*].dispatch_total' => 'Required|IntGt:0',
    ];
    //查看发货记录
    public static $validate_delivery_detail = [
        'product_id' => 'Required|IntGt:0',
    ];

    //数据查询-采购订单-编辑
    public static $validate_data_update = [
        'id' => 'Required|IntGe:1',
        'products' => 'Required|Arr|ArrLenGe:1',
        'products[*]' => 'Required|Obj',
        'products[*].id' => 'Required|IntGt:0',
        'products[*].product_option_code' => 'StrLenGeLe:0,30',
        'products[*].desc' => self::NOTREQUIRED_LONG_TEXT_LEN,
        'products[*].unit' => 'StrLenGeLe:0,50',//单位
        'products[*].metere_unit' => 'StrLenGeLe:0,20',//记量单位
        'products[*].product_option' => 'StrLenGeLe:0,255',//规格型号
    ];

    private static $instance;

    private function __construct()
    {
    }

    /**
     * @return OrderService
     */
    public static function getInstance()
    {
        if (!isset(self::$instance) || !(self::$instance instanceof self)) {
            self::$instance = new self;
        }
        return self::$instance;
    }

    /**
     * 获取页面初始化枚举
     * @return array
     * @date 2022/12/6
     */
    public function getOptionsDefault()
    {
        $code = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $data = [];
        try {
            $enums_arr = [
                'execute_status' => PurchaseEnums::$order_execute_status_list,
                'is_close' => PurchaseEnums::$is_close_list,
                'pdf_format' => PurchaseEnums::$pdf_format,//pdf格式
            ];
            foreach ($enums_arr as $key => $value) {
                foreach ($value as $k => $v) {
                    $data[$key][] = [
                        'value' => $k,
                        'label' => static::$t->_($v)
                    ];
                }
            }
        } catch (\Exception $e) {
            $code = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage();
            $this->logger->warning('采购订单申请-枚举异常信息: ' . $real_message);
        }
        return [
            'code' => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data' => $data,
        ];
    }

    /**
     * 获取新增默认值
     *
     * @param array $user
     * @return array
     */
    public function defaultData($user = [])
    {
        $code = ErrCode::$SUCCESS;
        $message = 'success';
        $real_message = '';

        $data = [];
        try {
            if (empty($user)) {
                throw new ValidationException(static::$t->_('access_data_staff_info_not_exist'), ErrCode::$VALIDATE_ERROR);
            }

            $arr = $this->getUserMetaFromBi($user['id']);
            if (empty($arr)) {
                throw new ValidationException(static::$t->_('re_staff_info_id_not_exist'), ErrCode::$VALIDATE_ERROR);
            }

            $data['pono'] = self::PURCHASE_ORDER_NO_PREFIX . static::getNo(date('Ymd'));
            $data['apply_date'] = date('Y-m-d');
            $data['create_id'] = $user['id'];

            $data = array_merge($data, $arr);
            $data['create_name'] = $this->getNameAndNickName($user['name'] ?? '', $user['nick_name'] ?? '');

            //查询未被关联且已通过审核的采购申请单
            $builder = $this->modelsManager->createBuilder();
            $builder->columns('p.id, p.pano');
            $builder->from(['p' => PurchaseApply::class]);
            $builder->where('p.status = :statue: AND p.is_cite = :is_cite:', [
                'statue' => Enums::PURCHASE_APPLY_STATUS_APPROVAL,
                'is_cite' => PurchaseEnums::IS_CITE_NO
            ]);
            $builder->andWhere('p.is_close != :is_close:', ['is_close' => PurchaseEnums::IS_CLOSE_YES]);
            $data['applys'] = $builder->getQuery()->execute()->toArray();

            //查询采购合同
            // 采购合同类别
            $purchase_contract_categorys = ContractCategoryRepository::getInstance()->getSubCategoryIdsByAncestryId(Enums::CONTRACT_TEMPLATE_PURCHASE);

            $builder = $this->modelsManager->createBuilder();
            $builder->columns('c.id, c.cno, c.cname AS name');
            $builder->from(['c' => Contract::class]);
            $builder->inWhere('c.template_id', $purchase_contract_categorys);
            $data['contracts'] = $builder->getQuery()->execute()->toArray();

            //获取配置
            $configs = EnumsService::getInstance()->getSettingEnvValueIds('not_display_clearance_order_type');
            $data['configs'] = ['not_display_clearance_order_type' => $configs];

        } catch (ValidationException $e) {
            $code = $e->getCode();
            $message = $e->getMessage();

        } catch (BusinessException $e) {
            $code = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage();

        } catch (\Exception $e) {
            $code = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage();
            $data = [];
        }

        if (!empty($real_message)) {
            $this->logger->warning('order-get-default-data-failed:' . $real_message);
        }

        return [
            'code' => $code,
            'message' => $message,
            'data' => $data
        ];
    }

    /**
     * 获取自定义的校验规则
     *
     * @param array $biz_data 采购订单入参组
     * @return void
     * @throws ValidationException
     */
    public function addParamValidate(array $biz_data)
    {
        // 静态校验参数
        $validate_param = BaseService::$validate_param;

        //V22269 产品行 - 网点/总部必填拦截
        if (!empty($biz_data['cost_store'])) {
            //费用所属网点：1-总部； 2-网点
            if ($biz_data['cost_store'] == Enums::PAYMENT_COST_STORE_TYPE_01) {
                $validate_param['product[*].cost_department']      = 'Required|IntGt:0';         //费用部门id
                $validate_param['product[*].cost_department_name'] = 'Required|StrLenGeLe:1,255';//费用部门名称
            } elseif ($biz_data['cost_store'] == Enums::PAYMENT_COST_STORE_TYPE_02) {
                $validate_param['product[*].cost_store_id']   = 'Required|StrLenGeLe:1,32|>>>:expense store id error[item]';   // 费用所属网点id
                $validate_param['product[*].cost_store_name'] = 'Required|StrLenGeLe:1,255|>>>:expense store name error[item]';// 费用所属网点名称
            }
        }

        // 关联的申请单产品明细必填
        $validate_param['product[*].id'] = 'Required|IntGe:0|>>>:' . static::$t->_('submit_products_data_null');

        // 费用所属公司: Flash Express是，物料编码必填
        $company_list = EnumsService::getInstance()->getSysDepartmentCompanyIds();
        if (isset($biz_data['cost_company']) && $biz_data['cost_company'] == $company_list['FlashExpress']) {
            $validate_param['wrs_code'] = 'Required|StrLenGeLe:0,64|>>>:' . static::$t->_('setting_excel_import_product_cate_empty');
        }
        // 有预扣税
        if ($biz_data['is_tax'] == PurchaseEnums::IS_WITHHOLDING_TAX_YES) {
            // wht 校验
            $wht_config = EnumsService::getInstance()->getWhtRateMap();
            foreach ($biz_data['product'] as $item) {
                $_wht_info = $wht_config[$item['wht_category']] ?? [];
                if (empty($_wht_info)) {
                    throw new ValidationException(static::$t->_('wht_type_error_hint'), ErrCode::$VALIDATE_ERROR);
                }

                if (empty($_wht_info['rate_list'][$item['wht_rate']])) {
                    throw new ValidationException(static::$t->_('wht_rate_error_hint'), ErrCode::$VALIDATE_ERROR);
                }
            }

            $validate_param = array_merge($validate_param, static::$validate_wht_product_param);
        }

        // 付款模式校验
        if (isset($biz_data['payment_mode']) && !empty($biz_data['payment_mode'])) {
            $row = PaymentModeService::getInstance()->getOne($biz_data['payment_mode']);
            if (!$row) {
                throw new ValidationException(static::$t->_('payment_mode_error_hint'), ErrCode::$VALIDATE_ERROR);
            }
        }

        //sap编号
        $cost_company_id = (new EnumsService())->getSettingEnvValueIds('purchase_sap_company_ids');
        if (isset($biz_data['cost_company']) && in_array($biz_data['cost_company'], $cost_company_id)) {
            $validate_param['sap_supplier_no'] = 'Required|StrLenGeLe:1,50';
        }

        // 产品编号字段: 当采购订单头上的采购类型是 = 库存类 且 费用所属公司是flash express（快递公司），则该字段必填 v15814
        if (isset($biz_data['purchase_type']) && $biz_data['purchase_type'] == PurchaseEnums::PURCHASE_TYPE_STOCK && isset($biz_data['cost_company']) && $biz_data['cost_company'] == $company_list['FlashExpress']) {
            $validate_param['product[*].no_tax_num'] = 'Required|IntGt:0|>>>:' . static::$t->_('purchase_order_no_tax_num');
            $validate_param['product[*].product_option_code'] = 'Required|StrLenGeLe:1,30|>>>:' . static::$t->_('purchase_order_stock_product_option_code_error');
        }

        // 判断swift_code 字段长度: 境外时, 必填; 非境外时, 非必填, 但最长不得超过30个字符
        if (isset($biz_data['swift_code']) && isset($biz_data['pay_where']) && $biz_data['pay_where'] != Enums::PAY_WHERE_OUT) {
            $validate_param['swift_code'] = 'StrLenLe:30';
        }

        /**
         * 12614【ALL-OA】资产标准型号&采购入库&资产台账-P1
         * 新增如下判断逻辑
         * 1. 这个订单里的barcode的采购类型是否和采购订单头上的采购类型保持一致，若不一致则提示toast：{编号1}、{编号2}和采购订单的采购类型不一致，请分开创建订单。
         * 注：如果采购订单头上的采购类型选择混合类，不走此校验。
         */
        if ($biz_data['purchase_type'] && $biz_data['purchase_type'] != PurchaseEnums::PURCHASE_TYPE_MIX) {
            $error_barcode = '';
            foreach ($biz_data['product'] as $product) {
                if ($product['product_option_code']) {
                    if ($product['purchase_type'] != $biz_data['purchase_type']) {
                        $error_barcode .= '{' . $product['product_option_code'] . '}、';
                    }
                }
            }
            if (!empty($error_barcode)) {
                //{编号1}、{编号2}和采购订单的采购类型不一致，请分开创建订单。
                throw new ValidationException(static::$t->_('purchase_order_purchase_type_error', ['barcode' => trim($error_barcode, '、')]), ErrCode::$VALIDATE_ERROR);
            }
        }

        $vat_config = EnumsService::getInstance()->getVatRateValueItem();
        foreach ($biz_data['product'] as $product) {
            // vat 校验
            if (!in_array($product['vat7_rate'], $vat_config)) {
                throw new ValidationException(static::$t->_('vat_sst_rate_error_hint', ['VAT_SST' => EnumsService::getInstance()->getVatSStRateName()]), ErrCode::$VALIDATE_ERROR);
            }

            //15362 发货数量不能大于商品数量
            if (isset($product['dispatch_total']) && $product['dispatch_total'] > $product['total']) {
                throw new ValidationException(static::$t->_('purchase_order_dispatch_total_error'), ErrCode::$VALIDATE_ERROR);
            }
        }
        // 发货记录过滤非必要参数
        foreach ($biz_data['product'] as &$v) {
            $v = params_filter($v, OrderService::$not_must_delivery_add);
            //校验所有发货记录的到货数量不能超过这行的数量
            if (isset($v['delivery']) && !empty($v['delivery'])) {
                $all_dispatch_total = array_sum(array_column($v['delivery'], 'dispatch_total'));
                if ($all_dispatch_total > $v['total']) {
                    throw new ValidationException(static::$t->_('delivery_add_all_dispatch_total_max_error'), ErrCode::$VALIDATE_ERROR);
                }
            }
        }
        Validation::validate($biz_data, $validate_param);

    }

    /**
     * 保存采购订单
     *
     * @param array $data
     * @param array $user
     * @return array
     */
    public function saveOne(array $data, array $user)
    {
        $code = ErrCode::$SUCCESS;
        $message = 'success';
        $real_message = '';

        $db = $this->getDI()->get('db_oa');
        $db->begin();
        try {
            // 判断PO单号是否被占用
            $exists = PurchaseOrder::getFirst([
                'conditions' => 'pono = ?0',
                'columns' => 'id',
                'bind' => [$data['pono']]
            ]);
            if (!empty($exists)) {
                throw new ValidationException(static::$t->_('purchase_order_no_exist_error', ['order_no' => $data['pono']]), ErrCode::$VALIDATE_ERROR);
            }

            // 获取关联的采购申请单, 并查验是否符合被关联逻辑
            $apply = $this->getReleasableApplyModel($data['pa_id']);

            // 公共数据处理
            $data['pano'] = $apply->pano;
            $data['pa_currency'] = $apply->currency;
            $data['pa_exchange_rate'] = $apply->exchange_rate;
            $data = $this->handleData($data, $user);
            $order_attachment = $data['attachment'];
            unset($data['attachment']);

            // 创建PO
            $model = new PurchaseOrder();
            if ($model->i_create($data) === false) {
                throw new BusinessException('采购订单创建失败, 原因可能是: ' . get_data_object_error_msg($model) . '数据=' . json_encode($data, JSON_UNESCAPED_UNICODE), ErrCode::$BUSINESS_ERROR);
            }

            // 采购申请单下的产品是否全部被引用
            // 表单中的[采购申请单]所关联的产品是否已经存在在其他的采购订单中

            //剩下的数量
            $is_all = 0;

            //按order_use_total统计剩下的数量
            $is_use_all = 0;

            //查询每件商品的采购订单的含税金额 (不含驳回,和撤回)(采购订单币种,已用的含税金额)
            $apply_product_ids = array_column($data['product'], 'id');
            $purchase_order_list = $this->getPurchaseOrderByProducts($apply_product_ids);

            foreach ($data['product'] as $product) {
                $t_model = new PurchaseOrderProduct();
                $product['poid'] = $model->id;
                $product['apply_product_id'] = $product['id'];
                $product['is_can_update'] = PurchaseEnums::IS_CAN_UPDATE_YES;
                unset($product['id']);

                if (!empty($product['apply_product_id'])) {
                    $apply_product = PurchaseApplyProduct::findFirst([
                        'conditions' => 'id = ?0',
                        'bind' => [$product['apply_product_id']]
                    ]);
                    if (($apply_product->total < $product['total']) || (($apply_product->total - $apply_product->order_total) < $product['total'])) {
                        throw new ValidationException(static::$t->_('purchase_order_total_more_than_pur_error', ['p_id' => $product['id']]), ErrCode::$VALIDATE_ERROR);
                    }

                    //乘以1w以后的数
                    $t_price = ApplyService::getInstance()->transPriceAmount($apply->currency, $data['currency'], $apply_product->price);

                    //如果采购申请单的价格<用户填的金额
                    if (bccomp($t_price, $product['price']) < 0) {
                        throw new ValidationException(static::$t->_('purchase_order_price_more_than_pur_error', ['p_id' => $product['id']]), ErrCode::$CONTRACT_CREATE_ERROR);
                    }

                    //验证含税金额(含vat含wht)
                    //计算其他采购订单使用过此产品的含税金额(含vat含wht)总计
                    $used_all_total = 0;
                    if (isset($purchase_order_list[$product['apply_product_id']])) {
                        foreach ($purchase_order_list[$product['apply_product_id']] as $pol_v) {
                            //采购订单汇率都转换为本次的币种
                            $t_order_all_total = ApplyService::getInstance()->transPriceAmount($pol_v['currency'], $data['currency'], $pol_v['all_total']);
                            $used_all_total = bcadd($used_all_total, $t_order_all_total, 2);
                        }
                    }

                    //申请单含税金额(含vat含wht)
                    $t_apply_all_total = ApplyService::getInstance()->transPriceAmount($apply->currency, $data['currency'], $apply_product->all_total);

                    // 本次含税金额(含vat含wht) + 已用含税金额(含vat含wht) 不能大于 申请单含税金额(含vat含wht)
                    $add_all_total = bcadd($product['all_total'], $used_all_total, 2);
                    if (bccomp((string)$add_all_total, (string)$t_apply_all_total, 2) == 1) {
                        $this->logger->info("purchase-apply-pano={$apply->pano}=create_purchase_order_validation=ppid={$product['apply_product_id']}=采购订单申请金额(含两税)大于采购申请单金额(含两税)，请检查！(used_all_total={$used_all_total};this all_total={$product['all_total']};apply_all_total={$t_apply_all_total};currency={$data['currency']};)");
                        throw new ValidationException(static::$t->_("purchase_order_validation_all_total"), ErrCode::$CONTRACT_CREATE_ERROR);
                    }

                    $apply_product->order_total = bcadd($apply_product->order_total, $product['total']);
                    $apply_product->is_can_update = PurchaseEnums::IS_CAN_UPDATE_NO;  //关联过不能再被修改

                    //统计是否还有剩下的
                    $is_all += ($apply_product->total - $apply_product->order_total);
                    //15362 维护po已使用数量,execute_status=0是历史数据不处理
                    if ($apply->execute_status != PurchaseEnums::APPLY_EXECUTE_STATUS_HISTORY) {
                        $old_order_use_total = $apply_product->order_use_total;
                        $apply_product->order_use_total = bcadd($apply_product->order_use_total, $product['total']);
                        $is_use_all += ($apply_product->total - $apply_product->order_use_total);
                        $this->logger->info('save-order-update-order_use_total : pap_id= ' . $apply_product->id . ';修改前order_use_total=' . $old_order_use_total . ';修改后order_use_total=' . $apply_product->order_use_total);
                    }
                    $up_apply_product = $apply_product->i_update();
                    if ($up_apply_product === false) {
                        throw new BusinessException('采购订单添加-采购申请单行数据修改失败, 原因可能是: ' . get_data_object_error_msg($up_apply_product) . '; update_data = ' . json_encode($apply_product->toArray(), JSON_UNESCAPED_UNICODE), ErrCode::$BUSINESS_ERROR);
                    }
                }

                $product_bool = $t_model->i_create($product);
                if ($product_bool === false) {
                    throw new BusinessException('采购订单添加-采购订单行创建失败, 原因可能是: ' . get_data_object_error_msg($t_model) . '; data = ' . json_encode($product, JSON_UNESCAPED_UNICODE), ErrCode::$BUSINESS_ERROR);
                }
                if (isset($product['delivery']) && !empty($product['delivery'])) {
                    //添加发货记录
                    $insert_params = [
                        'po_id' => $model->id,
                        'product_id' => $t_model->id,
                        'delivery' => $product['delivery']
                    ];
                    $delivery_add_result = $this->deliveryAdd($insert_params, $user);
                    if (!isset($delivery_add_result['code']) || $delivery_add_result['code'] != ErrCode::$SUCCESS) {
                        throw new BusinessException('采购订单添加-采购订单行-发货记录创建失败, data = ' . json_encode($insert_params, JSON_UNESCAPED_UNICODE), ErrCode::$BUSINESS_ERROR);
                    }
                }
            }

            // PO附件
            if (!empty($order_attachment)) {
                $attachments = [];
                foreach ($order_attachment as $attachment) {
                    $attachments[] = [
                        'file_name' => $attachment['file_name'] ?? '',
                        'bucket_name' => $attachment['bucket_name'] ?? '',
                        'object_key' => $attachment['object_key'] ?? '',
                        'oss_bucket_key' => $model->id,
                        'oss_bucket_type' => Enums::OSS_BUCKET_TYPE_PURCHASE_ORDER
                    ];
                }
                $t_model = new AttachModel();
                $attach_bool = $t_model->batch_insert($attachments);
                if ($attach_bool === false) {
                    throw new BusinessException('采购订单添加-附件创建失败, 原因可能是: ' . get_data_object_error_msg($t_model) . '; 数据 = ' . json_encode($attachments, JSON_UNESCAPED_UNICODE), ErrCode::$BUSINESS_ERROR);
                }
            }

            //判断如果所有都关联过，则外面也不显示修改
            //采购申请单修改, 可能修改的字段 : is_can_update,is_link_po,is_cite,is_close,execute_status
            //查询所有采购申请单的行数据,统一计算: 总行数,不允许编辑总行数,总产品数量,总po已使用数量 , 用来判断对采购申请单主表字段的影响
            $all_apply_product = PurchaseApplyProduct::find([
                'columns' => 'id, is_can_update, total, order_total, order_use_total',
                'conditions' => 'paid = :id:',
                'bind' => ['id' => $data['pa_id']]
            ])->toArray();
            $all_count = count($all_apply_product); //申请单总行数
            $all_can_not_update = 0; //禁止编辑的行数
            $all_total = 0; //所有行总产品数量
            $all_order_total = 0; //所有行总"po已使用数量和采购付款已使用数量" ,这个数量在采购付款也维护了
            $all_order_use_total = 0; //所有行总"po已使用数量" , 这个数量只在采购订单维护
            foreach ($all_apply_product as $apply_product) {
                //禁止编辑的行数
                if ($apply_product['is_can_update'] == PurchaseEnums::IS_CAN_UPDATE_NO) {
                    $all_can_not_update += 1;
                }
                //所有行总产品数量
                $all_total += $apply_product['total'];
                //所有行总"po已使用数量和采购付款已使用数量" ,这个数量在采购付款也维护了
                $all_order_total += $apply_product['order_total'];
                //所有行总"po已使用数量" , 这个数量只在采购订单维护
                $all_order_use_total += $apply_product['order_use_total'];
            }

            // 更新被关联的采购申请单的相关字段
            //设置整个采购申请单不可编辑
            if ($all_count == $all_can_not_update) {
                $this->logger->info("purchase-apply-id=" . $data['pa_id'] . "===total==" . $all_count . "===can_not_update_total=" . $all_can_not_update);
                $apply->is_can_update = PurchaseEnums::IS_CAN_UPDATE_NO;
            }

            //设置关联过采购订单，不能再被采购付款申请单关联
            $apply->is_link_po = PurchaseEnums::IS_LINK_PO_YES;
            //设置已使用数量和是否全部关联po
            if ($is_all <= 0 && $all_total == $all_order_total) {
                $this->logger->info("purchase-apply-id=" . $data['pa_id'] . "===total==" . $all_total . "===order_total=" . $all_order_total);
                $apply->is_cite = PurchaseEnums::IS_CITE_YES;
            }

            //15362设置po已使用数量和执行状态, execute_status=0为历史数据,不处理
            if ($apply->execute_status != PurchaseEnums::APPLY_EXECUTE_STATUS_HISTORY) {
                //全部订单关联完毕
                if ($is_use_all <= 0 && $all_total == $all_order_use_total) {
                    $apply->is_close = PurchaseEnums::IS_CLOSE_YES;
                    $apply->execute_status = PurchaseEnums::APPLY_EXECUTE_STATUS_DONE;
                    $this->logger->info('保存采购订单修改采购申请单状态 : purchase-apply-id=' . $data['pa_id'] . ';is_close=' . $apply->is_close . ';execute_status=' . $apply->execute_status);
                } else {
                    //部分关联po
                    $apply->execute_status = PurchaseEnums::APPLY_EXECUTE_STATUS_PARTLY;
                    $this->logger->info('保存采购订单修改采购申请单状态 : purchase-apply-id=' . $data['pa_id'] . ';execute_status=' . $apply->execute_status);
                }
            }

            if ($apply->save() === false) {
                throw new BusinessException('采购订单添加-采购申请单更新失败, 原因可能是: ' . get_data_object_error_msg($apply) . '; 数据 = ' . json_encode($apply->toArray(), JSON_UNESCAPED_UNICODE), ErrCode::$BUSINESS_ERROR);
            }

            // 创建PO审批流
            $flow_bool = (new PayFlowService(Enums::WF_PURCHASE_ORDER))->createRequest($model->id, $user);
            if ($flow_bool === false) {
                throw new BusinessException('采购订单添加-审批流创建失败, 业务数据 = ' . json_encode($model->toArray(), JSON_UNESCAPED_UNICODE), ErrCode::$BUSINESS_ERROR);
            }

            $db->commit();
        } catch (ValidationException $e) {
            $db->rollback();

            //校验错误，可对外抛出
            $code = $e->getCode();
            $message = $e->getMessage();
        } catch (BusinessException $e) {
            $db->rollback();

            //业务错误（数据错误等），不可对外抛出
            $code = $e->getCode();
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        } catch (\Exception $e) {
            $db->rollback();

            //系统错误，不可对外抛出
            $code = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        }

        if (!empty($real_message)) {
            $this->logger->warning('purchaseOrder-create-failed:' . $real_message);
        }

        return [
            'code' => $code,
            'message' => $message,
            'data' => []
        ];
    }

    /**
     * 获取订单可关联的申请单
     *
     * @param $pa_id
     * @return mixed
     * @throws ValidationException
     */
    private function getReleasableApplyModel($pa_id)
    {
        // 查询采购申请单是否已被引用
        $apply = PurchaseApply::findFirst([
            'id = :id:',
            'bind' => ['id' => $pa_id]
        ]);
        if (empty($apply)) {
            throw new ValidationException(static::$t->_('purchase_order_linked_pa_data_null', ['pa_id' => $pa_id]), ErrCode::$VALIDATE_ERROR);
        }

        if ($apply->is_cite == PurchaseEnums::IS_CITE_YES) {
            throw new ValidationException(static::$t->_('purchase_apply_is_cite_error', ['pur_no' => $apply->pano]), ErrCode::$VALIDATE_ERROR);
        }

        if ($apply->is_close == PurchaseEnums::IS_CLOSE_YES) {
            throw new ValidationException(static::$t->_('purchase_apply_is_close_error', ['pur_no' => $apply->pano]), ErrCode::$VALIDATE_ERROR);
        }

        return $apply;
    }

    /**
     * 处理数据
     *
     * @param array $data
     * @param array $user
     * @return array
     * @throws BusinessException
     * @throws ValidationException
     */
    private function handleData(array $data, array $user)
    {
        // 供应商信息验证
        $vendor_info = Vendor::getFirst([
            'conditions' => 'vendor_id = :vendor_id:',
            'columns' => 'vendor_id, vendor_name, company_address, tax_number, grade, grade_status, status, grade_change_at',
            'bind' => ['vendor_id' => $data['vendor_id']]
        ]);
        if (empty($vendor_info)) {
            throw new ValidationException(static::$t->_('related_vendor_info_is_null', ['vendor_id' => $data['vendor_id']]), ErrCode::$VALIDATE_ERROR);
        }

        //不满足状态(供应商信息通过等级状态正常注册或认证)提示
        if (!($vendor_info->status == Enums::WF_STATE_APPROVED && $vendor_info->grade_status == VendorEnums::VENDOR_GRADE_STATUS_NORMAL && in_array($vendor_info->grade, [VendorEnums::VENDOR_GRADE_REGISTER, VendorEnums::VENDOR_GRADE_AUTHENTICATION]))) {
            throw new ValidationException(static::$t->_('vendor_grade_or_status_wrong'), ErrCode::$VALIDATE_ERROR);
        }

        //供应商等级注册金额校验
        if ($vendor_info->grade == VendorEnums::VENDOR_GRADE_REGISTER) {
            $grade_start_date = $vendor_info->grade_change_at;
            //判断是否是本自然年
            $this_year_date = date('Y-01-01 00:00:00');
            if ($grade_start_date < $this_year_date) {
                $grade_start_date = $this_year_date;
            }

            $sum_total_amount = $this->checkTotalAmountByVendor($data['vendor_id'], $grade_start_date);
            $vendor_limit_amount = EnumsService::getInstance()->getSettingEnvValue('vendor_purchase_order_amount', 0);
            //币种金额转换
            $vendor_limit_amount = EnumsService::getInstance()->currencyAmountConversion(GlobalEnums::CURRENCY_USD, 1000 * $vendor_limit_amount, 2);
            $total_amount = EnumsService::getInstance()->currencyAmountConversion($data['currency'], 1000 * $data['total_amount'], 2);
            if (bcsub(bcadd($sum_total_amount, $total_amount), $vendor_limit_amount) > 0) {
                throw new ValidationException(static::$t->_('purchase_order_amount_over_limit'), ErrCode::$VALIDATE_ERROR);
            }
        }
        $data['vendor'] = $vendor_info->vendor_name;
        $data['vendor_tax_number'] = $vendor_info->tax_number;
        $data['swift_code'] = $data['swift_code'] ?? '';

        // 默认数据
        $data['sap_order_no'] = null;
        $data['operation_remark'] = '';
        $data['apply_type'] = Enums::PURCHASE_APPLY_TYPE_DEFAULT;
        $data['apply_date'] = date('Y-m-d');
        $data['created_at'] = date('Y-m-d H:i:s');
        $data['updated_at'] = $data['created_at'];
        $data['create_id'] = $user['id'] ?? ($data['create_id'] ?? 0);
        $data['create_name'] = $this->getNameAndNickName($user['name'] ?? '', $user['nick_name'] ?? '');
        $data['create_department'] = $data['create_department_id'];
        $data['sys_department_id'] = $user['sys_department_id'] ?? 0;
        $data['node_department_id'] = $user['node_department_id'] ?? 0;
        $data['cost_department'] = $data['department_id'];

        // 实时获取相关部门名称
        $department_ids = [
            $data['create_department'],
            $data['cost_department'],
            $data['cost_company']
        ];
        $department_item = (new DepartmentRepository())->getDepartmentByIds($department_ids, 2);
        $data['create_department_name'] = $department_item[$data['create_department']]['name'] ?? '';
        $data['cost_department_name'] = $department_item[$data['cost_department']]['name'] ?? '';
        $data['cost_company_name'] = $department_item[$data['cost_company']]['name'] ?? '';

        // 基本信息: 费用所属网点/总部变更：具体网点 变更为 总部[1] 或 网点[2]两个类型
        $data['cost_store_name'] = $data['cost_store'] == Enums::PAYMENT_COST_STORE_TYPE_01 ? Enums::PAYMENT_HEADER_STORE_NAME : 'Branch';
        $data['status'] = Enums::WF_STATE_PENDING;
        $data['is_cite'] = PurchaseEnums::IS_CITE_NO;
        $data['is_can_update'] = PurchaseEnums::IS_CAN_UPDATE_YES;
        $data['execute_status'] = PurchaseEnums::ORDER_EXECUTE_STATUS_NO;
        $data['is_close'] = PurchaseEnums::IS_CLOSE_NO;

        // 获取币种与系统默认币种的汇率
        // 当PO币种 与 PUR币种一致时, PO汇率取PUR汇率; 不一致时, 取币种当前配置的汇率 v17472
        $exchange_rate = $data['pa_currency'] == $data['currency'] ? $data['pa_exchange_rate'] : EnumsService::getInstance()->getCurrencyExchangeRate($data['currency']);
        $data['exchange_rate'] = $exchange_rate ? $exchange_rate : 1;


        // 去除PO表未用到的字段
        if (isset($data['from_currency'])) {
            unset($data['from_currency']);
        }
        unset($data['pa_currency'], $data['pa_exchange_rate']);

        // 其他数据
        $category_b = Enums::PURCHASE_PRODUCT_AD_CATEGORY;      //广告类

        // 行计算
        $this->handleProductsData($data);

        /**
         * 12614【ALL-OA】资产标准型号&采购入库&资产台账--p1
         * 采购订单是否需要验收，要从单行产品barcode身上的是否需要验收作为判断依据
         */
        $is_check_flag = PurchaseEnums::IS_CHECK_FLAG_NO;
        foreach ($data['product'] as $k => $v) {
            if ($data['cost_store'] && $v['category_b'] == $category_b) {
                throw new ValidationException(static::$t->_('store_purchase_order_dont_apply_ad_error'), ErrCode::$VALIDATE_ERROR);
            }

            $is_check = PurchaseEnums::IS_CHECK_FLAG_NO;
            //该产品行传递了barcode&&该barcode也需要验收，则标记该产品行需要验收
            if ($v['product_option_code'] && isset($v['update_to_acceptance']) && $v['update_to_acceptance'] == MaterialClassifyEnums::MATERIAL_CATEGORY_NO) {
                $is_check_flag = PurchaseEnums::IS_CHECK_FLAG_YES;
                $is_check = PurchaseEnums::IS_CHECK_FLAG_YES;
            }

            $data['product'][$k]['is_check'] = $is_check;
            $data['product'][$k]['product_name'] = !$v['budget_id'] && ProductService::getInstance()->getTkey($v['product_name'], static::$language ?? 'th') ? ProductService::getInstance()->getTkey($v['product_name'], static::$language ?? 'th') : $v['product_name'];
            $data['product'][$k]['unit'] = !$v['budget_id'] && ProductService::getInstance()->getTkey($v['product_name'], static::$language ?? 'th') ? ProductService::getInstance()->getTkey($v['product_name'], static::$language ?? 'th') : $v['unit'];


            //V22269 区分总部、网点
            $data['products'][$k]['cost_store_id']        = $v['cost_store_id'] ?? '';
            $data['products'][$k]['cost_store_name']      = $v['cost_store_name'] ?? '';
            $data['products'][$k]['cost_department']      = $v['cost_department'] ?? 0;
            $data['products'][$k]['cost_department_name'] = $v['cost_department_name'] ?? '';
        }

        $data['is_check_flag'] = $is_check_flag;

        return $data;
    }

    /**
     * 计算相关提取出来，多一次循环
     *
     * @param array $data
     * @param bool $is_mul_thousands 是否乘以（单价乘以1w)
     * @param bool $is_submit 是否属于提交(落库存储)
     * @param bool $is_cal_total 是否需计算相关总额
     * @return mixed
     * @throws BusinessException
     */
    public function handleProductsData(&$data, $is_mul_thousands = true, $is_submit = true, $is_cal_total = true)
    {
        $amount = 0;
        $taxation = 0;
        $subtotal_amount = 0;
        $wht_total_amount = 0;
        $total_amount = 0;

        foreach ($data['product'] as $k => &$v) {
            //单价转string,避免程序自动转科学技术法
            $v['price'] = strval($v['price']);
            //不含税单价
            //有可能需要转换货币
            if (isset($data['from_currency']) && isset($data['currency'])) {
                $v['price'] = ApplyService::getInstance()->transPriceAmount($data['from_currency'], $data['currency'], $v['price']);
            }

            $v['price'] = round($v['price'], $this->digits_num);
            $v['price'] = bcadd($v['price'], 0, $this->digits_num);   //保留6个0

            //不含税总价
            $v['total_price'] = round($v['price'] * $v['total'], 2);
            $v['total_price'] = bcadd($v['total_price'], 0, 2);
            $v['no_tax_price'] = round($v['price'] * (!empty($v['no_tax_num']) ? $v['no_tax_num'] : 1), 2);
            if ($is_mul_thousands) {
                $v['total_price'] = bcmul($v['total_price'], 1000);
                $v['no_tax_price'] = bcmul($v['no_tax_price'], 1000);

            }
            $subtotal_amount = bcadd($subtotal_amount, $v['total_price'], 2);

            // 税额
            // 是否为币种切换后计算 或者 不在可编辑范围内
            if ((isset($data['from_currency']) && isset($data['currency']) && $data['currency'] != $data['from_currency'])) {
                $v['vat7'] = round($v['price'] * $v['total'] * $v['vat7_rate'] / 100, 2);
            } else {
                $v['vat7'] = round($v['vat7'], 2);
            }
            $v['vat7'] = bcadd($v['vat7'], 0, 2);
            if ($is_mul_thousands) {
                $v['vat7'] = bcmul($v['vat7'], 1000);
            }
            //税额和
            $taxation = bcadd($taxation, $v['vat7'], 2);


            //含税金额
            $v['all_total'] = bcadd($v['vat7'], $v['total_price'], 2);
            $total_amount = bcadd($total_amount, $v['all_total'], 2);
            $v['all_total_no_wht'] = $v['all_total'];
            if ($data['is_tax'] == 1) {
                // 是否为币种切换后计算 或者 不在可编辑范围内
                if ((isset($data['from_currency']) && isset($data['currency']) && $data['currency'] != $data['from_currency'])) {
                    $v['wht_amount'] = round($v['price'] * $v['total'] * $v['wht_rate'] / 100, 2);
                } else {
                    $v['wht_amount'] = round($v['wht_amount'], 2);
                }
                $v['wht_amount'] = bcadd($v['wht_amount'], 0, 2);
                if ($is_mul_thousands) {
                    $v['wht_amount'] = bcmul($v['wht_amount'], 1000);
                }
                $v['all_total_no_wht'] = bcsub($v['all_total'], $v['wht_amount'], 2);
                $wht_total_amount = bcadd($wht_total_amount, $v['wht_amount'], 2);
            }

            //总数
            $amount = bcadd($amount, $v['all_total_no_wht'], 2);

            //单价最后乘以1w
            if ($is_mul_thousands) {
                //价格保留4位小数
                $v['price'] = bcmul($v['price'], $this->digits);
            }
        }

        $data['subtotal_amount'] = $subtotal_amount;//不含税金额总计
        $data['amount'] = $amount;//含税金额总计 含vat 不含wht/pnd
        $data['taxation'] = $taxation;//vat 金额总计
        $data['wht_total_amount'] = $wht_total_amount;//wht金额总计
        $data['total_amount'] = $total_amount;//含税金额总计 含vat 含wht/pnd
    }

    /**
     * 获取采购订单详情
     *
     * @param int $id
     * @param int $uid
     * @param boolean $download
     * @param boolean $is_filter 是否过滤掉已经全部付完款的订单
     * @param int $data_type
     * @return array
     */
    public function getDetail($id, $uid = 0, $download = false, $is_filter = false, $data_type = 0)
    {
        $code = ErrCode::$SUCCESS;
        $message = 'success';
        $data = [];

        try {
            $apply = PurchaseOrder::findFirst([
                'id = :id:',
                'bind' => ['id' => $id]
            ]);

            if (empty($apply)) {
                throw new ValidationException(static::$t->_('empty_data'), ErrCode::$VALIDATE_ERROR);
            }

            // 采购订单审批流实例
            $pay_flow_service = new PayFlowService(Enums::WF_PURCHASE_ORDER);
            $req = $pay_flow_service->getRequest($id);
            if (empty($req->id)) {
                throw new BusinessException('获取工作流批次失败', ErrCode::$BUSINESS_ERROR);
            }

            $products = $apply->getProducts();
            $attachments = $apply->getAttachments(['columns' => 'bucket_name, object_key, file_name']);
            $data = $apply->toArray();
            // 历史数据兼容处理 - 费用所属网点/总部: -1,1 总部
            $data['cost_store'] = in_array($data['cost_store'], [-1, 1]) ? 1 : 2;

            $builder = $this->modelsManager->createBuilder();
            $builder->columns('s.staff_info_id,s.job_title,w.job_name job_title_name');
            $builder->from(['s' => HrStaffInfoModel::class]);
            $builder->join(HrJobTitleModel::class, 'w.id = s.job_title', 'w');
            $builder->where('s.staff_info_id = :staff_info_id:', ['staff_info_id' => $data['create_id'] ?? '']);
            $createrInfo = $builder->getQuery()->execute()->getFirst();
            $data['create_job_title'] = '';
            if (!empty($createrInfo)) {
                $createrInfo = $createrInfo->toArray();
                $data['create_job_title'] = $createrInfo['job_title_name'] ?? '';
            }

            $products = $products ? $products->toArray() : [];

            $categories = array_values(array_filter(array_column($products, 'category_a')));
            $categories = array_merge($categories, array_values(array_filter(array_column($products, 'category_b'))));
            $budgetIds = array_values(array_filter(array_column($products, 'budget_id')));
            if ($categories) {
                $categories = PurchaseProductCategory::find([
                    'conditions' => ' id in ({ids:array}) ',
                    'bind' => ['ids' => $categories]
                ])->toArray();
                $categories = array_column($categories, null, 'id');
            }
            if ($budgetIds) {
                $budgetService = new BudgetService();
                $budgets = $budgetService->budgetObjectList($budgetIds);
            }

            $ledger_account_ids = array_values(array_filter(array_column($products, 'ledger_account_id')));
            $ledgerIdToName = [];
            $accounting_subjects_to_name = [];
            if (!empty($ledger_account_ids)) {
                //V21698-科目类型为：1快递公司科目，则回显核算科目的名称
                if ($data['account_type'] == KingDeeEnums::ACCOUNT_TYPE_FLASH_EXPRESS_COMPANY) {
                    $res = LedgerAccountService::getInstance()->getList($ledger_account_ids);
                    if ($res['code'] == ErrCode::$SUCCESS) {
                        $ledgerIdToName = array_column($res['data'], 'name', 'id');
                    }
                } elseif ($data['account_type'] == KingDeeEnums::ACCOUNT_TYPE_SUB_COMPANY) {
                    //V21698-科目类型为：2子公司科目，则回显会计科目的名称
                    $accounting_subjects_to_name = AccountingSubjectsRepository::getInstance(static::$language)->getListByIds($ledger_account_ids);
                }
            }

            /**
             * 12614【ALL-OA】资产标准型号&采购入库&资产台账-P1
             * 针对采购付款申请单获取采购订单详情时增加如下逻辑
             * 1. 采购付款申请单的发票金额（不含税）可以自动带出值：若选择了采购订单，对应PAR明细行的“发票金额（不含税）”自动带出=该采购订单该明细行的“不含税金额”—所有关联此采购订单明细行的PAR申请状态=待审核\申请状态=已通过，支付状态=待支付\申请状态=已通过，支付状态=已支付的采购付款申请单中此采购订单明细行的发票金额（不含税）的合计。
             * 2. 采购付款申请单的发票金额（含税）可以自动带出值：若选择了采购订单，对应PAR明细行的“发票金额（含税）”自动带出=该采购订单该明细行的“含税金额(含VAT含WHT)”—所有关联此采购订单明细行的PAR申请状态=待审核\申请状态=已通过，支付状态=待支付\申请状态=已通过，支付状态=已支付的采购付款申请单中此采购订单明细行的发票金额（含税）的合计。
             */
            $payment_product_amount_list = [];
            if ($is_filter) {
                //跟前端沟通过，只有采购付款申请单才会传递flag=1的参数，那么这个is_filter会变更为true，所以在此增加逻辑
                $builder = $this->modelsManager->createBuilder();
                $builder->from(['p' => PurchasePayment::class]);
                $builder->columns('SUM(ticket_amount) AS ticket_amount, SUM(ticket_amount_not_tax) AS ticket_amount_not_tax, pr.pop_id');
                $builder->leftjoin(PurchasePaymentReceipt::class, 'pr.ppid=p.id', 'pr');
                $builder->where("p.po_id=:po_id: AND (p.status=:status_pending: OR (p.status=:status_approval: AND p.pay_status IN ({pay_status_items:array})))", [
                    'po_id' => $id,
                    'status_pending' => Enums::CONTRACT_STATUS_PENDING,
                    'status_approval' => Enums::CONTRACT_STATUS_APPROVAL,
                    'pay_status_items' => [
                        Enums::PAYMENT_PAY_STATUS_PENDING,
                        Enums::PAYMENT_PAY_STATUS_PAY,
                    ]
                ]);
                $builder->groupBy('pr.pop_id');
                $payment_product_amount_list = $builder->getQuery()->execute()->toArray();
                if (!empty($payment_product_amount_list)) {
                    $payment_product_amount_list = array_column($payment_product_amount_list, null, 'pop_id');
                }
            }
            foreach ($products as $k => $product) {
                if (isset($categories) && isset($categories[$product['category_a']])) {
                    $products[$k]['category_a_text'] = $categories[$product['category_a']]['name'];
                }
                if (isset($categories) && isset($categories[$product['category_b']])) {
                    $products[$k]['category_b_text'] = $categories[$product['category_b']]['name'];
                }
                if (isset($budgets) && isset($budgets[$product['budget_id']])) {
                    $products[$k]['budget_text'] = $budgets[$product['budget_id']]['name_' . strtolower(substr(static::$language, -2))];
                }

                //核算科目名字
                $products[$k]['ledger_account_name'] = $ledgerIdToName[$product['ledger_account_id']] ?? ($accounting_subjects_to_name[$product['ledger_account_id']]['subjects_name'] ?? '');
                $products[$k]['delivery_place_txt'] = (array_column($this->getEnvByCode('purchase_order_place'), 'place_name', 'id'))[$product['delivery_place']] ?? '';
                $products[$k]['unit'] = (array_column($this->getEnvByCode('purchase_order_unit'), 'description', 'id'))[$product['unit_code']] ?? $product['unit'];

                $products[$k]['material_id_txt'] = !empty($product['material_id']) ? ((array_column($this->getPurchaseProductList(), 'description', 'material_id'))[$product['material_id']] ?? '') : '';

                // 老数据的费用所属网点获取: -1 总部; -2 其他=>按网点处理
                if (empty($product['cost_store_id'])) {
                    if ($data['cost_store'] == 1) {
                        $products[$k]['cost_store_id'] = Enums::PAYMENT_HEADER_STORE_ID;
                        $products[$k]['cost_store_name'] = self::$t['payment_cost_store_type_1'];
                    } else {
                        $products[$k]['cost_store_id'] = (string)$data['cost_store'];
                        $products[$k]['cost_store_name'] = $data['cost_store_name'];
                    }
                }
                if (!empty($payment_product_amount_list) && isset($payment_product_amount_list[$product['id']])) {
                    //如果改采购订单被关联过并且改产品也被关联过
                    $product_amount = $payment_product_amount_list[$product['id']];
                    $no_tax = bcsub($product['total_price'], $product_amount['ticket_amount_not_tax']);
                    $tax = bcsub($product['all_total'], $product_amount['ticket_amount']);
                    $products[$k]['ticket_amount_not_tax'] = intval($no_tax) >= 0 ? bcdiv($no_tax, 1000, 2) : 0.00;//发票金额（不含税）
                    $products[$k]['ticket_amount'] = intval($tax) >= 0 ? bcdiv($tax, 1000, 2) : 0.00;//发票金额（含税）
                } else {
                    $products[$k]['ticket_amount_not_tax'] = bcdiv($product['total_price'], 1000, 2);//发票金额（不含税）
                    $products[$k]['ticket_amount'] = bcdiv($product['all_total'], 1000, 2);//发票金额（含税）
                }

                // 来自采购付款单关联PO时, PO单剔除发票金额(含税)为0的行
                if ($data_type == self::DATA_TYPE_PURCHASE_PAYMENT && $products[$k]['ticket_amount'] == 0) {
                    unset($products[$k]);
                }
            }

            if ($budgetIds) {
                $data['product_v1'] = $products;
            } else {
                $data['product'] = $products;
            }

            $ask = (new FYRService())->getRequestToByReplyAsk($req, $uid);
            $data['ask_id'] = $ask ? $ask->id : '';
            $data['auth_logs'] = $this->getAuditLogs($req, $download);
            $data['edit_logs'] = (new PayFlowService(Enums::WF_PURCHASE_ORDER))->getEditLog($req);
            $data['attachment'] = $attachments ? $attachments->toArray() : [];

            $data['bank_name'] = !empty($data['bank_name']) ? $data['bank_name'] : '-';
            $data['bank_account_name'] = !empty($data['bank_account_name']) ? $data['bank_account_name'] : '-';
            $data['bank_no'] = !empty($data['bank_no']) ? $data['bank_no'] : '-';

            // 审批节点可编辑的字段
            $data['can_edit'] = false;
            $data['can_edit_fields'] = (object)[];

            // 获取是审批详情, 则获取审批节点的标识
            $data['node_tag'] = '0';

            // 来自审批详情, 获取审批相关的配置
            if ($data_type == self::DATA_TYPE_AUDIT) {
                $can_edit_fields = (new PayFlowService(Enums::WF_PURCHASE_ORDER))->getCanEditFieldByReq($req, $uid);
                if ($can_edit_fields !== false) {
                    $data['can_edit_fields'] = $can_edit_fields;
                    $data['can_edit'] = true;
                }

                $node_tag = $pay_flow_service->getPendingNodeTag($req, $uid);
                $data['node_tag'] = $node_tag ? $node_tag : '0';
            }

            if (get_country_code() == GlobalEnums::MY_COUNTRY_CODE) {
                if (!empty($data['vendor_id'])) {
                    $vendorDetail = Vendor::findFirstByVendorId($data['vendor_id']);
                    $data['ownership'] = $vendorDetail ? $vendorDetail->ownership : '';
                }
            }

            $data = $this->handleDetailData($data, $is_filter);

        } catch (ValidationException $e) {
            $code = $e->getCode();
            $message = $e->getMessage();

        } catch (BusinessException $e) {
            $code = $e->getCode();
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage();

        } catch (\Exception $e) {
            $code = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        }

        if (!empty($real_message)) {
            $this->logger->warning('purchaseOrder-detail-failed:' . $real_message);
        }

        return [
            'code' => $code,
            'message' => $message,
            'data' => $data
        ];
    }

    /**
     * 处理详情数据
     *
     * @param array $data
     * @param boolean $is_filter
     * @return array
     */
    private function handleDetailData($data, $is_filter = false)
    {
        if (empty($data) || !is_array($data)) {
            return [];
        }

        $data['amount'] = bcdiv($data['amount'], 1000, 2);

        //为0，前端不显示
        $data['discount'] = bcdiv($data['discount'], 1000, 2);
        $data['freight'] = bcdiv($data['freight'], 1000, 2);
        $data['installation_cost'] = bcdiv($data['installation_cost'], 1000, 2);
        //这3个字段，以后删除

        $data['taxation'] = bcdiv($data['taxation'], 1000, 2);
        $data['subtotal_amount'] = bcdiv($data['subtotal_amount'], 1000, 2);
        $data['wht_total_amount'] = bcdiv($data['wht_total_amount'], 1000, 2);
        $data['total_amount'] = bcdiv($data['total_amount'], 1000, 2);

        $data['create_department'] = $data['create_department_name'];
        $data['department_id'] = $data['cost_department'];
        $data['department_name'] = $data['cost_department_name'];
        $data['store_id'] = $data['cost_store_name'];
        $data['purchase_type_txt'] = (array_column($this->getPurchaseType('purchase_order_type'), 'name_key', 'id'))[$data['purchase_type']] ?? '';
        //$data['attachment'] = explode(',',$data['attachment']);

        if ($data['currency'] == 1) {
            $data['is_greater'] = bccomp($data['amount'], 500000, 2) > 0;
        } else if ($data['currency'] == 2) {
            $data['is_greater'] = bccomp($data['amount'], 16000, 2) > 0;
        } else if ($data['currency'] == 3) {
            $data['is_greater'] = bccomp($data['amount'], 110000, 2) > 0;
        } else {
            $data['is_greater'] = false;
        }

        if (!empty($data['product'])) {
            $key = 'product';
        } else {
            $key = 'product_v1';
        }

        /**
         * 12614【ALL-OA】资产标准型号&采购入库&资产台账-P1
         * 针对用于可编辑权限的节点修改物料编码时需要把当前选择的物料编码所属的所有父code返回给前端用于树状图回显
         */
        $parents_code_list = [];
        if ($data['can_edit'] === true) {
            $wrs_code_arr = array_values(array_filter(array_column($data[$key], 'wrs_code')));
            $parents_code_list = (new MaterialFinanceCategoryModel())->getParentsCodes($wrs_code_arr);
        }

        foreach ($data[$key] as $k => $v) {
            if ($key == 'product') {
                $data['product'][$k]['product_name'] = static::$t->_($v['product_name']);
            } else {
                $data[$key][$k]['product_name'] = $v['product_name'];
            }
            $data[$key][$k]['unit'] = $v['unit'];

            $data[$key][$k]['price'] = bcdiv($v['price'], $this->digits, $this->digits_num);
            $data[$key][$k]['total_price'] = bcdiv($v['total_price'], 1000, 2);
            $data[$key][$k]['no_tax_price'] = bcdiv($v['no_tax_price'], 1000, 2);

            $data[$key][$k]['can_pay_total'] = $v['total'];//- $v['pay_total'];改规则后，数量没有限制

            $data[$key][$k]['vat7_rate'] = '0';
            if (is_null($v['vat7'])) {
                $data[$key][$k]['vat7'] = "";
            } else {
                $data[$key][$k]['vat7'] = bcdiv($v['vat7'], 1000, 2);

                $data[$key][$k]['vat7_rate'] = $v['vat7_rate'];

            }

            // 无预扣税，wht 相关字段不可填，默认为空
            if (empty($data['is_tax'])) {
                $_tmp_wht_cate = '';
                $_tmp_wht_rate = '';
                $_tmp_wht_amount = '';
            } else {
                $_tmp_wht_cate = $v['wht_cate'] ?? '';// WHT类别
                $_tmp_wht_rate = $v['wht_rate'] ?? '';// WHT税率
                $_tmp_wht_amount = is_null($v['wht_amount']) ? '' : bcdiv($v['wht_amount'], 1000, 2);// WHT金额
            }

            $data[$key][$k]['wht_cate'] = $_tmp_wht_cate;
            $data[$key][$k]['wht_rate'] = $_tmp_wht_rate;
            $data[$key][$k]['wht_amount'] = $_tmp_wht_amount;

            $data[$key][$k]['all_total'] = bcdiv($v['all_total'], 1000, 2);
            $data[$key][$k]['all_total_no_wht'] = bcdiv($v['all_total_no_wht'], 1000, 2);

            if (!empty($v['wrs_code']) && !empty($parents_code_list) && isset($parents_code_list[$v['wrs_code']])) {
                $data[$key][$k]['parent_wrs_code'] = $parents_code_list[$v['wrs_code']];
            } else {
                $data[$key][$k]['parent_wrs_code'] = [];
            }
        }

        if ($is_filter) {
            $productArr = [];
            foreach ($data[$key] as $k => $v) {
                if ($v['can_pay_total'] == 0) {
                    continue;
                }
                $productArr[] = $v;
            }
            $data[$key] = $productArr;
        }


        unset($data['is_cite'], $data['apply_type'], $data['create_department_name'], $data['operation_remark']);
        return $data;
    }

    /**
     * 获取审批日志
     *
     * @param $req
     * @param boolean $download
     * @return array
     */
    private function getAuditLogs($req, $download = FALSE)
    {
        $auth_logs = (new WorkflowServiceV2())->getAuditLogs($req);

        //下载的时候不要申请
        if ($download) {
            $temp = [];
            foreach ($auth_logs as $k => $v) {
                //如果申请的就跳过
                if ($v['action'] == 0) {
                    continue;
                }
                $temp[] = $v;
            }
            $auth_logs = $temp;
        }

        return $auth_logs;
    }

    /**
     * 获取采购订单列表
     *
     * @param $condition
     * @param $user
     * @param int $type
     * @return array
     */
    public function getList($condition, $user = [], $type = 0)
    {
        $uid = $condition['uid'] = $user['id'] ?? 0;
        $page_size = empty($condition['pageSize']) ? 20 : $condition['pageSize'];
        $page_num = empty($condition['pageNum']) ? 1 : $condition['pageNum'];
        $offset = $page_size * ($page_num - 1);

        $code = ErrCode::$SUCCESS;
        $message = 'success';
        $data = [];

        try {
            $column_str = 'o.id,o.pono,o.apply_date,o.currency,o.create_name,o.create_department_name,o.create_id,o.amount,o.cost_department_name,o.cost_store_name,o.status,o.is_can_update,o.is_cite,o.created_at,o.is_close,o.execute_status,p.budget_id';

            // 审核模块的已处理列表, 展示处理时间
            if ($type == self::LIST_TYPE_AUDIT && isset($condition['flag']) && $condition['flag'] == GlobalEnums::AUDIT_TAB_PROCESSED) {
                $column_str .= ',log.audit_at';
            }

            $builder = $this->modelsManager->createBuilder();
            $builder->from(['o' => PurchaseOrder::class]);
            $builder->leftjoin(PurchaseOrderProduct::class, 'o.id=p.poid', 'p');
            $builder = $this->getListCondition($builder, $condition, $user, $type);

            $count = (int)$builder->columns('COUNT(DISTINCT o.id) AS total')->getQuery()->getSingleResult()->total;

            $items = [];
            if ($count) {
                $builder->columns($column_str);
                $builder->groupBy('o.id');

                if (!in_array($type, [self::LIST_TYPE_AUDIT, self::LIST_TYPE_FYR])) {
                    $builder->orderBy('o.id desc');
                }

                $builder->limit($page_size, $offset);
                $items = $builder->getQuery()->execute()->toArray();
                $items = $this->handleListItems($items, $uid, false, $type);
            }

            $data = [
                'items' => $items,
                'pagination' => [
                    'current_page' => $page_num,
                    'per_page' => $page_size,
                    'total_count' => $count,
                ]
            ];
        } catch (\Exception $e) {
            $code = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $this->logger->error('purchase-order-list-failed:' . $e->getMessage());
        }

        return [
            'code' => $code,
            'message' => $message,
            'data' => $data
        ];
    }

    /**
     * 获取列表条件
     * @param object $builder
     * @param $condition
     * @param null $user
     * @param int $type
     * @return mixed|object
     * @throws BusinessException
     */
    private function getListCondition(object $builder, $condition, $user = [], $type = 0)
    {
        $pono = $condition['pono'] ?? '';

        // v11037: 支持审批状态多选
        $status = isset($condition['status']) && is_array($condition['status']) ? $condition['status'] : [];
        if (!empty($condition['status']) && !is_array($condition['status'])) {
            $status = [
                $condition['status']
            ];
        }

        $apply_date_start = $condition['created_at_start'] ?? '';
        $apply_date_end = $condition['created_at_end'] ?? '';
        $product_name = $condition['product_name'] ?? "";
        $create_id = $condition['create_id'] ?? '';
        $approved_start_date = $condition['approved_start_date'] ?? '';
        $approved_end_date = $condition['approved_end_date'] ?? '';
        $is_close = $condition['is_close'] ?? [];
        $execute_status = $condition['execute_status'] ?? [];

        // 征询回复进度
        $is_reply = $condition['is_reply'] ?? GlobalEnums::CONSULTED_REPLY_STATE_PENDING;
        $is_reply = in_array($is_reply, GlobalEnums::CONSULTED_REPLY_STATE_ITEM) ? $is_reply : GlobalEnums::CONSULTED_REPLY_STATE_PENDING;

        // 审批处理进度
        $flag = $condition['flag'] ?? GlobalEnums::AUDIT_TAB_PENDING;
        $flag = in_array($flag, GlobalEnums::AUDIT_TAB_STATE_ITEM) ? $flag : GlobalEnums::AUDIT_TAB_PENDING;

        $pano = $condition['pano'] ?? 0;
        $cost_company_id = $condition['cost_company_id'] ?? [];
        $product_code = $condition['product_code'] ?? '';//产品编码

        // 审核列表
        if ($type == self::LIST_TYPE_AUDIT) {
            $builder = (new WorkflowServiceV2())->getBizWorkflowOrderListByProcessState($builder, $flag, [Enums::WF_PURCHASE_ORDER], $condition['uid'], 'o');

        } else if ($type == self::LIST_TYPE_APPLY) {
            if ($condition['uid']) {
                $builder->andWhere('o.create_id = :uid:', ['uid' => $condition['uid']]);
            }
        } else if ($type == self::LIST_TYPE_FYR) {
            // 意见征询回复列表
            $biz_table_info = ['table_alias' => 'o'];
            $builder = (new FYRService())->getBizOrderConsultationReplyListByProcessState($builder, $is_reply, [Enums::WF_PURCHASE_ORDER], $condition['uid'], $biz_table_info);

        } else if ($type == self::LIST_TYPE_DATA) {
            // 数据查询模块的
            // OA供应商编号
            if (!empty($condition['vendor_id'])) {
                $builder->andWhere('o.vendor_id = :vendor_id:', ['vendor_id' => $condition['vendor_id']]);
            }

            // 供应商名称
            if (!empty($condition['vendor'])) {
                $builder->andWhere('o.vendor LIKE :vendor_name:', ['vendor_name' => "{$condition['vendor']}%"]);
            }
            //兼容：给指定邮箱地址发送采购订单的全量导出数据，此时 user 为 null
            if(!empty($user)) {
                // 对接通用数据权限
                // 业务表参数
                $table_params = [
                    'table_alias_name' => 'o',
                    'create_id_field' => 'create_id',
                    'create_node_department_id_filed' => 'cost_department',
                ];
                $builder = CommonDataPermissionService::getInstance()->getCommonDataPermission($user, $builder, Enums\SysConfigEnums::SYS_MODULE_PURCHASE_ORDER, $table_params);
            }
        }

        if (!empty($pono)) {
            $builder->andWhere('o.pono = :pono:', ['pono' => $pono]);
        }

        // 费用所属公司
        if (!empty($cost_company_id)) {
            if (is_array($cost_company_id)) {
                $builder->andWhere('o.cost_company IN ({cost_company_id:array})', ['cost_company_id' => array_values($cost_company_id)]);
            } else {
                $builder->andWhere('o.cost_company = :cost_company_id:', ['cost_company_id' => $cost_company_id]);
            }
        }

        //工号或者姓名
        if (!empty($create_id)) {
            $builder->andWhere('o.create_id = :create_id: or o.create_name=:create_id:', ['create_id' => $create_id]);
        }

        if (!empty($status)) {
            $builder->inWhere('o.status', array_values($status));
        }

        if (!empty($apply_date_start)) {
            $builder->andWhere('o.apply_date >= :apply_date_start:', ['apply_date_start' => $apply_date_start]);
        }

        if (!empty($apply_date_end)) {
            $builder->andWhere('o.apply_date <= :apply_date_end:', ['apply_date_end' => $apply_date_end]);
        }

        if (!empty($product_name)) {
            $builder->andWhere('p.product_name=:product_name:', ['product_name' => $product_name]);
        }

        if (!empty($pano)) {
            $builder->andWhere('o.pano = :pano:', ['pano' => $pano]);
        }

        /**
         * 12614【ALL-OA】资产标准型号&采购入库&资产台账-P1需求
         * 发现历史bug，采购订单，按照产品编码搜索不起作用，在此增加产品编码
         */
        if (!empty($product_code)) {
            $builder->andWhere('p.product_option_code=:product_option_code:', ['product_option_code' => $product_code]);
        }

        //起始日期
        if (!empty($approved_start_date)) {
            $approved_start_date .= ' 00:00:00';
            $builder->andWhere('o.approve_at >= :approve_start_date:', ['approve_start_date' => $approved_start_date]);
        }

        //截止日期
        if (!empty($approved_end_date)) {
            $approved_end_date .= ' 23:59:59';
            $builder->andWhere('o.approve_at <= :approve_end_date:', ['approve_end_date' => $approved_end_date]);
        }
        //是否关闭
        if (!empty($is_close)) {
            foreach ($is_close as &$is_close_v) {
                $is_close_v = (int)$is_close_v;
            }
            $builder->inWhere('o.is_close', $is_close);
        }
        //执行状态
        if (!empty($execute_status)) {
            foreach ($execute_status as &$execute_status_v) {
                $execute_status_v = (int)$execute_status_v;
            }
            $builder->inWhere('o.execute_status', $execute_status);
        }

        return $builder;
    }

    /**
     * 处理列表数据
     *
     * @param array $items
     * @param int $uid
     * @param bool $is_export 是否excel导出
     * @return array
     */
    private function handleListItems(array $items, $uid = 0, $is_export = false, $type = 0)
    {
        if (empty($items) || !is_array($items)) {
            return [];
        }

        // 当前用户是否是特权用户
        $is_privilege_staff_id = EnumsService::getInstance()->isPrivilegeStaffId($uid);

        //获取支付模式枚举项【导出才有】
        $payment_modes = [];
        if ($is_export) {
            $payment_mode_list = PurchaseOrderPaymentMode::find('is_del = ' . GlobalEnums::IS_NO_DELETED)->toArray();
            if ($payment_mode_list) {
                $payment_modes = array_column($payment_mode_list, 'payment_mode_code', 'id');
            }
        }

        foreach ($items as &$item) {
            $status = Enums::$loan_status[$item['status']] ?? '';
            $item['amount'] = bcdiv($item['amount'], 1000, 2);
            $item['status_text'] = static::$t->_($status);
            $payment_currency = GlobalEnums::$currency_item[$item['currency']] ?? '';
            $item['currency_text'] = static::$t->_($payment_currency);

            // 无用户 或 非终审通过, 可修改数量的标识置为0 (目前 仅 我的申请 和 数据查询菜单用到)
            if (empty($uid) || $item['status'] != Enums::CONTRACT_STATUS_APPROVAL) {
                $item['is_can_update'] = '0';
            }

            // 明细里的费用所属网点/总部历史数据兼容
            if (!empty($item['item_cost_store_name'])) {
                $item['cost_store_name'] = $item['item_cost_store_name'];
            }
            //支付模式枚举项
            if ($is_export) {
                $item['payment_mode_txt'] = $payment_modes && !empty($item['payment_mode']) ? self::$t->_($payment_modes[$item['payment_mode']]) : '';
            }

            $item['is_privilege'] = $is_privilege_staff_id;

            //1、创建日期在2022.8.1之后的订单
            //2、采购订单状态为“待审核”或“已通过”
            //3、是否所有产品关联过付款申请单 is_cite = 0
            if (isset($item['created_at']) && $item['created_at'] >= PurchaseEnums::CAN_EDIT_DELIVERY_DATE && in_array($item['status'], [Enums::CONTRACT_STATUS_PENDING, Enums::CONTRACT_STATUS_APPROVAL]) && isset($item['is_cite']) && $item['is_cite'] == PurchaseEnums::IS_CITE_NO) {
                $item['is_can_edit_delivery_date'] = 1;
            } else {
                $item['is_can_edit_delivery_date'] = 0;
            }
            //增加关闭状态和执行状态
            $item['is_close_text'] = isset(PurchaseEnums::$is_close_list[$item['is_close']]) ? static::$t->_(PurchaseEnums::$is_close_list[$item['is_close']]) : '';
            $item['execute_status_text'] = isset(PurchaseEnums::$order_execute_status_list[$item['execute_status']]) ? static::$t->_(PurchaseEnums::$order_execute_status_list[$item['execute_status']]) : '';
            //是否可以关闭
            $item['can_close'] = PurchaseEnums::CAN_CLOSE_NO;
            //申请状态为"已通过",执行状态为"未交付","部分交付",且关闭状态为未关闭
            $can_close_conditions = $item['status'] == Enums::PURCHASE_ORDER_STATUS_APPROVAL && in_array($item['execute_status'], [PurchaseEnums::ORDER_EXECUTE_STATUS_PARTLY, PurchaseEnums::ORDER_EXECUTE_STATUS_NO]) && isset($item['is_close']) && $item['is_close'] == PurchaseEnums::IS_CLOSE_NO;
            if ($type == self::LIST_TYPE_DATA) {
                //数据查询-必须要有特权才能关闭
                if ($can_close_conditions && $is_privilege_staff_id) {
                    $item['can_close'] = PurchaseEnums::CAN_CLOSE_YES;
                }
            } elseif ($type == self::LIST_TYPE_APPLY) {
                if ($can_close_conditions) {
                    $item['can_close'] = PurchaseEnums::CAN_CLOSE_YES;
                }
            }
        }

        return $items;
    }

    /**
     * 采购订单全部审批通过 并且 采购申请单全部被关联完毕
     *
     * @param $id integer 采购订单的ID
     * @param $user
     *
     */
    public function freedBudget($id, $user)
    {
        // 验证默认国家是否开启预算(0:关闭 1:打开)
        if (!(new EnumsService())->getBudgetStatus()) {
            return true;
        }
        $purchaseOrder = PurchaseOrder::findFirst([
            'conditions' => ' id = :id: ',
            'bind' => ['id' => $id]
        ]);
        $purchaseOrder = $purchaseOrder ? $purchaseOrder->toArray() : [];
        if ($purchaseOrder) {
            $paId = $purchaseOrder['pa_id'];
            $purchaseApply = ApplyService::getInstance()->purchaseInfo($paId);
            if ($purchaseApply && $purchaseApply['is_cite'] == 1  // 全部关联完成
                && isset($purchaseApply['products']) && $purchaseApply['products'][0]['budget_id'] // 采购申请单是新数据 占预算
                && $this->isOldData($purchaseApply['created_at'])
            ) {

                // 存在待审批的采购订单
                $existPending = PurchaseOrder::findFirst([
                    'conditions' => ' pa_id = :pa_id: and status = :status: ',
                    'bind' => [
                        'pa_id' => $paId,
                        'status' => Enums::CONTRACT_STATUS_PENDING,
                    ],
                ]);

                if ($existPending) {
                    return true;
                }
                $purchaseOrderList = $this->purchaseOrderList($purchaseOrder['pa_id']);
                // 采购订单金额 科目code与金额映射
                $orderAmount = [];
                foreach ($purchaseOrderList as $purchaseOrder) {
                    if (isset($purchaseOrder['products'])) foreach ($purchaseOrder['products'] as $product) {
                        // 金额根据币种汇率转换为系统默认币种的额度: 比如泰铢
                        $mount = (new EnumsService())->amountExchangeRateCalculation($product['all_total_no_wht'], $purchaseOrder['exchange_rate'], 0);

                        if (isset($orderAmount[$product['level_code']])) {
                            $orderAmount[$product['level_code']] += $mount;
                        } else {
                            $orderAmount[$product['level_code']] = $mount;
                        }
                    }
                }

            }
            if (isset($orderAmount) && $orderAmount) {
                $budgetService = new BudgetService();
                $result = $budgetService->re_back_budget($purchaseApply['pano'], $user, BudgetService::ORDER_TYPE_2, $orderAmount);
                $this->getDI()->get('logger')->info('purchase_order  释放预算判断 params ' . json_encode([
                        $purchaseApply['pano'],
                        $user,
                        BudgetService::ORDER_TYPE_2,
                        $orderAmount
                    ]) . ' results ' . json_encode([$result]));
                if ($result['code'] != ErrCode::$SUCCESS) {
                    throw new ValidationException($result['message']);
                }
            }
        }

        return true;
    }

    /**
     * 根据采购申请单ID获取所有关联的采购订单
     *
     * @param $paId integer 采购申请单ID
     *
     */
    private function purchaseOrderList($paId)
    {
        $purchaseOrders = PurchaseOrder::find([
            'conditions' => ' pa_id = :pa_id: and status = :status: ',
            'bind' => [
                'pa_id' => $paId,
                'status' => Enums::CONTRACT_STATUS_APPROVAL,
            ],
        ])->toArray();
        $ids = array_column($purchaseOrders, 'id');
        if ($ids) {
            $purchaseOrderProducts = PurchaseOrderProduct::find([
                'conditions' => ' poid in ({poids:array})',
                'bind' => ['poids' => $ids]
            ])->toArray();
            $purchaseOrderProductsGrpByOrderId = [];
            foreach ($purchaseOrderProducts as $purchaseOrderProduct) {
                $purchaseOrderProductsGrpByOrderId[$purchaseOrderProduct['poid']][] = $purchaseOrderProduct;
            }
            foreach ($purchaseOrders as $key => $purchaseOrder) {
                if (isset($purchaseOrderProductsGrpByOrderId[$purchaseOrder['id']])) {
                    $purchaseOrders[$key]['products'] = $purchaseOrderProductsGrpByOrderId[$purchaseOrder['id']];
                }
            }
        }

        return $purchaseOrders;
    }

    /**
     * 采购订单详情下载
     *
     * @param Integer $id
     * @param Integer $uid
     * @param integer $pdf_format 1采购订单（不含条款）、2采购订单（含条款）
     * @return array
     */
    public function download($id, $pdf_format, $uid = NULL)
    {
        //导出锁
        if ($this->checkLock(RedisKey::PURCHASE_DOWNLOAD_LOCK . $uid)) {
            return ['code' => ErrCode::$SYSTEM_ERROR, 'message' => 'exporting now Please wait!'];
        } else {
            $this->setLock(RedisKey::PURCHASE_DOWNLOAD_LOCK . $uid, 1, 10);
        }
        $code = ErrCode::$SUCCESS;
        $message = $real_message = '';

        $download_url = '';

        $lang = static::$language;
        $country_code = get_country_code();
        $watermark = BASE_PATH . '/public/images/watermark_logo.png';
        try {
            $data = $this->getDetail($id, $uid, true);
            if ($data['code'] != ErrCode::$SUCCESS) {
                throw new BusinessException('获取采购订单信息失败', $data['code']);
            }
            $data = $data['data'];
            $data['country_code'] = $country_code;

            if (isset($data['product_v1']) && $data['product_v1']) {

                //V21008【TH/MY/PH｜OA】采购订单pdf修改
                if (in_array($country_code, [GlobalEnums::TH_COUNTRY_CODE, GlobalEnums::MY_COUNTRY_CODE, GlobalEnums::PH_COUNTRY_CODE])) {
                    //pdf格式必传
                    if (!in_array($pdf_format, [PurchaseEnums::PURCHASE_ORDER_PDF_FORMAT_EXCLUDING_TERMS, PurchaseEnums::PURCHASE_ORDER_PDF_FORMAT_INCLUDING_TERMS])) {
                        throw new ValidationException(static::$t->_('purchase_order_pdf_error'), ErrCode::$VALIDATE_ERROR);
                    }
                    //校验单据上的费用所属公司是否有后台映射，如果没有，则提示：单据缺少费用所属公司的映射，请联系产品
                    $set_company_info = EnumsService::getInstance()->getCompanyInfo($data['cost_company']);
                    if (!$set_company_info) {
                        throw new ValidationException(static::$t->_('company_info_set_miss'), ErrCode::$VALIDATE_ERROR);
                    }
                    $data['cost_company_name'] = ($set_company_info['name'] ?? '') . ($country_code == GlobalEnums::MY_COUNTRY_CODE ? ($set_company_info['registration_no'] ?? '') : '');
                    $data['logo'] = $set_company_info['logo'] ?? '';
                    $watermark = $set_company_info['watermark'] ?? '';
                    $data['pdf_format'] = $pdf_format;
                    $version = 'order_v2_' . (($country_code == GlobalEnums::MY_COUNTRY_CODE) ? 'en' :$lang);

                } else {
                    //其他国家默认不含条款
                    $data['pdf_format'] = PurchaseEnums::PURCHASE_ORDER_PDF_FORMAT_EXCLUDING_TERMS;
                    $version = 'order_v1_'. $lang;
                }
            } else {
                $version = 'order_'. $lang;
            }

            $data['payment_method'] = static::$t->_('global.payment.method.' . $data['payment_method']);
            $data['currency_text'] = static::$t->_(GlobalEnums::$currency_item[$data['currency']]);

            $data['product'] = $data['product'] ?? [];
            foreach ($data['product'] as $k => $v) {
                $data['product'][$k]['category_a_name'] = static::$t->_(PurchaseProductCategory::getFirst([
                    'conditions' => 'id=?0',
                    'columns' => 'name_key',
                    'bind' => [$v['category_a']]
                ])->name_key);
                $data['product'][$k]['category_b_name'] = static::$t->_(PurchaseProductCategory::getFirst([
                    'conditions' => 'id=?0',
                    'columns' => 'name_key',
                    'bind' => [$v['category_b']]
                ])->name_key);
            }


            //合并单元格的数
            $data['total_rowspan'] = 3;
            if ($data['discount'] !== '0.00') {
                $data['total_rowspan']++;
            }
            if ($data['freight'] !== '0.00') {
                $data['total_rowspan']++;
            }
            if ($data['installation_cost'] !== '0.00') {
                $data['total_rowspan']++;
            }

            $data['pm_staff_id'] = EnvModel::getEnvByCode('procurement_manager_staff_id', 60879);
            $staffInfoModel = (new UserService)->getUserByIdInRbi($data['pm_staff_id']);
            $data['pm_staff_name'] = '';
            if (!empty($staffInfoModel)) {
                if ($lang == 'en') {
                    $data['pm_staff_name'] = $staffInfoModel->name_en;
                } else {
                    $data['pm_staff_name'] = $staffInfoModel->name;
                }
            }

            if (!empty($data['cost_company_name'])) {
                $departId = $data['cost_company'];
            } else {
                $companyModel = SysDepartmentModel::findFirst([
                    'conditions' => ' id = :id: ',
                    'bind' => [
                        'id' => $data['cost_department']
                    ],
                ]);
                if (!empty($companyModel) && !empty($companyModel->company_name)) {
                    $departId = $companyModel->company_id;
                } else {
                    $companyFindName = GlobalEnums::PH_COUNTRY_CODE == get_country_code() ? 'Flash Express_PH' : 'Flash Express';
                    $companyModel = SysDepartmentModel::findFirst([
                        'conditions' => ' name = :name: ',
                        'bind' => [
                            'name' => $companyFindName
                        ],
                    ]);
                    $departId = $companyModel->company_id ?? 0;
                }
            }


            $departModel = SysDepartmentModel::findFirst([
                'conditions' => ' id = :id: ',
                'bind' => [
                    'id' => $departId
                ],
            ]);

            // 公司地址
            $data['sap_company_address'] = !empty($departModel) ? $departModel->sap_company_address : '';
            // tax id
            $data['sap_tax_id'] = !empty($departModel) ? $departModel->sap_tax_id : '';

            $file_path = sys_get_temp_dir() . '/';
            $file_name = "order_" . md5($id) . "_" . $lang . ".pdf";

            $view = new \Phalcon\Mvc\View();
            $path = APP_PATH . '/views';
            $view->setViewsDir($path);
            $view->setVars($data);
            $view->start();
            $view->disableLevel(
                [
                    \Phalcon\Mvc\View::LEVEL_LAYOUT => false,
                    \Phalcon\Mvc\View::LEVEL_MAIN_LAYOUT => false,
                ]
            );

            $view->render("purchase", $version);

            $view->finish();
            $content = $view->getContent();

            $mpdf = new Mpdf([
                'format' => 'A4',
                'mode' => 'zh-CN'
            ]);

            $mpdf->useAdobeCJK = true;
            $mpdf->SetDisplayMode('fullpage');
            $mpdf->SetHTMLHeader("");
            $mpdf->SetHTMLFooter("");
            $mpdf->WriteHTML($content);
            $mpdf->Output($file_path . $file_name, "f");

            // pdf 加水印
            WaterMarkerService::getInstance()->addWaterMarkerToPdfFile($file_path . $file_name, $file_path . $file_name, $watermark);

            // 生成成功, 上传OSS
            $upload_res = OssHelper::uploadFile($file_path . $file_name);
            $download_url = !empty($upload_res['object_url']) ? $upload_res['object_url'] : '';

        } catch (ValidationException $e) {
            $code = $e->getCode();
            $message = $e->getMessage();
        } catch (BusinessException $e) {
            $code = $e->getCode();
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        } catch (\Mpdf\MpdfException $e) {
            $code = $e->getCode();
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        } catch (Exception $e) {
            $code = $e->getCode();
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        }

        if (!empty($real_message)) {
            $logger = $this->getDI()->get('logger');
            $logger->warning('Purchase-order-detail-download-failed:' . $real_message);
        }
        $this->unLock(RedisKey::PURCHASE_DOWNLOAD_LOCK);
        return [
            'code' => $code,
            'message' => $message,
            'data' => ["url" => $download_url]
        ];

    }

    /**
     * 导出列表
     *
     * @param $condition
     * @param $user
     * @param int $type
     * @return array
     * @throws BusinessException
     * @throws GuzzleException
     */
    public function export($condition, $user = [], $type = 0)
    {
        $uid = $user['id'] ?? 0;
        // 来自api
        if (!empty($uid)) {
            ini_set('memory_limit', '2560M');
        }

        $return = ['code' => ErrCode::$BUSINESS_ERROR, 'message' => 'error', 'data' => ''];

        //导出锁
        if ($this->checkLock(RedisKey::PURCHASE_EXPORT_LOCK . $uid)) {
            $return['code'] = ErrCode::$VALIDATE_ERROR;
            $return['message'] = 'exporting now Please wait!';
            return $return;
        } else {
            $this->setLock(RedisKey::PURCHASE_EXPORT_LOCK . $uid, 1, 10);
        }

        $data = $this->getDownloadList($condition, $user, $type);
        if ($data['code'] != ErrCode::$SUCCESS) {
            $return['message'] = $data['message'];
            return $return;
        }

        $data = $data['data']['items'];
        if (empty($data)) {
            $return['message'] = 'not find data[order]';
            return $return;
        }

        $order_product_ids = array_column($data, "pop_id");
        $payments = $this->getPaymentsProducts($order_product_ids);
        if ($payments['code'] != ErrCode::$SUCCESS) {
            $return['message'] = 'not find data[products]';
            return $return;
        }
        $payments = $payments['data'];
        $list = PurchaseProductCategory::find()->toArray();
        $nameKeyArr = array_column($list, "name_key", "id");

        $new_data = [];

        $budgetIds = array_values(array_unique(array_column($data, 'budget_id')));
        $budgetService = new BudgetService();
        $budgets = $budgetService->budgetObjectList($budgetIds);

        // 获取核算科目配置表数据
        $ledger_account_list = LedgerAccountService::getInstance()->getList();
        $ledger_account_list = !empty($ledger_account_list['data']) ? array_column($ledger_account_list['data'], 'name', 'id') : [];

        //V21689-会计科目
        $accounting_subjects_to_name = AccountingSubjectsRepository::getInstance(static::$language)->getListByIds();

        // 获取wht类别和税率
        $wht_setting = EnumsService::getInstance()->getWhtRateMap(0);

        foreach ($data as $key => $val) {
            //一般是被物理删除的
            if (empty($val['pono'])) {
                continue;
            }

            $new_data[$key] = [];
            $new_data[$key][] = $val['pono'];
            $new_data[$key][] = $val['create_name'];
            $new_data[$key][] = $val['create_id'];
            $new_data[$key][] = $val['apply_date'];
            $new_data[$key][] = $val['pano'];   //关联采购申请单
            $new_data[$key][] = $val['cno'];   // 相关合同

            $new_data[$key][] = $val['cost_department_name'];   //费用部门
            $new_data[$key][] = $val['cost_company_name'];   //费用所属公司
            $new_data[$key][] = $val['cost_store_name'];    //费用网点
            $new_data[$key][] = $val['vendor'];     //供应商名字
            $new_data[$key][] = $val['vendor_email']; //供应商邮箱
            $new_data[$key][] = $val['vendor_tax_number']; //供应商税务号
            $new_data[$key][] = $val['bank_name']; //供应商银行名称
            $new_data[$key][] = $val['bank_account_name']; //供应商银行账户名称
            $new_data[$key][] = $val['bank_no']; //供应商银行账号
            $new_data[$key][] = $val['swift_code']; //swift code
            $new_data[$key][] = $val['loan_time'];  //信贷期限
            $new_data[$key][] = $val['payment_mode_txt'];  //支付模式
            $new_data[$key][] = static::$t->_('global.payment.method.' . $val['payment_method']);  //付款方式
            $new_data[$key][] = static::$t->_('pay_where.' . $val['pay_where']);  //境内外支付
            $new_data[$key][] = $val['sap_order_no'];  //SAP采购订单号码

            $new_data[$key][] = $val['payment_to'] ?? '';  //支付给
            $new_data[$key][] = $val['delivery_addr'];  //送货地址
            $new_data[$key][] = static::$t->_(GlobalEnums::$currency_item[$val['currency']]);  //币种

            if (empty($payments[$val['pop_id']])) {
                $new_data[$key][] = "";     //付款申请单编号
                $new_data[$key][] = "";     //付款申请单状态
                $new_data[$key][] = "";     //付款申请单产品数量
                $new_data[$key][] = "";     //付款申请单发票金额
                $new_data[$key][] = "";     //付款申请单当前审批人
                $new_data[$key][] = "";     //付款申请单申请日期
                $new_data[$key][] = "";     //付款申请单发票金额总计
                $new_data[$key][] = "";     //付款申请单付款日期
                $new_data[$key][] = "";     //付款申请单是否支付

            } else {
                //相关采购订单编码
                $new_data[$key][] = $payments[$val['pop_id']]['ppno'];

                $str = $payments[$val['pop_id']]['status'];
                $status_str = "";
                if (!empty($str)) {
                    $statusArr = explode(";", $str);
                    foreach ($statusArr as $kk => $vv) {
                        $status_str .= (static::$t->_(Enums::$loan_status[$vv]) ?? '') . ";";
                    }
                    $status_str = trim($status_str, ";");
                }

                //相关采购付款申请单状态
                $new_data[$key][] = $status_str;

                //相关采购付款申请单数量
                $new_data[$key][] = $payments[$val['pop_id']]['total'];

                //发票金额
                $new_data[$key][] = $payments[$val['pop_id']]['ticket_amount'];

                //相关采购付款申请单当前审批人
                $new_data[$key][] = $payments[$val['pop_id']]['auditor_id'];

                //相关采购付款申请单日期
                $new_data[$key][] = $payments[$val['pop_id']]['apply_date'];

                //发票金额总计
                $new_data[$key][] = $payments[$val['pop_id']]['receipt_amount'];

                //相关采购付款申请单日期
                $new_data[$key][] = $payments[$val['pop_id']]['real_pay_at'];

                //付款申请单是否支付
                $pay_str = $payments[$val['pop_id']]['pay_status'];

                $pay_status_str = "";
                if (!empty($pay_str)) {
                    $statusArr = explode(";", $pay_str);
                    foreach ($statusArr as $kk => $vv) {
                        $pay_status_str .= (static::$t->_(Enums::$loan_pay_status[$vv]) ?? '') . ";";
                    }

                    $pay_status_str = trim($pay_status_str, ";");
                }

                $new_data[$key][] = $pay_status_str;
            }
            // 申请状态
            $new_data[$key][] = static::$t->_(Enums::$loan_status[$val['status']]) ?? '';
            // 执行状态
            $new_data[$key][] = isset(PurchaseEnums::$order_execute_status_list[$val['execute_status']]) ? static::$t->_(PurchaseEnums::$order_execute_status_list[$val['execute_status']]) : '';
            // 关闭状态
            $new_data[$key][] = isset(PurchaseEnums::$is_close_list[$val['is_close']]) ? static::$t->_(PurchaseEnums::$is_close_list[$val['is_close']]) : '';
            // 审核通过时间
            $new_data[$key][] = $val['approve_at'] ?? '-';

            if ($val['budget_id'] && isset($budgets[$val['budget_id']])) {
                $new_data[$key][] = $budgets[$val['budget_id']]['name_' . strtolower(substr(self::$language, -2))];
            } else {
                $new_data[$key][] = '';
            }

            // 产品分类(一级分类) 导出去掉
//            if(!empty($nameKeyArr[$val['category_a']])){
//                $new_data[$key][] = static::$t->_($nameKeyArr[$val['category_a']]);
//            }else{
//                $new_data[$key][] = '';
//            }
//
            // 申请事项(二级分类) 导出去掉
//            if(!empty($nameKeyArr[$val['category_b']])){
//                $new_data[$key][] = static::$t->_($nameKeyArr[$val['category_b']]);
//            }else{
//                $new_data[$key][] = '';
//            }

            $new_data[$key][] = static::$t->_($val['product_name']);

            // 核算科目
            $new_data[$key][] = $val['account_type'] == KingDeeEnums::ACCOUNT_TYPE_FLASH_EXPRESS_COMPANY ? ($ledger_account_list[$val['ledger_account_id']] ?? '') : ($accounting_subjects_to_name[$val['ledger_account_id']]['subjects_name'] ?? '');;

            $new_data[$key][] = $val['cost_center_name']; // 费用所属中心
            $new_data[$key][] = $val['product_option_code'];
            $new_data[$key][] = $val['wrs_code'];// 物料编码
            $new_data[$key][] = $val['desc'];
            $new_data[$key][] = $val['product_option'];//规格型号
            $new_data[$key][] = $val['total'];
            $new_data[$key][] = static::$t->_($val['unit']);
            $new_data[$key][] = $val['metere_unit'];//计量单位

            $new_data[$key][] = bcdiv($val['price'], $this->digits, $this->digits_num);

            $new_data[$key][] = $val['no_tax_num'];// 不含税单价单位数量（SAP）
            $new_data[$key][] = bcdiv($val['no_tax_price'], 1000, 2);// 不含税单价（SAP）


            $new_data[$key][] = bcdiv($val['total_price'], 1000, 2);
            $new_data[$key][] = bcdiv($val['vat7'], 1000, 2);
            $new_data[$key][] = bcdiv($val['all_total_no_wht'], 1000, 2);

            // 无预扣税，wht 相关字段不可填，默认为空
            if (empty($val['is_tax'])) {
                $_tmp_wht_cate = '';
                $_tmp_wht_rate = '';
                $_tmp_wht_amount = '';
            } else {
                $_tmp_wht_setting = $wht_setting[$val['wht_cate']] ?? [];
                $_tmp_wht_cate = $_tmp_wht_setting['label'] ?? '';// WHT类别
                $_tmp_wht_rate = $_tmp_wht_setting['rate_list'][$val['wht_rate']]['label'] ?? '';// WHT税率
                $_tmp_wht_amount = is_null($val['wht_amount']) ? '' : bcdiv($val['wht_amount'], 1000, 2);// WHT金额
            }

            $new_data[$key][] = $_tmp_wht_cate;// WHT类别
            $new_data[$key][] = $_tmp_wht_rate;// WHT税率
            $new_data[$key][] = $_tmp_wht_amount;// WHT金额

            $new_data[$key][] = $val['remark'];

            $new_data[$key][] = bcdiv($val['subtotal_amount'], 1000, 2);//不含税金额总计
            $new_data[$key][] = bcdiv($val['taxation'], 1000, 2);//vat7总计

            $new_data[$key][] = bcdiv($val['wht_total_amount'], 1000, 2);// WHT金额总计

            $new_data[$key][] = bcdiv($val['o_amount'], 1000, 2);//含税金额总计

            $new_data[$key][] = bcdiv($val['total_amount'], 1000, 2);// 含税金额总计（含VAT含WHT）
            $new_data[$key][] = $val['delivery_date']; //计划交货日期
            $new_data[$key][] = $val['delivery_total']; //交付数量
            $new_data[$key][] = $val['storage_date']; //计划入库日期
            $new_data[$key][] = $val['dispatch_date']; //发货日期
            $new_data[$key][] = $val['dispatch_total']; //发货数量
            $new_data[$key][] = $val['sailing_date']; //开船日期
            $new_data[$key][] = $val['sailing_arrive_date']; //到港日期
            $new_data[$key][] = $val['clearance_date']; //清关日期
        }
        $file_name = "purchase_order_" . date("YmdHis");
        $header = [
            static::$t->_('global.number'),//编号
            static::$t->_('global.applicant.name'),   //申请人姓名
            static::$t->_('global.applicant.id'),//申请人工号
            static::$t->_('global.apply.date'),//申请日期

            static::$t->_('purchase_order_field_link_apply'),  //相关采购申请单
            static::$t->_('payment_store_renting_contract_no'),  //相关合同 √

            static::$t->_('purchase_order_field_cost_department'),  //费用部门
            static::$t->_('expense_company'),  //费用所属公司
            static::$t->_('purchase_order_field_cost_store'),  //费用网点
            static::$t->_('purchase_order_field_vendor'),  //供应商名称
            static::$t->_('purchase_order_field_vendor_email'),   //供应商邮箱
            static::$t->_('purchase_order_field_vendor_tax_number'),   //供应商税务号
            static::$t->_('purchase_order_field_bank_name'),   //供应商银行名称
            static::$t->_('purchase_order_field_bank_account_name'),   //供应商银行账户名称
            static::$t->_('purchase_order_field_bank_no'),   //供应商银行账号
            static::$t->_('purchase_order_field_swift_code'),   //swift code

            static::$t->_('purchase_order_field_loan_time'),   //信贷期限
            static::$t->_('purchase_order_payment_mode'),   //支付模式
            static::$t->_('payment_store_renting_pay_method'),   // 付款方式 √
            static::$t->_('purchase_apply_field_product_overseas_payment'),   // 境内境外支付 √
            static::$t->_('purchase_apply_field_product_sap_purchase_order_no'),   // SAP采购订单号码 √


            static::$t->_('purchase_order_field_payment_to'),   //支付给
            static::$t->_('purchase_order_field_delivery_addr'),   //送货地址
            static::$t->_('purchase_order_field_currency'),     //币种

            static::$t->_('purchase_order_field_link_payment_code'),  //相关采购付款单编码
            static::$t->_('purchase_order_field_link_payment_status'),//相关采购付款单状态
            static::$t->_('purchase_order_field_link_payment_num'),   //相关采购付款单产品数量
            static::$t->_('purchase_order_field_link_payment_ticket_amount'),   //相关采购付款单发票金额
            static::$t->_('purchase_order_field_link_payment_auditor_id'),    //相关采购付款单当前审批人
            static::$t->_('purchase_order_field_link_payment_apply_date'),    //相关采购付款单申请日期

            static::$t->_('purchase_order_field_link_payment_receipt_amount'),    //相关采购付款申请单发票金额总计
            static::$t->_('purchase_order_field_link_payment_real_pay_at'),    //相关采购付款申请单付款时间
            static::$t->_('purchase_order_field_link_payment_pay_status'),    //相关采购付款申请单是否支付

            static::$t->_('global.apply.status.text'), //申请状态
            static::$t->_('purchase_order_field_execute_status'),   //执行状态
            static::$t->_('purchase_order_field_is_close'),   //关闭状态
            static::$t->_('purchase_apply_approve_date'),   //审核通过时间

            static::$t->_('pruchase_budget_classification'),   //产品分类
//            static::$t->_('purchase_payment_invoice_head_3'),   //产品分类（一级分类）×
//            static::$t->_('purchase_payment_invoice_head_4'),   //申请事项（二级分类）×
            static::$t->_('purchase_payment_invoice_head_5'),   //产品名称

            static::$t->_('purchase_apply_ledger_account'),   // 核算科目 √
            static::$t->_('payment_store_renting_cost_center'),   // 费用所属中心 √

            static::$t->_('purchase_apply_field_product_option_code'), //产品编码
            static::$t->_('purchase_apply_field_product_wrs_code'), // 物料编码 √

            static::$t->_('purchase_apply_field_desc'),      //产品描述
            static::$t->_('purchase_apply_field_product_option'),     //规格型号
            static::$t->_('purchase_payment_invoice_head_6'),   //数量
            static::$t->_('purchase_payment_invoice_head_7'),   //单位
            static::$t->_('material_measuringunit'),   //计量单位

            static::$t->_('purchase_order_product_field_price'),        //不含税单价
            static::$t->_('purchase_apply_field_product_no_tax_num'),        // 不含税单价单位数量（SAP） √
            static::$t->_('purchase_apply_field_product_no_tax_price'),        // 不含税单价（SAP） √


            static::$t->_('purchase_order_product_field_total_price'),  //不含税金额
            static::$t->_('purchase_order_product_field_vat_rate_amount'),// VAT税额
            static::$t->_('purchase_order_product_field_with_vat_no_with_wht'),  //含税金额(含VAT不含WHT）

            static::$t->_('payment_store_renting_wht_category'),  //WHT类别 √
            static::$t->_('payment_store_renting_wht_category_tax'),  //WHT税率 √
            static::$t->_('payment_store_renting_wht_amount'),  //WHT金额 √

            static::$t->_('purchase_order_product_field_remark'),  //备注

            static::$t->_('purchase_order_field_subtotal_amount'),  //subtotal_amount不含税金额总计
            static::$t->_('purchase_order_field_taxation'),  //vat7总计

            static::$t->_('payment_store_renting_wht_total_amount'),  // WHT金额总计 √
            static::$t->_('purchase_order_field_with_vat_no_with_wht_total_amount'),  //含税金额总计（含VAT不含WHT）
            static::$t->_('pay_field_amount_total_have_tax'),  //含税金额总计（含VAT含WHT） √
            static::$t->_('storage_product_delivery_date'),  //计划交货日期
            static::$t->_('purchase_order_field_delivery_total'),  //交付数量
            static::$t->_('purchase_order_field_storage_date'),  //计划入库日期
            static::$t->_('purchase_order_field_dispatch_date'),  //发货日期
            static::$t->_('purchase_order_field_dispatch_total'),  //发货数量
            static::$t->_('purchase_order_field_sailing_date'),  //开船时间
            static::$t->_('purchase_order_field_sailing_arrive_date'),  //到港时间
            static::$t->_('purchase_order_field_clearance_date'),  //清关时间

        ];


        $excel_data = $this->exportExcel($header, $new_data, $file_name);
        if (!empty($excel_data['data']) && $excel_data['code'] == 1) {
            $return['code'] = ErrCode::$SUCCESS;
            $return['message'] = 'success';
            $return['data'] = $excel_data['data'];
        }

        $this->unLock(RedisKey::PURCHASE_EXPORT_LOCK . $uid);

        return $return;
    }

    /**
     * 取消采购申请单的引用
     * 说明: PO单的撤回、驳回操作会调用
     *
     * @param PurchaseOrder $order_model
     * @param int $workflow_action
     * @return mixed
     * @throws Exception
     */
    public function cancelRelation(PurchaseOrder $order_model, int $workflow_action)
    {
        $this->logger->info('采购订单撤回/驳回-PO单数据: ' . json_encode($order_model->toArray(), JSON_UNESCAPED_UNICODE) . "; 审批动作: {$workflow_action}");

        $products = PurchaseOrderProduct::find([
            'conditions' => 'poid = ?0',
            'bind' => [$order_model->id]
        ])->toArray();

        $flag = 0;
        $apply = PurchaseApply::findFirst([
            'id = ?0',
            'bind' => [$order_model->pa_id]
        ]);
        if (empty($apply)) {
            throw new Exception('采购订单撤回/驳回-PUR主数据为空: PUR ID = ' . $order_model->pa_id, ErrCode::$SYSTEM_ERROR);
        }

        // 采购申请单据是否是老数据
        $is_old_apply_data = $this->isOldData($apply->created_at);

        $count_all = count($products);
        foreach ($products as $product) {
            $apply_product = PurchaseApplyProduct::findFirst([
                'conditions' => 'id = ?0',
                'bind' => [$product['apply_product_id']]
            ]);
            $apply_product->order_total = max(bcsub($apply_product->order_total, $product['total']), 0);
            //已经订单数量为0，且不是老数据的时候
            if ($apply_product->order_total == 0 && !$is_old_apply_data) {
                $apply_product->is_can_update = 1;
                $flag++;
            }
            if ($apply->execute_status != PurchaseEnums::APPLY_EXECUTE_STATUS_HISTORY) {
                $old_order_use_total = $apply_product->order_use_total;
                $apply_product->order_use_total = max(bcsub($apply_product->order_use_total, $product['total']), 0);
                $this->logger->info('cancelRelation-update-order_use_total : 修改前order_use_total=' . $old_order_use_total . ';修改后order_use_total=' . $apply_product->order_use_total);
            }
            $update_bool = $apply_product->i_update();
            if ($update_bool === false) {
                throw new Exception('采购订单撤回/驳回-采购申请单产品已购数量失败, 待更新的PUR行数据: ' . json_encode($apply_product->toArray(), JSON_UNESCAPED_UNICODE) . '; 原因可能是: ' . get_data_object_error_msg($apply_product), ErrCode::$SYSTEM_ERROR);
            }
        }

        $updateData = ['is_cite' => PurchaseEnums::IS_CITE_NO];
        //不是老数据
        if ($flag && !$is_old_apply_data) {
            $updateData['is_can_update'] = PurchaseEnums::IS_CAN_UPDATE_YES;

            //如果都回去了，相当于没关联过采购订单
            if ($flag == $count_all) {
                $updateData['is_link_po'] = PurchaseEnums::IS_LINK_PO_NO;
            }
        }
        //15362处理执行状态和关闭状态 , 0为历史数据,不处理
        if ($apply->execute_status != PurchaseEnums::APPLY_EXECUTE_STATUS_HISTORY) {
            $all_apply_product = $apply->getProducts()->toArray();
            $all_order_use_total = 0;
            $all_total = 0;
            if (!empty($all_apply_product)) {
                foreach ($all_apply_product as $apply_product) {
                    $all_order_use_total += $apply_product['order_use_total'];
                    $all_total += $apply_product['total'];
                }
                if ($all_order_use_total <= 0) {
                    //未关联po
                    $updateData['execute_status'] = PurchaseEnums::APPLY_EXECUTE_STATUS_NO;
                    //如果是从"完全关联po"->"未关联po",得改成"未关闭"
                    if ($apply->execute_status == PurchaseEnums::APPLY_EXECUTE_STATUS_DONE) {
                        $updateData['is_close'] = PurchaseEnums::IS_CLOSE_NO;
                    }
                } elseif ($all_order_use_total < $all_total) {
                    $updateData['execute_status'] = PurchaseEnums::APPLY_EXECUTE_STATUS_PARTLY;
                    //如果是从"完全关联po"->"部分关联po",得改成"未关闭"
                    if ($apply->execute_status == PurchaseEnums::APPLY_EXECUTE_STATUS_DONE) {
                        $updateData['is_close'] = PurchaseEnums::IS_CLOSE_NO;
                    }
                } else {
                    $updateData['execute_status'] = PurchaseEnums::APPLY_EXECUTE_STATUS_DONE;
                    $updateData['is_close'] = PurchaseEnums::IS_CLOSE_YES;
                }
            }
        }
        if ($apply->i_update($updateData) === false) {
            throw new Exception('采购订单撤回/驳回-更新PO单关联的PUR单主数据失败, 待更新的PUR行数据: ' . json_encode($updateData, JSON_UNESCAPED_UNICODE) . '; 原因可能是: ' . get_data_object_error_msg($apply), ErrCode::$SYSTEM_ERROR);
        }

        return true;
    }

    /**
     * 订单数据相关导出
     *
     * @param $condition
     * @param $user
     * @param $type
     * @return array
     */
    public function getDownloadList($condition, $user = [], $type = 0)
    {
        $condition['uid'] = $user['id'] ?? 0;
        $code = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $data = [];

        try {
            $builder = $this->modelsManager->createBuilder();

            $column_str = 'o.id,
            o.pono,
            o.create_name,
            o.create_id,
            o.apply_date,
            o.amount,
            o.currency,
            o.status,
            o.subtotal_amount,
            o.taxation,
            o.amount as o_amount,
            o.pano,
            o.cno,
            o.vendor,
            o.vendor_addr,
            o.vendor_contact,
            o.vendor_phone,
            o.vendor_email,
            o.vendor_tax_number,
            o.bank_name,
            o.bank_account_name,
            o.bank_no,
            o.swift_code,
            o.payment_to,
            o.payment_method,
            o.pay_where,
            o.loan_time,
            o.payment_mode,
            o.delivery_addr,
            o.cost_department_name,
            o.cost_store_name,
            o.cost_company_name,
            o.approve_at,
            o.sap_order_no,
            o.wht_total_amount,
            o.total_amount,
            o.is_tax,
            o.account_type,
            p.budget_id,
            p.ledger_account_id,
            p.cost_center_name,
            p.category_a,
            p.category_b,
            p.product_name,
            p.product_id,
            p.product_option,
            p.product_option_code,
            p.metere_unit,
            p.wrs_code,
            p.finance_code,
            p.no_tax_num,
            p.no_tax_price,
            p.total,
            p.unit,
            p.price,
            p.total_price,
            p.remark,
            p.vat7,
            p.wht_cate,
            p.wht_rate,
            p.wht_amount,
            p.all_total_no_wht,
            p.id as pop_id,
            p.cost_store_name AS item_cost_store_name,
            p.[desc],
            p.delivery_date,
            GROUP_CONCAT(COALESCE(ppd.dispatch_date,"null")) as dispatch_date,
            GROUP_CONCAT(COALESCE(ppd.dispatch_total,"null")) as dispatch_total,
            GROUP_CONCAT(COALESCE(ppd.sailing_date,"null")) as sailing_date,
            GROUP_CONCAT(COALESCE(ppd.sailing_arrive_date,"null")) as sailing_arrive_date,
            GROUP_CONCAT(COALESCE(ppd.clearance_date,"null")) as clearance_date,
            p.delivery_total,
            o.is_close,
            o.execute_status,
            GROUP_CONCAT(COALESCE(ppd.storage_date,"null")) as storage_date
            ';
            $builder->columns($column_str);
            $builder->from(['p' => PurchaseOrderProduct::class]);
            $builder->leftJoin(PurchaseOrder::class, "p.poid=o.id", "o");
            $builder->leftJoin(PurchaseOrderProductDeliveryModel::class, 'p.id = ppd.product_id', 'ppd');
            $builder = $this->getListCondition($builder, $condition, $user, $type);
            $builder->orderBy('o.id desc');
            $builder->groupBy('p.id');
            $items = $builder->getQuery()->execute()->toArray();
            $items = $this->handleListItems($items, $condition['uid'], true);
            $data = [
                'items' => $items,
            ];
        } catch (\Exception $e) {
            $code = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        }

        if (!empty($real_message)) {
            $logger = $this->getDI()->get('logger');
            $logger->warning('purchase-apply-download-list-failed:' . $real_message);
        }

        return [
            'code' => $code,
            'message' => $message,
            'data' => $data
        ];
    }

    public function getPaymentsProducts($pop_ids)
    {
        $code = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $data = [];


        try {

            $sql = "SELECT
group_concat(c.ppno SEPARATOR ';') as ppno,
group_concat(c.status SEPARATOR ';') as status,
group_concat(ppr.total SEPARATOR ';') as total,
group_concat(w.current_node_auditor_id SEPARATOR ';') as auditor_id,
group_concat(c.apply_date SEPARATOR ';') as apply_date,
group_concat(c.real_pay_at SEPARATOR ';') as real_pay_at,
group_concat(ppr.ticket_amount/1000 SEPARATOR ';') as ticket_amount,
group_concat(c.receipt_amount/1000 SEPARATOR ';') as receipt_amount,
group_concat(c.pay_status SEPARATOR ';') as pay_status,
ppr.pop_id
FROM
	purchase_payment_receipt AS ppr
	LEFT JOIN purchase_payment AS c ON c.id = ppr.ppid
	LEFT JOIN workflow_request AS w ON w.biz_value = c.id 
	and w.biz_type = 11
WHERE
	ppr.pop_id IN (" . implode(",", $pop_ids) . ")
	GROUP BY ppr.pop_id";

            $items = $this->db_oa->fetchAll($sql, \Phalcon\Db::FETCH_ASSOC);
            if (!empty($items)) {
                $data = array_column($items, null, "pop_id");
            }
        } catch (\Exception $e) {
            $code = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        }

        if (!empty($real_message)) {
            $logger = $this->getDI()->get('logger');
            $logger->warning('purchase-order-payment-product-list-failed:' . $real_message);
        }

        return [
            'code' => $code,
            'message' => $message,
            'data' => $data
        ];
    }


    /**
     * 调整采购订单数量
     * @param $data
     * @param $user
     * @param $type
     * @return mixed
     */
    public function updateTotal($data, $user, $type = 0)
    {
        $code = ErrCode::$SUCCESS;
        $message = 'success';
        $real_message = '';
        $result = [];

        // 是否需要释放预算标识: 0-否；1-是
        $is_free_budget_flag = isset($data['is_free_budget']) && $data['is_free_budget'] == 1;
        $this->logger->info("po_update_free_budget: [is_free_budget_flag={$is_free_budget_flag}]");

        foreach ($data['product'] as $k => $v) {
            $data['product'][$k] = array_only($v, array_keys(self::$validate_update_product_param));
        }

        $newProducts = array_column($data['product'], null, 'id');

        $db = $this->getDI()->get('db_oa');
        $db->begin();
        try {
            // 当前用户是否是特权用户: 来自数据查询模块的需校验
            $is_privilege_staff_id = EnumsService::getInstance()->isPrivilegeStaffId($user['id']);
            if ($type == self::LIST_TYPE_DATA && !$is_privilege_staff_id) {
                throw new ValidationException(static::$t->_('no_access_privilege'), ErrCode::$VALIDATE_ERROR);
            }

            $item = PurchaseOrder::findFirst([
                'conditions' => 'id = :id: AND status = :status: AND is_can_update = :is_can_update:',
                'bind' => [
                    'id' => $data['id'],
                    'status' => Enums::WF_STATE_APPROVED,
                    'is_can_update' => PurchaseEnums::IS_CAN_UPDATE_YES
                ],
                'for_updated' => true,
            ]);

            if (empty($item)) {
                throw new ValidationException(static::$t->_('purchase_order_data_error'), ErrCode::$VALIDATE_ERROR);
            }

            // PO单数据属主验证: 来自我的申请模块
            if ($type == self::LIST_TYPE_APPLY && $item->create_id != $user['id']) {
                throw new ValidationException(static::$t->_('purchase_order_owner_error'), ErrCode::$VALIDATE_ERROR);
            }

            $this->logger->info('采购订单修改[更新前数据]: ' . json_encode($item->toArray(), JSON_UNESCAPED_UNICODE));

            $apply = PurchaseApply::findFirst([
                'conditions' => 'id = :id:',
                'bind' => ['id' => $item->pa_id]
            ]);
            if (empty($apply)) {
                throw new ValidationException(static::$t->_('purchase_order_linked_pur_data_null', ['pur_id' => $item->pa_id]), ErrCode::$VALIDATE_ERROR);
            }

            // PO单原产品信息
            $products = $item->getProducts();

            // PUR单原产品信息: PO单产品预算释放，要用到PUR单中对应产品的不含税单价数据
            $pur_products_item = $apply->getProducts();
            $pur_products_item = !empty($pur_products_item) ? array_column($pur_products_item->toArray(), null, 'id') : [];

            //用来算总金额, 需要本次入表的PO单产品数据
            $newData = ['amount' => 0, 'taxation' => 0, 'subtotal_amount' => 0, 'product' => []];

            // 待同步PUR单的产品列表
            $waiting_sync_pur_products = [];

            $is_update = 0;
            foreach ($products as $product) {
                $tmp = [];
                $tmp['item'] = $product;
                $tmp['total'] = $product->total;
                $tmp['price'] = bcdiv($product->price, $this->digits, $this->digits_num);
                $tmp['vat7_rate'] = '0';
                $tmp['is_update_flag'] = 0;
                $tmp['wht_rate'] = $product->wht_rate;
                $tmp['vat7'] = $newProducts[$product->id]['vat7'] ? $newProducts[$product->id]['vat7'] : 0;
                $tmp['wht_amount'] = $newProducts[$product->id]['wht_amount'] ? $newProducts[$product->id]['wht_amount'] : 0;
                $tmp['delivery_total'] = $product->delivery_total;

                // PO单是否有预扣税
                $newData['is_tax'] = $item->is_tax;

                if (!empty($product->total_price)) {
                    $tmp['vat7_rate'] = '' . round($product->vat7 / $product->total_price, 2) * 100;
                }

                //不能修改，直接跳过,或者本次没有传跳过
                if ($product->is_can_update == 0 || !isset($newProducts[$product->id])) {
                    $newData['product'][] = $tmp;
                    continue;
                }

                //如果数量相等，则不修改
                if ($product->total == $newProducts[$product->id]['total']) {
                    $newData['product'][] = $tmp;
                    continue;
                }

                //不能调大
                if ($product->total < $newProducts[$product->id]['total']) {
                    throw new ValidationException(static::$t->_('purchase_product_total_adjustment_error',
                        [
                            'new_total' => $newProducts[$product->id]['total'],
                            'old_total' => $product->total,
                            'item_id' => $product->id
                        ]
                    ), ErrCode::$VALIDATE_ERROR);
                }

                $is_update = 1;

                // PO单产品减少的数量: 变更前数量 - 变更后数量
                $tmp['sub_total'] = $tmp['total'] - $newProducts[$product->id]['total'];
                $tmp['total'] = $newProducts[$product->id]['total'];
                $tmp['remark'] = $newProducts[$product->id]['remark'];
                $tmp['is_update_flag'] = 1;

                // 需要释放预算的PO单产品
                $pur_products_info = $pur_products_item[$product->apply_product_id] ?? [];
                $tmp['is_free_budget'] = 'N';
                if ($is_free_budget_flag) {
                    $tmp['is_free_budget'] = 'Y';

                    $pur_products_total = $pur_products_info['total'] ?? 0;
                    $waiting_sync_pur_products_new_total = $pur_products_total - $tmp['sub_total'];
                    $waiting_sync_pur_products[] = [
                        'id' => $product->apply_product_id,
                        'total' => $waiting_sync_pur_products_new_total > 0 ? $waiting_sync_pur_products_new_total : 0,
                        'remark' => $pur_products_info['remark'] ?? '',
                    ];
                }

                $newData['product'][] = $tmp;
            }

            $this->logger->info('po_update_free_budget: 待同步pur的产品数据: ' . json_encode($waiting_sync_pur_products, JSON_UNESCAPED_UNICODE));

            if (empty($is_update)) {
                throw new ValidationException(static::$t->_('purchase_no_update_product'), ErrCode::$VALIDATE_ERROR);
            }

            $this->handleProductsData($newData);
            $this->logger->info('采购订单修改[处理后的数据]: ' . json_encode($newData, JSON_UNESCAPED_UNICODE));

            // 计算PO单需释放的预算金额
            // 更新PO单产品数据
            $total = 0; // 订单行采购数量合计
            $delivery_total_sum = 0;// 订单行的入库/交付数量合计

            foreach ($newData['product'] as $product) {
                // item PO单产品原数据
                $detail = $product['item'];

                $old = $detail->toArray();
                if (!empty($product['is_update_flag'])) {
                    $detail->total = $product['total'];
                    $detail->total_price = $product['total_price'];
                    $detail->all_total = $product['all_total'];
                    $detail->remark = $product['remark'];
                    $detail->vat7 = $product['vat7'];
                    $detail->wht_amount = $product['wht_amount'];
                    $detail->all_total_no_wht = $product['all_total_no_wht'];

                    //数量为0，这条数据不能改。
                    if (empty($product['total'])) {
                        $detail->is_can_update = PurchaseEnums::IS_CAN_UPDATE_NO;
                    }

                    if ($detail->save() === false) {
                        throw new BusinessException('采购订单修改[采购产品信息更新失败]: 待处理数据: ' . json_encode($detail->toArray(), JSON_UNESCAPED_UNICODE) . '; 可能存在的问题: ' . get_data_object_error_msg($detail), ErrCode::$BUSINESS_ERROR);
                    }

                    // PUR单产品更新
                    $apply_product = PurchaseApplyProduct::findFirst([
                        'conditions' => 'id = :id:',
                        'bind' => ['id' => $detail->apply_product_id]
                    ]);
                    if (empty($apply_product)) {
                        throw new BusinessException('采购订单修改, not found the pur_p=' . $detail->apply_product_id, ErrCode::$BUSINESS_ERROR);
                    }

                    $apply_product->order_total -= $product['sub_total'];
                    //15362维护po使用数量order_use_total, execute_status=0是历史数据不维护
                    if ($apply->execute_status != PurchaseEnums::APPLY_EXECUTE_STATUS_HISTORY) {
                        $old_order_use_total = $apply_product->order_use_total;
                        $apply_product->order_use_total -= $product['sub_total'];
                        $this->logger->info('cancelRelation-update-order_use_total : pap_id= ' . $detail->apply_product_id . ';修改前order_use_total=' . $old_order_use_total . ';修改后order_use_total=' . $apply_product->order_use_total);
                    }
                    if ($apply_product->save() === false) {
                        throw new BusinessException('采购订单修改[采购申请单产品信息更新失败]: 待处理数据: ' . json_encode($apply_product->toArray(), JSON_UNESCAPED_UNICODE) . '; 可能存在的问题: ' . get_data_object_error_msg($apply_product), ErrCode::$BUSINESS_ERROR);
                    }

                    // PO单修改, 需记入日志
                    $after_detail_data = $detail->toArray();
                    $after_detail_data['is_free_budget'] = $product['is_free_budget'];
                    $after_detail_data['po_product_sub_count'] = $product['sub_total'];

                    // 添加PO单产品修改记录
                    $this->saveUpdateTotalLog(Enums::WF_PURCHASE_ORDER, $item->id, $item->pono, $old, $after_detail_data, $user);
                }

                $total += $product['total'];
                $delivery_total_sum += $product['delivery_total'];
            }

            $item->amount = $newData['amount'];
            $item->total_amount = $newData['total_amount'];
            $item->subtotal_amount = $newData['subtotal_amount'];
            $item->taxation = $newData['taxation'];
            $item->wht_total_amount = $newData['wht_total_amount'];
            $item->updated_at = date('Y-m-d H:i:s');
            if (empty($total)) {
                $item->is_can_update = PurchaseEnums::IS_CAN_UPDATE_NO;
            }

            // 新增维护PO关闭状态的逻辑: v17472 老数据无需处理, 即只处理 execute_status 非0状态的PO单
            // 1. 获取PO行数量合计 $total = SUM(total)
            // 2. 获取PO行的入库数量合计 $delivery_total_sum = SUM(delivery_total)
            // 3. 订单行数量均为0 或 订单行数量合计值 <= 交付数量合计值的情况, 订单的完成状态置为: 已关闭
            if ($apply->execute_status != PurchaseEnums::APPLY_EXECUTE_STATUS_HISTORY && ($total == 0 || $total <= $delivery_total_sum)) {
                $item->is_close = PurchaseEnums::IS_CLOSE_YES;
            }

            $this->logger->info('采购订单修改[更新后数据]: ' . json_encode($item->toArray(), JSON_UNESCAPED_UNICODE));
            if ($item->save() === false) {
                throw new BusinessException('采购订单修改[采购订单表更新失败], 可能存在的问题: ' . get_data_object_error_msg($item), ErrCode::$BUSINESS_ERROR);
            }

            // 同步PUR单产品数量
            if ($is_free_budget_flag && !empty($waiting_sync_pur_products)) {
                $sync_apply_update = [
                    'id' => $apply->id,
                    'products' => $waiting_sync_pur_products
                ];

                $this->logger->info('po_update_free_budget: 待同步pur的数据: ' . json_encode($sync_apply_update, JSON_UNESCAPED_UNICODE));
                $pur_sync_res = ApplyService::getInstance()->updateTotalByOrderModify($item->pono, $sync_apply_update, $user);

                $this->logger->info('po_update_free_budget: 同步pur的结果: ' . json_encode($pur_sync_res, JSON_UNESCAPED_UNICODE));
                if ($pur_sync_res['code'] != ErrCode::$SUCCESS) {
                    throw new BusinessException('[po->pur] sync result: ' . $pur_sync_res['message'], ErrCode::$BUSINESS_ERROR);
                }
            }

            // 关联的采购申请单相关状态字段维护
            $is_update_apply = false;
            if ($apply->is_cite == PurchaseEnums::IS_CITE_YES) {
                $apply->is_cite = PurchaseEnums::IS_CITE_NO;
                $is_update_apply = true;
            }

            //判断执行状态和关闭状态,execute_status=0是历史数据,不处理
            if ($apply->execute_status != PurchaseEnums::APPLY_EXECUTE_STATUS_HISTORY) {
                //查询调整后的pur产品总数量和po已使用总数量
                $all_apply_product = PurchaseApplyProduct::find([
                    'columns' => 'id, is_can_update, total, order_total, order_use_total',
                    'conditions' => 'paid = :id:',
                    'bind' => ['id' => $apply->id]
                ]);

                $all_total = 0; //所有行总产品数量
                $all_order_use_total = 0; //所有行总"po已使用数量" , 这个数量只在采购订单维护
                foreach ($all_apply_product as $apply_product) {
                    //所有行总产品数量
                    $all_total += $apply_product->total;
                    //所有行总"po已使用数量"
                    $all_order_use_total += $apply_product->order_use_total;
                }
                //判断执行状态
                if ($all_total > 0) {
                    if ($all_order_use_total <= 0) {
                        //未生成po
                        //如果由"完全生成po"变为"未生成po", 关闭状态要改成"未关闭"
                        if ($apply->execute_status == PurchaseEnums::APPLY_EXECUTE_STATUS_DONE) {
                            $apply->is_close = PurchaseEnums::IS_CLOSE_NO;
                        }
                        //未生成po
                        $apply->execute_status = PurchaseEnums::APPLY_EXECUTE_STATUS_NO;
                    } elseif ($all_order_use_total >= $all_total) {
                        //完全生成po(理论上调减po不会把pur变成完全生成)
                        $apply->execute_status = PurchaseEnums::APPLY_EXECUTE_STATUS_DONE;
                        $apply->is_close = PurchaseEnums::IS_CLOSE_YES;
                    } else {
                        //部分生成po
                        //如果由"完全生成po"变为"部分生成po", 关闭状态要改成"未关闭"
                        if ($apply->execute_status == PurchaseEnums::APPLY_EXECUTE_STATUS_DONE) {
                            $apply->is_close = PurchaseEnums::IS_CLOSE_NO;
                        }
                        //部分生成po
                        $apply->execute_status = PurchaseEnums::APPLY_EXECUTE_STATUS_PARTLY;
                    }
                    $is_update_apply = true;
                }
                $this->logger->info('采购订单调减更新采购申请单状态 : purchase-apply-id=' . $apply->id . ';all_apply_product=' . json_encode($all_apply_product, JSON_UNESCAPED_UNICODE) . ';after_apply=' . json_encode($apply->toArray(), JSON_UNESCAPED_UNICODE));

            }
            if ($is_update_apply === true) {
                $apply->updated_at = date('Y-m-d H:i:s');
                if ($apply->save() === false) {
                    throw new BusinessException('采购订单修改[采购申请表更新失败]: 待处理数据: ' . json_encode($apply->toArray(), JSON_UNESCAPED_UNICODE) . '; 可能存在的问题: ' . get_data_object_error_msg($apply), ErrCode::$BUSINESS_ERROR);
                }
            }
            $purchase_storage = PurchaseStorage::findFirst([
                'conditions' => 'po_id = :po_id: and status in ({status:array})',
                'bind'       => ['po_id' => $data['id'], 'status' => [PurchaseEnums::PURCHASE_STORAGE_STATUS_UN_SCM_DONE,PurchaseEnums::PURCHASE_STORAGE_STATUS_IN_STORAGE]],
            ]);
            if (!empty($purchase_storage)) {
                $is_seed_kingdee = StorageService::getInstance()->storageIsSeedKingDee($purchase_storage->toArray());
                if($is_seed_kingdee){
                    $sql = 'update purchase_order set is_seed_kingdee=' . Enums\KingDeeEnums::IS_SEED_KINGDEE . ' where id ='.$data['id'];
                    $bool = $this->getDI()->get('db_oa')->execute($sql);
                    if ($bool === false) {
                        throw new BusinessException('purchase_order更新失败==' . $sql, ErrCode::$VALIDATE_ERROR);
                    }
                }
            }

            $db->commit();
        } catch (ValidationException $e) {
            $db->rollback();

            $code = $e->getCode();
            $message = $e->getMessage();
        } catch (BusinessException $e) {
            $db->rollback();

            $code = ErrCode::$BUSINESS_ERROR;
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        } catch (\Exception $e) {
            $db->rollback();

            $code = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        }

        if (!empty($real_message)) {
            $this->logger->error('purchase-order-updateTotal-failed:' . $real_message);
        }

        return [
            'code' => $code,
            'message' => $message,
            'data' => $result
        ];
    }

    /**
     * 调整采购订单计划交货日期
     * @param $data
     * @param $user
     * @return mixed
     */
    public function updateDeliveryDate($data, $user)
    {
        $code = ErrCode::$SUCCESS;
        $message = 'success';
        $real_message = '';

        $db = $this->getDI()->get('db_oa');
        $db->begin();
        try {

            //1、创建日期在2022.8.1之后的订单
            //2、采购订单状态为“待审核”或“已通过”
            //3、是否所有产品关联过付款申请单 is_cite = 0
            $item = PurchaseOrder::findFirst(
                [
                    'conditions' => 'id = :id: and create_id =:create_id: and status in ({status:array}) and created_at >= :created_at: and is_cite = :is_cite:',
                    'bind' => [
                        'id' => $data['id'],
                        'create_id' => $user['id'],
                        'status' => [Enums::CONTRACT_STATUS_PENDING, Enums::CONTRACT_STATUS_APPROVAL],
                        'created_at' => PurchaseEnums::CAN_EDIT_DELIVERY_DATE,
                        'is_cite' => PurchaseEnums::IS_CITE_NO
                    ],
                ]
            );

            if (empty($item)) {
                throw new ValidationException(static::$t->_("data_empty_or_read_data_failed"), ErrCode::$VALIDATE_ERROR);
            }

            $this->logger->info('采购订单计划交货日期修改[更新前数据]: ' . json_encode($item->toArray(), JSON_UNESCAPED_UNICODE));
            //新产品信息
            $newProducts = array_column($data['product'], null, 'id');
            // PO单原产品信息
            $products = $item->getProducts();
            foreach ($products as $product) {
                if (!isset($newProducts[$product->id])) {
                    continue;
                }
                if (isset($newProducts[$product->id]['dispatch_total']) && $newProducts[$product->id]['dispatch_total'] > $product->total) {
                    throw new ValidationException(static::$t->_('purchase_order_dispatch_total_error'), ErrCode::$VALIDATE_ERROR);
                }
                $product->delivery_date = $newProducts[$product->id]['delivery_date'];
                $product->dispatch_date = $newProducts[$product->id]['dispatch_date'] ?? null;
                $product->dispatch_total = $newProducts[$product->id]['dispatch_total'] ?? 0;
                $product->sailing_date = $newProducts[$product->id]['sailing_date'] ?? null;
                $product->sailing_arrive_date = $newProducts[$product->id]['sailing_arrive_date'] ?? null;
                $product->clearance_date = $newProducts[$product->id]['clearance_date'] ?? null;
                if ($product->save() === false) {
                    throw new BusinessException('采购订单修改[更新计划交货日期失败]: 待处理数据: ' . json_encode($product->toArray(),
                            JSON_UNESCAPED_UNICODE) . '; 可能存在的问题: ' . get_data_object_error_msg($product),
                        ErrCode::$BUSINESS_ERROR);
                }
            }
            $db->commit();
        } catch (ValidationException $e) {
            $db->rollback();
            $code = $e->getCode();
            $message = $e->getMessage();
        } catch (BusinessException $e) {
            $db->rollback();
            $code = ErrCode::$BUSINESS_ERROR;
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        } catch (\Exception $e) {
            $db->rollback();
            $code = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        }

        if (!empty($real_message)) {
            $this->logger->error('purchase-order-update_delivery_date-failed:' . $real_message);
        }

        return [
            'code' => $code,
            'message' => $message,
            'data' => [],
        ];
    }


    /**
     * 获取修改数量日志
     * @param $id
     * @param $user
     * @return array
     */
    public function getUpdateLog($id, $user)
    {
        $code = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $data = [];

        try {
            $item = PurchaseOrder::findFirst(
                [
                    'conditions' => 'id = :id:',
                    'bind' => ['id' => $id]
                ]
            );

            if (empty($item)) {
                throw new ValidationException('not found the po');
            }

            $data = $this->getUpdateTotalLog(Enums::WF_PURCHASE_ORDER, $id);


        } catch (ValidationException $e) {

            $code = $e->getCode();
            $message = $e->getMessage();

        } catch (\Exception $e) {
            $code = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        }

        if (!empty($real_message)) {
            $this->logger->warning("purchase-order-getUpdateTotalLog-failed:" . $real_message);
        }

        return [
            'code' => $code,
            'message' => $message,
            'data' => $data
        ];
    }

    /**
     * 获取采购订单产品名称列表
     * @param $product_name
     * @return array
     */
    public function productNameList($product_name)
    {
        $code = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $data = [];

        $itemList = PurchaseOrderProduct::find(
            [
                'conditions' => "product_name like :product_name:",
                'column' => 'product_name',
                'group' => 'product_name',
                'bind' => ['product_name' => '%' . $product_name . '%']
            ]
        )->toArray();

        foreach ($itemList as $key => $item) {
            $data[] = [
                'id' => $key + 1,
                'name' => $item['product_name']
            ];
        }

        return [
            'code' => $code,
            'message' => $message,
            'data' => $data
        ];
    }

    /**
     * 采购类型枚举值
     * */
    public function getPurchaseType()
    {
//        return [];//废弃不用了
        $lang = $this->getLang();
        //$res = EnvModel::getEnvByCode('purchase_order_type');
        //$res = json_decode($res,true);
        //数据源和get_purchase_type接口保持一致
        $list = PurchaseType::find(['columns' => 'purchase_type as id,name_key']);
        $res = [];
        if (!empty($list)) {
            $res = $list->toArray();
        }
        foreach ($res as $key => &$value) {
            $value['name_key'] = self::getTranslation($lang)->t($value['name_key']);
        }

        return $res;
    }

    /**
     * 单位枚举值
     *
     * @param $env_key
     * @return array|mixed
     */
    public function getEnvByCode($env_key)
    {
        if (empty($env_key)) {
            return [];
        }
        $res = EnvModel::getEnvByCode($env_key);
        $res = json_decode($res, true);
        return $res;
    }


    /**
     * 产品编号枚举值
     * */
    public function getPurchaseProductList()
    {
        $product_list = PurchaseProductList::Find(['columns' => 'material_id,description,base_uom,product_cate', 'conditions' => 'is_del = 0'])->toArray();
        if (empty($product_list)) {
            return [];
        }

        return $product_list;
    }

    /**
     * 采购单枚举值
     * */

    public function getPurchaseParams()
    {
        $code = ErrCode::$SUCCESS;
        $message = $real_message = '';

        $data = [
            'unit'                             => $this->getEnvByCode('purchase_order_unit'),
            'load_time'                        => $this->getEnvByCode('purchase_order_loan_time'),
            'purchase_type'                    => $this->getPurchaseType('purchase_order_type'),
            'purchase_order_place'             => $this->getEnvByCode('purchase_order_place'),
            'cost_company'                     => $this->getCooCostCompany(),
            'pay_where_list'                   => [
                ['id' => (string)Enums\PayEnums::PAY_WHERE_IN, 'name' => static::$t->_('pay_where.1')],
                ['id' => (string)Enums\PayEnums::PAY_WHERE_OUT, 'name' => static::$t->_('pay_where.2')],
            ],
            'deductible_vat_tax'               => EnumsService::getInstance()->getFormatDeductibleVatTaxRateConfig(),
            'wrs_code_ledger_account'          => $this->wrsCodeAccountRelation(),
            'purchase_order_payment_mode'      => $this->getPurchaseOrderPaymentMode(),
            'sap_company_ids'                  => EnumsService::getInstance()->getSettingEnvValueIds('purchase_sap_company_ids'),
            'kingdee_company_ids'              => EnumsService::getInstance()->getSettingEnvValueIds(Enums\KingDeeEnums::KINGDEE_BU_SETTING_CODE),
            'not_display_clearance_order_type' => EnumsService::getInstance()->getSettingEnvValueIds('not_display_clearance_order_type'),
        ];

        $pay_method_enum = PurchaseEnums::$payment_pay_method_list;
        foreach ($pay_method_enum as $key => $value) {
            $data['pay_method'][] = [
                'value' => $key,
                'label' => static::$t->_($value)
            ];
        }
        return [
            'code' => $code,
            'message' => $message,
            'data' => $data
        ];
    }

    private function getLang()
    {
        $lang = self::$language;
        if (empty($lang) || !in_array($lang, ["th", "en", "zh-CN"], 1)) {
            $lang = "th";
        }
        return $lang;
    }

    /**
     * 创建库存类订单到SAP
     * @param array $request_data 采购订单信息组
     * @return array
     */
    public function createSapOrder($request_data)
    {
        if ($request_data['purchase_type'] == Enums\PurchaseEnums::PURCHASE_TYPE_STOCK) {
            //库存类
            $postData = $this->getStockPostDataToSap($request_data);
        } else if ($request_data['purchase_type'] == Enums\PurchaseEnums::PURCHASE_TYPE_COST) {
            //辅助类
            $postData = $this->getHelperPostDataToSap($request_data);
        }

        $xml = SapsService::getInstance()->httpRequestXml($method = '/sap/managepurchaseorderin', $postData);
        $this->logger->info('sap_purchase_order:' . $request_data['pono'] . '=========' . $postData . '=========' . $xml);

        preg_match_all("/\<PurchaseOrder\>(.*?)\<\/PurchaseOrder\>/s", $xml, $purChaseOrders);
        preg_match_all("/\<Log\>(.*?)\<\/Log\>/s", $xml, $log);

        foreach ($purChaseOrders[1] as $k => $purChaseOrder) {
            preg_match_all("/\<ChangeStateID\>(.*?)\<\/ChangeStateID\>/", $purChaseOrder, $ChangeStateID);
            preg_match_all("/\<BusinessTransactionDocumentID\>(.*?)\<\/BusinessTransactionDocumentID\>/", $purChaseOrder, $BusinessTransactionDocumentID);
            preg_match_all("/\<UUID\>(.*?)\<\/UUID\>/", $purChaseOrder, $UUID);

        }
        $data = [];
        if (isset($BusinessTransactionDocumentID[1][0])) {
            $data = [
                'change_state_id' => trim($ChangeStateID[1][0]) ?? '',
                'business_transaction_id' => trim($BusinessTransactionDocumentID[1][0]) ?? '',
                'uuid' => trim($UUID[1][0]) ?? '',
                'log' => json_encode(simplexml_load_string($log[0][0] ?? '', 'SimpleXMLElement', LIBXML_NOCDATA)) ?? '',

            ];
        }


        return $data;
    }

    /**
     * 通过apply_product_ids获取采购订单信息
     * @param $apply_product_ids
     * @return array
     */
    public function getPurchaseOrderByProducts($apply_product_ids)
    {
        $builder = $this->modelsManager->createBuilder();
        $column_str = 'po.currency,pop.apply_product_id,pop.all_total_no_wht,pop.all_total';
        $builder->columns($column_str);
        $builder->from(['pop' => PurchaseOrderProduct::class]);
        $builder->leftJoin(PurchaseOrder::class, "pop.poid=po.id", "po");
        $builder->where('pop.apply_product_id in ({apply_product_id:array})', ['apply_product_id' => $apply_product_ids]);
        $builder->andWhere('po.status not in ({status:array})', ['status' => [Enums::WF_STATE_REJECTED, Enums::WF_STATE_CANCEL]]);
        $purchase_order_list = $builder->getQuery()->execute()->toArray();
        $data = [];
        foreach ($purchase_order_list as $pok => $pov) {
            $data[$pov['apply_product_id']][] = $pov;
        }
        return $data;
    }

    /**
     * 获取采购合同列表
     * @param array $params
     * @return mixed
     */
    public function getPurchaseContractList(array $params)
    {
        $code = ErrCode::$SUCCESS;
        $message = 'success';
        $real_message = '';

        // 分页
        $page_size = empty($params['pageSize']) ? GlobalEnums::DEFAULT_PAGE_SIZE : (int)$params['pageSize'];
        $page_num = empty($params['pageNum']) ? GlobalEnums::DEFAULT_PAGE_NUM : (int)$params['pageNum'];
        $offset = $page_size * ($page_num - 1);

        $data = [
            'items' => [],
            'pagination' => [
                'current_page' => $page_num,
                'per_page' => $page_size,
                'total_count' => 0,
            ]
        ];

        try {
            // 采购合同的类别
            $purchase_contract_categorys = ContractCategoryRepository::getInstance()->getSubCategoryIdsByAncestryId(Enums::CONTRACT_TEMPLATE_PURCHASE);

            $builder = $this->modelsManager->createBuilder();
            $builder->from(['c' => Contract::class]);
            $builder->inWhere('c.template_id', $purchase_contract_categorys);
            $builder->andWhere('c.cno LIKE :cno:', ['cno' => "{$params['cno']}%"]);

            $count_info = $builder->columns('COUNT(c.id) as t_count')->getQuery()->getSingleResult();
            $count = $count_info->t_count ?? 0;

            $items = [];
            if ($count) {
                $builder->columns('c.id, c.cno, c.cname AS name');
                $builder->limit($page_size, $offset);
                $items = $builder->getQuery()->execute()->toArray();
            }

            $data = [
                'items' => $items,
                'pagination' => [
                    'current_page' => $page_num,
                    'per_page' => $page_size,
                    'total_count' => (int)$count,
                ]
            ];

        } catch (\Exception $e) {
            $code = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        }

        if (!empty($real_message)) {
            $this->logger->error('get_purchase_contract_list_failed:' . $real_message);
        }

        return [
            'code' => $code,
            'message' => $message,
            'data' => $data
        ];
    }

    /**
     * 组装库存类订单到SAP请求报文信息
     * @param array $request_data 采购订单信息组
     * @return string
     */
    public function getStockPostDataToSap($request_data)
    {
        $country_code = get_country_code();
        $item = '';
        foreach ($request_data['product_v1'] as $key => $value) {
            $item .= '<Item ItemImatListCompleteTransmissionIndicator="true" actionCode="01">
                <ItemID>' . $value['sap_item_id'] . '</ItemID>
               <BusinessTransactionDocumentItemTypeCode>' . $request_data['purchase_type'] . '</BusinessTransactionDocumentItemTypeCode>
               <!--数量单位:-->
               <Quantity>' . $value['total'] . '</Quantity>
               <!--价格:-->               
               <ListUnitPrice>
                  <!--单价:--> 
                  <Amount currencyCode="' . $request_data['currency'] . '">' . $value['no_tax_price'] . '</Amount>
                  <!--价格单位:--> 
                  <BaseQuantity>' . $value['no_tax_num'] . '</BaseQuantity>               
               </ListUnitPrice> 
               <!--交货日期:-->
               <DeliveryPeriod>
                  <!--开始日期:-->
                  <StartDateTime timeZoneCode="UTC+8">' . $value['delivery_date'] . 'T04:28:35Z</StartDateTime>
                  <!--结束日期:-->
                  <EndDateTime timeZoneCode="UTC+8">' . $value['delivery_date'] . 'T04:28:35Z</EndDateTime>
               </DeliveryPeriod>
            
               <!--流程类型:-->
               <DirectMaterialIndicator>true</DirectMaterialIndicator>
               <!--第三方交易状态指标：true未交货 false不相关:-->
               <ThirdPartyDealIndicator>false</ThirdPartyDealIndicator>
               <!--Optional:-->
               <ItemProduct actionCode="01">
                  <!--现金折扣指标:-->
                  <CashDiscountDeductibleIndicator>true</CashDiscountDeductibleIndicator>
                  <!--产品识别码:-->
                  <ProductKey>
                     <ProductTypeCode>1</ProductTypeCode>
                     <ProductIdentifierTypeCode>1</ProductIdentifierTypeCode>
                     <!--产品编号:-->
                     <ProductID>' . $value['product_option_code'] . '</ProductID>
                  </ProductKey>
               </ItemProduct>
               <!--收货地点:-->
               <ShipToLocation actionCode="01">
                  <!--收货地点编号:-->
                  <LocationID>' . $value['delivery_place_txt'] . '</LocationID>              
               </ShipToLocation>
               <FollowUpInvoice>              
                  <!--是否需要发票:-->
                  <RequirementCode>01</RequirementCode>
               </FollowUpInvoice>	
               <ItemTaxCalculation actionCode="01">
                    <!--征税国家/地区:-->
                    <CountryCode>' . $country_code . '</CountryCode>
                    <!--税务代码:-->
                    <TaxationCharacteristicsCode listID="' . $country_code . '">' . $value['vat_code'] . '</TaxationCharacteristicsCode>
                    <ItemTaxationTerms>                
                        <TaxDate>2021-06-21</TaxDate>
                    </ItemTaxationTerms>
               </ItemTaxCalculation>         
              </Item>';
        }

        return '<soapenv:Envelope xmlns:soapenv="http://schemas.xmlsoap.org/soap/envelope/" xmlns:glob="http://sap.com/xi/SAPGlobal20/Global" xmlns:glob1="http://sap.com/xi/AP/Globalization" xmlns:a3ok="http://sap.com/xi/AP/CustomerExtension/BYD/A3OK2">
   <soapenv:Header/>
   <soapenv:Body>
      <glob:PurchaseOrderBundleMaintainRequest_sync>
         <BasicMessageHeader>
</BasicMessageHeader>
         <!--1 or more repetitions:-->
         <PurchaseOrderMaintainBundle actionCode="01" ItemListCompleteTransmissionIndicator="true">
         <BusinessTransactionDocumentTypeCode>001</BusinessTransactionDocumentTypeCode>
            <!--货币类型代码:-->
            <CurrencyCode>' . $request_data['currency'] . '</CurrencyCode>
            <!--公司:-->
            <BuyerParty actionCode="01">
               <PartyKey>
                  <!--公司编号:-->
                  <PartyID>' . $request_data['cost_company'] . '</PartyID>
               </PartyKey>
            </BuyerParty>
            <!--供应商:-->
            <SellerParty actionCode="01">
               <PartyKey>
                  <!--供应商编号:-->
                  <PartyID>' . $request_data['sap_supplier_no'] . '</PartyID>
               </PartyKey>
            </SellerParty>
            <!--负责采购员:-->
            <EmployeeResponsibleParty actionCode="01">
               <PartyKey>
                  <!--负责采购员编号:-->
                  <PartyID>' . self::getPartId() . '</PartyID>
               </PartyKey>
            </EmployeeResponsibleParty>
            <!--收票方:-->
            <BillToParty actionCode="01">
               <PartyKey>
                  <!--收票方编号:-->
                  <PartyID>' . $request_data['cost_company'] . '</PartyID>
               </PartyKey>
            </BillToParty>
            <CashDiscountTerms ActionCode="01">
               <!--Optional:-->
               <Code>' . $request_data['loan_time_id'] . '</Code>
            </CashDiscountTerms>' . $item . '<!--OA的PO号:-->
            <a3ok:FlashContractNo>' . $request_data['pono'] . '</a3ok:FlashContractNo>
         </PurchaseOrderMaintainBundle>
      </glob:PurchaseOrderBundleMaintainRequest_sync>
   </soapenv:Body>
</soapenv:Envelope>';
    }

    /**
     * 组装辅助类订单到SAP请求报文信息
     * @param array $request_data 采购订单信息组
     * @return string
     */
    public function getHelperPostDataToSap($request_data)
    {
        $country_code = get_country_code();
        $item = '';
        foreach ($request_data['product_v1'] as $key => $value) {
            $item .= '<!--行项目:-->
            <Item actionCode="01">
               <!--行项目编号:-->
               <ItemID>' . $value['sap_item_id'] . '</ItemID>
               <!--项目类型:18物料:-->
               <BusinessTransactionDocumentItemTypeCode>18</BusinessTransactionDocumentItemTypeCode>
               <!--数量-->
               <Quantity unitCode="EA">' . $value['total'] . '</Quantity>        
               <!--描述-->
               <Description>' . mb_substr($value['desc'], 0, 40) . '</Description>     
            
             <!--净价:-->
               <ListUnitPrice>
                  <Amount currencyCode="' . $request_data['currency'] . '">' . $value['no_tax_price'] . '</Amount>
            <!--价格单位:-->
                  <BaseQuantity unitCode="EA">' . $value['no_tax_num'] . '</BaseQuantity>
               </ListUnitPrice>
               
               <!--行项目产品:-->
               <ItemProduct actionCode="01">
                <!--默认false:-->
                  <CashDiscountDeductibleIndicator>true</CashDiscountDeductibleIndicator> 
                  
                  <!--产品类别:-->
                  <ProductCategoryIDKey>
                     <ProductCategoryInternalID>' . $value['wrs_code'] . '</ProductCategoryInternalID>
                  </ProductCategoryIDKey>
               </ItemProduct>        
            
               <!--账户分配-->
               <ItemAccountingCodingBlockDistribution  ActionCode="01">
                  <AccountingCodingBlockAssignment ActionCode="01">
                     <!--账户分配类型:-->
                     <AccountingCodingBlockTypeCode>CC</AccountingCodingBlockTypeCode>
                     <!--成本中心:-->
                     <CostCentreID>' . $value['cost_center_name'] . '</CostCentreID>
               </AccountingCodingBlockAssignment>
              </ItemAccountingCodingBlockDistribution>    
                    
            <!--后续单据:-->
               <FollowUpDelivery>
                  <!--需要收料及服务确认：05不勾选:-->
                  <RequirementCode>05</RequirementCode>
               </FollowUpDelivery>
               
            <!--后续单据:-->
               <FollowUpInvoice>
                   <!--需要发票：01勾选:-->
                  <RequirementCode>01</RequirementCode>
               </FollowUpInvoice>
               
               <ItemTaxCalculation actionCode="01">			
                   <!--征税国家/地区:-->
                   <CountryCode>' . $country_code . '</CountryCode>
                   <!--税务代码:-->			
                   <TaxationCharacteristicsCode listID="' . $country_code . '">' . $value['vat_code'] . '</TaxationCharacteristicsCode>	                		
                   <ItemTaxationTerms>
                       <!--税务日期:-->		                			
                       <TaxDate>2021-06-21</TaxDate>			
                   </ItemTaxationTerms>			
               </ItemTaxCalculation>  
            </Item>';
        }

        return '<soapenv:Envelope xmlns:soapenv="http://schemas.xmlsoap.org/soap/envelope/" xmlns:glob="http://sap.com/xi/SAPGlobal20/Global" xmlns:glob1="http://sap.com/xi/AP/Globalization" xmlns:a3ok="http://sap.com/xi/AP/CustomerExtension/BYD/A3OK2">
   <soapenv:Header/>
   <soapenv:Body>
      <glob:PurchaseOrderBundleMaintainRequest_sync>
         <BasicMessageHeader>
         </BasicMessageHeader>
         
           <!--采购订单:-->
         <PurchaseOrderMaintainBundle actionCode="01">
          <!--采购订单类型001:-->
        <BusinessTransactionDocumentTypeCode>001</BusinessTransactionDocumentTypeCode>
            <!--货币:-->
            <CurrencyCode>' . $request_data['currency'] . '</CurrencyCode>
         
            <!--公司-->
            <BuyerParty actionCode="01">
               <PartyKey>
                  <!--公司编号:-->
                  <PartyID>' . $request_data['cost_company'] . '</PartyID>
               </PartyKey>
            </BuyerParty>
            
            <!--供应商-->
            <SellerParty actionCode="01">            
               <PartyKey>
                 <!--供应商编号:-->
                  <PartyID>' . $request_data['sap_supplier_no'] . '</PartyID>
               </PartyKey>
            </SellerParty>

            <!--收票方:-->
            <BillToParty actionCode="01">
                 <PartyKey>
                  <!--收票方编号:-->
                  <PartyID>' . $request_data['cost_company'] . '</PartyID>
               </PartyKey>
            </BillToParty>

        <!--负责采购员:-->
            <EmployeeResponsibleParty actionCode="01">
               <PartyKey>
                  <!--员工编号:-->
                  <PartyID>' . self::getPartId() . '</PartyID>
               </PartyKey>
            </EmployeeResponsibleParty>

         <!--付款条件:-->
            <CashDiscountTerms ActionCode="01">
               <!--付款条件编号-->
               <Code>' . $request_data['loan_time_id'] . '</Code>
            </CashDiscountTerms>' . $item . '<!--OA的PO号:-->
            <a3ok:FlashContractNo>' . $request_data['pono'] . '</a3ok:FlashContractNo>
         </PurchaseOrderMaintainBundle>
       
      </glob:PurchaseOrderBundleMaintainRequest_sync>
   </soapenv:Body>
</soapenv:Envelope>';
    }

    /**
     * @Desc: 获取付款模式枚举项
     * @return array
     * @author: W_uniQue
     * @Time: 2022/10/27 14:50
     */
    public function getPurchaseOrderPaymentMode()
    {
        $rows = PurchaseOrderPaymentMode::find([
            'columns' => 'id,payment_mode_code',
            'conditions' => 'is_del = :is_del:',
            'bind' => [
                'is_del' => Enums\GlobalEnums::IS_NO_DELETED,
            ],
        ])->toArray();
        if (!$rows) {
            return [];
        }
        $return = [];
        foreach ($rows as $k => $row) {
            $return[$k]['value'] = $row['id'];
            $return[$k]['label'] = self::$t->_($row['payment_mode_code']);
        }
        return $return;
    }

    /**
     * 获取供应商下指定时间下（待审核+已通过）含税金额总计累计值
     * @param $vendor_id
     * @param  $start_time
     * */
    public function checkTotalAmountByVendor($vendor_id, $start_time)
    {
        $res = PurchaseOrder::find([
            'conditions' => "vendor_id = :vendor_id: and status in ({status:array}) and created_at >= :start_time:",
            'columns' => 'sum(total_amount) as total_amount,currency',
            'bind' => ['vendor_id' => $vendor_id, 'status' => [Enums::CONTRACT_STATUS_PENDING, Enums::CONTRACT_STATUS_APPROVAL], 'start_time' => $start_time],
            'group' => 'currency'
        ])->toArray();
        $total_amount = 0;
        if (!empty($res)) {
            foreach ($res as $value) {
                $total_amount += EnumsService::getInstance()->currencyAmountConversion($value['currency'], $value['total_amount'], 2);
            }
        }
        return $total_amount;
    }

    /**
     * 关闭采购订单
     * @param $data
     * @param $user
     * @param bool $self
     * @return array
     */
    public function close($data, $user, $self = true)
    {
        $code = ErrCode::$SUCCESS;
        $message = 'success';
        $real_message = '';

        try {
            $item = PurchaseOrder::findFirst([
                'conditions' => 'id = :id:',
                'bind' => [
                    'id' => $data['id']
                ]
            ]);
            if (empty($item)) {
                throw new ValidationException(static::$t->_('purchase_order_empty_can_not_close'), ErrCode::$VALIDATE_ERROR);
            }
            if ($self) {
                //我的申请-不能关闭别人的
                if ($item->create_id != $user['id']) {
                    throw new ValidationException(static::$t->_('purchase_order_close_not_self'), ErrCode::$VALIDATE_ERROR);
                }
            } else {
                //数据查询-必须要有特权才能关闭
                $is_privilege_staff_id = EnumsService::getInstance()->isPrivilegeStaffId($user['id']);
                if (!$is_privilege_staff_id) {
                    throw new ValidationException(static::$t->_('purchase_order_privilege_can_not_close'), ErrCode::$VALIDATE_ERROR);
                }
            }
            //审批通过,执行状态为"未交付","部分交付",且关闭状态为未关闭
            $can_close_conditions = $item->status == Enums::PURCHASE_ORDER_STATUS_APPROVAL && in_array($item->execute_status, [PurchaseEnums::ORDER_EXECUTE_STATUS_PARTLY, PurchaseEnums::ORDER_EXECUTE_STATUS_NO]) && $item->is_close == PurchaseEnums::IS_CLOSE_NO;
            if (!$can_close_conditions) {
                throw new ValidationException(static::$t->_('purchase_order_can_not_close'), ErrCode::$VALIDATE_ERROR);
            }
            $item->is_close = PurchaseEnums::IS_CLOSE_YES;
            $item->is_hand_close = PurchaseEnums::IS_HAND_CLOSE_YES;
            if ($item->save() === false) {
                throw new BusinessException('采购申请单-我的申请-关闭采购申请单失败, 原因可能是: ' . get_data_object_error_msg($item) . '; 待更新数据: ' . json_encode($item->toArray(), JSON_UNESCAPED_UNICODE), ErrCode::$BUSINESS_ERROR);
            }
            $this->logger->info("purchase-order-close-success params=" . json_encode($data) . '; user_id=' . $user['id']);
        } catch (ValidationException $e) {
            $code = $e->getCode();
            $message = $e->getMessage();
        } catch (BusinessException $e) {
            $code = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        } catch (\Exception $e) {
            $code = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        }
        if (!empty($real_message)) {
            $this->logger->warning("purchase-order-close-failed:" . $real_message);
        }

        return [
            'code' => $code,
            'message' => $message,
            'data' => $result ?? []
        ];
    }

    /**
     * 采购订单-添加发货记录
     * @param $data
     * @param $user
     * @return array
     */
    public function deliveryAdd($data, $user)
    {
        $code = ErrCode::$SUCCESS;
        $message = 'success';
        $real_message = '';
        try {
            $item = PurchaseOrderProduct::findFirst([
                'conditions' => 'id = :id: and poid = :poid:',
                'bind' => [
                    'id' => $data['product_id'],
                    'poid' => $data['po_id']
                ]
            ]);
            if (empty($item)) {
                throw new ValidationException(static::$t->_('purchase_order_product_not_exist'), ErrCode::$VALIDATE_ERROR);
            }
            if (!empty($data['delivery'])) {
                //校验所有发货记录的到货数量不能超过这行的数量
                $all_dispatch_total = array_sum(array_column($data['delivery'], 'dispatch_total'));
                if ($all_dispatch_total > $item->total) {
                    throw new ValidationException(static::$t->_('delivery_add_all_dispatch_total_max_error'), ErrCode::$VALIDATE_ERROR);
                }
            }
            $model = new PurchaseOrderProductDeliveryModel();
            //是否存在发货记录
            $exist = $model::find([
                'conditions' => 'product_id = :product_id:',
                'bind' => [
                    'product_id' => $data['product_id']
                ]
            ]);
            //如果存在就删除
            $exist_arr = $exist->toArray();
            if (!empty($exist_arr) && !$exist->delete()) {
                throw new BusinessException('采购订单-添加发货记录失败-删除老数据失败, 数据: ' . json_encode($exist_arr, JSON_UNESCAPED_UNICODE), ErrCode::$BUSINESS_ERROR);
            }
            //如果不为空就添加, 为空的话说明要编辑为空, 上边已经删除了, 达到编辑为空的目的
            if (!empty($data['delivery'])) {
                $batch_data = [];
                foreach ($data['delivery'] as $v) {
                    $batch_data[] = [
                        'po_id' => $data['po_id'],
                        'product_id' => $data['product_id'],
                        'dispatch_total' => $v['dispatch_total'],
                        'storage_date' => !empty($v['storage_date']) ? $v['storage_date'] : null,
                        'dispatch_date' => !empty($v['dispatch_date']) ? $v['dispatch_date'] : null,
                        'sailing_arrive_date' => !empty($v['sailing_arrive_date']) ? $v['sailing_arrive_date'] : null,
                        'sailing_date' => !empty($v['sailing_date']) ? $v['sailing_date'] : null,
                        'clearance_date' => !empty($v['clearance_date']) ? $v['clearance_date'] : null,
                    ];
                }
                if ($model->batch_insert($batch_data) === false) {
                    throw new BusinessException('采购订单-添加发货记录失败, 参数: ' . json_encode($data, JSON_UNESCAPED_UNICODE) . '; 数据: ' . json_encode($batch_data, JSON_UNESCAPED_UNICODE), ErrCode::$BUSINESS_ERROR);
                }
            }
            $this->logger->info("purchase-order-deliveryAdd-success params=" . json_encode($data, JSON_UNESCAPED_UNICODE) . '; user_id=' . $user['id']);

        } catch (ValidationException $e) {
            $code = $e->getCode();
            $message = $e->getMessage();
        } catch (BusinessException $e) {
            $code = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        } catch (\Exception $e) {
            $code = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        }
        if (!empty($real_message)) {
            $this->logger->warning("purchase-order-deliveryAdd-failed:" . $real_message);
        }

        return [
            'code' => $code,
            'message' => $message,
            'data' => $result ?? []
        ];
    }

    /**
     * 获取新增默认值
     * @param int $company_id 费用公司id
     * @param string $pono 采购订单号
     * @return array
     */
    public function getPurchaseOrderOptions($company_id, $pono = '')
    {
        $code = ErrCode::$SUCCESS;
        $message = 'success';
        $real_message = '';
        try {
            $conditions = $bind = [];
            $conditions[] = 'cost_company = :cost_company: AND status = :status:';
            $bind['cost_company'] = $company_id;
            $bind['status'] = Enums::CONTRACT_STATUS_APPROVAL;
            if (!empty($pono)) {
                $conditions[] = 'pono like :pono:';
                $bind['pono'] = $pono . '%';
            }
            $conditions = implode(' AND ', $conditions);
            $data = PurchaseOrder::find([
                'columns' => 'pono',
                'conditions' => $conditions,
                'bind' => $bind,
                'limit' => 20,
                'order' => 'id desc'
            ])->toArray();
        } catch (\Exception $e){
            $code = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage();
            $data = [];
        }

        if (!empty($real_message)) {
            $this->logger->warning('getPurchaseOrderOptions-failed:' . $real_message);
        }

        return [
            'code' => $code,
            'message' => $message,
            'data' => $data
        ];
    }

    /**
     * 采购订单-查看发货记录
     * @param $params
     * @return array
     */
    public function deliveryDetail($params)
    {
        $code = ErrCode::$SUCCESS;
        $message = 'success';
        $data = [];
        try {
            //发货记录
            $delivery_data = PurchaseOrderProductDeliveryModel::find([
                'conditions' => 'product_id = :product_id:',
                'bind' => [
                    'product_id' => $params['product_id']
                ]
            ])->toArray();
            foreach ($delivery_data as &$v) {
                //时间格式null转空字符
                $v['storage_date'] = !empty($v['storage_date']) ? $v['storage_date'] : '';
                $v['dispatch_date'] = !empty($v['dispatch_date']) ? $v['dispatch_date'] : '';
                $v['sailing_arrive_date'] = !empty($v['sailing_arrive_date']) ? $v['sailing_arrive_date'] : '';
                $v['sailing_date'] = !empty($v['sailing_date']) ? $v['sailing_date'] : '';
                $v['clearance_date'] = !empty($v['clearance_date']) ? $v['clearance_date'] : '';
            }
            $data = $delivery_data;
        } catch (\Exception $e) {
            $code = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $this->logger->error('purchase-order-deliveryDetail-failed:' . $e->getMessage() . '; trace:' . $e->getTraceAsString());
        }
        return [
            'code' => $code,
            'message' => $message,
            'data' => $data
        ];
    }

    /**
     * 更新采购订单验收数量和采购订单执行状态, 不需要事务,当前调用的地方有事务包裹,新调用的需要注意
     * @param object $purchase_acceptance_info 采购验收单对象
     * @param array $user 操作人信息组
     * @return bool
     * @throws BusinessException
     */
    public function updateAcceptanceTotal($purchase_acceptance_info, $user)
    {
        $result = false;
        //验收单关联的非采购订单，无需变更
        if ($purchase_acceptance_info->acceptance_type != PurchaseEnums::ACCEPTANCE_TYPE_PO) {
            return $result;
        }

        //获取验收单明细行上关联的各采购订单行的验收数量
        $purchase_acceptance_product_list = $purchase_acceptance_info->getProducts()->toArray();
        $order_product_acceptance_total = array_column($purchase_acceptance_product_list, null, 'order_product_id');

        //查询采购订单
        $po_order = PurchaseOrder::findFirst([
            'conditions' => 'id = :id: and pono  = :order_no:',
            'bind' => ['id' => $purchase_acceptance_info->po_id, 'order_no' => $purchase_acceptance_info->po]
        ]);
        $po_order_arr = $po_order ? $po_order->toArray() : [];
        //execute_status=0是历史数据, 不处理
        if (empty($po_order_arr) || $po_order_arr['execute_status'] == 0) {
            return $result;
        }
        //查询采购订单明细行验收数量
        $purchase_order_products_obj = $po_order->getProducts();
        $purchase_order_products = $purchase_order_products_obj->toArray();
        if (empty($purchase_order_products)) {
            return $result;
        }

        //加上本次行(通过)验收数量, 判断采购订单执行状态
        $all_acceptance_total = 0;
        $all_total = 0;
        $new_product_acceptance_total = [];
        foreach ($purchase_order_products as $one_product) {
            $this_product_acceptance_total = $one_product['acceptance_total'];
            if (isset($order_product_acceptance_total[$one_product['id']]) && $order_product_acceptance_total[$one_product['id']]['check_result'] == 1) {
                $this_product_acceptance_total += $order_product_acceptance_total[$one_product['id']]['check_num'];
                $new_product_acceptance_total[$one_product['id']] = $this_product_acceptance_total;
            }
            //累加全部行的验收数量
            $all_acceptance_total += $this_product_acceptance_total;

            //累加全部行的总数量
            $all_total += $one_product['total'];
        }

        //更新本次行交付数量
        foreach ($purchase_order_products_obj as $product_obj) {
            if (isset($new_product_acceptance_total[$product_obj->id])) {
                $old_product = $product_obj->toArray();
                $product_obj->acceptance_total = $new_product_acceptance_total[$product_obj->id];
                $after_product = $product_obj->toArray();
                if ($product_obj->save() === false) {
                    $this->logger->warning('采购订单修改[更新验收数量失败]: 待处理数据: before=' . json_encode($old_product, JSON_UNESCAPED_UNICODE) . '; after=' . json_encode($after_product, JSON_UNESCAPED_UNICODE) . '; 可能存在的问题: ' . get_data_object_error_msg($product_obj));
                    throw new BusinessException('采购订单修改[更新验收数量失败]', ErrCode::$BUSINESS_ERROR);
                }
                $this->saveUpdateTotalLog(Enums::WF_PURCHASE_ORDER, $po_order_arr['id'], $po_order_arr['pono'], $old_product, $after_product, $user);
                $this->logger->info('采购订单修改[更新验收数量成功]: 待处理数据: before=' . json_encode($old_product, JSON_UNESCAPED_UNICODE) . '; after=' . json_encode($product_obj->toArray(), JSON_UNESCAPED_UNICODE));
            }
        }
        //交付状态更新
        //总验收数量 <= 0 执行状态 = 未交付
        //总验收数量 > 0  执行状态 = 部分交付
        //总验收数量 >= 所有行总数量 执行状态 = 完全交付
        $old_po_order = $po_order_arr;
        if ($all_acceptance_total >= $all_total) {
            $update_execute_status = PurchaseEnums::ORDER_EXECUTE_STATUS_DONE;
        } elseif ($all_acceptance_total > 0) {
            $update_execute_status = PurchaseEnums::ORDER_EXECUTE_STATUS_PARTLY;
        } else {
            $update_execute_status = PurchaseEnums::ORDER_EXECUTE_STATUS_NO;
        }
        if ($po_order->execute_status != $update_execute_status) {
            $po_order->execute_status = $update_execute_status;
            //如果全部交付,需要把关闭状态改成'已关闭'
            if ($update_execute_status == PurchaseEnums::ORDER_EXECUTE_STATUS_DONE) {
                $po_order->is_close = PurchaseEnums::IS_CLOSE_YES;
            }
            if ($po_order->save() === false) {
                $this->logger->warning('采购订单修改[更新执行状态失败]: 待处理数据: before=' . json_encode($old_po_order, JSON_UNESCAPED_UNICODE) . '; after=' . json_encode($po_order->toArray(), JSON_UNESCAPED_UNICODE) . '; 可能存在的问题: ' . get_data_object_error_msg($po_order));
                throw new BusinessException('采购订单修改[更新执行状态失败]', ErrCode::$BUSINESS_ERROR);
            }
            $this->logger->info('采购订单修改[更新执行状态成功]: 待处理数据: before=' . json_encode($old_po_order, JSON_UNESCAPED_UNICODE) . '; after=' . json_encode($po_order->toArray(), JSON_UNESCAPED_UNICODE));
        }
        return true;
    }

    /**
     * 数据查询-采购订单-编辑 -【barcode、产品描述，单位，计量单位，规格型号】
     * @param array $params 参数组
     * @param array $user 登陆者信息组
     * @return array
     */
    public function dataUpdate($params, $user)
    {
        $code = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $db = $this->getDI()->get('db_oa');
        $db->begin();
        try {
            //校验是否有修改权限
            if (!EnumsService::getInstance()->isPrivilegeStaffId($user['id'])) {
                throw new ValidationException(static::$t->_('purchase_apply_privilege_can_not_close'), ErrCode::$VALIDATE_ERROR);
            }
            $item = PurchaseOrder::findFirst([
                'conditions' => 'id = :id: AND status = :status: AND is_close = :is_close:',
                'bind' => [
                    'id' => $params['id'],
                    'status' => Enums::WF_STATE_APPROVED,
                    'is_close' => PurchaseEnums::IS_CLOSE_NO
                ]
            ]);
            //采购订单的关闭状态不等于“已关闭”且审核状态为“已通过”才可编辑
            if (empty($item)) {
                throw new ValidationException(static::$t->_('purchase_order_cannot_update'), ErrCode::$VALIDATE_ERROR);
            }
            // 要处理的PO行
            foreach ($params['products'] as $product) {
                $purchase_order_product = PurchaseOrderProduct::findFirst([
                    'conditions' => 'id = :id: AND poid = :poid:',
                    'bind' => ['id' => $product['id'], 'poid' => $item->id]
                ]);
                if (empty($purchase_order_product)) {
                    throw new ValidationException(static::$t->_('purchase_product_not_exits'), ErrCode::$VALIDATE_ERROR);
                }

                $before = $purchase_order_product->toArray();

                //保存明细行信息
                $update_product_data = [
                    'product_option_code' => $product['product_option_code'] ?? '',
                    'desc' => $product['desc'] ?? '',
                    'unit' => $product['unit'] ?? '',
                    'metere_unit' => $product['metere_unit'] ?? '',
                    'product_option' => $product['product_option'] ?? ''
                ];
                $bool = $purchase_order_product->save($update_product_data);
                if ($bool === false) {
                    throw new BusinessException('数据查询-采购订单-编辑-PO主数据更新失败, 原因可能是: ' . get_data_object_error_msg($purchase_order_product) . '; 待更新数据: ' . json_encode($product, JSON_UNESCAPED_UNICODE), ErrCode::$BUSINESS_ERROR);
                }
                $after = $purchase_order_product->toArray();
                $this->saveUpdateLog(Enums::WF_PURCHASE_ORDER, $item->id, $item->pono, $before, $after, $user);
            }
            $db->commit();
        } catch (ValidationException $e) {
            $code = $e->getCode();
            $message = $e->getMessage();
        } catch (BusinessException $e){
            $code = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        } catch (\Exception $e) {
            $code = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        }

        if (!empty($real_message)) {
            $this->logger->warning('purchase-order-dataUpdate-failed:' . $real_message);
        }

        if (!empty($message)) {
            $db->rollback();
        }

        return [
            'code' => $code,
            'message' => $message,
            'data' => $data ?? []
        ];
    }

    /**
     * 返回可同步至金蝶的列表
     * @param int $page_num 当天第几页
     * @param array $params ['kingdee_company_ids' => '金蝶BU公司ID组', 'date_start' => '数据范围起始日期', 'date_end' => '数据范围截止日期']
     * @return mixed
     */
    public function getSendKingDeeList($params)
    {
        $page_size                     = 100;
        $kingdee_company_ids           = $params['kingdee_company_ids'];
        $kingdee_purchase_storage_type = $params['kingdee_purchase_storage_type'];
        $max_id = $params['max_id'] ?? 0;
        $builder                       = $this->modelsManager->createBuilder();
        $builder->columns([
            'o.id',
            'o.pono',
            'o.currency',
            'o.purchase_type',
            'o.payable_positive_is_send_kingdee',
            'o.cost_company',
            'o.subtotal_amount',
            'o.cost_store_name',
            'o.cost_store',
            'o.vendor_id',
            'o.taxation',
            'o.cost_department',
            'o.approve_at',
        ]);
        $builder->from(['o' => PurchaseOrder::class]);
        $builder->where('o.payable_positive_is_send_kingdee = :is_send: and o.id > :max_id:', ['is_send' => Enums\KingDeeEnums::PAY_IS_SEND_KINGDEE_NO, 'max_id' => $max_id]);
        $builder->inWhere('o.cost_company', $kingdee_company_ids);
        $builder->inWhere('o.purchase_type', $kingdee_purchase_storage_type);
        $builder->andWhere('o.is_seed_kingdee = :is_seed_kingdee:',
            ['is_seed_kingdee' => Enums\KingDeeEnums::IS_SEED_KINGDEE]);
        
        $builder->limit($page_size);
        $builder->orderBy('o.id ASC');
        return $builder->getQuery()->execute();
    }

    /**
     * 获取订单单独抛税参数
     * @param object $item 报销单据对象信息
     * @param array $params['is_cancel' => '应付（付款）是否取消支付：0否（正向），1是（反向）', 'expire_date' => '到期日', 'vendor_id' => '供应商编码', 'sap_company_list' => 'sap公司ID组', 'account_subjects_list' => '会计科目组']
     * @return array
     */
    public function getPayableParams($item, $params)
    {
        $storage_type_business_type = EnumsService::getInstance()->getSettingEnvValueJson(Enums\KingDeeEnums::STORAGE_TYPE_BUSINESS_TYPE);
        $business_type_no = $storage_type_business_type[$item['purchase_type']] ?? '';
        $apply_date = date('Y-m-d');
        $bill_no = get_country_code() . ($item->pono ?? '');
        // 订单行信息
        $purchase_order_products = PurchaseOrderProduct::find([
            'conditions' => 'poid = :poid:',
            'bind' => ['poid' => $item->id]
        ])->toArray();
        $_cost_store = in_array($item->cost_store, [-1, 1]) ? 1 : 2;
        foreach ($purchase_order_products as $k => $product){
            if (empty($product['cost_store_id'])) {
                if ($_cost_store == 1) {
                    $purchase_order_products[$k]['cost_store_name'] = self::$t['payment_cost_store_type_1'];
                } else {
                    $purchase_order_products[$k]['cost_store_name'] = $item->cost_store_name;
                }
            }
        }
        $cost_store_name = $purchase_order_products[0]['cost_store_name'] ?? '';
        $payable_params = [
            'bill_no' =>  $bill_no,
            'apply_date' => $apply_date,
            'expire_date' => $params['expire_date'] ?? '',
            'vendor_id' => $params['vendor_data'][$item->vendor_id] ?? '',
            'business_type_no' => $business_type_no,
            'currency' => $item->currency ?? '',
            'sap_company_id' => $params['sap_company_list'][$item->cost_company]['sap_company_id'] ?? '',
            'cost_store_name' => $cost_store_name,
            'is_offset_loan' => '否',
            'loan_amount' => '',
            'remark' => $item->pono ?? '',
        ];
        foreach ($purchase_order_products as $detail) {
            // 不含税单价 
            $tax_not_price = bcdiv($detail['vat7'] ?? 0, 1000, 2);
            // 不含税金额 含税单价  含税金额价税合计 
            $amount = $tax_not = $amount_price = $tax_not_price;
            // 税额
            $tax = 0;
            // 税率
            $rate = 0;
            $payable_params['details'][] = [
                'company_project' => $params['company_project_enum'][$params['sap_company_list'][$item->cost_company]['sap_company_id']] ?? '',
                'abstract' => 'Payment_'.$item->pono.'_'.$params['purchase_type_list'][$item->purchase_type].'('.$detail['product_name'].')_'.'input vat',
                'product_desc' => '',
                'product_option_code' => '',
                'quantity' => 1,
                'cost_center_code' => $detail['cost_center_name'] ?? '',
                'tax_not_price' => $tax_not_price,//不含税单价
                'tax_not' => $tax_not,
                'amount_price' => $amount_price,
                'amount' => $amount,
                'rate' => $rate,// 税率
                'subjects_code' => '2221.001.004',//科名编码
                'tax' => $tax, // 税额
            ];
        }
        $no = get_country_code() . ($item->pono ?? '');
        // 折前价税合计
        $payable_params['amount'] = $item->taxation ? bcdiv($item->taxation,1000,2) : '';
        $payable_params['no'] = $no;
        return $payable_params;
    }
}
