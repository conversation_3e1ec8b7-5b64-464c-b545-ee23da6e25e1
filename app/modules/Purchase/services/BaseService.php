<?php

namespace App\Modules\Purchase\Services;

use App\Library\Enums;
use App\Library\Enums\KingDeeEnums;
use App\Library\Enums\PurchaseEnums;

use App\Library\ErrCode;
use App\Library\Exception\BusinessException;
use App\Library\RedisClient;
use App\Library\Validation\Validation;
use App\Library\Validation\ValidationException;
use App\Models\oa\PurchaseUpdateLogModel;
use App\Modules\Budget\Services\BudgetService;
use App\Modules\Common\Models\EnvModel;
use App\Modules\Ledger\Models\LedgerAccountModel;
use App\Modules\Material\Services\StandardService;
use App\Modules\Organization\Models\SysDepartmentModel;

use App\Modules\Purchase\Models\PurchaseTypeWrsCode;
use App\Modules\Purchase\Models\PurchaseUpdateTotalLog;
use App\Modules\Purchase\Models\ScmCargoOwnerModel;
use App\Modules\User\Models\DepartmentModel;
use App\Modules\User\Services\UserService;
use App\Util\RedisExpire;
use App\Util\RedisKey;

class BaseService extends \App\Library\BaseService
{
    const LIST_TYPE_APPLY = 1;
    const LIST_TYPE_AUDIT = 2;
    const LIST_TYPE_FYR = 3;
    const LIST_TYPE_DATA = 4; //数据查询
    const LIST_TYPE_ASK = 5; //征询
    const LIST_TYPE_PAY = 6; //支付
    const SCM_TYPE_PU = 1;//采购单关联入库单
    const SCM_TYPE_PO = 2;//入库单

    const SCM_CARGO_OWNER_KEY = 'scm_cargo_owner_key';

    public $digits = 1000000;//原来4位 扩大为6位 需刷数据库
    public $digits_num = 6;

    const IS_QUALIFIED = 1;//合格
    const IS_NO_QUALIFIED = 2;//不合格
    public static $qualified = [
        self::IS_QUALIFIED    => 'sample_is_qualified',
        self::IS_NO_QUALIFIED => 'sample_no_qualified'
    ];

    public static $validate_supplement = [
        'id'                       => 'Required|IntGe:1',
        'required_supplement_file' => 'Required|ArrLenGeLe:0,30'

    ];

    const NOTREQUIRED_LONG_TEXT_LEN = 'Required|StrLenGeLe:1,500';
    const REQUIRED_LONG_TEXT_LEN = 'Required|StrLenGeLe:1,100';
    const VENDOR_EMAIL_RULE = 'Required|StrLenGeLe:1,200|>>>:supplier email error';
    const REQUIRED_LONG_TEXT_LEN_BIG = 'Required|StrLenGeLe:1,500';
    const SWIFT_CODE_RULE = 'IfIntEq:pay_where,2|Required|StrLenGeLe:1,30';
    const PAY_WHERE_RULE = 'Required|StrIn:1,2';
    public static $validate_param = [
        'pono' => 'Required|StrLenGeLe:10,20',
        'create_id' => 'Required|Int',
        'create_name' => 'Required',
        'create_department_id' => 'Required|Int',
        'pa_id' => 'Required|Int',
        'department_id' => 'Required',
        //'vendor' => self::REQUIRED_LONG_TEXT_LEN,
        'vendor_addr' => self::NOTREQUIRED_LONG_TEXT_LEN,//供应商地址
        'vendor_contact' => self::REQUIRED_LONG_TEXT_LEN,//供应商联系人
        'vendor_phone' => self::REQUIRED_LONG_TEXT_LEN,//供应商电话/手机号
        'vendor_email'  => self::VENDOR_EMAIL_RULE,//供应商邮箱
       // 'payment_to' => self::REQUIRED_LONG_TEXT_LEN_BIG,
        'bank_name' => 'Required|StrLenGeLe:1,100',
        'bank_account_name' => 'Required|StrLenGeLe:1,100',
        'bank_no' => 'Required|StrLenGeLe:1,100',
        'purchase_type'=>'Required|IntGe:0',
        'loan_time' => 'Required|StrLenGeLe:0,500',
        'payment_method' => 'Required|IntIn:' . Enums::PURCHASE_PAY_TYPE_BANK . ',' . Enums::PURCHASE_PAY_TYPE_CASH.",".Enums::PURCHASE_PAY_TYPE_CHEQUE,
        'delivery_addr' => self::REQUIRED_LONG_TEXT_LEN,
        'remark'=>'StrLenGeLe:0,5000',
        'attachment' => 'Required|Arr|ArrLenGe:1',
        'account_type' => 'Required|IntIn:' . KingDeeEnums::ACCOUNT_TYPE_FLASH_EXPRESS_COMPANY . ',' . KingDeeEnums::ACCOUNT_TYPE_SUB_COMPANY,// 科目类型：1快递公司科目，2子公司科目
        // 'attachment[*].bucket_name'=>'Required',
        // 'attachment[*].object_key'=>'Required',
        // 'attachment[*].file_name'=>'Required',
        'product' => 'Required|Arr|ArrLenGe:1',
        'product[*].category_a' => 'Required|Int',
        'product[*].category_b' => 'Required|Int',
        'product[*].budget_id' => 'Required|Int',
        'product[*].level_code' => 'Required|StrLenGeLe:0,30',
        'product[*].product_id' => 'Required',
        'product[*].product_name' => 'StrLenGeLe:0,200',
        'product[*].ledger_account_id' => 'IntGe:0',            //核算科目id
        'product[*].product_option' => 'StrLenGeLe:0,255',//规格型号
        'product[*].product_option_code'=>'StrLenGeLe:0,32|>>>:product_option_code error',
        'product[*].finance_code' => 'Required',
        'product[*].desc' => self::REQUIRED_LONG_TEXT_LEN,
        'product[*].total' => 'Required|IntGt:0',
        'product[*].unit' => 'Required|StrLenGeLe:1,30',
        'product[*].price' => 'Required|FloatGe:0',
        'product[*].total_price' => 'Required|FloatGe:0',
        'product[*].remark' => 'StrLenGeLe:0,500',
        'product[*].unit_code' => 'Required|StrLenGeLe:1,30',
        'product[*].no_tax_price' => 'Required|FloatGe:0',
        'product[*].delivery_date' => 'Required|Date',
        'product[*].delivery_place' => 'Required|StrLenGeLe:1,30',
        'product[*].cost_center_name'=>'StrLenGeLe:0,256|>>>:cost center error',         // 费用所属中心
        'product[*].no_tax_num' => 'IfIntGt:no_tax_num,0|IntLe:100000|>>>:no_tax_num no more than 100,000',
        'product[*].purchase_type' =>'IntGe:0',//采购类型
        'product[*].update_to_acceptance' =>'IntGe:0',//是否验收，1否，2是
        'product[*].metere_unit' => 'StrLenGeLe:0,20',//记量单位
        'product[*].wrs_code' => 'StrLenGeLe:0,30',//财务分类编码
        'product[*].delivery' => 'Required|Arr',//发货记录
        'product[*].delivery[*].storage_date' => 'Date',
        'product[*].delivery[*].dispatch_date' => 'Date',
        'product[*].delivery[*].sailing_arrive_date' => 'Date',
        'product[*].delivery[*].sailing_date' => 'Date',
        'product[*].delivery[*].clearance_date' => 'Date',
        'product[*].delivery[*].dispatch_total' => 'Required|IntGt:0',
        'subtotal_amount' => 'Required|FloatGe:0',
        //'discount' => 'Required|FloatGe:0',
        //'freight' => 'Required|FloatGe:0',
        //'installation_cost' => 'Required|FloatGe:0',
        'taxation' => 'Required|FloatGe:0',
        'amount' => 'Required|FloatGe:0',
        'cost_company'=>'Required|IntGe:1',
        'cost_company_name'=>'Required|StrLenGeLe:0,50',
        'cost_center_code'=>'Required|StrLenGeLe:0,50',
        'cost_store' => 'Required|IntIn:1,2|>>>:expense store error',
        'wrs_code' => 'StrLenGeLe:0,64|>>>:wrs_code error',
        'pay_where' => self::PAY_WHERE_RULE,//1 境内 2 境外
        'swift_code' => self::SWIFT_CODE_RULE,//Swift Code

    ];

    //采购申请付款单验证组
    public static $validate_payment_param = [
        'ppno' => 'Required|StrLenGeLe:10,20',
        'create_id' => 'Required|Int',
        'create_name' => 'Required',
        'create_department_id' => 'Required|Int',
        'is_link_pa' => 'Required|IntIn:0,1',       //是否采购框架合同，0否，1是（直接关联采购申请单）
        'po_id' => 'IfIntEq:is_link_pa,0|Required|Int',
        'pa_id' => 'IfIntEq:is_link_pa,1|Required|Int',
        'method' => 'Required|IntIn:1,2',
        // 'inbound_no'=>'Required|Arr|ArrLenGeLe:1,10',
        //'vendor_id' => 'Required|StrLenGeLe:1,32',
        'vendor' => self::REQUIRED_LONG_TEXT_LEN,
        'vendor_addr' => self::NOTREQUIRED_LONG_TEXT_LEN,//供应商地址
        'vendor_contact' => self::REQUIRED_LONG_TEXT_LEN,//供应商联系人
        'vendor_phone' => self::REQUIRED_LONG_TEXT_LEN,//供应商电话/手机号
        'vendor_email'  => self::VENDOR_EMAIL_RULE,//供应商邮箱
        'vendor_tax_number' => 'StrLenGeLe:0,30',
       // 'payment_to' => self::REQUIRED_LONG_TEXT_LEN_BIG,
        'bank_name' => 'Required|StrLenGeLe:1,100',
        'bank_account_name' => 'Required|StrLenGeLe:1,100',
        'bank_no' => 'Required|StrLenGeLe:1,100',
        'pay_where' => self::PAY_WHERE_RULE,//1 境内 2 境外
        'swift_code' => self::SWIFT_CODE_RULE,//Swift Code
        'amount' => 'Required|FloatGt:0',
        'loan_time' => 'Required|StrLenGeLe:0,500',
        'payment_method' => 'Required|IntIn:' . Enums::PURCHASE_PAY_TYPE_BANK . ',' . Enums::PURCHASE_PAY_TYPE_CASH . "," . Enums::PURCHASE_PAY_TYPE_CHEQUE,
        'due_date' => 'Required|Date',
        'attachment' => 'Required|Arr|ArrLenGeLe:1,10',
        'attachment[*].bucket_name' => 'Required',
        'attachment[*].object_key' => 'Required',
        'attachment[*].file_name' => 'Required',
        'receipt' => 'Required|Arr|ArrLenGe:1',
        'receipt[*].pop_id' => 'Int',
        'receipt[*].pap_id' => 'Int',
        //'receipt[*].vendor' => self::REQUIRED_LONG_TEXT_LEN,
        'receipt[*].category_a' => 'Required|Int',
        'receipt[*].category_b' => 'Required|Int',
        'receipt[*].budget_id' => 'Required|Int',
        'receipt[*].level_code' => 'Required|StrLenGeLe:0,30',
        'receipt[*].product_id' => 'Required|Int',
        'receipt[*].product_name' => 'StrLenGeLe:0,200',
        'receipt[*].ledger_account_id' => 'IntGe:0',
        'receipt[*].total' => 'Required|IntGe:0',
        'receipt[*].unit' => self::REQUIRED_LONG_TEXT_LEN,
        'receipt[*].not_tax_price' => 'Required|FloatGt:0',
        'receipt[*].total_price' => 'Required|FloatGt:0',
        'receipt[*].vat7_rate' => 'Required|FloatGe:0',
        'receipt[*].tax_ratio' => 'Required|FloatGe:0',
        'receipt[*].tax_total_price' => 'Required|FloatGt:0',
        'receipt[*].ticket_number' => self::REQUIRED_LONG_TEXT_LEN,
        'receipt[*].ticket_date' => self::REQUIRED_LONG_TEXT_LEN,
        'receipt[*].ticket_amount' => 'Required|FloatGt:0',
        'receipt[*].ticket_amount_not_tax' => 'Required|FloatGe:0',
        'receipt[*].input_tax' => 'Required|FloatGe:0',
        'receipt[*].deduct_input_tax' => 'Required|FloatGe:0',
        'receipt[*].wht_type' => 'Required|FloatGe:0',          //0=无,1=PND3，2=PND53，
        'receipt[*].wht_ratio' => 'Required|FloatGe:0',         //0=无,1,2,3,5
        'receipt[*].wht_amount' => 'Required|FloatGe:0',
        'receipt[*].real_amount' => 'Required|FloatGe:0',
        'receipt[*].cost_center_name' => 'StrLenGeLe:0,256|>>>:cost center error',         // 费用所属中心
        'receipt[*].vat_invoice_number' => 'Required|StrLenGeLe:0,100|>>>:params error[vat_invoice_number]',//增值税发票号
        'receipt[*].product_desc' => 'Required|Str',//产品描述
        'receipt[*].deductible_vat_tax'    => 'Required|Str',//可抵扣VAT税率
        'receipt[*].deductible_vat_amount' => 'Required|Str',//可抵扣VAT金额
        'receipt[*].pay_method' => 'Required|IntIn:' . PurchaseEnums::VALIDATE_PAYMENT_PAY_METHOD,//付款形式 1.按百分比 2.按收货数量付
    ];

    //货主字段验证
    public static $cargo_owner_param = [
        'inbound_no'              => 'Required|Arr|ArrLenGe:1',
        'inbound_no[*].no'        => 'Required|StrLenGeLe:1,20',
        'inbound_no[*].mach_code' => 'Required|StrLenGeLe:1,128',
        'inbound_no[*].mach_name' => 'Required|StrLenGeLe:1,256'
    ];

    // 采购付款支付列表参数
    public static $payment_pay_list_validate = [
        'flag' => 'Required|Int',
        'ppno' => 'Str',
        'create_id' => 'Str',
        'status' => 'Arr',
        'product_name' => 'Str',
        'created_at_start' => 'Str',
        'created_at_end' => 'Str',
        'pageSize' => 'Int',
        'pageNum' => 'Int'
    ];

    /**
     * 根据参数名获取上传的文件并转存
     *
     * @param String $key
     * @param String $path
     * @return String
     */
    public function getUploadFile($key, $path = APP_PATH . '/runtime/upload')
    {
        if ($this->request->hasFiles()) {
            foreach ($this->request->getUploadedFiles() as $file) {
                if ($key != $file->getKey()) {
                    continue;
                }
                $path = rtrim($path, '/') . '/';
                if (!file_exists($path)) {
                    mkdir($path, 0777, true);
                }
                $file->moveTo($path . $file->getName());
                $filename = $path . $file->getName();
            }
            return $filename;
        } else {
            return FALSE;
        }
    }

    /**
     * 去bi里面取相关数据
     * @param $userId
     * @return array
     */
    public function getUserMetaFromBi($userId)
    {

        $model = (new UserService)->getUserByIdInRbi($userId);
        if (empty($model)) {
            return [];
        }
        $department_id = $model->sys_department_id;
        $node_department_id = $model->node_department_id;

        $t = DepartmentModel::findFirst([
            "conditions" => "id = :id:",
            "bind" => [
                "id" => empty($node_department_id) ? $department_id : $node_department_id,
            ]
        ]);
        $jt = $model->getJobTitle();

        return [
            'create_id' => $model->staff_info_id ?? "",
            'create_name' => $this->getNameAndNickName($model->name ?? "",$model->nick_name??""),
            'create_department' => !empty($t) ? $t->name : "",
            'create_department_id' => empty($node_department_id) ? $department_id : $node_department_id,
            'department_id' => $department_id,
            'node_department_id' => $node_department_id, //前端用来判断是否去掉广告
            'create_job_title_name' => $jt ? $jt->job_name : '',
            'apply_email' => $model->email ?? "",

        ];

    }

    /**
     * 获取编号
     * @return string
     */
    public static function getNo($date, $key = NULL)
    {
        $key = self::getCounterKey($date, $key);
        if (self::getCounter($key)) {           //有计数器
            $lno = self::incrCounter($key);
        } else {
            $lno = self::setCounter($key);
        }
        return $date . sprintf('%04s', $lno);
    }

    /**
     * 判断计数器是否存在
     * @param string $key
     * @return bool|int
     */
    private static function getCounter($key)
    {
        return RedisClient::getInstance()->getClient()->exists($key);
    }

    /**
     * 计数器不存在的情况下
     * @param string $key
     * @return bool|int
     */
    private static function setCounter($key)
    {
        $lno = 1;
        RedisClient::getInstance()->getClient()->setex($key, RedisExpire::ONE_DAY, $lno);
        return $lno;
    }

    /**
     * 计数器存在的情况下
     * @param string $key
     * @return int
     */
    private static function incrCounter($key)
    {
        return RedisClient::getInstance()->getClient()->incrBy($key, 1);
    }


    private static function getCounterKey($date, $key = NULL)
    {
        $key = $key ?? RedisKey::PURCHASE_ORDER_COUNTER;
        return $key . "_" . $date;
    }



    /**
     * 获得采购付款申请单，待支付的人员
     * @return array|false|string[]
     */

    public function getPurchasePaymentPayStaffIds(){
        $pay_staff_id = EnvModel::getEnvByCode('purchase_payment_pay_staff_id');
        $pay_staff_id = explode(',', $pay_staff_id);
        return $pay_staff_id ?? [];
    }


    /**
     * 记录修改数量日志
     *
     * @param $biz_type
     * @param $biz_value
     * @param $no
     * @param $before
     * @param $after
     * @param $user
     * @throws BusinessException
     *
     * @return mixed
     */
    public function saveUpdateTotalLog($biz_type, $biz_value, $no, $before, $after, $user)
    {
        $data = [];
        $data['biz_type'] = $biz_type;
        $data['biz_value'] = $biz_value;
        $data['no'] = $no;
        $data['meta_id'] = $before['id'];
        $data['before'] = json_encode($before, JSON_UNESCAPED_UNICODE);
        $data['after'] = json_encode($after, JSON_UNESCAPED_UNICODE);
        $data['created_id'] = $user['id'];
        $data['created_name'] = $user['name'];
        $data['created_at'] = date("Y-m-d H:i:s");
        $purchase_update_total_log_model = (new PurchaseUpdateTotalLog());
        $save_update_log_res = $purchase_update_total_log_model->create($data);
        if ($save_update_log_res === false) {
            throw new BusinessException('采购-产品修改记录添加失败, 原因可能是: ' . get_data_object_error_msg($purchase_update_total_log_model) . '; 待入表数据为: ' . json_encode($data, JSON_UNESCAPED_UNICODE), ErrCode::$BUSINESS_ERROR);
        }

        return $save_update_log_res;
    }



    public function getUpdateTotalLog($biz_type,$biz_value){
        $list = PurchaseUpdateTotalLog::find(
            [
                'conditions'=>'biz_type =:biz_type: and biz_value=:biz_value:',
                'bind'=>['biz_type'=>$biz_type,'biz_value'=>$biz_value]

            ]
        )->toArray();
        $data = [];
        foreach ($list as $k=>$v){
            // 变更后数据
            $tmp = json_decode($v['after'],1);

            // 变更前数据
            $before = json_decode($v['before'],1);

            $tmp['created_at'] = $v['created_at'];
            $tmp['no'] = $v['no'];
            $tmp['vat7_rate'] = '0%';
            $tmp['price'] = bcdiv($tmp['price'],$this->digits,$this->digits_num);
            if(!empty($tmp['total_price'])){
                $tmp['vat7_rate'] = ''.round($tmp['vat7']/$tmp['total_price']*100).'%';
            }
            $tmp['vat7'] = bcdiv($tmp['vat7'],1000,2);
            $tmp['total_price'] = bcdiv($tmp['total_price'],1000,2);
            $tmp['all_total'] = bcdiv($tmp['all_total'],1000,2);
            $tmp['before_total'] = $before['total'];

            // is_free_budget, 来自PO单渠道释放预算的标识
            $tmp['is_free_budget'] = $tmp['is_free_budget'] ?? 'N';
            $tmp['remark'] = !empty($tmp['po_free_budget_remark']) ? (!empty($tmp['remark']) ? $tmp['remark']." [{$tmp['po_free_budget_remark']}]" : $tmp['po_free_budget_remark']) : $tmp['remark'];

            $tmp['free_budget_amount'] = !empty($tmp['free_budget_amount']) ? bcdiv($tmp['free_budget_amount'],1000,2) : '0.00';

            $data[] = $tmp;
        }
        $budget_ids = array_values(array_unique(array_filter(array_column($data,"budget_id"))));
        $budgetService = new BudgetService();
        $budgets = $budgetService->budgetObjectList($budget_ids);

        foreach ($data as &$item){
            $item['budget_text'] = $budgets[$item['budget_id']]['name_' . strtolower(substr(static::$language, -2))];
        }
        return $data;
    }


    /**
     * 判断是否是老的采购申请单，老的还是采购订单时候释放
     * @param $datetime
     * @return bool
     */
    public function isOldData($datetime){
        return $datetime < env('purchase_apply_old_datetime','2021-03-25 00:00:00');
    }


    /**
     * 订单编号规则
     * @param
     * @return bool
     */
    public function country_code()
    {
        switch (strtoupper(env('country_code', 'TH'))) {
            case 'TH':
                $code = '01';
                break;
            case 'PH':
                $code = '02';
                break;
            case 'LA':
                $code = '03';
                break;
            case 'MY':
                $code = '04';
                break;
            default:
                $code = '01';
        }

        return $code;
    }


    /**
     * 获取COO\CEO 级别下的所有BU级列表
     * @return array
     */
    public function getCooCostCompany()
    {
        $data = [];
        $coo_department_val = EnvModel::getEnvByCode("coo_department_id");
        if (!empty($coo_department_val)) {
            $coo_department_id_arr = explode(",", $coo_department_val);
            $item = SysDepartmentModel::find(
                [
                    'conditions' => 'ancestry IN ({ancestry:array}) and deleted = 0 and type =1 and level =0',
                    'bind'       => ['ancestry' => $coo_department_id_arr]
                ]
            );
            if ($item) {
                $department_arr = $item->toArray();
                foreach ($department_arr as $d_key=>$d_value){
                    $data[] = [
                        'cost_company_id'=>$d_value['id'],
                        'cost_company_name'=>$d_value['name'],
                    ];
                }
            }
        }
        return $data;
    }
    /**
     * sap收货员ID
     * */
    public function getPartId()
    {
        $country_code = get_country_code();
        $part_ids     = EnvModel::getEnvByCode("sap_part_ids");
        $part_ids     = json_decode($part_ids, true);
        return $part_ids[$country_code] ?? 7000019;
    }

    /**
     * 货主信息
     * */
    public function scmCargoOwner($type)
    {
        $redis = $this->getDI()->get("redis");

        $key = self::SCM_CARGO_OWNER_KEY . '_' . $type;
        $scm_cargo_owner = $redis->get($key);
        if (empty($scm_cargo_owner)) {
            $scm_cargo_owner = ScmCargoOwnerModel::find([
                'conditions' => 'type =:type:',
                'bind'       => ['type' => $type]
            ]);
            
            if (empty($scm_cargo_owner)) {
                return [];
            }
            $scm_cargo_owner = $scm_cargo_owner->toArray();

            $redis->setex($key, 86400, json_encode($scm_cargo_owner));
            return $scm_cargo_owner;
        }

        return json_decode($scm_cargo_owner, true);
    }


    /**
     * 货主枚举配置
     * */

    public function cargoOwnerEnums($type)
    {
        $cargo_owner_enums = [];
        $cargo_owner_res   = $this->scmCargoOwner($type);

        if (!empty($cargo_owner_res)) {
            foreach ($cargo_owner_res as $key => $value) {
                $cargo_owner_enums[] = [
                    'name' => $value['name'],
                    'code' => $value['mach_code']
                ];
            }
        }

        return $cargo_owner_enums;

    }
    /**
     * 物料编码wrs_code
     * */
    public function wrsCodeList($name_key)
    {
        $code_list = PurchaseTypeWrsCode::find(
            [
                'columns'    => 'id,wrs_code',
                'conditions' => 'wrs_code LIKE :wrs_code:',
                'bind'       => [
                    'wrs_code' => '%' . $name_key . '%',
                ],
                'limit'      => 50,
            ]
        )->toArray();
        $code_data = [];
        if (!empty($code_list)) {
            foreach ($code_list as $c) {
                $row['id']       = $c['id'];
                $row['name_key'] = $c['wrs_code'];
                $code_data[]     = $row;
            }
        }

        return $code_data;
    }

    /**
     * 获取启用中的标准型号barcode列表
     * @param string $name_key barcode
     * @return array
     */
    public function purchaseProductList($name_key)
    {
        $product_list = StandardService::getInstance()->getOpenBarcodeList($name_key);
        if (empty($product_list)) {
            return [];
        }
        return $product_list;
    }

    /**
     * 物料编码和核算科目映射关系
     * */
    public function wrsCodeAccountRelation()
    {

        $data      = [];
        $code_list = PurchaseTypeWrsCode::find(
            [
                'columns'    => 'wrs_code,ledger_account_id',
                'conditions' => 'is_delete = 0',

            ]
        )->toArray();

        $ledgers = LedgerAccountModel::find([
            'columns' => 'id,account',
        ])->toArray();
        $ledgers = array_column($ledgers, 'id', 'account');

        foreach ($code_list as $key => $value) {
            $data[] = [
                'wrs_code'          => $value['wrs_code'],
                'ledger_account_id' => $ledgers[$value['ledger_account_id']]??'0',

            ];
        }

        return $data;

    }

    /**
     * 样品确认
     * 送样原因枚举
     * */
    public function sampleSendReason()
    {
        $data        = [];
        $reason_list = PurchaseEnums::$send_reason;

        if (!empty($reason_list)) {
            foreach ($reason_list as $k=> $v) {
                $data[] = [
                    'id'   => $k,
                    'name' => static::$t->_($v)
                ];
            }

        }
        return $data;
    }

    /**
     * 根据 语言环境 获取对应字段名
     * @param $lang
     * @return string
     */
    protected function get_lang_column($lang = 'th'){
        $lang_arr = array(
            'en' => 'name_en',
            'th' => 'name_th',
            'zh-CN' => 'name_cn',
            'zh' => 'name_cn'
        );
        return empty($lang_arr[$lang]) ? '' : $lang_arr[$lang];
    }
    /**
     * 样品合格枚举
     * */
    public function sampleQualified()
    {
        $data = [];
        foreach (self::$qualified as $key => $value) {
            $data[] = [
                'id'   => $key,
                'name' => static::$t->_($value)
            ];
        }

        return $data;
    }

    private function getLang()
    {
        $lang = self::$language;
        if (empty($lang) || !in_array($lang, ["th", "en", "zh-CN"], 1)) {
            $lang = "en";
        }
        return $lang;
    }

    /**
     * 验收类别枚举
     * */
    public function acceptanceCategory()
    {
        $data        = [];
        $reason_list = PurchaseEnums::$acceptance_order_type;

        if (!empty($reason_list)) {
            foreach ($reason_list as $k => $v) {
                if (get_country_code() != Enums\GlobalEnums::TH_COUNTRY_CODE && $k == PurchaseEnums::ACCEPTANCE_TYPE_9) {
                    continue;
                }
                $data[] = [
                    'id'   => $k,
                    'name' => static::$t->_($v)
                ];
            }

        }
        return $data;
    }

    /**
     * 验收类型枚举
     * */
    public function acceptanceType()
    {
        $data        = [];
        $reason_list = PurchaseEnums::$acceptance_type;

        if (!empty($reason_list)) {
            foreach ($reason_list as $k=> $v) {
                $data[] = [
                    'id'   => $k,
                    'name' => static::$t->_($v)
                ];
            }

        }
        return $data;
    }

    /**
     * 验收结果类型枚举
     * */
    public function acceptanceResult()
    {
        $data        = [];
        $reason_list = PurchaseEnums::$check_result_type;

        if (!empty($reason_list)) {
            foreach ($reason_list as $k=> $v) {
                $data[] = [
                    'id'   => $k,
                    'name' => static::$t->_($v)
                ];
            }
        }
        return $data;
    }

    public static function handleParams($params, $not_must)
    {
        if (empty($not_must) || !is_array($not_must)) {
            return $params;
        }
        foreach ($not_must as $value) {
            if (isset($params[$value]) && empty($params[$value])) {
                unset($params[$value]);
            }
        }
        return $params;
    }

    /**
     * 记录采购相关操作日志记录表
     * @param integer $biz_type 采购业务类型
     * @param integer $biz_value 采购主数据ID
     * @param string $no 采购编号
     * @param array $before 修改前
     * @param array $after 修改后
     * @param array $user 操作者信息组
     * @throws BusinessException
     *
     * @return mixed
     */
    public function saveUpdateLog($biz_type, $biz_value, $no, $before, $after, $user)
    {
        $data = [];
        $data['biz_type'] = $biz_type;
        $data['biz_value'] = $biz_value;
        $data['no'] = $no;
        $data['meta_id'] = $before['id'];
        $data['before'] = json_encode($before, JSON_UNESCAPED_UNICODE);
        $data['after'] = json_encode($after, JSON_UNESCAPED_UNICODE);
        $data['created_id'] = $user['id'];
        $data['created_name'] = $user['name'];
        $data['created_at'] = date("Y-m-d H:i:s");
        $purchase_update_log_model = new PurchaseUpdateLogModel();
        $save_update_log_res = $purchase_update_log_model->create($data);
        if ($save_update_log_res === false) {
            throw new BusinessException('采购-修改记录添加失败, 原因可能是: ' . get_data_object_error_msg($purchase_update_log_model) . '; 待入表数据为: ' . json_encode($data, JSON_UNESCAPED_UNICODE), ErrCode::$BUSINESS_ERROR);
        }

        return $save_update_log_res;
    }
}
