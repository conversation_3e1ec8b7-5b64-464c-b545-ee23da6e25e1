<?php
/**
 * 仓库管理 - 仓库线索管理
 */

namespace App\Modules\Warehouse\Services;

use App\Library\Enums;
use App\Library\Enums\ByWorkflowEnums;
use App\Library\Enums\DownloadCenterEnum;
use App\Library\Enums\WarehouseEnums;
use App\Library\Enums\GlobalEnums;
use App\Library\Enums\ContractEnums;
use App\Library\ErrCode;
use App\Library\Exception\BusinessException;
use App\Library\Validation\Validation;
use App\Library\Validation\ValidationException;
use App\Models\oa\ContractWarehouseModel;
use App\Models\oa\SysAttachmentModel;
use App\Models\oa\WarehouseRequirementModel;
use App\Models\oa\WarehouseStoreModel;
use App\Models\oa\WarehouseThreadModel;
use App\Models\oa\WarehouseThreadPriceRecordModel;
use App\Models\oa\WarehouseThreadVerifyRecordModel;
use App\Modules\Common\Services\DownloadCenterService;
use App\Modules\Common\Services\EnumsService;
use App\Modules\Contract\Models\ContractStoreRentingDetailModel;
use App\Modules\Contract\Models\ContractStoreRentingModel;
use App\Modules\Third\Services\ByWorkflowService;
use App\Repository\oa\ByWorkflowBusinessRelRepository;
use App\Repository\oa\ContractWarehouseRepository;
use App\Repository\oa\SysAttachmentRepository;
use App\Repository\oa\WarehouseRequirementRepository;
use App\Repository\oa\WarehouseThreadRepository;
use App\Repository\StoreRepository;
use App\Util\RedisKey;
use Exception;
use GuzzleHttp\Exception\GuzzleException;

class ThreadService extends BaseService
{
    //列表类型
    const THREAD_LIST = 1;       //仓库线索管理-列表
    const THREAD_PRICE_LIST = 2; //处理仓库线索-待报价
    const THREAD_SIGN_LIST = 3;  //处理仓库线索-待签约
    const THREAD_VERIFY_LIST = 4;//处理仓库线索-待验证

    //列表与导出类型映射
    public static $task_type = [
        self::THREAD_LIST       => DownloadCenterEnum::FINANCE_WAREHOUSE_THREAD_EXPORT,       //仓库线索管理-导出
        self::THREAD_PRICE_LIST => DownloadCenterEnum::FINANCE_WAREHOUSE_THREAD_PRICE_EXPORT, //处理仓库线索-待报价-导出
        self::THREAD_SIGN_LIST  => DownloadCenterEnum::FINANCE_WAREHOUSE_THREAD_SIGN_EXPORT,  //处理仓库线索-待签约-导出
    ];

    private static $instance;

    private function __construct()
    {
    }

    private function __clone()
    {
    }

    public static function getInstance()
    {
        if (!isset(self::$instance) || !(self::$instance instanceof self)) {
            self::$instance = new self;
        }
        return self::$instance;
    }

    //仓库线索管理-新增-参数验证
    public static $validate_add = [
        'no'                    => 'Required|StrLenGeLe:1,32',
        'requirement_id'        => 'Required|IntGt:0',                                  //需求表主键ID
        'staff_id'              => 'Required|IntGt:0',                                  //寻仓人工号
        'staff_name'            => 'Required|StrLenGeLe:1,50',                          //寻仓人姓名
        'sys_department_id'     => 'Required|IntGt:0',                                  //寻仓人所属一级部门ID
        'sys_department_name'   => 'Required|StrLenGeLe:1,50',                          //寻仓人一级部门名称
        'node_department_id'    => 'Required|IntGt:0',                                  //寻仓人所属部门ID
        'node_department_name'  => 'Required|StrLenGeLe:1,50',                          //寻仓人所属部门名称
        'job_id'                => 'Required|IntGt:0',                                  //寻仓人职位ID
        'job_name'              => 'Required|StrLenGeLe:1,255',                         //寻仓人职位名称
        'warehouse_address'     => 'Required|StrLenGeLe:1,200',                         //仓库地址
        'contract_period'       => 'Required|IntGtLe:0,999999',                         //合同期
        'contract_period_unit'  => 'Required|IntIn:' . WarehouseEnums::THREAD_CONTRACT_PERIOD_UNIT_VALIDATE,//合同期单位
        'deposit'               => 'Required|FloatGeLe:0,99.99',                                            //押金
        'down_payment'          => 'Required|FloatGeLe:0,99.99',                                            //预付金
        'landlord_type'         => 'Required|IntIn:' . WarehouseEnums::THREAD_LANDLORD_TYPE_VALIDATE,      //房东类型
        'landlord_name'         => 'Required|StrLenGeLe:1,50',                                             //房东联系人
        'landlord_mobile'       => 'Required|StrLenGeLe:1,50',                                             //房东联系号码
        'landlord_email'        => 'Required|StrLenGeLe:1,50',                                             //房东联系邮箱
        'landlord_aptitude'     => 'Required|IntIn:' . WarehouseEnums::THREAD_LANDLORD_APTITUDE_VALIDATE,  //房东资质文件是否齐全
        'remark'                => 'StrLenGeLe:0,500',                                                     //备注
        'attachments'           => 'Arr|ArrLenGeLe:0,20',                                                  //附件
        'plan_attachments'      => 'Arr|ArrLenGeLe:0,20',                                                  //平面图
    ];

    //非必须参数
    public static $not_must_params = [
        'status',
        'sys_department_id',
        'staff_id',
        'pageNum',
        'pageSize'
    ];

    //仓库需求管理-列表-参数验证
    public static $validate_list = [
        'no'                => 'Str',
        'requirement_no'    => 'Str',                                            //需求ID
        'status'            => 'Arr',                                            //线索状态
        'status[*]'         => 'IntIn:' . WarehouseEnums::THREAD_STATUS_VALIDATE,//线索状态
        'store_id'          => 'Str',                                            //需求网点
        'sys_department_id' => 'IntGt:0',                                        //寻仓人部门
        'staff_id'          => 'IntGt:0',                                        //寻仓人
        'warehouse_id'      => 'Str',                                            //关联仓库
        'pageNum'           => 'IntGt:0',
        'pageSize'          => 'IntGt:0',
    ];

    //ID
    public static $validate_id = [
        'id' => 'Required|IntGt:0',
    ];

    //仓库线索管理-作废-参数验证
    public static $validate_cancel = [
        'id'            => 'Required|IntGt:0',
        'cancel_reason' => 'Required|StrLenGeLe:1,500',
    ];

    //仓库需求管理-查看-关联线索-转移/处理仓库需求-待寻找-录入线索-关联线索-转移
    public static $validate_transfer = [
        'id'             => 'Required|IntGt:0',
        'requirement_id' => 'Required|IntGt:0',//需求表主键ID
    ];

    //处理仓库线索-待验证-转交
    public static $validate_verify_transfer = [
        'id'                 => 'Required|IntGt:0',
        'person_liable_id'   => 'Required|IntGt:0',
        'person_liable_name' => 'Required|StrLenGeLe:1,50',
    ];

    //处理仓库线索-待验证-转交
    public static $validate_verify_done = [
        'id'                              => 'Required|IntGt:0',
        'verify_staff_id'                 => 'Required|IntGt:0',
        'verify_staff_name'               => 'Required|StrLenGeLe:1,50',
        'remark'                          => 'StrLenGeLe:0,500',
        'door_attachments'                => 'Required|Arr|ArrLenGeLe:1,20',
        'door_attachments[*]'             => 'Required|Obj',
        'door_attachments[*].bucket_name' => 'Required|StrLenGeLe:1,63',
        'door_attachments[*].object_key'  => 'Required|StrLenGeLe:1,100',
        'door_attachments[*].file_name'   => 'Required|StrLenGeLe:1,200',
        'road_attachments'                => 'Required|Arr|ArrLenGeLe:1,20',
        'road_attachments[*]'             => 'Required|Obj',
        'road_attachments[*].bucket_name' => 'Required|StrLenGeLe:1,63',
        'road_attachments[*].object_key'  => 'Required|StrLenGeLe:1,100',
        'road_attachments[*].file_name'   => 'Required|StrLenGeLe:1,200',
    ];

    //处理仓库线索-待报价-去报价、处理仓库线索-待签约-重新报价、仓库信息管理-续签报价-获得默认值
    // 处理仓库需求-待续约-续约报价
    public static $validate_add_price_default = [
        'source_type'       => 'Required|IntIn:' . WarehouseEnums::THREAD_PRICE_SOURCE_VALIDATE,
        'thread_id'         => 'IfIntEq:source_type,' . WarehouseEnums::THREAD_PRICE_SOURCE_TYPE_THREAD . '|Required|IntGt:0',
        'warehouse_main_id' => 'IfIntEq:source_type,' . WarehouseEnums::THREAD_PRICE_SOURCE_TYPE_WAREHOUSE . '|Required|IntGt:0',
        'requirement_id'    => 'IfIntEq:source_type,' . WarehouseEnums::THREAD_PRICE_SOURCE_TYPE_RM_RENEWED . '|Required|IntGt:0',
    ];

    //处理仓库线索-待报价-去报价、处理仓库线索-待签约-重新报价、仓库信息管理-续签报价-同区域已租赁仓库平均价格
    public static $validate_leased_average_price = [
        'province_code'  => 'Required|StrLenGeLe:1,10',//省编码
        'city_code'      => 'Required|StrLenGeLe:1,10',//市编码
    ];

    //处理仓库线索-待报价-去报价、处理仓库线索-待签约-重新报价、仓库信息管理-续签报价-上次报价信息
    public static $validate_warehouse_id = [
        'warehouse_id' => 'Required|StrLenGeLe:1,32'
    ];

    //处理仓库线索-待报价-去报价、处理仓库线索-待签约-重新报价、仓库信息管理-续签报价-添加报价
    public static $validate_add_price = [
        'no'                             => 'Required|StrLenGeLe:1,32', //报价单号
        'store_id'                       => 'Required|StrLenGeLe:1,10', //网点编号
        'store_name'                     => 'Required|StrLenGeLe:1,50', //网点名称
        'apply_type'                     => 'Required|IntIn:' . WarehouseEnums::THREAD_APPLY_TYPE_VALIDATE, //申请类型
        'origin_warehouse_main_id'       => 'IfIntIn:apply_type,' . WarehouseEnums::THREAD_APPLY_TYPE_RENEW . ',' . WarehouseEnums::THREAD_APPLY_TYPE_REPLACE . '|Required|IntGt:0', //原仓库（仓库表主键ID）
        'origin_warehouse_id'            => 'IfIntIn:apply_type,' . WarehouseEnums::THREAD_APPLY_TYPE_RENEW . ',' . WarehouseEnums::THREAD_APPLY_TYPE_REPLACE . '|Required|StrLenGeLe:1,32', //原仓库ID
        'origin_warehouse_name'          => 'IfIntIn:apply_type,' . WarehouseEnums::THREAD_APPLY_TYPE_RENEW . ',' . WarehouseEnums::THREAD_APPLY_TYPE_REPLACE . '|Required|StrLenGeLe:1,128', //原仓库名称
        'leased_warehouse_average_price' => 'Required|StrLenGeLe:1,255', //同区域已租赁仓库平均价格(元/月/m²)
        'lease_warehouse_price'          => 'Required|FloatGt:0|Regexp:' . WarehouseEnums::PRICE, //本次租赁仓库价格(元/月/m²)
        'warehouse_address'              => 'Required|StrLenGeLe:1,200', //仓库地址
        'contract_period'                => 'Required|IntGtLe:0,999999', //合同期
        'contract_period_unit'           => 'Required|IntIn:' . WarehouseEnums::THREAD_CONTRACT_PERIOD_UNIT_VALIDATE, //合同期单位
        'deposit'                        => 'Required|FloatGeLe:0,99.99',                                            //押金
        'down_payment'                   => 'Required|FloatGeLe:0,99.99',                                            //预付金
        'landlord_type'                  => 'Required|IntIn:' . WarehouseEnums::THREAD_LANDLORD_TYPE_VALIDATE, //房东类型
        'landlord_name'                  => 'Required|StrLenGeLe:1,50', //房东联系人
        'landlord_aptitude'              => 'Required|IntIn:' . WarehouseEnums::THREAD_LANDLORD_APTITUDE_VALIDATE, //房东资质文件是否齐全
        'warehouse_evaluate'             => 'Required|StrLenGeLe:1,500', //仓库评估
        'remark'                         => 'StrLenGeLe:0,1000', //备注
        'attachments'                    => 'Arr|ArrLenGeLe:0,20',//附件
    ];

    /**
     * 仓库需求枚举
     * @return array
     */
    public function getOptionsDefault()
    {
        $code    = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $data    = [];
        try {
            $data = $this->getEnums();
        } catch (\Exception $e) {
            $code         = ErrCode::$SYSTEM_ERROR;
            $message      = static::$t->_('retry_later');
            $real_message = $e->getMessage();
            $this->logger->warning('仓库管理-仓库线索管理-获取枚举异常: ' . $real_message);
        }
        return [
            'code'    => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data'    => $data,
        ];
    }

    /**
     * 获取线索状态、合同期单位、房东类型、房东资质文件是否齐全、仓库是否有CR、仓库电供应、仓库水供应、验证-供应情况、申请类型
     * @return array
     */
    public function getEnums()
    {
        $data      = [];
        $enums_arr = [
            'status'               => WarehouseEnums::$thread_status,
            'contract_period_unit' => WarehouseEnums::$contract_period_unit,
            'landlord_type'        => WarehouseEnums::$landlord_type,
            'landlord_aptitude'    => WarehouseEnums::$landlord_aptitude,
            'thread_has'           => WarehouseEnums::$thread_has,
            'thread_verify_state'  => WarehouseEnums::$thread_verify_state,
            'apply_type'           => WarehouseEnums::$thread_price_apply_type,
            'rent_payment_method'              => WarehouseEnums::$thread_rent_payment_method,
            'water_bill_payment_type'          => WarehouseEnums::$thread_water_bill_payment_type,
            'electricity_bill_payment_type'    => WarehouseEnums::$thread_electricity_bill_payment_type,
            'water_billing_method'             => WarehouseEnums::$thread_water_billing_method,
            'electricity_billing_method'       => WarehouseEnums::$thread_electricity_billing_method,
            'is_install_fire_extinguishers'    => WarehouseEnums::$thread_install_fire_extinguishers,
            'withholding_tax_liability_bearer' => WarehouseEnums::$thread_withholding_tax_liability_bearer,
            'land_tax_liability_bearer'        => WarehouseEnums::$thread_land_tax_liability_bearer,
            'stamp_duty_liability_bearer'      => WarehouseEnums::$thread_stamp_duty_liability_bearer,
        ];
        foreach ($enums_arr as $key => $value) {
            foreach ($value as $k => $v) {
                $data[$key][] = [
                    'value' => $k,
                    'label' => static::$t->_($v),
                ];
            }
        }

        return $data;
    }

    /**
     * 关联仓库
     * @param array $params 参数组
     * @return array
     */
    public function getRelateWarehouseList(array $params)
    {
        $code    = ErrCode::$SUCCESS;
        $message = 'success';
        $item    = [];

        try {
            $item = ContractWarehouseRepository::getInstance()->onlySearchWarehouse($params);
        } catch (Exception $e) {
            $code    = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $this->logger->error('仓库管理-仓库线索管理-关联仓库-搜索失败:' . $e->getMessage());
        }

        return [
            'code'    => $code,
            'message' => $message,
            'data'    => $item,
        ];
    }

    /**
     * 关联需求 - 待寻找
     * @param array $params 参数组
     * @return array
     */
    public function getRelateRequirementList(array $params)
    {
        $code    = ErrCode::$SUCCESS;
        $message = 'success';
        $item    = [];

        try {
            $params['status'] = WarehouseEnums::REQUIREMENT_STATUS_SEARCH;
            $item             = WarehouseRequirementRepository::getInstance()->onlySearchRequirement($params);
        } catch (Exception $e) {
            $code    = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $this->logger->error('仓库管理-仓库线索管理-关联需求-搜索失败:' . $e->getMessage());
        }

        return [
            'code'    => $code,
            'message' => $message,
            'data'    => $item,
        ];
    }

    /**
     * 仓库线索添加-获得默认值
     * @return array
     */
    public function getAddDefault()
    {
        $code    = ErrCode::$SUCCESS;
        $message = 'success';

        $data = [];
        try {
            $data['no'] = static::genSerialNo(WarehouseEnums::THREAD_NO_PREFIX, RedisKey::WAREHOUSE_THREAD_ADD_COUNTER);
        } catch (\Exception $e) {
            $code    = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $this->logger->warning('仓库管理-仓库线索管理-添加-获得默认值:' . $e->getMessage());
        }

        return [
            'code'    => $code,
            'message' => $message,
            'data'    => $data,
        ];
    }

    /**
     * 获取仓库面积以及各类费用验证规则
     * @param array $validation 验证规则组
     * @return mixed
     */
    private function getAddPriceValidation(array $validation)
    {
        $country_code = get_country_code();
        if ($country_code == GlobalEnums::PH_COUNTRY_CODE) {
            $validation['cusa_fee']        = 'Required|Regexp:' . WarehouseEnums::PRICE . '|>>>:' . static::$t->_('warehouse_thread_price_error');
            $validation['maintenance_fee'] = 'Required|Regexp:' . WarehouseEnums::PRICE . '|>>>:' . static::$t->_('warehouse_thread_price_error');
            $validation['entrance_fee']    = 'Required|Regexp:' . WarehouseEnums::PRICE . '|>>>:' . static::$t->_('warehouse_thread_price_error');
            $validation['garbage_fee']     = 'Required|Regexp:' . WarehouseEnums::PRICE . '|>>>:' . static::$t->_('warehouse_thread_price_error');
            $validation['parking_fee']     = 'Required|Regexp:' . WarehouseEnums::PRICE . '|>>>:' . static::$t->_('warehouse_thread_price_error');
            $validation['other_fee']       = 'Required|Regexp:' . WarehouseEnums::PRICE . '|>>>:' . static::$t->_('warehouse_thread_price_error');

        } elseif ($country_code == GlobalEnums::TH_COUNTRY_CODE) {
            $rent_payment_methods              = implode(',', array_keys(WarehouseEnums::$thread_rent_payment_method));
            $validation['rent_payment_method'] = "Required|Arr|>>>:" . static::$t->_('params_error', ['param' => 'rent_payment_method']);
            $validation['rent_payment_method[*]'] = "Required|IntIn:{$rent_payment_methods}|>>>:" . static::$t->_('params_error', ['param' => 'rent_payment_method']);

            $validation['water_bill_payment_type']       = 'Required|IntIn:1,2|>>>:' . static::$t->_('params_error', ['param' => 'water_bill_payment_type']);
            $validation['electricity_bill_payment_type'] = 'Required|IntIn:1,2|>>>:' . static::$t->_('params_error', ['param' => 'electricity_bill_payment_type']);
            $validation['water_billing_method']          = 'Required|IntIn:1,2|>>>:' . static::$t->_('params_error', ['param' => 'water_billing_method']);
            $validation['water_usage_units']             = 'Required|FloatGeLe:0,*********.99|>>>:' . static::$t->_('params_error', ['param' => 'water_usage_units']);

            $validation['electricity_billing_method'] = 'Required|IntIn:1,2|>>>:' . static::$t->_('params_error', ['param' => 'electricity_billing_method']);
            $validation['electricity_usage_units']    = 'Required|FloatGeLe:0,*********.99|>>>:' . static::$t->_('params_error', ['param' => 'electricity_usage_units']);

            $validation['is_install_fire_extinguishers'] = 'Required|IntIn:1,2|>>>:' . static::$t->_('params_error', ['param' => 'is_install_fire_extinguishers']);

            $validation['withholding_tax_liability_bearer'] = 'Required|IntIn:1,2|>>>:' . static::$t->_('params_error', ['param' => 'withholding_tax_liability_bearer']);
            $validation['land_tax_liability_bearer']        = 'Required|IntIn:1,2|>>>:' . static::$t->_('params_error', ['param' => 'land_tax_liability_bearer']);
            $validation['stamp_duty_liability_bearer']      = 'Required|IntIn:1,2|>>>:' . static::$t->_('params_error', ['param' => 'stamp_duty_liability_bearer']);

            $validation['surrounding_stores_average_rent'] = 'Required|FloatGeLe:0,*********.99|>>>:' . static::$t->_('params_error', ['param' => 'surrounding_stores_average_rent']);
            $validation['other_options_average_cost']      = 'Required|FloatGeLe:0,*********.99|>>>:' . static::$t->_('params_error', ['param' => 'other_options_average_cost']);
        }

        $validation['warehouse_month_rent'] = 'Required|FloatGt:0|Regexp:' . WarehouseEnums::PRICE;//仓库每月租价
        
        return $validation;
    }

    /**
     * 添加参数拦截
     * @param array $params 参数组
     * @throws ValidationException
     */
    public function addValidation(array $params)
    {
        $validation = self::$validate_add;
        if (get_country_code() == GlobalEnums::PH_COUNTRY_CODE) {
            $validation['warehouse_cr']          = 'Required|IntIn:' . WarehouseEnums::THREAD_HAS_VALIDATE;//仓库是否有CR
            $validation['warehouse_electricity'] = 'Required|IntIn:' . WarehouseEnums::THREAD_HAS_VALIDATE;//仓库电供应
            $validation['warehouse_water']       = 'Required|IntIn:' . WarehouseEnums::THREAD_HAS_VALIDATE;//仓库水供应
        }

        $validation  = $this->getAddPriceValidation($validation);
        $validation['warehouse_area']  = 'Required|Regexp:' . WarehouseEnums::AREA_RULE . '|>>>:' . static::$t->_('warehouse_requirement_area_error');

        self::commonValidate($params, $validation);
    }

    /**
     * 获取线索信息
     * @param integer $id 线索ID
     * @return mixed
     * @throws ValidationException
     */
    public function getThreadInfo(int $id)
    {
        $thread_info = WarehouseThreadModel::findFirst([
            'conditions' => 'id = :id:',
            'bind'       => ['id' => $id],
        ]);
        if (empty($thread_info)) {
            throw new ValidationException(static::$t->_('warehouse_thread_not_exists'), ErrCode::$VALIDATE_ERROR);
        }
        return $thread_info;
    }

    /**
     * 不允许重复，新增保存时如果与现有数据中的编号重复，则自动取库中当前日期最大的序号+1，不需要拦截提交
     * @param string $no 线索编号
     * @return string
     * @throws BusinessException
     * @throws ValidationException
     */
    private function getThreadNo(string $no)
    {
        // 验证单号是否已创建 或 占用
        $exist_no   = WarehouseThreadModel::findFirst([
            'conditions' => 'no = :no:',
            'bind'       => ['no' => $no],
            'columns'    => ['id'],
        ]);
        $make_count = 0;
        if (!empty($exist_no)) {
            $no = static::genSerialNo(WarehouseEnums::THREAD_NO_PREFIX, RedisKey::WAREHOUSE_THREAD_ADD_COUNTER);
            $make_count++;
            $thread_nos[] = $no;
            if ($make_count >= 3) {
                throw new BusinessException("仓库线索编号生成已重试 {$make_count} 次, 请检查, 尝试生成的: " . json_encode($thread_nos), ErrCode::$BUSINESS_ERROR);
            }
            return $this->getThreadNo($no);
        }
        return $no;
    }

    /**
     * 仓库线索添加
     * @param array $params 参数组
     * @param array $user 当前登陆者信息组
     * @return array
     */
    public function add(array $params, array $user)
    {
        $code    = ErrCode::$SUCCESS;
        $message = $real_message = '';

        $db = $this->getDI()->get('db_oa');
        $db->begin();
        try {
            self::addValidation($params);
            $params['no'] = $this->getThreadNo($params['no']);
            $this->saveThreadDb($db, $params, $user);
            $db->commit();
        } catch (ValidationException $e) {
            $code    = $e->getCode();
            $message = $e->getMessage();
        } catch (BusinessException $e) {
            $code         = $e->getCode();
            $message      = static::$t->_('retry_later');
            $real_message = $e->getMessage() . $e->getTraceAsString();
        } catch (Exception $e) {
            $code         = ErrCode::$SYSTEM_ERROR;
            $message      = static::$t->_('retry_later');
            $real_message = $e->getMessage() . $e->getTraceAsString();
        }

        if ($message) {
            $db->rollback();
        }
        if ($real_message) {
            $this->logger->error('仓库管理-仓库线索管理-添加失败: ' . $real_message);
        }

        return [
            'code'    => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data'    => [],
        ];
    }

    /**
     * 仓库线索 编辑
     * @param array $params 参数组
     * @param array $user 当前登陆者信息组
     * @return array
     */
    public function edit(array $params, array $user)
    {
        $code    = ErrCode::$SUCCESS;
        $message = $real_message = '';

        $db = $this->getDI()->get('db_oa');
        $db->begin();
        try {
            Validation::validate($params, self::$validate_id);
            self::addValidation($params);
            $this->saveThreadDb($db, $params, $user, 'edit');
            $db->commit();
        } catch (ValidationException $e) {
            $code    = $e->getCode();
            $message = $e->getMessage();
        } catch (BusinessException $e) {
            $code         = $e->getCode();
            $message      = static::$t->_('retry_later');
            $real_message = $e->getMessage() . $e->getTraceAsString();
        } catch (Exception $e) {
            $code         = ErrCode::$SYSTEM_ERROR;
            $message      = static::$t->_('retry_later');
            $real_message = $e->getMessage() . $e->getTraceAsString();
        }

        if ($message) {
            $db->rollback();
        }
        if ($real_message) {
            $this->logger->error('仓库管理-仓库线索管理-编辑失败: ' . $real_message);
        }

        return [
            'code'    => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data'    => [],
        ];
    }

    /**
     * 保存线索
     * @param object $db 事物对象
     * @param array $params 参数组
     * @param array $user 当前登陆者信息组
     * @param string $type 操作类型 add添加，edit编辑
     * @throws BusinessException
     * @throws ValidationException
     */
    private function saveThreadDb(object $db, array $params, array $user, string $type = 'add')
    {
        //获取需求信息
        $requirement_info = RequirementService::getInstance()->getRequirementInfo($params['requirement_id']);
        //只有待寻找的需求才能做此操作
        if ($requirement_info->status != WarehouseEnums::REQUIREMENT_STATUS_SEARCH) {
            throw new ValidationException(static::$t->_('warehouse_requirement_status_error', ['status' => static::$t->_(WarehouseEnums::getRequirementStatus()[WarehouseEnums::REQUIREMENT_STATUS_SEARCH])]), ErrCode::$VALIDATE_ERROR);
        }

        $rent_payment_method = '';
        if (!empty($params['rent_payment_method'])) {
            $rent_payment_method = implode(',', array_unique($params['rent_payment_method']));
        }

        //意向地址距离计算规则 = 线索中的仓库经纬度与需求中意向经纬度的距离，保留2位小数，单位为KM
        $intention_address_distance = get_distance($params['warehouse_latitude'], $params['warehouse_longitude'], $requirement_info->warehouse_latitude, $requirement_info->warehouse_longitude);
        $now  = date('Y-m-d H:i:s');
        $data = [
            'requirement_id'             => $params['requirement_id'],
            'requirement_no'             => $requirement_info->no,
            'apply_date'                 => date('Y-m-d'),
            'staff_id'                   => $params['staff_id'],
            'staff_name'                 => $params['staff_name'],
            'sys_department_id'          => $params['sys_department_id'],
            'sys_department_name'        => $params['sys_department_name'],
            'node_department_id'         => $params['node_department_id'],
            'node_department_name'       => $params['node_department_name'],
            'job_id'                     => $params['job_id'],
            'job_name'                   => $params['job_name'],
            'warehouse_address'          => $params['warehouse_address'],
            'warehouse_latitude'         => $params['warehouse_latitude'],
            'warehouse_longitude'        => $params['warehouse_longitude'],
            'intention_address_distance' => $intention_address_distance,
            'warehouse_area'             => $params['warehouse_area'],
            'warehouse_month_rent'       => $params['warehouse_month_rent'],
            'contract_period'            => $params['contract_period'],
            'contract_period_unit'       => $params['contract_period_unit'],
            'deposit'                    => $params['deposit'],
            'down_payment'               => $params['down_payment'],
            'cusa_fee'                   => $params['cusa_fee'] ?? 0,
            'maintenance_fee'            => $params['maintenance_fee'] ?? 0,
            'entrance_fee'               => $params['entrance_fee'] ?? 0,
            'garbage_fee'                => $params['garbage_fee'] ?? 0,
            'parking_fee'                => $params['parking_fee'] ?? 0,
            'other_fee'                  => $params['other_fee'] ?? 0,
            'landlord_type'              => $params['landlord_type'],
            'landlord_name'              => $params['landlord_name'],
            'landlord_mobile'            => $params['landlord_mobile'],
            'landlord_email'             => $params['landlord_email'],
            'warehouse_cr'               => $params['warehouse_cr'] ?? 0,
            'warehouse_electricity'      => $params['warehouse_electricity'] ?? 0,
            'warehouse_water'            => $params['warehouse_water'] ?? 0,
            'landlord_aptitude'          => $params['landlord_aptitude'],
            'remark'                     => $params['remark'],
            'status'                     => WarehouseEnums::THREAD_STATUS_CONFIRM,
            'created_id'                 => $user['id'],
            'created_name'               => $user['name'],
            'updated_id'                 => $user['id'],
            'updated_name'               => $user['name'],
            'created_at'                 => $now,
            'updated_at'                 => $now,
            'rent_payment_method'              => $rent_payment_method,
            'water_bill_payment_type'          => $params['water_bill_payment_type'] ?? 0,
            'electricity_bill_payment_type'    => $params['electricity_bill_payment_type'] ?? 0,
            'water_billing_method'             => $params['water_billing_method'] ?? 0,
            'water_usage_units'                => $params['water_usage_units'] ?? 0,
            'electricity_billing_method'       => $params['electricity_billing_method'] ?? 0,
            'electricity_usage_units'          => $params['electricity_usage_units'] ?? 0,
            'is_install_fire_extinguishers'    => $params['is_install_fire_extinguishers'] ?? 0,
            'withholding_tax_liability_bearer' => $params['withholding_tax_liability_bearer'] ?? 0,
            'land_tax_liability_bearer'        => $params['land_tax_liability_bearer'] ?? 0,
            'stamp_duty_liability_bearer'      => $params['stamp_duty_liability_bearer'] ?? 0,
            'surrounding_stores_average_rent'  => $params['surrounding_stores_average_rent'] ?? 0,
            'other_options_average_cost'       => $params['other_options_average_cost'] ?? 0,
        ];

        if ($type == 'add') {
            //获取默认币种
            $default_currency = EnumsService::getInstance()->getSysCurrencyInfo();
            $model = new WarehouseThreadModel();
            $add_data = [
                'no'       => $params['no'],
                'currency' => $default_currency['code'],
            ];
            $data = array_merge($add_data, $data);
            $bool = $model->i_create($data);
            if ($bool === false) {
                throw new BusinessException('仓库管理-仓库线索管理-添加失败: ' . json_encode($data, JSON_UNESCAPED_UNICODE) . '; 可能的原因是：' . get_data_object_error_msg($model), ErrCode::$BUSINESS_ERROR);
            }
        } else {
            $model = $this->getThreadInfo($params['id']);
            //当线索状态为待确认、被淘汰、待关联时才可编辑
            if (!in_array($model->status, [WarehouseEnums::THREAD_STATUS_CONFIRM, WarehouseEnums::THREAD_STATUS_ELIMINATE, WarehouseEnums::THREAD_STATUS_ASSOCIATION])) {
                throw new ValidationException(static::$t->_('warehouse_thread_edit_status_error'), ErrCode::$VALIDATE_ERROR);
            }

            $bool  = $model->i_update($data);
            if ($bool === false) {
                throw new BusinessException('仓库管理-仓库线索管理-编辑失败: ' . json_encode($data, JSON_UNESCAPED_UNICODE) . '; 可能的原因是：' . get_data_object_error_msg($model), ErrCode::$BUSINESS_ERROR);
            }

            //删除原来的平面图附件、附件
            $oss_bucket_type = Enums::OSS_BUCKET_TYPE_WAREHOUSE_THREAD_PLAN_FILE . ',' . Enums::OSS_BUCKET_TYPE_WAREHOUSE_THREAD_INFO_FILE;
            $db->updateAsDict(
                (new SysAttachmentModel())->getSource(),
                ['deleted' => GlobalEnums::IS_DELETED],
                ['conditions' => "oss_bucket_key = {$model->id} and oss_bucket_type IN ({$oss_bucket_type})"]
            );
        }

        $attach_arr = [];
        //添加平面图
        if (!empty($params['plan_attachments'])) {
            foreach ($params['plan_attachments'] as $attachment) {
                $attach_arr[] = [
                    'oss_bucket_type' => Enums::OSS_BUCKET_TYPE_WAREHOUSE_THREAD_PLAN_FILE,
                    'oss_bucket_key'  => $model->id,
                    'sub_type'        => 0,
                    'bucket_name'     => $attachment['bucket_name'],
                    'object_key'      => $attachment['object_key'],
                    'file_name'       => $attachment['file_name'],
                ];
            }
        }

        //添加附件
        if (!empty($params['attachments'])) {
            foreach ($params['attachments'] as $attachment) {
                $attach_arr[] = [
                    'oss_bucket_type' => Enums::OSS_BUCKET_TYPE_WAREHOUSE_THREAD_INFO_FILE,
                    'oss_bucket_key'  => $model->id,
                    'sub_type'        => 0,
                    'bucket_name'     => $attachment['bucket_name'],
                    'object_key'      => $attachment['object_key'],
                    'file_name'       => $attachment['file_name'],
                ];
            }
        }

        if (!empty($attach_arr)) {
            $attachment_model = new SysAttachmentModel();
            if (!$attachment_model->batch_insert($attach_arr)) {
                throw new BusinessException('仓库管理-仓库线索管理-添加平面图或附件失败: ' . json_encode($attach_arr,
                        JSON_UNESCAPED_UNICODE) . '; 可能的原因是: ' . get_data_object_error_msg($attachment_model),
                    ErrCode::$BUSINESS_ERROR);
            }
        }
    }

    /**
     * 仓库线索列表
     * @param array $params 请求参数组
     * @param array $user 当前登陆者信息组
     * @param int $type 列表 1仓库线索管理-列表、2处理仓库线索-待报价、3处理仓库线索-待签约、4处理仓库线索-待验证
     * @return array
     */
    public function list(array $params, array $user, int $type = self::THREAD_LIST)
    {
        $page_size = empty($params['pageSize']) ? GlobalEnums::DEFAULT_PAGE_SIZE : (int)$params['pageSize'];
        $page_num  = empty($params['pageNum']) ? GlobalEnums::DEFAULT_PAGE_NUM : (int)$params['pageNum'];

        $code    = ErrCode::$SUCCESS;
        $message = 'success';
        $data    = [
            'items'      => [],
            'pagination' => [
                'current_page' => $page_num,
                'per_page'     => $page_size,
                'total_count'  => 0,
            ],
        ];

        try {
            $count = $this->getDataTotal($params, $user, $type);
            $items = [];
            if ($count) {
                $offset = $page_size * ($page_num - 1);
                $builder = $this->modelsManager->createBuilder();
                $builder->from(['main' => WarehouseThreadModel::class]);
                $builder->leftJoin(WarehouseRequirementModel::class, 'requirement.id = main.requirement_id', 'requirement');
                $builder->leftJoin(WarehouseThreadPriceRecordModel::class, 'price.thread_id = main.id', 'price');
                $builder = $this->getCondition($builder, $params, $user, $type);
                $columns = [
                    'main.id',
                    'main.no',
                    'main.status',
                    'main.requirement_id',
                    'main.requirement_no',
                    'requirement.store_id',
                    'requirement.store_name',
                    'requirement.province_code',
                    'requirement.city_code',
                    'requirement.district_code',
                    'main.warehouse_latitude',
                    'main.warehouse_longitude',
                    'main.warehouse_area',
                    'main.warehouse_month_rent',
                    'main.warehouse_cr',
                    'main.warehouse_electricity',
                    'main.warehouse_water',
                    'main.apply_date',
                    'main.warehouse_address',
                    'main.remark',
                    'main.staff_id',
                    'main.staff_name',
                    'main.job_id',
                    'main.job_name',
                    'main.sys_department_id',
                    'main.sys_department_name',
                    'main.is_install_fire_extinguishers',
                    'price.id as price_id'
                ];

                //处理仓库线索-待验证 - 不可看到金额相关信息
                if ($type != self::THREAD_VERIFY_LIST) {
                    $columns = array_merge($columns, [
                        'main.contract_period',
                        'main.contract_period_unit',
                        'main.deposit',
                        'main.down_payment',
                    ]);
                }

                if ($type == self::THREAD_LIST ) {
                    $sort = 'main.created_at DESC';
                } else if ($type == self::THREAD_VERIFY_LIST) {
                    $sort = 'verify.id ASC';
                } else {
                    $sort = 'main.no ASC';
                }
                $builder->columns($columns);
                $builder->orderby($sort);
                $builder->limit($page_size, $offset);
                $items = $builder->getQuery()->execute()->toArray();
                $items = $this->handleItems($items);
            }
            $data['pagination']['total_count'] = $count;
            $data['items']                     = $items;
        } catch (Exception $e) {
            $code    = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $this->logger->error('仓库管理-仓库线索管理-列表获取失败: ' . $e->getMessage());
        }

        return [
            'code'    => $code,
            'message' => $message,
            'data'    => $data,
        ];
    }

    /**
     * @param array $params 参数组
     * @param array $user 当前登陆者信息组
     * @param int $type 列表 1仓库线索管理-列表、2处理仓库线索-待报价、3处理仓库线索-待签约
     * @return array|mixed
     * @throws BusinessException
     * @throws GuzzleException
     */
    public function exportMethod(array $params, array $user, int $type = self::THREAD_LIST)
    {
        $excel_type = self::$task_type[$type];
        // 大于指定数量, 添加异步任务 导出
        if ($this->getDownloadDataTotal($params, $user, $type) > WarehouseEnums::SYNC_EXPORT_MAX_COUNT) {
            $result         = DownloadCenterService::getInstance()->addDownloadCenter($user['id'], $excel_type, $params);
            $result['data'] = [
                'export_method' => DownloadCenterEnum::EXPORT_METHOD_ASYNC,
                'file_url'      => '',
            ];
        } else {
            // 小于等于指定数量, 同步导出
            $params['pageSize'] = WarehouseEnums::SYNC_EXPORT_MAX_COUNT;
            $data               = $this->export($params, $user, $type);
            $result             = $this->generateInfoExportFile($data, $excel_type);
            $result['data']     = [
                'export_method' => DownloadCenterEnum::EXPORT_METHOD_SYNC,
                'file_url'      => is_string($result['data']) ? $result['data'] : '',
            ];
        }
        return $result;
    }

    /**
     * 获得列表总数或小红点
     * @param array $params 请求参数组
     * @param array $user 当前登陆者信息组
     * @param int $type 列表 1仓库线索管理-列表、2处理仓库线索-待报价、3处理仓库线索-待签约、4处理仓库线索-待验证
     * @return int
     */
    public function getDataTotal(array $params, array $user, int $type = self::THREAD_LIST)
    {
        $total_count = 0;
        try {
            $builder = $this->modelsManager->createBuilder();
            $builder->from(['main' => WarehouseThreadModel::class]);
            $builder->leftJoin(WarehouseRequirementModel::class, 'requirement.id = main.requirement_id', 'requirement');
            $builder     = $this->getCondition($builder, $params, $user, $type);
            $total_count = (int)$builder->columns('COUNT(main.id) AS total')->getQuery()->getSingleResult()->total;
        } catch (\Exception $e) {
            $this->logger->error('仓库管理-仓库线索管理-获得下载数据的总数失败: ' . $e->getMessage());
        }

        return $total_count;
    }

    /**
     * 获得下载数据查询对象
     * @param array $params 请求参数组
     * @param array $user 当前登陆者信息组
     * @param int $type 列表 1仓库线索管理、2处理仓库线索-待报价、3处理仓库线索-待签约
     * @return object
     */
    private function exportBuilder(array $params, array $user, int $type = self::THREAD_LIST)
    {
        $builder = $this->modelsManager->createBuilder();
        $builder->from(['main' => WarehouseThreadModel::class]);
        $builder->leftJoin(WarehouseRequirementModel::class, 'requirement.id = main.requirement_id', 'requirement');
        //仓库线索管理 - 需要获取验仓信息
        if ($type == self::THREAD_LIST) {
            $builder->leftJoin(ContractWarehouseModel::class, 'warehouse.id = main.warehouse_main_id', 'warehouse');
            $builder->leftJoin(WarehouseThreadVerifyRecordModel::class, 'verify.thread_id = main.id', 'verify');
        }
        return $this->getCondition($builder, $params, $user, $type);
    }

    /**
     * 获得下载数据的总数
     * @param array $params 请求参数组
     * @param array $user 当前登陆者信息组
     * @param int $type 列表 1仓库线索管理-列表、2处理仓库线索-待报价、3处理仓库线索-待签约
     * @return int
     */
    public function getDownloadDataTotal(array $params, array $user, int $type = self::THREAD_LIST)
    {
        $total_count = 0;
        try {
            $builder     = $this->exportBuilder($params, $user, $type);
            $total_count = (int)$builder->columns('COUNT(main.id) AS total')->getQuery()->getSingleResult()->total;
        } catch (\Exception $e) {
            $this->logger->error('仓库管理-仓库线索管理-获得下载数据的总数失败: ' . $e->getMessage());
        }

        return $total_count;
    }

    /**
     * 仓库线索导出
     * @param array $params 参数组
     * @param array $user 当前登陆者信息组
     * @param int $type 列表 1仓库线索管理-列表、2处理仓库线索-待报价、3处理仓库线索-待签约
     * @return array
     */
    public function export(array $params, array $user, int $type)
    {
        $page_size = empty($params['pageSize']) ? GlobalEnums::DEFAULT_PAGE_SIZE : (int)$params['pageSize'];
        $page_num  = empty($params['pageNum']) ? GlobalEnums::DEFAULT_PAGE_NUM : (int)$params['pageNum'];
        $builder   = $this->exportBuilder($params, $user, $type);
        $offset    = $page_size * ($page_num - 1);
        $columns   = [
            'main.id',
            'main.no',
            'main.staff_name',
            'main.staff_id',
            'main.sys_department_name',
            'main.job_name',
            'main.warehouse_address',
            'main.warehouse_latitude',
            'main.warehouse_longitude',
            'main.warehouse_area',
            'main.warehouse_month_rent',
            'main.contract_period',
            'main.contract_period_unit',
            'main.deposit',
            'main.down_payment',
            'main.cusa_fee',
            'main.maintenance_fee',
            'main.entrance_fee',
            'main.garbage_fee',
            'main.parking_fee',
            'main.other_fee',
            'main.currency',
            'main.rent_payment_method',
            'main.water_bill_payment_type',
            'main.electricity_bill_payment_type',
            'main.water_billing_method',
            'main.water_usage_units',
            'main.electricity_billing_method',
            'main.electricity_usage_units',
            'main.is_install_fire_extinguishers',
            'main.withholding_tax_liability_bearer',
            'main.land_tax_liability_bearer',
            'main.stamp_duty_liability_bearer',
            'main.surrounding_stores_average_rent',
            'main.other_options_average_cost',
            'main.landlord_type',
            'main.landlord_name',
            'main.landlord_mobile',
            'main.landlord_email',
            'main.warehouse_cr',
            'main.warehouse_electricity',
            'main.warehouse_water',
            'main.landlord_aptitude',
            'main.status',
            'main.cancel_reason',
            'main.requirement_id',
            'main.requirement_no',
            'requirement.store_name',
            'requirement.store_id',
            'requirement.store_category',
            'requirement.original_warehouse_id',
            'requirement.opening_status',
            'requirement.warehouse_type',
            'requirement.priority_level',
            'requirement.expect_settled_date',
            'requirement.province_code',
            'requirement.city_code',
            'requirement.district_code',
            'requirement.warehouse_longitude as requirement_warehouse_longitude',
            'requirement.warehouse_latitude as requirement_warehouse_latitude',
            'requirement.area_min',
            'requirement.area_max',
            'requirement.parking_num_min',
            'requirement.parking_num_max',
            'requirement.status as requirement_status',
            'requirement.cancel_reason as requirement_cancel_reason',
            'requirement.created_id as requirement_created_id',
            'requirement.created_name as requirement_created_name',
            'requirement.apply_date as requirement_apply_date',
            'requirement.remark as requirement_remark',
        ];

        //仓库线索管理 - 多导出几列
        if ($type == self::THREAD_LIST) {
            $columns = array_merge($columns, [
                'verify.id as verify_id',
                'verify.electricity',
                'verify.water',
                'verify.lighting',
                'verify.remark as verify_remark',
                'verify.verify_staff_id',
                'verify.verify_staff_name',
                'verify.verify_date',
                'verify.is_install_fire_extinguishers AS verify_is_install_fire_extinguishers',
                'requirement.actual_settled_store_id',
                'requirement.actual_settled_store_name',
                'requirement.actual_settled_date',
                'main.warehouse_id',
                'warehouse.warehouse_name'
            ]);
        }
        $builder->columns($columns);
        $builder->orderby('main.id DESC');
        $builder->limit($page_size, $offset);
        $items = $builder->getQuery()->execute()->toArray();
        return $this->handleItems($items, $type, true);
    }

    /**
     * 仓库管理 - 仓库线索管理 - 导出表头
     * @param array $data 导出数据
     * @param int $excel_type 1305 、1306、1307
     * @return array
     * @throws BusinessException
     * @throws GuzzleException
     */
    public function generateInfoExportFile(array $data, int $excel_type)
    {
        $excel_header = $this->getExportFileHeader($excel_type);

        $excel_data = [];
        foreach ($data as $value) {
            $excel_data[] = $value;
        }
        unset($data);

        $excel_file_name = str_replace( '{YmdHis}', date('YmdHis'), DownloadCenterEnum::$download_center_excel_setting_item[$excel_type]['file_name']);
        return $this->exportExcel($excel_header, $excel_data, $excel_file_name);
    }

    /**
     * 获取导出文件表头
     */
    public function getExportFileHeader($excel_type)
    {
        if (get_country_code() == GlobalEnums::TH_COUNTRY_CODE) {
            $excel_header = [
                static::$t['warehouse_thread_no'],                              //线索ID
                static::$t['warehouse_thread_staff_name'],                      //寻仓人姓名
                static::$t['warehouse_thread_staff_id'],                        //寻仓人工号
                static::$t['warehouse_export_018'],                             //寻仓人一级部门
                static::$t['warehouse_thread_job_id'],                          //寻仓人职位
                static::$t['warehouse_thread_warehouse_address'],               //仓库地址
                static::$t['warehouse_thread_longitude_latitude'],              //仓库经纬度
                static::$t['warehouse_thread_warehouse_area'],                  //仓库面积
                static::$t['warehouse_thread_warehouse_month_rent'],            //仓库每月租金
                static::$t['warehouse_thread_contract_period'],                 //合同期
                static::$t['warehouse_thread_deposit'],                         //押金
                static::$t['warehouse_thread_down_payment'],                    //预付金
                static::$t['warehouse_export_004'],                             // 租金支付方式
                static::$t['warehouse_export_005'],                             // 水费支付类型
                static::$t['warehouse_export_006'],                             // 电费支付类型
                static::$t['warehouse_export_007'],                             // 水费使用计费方式
                static::$t['warehouse_export_008'],                             // 水费使用单位数
                static::$t['warehouse_export_009'],                             // 电费使用计费方式
                static::$t['warehouse_export_010'],                             // 电费使用单位数
                static::$t['warehouse_export_011'],                             // 是否安装灭火器
                static::$t['warehouse_export_012'],                             // 扣缴税责任承担方
                static::$t['warehouse_export_013'],                             // 土地税责任承担方
                static::$t['warehouse_export_014'],                             // 印花税责任承担方
                static::$t['warehouse_export_015'],                             // 周边门店平均租金
                static::$t['warehouse_export_016'],                             // 其他选址方案平均费用
                static::$t['warehouse_thread_landlord_type'],                   //房东类型
                static::$t['warehouse_thread_landlord_name'],                   //房东联系人
                static::$t['warehouse_thread_landlord_mobile'],                 //房东联系号码
                static::$t['warehouse_thread_landlord_email'],                  //房东联系邮箱
                static::$t['warehouse_thread_landlord_aptitude'],               //房东资质文件是否齐全
                static::$t['warehouse_thread_plan_attachment'],                 //平面图
                static::$t['warehouse_thread_attachment'],                      //仓库线索附件
                static::$t['warehouse_thread_status'],                          //线索状态
                static::$t['warehouse_thread_cancel_reason'],                   //线索作废原因
                static::$t['warehouse_requirement_no'],                         // 需求ID
                static::$t['warehouse_requirement_store_name'],                 // 需求网点名称
                static::$t['warehouse_requirement_store_id'],                   // 需求网点编码
                static::$t['warehouse_export_001'],                             // 需求网点类型
                static::$t['warehouse_requirement_opening_status'],             //创建仓库需求时网点开业状态
                static::$t['warehouse_requirement_warehouse_type'],             //仓库类型
//                static::$t['warehouse_export_002'],                             // 原有仓库名称
                static::$t['warehouse_requirement_priority_level'],             //优先级
                static::$t['warehouse_requirement_expect_settled_date'],        //预计入驻日期
                static::$t['warehouse_requirement_intended_regions'],           // 意向地区
                static::$t['warehouse_requirement_intended_longitude_latitude'],// 意向经纬度
                static::$t['warehouse_requirement_intended_area'],              // 意向面积
                static::$t['warehouse_export_003'],                             // van停车位数量
                static::$t['warehouse_requirement_status'],                     // 需求状态
            ];

            if ($excel_type == DownloadCenterEnum::FINANCE_WAREHOUSE_THREAD_EXPORT) {
                $excel_header[] = static::$t['warehouse_requirement_cancel_reason'];// 作废原因
            }

            $excel_header = array_merge($excel_header, [
                static::$t['warehouse_requirement_created_name'],               // 需求提出人
                static::$t['warehouse_requirement_created_id'],                 // 需求提出人工号
                static::$t['warehouse_requirement_apply_date'],                 // 需求提出日期
                static::$t['warehouse_requirement_remark'],                     // 备注
                static::$t['warehouse_requirement_attachment'],                 // 附件
            ]);

            //仓库线索管理-导出 - 多导出几列
            if ($excel_type == DownloadCenterEnum::FINANCE_WAREHOUSE_THREAD_EXPORT) {
                $excel_header = array_merge($excel_header, [
                    static::$t['warehouse_export_017'],                           // 验仓结果-是否有灭火器
                    static::$t['warehouse_thread_verify_door'],                   //验仓结果-大门照片
                    static::$t['warehouse_thread_verify_road'],                   //验仓结果-门口道路照片
                    static::$t['warehouse_thread_verify_remark'],                 //验仓结果-备注
                    static::$t['warehouse_thread_verify_name'],                   //验仓结果-验证人
                    static::$t['warehouse_thread_verify_date'],                   //验仓结果-验证日期
                    static::$t['warehouse_requirement_actual_settled_store_name'],//实际入驻网点
                    static::$t['warehouse_requirement_actual_settled_store_id'],  //实际入驻网点编码
                    static::$t['warehouse_requirement_actual_settled_date'],      //实际入驻日期
                    static::$t['warehouse_thread_warehouse_id'],                  //关联仓库
                ]);
            }

        } else {
            $excel_header = [
                static::$t['warehouse_thread_no'],                              //线索ID
                static::$t['warehouse_thread_staff_name'],                      //寻仓人姓名
                static::$t['warehouse_thread_staff_id'],                        //寻仓人工号
                static::$t['warehouse_thread_sys_department_id'],               //寻仓人一级部门
                static::$t['warehouse_thread_job_id'],                          //寻仓人职位
                static::$t['warehouse_thread_warehouse_address'],               //仓库地址
                static::$t['warehouse_thread_longitude_latitude'],              //仓库经纬度
                static::$t['warehouse_thread_warehouse_area'],                  //仓库面积
                static::$t['warehouse_thread_warehouse_month_rent'],            //仓库每月租金
                static::$t['warehouse_thread_contract_period'],                 //合同期
                static::$t['warehouse_thread_deposit'],                         //押金
                static::$t['warehouse_thread_down_payment'],                    //预付金
                static::$t['warehouse_thread_cusa_fee'],                        //Cusa费用
                static::$t['warehouse_thread_maintenance_fee'],                 //维护费
                static::$t['warehouse_thread_entrance_fee'],                    //入场费
                static::$t['warehouse_thread_garbage_fee'],                     //垃圾收集费
                static::$t['warehouse_thread_parking_fee'],                     //停车费
                static::$t['warehouse_thread_other_fee'],                       //其他费用
                static::$t['warehouse_thread_landlord_type'],                   //房东类型
                static::$t['warehouse_thread_landlord_name'],                   //房东联系人
                static::$t['warehouse_thread_landlord_mobile'],                 //房东联系号码
                static::$t['warehouse_thread_landlord_email'],                  //房东联系邮箱
                static::$t['warehouse_thread_warehouse_cr'],                    //仓库是否有CR
                static::$t['warehouse_thread_warehouse_electricity'],           //仓库电供应
                static::$t['warehouse_thread_warehouse_water'],                 //仓库水供应
                static::$t['warehouse_thread_landlord_aptitude'],               //房东资质文件是否齐全
                static::$t['warehouse_thread_plan_attachment'],                 //平面图
                static::$t['warehouse_thread_attachment'],                      //仓库线索附件
                static::$t['warehouse_thread_status'],                          //线索状态
                static::$t['warehouse_thread_cancel_reason'],                   //线索作废原因
                static::$t['warehouse_requirement_no'],                         // 需求ID
                static::$t['warehouse_requirement_store_name'],                 // 需求网点名称
                static::$t['warehouse_requirement_store_id'],                   // 需求网点编码
                static::$t['warehouse_requirement_opening_status'],             //创建仓库需求时网点开业状态
                static::$t['warehouse_requirement_warehouse_type'],             //仓库类型
                static::$t['warehouse_requirement_priority_level'],             //优先级
                static::$t['warehouse_requirement_expect_settled_date'],        //预计入驻日期
                static::$t['warehouse_requirement_intended_regions'],           // 意向地区
                static::$t['warehouse_requirement_intended_longitude_latitude'],// 意向经纬度
                static::$t['warehouse_requirement_intended_area'],              // 意向面积
                static::$t['warehouse_requirement_status'],                     // 需求状态
                static::$t['warehouse_requirement_created_name'],               // 需求提出人
                static::$t['warehouse_requirement_created_id'],                 // 需求提出人工号
                static::$t['warehouse_requirement_apply_date'],                 // 需求提出日期
                static::$t['warehouse_requirement_remark'],                     // 备注
                static::$t['warehouse_requirement_attachment'],                 // 附件
            ];

            //仓库线索管理-导出 - 多导出几列
            if ($excel_type == DownloadCenterEnum::FINANCE_WAREHOUSE_THREAD_EXPORT) {
                $excel_header = array_merge($excel_header, [
                    static::$t['warehouse_thread_verify_electricity'],            //验仓结果-仓库电供应
                    static::$t['warehouse_thread_verify_water'],                  //验仓结果-仓库水供应
                    static::$t['warehouse_thread_verify_lighting'],               //验仓结果-仓库照明
                    static::$t['warehouse_thread_verify_door'],                   //验仓结果-大门照片
                    static::$t['warehouse_thread_verify_road'],                   //验仓结果-门口道路照片
                    static::$t['warehouse_thread_verify_remark'],                 //验仓结果-备注
                    static::$t['warehouse_thread_verify_name'],                   //验仓结果-验证人
                    static::$t['warehouse_thread_verify_date'],                   //验仓结果-验证日期
                    static::$t['warehouse_requirement_actual_settled_store_name'],//实际入驻网点
                    static::$t['warehouse_requirement_actual_settled_store_id'],  //实际入驻网点编码
                    static::$t['warehouse_requirement_actual_settled_date'],      //实际入驻日期
                    static::$t['warehouse_thread_warehouse_id'],                  //关联仓库
                ]);
            }
        }

        return $excel_header;
    }

    /**
     * 处理仓库线索列表数据格式
     *
     * @param array $items
     * @param int $type 列表 1仓库线索管理-列表、2处理仓库线索-待报价、3处理仓库线索-待签约、4处理仓库线索-待验证
     * @param bool $is_export 是否导出，false否，true是
     * @return array
     */
    protected function handleItems(array $items, int $type = self::THREAD_LIST, bool $is_export = false)
    {
        if (empty($items)) {
            return [];
        }
        $data = [];
        if ($is_export) {
            //需求附件
            $requirement_attachment = [];
            $items                  = $this->getAreaInfo($items);
            $requirement_ids        = array_values(array_unique(array_filter(array_column($items, 'requirement_id'))));
            if ($requirement_ids) {
                $requirement_attachment = SysAttachmentRepository::getInstance()->getAttachmentsUrlsById($requirement_ids, Enums::OSS_BUCKET_TYPE_WAREHOUSE_REQUIREMENT_INFO_FILE);
            }

            $thread_ids = array_values(array_unique(array_filter(array_column($items, 'id'))));
            //线索平面图
            $thread_plan_attachment = SysAttachmentRepository::getInstance()->getAttachmentsUrlsById($thread_ids, Enums::OSS_BUCKET_TYPE_WAREHOUSE_THREAD_PLAN_FILE);
            //仓库线索附件
            $thread_attachment = SysAttachmentRepository::getInstance()->getAttachmentsUrlsById($thread_ids, Enums::OSS_BUCKET_TYPE_WAREHOUSE_THREAD_INFO_FILE);

            if (get_country_code() == GlobalEnums::TH_COUNTRY_CODE) {
                // 网点类型
                $store_category_map = self::getSettingEnvStoreCategoryMap();

                // 原有仓库
//                $warehouse_ids = array_column($items, 'original_warehouse_id');
//                $original_warehouse_map = ContractWarehouseRepository::getInstance()->getListByWarehouseIds($warehouse_ids);

                $thread_enums = ThreadService::getInstance()->getEnums();
                $rent_payment_method_map              = array_column($thread_enums['rent_payment_method'], 'label', 'value');
                $water_bill_payment_type_map          = array_column($thread_enums['water_bill_payment_type'], 'label', 'value');
                $electricity_bill_payment_type_map    = array_column($thread_enums['electricity_bill_payment_type'], 'label', 'value');
                $water_billing_method_map             = array_column($thread_enums['water_billing_method'], 'label', 'value');
                $electricity_billing_method_map       = array_column($thread_enums['electricity_billing_method'], 'label', 'value');
                $is_install_fire_extinguishers_map    = array_column($thread_enums['is_install_fire_extinguishers'], 'label', 'value');
                $withholding_tax_liability_bearer_map = array_column($thread_enums['withholding_tax_liability_bearer'], 'label', 'value');
                $land_tax_liability_bearer_map        = array_column($thread_enums['land_tax_liability_bearer'], 'label', 'value');
                $stamp_duty_liability_bearer_map      = array_column($thread_enums['stamp_duty_liability_bearer'], 'label', 'value');

                $th_fee_amount_unit = static::$t->_('th_fee_amount_unit');

                foreach ($items as $item) {
                    $rent_payment_method = !empty($item['rent_payment_method']) ? explode(',', $item['rent_payment_method']) : [];

                    $rent_payment_method_item = [];
                    foreach ($rent_payment_method as $method_val) {
                        $rent_payment_method_item[] = $rent_payment_method_map[$method_val] ?? '';
                    }

                    $water_fee_unit = '';
                    if ($item['water_billing_method'] == WarehouseEnums::THREAD_WATER_BILLING_METHOD_UNIT) {
                        $water_fee_unit = static::$t->_('currency_thai_baht') . '/' . static::$t->_('water_billing_unit_ton');
                    } elseif ($item['water_billing_method'] == WarehouseEnums::THREAD_WATER_BILLING_METHOD_UNITY) {
                        $water_fee_unit = static::$t->_('currency_thai_baht');
                    }
                    $water_usage_units = $item['water_usage_units'] . $water_fee_unit;

                    $electricity_fee_unit = '';
                    if ($item['electricity_billing_method'] == WarehouseEnums::THREAD_ELECTRICITY_BILLING_METHOD_UNIT) {
                        $electricity_fee_unit = static::$t->_('currency_thai_baht') . '/' . static::$t->_('electricity_billing_unit_kwh');
                    } elseif ($item['electricity_billing_method'] == WarehouseEnums::THREAD_ELECTRICITY_BILLING_METHOD_UNITY) {
                        $electricity_fee_unit = static::$t->_('currency_thai_baht');
                    }
                    $electricity_usage_units = $item['electricity_usage_units'] . $electricity_fee_unit;

                    $rows = [
                        $item['no'],
                        $item['staff_name'],
                        $item['staff_id'],
                        $item['sys_department_name'],
                        $item['job_name'],
                        $item['warehouse_address'],
                        $item['warehouse_latitude'] . ',' . $item['warehouse_longitude'],
                        $item['warehouse_area'],
                        $item['warehouse_month_rent'],
                        $item['contract_period'] . static::$t->_(WarehouseEnums::$contract_period_unit[$item['contract_period_unit']]),
                        $item['deposit'],
                        $item['down_payment'],
                        implode(',', $rent_payment_method_item), // 租金支付方式
                        !empty($item['water_bill_payment_type']) ? $water_bill_payment_type_map[$item['water_bill_payment_type']] : '',
                        !empty($item['electricity_bill_payment_type']) ? $electricity_bill_payment_type_map[$item['electricity_bill_payment_type']] : '',
                        !empty($item['water_billing_method']) ? $water_billing_method_map[$item['water_billing_method']] : '',
                        $water_usage_units,
                        !empty($item['electricity_billing_method']) ? $electricity_billing_method_map[$item['electricity_billing_method']] : '',
                        $electricity_usage_units,
                        !empty($item['is_install_fire_extinguishers']) ? $is_install_fire_extinguishers_map[$item['is_install_fire_extinguishers']] : '',
                        !empty($item['withholding_tax_liability_bearer']) ? $withholding_tax_liability_bearer_map[$item['withholding_tax_liability_bearer']] : '',
                        !empty($item['land_tax_liability_bearer']) ? $land_tax_liability_bearer_map[$item['land_tax_liability_bearer']] : '',
                        !empty($item['stamp_duty_liability_bearer']) ? $stamp_duty_liability_bearer_map[$item['stamp_duty_liability_bearer']] : '',
                        $item['surrounding_stores_average_rent'] . $th_fee_amount_unit,
                        $item['other_options_average_cost'] . $th_fee_amount_unit,

                        static::$t->_(WarehouseEnums::$landlord_type[$item['landlord_type']]),
                        $item['landlord_name'],
                        $item['landlord_mobile'],
                        $item['landlord_email'],

                        static::$t->_(WarehouseEnums::$landlord_aptitude[$item['landlord_aptitude']]),
                        implode(PHP_EOL, $thread_plan_attachment[$item['id']] ?? []),//平面图
                        implode(PHP_EOL, $thread_attachment[$item['id']] ?? []),     //仓库线索附件
                        static::$t->_(WarehouseEnums::$thread_status[$item['status']]),
                        $item['cancel_reason'],
                        $item['requirement_no'],
                        $item['store_name'],
                        $item['store_id'],
                        $store_category_map[$item['store_category']] ?? '',
                        static::$t->_(WarehouseEnums::$opening_status[$item['opening_status']]),
                        static::$t->_(static::getWarehouseTypeKeyValueMap()[$item['warehouse_type']]),
//                        $original_warehouse_map[$item['original_warehouse_id']]['warehouse_name'] ?? '',
                        static::$t->_(WarehouseEnums::$priority_level[$item['priority_level']]),
                        $item['expect_settled_date'],
                        $item['district_name'] . ' ' . $item['city_name'] . ' ' . $item['province_name'],
                        $item['requirement_no'] ? ($item['requirement_warehouse_latitude'] . ',' . $item['requirement_warehouse_longitude']) : '',
                        $item['requirement_no'] ? ($item['area_min'] . '-' . $item['area_max']) : '',
                        $item['requirement_no'] ? ($item['parking_num_min'] . '-' . $item['parking_num_max']) : '',
                        static::$t->_(WarehouseEnums::getRequirementStatus()[$item['requirement_status']]),
                    ];

                    if ($type == self::THREAD_LIST) {
                        $rows[] = $item['requirement_cancel_reason'];
                    }

                    $rows = array_merge($rows, [
                        $item['requirement_created_name'],
                        $item['requirement_created_id'],
                        $item['requirement_apply_date'],
                        $item['requirement_remark'],
                        implode(PHP_EOL, $requirement_attachment[$item['requirement_id']] ?? []),//需求附件
                    ]);

                    //仓库线索管理-列表 - 多导出几列
                    if ($type == self::THREAD_LIST) {
                        $verify_ids = array_values(array_unique(array_filter(array_column($items, 'verify_id'))));
                        //验仓结果-大门照片
                        $verify_door_attachment = SysAttachmentRepository::getInstance()->getAttachmentsUrlsById($verify_ids, Enums::OSS_BUCKET_TYPE_WAREHOUSE_THREAD_VERIFY_DOOR_FILE);
                        //验仓结果-门口道路照片
                        $verify_road_attachment  = SysAttachmentRepository::getInstance()->getAttachmentsUrlsById($verify_ids, Enums::OSS_BUCKET_TYPE_WAREHOUSE_THREAD_VERIFY_ROAD_FILE);

                        $rows = array_merge($rows, [
                            !empty($item['verify_is_install_fire_extinguishers']) ? $is_install_fire_extinguishers_map[$item['verify_is_install_fire_extinguishers']] : '',
                            implode(PHP_EOL, $verify_door_attachment[$item['verify_id']] ?? []),//验仓结果-大门照片
                            implode(PHP_EOL, $verify_road_attachment[$item['verify_id']] ?? []),//验仓结果-门口道路照片
                            $item['verify_remark'] ?? '',
                            $item['verify_staff_name'] ? $item['verify_staff_name'] . '(' . $item['verify_staff_id'] . ')' : '',
                            $item['verify_date'] ?? '',
                            $item['actual_settled_store_name'] ?? '',
                            $item['actual_settled_store_id'] ?? '',
                            $item['actual_settled_date'] ?? '',
                            $item['warehouse_name'] ? $item['warehouse_id'] . '(' . $item['warehouse_name'] . ')' : '',
                        ]);
                    }

                    $data[] = $rows;
                }

            } else {
                foreach ($items as $item) {
                    $currency = static::$t->_(GlobalEnums::$currency_item[$item['currency']]);
                    $rows     = [
                        $item['no'],
                        $item['staff_name'],
                        $item['staff_id'],
                        $item['sys_department_name'],
                        $item['job_name'],
                        $item['warehouse_address'],
                        $item['warehouse_latitude'] . ',' . $item['warehouse_longitude'],
                        $item['warehouse_area'],
                        $item['warehouse_month_rent'],
                        $item['contract_period'] . static::$t->_(WarehouseEnums::$contract_period_unit[$item['contract_period_unit']]),
                        $item['deposit'],
                        $item['down_payment'],
                        $item['cusa_fee'] . ' ' . $currency,
                        $item['maintenance_fee'] . $currency,
                        $item['entrance_fee'] . ' ' . $currency,
                        $item['garbage_fee'] . ' ' . $currency,
                        $item['parking_fee'] . ' ' . $currency,
                        $item['other_fee'] . ' ' . $currency,
                        static::$t->_(WarehouseEnums::$landlord_type[$item['landlord_type']]),
                        $item['landlord_name'],
                        $item['landlord_mobile'],
                        $item['landlord_email'],
                        static::$t->_(WarehouseEnums::$thread_has[$item['warehouse_cr']]),
                        static::$t->_(WarehouseEnums::$thread_has[$item['warehouse_electricity']]),
                        static::$t->_(WarehouseEnums::$thread_has[$item['warehouse_water']]),
                        static::$t->_(WarehouseEnums::$landlord_aptitude[$item['landlord_aptitude']]),
                        implode(PHP_EOL, $thread_plan_attachment[$item['id']] ?? []),//平面图
                        implode(PHP_EOL, $thread_attachment[$item['id']] ?? []),//仓库线索附件
                        static::$t->_(WarehouseEnums::$thread_status[$item['status']]),
                        $item['cancel_reason'],
                        $item['requirement_no'],
                        $item['store_name'],
                        $item['store_id'],
                        static::$t->_(WarehouseEnums::$opening_status[$item['opening_status']]),
                        static::$t->_(static::getWarehouseTypeKeyValueMap()[$item['warehouse_type']]),
                        static::$t->_(WarehouseEnums::$priority_level[$item['priority_level']]),
                        $item['expect_settled_date'],
                        $item['district_name'] . ' ' . $item['city_name'] . ' ' . $item['province_name'],
                        $item['requirement_no'] ? ($item['requirement_warehouse_latitude'] . ',' . $item['requirement_warehouse_longitude']) : '',
                        $item['requirement_no'] ? ($item['area_min'] . '-' . $item['area_max']) : '',
                        static::$t->_(WarehouseEnums::getRequirementStatus()[$item['requirement_status']]),
                        $item['requirement_created_name'],
                        $item['requirement_created_id'],
                        $item['requirement_apply_date'],
                        $item['requirement_remark'],
                        implode(PHP_EOL, $requirement_attachment[$item['requirement_id']] ?? []),//需求附件
                    ];

                    //仓库线索管理-列表 - 多导出几列
                    if ($type == self::THREAD_LIST) {
                        $verify_ids = array_values(array_unique(array_filter(array_column($items, 'verify_id'))));
                        //验仓结果-大门照片
                        $verify_door_attachment = SysAttachmentRepository::getInstance()->getAttachmentsUrlsById($verify_ids, Enums::OSS_BUCKET_TYPE_WAREHOUSE_THREAD_VERIFY_DOOR_FILE);
                        //验仓结果-门口道路照片
                        $verify_road_attachment  = SysAttachmentRepository::getInstance()->getAttachmentsUrlsById($verify_ids, Enums::OSS_BUCKET_TYPE_WAREHOUSE_THREAD_VERIFY_ROAD_FILE);
                        $rows = array_merge($rows, [
                            $item['electricity'] ? static::$t->_(WarehouseEnums::$thread_verify_state[$item['electricity']]) : '',
                            $item['water'] ? static::$t->_(WarehouseEnums::$thread_verify_state[$item['water']]) : '',
                            $item['lighting'] ? static::$t->_(WarehouseEnums::$thread_verify_state[$item['lighting']]) : '',
                            implode(PHP_EOL, $verify_door_attachment[$item['verify_id']] ?? []),//验仓结果-大门照片
                            implode(PHP_EOL, $verify_road_attachment[$item['verify_id']] ?? []),//验仓结果-门口道路照片
                            $item['verify_remark'] ?? '',
                            $item['verify_staff_name'] ? $item['verify_staff_name'] . '(' . $item['verify_staff_id'] . ')' : '',
                            $item['verify_date'] ?? '',
                            $item['actual_settled_store_id'] ?? '',
                            $item['actual_settled_store_name'] ?? '',
                            $item['actual_settled_date'] ?? '',
                            $item['warehouse_name'] ? $item['warehouse_id'] . '(' . $item['warehouse_name'] . ')' : '',
                        ]);
                    }
                    $data[] = $rows;
                }
            }

        } else {
            $install_fire_extinguishers_item = WarehouseEnums::$thread_install_fire_extinguishers;

            foreach ($items as &$val) {
                $val['status_text']                = static::$t->_(WarehouseEnums::$thread_status[$val['status']]);
                $val['warehouse_cr_text']          = $val['warehouse_cr'] ? static::$t->_(WarehouseEnums::$thread_has[$val['warehouse_cr']]) : '';
                $val['warehouse_electricity_text'] = $val['warehouse_electricity'] ? static::$t->_(WarehouseEnums::$thread_has[$val['warehouse_electricity']]) : '';
                $val['warehouse_water_text']       = $val['warehouse_water'] ? static::$t->_(WarehouseEnums::$thread_has[$val['warehouse_water']]) : '';
                $val['price_id']                   = $val['price_id'] ? $val['price_id'] : 0;
                if (!empty($val['contract_period_unit'])) {
                    $val['contract_period_unit_text'] = static::$t->_(WarehouseEnums::$contract_period_unit[$val['contract_period_unit']]);
                }

                $val['is_install_fire_extinguishers_text'] = '';
                if ($val['is_install_fire_extinguishers']) {
                    $val['is_install_fire_extinguishers_text'] = static::$t->_($install_fire_extinguishers_item[$val['is_install_fire_extinguishers']]);
                }
            }
            $data = $items;
        }
        return $data;
    }

    /**
     * 查询条件
     * @param object $builder
     * @param array $params 请求参数组
     * @param array $user 当前登陆者信息组
     * @param int $type 列表 1仓库线索管理-列表、2处理仓库线索-待报价、3处理仓库线索-待签约、4处理仓库线索-待验证
     * @return object
     */
    protected function getCondition(object $builder, array $params, array $user, int $type)
    {
        $no                = $params['no'] ?? '';              //线索编号
        $requirement_no    = $params['requirement_no'] ?? '';  //需求ID
        $status            = $params['status'] ?? [];          //线索状态
        $sys_department_id = $params['sys_department_id'] ?? 0;//寻仓人部门与仓库线索中的寻仓人一级部门精确匹配
        $staff_id          = $params['staff_id'] ?? 0;         //寻仓人工号
        $warehouse_id      = $params['warehouse_id'] ?? '';    //关联仓库
        $store_id          = $params['store_id'] ?? '';        //需求网点

        if ($type == self::THREAD_PRICE_LIST) {
            //处理仓库线索-待报价
            $status = WarehouseEnums::THREAD_STATUS_PRICE;
        } elseif ($type == self::THREAD_SIGN_LIST) {
            //处理仓库线索-待签约
            $status = WarehouseEnums::THREAD_STATUS_SIGN;
        } elseif ($type == self::THREAD_VERIFY_LIST) {
            //处理仓库线索-待验证 并且验证责任人为当前登录人的仓库验证数据
            $builder->leftJoin(WarehouseThreadVerifyRecordModel::class, 'verify.thread_id = main.id and verify.status = ' . WarehouseEnums::THREAD_VERIFY_STATUS_VERIFY, 'verify');
            $builder->andWhere('verify.person_liable_id = :person_liable_id:', ['person_liable_id' => $user['id']]);
        }

        if (!empty($no)) {
            $builder->andWhere('main.no LIKE :no:', ['no' => '%' . $params['no'] . '%']);
        }

        if (!empty($requirement_no)) {
            $builder->andWhere('main.requirement_no LIKE :requirement_no:', ['requirement_no' => '%' . $params['requirement_no'] . '%']);
        }

        if (!empty($status)) {
            if (is_array($status)) {
                $builder->inWhere('main.status', $status);
            } else {
                $builder->andWhere('main.status = :status:', ['status' => $status]);
            }
        }

        if (!empty($sys_department_id)) {
            $builder->andWhere('main.sys_department_id = :sys_department_id:', ['sys_department_id' => $sys_department_id]);
        }

        if (!empty($staff_id)) {
            $builder->andWhere('main.staff_id = :staff_id:', ['staff_id' => $staff_id]);
        }
        if (!empty($warehouse_id)) {
            $builder->andWhere('main.warehouse_id = :warehouse_id:', ['warehouse_id' => $warehouse_id]);
        }

        if (!empty($store_id)) {
            $builder->andWhere('requirement.store_id = :store_id:', ['store_id' => $store_id]);
        }
        return $builder;
    }

    /**
     * 获取线索详情 - 查看
     * @param object $thread_info 线索对象
     * @param bool $show_price 是否能看到价位信息
     * @return array
     */
    public function getThreadDetail(object $thread_info, bool $show_price = true)
    {
        $enums                                = $this->getEnums();
        $rent_payment_method_map              = array_column($enums['rent_payment_method'], 'label', 'value');
        $water_bill_payment_type_map          = array_column($enums['water_bill_payment_type'], 'label', 'value');
        $electricity_bill_payment_type_map    = array_column($enums['electricity_bill_payment_type'], 'label', 'value');
        $water_billing_method_map             = array_column($enums['water_billing_method'], 'label', 'value');
        $electricity_billing_method_map       = array_column($enums['electricity_billing_method'], 'label', 'value');
        $is_install_fire_extinguishers_map    = array_column($enums['is_install_fire_extinguishers'], 'label', 'value');
        $withholding_tax_liability_bearer_map = array_column($enums['withholding_tax_liability_bearer'], 'label', 'value');
        $land_tax_liability_bearer_map        = array_column($enums['land_tax_liability_bearer'], 'label', 'value');
        $stamp_duty_liability_bearer_map      = array_column($enums['stamp_duty_liability_bearer'], 'label', 'value');

        $rent_payment_method = [];
        if (!empty($thread_info->rent_payment_method)) {
            $rent_payment_method = array_map(function ($v) {
                return (int)$v;
            }, explode(',', $thread_info->rent_payment_method));
        }

        $data = [
            'id'                         => $thread_info->id,
            'no'                         => $thread_info->no,
            'requirement_id'             => $thread_info->requirement_id ? $thread_info->requirement_id : '',
            'staff_id'                   => $thread_info->staff_id,
            'staff_name'                 => $thread_info->staff_name,
            'sys_department_id'          => $thread_info->sys_department_id,
            'sys_department_name'        => $thread_info->sys_department_name,
            'node_department_id'         => $thread_info->node_department_id,
            'node_department_name'       => $thread_info->node_department_name,
            'job_id'                     => $thread_info->job_id,
            'job_name'                   => $thread_info->job_name,
            'warehouse_address'          => $thread_info->warehouse_address,
            'warehouse_latitude'         => $thread_info->warehouse_latitude,
            'warehouse_longitude'        => $thread_info->warehouse_longitude,
            'warehouse_area'             => $thread_info->warehouse_area,
            'landlord_type'              => $thread_info->landlord_type,
            'landlord_type_text'         => !empty($thread_info->landlord_type) ? static::$t->_(WarehouseEnums::$landlord_type[$thread_info->landlord_type]) : '',
            'landlord_name'              => $thread_info->landlord_name,
            'landlord_mobile'            => $thread_info->landlord_mobile,
            'landlord_email'             => $thread_info->landlord_email,
            'landlord_aptitude'          => $thread_info->landlord_aptitude,
            'landlord_aptitude_text'     => static::$t->_(WarehouseEnums::$landlord_aptitude[$thread_info->landlord_aptitude]),
            'warehouse_cr'               => $thread_info->warehouse_cr,
            'warehouse_cr_text'          => $thread_info->warehouse_cr ? static::$t->_(WarehouseEnums::$thread_has[$thread_info->warehouse_cr]) : '',
            'warehouse_electricity'      => $thread_info->warehouse_electricity,
            'warehouse_electricity_text' => $thread_info->warehouse_electricity ? static::$t->_(WarehouseEnums::$thread_has[$thread_info->warehouse_electricity]) : '',
            'warehouse_water'            => $thread_info->warehouse_water,
            'warehouse_water_text'       => $thread_info->warehouse_water ? static::$t->_(WarehouseEnums::$thread_has[$thread_info->warehouse_water]) : '',
            'remark'                     => $thread_info->remark,
            'status'                     => $thread_info->status,
            'status_text'                => static::$t->_(WarehouseEnums::$thread_status[$thread_info->status]),
            'cancel_reason'              => $thread_info->cancel_reason,
            'plan_attachments'           => $thread_info->getPlanAttachment()->toArray(),
            'attachments'                => $thread_info->getAttachment()->toArray(),

            'rent_payment_method'                   => $rent_payment_method,
            'water_bill_payment_type'               => $thread_info->water_bill_payment_type ? (int)$thread_info->water_bill_payment_type : '',
            'water_bill_payment_type_text'          => $water_bill_payment_type_map[$thread_info->water_bill_payment_type] ?? '',
            'electricity_bill_payment_type'         => $thread_info->electricity_bill_payment_type ? (int)$thread_info->electricity_bill_payment_type : '',
            'electricity_bill_payment_type_text'    => $electricity_bill_payment_type_map[$thread_info->electricity_bill_payment_type] ?? '',
            'water_billing_method'                  => $thread_info->water_billing_method ? (int)$thread_info->water_billing_method : '',
            'water_billing_method_text'             => $water_billing_method_map[$thread_info->water_billing_method] ?? '',
            'electricity_billing_method'            => $thread_info->electricity_billing_method ? (int)$thread_info->electricity_billing_method : '',
            'electricity_billing_method_text'       => $electricity_billing_method_map[$thread_info->electricity_billing_method] ?? '',
            'is_install_fire_extinguishers'         => $thread_info->is_install_fire_extinguishers ? (int)$thread_info->is_install_fire_extinguishers : '',
            'is_install_fire_extinguishers_text'    => $is_install_fire_extinguishers_map[$thread_info->is_install_fire_extinguishers] ?? '',
            'withholding_tax_liability_bearer'      => $thread_info->withholding_tax_liability_bearer ? (int)$thread_info->withholding_tax_liability_bearer : '',
            'withholding_tax_liability_bearer_text' => $withholding_tax_liability_bearer_map[$thread_info->withholding_tax_liability_bearer] ?? '',
            'land_tax_liability_bearer'             => $thread_info->land_tax_liability_bearer ? (int)$thread_info->land_tax_liability_bearer : '',
            'land_tax_liability_bearer_text'        => $land_tax_liability_bearer_map[$thread_info->land_tax_liability_bearer] ?? '',
            'stamp_duty_liability_bearer'           => $thread_info->stamp_duty_liability_bearer ? (int)$thread_info->stamp_duty_liability_bearer : '',
            'stamp_duty_liability_bearer_text'      => $stamp_duty_liability_bearer_map[$thread_info->stamp_duty_liability_bearer] ?? '',
            'surrounding_stores_average_rent'       => $thread_info->surrounding_stores_average_rent,
            'other_options_average_cost'            => $thread_info->other_options_average_cost,
            'electricity_usage_units'               => $thread_info->electricity_usage_units,
            'water_usage_units'                     => $thread_info->water_usage_units,
        ];

        if ($show_price) {
            $data = array_merge($data, [
                'warehouse_month_rent'      => $thread_info->warehouse_month_rent,
                'contract_period'           => $thread_info->contract_period,
                'contract_period_unit'      => $thread_info->contract_period_unit,
                'contract_period_unit_text' => static::$t->_(WarehouseEnums::$contract_period_unit[$thread_info->contract_period_unit]),
                'deposit'                   => $thread_info->deposit,
                'down_payment'              => $thread_info->down_payment,
                'cusa_fee'                  => $thread_info->cusa_fee,
                'maintenance_fee'           => $thread_info->maintenance_fee,
                'entrance_fee'              => $thread_info->entrance_fee,
                'garbage_fee'               => $thread_info->garbage_fee,
                'parking_fee'               => $thread_info->parking_fee,
                'other_fee'                 => $thread_info->other_fee,
                'currency_text'             => static::$t->_(GlobalEnums::$currency_item[$thread_info->currency]),
            ]);
        }
        return $data;
    }

    /**
     * 获取线索已提交的验证结果
     * @param int $thread_id 线索ID
     * @return array
     */
    public function getThreadVerifySubmitDetail(int $thread_id)
    {
        $data = (object)[];
        //当提交了验仓结果以后展示具体字段界面，如果没有提交验仓结果时显示暂无数据
        $verify_info = WarehouseThreadVerifyRecordModel::findFirst([
            'conditions' => 'thread_id = :thread_id: and status = :status:',
            'bind'       => ['thread_id' => $thread_id, 'status' => WarehouseEnums::THREAD_VERIFY_STATUS_SUBMIT],
        ]);
        if ($verify_info) {
            $data = [
                'electricity_text'  => $verify_info->electricity ? static::$t->_(WarehouseEnums::$thread_verify_state[$verify_info->electricity]) : '', //仓库电供应
                'water_text'        => $verify_info->water ? static::$t->_(WarehouseEnums::$thread_verify_state[$verify_info->water]) : '', //仓库水供应
                'lighting_text'     => $verify_info->lighting ? static::$t->_(WarehouseEnums::$thread_verify_state[$verify_info->lighting]) : '', //仓库照明
                'remark'            => $verify_info->remark, //备注
                'verify_staff_id'   => $verify_info->verify_staff_id, //验证人-工号
                'verify_staff_name' => $verify_info->verify_staff_name, //验证人-姓名
                'verify_date'       => $verify_info->verify_date,//验证日期
                'door_attachments'  => $verify_info->getDoorAttachment()->toArray(), //大门照片
                'road_attachments'  => $verify_info->getRoadAttachment()->toArray(), //门口道路照片
                'is_install_fire_extinguishers_text' => ''
            ];

            $install_fire_extinguishers_item = WarehouseEnums::$thread_install_fire_extinguishers;
            if ($verify_info->is_install_fire_extinguishers) {
                $data['is_install_fire_extinguishers_text'] = static::$t->_($install_fire_extinguishers_item[$verify_info->is_install_fire_extinguishers]);
            }
        }
        return $data;
    }

    /**
     * 获取线索 - 关联需求 - 查看
     * @param object $thread_info 线索对象
     * @return array
     */
    public function getRequirementDetail(object $thread_info)
    {
        $data = [];
        if (empty($thread_info->requirement_id)) {
            return $data;
        }
        $requirement_info = $thread_info->getRequirement();
        if ($requirement_info) {
            //需求信息
            $data['requirement_info'] = RequirementService::getInstance()->getRequirementDetail($requirement_info);

            //入驻信息
            if (!empty($requirement_info->actual_settled_store_id)) {
                $data['settled_info'] = [
                    'actual_settled_store_id'   => $requirement_info->actual_settled_store_id,
                    'actual_settled_store_name' => $requirement_info->actual_settled_store_name,
                    'actual_settled_date'       => $requirement_info->actual_settled_date,
                ];
            }
        }
        return $data;
    }

    /**
     * 仓库线索查看
     * @param int $id 需求ID
     * @param int $type 列表 1仓库线索管理-列表、2处理仓库线索-待报价、3处理仓库线索-待签约、4处理仓库线索-待验证
     * @return array
     */
    public function view(int $id, int $type = self::THREAD_LIST)
    {
        $code    = ErrCode::$SUCCESS;
        $message = 'success';
        $data    = [];

        try {
            $thread_info = $this->getThreadInfo($id);
            //处理仓库线索-只能看各自状态下的数据
            if (($type == self::THREAD_PRICE_LIST && $thread_info->status != WarehouseEnums::THREAD_STATUS_PRICE) || ($type == self::THREAD_SIGN_LIST && $thread_info->status != WarehouseEnums::THREAD_STATUS_SIGN)) {
                throw new ValidationException(static::$t->_('warehouse_thread_not_exists'), ErrCode::$VALIDATE_ERROR);
            }

            $data = $this->getThreadDetail($thread_info);

            //仓库需求信息
            $requirement_info         = $this->getRequirementDetail($thread_info);
            $data['requirement_info'] = $requirement_info['requirement_info'] ?? (object)[];
            //入驻信息
            $data['settled_info'] = $requirement_info['settled_info'] ?? (object)[];

            //验仓结果
            $data['verify_info'] = $this->getThreadVerifySubmitDetail($thread_info->id);

            //仓库信息
            $data['warehouse_info'] = (object)[];
            if (!empty($thread_info->warehouse_main_id)) {
                $warehouse_info = $thread_info->getWarehouse();
                if ($warehouse_info) {
                    $warehouse_area_info    = $this->getAreaInfo([$warehouse_info->toArray()])[0] ?? [];
                    $data['warehouse_info'] = [
                        'warehouse_main_id'   => $warehouse_info->id,
                        'warehouse_id'        => $warehouse_info->warehouse_id,//仓库ID
                        'warehouse_name'      => $warehouse_info->warehouse_name,//仓库名称
                        'real_area'           => $warehouse_info->real_area,   //仓库实际面积
                        'province_code'       => $warehouse_info->province_code,
                        'province_name'       => $warehouse_area_info['province_name'],//仓库省
                        'city_code'           => $warehouse_info->city_code,
                        'city_name'           => $warehouse_area_info['city_name'],//仓库市
                        'district_code'       => $warehouse_info->district_code,
                        'district_name'       => $warehouse_area_info['district_name'],//区信息
                        'warehouse_latitude'  => $warehouse_info->warehouse_latitude,  //仓库纬度
                        'warehouse_longitude' => $warehouse_info->warehouse_longitude, //仓库经度
                    ];
                }
            }
        } catch (ValidationException $e) {
            $code    = $e->getCode();
            $message = $e->getMessage();
        } catch (Exception $e) {
            $code    = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $this->logger->error('仓库管理-仓库线索管理-详情获取失败:' . $e->getMessage());
        }

        return [
            'code'    => $code,
            'message' => $message,
            'data'    => $data,
        ];
    }

    /**
     * 仓库线索作废
     * @param array $params 参数组
     * @param array $user 当前登陆人信息组
     * @return array
     */
    public function cancel(array $params, array $user)
    {
        $code    = ErrCode::$SUCCESS;
        $message = $real_message = '';
        try {
            Validation::validate($params, self::$validate_cancel);
            $thread_info = $this->getThreadInfo($params['id']);
            //线索状态为待确认、被淘汰、待关联才可作废
            if (!in_array($thread_info->status, [WarehouseEnums::THREAD_STATUS_CONFIRM, WarehouseEnums::THREAD_STATUS_ELIMINATE, WarehouseEnums::THREAD_STATUS_ASSOCIATION])) {
                throw new ValidationException(static::$t->_('warehouse_thread_cancel_status_error'), ErrCode::$VALIDATE_ERROR);
            }
            $now                         = date('Y-m-d H:i:s');
            $thread_info->requirement_id = 0;
            $thread_info->requirement_no = '';
            $thread_info->cancel_reason  = $params['cancel_reason'];
            $thread_info->cancel_at      = $now;
            $thread_info->status         = WarehouseEnums::THREAD_STATUS_CANCEL;//已作废
            $thread_info->updated_id     = $user['id'];
            $thread_info->updated_name   = $user['name'];
            $thread_info->updated_at     = date('Y-m-d H:i:s');
            $bool                        = $thread_info->save();
            if ($bool === false) {
                throw new BusinessException('线索作废失败，参数为：' . json_encode($params, JSON_UNESCAPED_UNICODE) . '; 可能的原因是：' . get_data_object_error_msg($thread_info), ErrCode::$BUSINESS_ERROR);
            }
        } catch (ValidationException $e) {
            $code    = $e->getCode();
            $message = $e->getMessage();
        } catch (BusinessException $e) {
            $code         = $e->getCode();
            $message      = static::$t->_('retry_later');
            $real_message = $e->getMessage() . $e->getTraceAsString();
        } catch (Exception $e) {
            $code         = ErrCode::$SYSTEM_ERROR;
            $message      = static::$t->_('retry_later');
            $real_message = $e->getMessage() . $e->getTraceAsString();
        }
        if ($real_message) {
            $this->logger->error('仓库管理-仓库线索管理-作废失败: ' . $real_message);
        }

        return [
            'code'    => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data'    => [],
        ];
    }

    /**
     * 仓库线索查看报价
     * @param int $id 报价单id
     * @param array $user 当前登陆者信息组
     * @return array
     */
    public function viewPrice(int $id, array $user)
    {
        return PriceService::getInstance()->view($id, $user);
    }

    /**
     * 仓库需求管理-查看-关联线索-转移/处理仓库需求-待寻找-录入线索-关联线索-转移
     * @param array $params 参数组
     * @param array $user 当前登陆人信息组
     * @return array
     */
    public function transferThread(array $params, array $user)
    {
        $code    = ErrCode::$SUCCESS;
        $message = $real_message = '';

        try {
            Validation::validate($params, self::$validate_transfer);
            $thread_info = $this->getThreadInfo($params['id']);
            //线索状态只能是待确认或被淘汰才可转移
            if (!in_array($thread_info->status, [WarehouseEnums::THREAD_STATUS_CONFIRM, WarehouseEnums::THREAD_STATUS_ELIMINATE])) {
                throw new ValidationException(static::$t->_('warehouse_thread_transfer_status_error'), ErrCode::$VALIDATE_ERROR);
            }

            $requirement_info = RequirementService::getInstance()->getRequirementInfo($params['requirement_id']);
            //线索转移时关联需求需是待寻找状态的需求
            if ($requirement_info->status != WarehouseEnums::REQUIREMENT_STATUS_SEARCH) {
                throw new ValidationException(static::$t->_('warehouse_requirement_status_error', ['status' => static::$t->_(WarehouseEnums::getRequirementStatus()[WarehouseEnums::REQUIREMENT_STATUS_SEARCH])]), ErrCode::$VALIDATE_ERROR);
            }

            //意向地址距离计算规则 = 线索中的仓库经纬度与需求中意向经纬度的距离，保留2位小数，单位为KM
            $intention_address_distance              = get_distance($thread_info->warehouse_latitude, $thread_info->warehouse_longitude, $requirement_info->warehouse_latitude, $requirement_info->warehouse_longitude);
            $thread_info->requirement_id             = $requirement_info->id;
            $thread_info->requirement_no             = $requirement_info->no;
            $thread_info->intention_address_distance = $intention_address_distance;
            $thread_info->status                     = WarehouseEnums::THREAD_STATUS_CONFIRM;//待确认
            $thread_info->updated_id                 = $user['id'];
            $thread_info->updated_name               = $user['name'];
            $thread_info->updated_at                 = date('Y-m-d H:i:s');
            $bool                                    = $thread_info->save();
            if ($bool === false) {
                throw new BusinessException('线索转移失败，参数为：' . json_encode($params, JSON_UNESCAPED_UNICODE) . '; 可能的原因是：' . get_data_object_error_msg($thread_info), ErrCode::$BUSINESS_ERROR);
            }
        } catch (ValidationException $e) {
            $code    = $e->getCode();
            $message = $e->getMessage();
        } catch (BusinessException $e) {
            $code         = $e->getCode();
            $message      = static::$t->_('retry_later');
            $real_message = $e->getMessage() . $e->getTraceAsString();
        } catch (Exception $e) {
            $code         = ErrCode::$SYSTEM_ERROR;
            $message      = static::$t->_('retry_later');
            $real_message = $e->getMessage() . $e->getTraceAsString();
        }

        if ($real_message) {
            $this->logger->error('仓库管理-仓库需求管理-关联线索-转移失败: ' . $real_message);
        }

        return [
            'code'    => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data'    => [],
        ];
    }

    /**
     * 导出某个需求下待确认的线索
     * @param int $requirement_id 需求id
     * @return array
     * @throws BusinessException
     * @throws GuzzleException
     */
    public function exportRequirementThread(int $requirement_id)
    {
        $country_code = get_country_code();

        //展示所有关联需求ID为当前仓库需求的仓库线索并且线索状态为确认中的线索
        $relate_thread_list = WarehouseThreadRepository::getInstance()->onlySearchThread(['requirement_id' => $requirement_id, 'status' => WarehouseEnums::THREAD_STATUS_CONFIRM_ING], 'intention_address_distance, no ASC');

        $excel_header = [
            static::$t['warehouse_thread_no'],                        //线索ID
            static::$t['warehouse_latitude'],                         //仓库纬度
            static::$t['warehouse_longitude'],                        //仓库经度
            static::$t['warehouse_thread_intention_address_distance'],//意向地址距离
            static::$t['warehouse_thread_warehouse_area'],            //仓库面积
            static::$t['warehouse_thread_warehouse_month_rent'],      //仓库每月租金
            static::$t['warehouse_thread_contract_period'],           //合同期
            static::$t['warehouse_thread_deposit'],                   //押金
            static::$t['warehouse_thread_down_payment'],              //预付金
        ];

        if ($country_code == GlobalEnums::TH_COUNTRY_CODE) {
            $excel_header = array_merge($excel_header, [
                static::$t['warehouse_export_004'], // 租金支付方式
                static::$t['warehouse_export_005'], // 水费支付类型
                static::$t['warehouse_export_006'], // 电费支付类型
                static::$t['warehouse_export_007'], // 水费使用计费方式
                static::$t['warehouse_export_008'], // 水费使用单位数
                static::$t['warehouse_export_009'], // 电费使用计费方式
                static::$t['warehouse_export_010'], // 电费使用单位数
                static::$t['warehouse_export_011'], // 是否安装灭火器
                static::$t['warehouse_export_012'], // 扣缴税责任承担方
                static::$t['warehouse_export_013'], // 土地税责任承担方
                static::$t['warehouse_export_014'], // 印花税责任承担方
                static::$t['warehouse_export_015'], // 周边门店平均租金
                static::$t['warehouse_export_016'], // 其他选址方案平均费用
            ]);
        } else {
            $excel_header = array_merge($excel_header, [
                static::$t['warehouse_thread_cusa_fee'],                  //Cusa费用
                static::$t['warehouse_thread_maintenance_fee'],           //维护费
                static::$t['warehouse_thread_entrance_fee'],              //入场费
                static::$t['warehouse_thread_garbage_fee'],               //垃圾收集费
                static::$t['warehouse_thread_parking_fee'],               //停车费
                static::$t['warehouse_thread_other_fee'],                 //其他费用
                static::$t['warehouse_thread_warehouse_cr'],              //仓库是否有CR
                static::$t['warehouse_thread_warehouse_electricity'],     //仓库电供应
                static::$t['warehouse_thread_warehouse_water'],           //仓库水供应
            ]);
        }

        $excel_header = array_merge($excel_header, [
            static::$t['warehouse_thread_landlord_type'],             //房东类型
            static::$t['warehouse_thread_landlord_aptitude'],         //房东资质文件是否齐全
            static::$t['warehouse_thread_plan_attachment'],           //平面图
            static::$t['warehouse_thread_warehouse_address'],         //仓库地址
            static::$t['warehouse_requirement_remark'],               //备注
            static::$t['warehouse_requirement_attachment'],           //附件
        ]);

        $thread_ids = array_values(array_unique(array_filter(array_column($relate_thread_list, 'id'))));

        //线索平面图
        $thread_plan_attachment = SysAttachmentRepository::getInstance()->getAttachmentsUrlsById($thread_ids, Enums::OSS_BUCKET_TYPE_WAREHOUSE_THREAD_PLAN_FILE);

        //仓库线索附件
        $thread_attachment  = SysAttachmentRepository::getInstance()->getAttachmentsUrlsById($thread_ids, Enums::OSS_BUCKET_TYPE_WAREHOUSE_THREAD_INFO_FILE);

        if ($country_code == GlobalEnums::TH_COUNTRY_CODE) {
            $thread_enums = ThreadService::getInstance()->getEnums();
            $rent_payment_method_map              = array_column($thread_enums['rent_payment_method'], 'label', 'value');
            $water_bill_payment_type_map          = array_column($thread_enums['water_bill_payment_type'], 'label', 'value');
            $electricity_bill_payment_type_map    = array_column($thread_enums['electricity_bill_payment_type'], 'label', 'value');
            $water_billing_method_map             = array_column($thread_enums['water_billing_method'], 'label', 'value');
            $electricity_billing_method_map       = array_column($thread_enums['electricity_billing_method'], 'label', 'value');
            $is_install_fire_extinguishers_map    = array_column($thread_enums['is_install_fire_extinguishers'], 'label', 'value');
            $withholding_tax_liability_bearer_map = array_column($thread_enums['withholding_tax_liability_bearer'], 'label', 'value');
            $land_tax_liability_bearer_map        = array_column($thread_enums['land_tax_liability_bearer'], 'label', 'value');
            $stamp_duty_liability_bearer_map      = array_column($thread_enums['stamp_duty_liability_bearer'], 'label', 'value');

            $th_fee_amount_unit = static::$t->_('th_fee_amount_unit');
        }

        $excel_data = [];
        foreach ($relate_thread_list as $item) {
            $currency = static::$t->_(GlobalEnums::$currency_item[$item['currency']]);

            $row = [
                $item['no'],                                                                                                   //线索编号
                $item['warehouse_latitude'],                                                                                   //仓库纬度
                $item['warehouse_longitude'],                                                                                  //仓库经度
                $item['intention_address_distance'],                                                                           //意向地址距离
                $item['warehouse_area'],                                                                                       //仓库面积
                $item['warehouse_month_rent'],                                                                                 //每月租金
                $item['contract_period'] . static::$t->_(WarehouseEnums::$contract_period_unit[$item['contract_period_unit']]),//合同期
                $item['deposit'],                                                                                              //押金
                $item['down_payment'],                                                                                         //预付款
            ];

            if ($country_code == GlobalEnums::TH_COUNTRY_CODE) {
                $rent_payment_method = !empty($item['rent_payment_method']) ? explode(',', $item['rent_payment_method']) : [];

                $rent_payment_method_item = [];
                foreach ($rent_payment_method as $method_val) {
                    $rent_payment_method_item[] = $rent_payment_method_map[$method_val] ?? '';
                }

                $water_fee_unit = '';
                if ($item['water_billing_method'] == WarehouseEnums::THREAD_WATER_BILLING_METHOD_UNIT) {
                    $water_fee_unit = static::$t->_('currency_thai_baht') . '/' . static::$t->_('water_billing_unit_ton');
                } elseif ($item['water_billing_method'] == WarehouseEnums::THREAD_WATER_BILLING_METHOD_UNITY) {
                    $water_fee_unit = static::$t->_('currency_thai_baht');
                }
                $water_usage_units = $item['water_usage_units'] . $water_fee_unit;

                $electricity_fee_unit = '';
                if ($item['electricity_billing_method'] == WarehouseEnums::THREAD_ELECTRICITY_BILLING_METHOD_UNIT) {
                    $electricity_fee_unit = static::$t->_('currency_thai_baht') . '/' . static::$t->_('electricity_billing_unit_kwh');
                } elseif ($item['electricity_billing_method'] == WarehouseEnums::THREAD_ELECTRICITY_BILLING_METHOD_UNITY) {
                    $electricity_fee_unit = static::$t->_('currency_thai_baht');
                }
                $electricity_usage_units = $item['electricity_usage_units'] . $electricity_fee_unit;

                $row = array_merge($row, [
                    implode(',', $rent_payment_method_item),
                    !empty($item['water_bill_payment_type']) ? $water_bill_payment_type_map[$item['water_bill_payment_type']] : '',
                    !empty($item['electricity_bill_payment_type']) ? $electricity_bill_payment_type_map[$item['electricity_bill_payment_type']] : '',
                    !empty($item['water_billing_method']) ? $water_billing_method_map[$item['water_billing_method']] : '',
                    $water_usage_units,
                    !empty($item['electricity_billing_method']) ? $electricity_billing_method_map[$item['electricity_billing_method']] : '',
                    $electricity_usage_units,
                    !empty($item['is_install_fire_extinguishers']) ? $is_install_fire_extinguishers_map[$item['is_install_fire_extinguishers']] : '',
                    !empty($item['withholding_tax_liability_bearer']) ? $withholding_tax_liability_bearer_map[$item['withholding_tax_liability_bearer']] : '',
                    !empty($item['land_tax_liability_bearer']) ? $land_tax_liability_bearer_map[$item['land_tax_liability_bearer']] : '',
                    !empty($item['stamp_duty_liability_bearer']) ? $stamp_duty_liability_bearer_map[$item['stamp_duty_liability_bearer']] : '',
                    $item['surrounding_stores_average_rent'] . $th_fee_amount_unit,
                    $item['other_options_average_cost'] . $th_fee_amount_unit,
                ]);

            } else {
                $row = array_merge($row, [
                    $item['cusa_fee'] . ' ' . $currency,                                          //Cusa费用
                    $item['maintenance_fee'] . $currency,                                         //维护费
                    $item['entrance_fee'] . ' ' . $currency,                                      //入场费
                    $item['garbage_fee'] . ' ' . $currency,                                       //垃圾收集费
                    $item['parking_fee'] . ' ' . $currency,                                       //停车费
                    $item['other_fee'] . ' ' . $currency,                                         //其他费用
                    static::$t->_(WarehouseEnums::$thread_has[$item['warehouse_cr']]),            //是否有CR
                    static::$t->_(WarehouseEnums::$thread_has[$item['warehouse_electricity']]),   //电供应
                    static::$t->_(WarehouseEnums::$thread_has[$item['warehouse_water']]),         //水供应
                ]);
            }

            $row = array_merge($row, [
                static::$t->_(WarehouseEnums::$landlord_type[$item['landlord_type']]),        //房东类型
                static::$t->_(WarehouseEnums::$landlord_aptitude[$item['landlord_aptitude']]),//资质文件是否齐全
                implode(PHP_EOL, $thread_plan_attachment[$item['id']] ?? []),                 //平面图
                $item['warehouse_address'],                                                   //仓库地址
                $item['remark'],                                                              //备注
                implode(PHP_EOL, $thread_attachment[$item['id']] ?? []),                      //仓库线索附件
            ]);

            $excel_data[] = $row;
        }

        unset($relate_thread_list);
        $result = $this->exportExcel($excel_header, $excel_data, 'requirement_confirm_thread_' . date('YmdHis') . '.xlsx');
        return [
            'code'    => ErrCode::$SUCCESS,
            'message' => '',
            'data'    => is_string($result['data']) ? $result['data'] : '',
        ];
    }

    /**
     * 处理仓库线索-待报价-去报价、处理仓库线索-待签约-重新报价、仓库信息管理-续签报价-获得默认值
     * @param array $params 参数组
     * @param array $user 当前登陆人信息组
     * @return array
     */
    public function getAddPriceDefault(array $params, array $user)
    {
        $code = ErrCode::$SUCCESS;
        $message = 'success';

        $data = [
            'id'                       => '',
            'no'                       => '',
            'thread_id'                => 0,
            'thread_no'                => '',
            'requirement_id'           => 0,
            'requirement_no'           => '',
            'store_id'                 => '',
            'store_name'               => '',
            'apply_type'               => '',
            'origin_warehouse_main_id' => 0,
            'origin_warehouse_id'      => '',
            'origin_warehouse_name'    => '',
            'province_code'            => '',
            'city_code'                => '',
            'warehouse_address'        => '',
            'warehouse_latitude'       => '',
            'warehouse_longitude'      => '',
            'warehouse_area'           => '',
            'warehouse_month_rent'     => '',
            'contract_period'          => '',
            'contract_period_unit'     => '',
            'deposit'                  => '',
            'down_payment'             => '',
            'cusa_fee'                 => '',
            'maintenance_fee'          => '',
            'entrance_fee'             => '',
            'garbage_fee'              => '',
            'parking_fee'              => '',
            'other_fee'                => '',

            'rent_payment_method'              => [],
            'water_bill_payment_type'          => '',
            'electricity_bill_payment_type'    => '',
            'water_billing_method'             => '',
            'water_usage_units'                => '',
            'electricity_billing_method'       => '',
            'electricity_usage_units'          => '',
            'is_install_fire_extinguishers'    => '',
            'withholding_tax_liability_bearer' => '',
            'land_tax_liability_bearer'        => '',
            'stamp_duty_liability_bearer'      => '',
            'surrounding_stores_average_rent'  => '',
            'other_options_average_cost'       => '',

            'landlord_type'            => '',
            'landlord_name'            => '',
            'landlord_aptitude'        => '',
            'warehouse_evaluate'       => '',
            'remark'                   => '',
            'attachments'              => [],
            'verify_info'              => (object)[],
            'auth_logs'                => [],
        ];
        try {
            $source_type = $params['source_type'];
            if ($source_type == WarehouseEnums::THREAD_PRICE_SOURCE_TYPE_THREAD) {
                $thread_info = $this->getThreadInfo($params['thread_id']);


                //驳回后的重新发起以及重新报价需要先加载原有的字段
                $price_info       = $thread_info->getPrice();
                $requirement_info = RequirementService::getInstance()->getRequirementInfo($thread_info->requirement_id);

                //因为一个线索一个报价
                if ($price_info && $price_info->requirement_id == $thread_info->requirement_id) {
                    //只返回本次报价需要字段信息
                    $price_data = $price_info->toArray();
                    foreach ($price_data as $key => $value) {
                        if (array_key_exists($key, $data)) {
                            $data[$key] = $value;
                        }
                    }

                    $data['attachments'] = $price_info->getAttachment()->toArray();

                    //审批日志 - 驳回后重新发起或重新报价需要显示原有的审批记录
                    $data['auth_logs']       = ($thread_info->status == WarehouseEnums::THREAD_STATUS_PRICE || $thread_info->status == WarehouseEnums::THREAD_STATUS_SIGN) ? PriceService::getInstance()->getAuthLogs($price_data['workflow_no'], $user['id']) : [];
                } else {
                    $data['no']                   = static::genSerialNo(WarehouseEnums::THREAD_PRICE_NO_PREFIX, RedisKey::WAREHOUSE_THREAD_PRICE_ADD_COUNTER);
                    $data['thread_id']            = $thread_info->id;
                    $data['thread_no']            = $thread_info->no;
                    $data['warehouse_address']    = $thread_info->warehouse_address;
                    $data['warehouse_latitude']   = $thread_info->warehouse_latitude;
                    $data['warehouse_longitude']  = $thread_info->warehouse_longitude;
                    $data['warehouse_area']       = $thread_info->warehouse_area;
                    $data['warehouse_month_rent'] = $thread_info->warehouse_month_rent;
                    $data['contract_period']      = $thread_info->contract_period;
                    $data['contract_period_unit'] = $thread_info->contract_period_unit;
                    $data['deposit']              = $thread_info->deposit;
                    $data['down_payment']         = $thread_info->down_payment;
                    $data['cusa_fee']             = $thread_info->cusa_fee;
                    $data['maintenance_fee']      = $thread_info->maintenance_fee;
                    $data['entrance_fee']         = $thread_info->entrance_fee;
                    $data['garbage_fee']          = $thread_info->garbage_fee;
                    $data['parking_fee']          = $thread_info->parking_fee;
                    $data['other_fee']            = $thread_info->other_fee;
                    $data['rent_payment_method']              = $thread_info->rent_payment_method;
                    $data['water_bill_payment_type']          = $thread_info->water_bill_payment_type;
                    $data['electricity_bill_payment_type']    = $thread_info->electricity_bill_payment_type;
                    $data['water_billing_method']             = $thread_info->water_billing_method;
                    $data['water_usage_units']                = $thread_info->water_usage_units;
                    $data['electricity_billing_method']       = $thread_info->electricity_billing_method;
                    $data['electricity_usage_units']          = $thread_info->electricity_usage_units;
                    $data['is_install_fire_extinguishers']    = $thread_info->is_install_fire_extinguishers;
                    $data['withholding_tax_liability_bearer'] = $thread_info->withholding_tax_liability_bearer;
                    $data['land_tax_liability_bearer']        = $thread_info->land_tax_liability_bearer;
                    $data['stamp_duty_liability_bearer']      = $thread_info->stamp_duty_liability_bearer;
                    $data['surrounding_stores_average_rent']  = $thread_info->surrounding_stores_average_rent;
                    $data['other_options_average_cost']       = $thread_info->other_options_average_cost;
                    $data['landlord_type']        = $thread_info->landlord_type;
                    $data['landlord_name']        = $thread_info->landlord_name;
                    $data['landlord_aptitude']    = $thread_info->landlord_aptitude;
                    $data['apply_type']           = static::getPriceApplyType($requirement_info->warehouse_type);
                }

                if (!empty($data['rent_payment_method'])) {
                    $data['rent_payment_method'] = array_map(function ($v) {
                        return (int)$v;
                    }, explode(',', $data['rent_payment_method']));
                } else {
                    $data['rent_payment_method'] = [];
                }

                $data['water_bill_payment_type']          = convert_type($data['water_bill_payment_type']);
                $data['electricity_bill_payment_type']    = convert_type($data['electricity_bill_payment_type']);
                $data['water_billing_method']             = convert_type($data['water_billing_method']);
                $data['electricity_billing_method']       = convert_type($data['electricity_billing_method']);
                $data['is_install_fire_extinguishers']    = convert_type($data['is_install_fire_extinguishers']);
                $data['withholding_tax_liability_bearer'] = convert_type($data['withholding_tax_liability_bearer']);
                $data['land_tax_liability_bearer']        = convert_type($data['land_tax_liability_bearer']);
                $data['stamp_duty_liability_bearer']      = convert_type($data['stamp_duty_liability_bearer']);

                //验仓结果
                $data['requirement_id'] = $requirement_info->id;
                $data['requirement_no'] = $requirement_info->no;
                $data['store_id']       = $requirement_info->store_id;
                $data['store_name']     = $requirement_info->store_name;
                $data['province_code']  = $requirement_info->province_code;
                $data['city_code']      = $requirement_info->city_code;
                $data['verify_info']    = $this->getThreadVerifySubmitDetail($params['thread_id']);
            } elseif ($source_type == WarehouseEnums::THREAD_PRICE_SOURCE_TYPE_WAREHOUSE) {
                //获取仓库信息
                $warehouse_model = ContractWarehouseRepository::getInstance()->getOneById($params['warehouse_main_id']);
                if (empty($warehouse_model)) {
                    throw new ValidationException(static::$t->_('warehouse_info_is_null_hint', ['warehouse_id' => $params['warehouse_main_id']]), ErrCode::$VALIDATE_ERROR);
                }

                //获取主网点信息
                $warehouse_store = WarehouseStoreModel::findFirst([
                    'conditions' => 'store_flag = :store_flag: AND warehouse_main_id = :warehouse_main_id: AND use_status = :use_status:',
                    'bind' => ['store_flag' => ContractEnums::WAREHOUSE_STORE_FLAG_MAIN, 'warehouse_main_id' => $warehouse_model->id, 'use_status' => ContractEnums::WAREHOUSE_STORE_USE_STATUS_ING],
                ]);
                if (empty($warehouse_store)) {
                    throw new ValidationException(static::$t->_('warehouse_main_not_exists'), ErrCode::$VALIDATE_ERROR);
                }

                $data['no']                       = static::genSerialNo(WarehouseEnums::THREAD_PRICE_NO_PREFIX, RedisKey::WAREHOUSE_THREAD_PRICE_ADD_COUNTER);
                $data['origin_warehouse_main_id'] = $warehouse_model->id;
                $data['origin_warehouse_id']      = $warehouse_model->warehouse_id;
                $data['origin_warehouse_name']    = $warehouse_model->warehouse_name;
                $data['province_code']            = $warehouse_model->province_code;
                $data['city_code']                = $warehouse_model->city_code;
                $data['warehouse_latitude']       = $warehouse_model->warehouse_latitude;
                $data['warehouse_longitude']      = $warehouse_model->warehouse_longitude;
                $data['warehouse_area']           = $warehouse_model->real_area;
                $data['store_id']                 = $warehouse_store->store_id;
                $store_info                       = (new StoreRepository())->getStoreDetail($warehouse_store->store_id, 0);
                $data['store_name']               = $store_info['name'] ?? '';
                //当从仓库管理-仓库信息管理-续签报价时，默认值为Renew，不允许修改
                $data['apply_type'] = (string)WarehouseEnums::THREAD_APPLY_TYPE_RENEW;

            } elseif ($source_type == WarehouseEnums::THREAD_PRICE_SOURCE_TYPE_RM_RENEWED) {
                $requirement_info = RequirementService::getInstance()->getRequirementInfo($params['requirement_id']);

                // 获取仓库信息
                $warehouse_model = ContractWarehouseRepository::getInstance()->getOneByWarehouseId($requirement_info->original_warehouse_id);
                $main_store_info = [];
                if (!empty($warehouse_model)) {
                    $main_store_info = $warehouse_model->getMainStoreInfo();
                    $main_store_info = !empty($main_store_info) ? $this->getWarehouseStoreDetail([$main_store_info->toArray()])[0] : [];
                }

                $data['no']         = static::genSerialNo(WarehouseEnums::THREAD_PRICE_NO_PREFIX, RedisKey::WAREHOUSE_THREAD_PRICE_ADD_COUNTER);
                $data['apply_type'] = (string)WarehouseEnums::THREAD_APPLY_TYPE_RENEW;

                $data['requirement_id']           = $requirement_info->id;
                $data['requirement_no']           = $requirement_info->no;

                $data['store_id']                 = $main_store_info['store_id'] ?? '';
                $data['store_name']               = $main_store_info['store_name'] ?? '';
                $data['origin_warehouse_id']      = $requirement_info->original_warehouse_id;
                $data['origin_warehouse_main_id'] = $warehouse_model->id ?? 0;
                $data['origin_warehouse_name']    = $warehouse_model->warehouse_name ?? '';
                $data['warehouse_latitude']       = $warehouse_model->warehouse_latitude ?? '';
                $data['warehouse_longitude']      = $warehouse_model->warehouse_longitude ?? '';
                $data['warehouse_area']           = $warehouse_model->real_area ?? '';
                $data['province_code']            = $warehouse_model->province_code ?? '';
                $data['city_code']                = $warehouse_model->city_code ?? '';
            }
            $this->logger->info('仓库管理-处理仓库线索或仓库信息管理-报价-获得默认值：' . json_encode($data, JSON_UNESCAPED_UNICODE));
        } catch (ValidationException $e) {
            $code    = $e->getCode();
            $message = $e->getMessage();
            $data    = [];
        } catch (\Exception $e) {
            $code    = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $data    = [];
            $this->logger->warning('仓库管理-处理仓库线索或仓库信息管理-报价-获得默认值:' . $e->getMessage());
        }

        return [
            'code'    => $code,
            'message' => $message,
            'data'    => $data,
        ];
    }

    /**
     * 报价-申请类型获取
     */
    public static function getPriceApplyType($warehouse_type)
    {
        $apply_type = '';
        $country_code = get_country_code();
        if ($country_code == GlobalEnums::TH_COUNTRY_CODE) {
            $warehouse_type_config = static::getWarehouseTypeConstantConfig();

            // 仓库类型为新开网点或临时仓库或站点则默认为New
            if (in_array($warehouse_type, [
                $warehouse_type_config[WarehouseEnums::REQUIREMENT_WAREHOUSE_TYPE_NEW_STORE],
                $warehouse_type_config[WarehouseEnums::REQUIREMENT_WAREHOUSE_TYPE_TEMPORARY],
                $warehouse_type_config[WarehouseEnums::REQUIREMENT_WAREHOUSE_TYPE_SITE],
            ])) {
                $apply_type = WarehouseEnums::THREAD_APPLY_TYPE_NEW;
            } elseif ($warehouse_type == $warehouse_type_config[WarehouseEnums::REQUIREMENT_WAREHOUSE_TYPE_CHANGE_ADDR]) {
                // 仓库类型为更换地址则为Replace
                $apply_type = WarehouseEnums::THREAD_APPLY_TYPE_REPLACE;
            }

        } else {
            if ($warehouse_type == WarehouseEnums::REQUIREMENT_WAREHOUSE_TYPE_ADD) {
                //如果需求中仓库类型为拆分新建则默认为New
                $apply_type = WarehouseEnums::THREAD_APPLY_TYPE_NEW;
            } elseif (in_array($warehouse_type, [WarehouseEnums::REQUIREMENT_WAREHOUSE_TYPE_REPLACE, WarehouseEnums::REQUIREMENT_WAREHOUSE_TYPE_OPTIMIZE])) {
                //如果需求中仓库类型为到期更换或优化仓则为Replace
                $apply_type = WarehouseEnums::THREAD_APPLY_TYPE_REPLACE;
            }
        }

        return (string)$apply_type;
    }

    /**
     * 获取同区域已租赁仓库平均价格-查询条件
     * @param object $builder 查询对象
     * @param array $params 参数组
     * @param array $warehouse_price_province_codes 仓库管理-报价-按市计算区域平均价格省编码
     * @return mixed
     */
    private function getAveragePriceConditions(object $builder, array $params, array $warehouse_price_province_codes)
    {
        //付款方式不等于一次性付款
        $builder->where('main.contract_lease_type != :contract_lease_type:', ['contract_lease_type' => ContractEnums::CONTRACT_STORE_RENTING_CONTRACT_LEASE_TYPE_ONE]);
        //审批状态为已通过
        $builder->andWhere('main.contract_status = :contract_status:', ['contract_status' => Enums::WF_STATE_APPROVED]);
        //当前日期-合同开始日期<=365
        $builder->andWhere('DATEDIFF(CURDATE(), main.contract_begin) <= :days:', ['days' => 365]);
        //本次报价区域 = 如果省属于配置的按市计算区域平均价格省编码则取市;如果本次报价区域为市，则获取网点租房合同中关联仓库所在市等于本次报价区域
        if (in_array($params['province_code'], $warehouse_price_province_codes)) {
            $builder->andWhere('warehouse.city_code = :city_code:', ['city_code' => $params['city_code']]);
        } else {
            //本次报价区域 = 如果省不属于配置则取省;如果本次报价区域为省，则获取网点租房合同中关联仓库所在省等于本次报价区域
            $builder->andWhere('warehouse.province_code = :province_code:', ['province_code' => $params['province_code']]);
        }
        return $builder;
    }

    /**
     * 处理仓库线索-待报价-去报价、处理仓库线索-待签约-重新报价、仓库信息管理-续签报价-同区域已租赁仓库平均价格
     * @param array $params 参数组
     * @return array
     */
    public function getLeasedAveragePrice(array $params)
    {
        $code = ErrCode::$SUCCESS;
        $message = 'success';

        $data = [];
        try {
            //仓库管理-报价-按市计算区域平均价格省编码
            $warehouse_price_province_codes = EnumsService::getInstance()->getSettingEnvValueIds('warehouse_price_province_codes');
            $builder = $this->modelsManager->createBuilder();
            $builder->from(['main' => ContractStoreRentingModel::class]);
            $builder->leftJoin(ContractWarehouseModel::class, 'warehouse.warehouse_id = main.warehouse_id', 'warehouse');
            $builder = $this->getAveragePriceConditions($builder, $params, $warehouse_price_province_codes);
            //合同数量
            $total_count = intval($builder->columns('COUNT(main.id) as total_count')->getQuery()->getSingleResult()->total_count);
            if ($total_count) {
                // 查询获取合同详情
                $detail_builder = $this->modelsManager->createBuilder();
                $detail_builder->from(['main' => ContractStoreRentingModel::class]);
                $detail_builder->leftJoin(ContractWarehouseModel::class, 'warehouse.warehouse_id = main.warehouse_id', 'warehouse');
                $detail_builder->leftJoin(ContractStoreRentingDetailModel::class, 'detail.contract_store_renting_id = main.id', 'detail');
                $detail_builder = $this->getAveragePriceConditions($detail_builder, $params, $warehouse_price_province_codes);

                $detail_builder->columns('main.id, main.house_contract_area, main.contract_lease_type, SUM(detail.amount_has_tax) as total_amount_has_tax, COUNT(main.id) as total_detail_count');
                $detail_builder->groupBy('main.id');
                $items = $detail_builder->getQuery()->execute()->toArray();

                //如果获取到数据，则平均价格=所有满足条件合同的平均价格的合计/合同数量，四舍五入保留2位小数
                $total_average_price = '0';
                foreach ($items as $item) {
                    // 检查除数是否为零
                    if ($item['total_detail_count'] <= 0 || $item['house_contract_area'] <= 0) {
                        continue;
                    }
                    //租房合同-付款方式 - 对应月份得到月份
                    $month = ContractEnums::$contract_lease_type_month[$item['contract_lease_type']] ?? 0;
                    if ($month <= 0) {
                        continue;
                    }

                    // 先计算租金信息中每月含税金额的合计/租金信息行数
                    $amount = bcdiv($item['total_amount_has_tax'], $item['total_detail_count'], 10);

                    // 再除以月份得到月租金
                    $monthly_amount = bcdiv($amount, $month, 10);

                    // 再除以房屋合约面积
                    $average_price = bcdiv($monthly_amount, $item['house_contract_area'], 10);

                    //合同的平均价格的合计
                    $total_average_price = bcadd($total_average_price, $average_price, 10);
                }

                //最后再四舍五入到2位小数
                $data = round(bcdiv($total_average_price, $total_count, 10), 2);
                //若四舍五入后，结果是个0
                if (empty($data)) {
                    $data = static::$t->_('same_area_leased_average_price_null_tips');
                }
            } else {
                //如果未获取到数据
                $data = static::$t->_('same_area_leased_average_price_null_tips');
            }
        } catch (\Exception $e) {
            $code = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $this->logger->warning('仓库管理-处理仓库线索或仓库信息管理-报价-同区域已租赁仓库平均价格:' . $e->getMessage());
        }

        return [
            'code'    => $code,
            'message' => $message,
            'data'    => $data,
        ];
    }

    /**
     * 处理仓库线索-待报价-去报价、处理仓库线索-待签约-重新报价、仓库信息管理-续签报价-上次报价信息
     * @param array $params 参数组
     * @return array
     */
    public function getLastPriceInfo(array $params)
    {
        $code = ErrCode::$SUCCESS;
        $message = 'success';

        $data = [
            'last_price_id'                   => 0,
            'last_price_warehouse_address'    => '',
            'last_price_warehouse_latitude'   => '',
            'last_price_warehouse_longitude'  => '',
            'last_price_warehouse_area'       => '',
            'last_price_warehouse_month_rent' => '',
            'last_price_contract_period'      => '',
            'last_price_contract_period_unit' => '',
            'last_price_deposit'              => '',
            'last_price_down_payment'         => '',
            'last_price_cusa_fee'             => '',
            'last_price_maintenance_fee'      => '',
            'last_price_entrance_fee'         => '',
            'last_price_garbage_fee'          => '',
            'last_price_parking_fee'          => '',
            'last_price_other_fee'            => '',
            'last_price_rent_payment_method'              => [],
            'last_price_water_bill_payment_type'          => '',
            'last_price_electricity_bill_payment_type'    => '',
            'last_price_water_billing_method'             => '',
            'last_price_water_usage_units'                => '',
            'last_price_electricity_billing_method'       => '',
            'last_price_electricity_usage_units'          => '',
            'last_price_is_install_fire_extinguishers'    => '',
            'last_price_withholding_tax_liability_bearer' => '',
            'last_price_land_tax_liability_bearer'        => '',
            'last_price_stamp_duty_liability_bearer'      => '',
            'last_price_surrounding_stores_average_rent'  => '',
            'last_price_other_options_average_cost'       => '',
            'last_price_landlord_type'        => '',
            'last_price_landlord_name'        => '',
            'last_price_landlord_aptitude'    => '',
            'last_price_warehouse_evaluate'   => '',
            'last_price_remark'               => '',
            'last_price_source'               => WarehouseEnums::THREAD_LAST_PRICE_NONE,
            'last_price_attachments'          => [],
        ];
        try {
            $warehouse_id = $params['warehouse_id'];
            //获取审批通过的 仓库ID关联的所有线索的报价信息 或 原仓库ID为本次仓库ID的 最新的本次报价信息作为上次报价信息
            $builder = $this->modelsManager->createBuilder();
            $builder->columns([
                'main.id as last_price_id',
                'main.warehouse_address as last_price_warehouse_address',
                'main.warehouse_latitude as last_price_warehouse_latitude',
                'main.warehouse_longitude as last_price_warehouse_longitude',
                'main.warehouse_area as last_price_warehouse_area',
                'main.warehouse_month_rent as last_price_warehouse_month_rent',
                'main.contract_period as last_price_contract_period',
                'main.contract_period_unit as last_price_contract_period_unit',
                'main.deposit as last_price_deposit',
                'main.down_payment as last_price_down_payment',
                'main.cusa_fee as last_price_cusa_fee',
                'main.maintenance_fee as last_price_maintenance_fee',
                'main.entrance_fee as last_price_entrance_fee',
                'main.garbage_fee as last_price_garbage_fee',
                'main.parking_fee as last_price_parking_fee',
                'main.other_fee as last_price_other_fee',
                'main.rent_payment_method as last_price_rent_payment_method',
                'main.water_bill_payment_type as last_price_water_bill_payment_type',
                'main.electricity_bill_payment_type as last_price_electricity_bill_payment_type',
                'main.water_billing_method as last_price_water_billing_method',
                'main.water_usage_units as last_price_water_usage_units',
                'main.electricity_billing_method as last_price_electricity_billing_method',
                'main.electricity_usage_units as last_price_electricity_usage_units',
                'main.is_install_fire_extinguishers as last_price_is_install_fire_extinguishers',
                'main.withholding_tax_liability_bearer as last_price_withholding_tax_liability_bearer',
                'main.land_tax_liability_bearer as last_price_land_tax_liability_bearer',
                'main.stamp_duty_liability_bearer as last_price_stamp_duty_liability_bearer',
                'main.surrounding_stores_average_rent as last_price_surrounding_stores_average_rent',
                'main.other_options_average_cost as last_price_other_options_average_cost',
                'main.landlord_type as last_price_landlord_type',
                'main.landlord_name as last_price_landlord_name',
                'main.landlord_aptitude as last_price_landlord_aptitude',
                'main.warehouse_evaluate as last_price_warehouse_evaluate',
                'main.remark as last_price_remark',
            ]);
            $builder->from(['main' => WarehouseThreadPriceRecordModel::class]);
            $builder->leftJoin(WarehouseThreadModel::class, 'thread.id = main.thread_id', 'thread');
            $builder->where('main.status = :status:', ['status' => Enums::WF_STATE_APPROVED]);
            $builder->andWhere('thread.warehouse_id = :warehouse_id: OR main.origin_warehouse_id = :warehouse_id:', ['warehouse_id' => $warehouse_id]);
            $builder->orderBy('main.id DESC');
            $builder->limit(1);
            $item = $builder->getQuery()->getSingleResult();
            if (!empty($item)) {
                $data = $item->toArray();
                if (!empty($data['last_price_rent_payment_method'])) {
                    $data['last_price_rent_payment_method'] = array_map(function ($v) {
                        return (int)$v;
                    }, explode(',', $data['last_price_rent_payment_method']));

                } else {
                    $data['last_price_rent_payment_method'] = [];
                }

                $data['last_price_water_bill_payment_type']          = convert_type($data['last_price_water_bill_payment_type']);
                $data['last_price_electricity_bill_payment_type']    = convert_type($data['last_price_electricity_bill_payment_type']);
                $data['last_price_water_billing_method']             = convert_type($data['last_price_water_billing_method']);
                $data['last_price_electricity_billing_method']       = convert_type($data['last_price_electricity_billing_method']);
                $data['last_price_is_install_fire_extinguishers']    = convert_type($data['last_price_is_install_fire_extinguishers']);
                $data['last_price_withholding_tax_liability_bearer'] = convert_type($data['last_price_withholding_tax_liability_bearer']);
                $data['last_price_land_tax_liability_bearer']        = convert_type($data['last_price_land_tax_liability_bearer']);
                $data['last_price_stamp_duty_liability_bearer']      = convert_type($data['last_price_stamp_duty_liability_bearer']);

                $data['last_price_source'] = WarehouseEnums::THREAD_LAST_PRICE_PRICE;
                $data['last_price_attachments'] = PriceService::getInstance()->getPriceAttachments($data['last_price_id']);
            } else {
                //无满足条件的报价信息，则获取仓库关联的网点租房合同(审批状态为已通过、付款方式不等于一次性付款)并且合同结束日期最晚的合同,结束日期相同取合同编号最大的
                $contract_builder = $this->modelsManager->createBuilder();
                $contract_builder->columns([
                    'main.id as last_price_id',
                    'main.store_addr as last_price_warehouse_address',
                    'main.warehouse_latitude as last_price_warehouse_latitude',
                    'main.warehouse_longitude as last_price_warehouse_longitude',
                    'main.warehouse_real_area as last_price_warehouse_area',
                    'main.contract_deadline as last_price_contract_period',
                    'main.deposit_amount as last_price_deposit',
                    'main.contract_deposit as last_price_down_payment',
                    'main.house_owner_type as last_price_landlord_type',
                    'main.house_owner_name as last_price_landlord_name',
                    'main.contract_lease_type'
                ]);
                $contract_builder->from(['main' => ContractStoreRentingModel::class]);
                $contract_builder->where('main.contract_status = :contract_status: and main.warehouse_id = :warehouse_id:', ['contract_status' => Enums::WF_STATE_APPROVED, 'warehouse_id' => $warehouse_id]);
                $contract_builder->andWhere('main.contract_lease_type != :contract_lease_type:', ['contract_lease_type' => ContractEnums::CONTRACT_STORE_RENTING_CONTRACT_LEASE_TYPE_ONE]);
                $contract_builder->groupBy('main.id');
                $contract_builder->orderBy('main.contract_end DESC, contract_id DESC');
                $contract_builder->limit(1);
                $contract_item = $contract_builder->getQuery()->getSingleResult();
                if ($contract_item) {
                    $contract_data = $contract_item->toArray();
                    //获取合同租金详情中的含税金额合计/行数/月数
                    $month = ContractEnums::$contract_lease_type_month[$contract_data['contract_lease_type']] ?? 0;

                    //将合同获取的上次报价有值的信息灌入到返回值里
                    foreach ($contract_data as $key => $value) {
                        if (array_key_exists($key, $data)) {
                            $data[$key] = $value;
                        }
                    }
                    $data['last_price_landlord_type']        = ($data['last_price_landlord_type'] == WarehouseEnums::THREAD_LANDLORD_TYPE_COMPANY || $data['last_price_landlord_type'] == WarehouseEnums::THREAD_LANDLORD_TYPE_PERSONAL) ? $data['last_price_landlord_type'] : '';
                    $data['last_price_contract_period_unit'] = (string)WarehouseEnums::THREAD_CONTRACT_PERIOD_UNIT_MONTH;
                    $data['last_price_source']               = WarehouseEnums::THREAD_LAST_PRICE_CONTRACT;

                    //获取详情
                    $details = ContractStoreRentingDetailModel::findFirst([
                        'columns' => 'SUM(amount_has_tax) as total_amount_has_tax, COUNT(id) as total_detail_count',
                        'conditions' => 'contract_store_renting_id = :contract_store_renting_id:',
                        'bind' => ['contract_store_renting_id' => $data['last_price_id']]
                    ]);
                    $total_detail_count   = $details ? $details->total_detail_count : 0;
                    $total_amount_has_tax = $details ? $details->total_amount_has_tax : 0;
                    if ($total_detail_count && $month) {
                        $data['last_price_warehouse_month_rent'] = round(bcdiv(bcdiv($total_amount_has_tax, $total_detail_count, 10), $month, 10), 2);
                        $data['last_price_deposit'] = round(bcdiv($data['last_price_deposit'], $data['last_price_warehouse_month_rent'], 10), 2);//获取合同信息中的合同押金/仓库每月租价(PHP)，四舍五入保留2位小数\
                        $data['last_price_down_payment'] = round(bcdiv($data['last_price_down_payment'], $data['last_price_warehouse_month_rent'], 10), 2);//获取合同信息中的合同定金/仓库每月租价(PHP)，四舍五入保留2位小数
                    } else {
                        $data['last_price_warehouse_month_rent'] = '';
                        $data['last_price_deposit']              = '';
                        $data['last_price_down_payment']         = '';
                    }
                }
            }
        } catch (\Exception $e) {
            $code    = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $this->logger->warning('仓库管理-处理仓库线索或仓库信息管理-报价-上次报价信息:' . $e->getMessage());
        }

        return [
            'code'    => $code,
            'message' => $message,
            'data'    => $data,
        ];
    }


    /**
     * 添加报价参数拦截
     * @param array $params 参数组
     * @param integer $source_type 数据来源：0-仓库管理-处理仓库线索-待报价-发起报价、1-仓库管理-仓库信息管理-续签报价
     * @throws ValidationException
     */
    public function addPriceValidation(array $params, int $source_type)
    {
        $validation = $this->getAddPriceValidation(self::$validate_add_price);
        $validation['warehouse_area'] = 'Required|Regexp:' . WarehouseEnums::AREA_RULE_PRICE . '|>>>:' . static::$t->_('warehouse_add_real_area_error');

        //仓库管理-处理仓库线索-待报价-发起报价/重新报价 - 线索需求必须有
        if ($source_type == WarehouseEnums::THREAD_PRICE_SOURCE_TYPE_THREAD) {
            $validation_thread = [
                'requirement_id' => 'Required|IntGt:0',        //需求表主键ID
                'requirement_no' => 'Required|StrLenGeLe:1,32',//需求ID
                'thread_id'      => 'Required|IntGt:0',        //线索表主键ID
                'thread_no'      => 'Required|StrLenGeLe:1,32',//线索编号

            ];
            $validation = array_merge($validation, $validation_thread);

        } elseif ($source_type == WarehouseEnums::THREAD_PRICE_SOURCE_TYPE_RM_RENEWED) {
            // 来自处理仓库需求-待续约-发起报价-提交
            $validation_thread = [
                'requirement_id' => 'Required|IntGt:0',        //需求表主键ID
                'requirement_no' => 'Required|StrLenGeLe:1,32',//需求ID
            ];
            $validation = array_merge($validation, $validation_thread);
        }

        $country_code = get_country_code();

        //当申请类型为Renew和Replace时显示
        if (isset($params['apply_type']) && in_array($params['apply_type'], [WarehouseEnums::THREAD_APPLY_TYPE_RENEW, WarehouseEnums::THREAD_APPLY_TYPE_REPLACE])) {
            //上次报价信息
            $last_price = [
                'last_price_source'             => 'Required|IntIn:' . WarehouseEnums::THREAD_LAST_PRICE_VALIDATE, //上次报价信息来源：0-无、1-报价单、2-网点租房合同
                'last_price_id'                 => 'Required|IntGe:0', //上次报价信息-单据ID：报价单ID、网点租房合同ID
                'last_price_landlord_type'      => 'Required|IntIn:' . WarehouseEnums::THREAD_LANDLORD_TYPE_VALIDATE, //上次报价信息-房东类型：1公司、2个人
                'last_price_landlord_aptitude'  => 'Required|IntIn:' . WarehouseEnums::THREAD_LANDLORD_APTITUDE_VALIDATE, //上次报价信息-房东资质文件是否齐全：1-齐全、2-不齐全
                'last_price_warehouse_evaluate' => 'Required|StrLenGeLe:1,500', //上次报价信息-仓库评估
                'last_price_remark'             => 'StrLenGeLe:0,1000', //上次报价信息-备注
            ];

            if ($country_code == GlobalEnums::PH_COUNTRY_CODE) {
                $last_price = array_merge($last_price, [
                    'last_price_cusa_fee'           => 'Required|Regexp:' . WarehouseEnums::PRICE. '|>>>:' . static::$t->_('warehouse_thread_price_error'), //上次报价信息-Cusa费用
                    'last_price_maintenance_fee'    => 'Required|Regexp:' . WarehouseEnums::PRICE . '|>>>:' . static::$t->_('warehouse_thread_price_error'), //上次报价信息-维护费
                    'last_price_entrance_fee'       => 'Required|Regexp:' . WarehouseEnums::PRICE . '|>>>:' . static::$t->_('warehouse_thread_price_error'), //上次报价信息-入场费
                    'last_price_garbage_fee'        => 'Required|Regexp:' . WarehouseEnums::PRICE . '|>>>:' . static::$t->_('warehouse_thread_price_error'), //上次报价信息-垃圾收理费
                    'last_price_parking_fee'        => 'Required|Regexp:' . WarehouseEnums::PRICE . '|>>>:' . static::$t->_('warehouse_thread_price_error'), //上次报价信息-停车费
                    'last_price_other_fee'          => 'Required|Regexp:' . WarehouseEnums::PRICE . '|>>>:' . static::$t->_('warehouse_thread_price_error'),//上次报价信息-其他费用
                ]);
            } elseif ($country_code == GlobalEnums::TH_COUNTRY_CODE) {
                $rent_payment_methods = implode(',', array_keys(WarehouseEnums::$thread_rent_payment_method));
                $last_price = array_merge($last_price, [
                    'last_price_rent_payment_method' => 'Required|Arr|>>>:' . static::$t->_('params_error', ['param' => 'last_price_rent_payment_method']),
                    'last_price_rent_payment_method[*]' => "Required|IntIn:{$rent_payment_methods}|>>>:" . static::$t->_('params_error', ['param' => 'last_price_rent_payment_method']),
                    'last_price_water_bill_payment_type' => 'Required|IntIn:1,2|>>>:' . static::$t->_('params_error', ['param' => 'last_price_water_bill_payment_type']),
                    'last_price_electricity_bill_payment_type' => 'Required|IntIn:1,2|>>>:' . static::$t->_('params_error', ['param' => 'last_price_electricity_bill_payment_type']),
                    'last_price_water_billing_method' => 'Required|IntIn:1,2|>>>:' . static::$t->_('params_error', ['param' => 'last_price_water_billing_method']),
                    'last_price_water_usage_units' => 'Required|FloatGeLe:0,*********.99|>>>:' . static::$t->_('params_error', ['param' => 'last_price_water_usage_units']),
                    'last_price_electricity_billing_method' => 'Required|IntIn:1,2|>>>:' . static::$t->_('params_error', ['param' => 'last_price_electricity_billing_method']),
                    'last_price_electricity_usage_units' => 'Required|FloatGeLe:0,*********.99|>>>:' . static::$t->_('params_error', ['param' => 'last_price_electricity_usage_units']),
                    'last_price_is_install_fire_extinguishers' => 'Required|IntIn:1,2|>>>:' . static::$t->_('params_error', ['param' => 'last_price_is_install_fire_extinguishers']),
                    'last_price_withholding_tax_liability_bearer' => 'Required|IntIn:1,2|>>>:' . static::$t->_('params_error', ['param' => 'last_price_withholding_tax_liability_bearer']),
                    'last_price_land_tax_liability_bearer' => 'Required|IntIn:1,2|>>>:' . static::$t->_('params_error', ['param' => 'last_price_land_tax_liability_bearer']),
                    'last_price_stamp_duty_liability_bearer' => 'Required|IntIn:1,2|>>>:' . static::$t->_('params_error', ['param' => 'last_price_stamp_duty_liability_bearer']),
                    'last_price_surrounding_stores_average_rent' => 'Required|FloatGeLe:0,*********.99|>>>:' . static::$t->_('params_error', ['param' => 'last_price_surrounding_stores_average_rent']),
                    'last_price_other_options_average_cost' => 'Required|FloatGeLe:0,*********.99|>>>:' . static::$t->_('params_error', ['param' => 'last_price_other_options_average_cost']),
                ]);
            }

            if (isset($params['last_price_source']) && $params['last_price_source'] == WarehouseEnums::THREAD_LAST_PRICE_NONE) {
                //如果则全部字段为空，所有字段填写规则与本次报价信息完全一致
                $last_price = array_merge($last_price, [
                    'last_price_warehouse_address'    => 'Required|StrLenGeLe:1,500', //上次报价信息-仓库地址
                    'last_price_warehouse_area'       => 'Required|Regexp:' . WarehouseEnums::AREA_RULE_PRICE . '|>>>:' . static::$t->_('warehouse_add_real_area_error'), //上次报价信息-仓库面积
                    'last_price_warehouse_month_rent' => 'Required|FloatGt:0|Regexp:' . WarehouseEnums::PRICE, //上次报价信息-仓库每月租价
                    'last_price_contract_period'      => 'Required|IntGtLe:0,999999', //上次报价信息-合同期限
                    'last_price_contract_period_unit' => 'Required|IntIn:' . WarehouseEnums::THREAD_CONTRACT_PERIOD_UNIT_VALIDATE, //上次报价信息-合同期单位
                    'last_price_deposit'              => 'Required|FloatGeLe:0,99.99', //上次报价信息-押金
                    'last_price_down_payment'         => 'Required|FloatGeLe:0,99.99',//上次报价信息-预付金
                    'last_price_landlord_name'        => 'Required|StrLenGeLe:1,50',//上次报价信息-房东联系人
                    'last_price_attachments'          => 'Arr|ArrLenGeLe:0,20',//上次报价信息-附件
                ]);
            }

            $validation = array_merge($validation, $last_price);
            $is_price = true;
        }

        self::commonValidate($params, $validation, $is_price ?? false);
    }


    /**
     * 处理仓库线索-待报价-去报价、处理仓库线索-待签约-重新报价、仓库信息管理-续签报价
     * 处理仓库需求-待续约-续约报价
     * @param array $params 参数组
     * @param array $user 当前登陆人信息组
     * @param integer $source_type 数据来源：0-仓库管理-处理仓库线索-待报价-发起报价、1-仓库管理-仓库信息管理-续签报价
     * @return array
     */
    public function addPrice(array $params, array $user, int $source_type = WarehouseEnums::THREAD_PRICE_SOURCE_TYPE_THREAD)
    {
        $code    = ErrCode::$SUCCESS;
        $message = $real_message = '';

        $db = $this->getDI()->get('db_oa');
        $db->begin();
        try {
            self::addPriceValidation($params, $source_type);
            $this->savePriceDb($db, $params, $user, $source_type);
            $db->commit();
        } catch (ValidationException $e) {
            $code    = $e->getCode();
            $message = $e->getMessage();
        } catch (BusinessException $e) {
            $code         = $e->getCode();
            $message      = static::$t->_('retry_later');
            $real_message = $e->getMessage() . $e->getTraceAsString();
        } catch (Exception $e) {
            $code         = ErrCode::$SYSTEM_ERROR;
            $message      = static::$t->_('retry_later');
            $real_message = $e->getMessage() . $e->getTraceAsString();
        }

        if ($message) {
            $db->rollback();
        }
        if ($real_message) {
            $this->logger->error('仓库管理-处理仓库线索或仓库信息管理-报价失败: ' . $real_message);
        }

        return [
            'code'    => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data'    => $data ?? [],
        ];
    }

    /**
     * 保存报价
     * @param object $db 事物对象
     * @param array $params 参数组
     * @param array $user 当前登陆者信息组
     * @param integer $source_type 数据来源：0-仓库管理-处理仓库线索-待报价-发起报价、1-仓库管理-仓库信息管理-续签报价
     * @throws BusinessException
     * @throws ValidationException
     */
    private function savePriceDb(object $db, array $params, array $user, int $source_type)
    {
        $rent_payment_method = '';
        if (!empty($params['rent_payment_method'])) {
            $rent_payment_method = implode(',', array_unique($params['rent_payment_method']));
        }

        $last_price_rent_payment_method = '';
        if (!empty($params['last_price_rent_payment_method'])) {
            $last_price_rent_payment_method = implode(',', array_unique($params['last_price_rent_payment_method']));
        }

        $now  = date('Y-m-d H:i:s');
        $data = [
            'requirement_id'                  => $params['requirement_id'] ?? 0,
            'requirement_no'                  => $params['requirement_no'] ?? '',
            'thread_id'                       => $params['thread_id'] ?? 0,
            'thread_no'                       => $params['thread_no'] ?? '',
            'store_id'                        => $params['store_id'],
            'store_name'                      => $params['store_name'],
            'apply_date'                      => date('Y-m-d'),
            'status'                          => Enums::WF_STATE_PENDING,
            'apply_type'                      => $params['apply_type'],
            'origin_warehouse_main_id'        => $params['origin_warehouse_main_id'] ?? 0,
            'origin_warehouse_id'             => $params['origin_warehouse_id'] ?? '',
            'origin_warehouse_name'           => $params['origin_warehouse_name'] ?? '',
            'leased_warehouse_average_price'  => $params['leased_warehouse_average_price'],
            'lease_warehouse_price'           => round(bcdiv($params['warehouse_month_rent'], $params['warehouse_area'], 10), 2),//本次报价中的仓库每月租价/仓库面积，保留2位小数
            'warehouse_address'               => $params['warehouse_address'],
            'warehouse_latitude'              => $params['warehouse_latitude'],
            'warehouse_longitude'             => $params['warehouse_longitude'],
            'warehouse_area'                  => $params['warehouse_area'],
            'warehouse_month_rent'            => $params['warehouse_month_rent'],
            'contract_period'                 => $params['contract_period'],
            'contract_period_unit'            => $params['contract_period_unit'],
            'deposit'                         => $params['deposit'],
            'down_payment'                    => $params['down_payment'],
            'cusa_fee'                        => $params['cusa_fee'] ?? 0,
            'maintenance_fee'                 => $params['maintenance_fee'] ?? 0,
            'entrance_fee'                    => $params['entrance_fee'] ?? 0,
            'garbage_fee'                     => $params['garbage_fee'] ?? 0,
            'parking_fee'                     => $params['parking_fee'] ?? 0,
            'other_fee'                       => $params['other_fee'] ?? 0,
            'landlord_type'                   => $params['landlord_type'],
            'landlord_name'                   => $params['landlord_name'],
            'landlord_aptitude'               => $params['landlord_aptitude'],
            'warehouse_evaluate'              => $params['warehouse_evaluate'],
            'remark'                          => $params['remark'] ?? '',
            'last_price_source'               => $params['last_price_source'] ?? 0,
            'last_price_id'                   => $params['last_price_id'] ?? 0,
            'last_price_warehouse_address'    => $params['last_price_warehouse_address'] ?? '',
            'last_price_warehouse_latitude'   => $params['last_price_warehouse_latitude'] ?? '',
            'last_price_warehouse_longitude'  => $params['last_price_warehouse_longitude'] ?? '',
            'last_price_warehouse_area'       => $params['last_price_warehouse_area'] ?? '',
            'last_price_warehouse_month_rent' => $params['last_price_warehouse_month_rent'] ?? null,
            'last_price_contract_period'      => $params['last_price_contract_period'] ?? '',
            'last_price_contract_period_unit' => $params['last_price_contract_period_unit'] ?? 0,
            'last_price_deposit'              => $params['last_price_deposit'] ?? null,
            'last_price_down_payment'         => $params['last_price_down_payment'] ?? null,
            'last_price_cusa_fee'             => $params['last_price_cusa_fee'] ?? null,
            'last_price_maintenance_fee'      => $params['last_price_maintenance_fee'] ?? null,
            'last_price_entrance_fee'         => $params['last_price_entrance_fee'] ?? null,
            'last_price_garbage_fee'          => $params['last_price_garbage_fee'] ?? null,
            'last_price_parking_fee'          => $params['last_price_parking_fee'] ?? null,
            'last_price_other_fee'            => $params['last_price_other_fee'] ?? null,
            'last_price_landlord_type'        => $params['last_price_landlord_type'] ?? 0,
            'last_price_landlord_name'        => $params['last_price_landlord_name'] ?? '',
            'last_price_landlord_aptitude'    => $params['last_price_landlord_aptitude'] ?? 0,
            'last_price_warehouse_evaluate'   => $params['last_price_warehouse_evaluate'] ?? '',
            'last_price_remark'               => $params['last_price_remark'] ?? '',
            'source_type'                     => $source_type,
            'created_id'                      => $user['id'],
            'created_name'                    => $user['name'],
            'updated_id'                      => $user['id'],
            'updated_name'                    => $user['name'],
            'created_at'                      => $now,
            'updated_at'                      => $now,

            'rent_payment_method'              => $rent_payment_method,
            'water_bill_payment_type'          => $params['water_bill_payment_type'] ?? 0,
            'electricity_bill_payment_type'    => $params['electricity_bill_payment_type'] ?? 0,
            'water_billing_method'             => $params['water_billing_method'] ?? 0,
            'water_usage_units'                => $params['water_usage_units'] ?? 0,
            'electricity_billing_method'       => $params['electricity_billing_method'] ?? 0,
            'electricity_usage_units'          => $params['electricity_usage_units'] ?? 0,
            'is_install_fire_extinguishers'    => $params['is_install_fire_extinguishers'] ?? 0,
            'withholding_tax_liability_bearer' => $params['withholding_tax_liability_bearer'] ?? 0,
            'land_tax_liability_bearer'        => $params['land_tax_liability_bearer'] ?? 0,
            'stamp_duty_liability_bearer'      => $params['stamp_duty_liability_bearer'] ?? 0,
            'surrounding_stores_average_rent'  => $params['surrounding_stores_average_rent'] ?? 0,
            'other_options_average_cost'       => $params['other_options_average_cost'] ?? 0,

            'last_price_rent_payment_method'              => $last_price_rent_payment_method,
            'last_price_water_bill_payment_type'          => $params['last_price_water_bill_payment_type'] ?? 0,
            'last_price_electricity_bill_payment_type'    => $params['last_price_electricity_bill_payment_type'] ?? 0,
            'last_price_water_billing_method'             => $params['last_price_water_billing_method'] ?? 0,
            'last_price_water_usage_units'                => $params['last_price_water_usage_units'] ?? null,
            'last_price_electricity_billing_method'       => $params['last_price_electricity_billing_method'] ?? 0,
            'last_price_electricity_usage_units'          => $params['last_price_electricity_usage_units'] ?? null,
            'last_price_is_install_fire_extinguishers'    => $params['last_price_is_install_fire_extinguishers'] ?? 0,
            'last_price_withholding_tax_liability_bearer' => $params['last_price_withholding_tax_liability_bearer'] ?? 0,
            'last_price_land_tax_liability_bearer'        => $params['last_price_land_tax_liability_bearer'] ?? 0,
            'last_price_stamp_duty_liability_bearer'      => $params['last_price_stamp_duty_liability_bearer'] ?? 0,
            'last_price_surrounding_stores_average_rent'  => $params['last_price_surrounding_stores_average_rent'] ?? null,
            'last_price_other_options_average_cost'       => $params['last_price_other_options_average_cost'] ?? null,
        ];

        //仓库管理-仓库信息管理-续签报价
        if ($source_type == WarehouseEnums::THREAD_PRICE_SOURCE_TYPE_WAREHOUSE) {
            WarehouseService::getInstance()->validateCanPrice($params['origin_warehouse_id']);
            $model = $this->addSavePrice($params, $user, $data);
        } elseif ($source_type == WarehouseEnums::THREAD_PRICE_SOURCE_TYPE_RM_RENEWED) {
            // 仓库校验
            WarehouseService::getInstance()->validateCanPrice($params['origin_warehouse_id']);

            // 需求校验
            $requirement_model = RequirementService::getInstance()->getRequirementInfo($params['requirement_id']);
            if ($requirement_model->status != WarehouseEnums::REQUIREMENT_STATUS_PENDING_RENEWED) {
                throw new ValidationException(static::$t->_('warehouse_requirement_status_error', ['status' => static::$t->_(WarehouseEnums::getRequirementStatus()[WarehouseEnums::REQUIREMENT_STATUS_PENDING_RENEWED])]), ErrCode::$VALIDATE_ERROR);
            }

            $model = $this->addSavePrice($params, $user, $data);

            // 将需求状态更新为报价审核中
            $change_status_data = [
                'status'       => WarehouseEnums::REQUIREMENT_STATUS_PRICE_AUDIT,
                'updated_id'   => $user['id'],
                'updated_name' => $user['name'],
                'updated_at'   => $now,
            ];
            if ($requirement_model->i_update($change_status_data) === false) {
                throw new BusinessException('处理仓库线索-待续约-报价提交-将需求状态更新为报价审核中失败: ' . json_encode($change_status_data, JSON_UNESCAPED_UNICODE) . '; 可能的原因是：' . get_data_object_error_msg($requirement_model), ErrCode::$BUSINESS_ERROR);
            }

        } else {
            //线索
            $thread_info = $this->getThreadInfo($params['thread_id']);
            //线索状态：处理仓库线索-待报价-去报价-待报价、处理仓库线索-待签约-重新报价-待签约
            if ($thread_info->status != $params['status']) {
                throw new ValidationException(static::$t->_('warehouse_thread_status_error', ['status' => static::$t->_(WarehouseEnums::$thread_status[$params['status']])]), ErrCode::$VALIDATE_ERROR);
            }

            //需求
            $requirement_info = RequirementService::getInstance()->getRequirementInfo($params['requirement_id']);

            //需求状态：处理仓库线索-待报价-去报价-待报价、处理仓库线索-待签约-重新报价-待签约
            if ($requirement_info->status != $params['status']) {
                throw new ValidationException(static::$t->_('warehouse_requirement_status_error', ['status' => static::$t->_(WarehouseEnums::getRequirementStatus()[$params['status']])]), ErrCode::$VALIDATE_ERROR);
            }
            //一个线索只能有一个报价；驳回后的重新发起以及重新报价-覆盖
            $model = $thread_info->getPrice();
            if ($model) {
                //报价单非待审核才能发起报价或重新报价
                if ($model->status == Enums::WF_STATE_PENDING) {
                    throw new ValidationException(static::$t->_('warehouse_price_recommit_error'), ErrCode::$VALIDATE_ERROR);
                }
                //驳回的重新提交-申请日期不更新
                if ($model->status == Enums::WF_STATE_REJECTED) {
                    $data['apply_date'] = $model->apply_date;
                }
                //创建审批重新申请
                $data['workflow_no'] = $this->editByWorkflow($user, $model->workflow_no, $data);
                $bool  = $model->i_update($data);
                if ($bool === false) {
                    throw new BusinessException('仓库管理-处理仓库线索或仓库信息管理-报价失败: ' . json_encode($data, JSON_UNESCAPED_UNICODE) . '; 可能的原因是：' . get_data_object_error_msg($model), ErrCode::$BUSINESS_ERROR);
                }

                //删除原来的附件
                $db->updateAsDict(
                    (new SysAttachmentModel())->getSource(),
                    ['deleted' => GlobalEnums::IS_DELETED],
                    ['conditions' => "oss_bucket_key = {$model->id} and oss_bucket_type = " . Enums::OSS_BUCKET_TYPE_WAREHOUSE_THREAD_PRICE_FILE]
                );
            } else {
                $model = $this->addSavePrice($params, $user, $data);
            }

            //将线索状态更新为报价审核中
            $change_status_data = [
                'status'       => WarehouseEnums::REQUIREMENT_STATUS_PRICE_AUDIT,//报价审核中
                'updated_id'   => $user['id'],
                'updated_name' => $user['name'],
                'updated_at'   => $now,
            ];
            $bool = $thread_info->save($change_status_data);
            if ($bool === false) {
                throw new BusinessException('仓库管理-处理仓库线索或仓库信息管理-报价-将线索状态更新为报价审核中失败: ' . json_encode($change_status_data, JSON_UNESCAPED_UNICODE) . '; 可能的原因是：' . get_data_object_error_msg($thread_info), ErrCode::$BUSINESS_ERROR);
            }

            //将需求状态更新为报价审核中
            $bool = $requirement_info->save($change_status_data);
            if ($bool === false) {
                throw new BusinessException('仓库管理-处理仓库线索或仓库信息管理-报价-将需求状态更新为报价审核中失败: ' . json_encode($change_status_data, JSON_UNESCAPED_UNICODE) . '; 可能的原因是：' . get_data_object_error_msg($requirement_info), ErrCode::$BUSINESS_ERROR);
            }
        }

        $attach_arr = [];
        //添加附件
        if (!empty($params['attachments'])) {
            foreach ($params['attachments'] as $attachment) {
                $attach_arr[] = [
                    'oss_bucket_type' => Enums::OSS_BUCKET_TYPE_WAREHOUSE_THREAD_PRICE_FILE,
                    'oss_bucket_key'  => $model->id,
                    'sub_type'        => Enums::OSS_SUB_TYPE_WAREHOUSE_THREAD_PRICE,
                    'bucket_name'     => $attachment['bucket_name'],
                    'object_key'      => $attachment['object_key'],
                    'file_name'       => $attachment['file_name'],
                ];
            }
        }

        //上次报价-附件
        if (!empty($params['last_price_attachments'])) {
            foreach ($params['last_price_attachments'] as $attachment) {
                $attach_arr[] = [
                    'oss_bucket_type' => Enums::OSS_BUCKET_TYPE_WAREHOUSE_THREAD_PRICE_FILE,
                    'oss_bucket_key'  => $model->id,
                    'sub_type'        => Enums::OSS_SUB_TYPE_WAREHOUSE_THREAD_LAST_PRICE,
                    'bucket_name'     => $attachment['bucket_name'],
                    'object_key'      => $attachment['object_key'],
                    'file_name'       => $attachment['file_name'],
                ];
            }
        }

        if (!empty($attach_arr)) {
            $attachment_model = new SysAttachmentModel();
            if (!$attachment_model->batch_insert($attach_arr)) {
                throw new BusinessException('仓库管理-处理仓库线索或仓库信息管理-报价添加附件失败: ' . json_encode($attach_arr,
                        JSON_UNESCAPED_UNICODE) . '; 可能的原因是: ' . get_data_object_error_msg($attachment_model),
                    ErrCode::$BUSINESS_ERROR);
            }
        }
    }

    /**
     * 不允许重复，新增保存时如果与现有数据中的编号重复，则自动取库中当前日期最大的序号+1，不需要拦截提交
     * @param string $no 报价编号
     * @return string
     * @throws BusinessException
     * @throws ValidationException
     */
    private function getPriceNo(string $no)
    {
        // 验证单号是否已创建 或 占用
        $exist_no = WarehouseThreadPriceRecordModel::findFirst([
            'conditions' => 'no = :no:',
            'bind'       => ['no' => $no],
            'columns'    => ['id'],
        ]);
        $make_count = 0;
        if (!empty($exist_no)) {
            $no = static::genSerialNo(WarehouseEnums::THREAD_PRICE_NO_PREFIX, RedisKey::WAREHOUSE_THREAD_PRICE_ADD_COUNTER);
            $make_count++;
            $price_nos[] = $no;
            if ($make_count >= 3) {
                throw new BusinessException("仓库线索-报价编号生成已重试 {$make_count} 次, 请检查, 尝试生成的: " . json_encode($price_nos), ErrCode::$BUSINESS_ERROR);
            }
            return $this->getPriceNo($no);
        }
        return $no;
    }

    /**
     * 添加报价-保存
     * @param array $params 参数组
     * @param array $user 当前登陆者信息组
     * @param array $data 入库信息组
     * @return WarehouseThreadPriceRecordModel
     * @throws BusinessException
     * @throws ValidationException
     */
    private function addSavePrice(array $params, array $user, array $data)
    {
        $data['no'] = $this->getPriceNo($params['no']);

        //创建审批
        $data['workflow_no'] = $this->addByWorkflow($user, $data);

        //获取默认币种
        $default_currency = EnumsService::getInstance()->getSysCurrencyInfo();
        $data['currency'] = $default_currency['code'];

        $model = new WarehouseThreadPriceRecordModel();
        $bool = $model->i_create($data);
        if ($bool === false) {
            throw new BusinessException('仓库管理-仓库管理-仓库信息管理-续签报价添加失败: ' . json_encode($data, JSON_UNESCAPED_UNICODE) . '; 可能的原因是：' . get_data_object_error_msg($model), ErrCode::$BUSINESS_ERROR);
        }

        return $model;
    }

    /**
     * 创建审批流
     * @param array $user 当前登陆者信息组
     * @param array $params 参数组
     * @return mixed
     * @throws ValidationException
     */
    private function addByWorkflow(array $user, array $params)
    {
        //创建审批
        $by_workflow   = new ByWorkflowService();
        $by_add_result = $by_workflow->add([
            'submitter_id' => $user['id'],
            'summary_data' => [],
            'biz_type'     => ByWorkflowEnums::BY_BIZ_TYPE_WAREHOUSE_THREAD_PRICE,
            'audit_params' => [],
            'form_params'  => [
                'sys_store_id' => $params['store_id'],
            ]
        ]);
        return $by_add_result['serial_no'];
    }

    /**
     * 创建审批流重新申请
     * @param array $user 当前登陆者信息组
     * @param string $workflow_no by审批流-流水号
     * @param array $params 参数组
     * @return mixed
     * @throws ValidationException
     */
    private function editByWorkflow(array $user, string $workflow_no, array $params)
    {
        //创建审批
        $by_workflow   = new ByWorkflowService();
        $by_edit_result = $by_workflow->edit([
            'serial_no'    => $workflow_no,
            'submitter_id' => $user['id'],
            'summary_data' => [],
            'biz_type'     => ByWorkflowEnums::BY_BIZ_TYPE_WAREHOUSE_THREAD_PRICE,
            'audit_params' => [],
            'form_params'  => [
                'sys_store_id' => $params['store_id'],
            ]
        ]);
        return $by_edit_result['serial_no'];
    }


    /**
     * 处理仓库线索-待报价-谈判失败
     * @param array $params 参数组
     * @param array $user 当前登陆人信息组
     * @return array
     */
    public function priceTalkFail(array $params, array $user)
    {
        $code    = ErrCode::$SUCCESS;
        $message = $real_message = '';

        $db = $this->getDI()->get('db_oa');
        $db->begin();
        try {
            //检查线索
            $thread_info = $this->getThreadInfo($params['id']);
            if ($thread_info->status != WarehouseEnums::THREAD_STATUS_PRICE) {
                throw new ValidationException(static::$t->_('warehouse_thread_status_error', ['status' => static::$t->_(WarehouseEnums::$thread_status[WarehouseEnums::THREAD_STATUS_PRICE])]), ErrCode::$VALIDATE_ERROR);
            }

            $requirement_info = RequirementService::getInstance()->getRequirementInfo($thread_info->requirement_id);

            $now = date('Y-m-d H:i:s');
            //1. 将线索状态更新为已作废，作废原因固定为“Negotiations with landlord failed”
            $thread_info->status        = WarehouseEnums::THREAD_STATUS_CANCEL;//已作废
            $thread_info->cancel_at     = $now;
            $thread_info->cancel_reason = 'Negotiations with landlord failed';
            $thread_info->updated_id    = $user['id'];
            $thread_info->updated_name  = $user['name'];
            $thread_info->updated_at    = $now;
            $bool = $thread_info->save();
            if ($bool === false) {
                throw new BusinessException('保存线索谈判失败信息失败，参数为：' . json_encode($params, JSON_UNESCAPED_UNICODE) . '; 可能的原因是：' . get_data_object_error_msg($thread_info), ErrCode::$BUSINESS_ERROR);
            }

            //2. 将关联的需求状态更新为待寻找，删除需求上确认的线索
            $requirement_info->status       = WarehouseEnums::REQUIREMENT_STATUS_SEARCH;//待寻找
            $requirement_info->thread_id    = 0;
            $requirement_info->thread_no    = '';
            $requirement_info->updated_id   = $user['id'];
            $requirement_info->updated_name = $user['name'];
            $requirement_info->updated_at   = $now;
            $bool = $requirement_info->save();
            if ($bool === false) {
                throw new BusinessException('变更需求状态为代寻找失败，参数为：' . json_encode($params, JSON_UNESCAPED_UNICODE) . '; 可能的原因是：' . get_data_object_error_msg($requirement_info), ErrCode::$BUSINESS_ERROR);
            }

            $db->commit();
        } catch (ValidationException $e) {
            $code    = $e->getCode();
            $message = $e->getMessage();
        } catch (BusinessException $e) {
            $code         = $e->getCode();
            $message      = static::$t->_('retry_later');
            $real_message = $e->getMessage() . $e->getTraceAsString();
        } catch (Exception $e) {
            $code         = ErrCode::$SYSTEM_ERROR;
            $message      = static::$t->_('retry_later');
            $real_message = $e->getMessage() . $e->getTraceAsString();
        }

        if ($message) {
            $db->rollback();
        }
        if ($real_message) {
            $this->logger->error('仓库管理-处理仓库线索-待报价-谈判失败失败: ' . $real_message);
        }

        return [
            'code'    => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data'    => $data ?? [],
        ];
    }

    /**
     * 处理仓库线索-待签约-新建仓库
     * @param array $params 参数组
     * @param array $user 当前登陆人信息组
     * @return array
     */
    public function signAddWarehouse(array $params, array $user)
    {
        $code    = ErrCode::$SUCCESS;
        $message = $real_message = '';

        $db = $this->getDI()->get('db_oa');
        $db->begin();
        try {
            //检查线索
            $thread_info = $this->getThreadInfo($params['id']);
            if ($thread_info->status != WarehouseEnums::THREAD_STATUS_SIGN) {
                throw new ValidationException(static::$t->_('warehouse_thread_status_error', ['status' => static::$t->_(WarehouseEnums::$thread_status[WarehouseEnums::THREAD_STATUS_SIGN])]), ErrCode::$VALIDATE_ERROR);
            }

            //新建仓库
            $data = WarehouseService::getInstance()->commonAdd($params, $user);
            if ($data['code'] == ErrCode::$SUCCESS) {
                //仓库创建成功才能改线索信息，其它情况返回前端同仓库信息管理-新增
                if (!empty($data['data']['save_result'])) {
                    $curr_warehouse_info            = $data['data']['curr_warehouse_info'];
                    $thread_info->warehouse_main_id = $curr_warehouse_info['id'];
                    $thread_info->warehouse_id      = $curr_warehouse_info['warehouse_id'];
                    $thread_info->status            = WarehouseEnums::THREAD_STATUS_SIGN_ING;
                    $thread_info->updated_id        = $user['id'];
                    $thread_info->updated_name      = $user['name'];
                    $thread_info->updated_at        = date('Y-m-d H:i:s');
                    $bool                           = $thread_info->save();
                    if ($bool === false) {
                        throw new BusinessException('线索变更为签约流程中、记录仓库信息失败：' . json_encode($params, JSON_UNESCAPED_UNICODE) . '; 可能的原因是：' . get_data_object_error_msg($thread_info), ErrCode::$BUSINESS_ERROR);
                    }
                }
                $data = $data['data'];
            } elseif ($data['code'] == ErrCode::$VALIDATE_ERROR) {
                throw new ValidationException($data['message']);
            } elseif ($data['code'] == ErrCode::$BUSINESS_ERROR) {
                throw new BusinessException($data['message']);
            } else {
                throw new Exception($data['message']);
            }

            $db->commit();
        } catch (ValidationException $e) {
            $code    = $e->getCode();
            $message = $e->getMessage();
        } catch (BusinessException $e) {
            $code         = $e->getCode();
            $message      = static::$t->_('retry_later');
            $real_message = $e->getMessage() . $e->getTraceAsString();
        } catch (Exception $e) {
            $code         = ErrCode::$SYSTEM_ERROR;
            $message      = static::$t->_('retry_later');
            $real_message = $e->getMessage() . $e->getTraceAsString();
        }

        if ($message) {
            $db->rollback();
        }
        if ($real_message) {
            $this->logger->error('仓库管理-处理仓库线索-待签约-新建仓库失败: ' . $real_message);
        }

        return [
            'code'    => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data'    => $data ?? [],
        ];
    }

    /**
     * 获取某个线索某个责任下待验证的验证数据
     * @param int $thread_id 线索ID
     * @param array $user 当前登陆人信息组
     * @return mixed
     * @throws ValidationException
     */
    private function getVerifyInfo(int $thread_id, array $user)
    {
        $verify_info = WarehouseThreadVerifyRecordModel::findFirst([
            'conditions' => 'thread_id = :thread_id: and person_liable_id = :person_liable_id: and status = :status:',
            'bind' => ['thread_id' => $thread_id, 'person_liable_id' => $user['id'], 'status' => WarehouseEnums::THREAD_VERIFY_STATUS_VERIFY]
        ]);
        if (empty($verify_info)) {
            throw new ValidationException(static::$t->_('warehouse_thread_verify_not_exists'), ErrCode::$VALIDATE_ERROR);
        }
        return $verify_info;
    }

    /**
     * 处理仓库线索-待验证-转交
     * @param array $params 参数组
     * @param array $user 当前登陆人信息组
     * @return array
     */
    public function verifyTransfer(array $params, array $user)
    {
        $code    = ErrCode::$SUCCESS;
        $message = $real_message = '';

        $db = $this->getDI()->get('db_oa');
        $db->begin();
        try {
            //检查线索
            $thread_info = $this->getThreadInfo($params['id']);

            //获取验证记录
            $verify_info = $this->getVerifyInfo($thread_info->id, $user);

            //1. 如果满足则执行转交，将当前线索验证的责任人更新为接收人
            $verify_info->person_liable_id   = $params['person_liable_id'];
            $verify_info->person_liable_name = $params['person_liable_name'];
            $verify_info->updated_at         = date('Y-m-d H:i:s');
            $bool                            = $verify_info->save();
            if ($bool === false) {
                throw new BusinessException('线索-待验证数据-转交失败：' . json_encode($params, JSON_UNESCAPED_UNICODE) . '; 可能的原因是：' . get_data_object_error_msg($verify_info), ErrCode::$BUSINESS_ERROR);
            }

            //2. 如果接收人无仓库验证的菜单权限则添加菜单权限
            RequirementService::getInstance()->addVerifyPermission($params['person_liable_id'], $user);
            $db->commit();
        } catch (ValidationException $e) {
            $code    = $e->getCode();
            $message = $e->getMessage();
        } catch (BusinessException $e) {
            $code         = $e->getCode();
            $message      = static::$t->_('retry_later');
            $real_message = $e->getMessage() . $e->getTraceAsString();
        } catch (Exception $e) {
            $code         = ErrCode::$SYSTEM_ERROR;
            $message      = static::$t->_('retry_later');
            $real_message = $e->getMessage() . $e->getTraceAsString();
        }

        if ($message) {
            $db->rollback();
        }
        if ($real_message) {
            $this->logger->error('仓库管理-处理仓库线索-待验证-转交失败: ' . $real_message);
        }

        return [
            'code'    => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data'    => $data ?? [],
        ];
    }

    /**
     * 处理仓库线索-待验证-录入验证结果-查看
     * @param int $id 线索ID
     * @return array
     */
    public function verifyView(int $id)
    {
        $code    = ErrCode::$SUCCESS;
        $message = 'success';
        $data    = [];

        try {
            //线索信息
            $thread_info = $this->getThreadInfo($id);
            $data = $this->getThreadDetail($thread_info, false);

            //仓库需求信息
            $requirement_info = $this->getRequirementDetail($thread_info);
            $data['requirement_info'] = $requirement_info['requirement_info'] ?? (object)[];
        } catch (ValidationException $e) {
            $code    = $e->getCode();
            $message = $e->getMessage();
        } catch (Exception $e) {
            $code    = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $this->logger->error('处理仓库线索-待验证-录入验证结果-查看失败:' . $e->getMessage());
        }

        return [
            'code'    => $code,
            'message' => $message,
            'data'    => $data,
        ];
    }

    /**
     * 获取完成录入参数校验规则
     */
    public static function checkValidateVerifyDone($params)
    {
        $validate = self::$validate_verify_done;

        $country_code = get_country_code();
        if ($country_code == GlobalEnums::TH_COUNTRY_CODE) {
            $validate['is_install_fire_extinguishers'] = 'Required|IntIn:1,2|>>>:' . static::$t->_('params_error', ['param' => 'is_install_fire_extinguishers']);
        } else {
            $validate['electricity'] = 'Required|IntIn:' . WarehouseEnums::THREAD_VERIFY_STATE_VALIDATE;
            $validate['water']       = 'Required|IntIn:' . WarehouseEnums::THREAD_VERIFY_STATE_VALIDATE;
            $validate['lighting']    = 'Required|IntIn:' . WarehouseEnums::THREAD_VERIFY_STATE_VALIDATE;
        }

        $validate['verify_date'] = 'Required|Date|DateTo:' . date('Y-m-d');
        Validation::validate($params, $validate);
    }

    /**
     * 处理仓库线索-待验证-录入验证结果-完成录入
     * @param array $params 参数组
     * @param array $user 当前登陆人信息组
     * @return array
     */
    public function verifyDone(array $params, array $user)
    {
        $code    = ErrCode::$SUCCESS;
        $message = $real_message = '';

        $db = $this->getDI()->get('db_oa');
        $db->begin();
        try {
            //获取验证记录
            $verify_info = $this->getVerifyInfo($params['id'], $user);


            //1. 则提交验仓结果，将验仓状态更新为已提交
            $verify_info->status            = WarehouseEnums::THREAD_VERIFY_STATUS_SUBMIT;//已提交
            $verify_info->water             = $params['water'] ?? 0;
            $verify_info->electricity       = $params['electricity'] ?? 0;
            $verify_info->lighting          = $params['lighting'] ?? 0;
            $verify_info->is_install_fire_extinguishers = $params['is_install_fire_extinguishers'] ?? 0;
            $verify_info->verify_staff_id   = $params['verify_staff_id'];
            $verify_info->verify_staff_name = $params['verify_staff_name'];
            $verify_info->verify_date       = $params['verify_date'];
            $verify_info->remark            = $params['remark'] ?? '';
            $verify_info->updated_at        = date('Y-m-d H:i:s');
            $bool                           = $verify_info->save();
            if ($bool === false) {
                throw new BusinessException('线索-待验证数据-提交验仓结果失败：' . json_encode($params, JSON_UNESCAPED_UNICODE) . '; 可能的原因是：' . get_data_object_error_msg($verify_info), ErrCode::$BUSINESS_ERROR);
            }

            $attach_arr = [];
            //2.仓库大门照片
            foreach ($params['door_attachments'] as $attachment) {
                $attach_arr[] = [
                    'oss_bucket_type' => Enums::OSS_BUCKET_TYPE_WAREHOUSE_THREAD_VERIFY_DOOR_FILE,
                    'oss_bucket_key'  => $verify_info->id,
                    'sub_type'        => 0,
                    'bucket_name'     => $attachment['bucket_name'],
                    'object_key'      => $attachment['object_key'],
                    'file_name'       => $attachment['file_name'],
                ];
            }

            //3. 仓库门口道路照片
            foreach ($params['road_attachments'] as $attachment) {
                $attach_arr[] = [
                    'oss_bucket_type' => Enums::OSS_BUCKET_TYPE_WAREHOUSE_THREAD_VERIFY_ROAD_FILE,
                    'oss_bucket_key'  => $verify_info->id,
                    'sub_type'        => 0,
                    'bucket_name'     => $attachment['bucket_name'],
                    'object_key'      => $attachment['object_key'],
                    'file_name'       => $attachment['file_name'],
                ];
            }

            $attachment_model = new SysAttachmentModel();
            if (!$attachment_model->batch_insert($attach_arr)) {
                throw new BusinessException('线索-待验证数据-提交验仓结果附件失败: ' . json_encode($attach_arr, JSON_UNESCAPED_UNICODE), ErrCode::$BUSINESS_ERROR);
            }
            $db->commit();
        } catch (ValidationException $e) {
            $code    = $e->getCode();
            $message = $e->getMessage();
        } catch (BusinessException $e) {
            $code         = $e->getCode();
            $message      = static::$t->_('retry_later');
            $real_message = $e->getMessage() . $e->getTraceAsString();
        } catch (Exception $e) {
            $code         = ErrCode::$SYSTEM_ERROR;
            $message      = static::$t->_('retry_later');
            $real_message = $e->getMessage() . $e->getTraceAsString();
        }

        if ($message) {
            $db->rollback();
        }
        if ($real_message) {
            $this->logger->error('仓库管理-处理仓库线索-待验证-录入验证结果-完成录入失败: ' . $real_message);
        }

        return [
            'code'    => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data'    => $data ?? [],
        ];
    }

    /**
     * 网点租房合同审批通过会调取
     *  1. 当网点租房合同审批通过时，如果关联了仓库并且仓库关联了线索，并且线索状态为签约流程中时将线索状态更新为待付款，将线索关联的仓库需求状态更新为待付款
     *  2. 如果线索关联的仓库验证状态为待验证，则将仓库验证状态更新为取消验证
     * @param string $warehouse_id 仓库ID
     * @param array $user 当前登陆者信息组
     * @return bool
     */
    public function syncRelatedTheadApprove(string $warehouse_id, array $user)
    {
        $now = date('Y-m-d H:i:s');
        //仓库关联的线索信息
        $thread_info = WarehouseThreadModel::findFirst([
            'conditions' => 'warehouse_id = :warehouse_id:',
            'bind'       => ['warehouse_id' => $warehouse_id],
        ]);

        //无关联线索
        if (empty($thread_info)) {
            $this->logger->info('网点租房合同审批通过时，仓库：' . $warehouse_id . '未找到关联的线索信息，无需处理线索');
            return true;
        }

        $log = '网点租房合同审批通过时，仓库：' . $warehouse_id . ' 对应的线索 【 ' . json_encode($thread_info, JSON_UNESCAPED_UNICODE). ' 】，';
        //线索状态非签约流程中
        if ($thread_info->status != WarehouseEnums::THREAD_STATUS_SIGN_ING) {
            $this->logger->info($log . '非签约流程中，无需处理线索');
            return true;
        }

        //将线索状态更新为待付款
        $thread_info->status       = WarehouseEnums::THREAD_STATUS_PAY;
        $thread_info->updated_id   = $user['id'];
        $thread_info->updated_name = $user['name'];
        $thread_info->updated_at   = $now;
        $bool = $thread_info->save();
        if ($bool === false) {
            $this->logger->error($log . '更新为待付款失败，请联系产品修复数据');
        }

        //线索关联的的需求信息
        $requirement_info = $thread_info->getRequirement();
        if (empty($requirement_info)) {
            $this->logger->info($log . '未找到关联的需求信息，无需处理需求');
        } else {
            //需求状态非待签约
            $log_requirement = $log . '关联的需求【' . json_encode($requirement_info, JSON_UNESCAPED_UNICODE) . '】，';

            //仓库需求状态更新为待付款
            $requirement_info->status       = WarehouseEnums::REQUIREMENT_STATUS_PAY;
            $requirement_info->updated_id   = $user['id'];
            $requirement_info->updated_name = $user['name'];
            $requirement_info->updated_at   = $now;
            $bool = $requirement_info->save();
            if ($bool === false) {
                $this->logger->error($log_requirement . '更新为待付款失败，请联系产品修复数据');
            }
        }

        //线索关联的的验证信息
        $verify_info = $thread_info->getVerify();
        if (empty($verify_info)) {
            $this->logger->info($log . '未找到关联的待验证的仓库验证信息，无需处理需求');
            return true;
        }

        //如果线索关联的仓库验证状态为待验证，则将仓库验证状态更新为取消验证
        $verify_info->status     = WarehouseEnums::THREAD_VERIFY_STATUS_CANCEL;
        $verify_info->updated_at = $now;
        $bool = $verify_info->save();
        if ($bool === false) {
            $this->logger->error($log . '关联的待验证验证数据【'.json_encode($verify_info, JSON_UNESCAPED_UNICODE).'】，更新为取消验证失败，请联系产品修复数据');
        }

        return true;
    }

    /**
     * 网点租房合同付款已支付时
     *  如果付款明细中关联了租房合同，并且租房合同关联了仓库并且仓库关联了线索，并且线索状态为待付款时将线索状态更新为待入驻，将线索关联的仓库需求状态更新为待入驻
     * @param array $contract_ids 关联的合同组
     * @param array $user 当前登陆者信息组
     * @return bool
     */
    public function syncRelatedTheadPay(array $contract_ids, array $user)
    {
        $log = '网点租房合同付款已支付时，';
        if (empty($contract_ids)) {
            $this->logger->info($log . '付款明细行未关联租房合同，无需处理');
            return true;
        }

        $this->logger->info($log . '付款明细中关联的租房合同组是：【' . json_encode($contract_ids, JSON_UNESCAPED_UNICODE) . '】');
        //获取网点合同
        $contract_renting_list = ContractStoreRentingModel::find([
            'columns'    => 'contract_id, warehouse_id',
            'conditions' => 'contract_id in ({contract_ids:array})',
            'bind'       => ['contract_ids' => $contract_ids],
        ])->toArray();
        $this->logger->info($log . '付款明细中关联的租房合同实际查询到的是：【' . json_encode(array_column($contract_renting_list, 'contract_id'), JSON_UNESCAPED_UNICODE) . '】');

        $now = date('Y-m-d H:i:s');
        foreach ($contract_renting_list as $item) {
            //未关联仓库跳过
            if (empty($item['warehouse_id'])) {
                $this->logger->info($log . '合同：' . $item['contract_id'] . '，未关联仓库，跳过');
                continue;
            }

            //仓库关联的线索信息
            $thread_info = WarehouseThreadModel::findFirst([
                'conditions' => 'warehouse_id = :warehouse_id:',
                'bind'       => ['warehouse_id' => $item['warehouse_id']],
            ]);

            //无关联线索
            if (empty($thread_info)) {
                $this->logger->info($log . '合同：' . $item['contract_id'] . '，关联的仓库未关联线索，跳过');
                continue;
            }

            //线索状态非待付款
            if ($thread_info->status != WarehouseEnums::THREAD_STATUS_PAY) {
                $this->logger->info($log . '合同：' . $item['contract_id'] . '，关联的仓库所关联的线索非待付款状态，跳过');
                continue;
            }

            //将线索状态更新为待入驻
            $thread_log = $log . '合同：' . $item['contract_id'] . '，关联的仓库所关联的线索 【 ' . json_encode($thread_info, JSON_UNESCAPED_UNICODE) . ' 】，';
            $thread_info->status       = WarehouseEnums::THREAD_STATUS_SETTLE;
            $thread_info->updated_id   = $user['id'];
            $thread_info->updated_name = $user['name'];
            $thread_info->updated_at   = $now;
            $bool = $thread_info->save();
            if ($bool === false) {
                $this->logger->error($thread_log . '更新为待入驻失败，请联系产品修复数据');
            }

            //线索关联的的需求信息
            $requirement_info = $thread_info->getRequirement();
            if (empty($requirement_info)) {
                $this->logger->info($thread_log . '未找到关联的需求信息，无需处理需求');
            } else {
                //需求状态非待付款
                $log_requirement = $thread_log . '关联的需求【' . json_encode($requirement_info, JSON_UNESCAPED_UNICODE) . '】，';

                //仓库需求状态更新为待入驻
                $requirement_info->status       = WarehouseEnums::REQUIREMENT_STATUS_SETTLE;
                $requirement_info->updated_id   = $user['id'];
                $requirement_info->updated_name = $user['name'];
                $requirement_info->updated_at   = $now;
                $bool = $requirement_info->save();
                if ($bool === false) {
                    $this->logger->error($log_requirement . '更新为待入驻失败，请联系产品修复数据');
                }
            }
        }
        return true;
    }

    /**
     * 网点租房合同审批通过回调: 同步仓库ID关联的线索/需求相关状态(version th)
     * @param string $warehouse_id 仓库ID
     * @param array $user 当前登录用户信息
     * @return bool
     */
    public function syncRelatedTheadApproveTH(string $warehouse_id, array $user)
    {
        $now = date('Y-m-d H:i:s');

        $is_continue_processing = false;

        // 仓库关联的线索信息
        $thread_info = WarehouseThreadModel::findFirst([
            'conditions' => 'warehouse_id = :warehouse_id:',
            'bind'       => ['warehouse_id' => $warehouse_id],
        ]);

        $this->logger->info([
            'syncRelatedTheadApproveTH' => [
                'warehouse_id'        => $warehouse_id,
                'related_thread_info' => !empty($thread_info) ? $thread_info->toArray() : [],
                'user'                => $user,
            ],
        ]);

        // 有线索
        if (!empty($thread_info)) {
            // 线索状态: 签约流程中
            if ($thread_info->status == WarehouseEnums::THREAD_STATUS_SIGN_ING) {
                $this->logger->info(['syncRelatedTheadApproveTH' => "线索编号 {$thread_info->no} 线索状态 {$thread_info->status} -> 签约流程中",]);

                //将线索状态更新为待付款
                $thread_update = [
                    'status'       => WarehouseEnums::THREAD_STATUS_PAY,
                    'updated_id'   => $user['id'],
                    'updated_name' => $user['name'],
                    'updated_at'   => $now,
                ];
                if ($thread_info->update($thread_update) === false) {
                    $this->logger->warning('仓库线索 ' . $thread_info->no . ' 更新为待付款失败，请联系产品修复数据');
                } else {
                    $this->logger->info('仓库线索 ' . $thread_info->no . ' 更新为待付款 - 成功');
                }

                // 线索关联的的需求信息
                $requirement_info = $thread_info->getRequirement();

                $this->logger->info([
                    'syncRelatedTheadApproveTH' => [
                        'thread_no'                => $thread_info->no,
                        'related_requirement_info' => !empty($requirement_info) ? $requirement_info->toArray() : [],
                    ],
                ]);

                // 仓库需求状态更新为待付款
                if (!empty($requirement_info)) {
                    $requirement_update = [
                        'status'       => WarehouseEnums::REQUIREMENT_STATUS_PAY,
                        'updated_id'   => $user['id'],
                        'updated_name' => $user['name'],
                        'updated_at'   => $now,
                    ];
                    if ($requirement_info->update($requirement_update) === false) {
                        $this->logger->warning('仓库需求 ' . $requirement_info->no . ' 更新为待付款失败，请联系产品修复数据');
                    } else {
                        $this->logger->info('仓库需求 ' . $requirement_info->no . ' 更新为 待付款 - 成功');
                    }
                }

                // 线索关联的待验证信息
                $pending_verify_info = $thread_info->getVerify();

                $this->logger->info([
                    'syncRelatedTheadApproveTH' => [
                        'thread_no'                   => $thread_info->no,
                        'related_pending_verify_info' => !empty($pending_verify_info) ? $pending_verify_info->toArray() : [],
                    ],
                ]);

                // 将仓库验证状态更新为取消验证
                if (!empty($pending_verify_info)) {
                    $verify_update = [
                        'status'     => WarehouseEnums::THREAD_VERIFY_STATUS_CANCEL,
                        'updated_at' => $now,
                    ];
                    if ($pending_verify_info->update($verify_update) === false) {
                        $this->logger->warning("关联的待验证数据 id={$pending_verify_info->id}, 更新为取消验证失败，请联系产品修复数据");
                    } else {
                        $this->logger->info("关联的待验证数据 id={$pending_verify_info->id}, 更新为 取消验证 - 成功");
                    }
                }
            } else {
                // 线索状态: 非签约流程中
                $this->logger->info(['syncRelatedTheadApproveTH' => "线索编号 {$thread_info->no} 线索状态 {$thread_info->status} -> 非签约流程中",]);

                $is_continue_processing = true;
                $is_null_thread_id      = true;
            }
        } else {
            // 无线索
            $this->logger->info(['syncRelatedTheadApproveTH' => '无关联线索',]);

            $is_continue_processing = true;
            $is_null_thread_id      = false;
        }

        // 继续处理最新审批通过的报价单关联的仓库需求
        if ($is_continue_processing) {
            $requirement_info = $this->getPendingSignRequirementInfo($warehouse_id, WarehouseEnums::REQUIREMENT_STATUS_SIGN, $is_null_thread_id);
            $this->logger->info([
                'syncRelatedTheadApproveTH' => [
                    'warehouse_id'             => $warehouse_id,
                    'is_null_thread_id'        => $is_null_thread_id,
                    'related_requirement_info' => $requirement_info,
                ],
            ]);

            if (!empty($requirement_info['requirement_id'])) {
                // 获取关联需求model
                $requirement_model  = WarehouseRequirementModel::findFirst($requirement_info['requirement_id']);
                $requirement_update = [
                    'status'       => WarehouseEnums::REQUIREMENT_STATUS_PAY,
                    'updated_id'   => $user['id'],
                    'updated_name' => $user['name'],
                    'updated_at'   => $now,
                ];
                if ($requirement_model->update($requirement_update) === false) {
                    $this->logger->warning('仓库需求 ' . $requirement_model->no . ' 更新为待付款失败，请联系产品修复数据');
                } else {
                    $this->logger->info('仓库需求 ' . $requirement_model->no . ' 更新为 待付款 - 成功');
                }
            }
        }

        return true;
    }

    /**
     * 基于报价单 根据仓库ID 获取指定条件的 仓库需求:
     * 报价单: 最新审批通过的 且 报价单中线索为空 且 报价单关联的仓库需求状态是待签约
     * 仓库需求: 待签约
     * @param $warehouse_id
     * @param $requirement_status
     * @param bool $is_null_thread_id
     * @return array
     */
    protected function getPendingSignRequirementInfo($warehouse_id, $requirement_status, bool $is_null_thread_id)
    {
        if (empty($warehouse_id)) {
            return [];
        }

        $builder = $this->modelsManager->createBuilder();
        $builder->from(['price' => WarehouseThreadPriceRecordModel::class]);
        $builder->leftJoin(WarehouseRequirementModel::class, 'requirement.id = price.requirement_id', 'requirement');
        $builder->where('price.origin_warehouse_id = :origin_warehouse_id: AND price.status = :status:', [
            'origin_warehouse_id' => $warehouse_id,
            'status'              => Enums::WF_STATE_APPROVED,
        ]);

        if ($is_null_thread_id) {
            $builder->andWhere('price.thread_id = :null_thread_id:', ['null_thread_id' => 0,]);
        }

        $builder->andWhere('requirement.status = :requirement_status:', ['requirement_status' => $requirement_status,]);

        $columns = [
            'requirement.id AS requirement_id',
        ];

        $builder->columns($columns);
        $builder->orderby('price.approval_at DESC');
        $latest_requirement_model = $builder->getQuery()->execute()->getFirst();
        return !empty($latest_requirement_model) ? $latest_requirement_model->toArray() : [];
    }

    /**
     * 网点租房合同付款已支付时 (version th)
     * @param array $contract_ids 关联的合同列表
     * @param array $user 当前登录用户信息
     * @return bool
     */
    public function syncRelatedTheadPayTH(array $contract_ids, array $user)
    {
        $this->logger->info([
            'syncRelatedTheadPayTH' => [
                'contract_ids' => $contract_ids,
                'user'         => $user,
            ],
        ]);
        if (empty($contract_ids)) {
            return true;
        }

        // 获取网点合同
        $contract_renting_list = ContractStoreRentingModel::find([
            'conditions' => 'contract_id in ({contract_ids:array})',
            'bind'       => ['contract_ids' => $contract_ids],
            'columns'    => 'contract_id, warehouse_id',
        ])->toArray();

        $this->logger->info(['syncRelatedTheadPayTH' => ['related_contract_renting_list' => $contract_renting_list,],]);

        $now = date('Y-m-d H:i:s');
        foreach ($contract_renting_list as $item) {
            // 未关联仓库跳过
            if (empty($item['warehouse_id'])) {
                $this->logger->info('合同 ' . $item['contract_id'] . ' 未关联仓库，跳过');
                continue;
            }

            $is_continue_processing = false;

            // 仓库关联的线索信息
            $thread_info = WarehouseThreadModel::findFirst([
                'conditions' => 'warehouse_id = :warehouse_id:',
                'bind'       => ['warehouse_id' => $item['warehouse_id']],
            ]);

            $this->logger->info(['related_thread_info' => !empty($thread_info) ? $thread_info->toArray() : []]);

            // 有关联线索
            if (!empty($thread_info)) {
                $this->logger->info("线索编号 {$thread_info->no}, 状态 {$thread_info->status}");

                // 线索状态待付款
                if ($thread_info->status == WarehouseEnums::THREAD_STATUS_PAY) {
                    $thread_update = [
                        'status'       => WarehouseEnums::THREAD_STATUS_SETTLE,
                        'updated_id'   => $user['id'],
                        'updated_name' => $user['name'],
                        'updated_at'   => $now,
                    ];
                    if ($thread_info->update($thread_update) === false) {
                        $this->logger->warning("线索编号 {$thread_info->no}, 更新为待入驻失败，请联系产品修复数据");
                    } else {
                        $this->logger->info("线索编号 {$thread_info->no}, 更新为 待入驻 - 成功");
                    }

                    // 线索关联的的需求信息
                    $requirement_info = $thread_info->getRequirement();

                    $this->logger->info(['related_requirement_info' => !empty($requirement_info) ? $requirement_info->toArray() : []]);

                    // 仓库需求状态更新为待入驻
                    if (!empty($requirement_info)) {
                        $requirement_update = [
                            'status'       => WarehouseEnums::REQUIREMENT_STATUS_SETTLE,
                            'updated_id'   => $user['id'],
                            'updated_name' => $user['name'],
                            'updated_at'   => $now,
                        ];
                        if ($requirement_info->update($requirement_update) === false) {
                            $this->logger->warning("需求编号 {$requirement_info->no} 更新为待入驻失败，请联系产品修复数据");
                        } else {
                            $this->logger->info("需求编号 {$requirement_info->no} 更新为 待入驻 - 成功");
                        }
                    }

                } else {
                    // 线索状态 非待付款
                    $this->logger->info(['syncRelatedTheadPayTH' => "线索编号 {$thread_info->no} 线索状态 {$thread_info->status} -> 非待付款",]);

                    $is_continue_processing = true;
                    $is_null_thread_id      = true;
                }

            } else {
                // 无关联线索
                $this->logger->info(['syncRelatedTheadPayTH' => '无关联线索',]);

                $is_continue_processing = true;
                $is_null_thread_id      = false;
            }

            // 继续处理最新审批通过的报价单关联的仓库需求
            if ($is_continue_processing) {
                $requirement_status = WarehouseEnums::REQUIREMENT_STATUS_PAY;
                $requirement_info = $this->getPendingSignRequirementInfo($item['warehouse_id'], $requirement_status, $is_null_thread_id);
                $this->logger->info([
                    'syncRelatedTheadPayTH' => [
                        'warehouse_id'             => $item['warehouse_id'],
                        'is_null_thread_id'        => $is_null_thread_id,
                        'related_requirement_info' => $requirement_info,
                    ],
                ]);

                if (!empty($requirement_info['requirement_id'])) {
                    // 获取关联需求model
                    $requirement_model  = WarehouseRequirementModel::findFirst($requirement_info['requirement_id']);
                    $requirement_update = [
                        'status'       => WarehouseEnums::REQUIREMENT_STATUS_SETTLED,
                        'updated_id'   => $user['id'],
                        'updated_name' => $user['name'],
                        'updated_at'   => $now,
                    ];
                    if ($requirement_model->update($requirement_update) === false) {
                        $this->logger->warning('仓库需求 ' . $requirement_model->no . ' 更新为已入驻失败，请联系产品修复数据');
                    } else {
                        $this->logger->info('仓库需求 ' . $requirement_model->no . ' 更新为 已入驻 - 成功');
                    }
                }
            }
        }

        return true;
    }

}
