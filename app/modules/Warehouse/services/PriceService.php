<?php
/**
 * 仓库管理 - 仓库报价审核
 */

namespace App\Modules\Warehouse\Services;

use App\Library\Enums;
use App\Library\Enums\ByWorkflowEnums;
use App\Library\Enums\DownloadCenterEnum;
use App\Library\Enums\WarehouseEnums;
use App\Library\Enums\GlobalEnums;
use App\Library\Enums\ContractEnums;
use App\Library\ErrCode;
use App\Library\Exception\BusinessException;
use App\Library\Validation\Validation;
use App\Library\Validation\ValidationException;
use App\Models\oa\ByWorkflowAuditLogModel;
use App\Models\oa\ContractWarehouseModel;
use App\Models\oa\SysAttachmentModel;
use App\Models\oa\WarehouseRequirementModel;
use App\Models\oa\WarehouseStoreModel;
use App\Models\oa\WarehouseThreadModel;
use App\Models\oa\WarehouseThreadPriceRecordModel;
use App\Models\oa\WarehouseThreadVerifyRecordModel;
use App\Modules\Common\Services\DownloadCenterService;
use App\Modules\Common\Services\EnumsService;
use App\Modules\Contract\Models\ContractStoreRentingDetailModel;
use App\Modules\Contract\Models\ContractStoreRentingModel;
use App\Modules\Contract\Services\ContractStoreRentingExportService;
use App\Modules\Third\Services\ByWorkflowService;
use App\Repository\oa\ByWorkflowAuditLogRepository;
use App\Repository\oa\ContractWarehouseRepository;
use App\Repository\oa\SysAttachmentRepository;
use App\Repository\oa\WarehouseRequirementRepository;
use App\Repository\oa\WarehouseThreadRepository;
use App\Repository\StoreRepository;
use App\Util\RedisKey;
use Exception;
use GuzzleHttp\Exception\GuzzleException;

class PriceService extends BaseService
{
    //列表类型
    const WAIT_LIST = 1;//仓库报价审核-待处理-列表
    const DONE_LIST = 2;//仓库报价审核-已处理-列表

    private static $instance;

    private function __construct()
    {
    }

    private function __clone()
    {
    }

    public static function getInstance()
    {
        if (!isset(self::$instance) || !(self::$instance instanceof self)) {
            self::$instance = new self;
        }
        return self::$instance;
    }

    //非必须参数
    public static $not_must_params = [
        'apply_type',
        'apply_date_start',
        'apply_date_end',
        'pageNum',
        'pageSize',
    ];

    //列表-参数验证
    public static $validate_list = [
        'no'               => 'Str',                                                //报价单号
        'store_id'         => 'Str',                                                //网点编号
        'apply_type'       => 'IntIn:' . WarehouseEnums::THREAD_APPLY_TYPE_VALIDATE,//申请类型
        'apply_date_start' => 'Date',                                               //申请日期-起始
        'apply_date_end'   => 'Date',                                               //申请日期-截止
        'pageNum'          => 'IntGt:0',
        'pageSize'         => 'IntGt:0',
    ];

    //ID
    public static $validate_id = [
        'id' => 'Required|IntGt:0',
    ];

    //通过
    public static $validate_pass = [
        'id'     => 'Required|IntGt:0',
        'reason' => 'StrLenGeLe:0,1000',
    ];

    //通过
    public static $validate_reject = [
        'id'     => 'Required|IntGt:0',
        'reason' => 'Required|StrLenGeLe:1,1000',
    ];

    /**
     * 报价审核列表
     * @param array $params 请求参数组
     * @param array $user 当前登陆者信息组
     * @param int $audit_type 列表 1仓库报价审核-待处理-列表、2仓库报价审核-已处理-列表
     * @return array
     */
    public function list(array $params, array $user, int $audit_type = self::WAIT_LIST)
    {
        $page_size = empty($params['pageSize']) ? GlobalEnums::DEFAULT_PAGE_SIZE : $params['pageSize'];
        $page_num  = empty($params['pageNum']) ? GlobalEnums::DEFAULT_PAGE_NUM : $params['pageNum'];
        $offset    = $page_size * ($page_num - 1);
        $code      = ErrCode::$SUCCESS;
        $message   = $real_message = '';
        $data      = [
            'items'      => [],
            'pagination' => [
                'current_page' => $page_num,
                'per_page'     => $page_size,
                'total_count'  => 0,
            ],
        ];

        try {
            //待处理列表
            if ($audit_type == self::WAIT_LIST) {
                //第一步在oa库查特定条件下的workflow_no号列
                $builder = $this->modelsManager->createBuilder();
                $builder->columns('main.workflow_no');
                $builder->from(['main' => WarehouseThreadPriceRecordModel::class]);
                $builder     = $this->getCondition($builder, $params, $audit_type, $user);
                $search_list = $builder->getQuery()->execute()->toArray();

                if (!empty($search_list)) {
                    // 第二步调取by接口获取当前登陆人要审批的审批流水号组
                    $workflow_no                       = array_column($search_list, 'workflow_no');
                    $by_list_params                    = [
                        'serial_no'   => $workflow_no,
                        'biz_type'    => [ByWorkflowEnums::BY_BIZ_TYPE_WAREHOUSE_THREAD_PRICE],
                        'approval_id' => $user['id'],
                        'state'       => [ByWorkflowEnums::BY_OPERATE_WAIT_AUDIT],
                        'page_num'    => $page_num,
                        'page_size'   => $page_size,
                        'sort'        => 4,//审批人接收时间升序
                    ];
                    $by_workflow_service               = new ByWorkflowService();
                    $result                            = $by_workflow_service->getList($by_list_params);
                    $data['pagination']['total_count'] = !empty($result['total_count']) ? $result['total_count'] : 0;
                    if (!empty($result['list'])) {
                        //第三步查询到该审批人明细存在要审批的数据，关联上业务单据然后展示
                        $params['workflow_no'] = array_values(array_column($result['list'], 'serial_no'));
                        if ($params['workflow_no']) {
                            $builder = $this->modelsManager->createBuilder();
                            $columns = 'main.id, main.workflow_no, main.no, main.store_id, main.store_name, main.apply_type, main.apply_date, main.created_id, main.created_name, main.warehouse_area, ';
                            $columns .= 'main.warehouse_month_rent, main.contract_period, main.contract_period_unit, main.deposit, main.down_payment';
                            $builder->columns($columns);
                            $builder->from(['main' => WarehouseThreadPriceRecordModel::class]);
                            $builder = $this->getCondition($builder, $params, $audit_type, $user);
                            $items   = $builder->getQuery()->execute()->toArray();
                            $items   = $this->handleListItems($items);
                            $items   = $by_workflow_service->pendingDataSort($result['list'], $items);
                        }
                    }
                }
            } else {
                if ($audit_type == self::DONE_LIST) {
                    //已处理列表
                    $builder = $this->modelsManager->createBuilder();
                    $builder->columns('count(distinct audit.biz_value) as total');
                    $builder->from(['main' => WarehouseThreadPriceRecordModel::class]);
                    $builder->leftjoin(ByWorkflowAuditLogModel::class, 'audit.biz_value = main.id', 'audit');
                    $builder    = $this->getCondition($builder, $params, $audit_type, $user);
                    $total_info = $builder->getQuery()->getSingleResult();
                    $count      = intval($total_info->total);
                    if ($count > 0) {
                        $columns = 'main.id, main.no, main.store_id, main.store_name, main.apply_type, main.apply_date, main.created_id, main.created_name, main.warehouse_area, ';
                        $columns.= 'main.warehouse_month_rent, main.contract_period, main.contract_period_unit, main.deposit, main.down_payment';
                        $builder->columns($columns);
                        $builder->limit($page_size, $offset);
                        //由于有驳回、撤回可重新提交，审批人反复对一个申请单审批的可能所以列表需要过滤下重复审批的单据只展示一个即可
                        $builder->groupBy('main.id');
                        $builder->orderby('main.id asc');
                        $items = $builder->getQuery()->execute()->toArray();
                        $items = $this->handleListItems($items);
                    }
                    $data['pagination']['total_count'] = $count;
                }
            }
            $data['items'] = $items ?? [];
        } catch (\Exception $e) {
            $code    = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $this->logger->error('仓库管理-仓库报价审核-列表获取失败: ' . $e->getMessage());
        }
        return [
            'code'    => $code,
            'message' => $message,
            'data'    => $data,
        ];
    }

    /**
     * 组装查询条件
     * @param object $builder 查询器
     * @param array $params 查询条件
     * @param int $audit_type 列表 1仓库报价审核-待处理-列表、2仓库报价审核-已处理-列表
     * @param array $user 当前登陆者信息组
     * @return object
     */
    public function getCondition(object $builder, array $params, int $audit_type, array $user)
    {
        $no               = !empty($params['no']) ? $params['no'] : '';                            //报价单号
        $store_id         = !empty($params['store_id']) ? $params['store_id'] : '';                //网点编号
        $apply_type       = !empty($params['apply_type']) ? $params['apply_type'] : 0;             //申请类型
        $apply_date_start = !empty($params['apply_date_start']) ? $params['apply_date_start'] : '';//申请日期-起始
        $apply_date_end   = !empty($params['apply_date_end']) ? $params['apply_date_end'] : '';    //申请日期-截止
        $workflow_no      = $params['workflow_no'] ?? [];//按照by的审批流水号搜索

        if ($audit_type == self::WAIT_LIST) {
            //待审核
            $builder->andWhere('main.status = :status:', ['status' => Enums::WF_STATE_PENDING]);
        } else {
            //已处理（驳回、通过）
            $builder->andWhere('audit.approval_id = :approval_id:', ['approval_id' => $user['id']]);
            $builder->andWhere('audit.biz_type = :biz_type:', ['biz_type' => Enums::WF_WAREHOUSE_PRICE_BIZ_TYPE]);
            $builder->inWhere('audit.status', [Enums::WF_STATE_REJECTED, Enums::WF_STATE_APPROVED]);
        }

        //报价单号
        if (!empty($no)) {
            $builder->andWhere('main.no like :no:', ['no' => '%' . $no . '%']);
        }

        //使用网点
        if (!empty($store_id)) {
            $builder->andWhere('main.store_id =:store_id:', ['store_id' => $store_id]);
        }

        //申请类型
        if (!empty($apply_type)) {
            $builder->andWhere('main.apply_type =:apply_type:', ['apply_type' => $apply_type]);
        }

        //申请日期-起始
        if (!empty($apply_date_start)) {
            $builder->andWhere('main.apply_date >= :apply_date_start:', ['apply_date_start' => $apply_date_start]);
        }

        //申请日期-截止
        if (!empty($apply_date_end)) {
            $builder->andWhere('main.apply_date <= :apply_date_end:', ['apply_date_end' => $apply_date_end]);
        }

        //按照by审批流水号搜索
        if (!empty($workflow_no)) {
            $builder->inWhere('main.workflow_no', $workflow_no);
        }

        return $builder;
    }


    /**
     * 格式化报价审核单列表
     * @param array $items 列表
     * @return array
     */
    private function handleListItems($items)
    {
        if (empty($items)) {
            return [];
        }

        foreach ($items as &$item) {
            $item['apply_type_text']           = static::$t->_(WarehouseEnums::$thread_price_apply_type[$item['apply_type']]);
            $item['contract_period_unit_text'] = static::$t->_(WarehouseEnums::$contract_period_unit[$item['contract_period_unit']]);
        }
        return $items;
    }

    /**
     * 获取报价信息
     * @param integer $id 需求ID
     * @return mixed
     * @throws ValidationException
     */
    public function getPriceInfo(int $id)
    {
        $price_info = WarehouseThreadPriceRecordModel::findFirst([
            'conditions' => 'id = :id:',
            'bind' => ['id' => $id],
        ]);
        if (empty($price_info)) {
            throw new ValidationException(static::$t->_('warehouse_price_not_exists'), ErrCode::$VALIDATE_ERROR);
        }
        return $price_info;
    }

    /**
     * 校验报价信息
     * @param integer $id 需求ID
     * @return mixed
     * @throws ValidationException
     */
    private function validatePriceOperate(int $id)
    {
        $price_info = $this->getPriceInfo($id);
        if ($price_info->status != Enums::WF_STATE_PENDING) {
            throw new ValidationException(static::$t->_('warehouse_price_status_error'), ErrCode::$VALIDATE_ERROR);
        }
        return $price_info;
    }

    /**
     * 驳回
     * @param array $params 请求参数组
     * @param array $user 当前登陆者信息组
     * @return array
     */
    public function reject(array $params, array $user)
    {
        $code = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $db = $this->getDI()->get('db_oa');
        try {
            $db->begin();
            $price_info = $this->validatePriceOperate($params['id']);

            //调取by驳回接口，by驳回成功，记录驳回信息
            $by_workflow = new ByWorkflowService();
            $by_workflow->audit([
                'serial_no'   => $price_info->workflow_no,
                'biz_type'    => ByWorkflowEnums::BY_BIZ_TYPE_WAREHOUSE_THREAD_PRICE,
                'reason'      => $params['reason'],
                'status'      => ByWorkflowEnums::BY_OPERATE_REJECT,
                'operator_id' => $user['id'],
            ]);

            //by审批成功，驳回单据
            $now_time = date('Y-m-d H:i:s');
            $reject_data = [
                'reason'       => $params['reason'],
                'status'       => Enums::WF_STATE_REJECTED,
                'updated_id'   => $user['id'],
                'updated_name' => $user['name'],
                'updated_at'   => $now_time,
            ];
            $bool = $price_info->i_update($reject_data);
            if ($bool === false) {
                throw new BusinessException('修改报价审核状态为驳回，失败: '. json_encode($reject_data, JSON_UNESCAPED_UNICODE) . '; 可能存在的问题: ' . get_data_object_error_msg($price_info), ErrCode::$BUSINESS_ERROR);
            }

            //记录审批操作记录
            ByWorkflowAuditLogRepository::getInstance()->addAuditLog(Enums::WF_WAREHOUSE_PRICE_BIZ_TYPE, $price_info->id, $price_info->created_id, $user['id'], Enums::WF_STATE_REJECTED);

            //将线索状态和需求状态更新为待报价
            if ($price_info->source_type == WarehouseEnums::THREAD_PRICE_SOURCE_TYPE_THREAD) {
                $thread_info = ThreadService::getInstance()->getThreadInfo($price_info->thread_id);
                if ($thread_info->status != WarehouseEnums::THREAD_STATUS_PRICE_AUDIT) {
                    throw new ValidationException(static::$t->_('warehouse_thread_status_error', ['status' => static::$t->_(WarehouseEnums::$thread_status[WarehouseEnums::THREAD_STATUS_PRICE_AUDIT])]), ErrCode::$VALIDATE_ERROR);
                }
                $bool = $thread_info->i_update([
                    'status'       => WarehouseEnums::THREAD_STATUS_PRICE,
                    'updated_id'   => $user['id'],
                    'updated_name' => $user['name'],
                    'updated_at'   => $now_time,
                ]);
                if ($bool === false) {
                    throw new BusinessException('修改报价所关联线索 【 ' . $thread_info->no . ' 】状态为待报价失败; 可能存在的问题: ' . get_data_object_error_msg($thread_info), ErrCode::$BUSINESS_ERROR);
                }

                $requirement_info = RequirementService::getInstance()->getRequirementInfo($price_info->requirement_id);
                if ($requirement_info->status != WarehouseEnums::REQUIREMENT_STATUS_PRICE_AUDIT) {
                    throw new ValidationException(static::$t->_('warehouse_requirement_status_error', ['status' => static::$t->_(WarehouseEnums::getRequirementStatus()[WarehouseEnums::REQUIREMENT_STATUS_PRICE_AUDIT])]), ErrCode::$VALIDATE_ERROR);
                }
                $bool = $requirement_info->i_update([
                    'status'       => WarehouseEnums::REQUIREMENT_STATUS_PRICE,
                    'updated_id'   => $user['id'],
                    'updated_name' => $user['name'],
                    'updated_at'   => $now_time,
                ]);
                if ($bool === false) {
                    throw new BusinessException('修改报价所关联需求 【 ' . $requirement_info->no . ' 】状态为待报价失败; 可能存在的问题: ' . get_data_object_error_msg($requirement_info), ErrCode::$BUSINESS_ERROR);
                }
            } elseif ($price_info->source_type == WarehouseEnums::THREAD_PRICE_SOURCE_TYPE_RM_RENEWED) {
                // 仅处理需求状态, 因为来自该渠道[处理仓库需求-待签约-发起报价]的需求报价类型 为 renew, 无需处理需求的线索(即便需求有线索)
                $requirement_model = RequirementService::getInstance()->getRequirementInfo($price_info->requirement_id);
                if ($requirement_model->status != WarehouseEnums::REQUIREMENT_STATUS_PRICE_AUDIT) {
                    throw new ValidationException(static::$t->_('warehouse_requirement_status_error', ['status' => static::$t->_(WarehouseEnums::getRequirementStatus()[WarehouseEnums::REQUIREMENT_STATUS_PRICE_AUDIT])]), ErrCode::$VALIDATE_ERROR);
                }

                $requirement_data = [
                    'status'       => WarehouseEnums::REQUIREMENT_STATUS_PENDING_RENEWED,
                    'updated_id'   => $user['id'],
                    'updated_name' => $user['name'],
                    'updated_at'   => $now_time,
                ];
                if ($requirement_model->i_update($requirement_data) === false) {
                    throw new BusinessException('修改报价所关联需求 【 ' . $requirement_data->no . ' 】状态为待续约失败; 原因可能是=' . get_data_object_error_msg($requirement_model), ErrCode::$BUSINESS_ERROR);
                }
            }

            $db->commit();
        } catch (ValidationException $e) {
            $code    = $e->getCode();
            $message = $e->getMessage();
        } catch (BusinessException $e) {
            // 业务错误（数据错误等），不可对外抛出
            $code         = $e->getCode();
            $message      = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        } catch (\Exception $e) {
            // 系统错误，不可对外抛出
            $code         = ErrCode::$SYSTEM_ERROR;
            $message      = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        }

        if (!empty($message)) {
            $db->rollback();
        }

        if (!empty($real_message)) {
            $price_source_type = $price_info->source_type ?? 'null';
            $this->logger->error("仓库管理-仓库报价审核-驳回失败: price_source_type-{$price_source_type}" . $real_message);
        }

        return [
            'code'    => $code,
            'message' => $message,
            'data'    => [],
        ];
    }

    /**
     * 通过
     * @param array $params 请求参数组
     * @param array $user 当前登陆者信息组
     * @return array
     */
    public function pass(array $params, array $user)
    {
        $code = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $db = $this->getDI()->get('db_oa');
        try {
            $db->begin();
            $price_info = $this->validatePriceOperate($params['id']);

            //调取by审批接口，by审核通过成功，记录通过信息
            $by_workflow = new ByWorkflowService();
            $result = $by_workflow->audit([
                'serial_no'   => $price_info->workflow_no,
                'biz_type'    => ByWorkflowEnums::BY_BIZ_TYPE_WAREHOUSE_THREAD_PRICE,
                'reason'      => $params['reason'] ?? '',
                'status'      => ByWorkflowEnums::BY_OPERATE_PASS,
                'operator_id' => $user['id'],
            ]);
            //by审批通过且最终节点审批才需要做下面的逻辑
            $now_time = date('Y-m-d H:i:s');
            if (!empty($result) && !empty($result['is_final']) && $result['is_final'] == 1) {
                $audit_data = [
                    'reason'       => $params['reason'] ?? '',
                    'status'       => Enums::WF_STATE_APPROVED,
                    'updated_id'   => $user['id'],
                    'updated_name' => $user['name'],
                    'updated_at'   => $now_time,
                    'approval_at'  => $now_time,
                ];
                $bool = $price_info->i_update($audit_data);
                if ($bool === false) {
                    throw new BusinessException('修改报价审核状态为通过，失败: '. json_encode($audit_data, JSON_UNESCAPED_UNICODE) . '; 可能存在的问题: ' . get_data_object_error_msg($price_info), ErrCode::$BUSINESS_ERROR);
                }

                //将线索状态更新和需求状态更新为待签约
                if ($price_info->source_type == WarehouseEnums::THREAD_PRICE_SOURCE_TYPE_THREAD) {
                    $thread_info = ThreadService::getInstance()->getThreadInfo($price_info->thread_id);
                    if ($thread_info->status != WarehouseEnums::THREAD_STATUS_PRICE_AUDIT) {
                        throw new ValidationException(static::$t->_('warehouse_thread_status_error', ['status' => static::$t->_(WarehouseEnums::$thread_status[WarehouseEnums::THREAD_STATUS_PRICE_AUDIT])]), ErrCode::$VALIDATE_ERROR);
                    }

                    $bool = $thread_info->i_update([
                        'status'       => WarehouseEnums::THREAD_STATUS_SIGN,
                        'updated_id'   => $user['id'],
                        'updated_name' => $user['name'],
                        'updated_at'   => $now_time,
                    ]);
                    if ($bool === false) {
                        throw new BusinessException('修改报价所关联线索 【 ' . $thread_info->no . ' 】状态为待签约失败; 可能存在的问题: ' . get_data_object_error_msg($thread_info), ErrCode::$BUSINESS_ERROR);
                    }

                    $requirement_info = RequirementService::getInstance()->getRequirementInfo($price_info->requirement_id);
                    if ($requirement_info->status != WarehouseEnums::REQUIREMENT_STATUS_PRICE_AUDIT) {
                        throw new ValidationException(static::$t->_('warehouse_requirement_status_error', ['status' => static::$t->_(WarehouseEnums::getRequirementStatus()[WarehouseEnums::REQUIREMENT_STATUS_PRICE_AUDIT])]), ErrCode::$VALIDATE_ERROR);
                    }
                    $bool = $requirement_info->i_update([
                        'status'       => WarehouseEnums::REQUIREMENT_STATUS_SIGN,
                        'updated_id'   => $user['id'],
                        'updated_name' => $user['name'],
                        'updated_at'   => $now_time,
                    ]);
                    if ($bool === false) {
                        throw new BusinessException('修改报价所关联需求 【 ' . $requirement_info->no . ' 】状态为待签约失败; 可能存在的问题: ' . get_data_object_error_msg($requirement_info), ErrCode::$BUSINESS_ERROR);
                    }
                } elseif ($price_info->source_type == WarehouseEnums::THREAD_PRICE_SOURCE_TYPE_RM_RENEWED) {
                    // 仅处理需求状态, 因为来自该渠道[处理仓库需求-待签约-发起报价], 无需处理需求的线索(即便需求有线索)
                    $requirement_model = RequirementService::getInstance()->getRequirementInfo($price_info->requirement_id);
                    if ($requirement_model->status != WarehouseEnums::REQUIREMENT_STATUS_PRICE_AUDIT) {
                        throw new ValidationException(static::$t->_('warehouse_requirement_status_error', ['status' => static::$t->_(WarehouseEnums::getRequirementStatus()[WarehouseEnums::REQUIREMENT_STATUS_PRICE_AUDIT])]), ErrCode::$VALIDATE_ERROR);
                    }

                    $requirement_data = [
                        'status'       => WarehouseEnums::REQUIREMENT_STATUS_SIGN,
                        'updated_id'   => $user['id'],
                        'updated_name' => $user['name'],
                        'updated_at'   => $now_time,
                    ];
                    if ($requirement_model->i_update($requirement_data) === false) {
                        throw new BusinessException('修改报价关联需求 【 ' . $requirement_model->no . ' 】状态为待签约失败; 原因可能是=' . get_data_object_error_msg($requirement_model), ErrCode::$BUSINESS_ERROR);
                    }
                }
            }

            //记录审批操作记录
            ByWorkflowAuditLogRepository::getInstance()->addAuditLog(Enums::WF_WAREHOUSE_PRICE_BIZ_TYPE, $price_info->id, $price_info->created_id, $user['id'], Enums::WF_STATE_APPROVED);

            $db->commit();
        } catch (ValidationException $e) {
            $code    = $e->getCode();
            $message = $e->getMessage();
        } catch (BusinessException $e) {
            // 业务错误（数据错误等），不可对外抛出
            $code         = $e->getCode();
            $message      = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        } catch (\Exception $e) {
            // 系统错误，不可对外抛出
            $code         = ErrCode::$SYSTEM_ERROR;
            $message      = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        }
        if (!empty($message)) {
            $db->rollback();
        }
        if (!empty($real_message)) {
            $this->logger->error('仓库管理-仓库报价审核-通过 失败:' . $real_message);
        }
        return [
            'code'    => $code,
            'message' => $message,
            'data'    => [],
        ];
    }


    /**
     * 查看
     * @param int $id 报价ID
     * @param array $user 当前登陆者信息组
     * @return array
     */
    public function view(int $id, array $user)
    {
        $code    = ErrCode::$SUCCESS;
        $message = 'success';
        $data    = [];

        try {
            $price_info                     = $this->getPriceInfo($id);
            $data                           = $price_info->toArray();

            $thread_enums = ThreadService::getInstance()->getEnums();
            $rent_payment_method_map              = array_column($thread_enums['rent_payment_method'], 'label', 'value');
            $water_bill_payment_type_map          = array_column($thread_enums['water_bill_payment_type'], 'label', 'value');
            $electricity_bill_payment_type_map    = array_column($thread_enums['electricity_bill_payment_type'], 'label', 'value');
            $water_billing_method_map             = array_column($thread_enums['water_billing_method'], 'label', 'value');
            $electricity_billing_method_map       = array_column($thread_enums['electricity_billing_method'], 'label', 'value');
            $is_install_fire_extinguishers_map    = array_column($thread_enums['is_install_fire_extinguishers'], 'label', 'value');
            $withholding_tax_liability_bearer_map = array_column($thread_enums['withholding_tax_liability_bearer'], 'label', 'value');
            $land_tax_liability_bearer_map        = array_column($thread_enums['land_tax_liability_bearer'], 'label', 'value');
            $stamp_duty_liability_bearer_map      = array_column($thread_enums['stamp_duty_liability_bearer'], 'label', 'value');

            $rent_payment_method      = !empty($data['rent_payment_method']) ? explode(',', $data['rent_payment_method']) : [];
            $rent_payment_method_item = [];
            foreach ($rent_payment_method as $method_val) {
                $rent_payment_method_item[] = $rent_payment_method_map[$method_val] ?? '';
            }

            $data['rent_payment_method'] = array_map(function ($v) {
                return (int)$v;
            }, explode(',', $data['rent_payment_method']));

            $data['water_bill_payment_type']          = convert_type($data['water_bill_payment_type']);
            $data['electricity_bill_payment_type']    = convert_type($data['electricity_bill_payment_type']);
            $data['water_billing_method']             = convert_type($data['water_billing_method']);
            $data['electricity_billing_method']       = convert_type($data['electricity_billing_method']);
            $data['is_install_fire_extinguishers']    = convert_type($data['is_install_fire_extinguishers']);
            $data['withholding_tax_liability_bearer'] = convert_type($data['withholding_tax_liability_bearer']);
            $data['land_tax_liability_bearer']        = convert_type($data['land_tax_liability_bearer']);
            $data['stamp_duty_liability_bearer']      = convert_type($data['stamp_duty_liability_bearer']);

            $data['rent_payment_method_item']              = implode(';', $rent_payment_method_item);
            $data['water_bill_payment_type_text']          = $water_bill_payment_type_map[$data['water_bill_payment_type']] ?? '';
            $data['electricity_bill_payment_type_text']    = $electricity_bill_payment_type_map[$data['electricity_bill_payment_type']] ?? '';
            $data['water_billing_method_text']             = $water_billing_method_map[$data['water_billing_method']] ?? '';
            $data['electricity_billing_method_text']       = $electricity_billing_method_map[$data['electricity_billing_method']] ?? '';
            $data['is_install_fire_extinguishers_text']    = $is_install_fire_extinguishers_map[$data['is_install_fire_extinguishers']] ?? '';
            $data['withholding_tax_liability_bearer_text'] = $withholding_tax_liability_bearer_map[$data['withholding_tax_liability_bearer']] ?? '';
            $data['land_tax_liability_bearer_text']        = $land_tax_liability_bearer_map[$data['land_tax_liability_bearer']] ?? '';
            $data['stamp_duty_liability_bearer_text']      = $stamp_duty_liability_bearer_map[$data['stamp_duty_liability_bearer']] ?? '';

            $last_price_rent_payment_method      = !empty($data['last_price_rent_payment_method']) ? explode(',', $data['last_price_rent_payment_method']) : [];
            $last_price_rent_payment_method_item = [];

            foreach ($last_price_rent_payment_method as $method_val) {
                $last_price_rent_payment_method_item[] = $rent_payment_method_map[$method_val] ?? '';
            }

            $data['last_price_rent_payment_method'] = array_map(function ($v) {
                return (int)$v;
            }, explode(',', $data['last_price_rent_payment_method']));

            $data['last_price_water_bill_payment_type']          = convert_type($data['last_price_water_bill_payment_type']);
            $data['last_price_electricity_bill_payment_type']    = convert_type($data['last_price_electricity_bill_payment_type']);
            $data['last_price_water_billing_method']             = convert_type($data['last_price_water_billing_method']);
            $data['last_price_electricity_billing_method']       = convert_type($data['last_price_electricity_billing_method']);
            $data['last_price_is_install_fire_extinguishers']    = convert_type($data['last_price_is_install_fire_extinguishers']);
            $data['last_price_withholding_tax_liability_bearer'] = convert_type($data['last_price_withholding_tax_liability_bearer']);
            $data['last_price_land_tax_liability_bearer']        = convert_type($data['last_price_land_tax_liability_bearer']);
            $data['last_price_stamp_duty_liability_bearer']      = convert_type($data['last_price_stamp_duty_liability_bearer']);

            $data['last_price_rent_payment_method_item']              = implode(';', $last_price_rent_payment_method_item);
            $data['last_price_water_bill_payment_type_text']          = $water_bill_payment_type_map[$data['last_price_water_bill_payment_type']] ?? '';
            $data['last_price_electricity_bill_payment_type_text']    = $electricity_bill_payment_type_map[$data['last_price_electricity_bill_payment_type']] ?? '';
            $data['last_price_water_billing_method_text']             = $water_billing_method_map[$data['last_price_water_billing_method']] ?? '';
            $data['last_price_electricity_billing_method_text']       = $electricity_billing_method_map[$data['last_price_electricity_billing_method']] ?? '';
            $data['last_price_is_install_fire_extinguishers_text']    = $is_install_fire_extinguishers_map[$data['last_price_is_install_fire_extinguishers']] ?? '';
            $data['last_price_withholding_tax_liability_bearer_text'] = $withholding_tax_liability_bearer_map[$data['last_price_withholding_tax_liability_bearer']] ?? '';
            $data['last_price_land_tax_liability_bearer_text']        = $land_tax_liability_bearer_map[$data['last_price_land_tax_liability_bearer']] ?? '';
            $data['last_price_stamp_duty_liability_bearer_text']      = $stamp_duty_liability_bearer_map[$data['last_price_stamp_duty_liability_bearer']] ?? '';

            $data['attachments']            = $price_info->getAttachment()->toArray();
            $data['last_price_attachments'] = $price_info->getLastPriceAttachment()->toArray();

            //验仓结果
            $data['verify_info'] = $price_info->thread_id ? ThreadService::getInstance()->getThreadVerifySubmitDetail($price_info->thread_id) : (object)[];

            //审批流
            $data['auth_logs'] = $this->getAuthLogs($data['workflow_no'], $user['id']);
        } catch (ValidationException $e) {
            $code    = $e->getCode();
            $message = $e->getMessage();
        } catch (Exception $e) {
            $code    = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $this->logger->error('仓库管理-仓库报价审核-详情获取失败:' . $e->getMessage());
        }

        return [
            'code'    => $code,
            'message' => $message,
            'data'    => $data,
        ];
    }

    /**
     * by审批流日志
     * @param string $workflow_no by审批流编号
     * @param int $user_id 查看人工号
     * @return array
     */
    public function getAuthLogs(string $workflow_no, int $user_id)
    {
        return (new ByWorkflowService())->log(['serial_no' => $workflow_no , 'operator_id' => $user_id, 'biz_type' => ByWorkflowEnums::BY_BIZ_TYPE_WAREHOUSE_THREAD_PRICE]);
    }

    /**
     * 获取报价单-报价时-报价的附件
     * @param integer $price_id 报价单ID
     * @return mixed
     */
    public function getPriceAttachments($price_id)
    {
       return SysAttachmentModel::find([
            'conditions' => 'oss_bucket_key = :oss_bucket_key: and oss_bucket_type = :oss_bucket_type: and sub_type = :sub_type: and deleted = :deleted:',
            'bind'       => [
                'oss_bucket_key'  => $price_id,
                'oss_bucket_type' => Enums::OSS_BUCKET_TYPE_WAREHOUSE_THREAD_PRICE_FILE,
                'sub_type'        => Enums::OSS_SUB_TYPE_WAREHOUSE_THREAD_PRICE,
                'deleted'         => GlobalEnums::IS_NO_DELETED,
            ],
        ])->toArray();
    }

    /**
     * 获取仓库最新审批通过的报价审批记录
     * @param string $warehouse_id 仓库ID
     * @param array $user 当前登陆者信息组
     * @return array
     */
    public function getLatestPriceRecord(string $warehouse_id, array $user): array
    {
        $code    = ErrCode::$SUCCESS;
        $message = 'success';
        $data    = [];
        try {
            $builder = $this->modelsManager->createBuilder();
            $builder->columns('main.id, main.no, main.store_id');
            $builder->from(['main' => WarehouseThreadPriceRecordModel::class]);
            $builder->where('main.status = :status:', ['status' => Enums::WF_STATE_APPROVED]);
            $builder->andWhere('main.origin_warehouse_id = :warehouse_id:', ['warehouse_id' => $warehouse_id]);
            $builder->orderBy('main.approval_at DESC');
            $builder->limit(1);
            $item = $builder->getQuery()->getSingleResult();
            if (empty($item)) {
                //未找到该仓库的报价详情！
                throw new ValidationException(static::$t->_('22497_not_found_warehouse_price_record'), ErrCode::$VALIDATE_ERROR);
            }
            $data['id'] = $item->id;
            $data['no'] = $item->no;

            //获取当前用户可取的网点类型
            $store_cate = ContractStoreRentingExportService::getInstance()->getStoreCateByUid($user['id']);
            $store_info = (new StoreRepository())->getStoreDetail($item->store_id, 0);
            $data['store_category'] = $store_info && in_array($store_info['category'], $store_cate) ? $store_info['category'] : '';
        } catch (ValidationException $e) {
            $code = $e->getCode();
            $message = $e->getMessage();
        } catch (Exception $e) {
            $code    = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $this->logger->error('获取仓库最新审批通过的报价审批记录失败:' . $e->getMessage());
        }

        return [
            'code'    => $code,
            'message' => $message,
            'data'    => $data,
        ];
    }
}
