<?php

namespace App\Modules\Reimbursement\Controllers;

use App\Library\Enums;
use App\Library\Enums\DownloadCenterEnum;
use App\Library\Enums\GlobalEnums;
use App\Library\Enums\ReimbursementEnums;
use App\Library\ErrCode;
use App\Library\Exception\BusinessException;
use App\Library\Validation\Validation;
use App\Library\Validation\ValidationException;
use App\Modules\Common\Services\DownloadCenterService;
use App\Modules\Common\Services\EnumsService;
use App\Modules\Reimbursement\Services\AddService;
use App\Modules\Reimbursement\Services\BaseService;
use App\Modules\Reimbursement\Services\CategoryService;
use App\Modules\Reimbursement\Services\DetailService;
use App\Modules\Reimbursement\Services\ListService;
use App\Modules\Reimbursement\Services\UpdateService;
use App\Modules\Reimbursement\Services\ReimbursementFlowService;
use App\Modules\Loan\Services\ListService as LoanList;
use App\Modules\ReserveFund\Services\ApplyService as ReserveFundApplyService;
use App\Util\RedisKey;
use Exception;
use GuzzleHttp\Exception\GuzzleException;
use Phalcon\Http\Response;
use Phalcon\Http\ResponseInterface;

class ReimbursementController extends BaseController
{
    /**
     * 申请单添加
     * @Permission(action='reimbursement.apply.apply')
     *
     * @api https://yapi.flashexpress.pub/project/133/interface/api/20344
     * @return mixed
     * @throws ValidationException
     */
    public function addAction()
    {
        $data = $this->request->get();
        $data = BaseService::handleParams($data, AddService::$not_must_params);

        $validate_param = BaseService::getExtendValidation(BaseService::$validate_param, ReimbursementEnums::CREATE_ACTION_ADD_SUBMIT, $data);
        Validation::validate($data, $validate_param);

        // OA提交的申请单为1;
        $data['source_type'] = ReimbursementEnums::SOURCE_TYPE_OA;

        //参数验证通过后校验其他额外关联项信息验证
        $data['first_apply_date'] = date('Y-m-d');

        $data = AddService::getInstance()->validationOther($data, ReimbursementEnums::CREATE_ACTION_ADD_SUBMIT);
        if (isset($data['can_apply']) && $data['can_apply'] == ReimbursementEnums::CAN_APPLY_NO) {
            return $this->returnJson(ErrCode::$SUCCESS, $this->t['success'], $data);
        }

        $lock_key = md5('reimbursement_add_' . $data['no']);
        $res      = $this->atomicLock(function () use ($data) {
            return AddService::getInstance()->one($data, $this->user);
        }, $lock_key, 30);

        if (isset($res['code'])) {
            $code    = $res['code'];
            $message = $res['message'];
            $data    = $res['data'];
        } else {
            $code    = ErrCode::$FREQUENT_VISIT_ERROR;
            $message = $this->t['sys_processing'];
            $data    = [];
        }

        return $this->returnJson($code, $message, $data);
    }

    /**
     * 暂存
     * @Permission(action='reimbursement.apply.apply')
     *
     * @api https://yapi.flashexpress.pub/project/133/interface/api/87869
     * @return mixed
     * @throws Exception
     */
    public function saveDraftAction()
    {
        $params = $this->request->get();

        // 基础结构校验
        $validate_param = [
            'no'                    => 'Required|StrLenGeLe:10,20',
            'created_id'            => 'Required',
            'created_name'          => 'Required',
            'created_department_id' => 'Required',

            // 报销实质
            'expense'               => 'Required|Arr|ArrLenGe:0',
        ];
        Validation::validate($params, $validate_param);

        // OA来源
        $params['source_type'] = ReimbursementEnums::SOURCE_TYPE_OA;
        $lock_key              = md5('reimbursement_save_draft_' . $this->user['id']);
        $res                   = $this->atomicLock(function () use ($params) {
            return AddService::getInstance()->saveDraft($params, $this->user);
        }, $lock_key, 10);

        if (isset($res['code'])) {
            $code    = $res['code'];
            $message = $res['message'];
            $data    = $res['data'];
        } else {
            $code    = ErrCode::$FREQUENT_VISIT_ERROR;
            $message = $this->t['sys_processing'];
            $data    = [];
        }

        return $this->returnJson($code, $message, $data);
    }

    /**
     * 报销申请-添加补充附件
     * @Token
     */
    public function applySupplementInvoiceAction()
    {
        $data = $this->request->get();

        Validation::validate($data, BaseService::$validate_supplement);

        $res = AddService::getInstance()->addFile($data, $this->user);
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }

    /**
     * 数据查询-添加补充附件
     * @Permission(action='reimbursement.data.upload')
     * @return mixed
     * @throws ValidationException
     */
    public function addSupplementInvoiceAction()
    {
        $data = $this->request->get();

        Validation::validate($data, BaseService::$validate_supplement);

        $res = AddService::getInstance()->addFile($data, $this->user);
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }

    /**
     * 报销新增/重新提交页 获得默认值
     * @Permission(action='reimbursement.apply.apply')
     *
     * @return mixed
     */
    public function getDefaultAction()
    {
        $param = $this->request->get();
        $res   = AddService::getInstance()->defaultData($param, $this->user);
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }

    /**
     * 获得默认值
     *
     * @Token
     * @return mixed
     * @throws ValidationException
     */
    public function getUserAction()
    {
        $data = $this->request->get();
        Validation::validate($data, BaseService::$validate_user);

        $res = AddService::getInstance()->getUser($data['id'], true);
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }

    /**
     * 获得费用列表,这个暂时不用了，上面接口的时候返回
     *
     * @Token
     * @return mixed
     */
    public function getExpenseAction()
    {
        $res = CategoryService::getInstance()->getList($this->user);
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }

    /**
     * 获取科目列表
     *
     * @Token
     * @return mixed
     * @throws ValidationException
     */
    public function getBudgetsAction()
    {
        $data = $this->request->get();
        Validation::validate($data, BaseService::$validate_budget);
        $data = AddService::getInstance()->getBudgetList($data['cost_department'], $data['cost_store_type']);
        return $this->returnJson(ErrCode::$SUCCESS, '', $data);
    }


    /**
     * 申请单列表
     * @Permission(action='reimbursement.apply.search')
     *
     * @return mixed
     */
    public function listAction()
    {
        $params = $this->request->get();
        $res    = ListService::getInstance()->getList($params, $this->user, BaseService::LIST_TYPE_APPLY);
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }

    /**
     * 申请单详情
     * @Permission(action='reimbursement.apply.view')
     *
     * @return mixed
     * @throws ValidationException
     */
    public function detailAction()
    {
        $data = $this->request->get();
        $id   = $this->request->get('id');

        Validation::validate($data, BaseService::$validate_detail);

        $res = DetailService::getInstance()->getCommonDetail($id, $this->user['id']);
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }

    /**
     * 根据部门ID获得PcCode
     *
     * @Token
     * @return Response|ResponseInterface
     * @throws ValidationException
     */
    public function getPcCodeAction()
    {
        $departmentId = $this->request->get('department_id');
        Validation::validate(['department_id' => $departmentId], BaseService::$validate_department);

        $data = DetailService::getInstance()->getPcCode($departmentId);
        return $this->returnJson($data['code'], $data['message'], $data['data']);
    }

    /**
     * 获取费用网点类型
     *
     * @Token
     * @return Response|ResponseInterface
     * @throws ValidationException
     */
    public function getCostStoreTypeAction()
    {
        $validate         = [
            'department_id' => 'Required|IntGe:1',
            'store_id'      => 'StrLenGe:0',
            'staff_id'      => 'StrLenGe:0',
        ];
        $data             = $this->request->get();
        $data['store_id'] = $data['store_id'] ?? '';
        $data['staff_id'] = $data['staff_id'] ?? '';
        Validation::validate($data, $validate);

        $data = DetailService::getInstance()->costStoreType($data['department_id'], $data['store_id'], $data['staff_id']);
        return $this->returnJson(ErrCode::$SUCCESS, 'success', $data);
    }

    /**
     * 撤销
     * @Permission(action='reimbursement.apply.cancel')
     *
     * @return mixed
     * @throws ValidationException
     */
    public function cancelAction()
    {
        $data = $this->request->get();
        $id   = $this->request->get('id', 'int');
        $note = $this->request->get('note', 'trim');

        $validate_cancel = [
            'id'   => 'Required|IntGe:1',
            'note' => 'Required|StrLenGeLe:1,100',
        ];
        Validation::validate($data, $validate_cancel);

        $res = (new ReimbursementFlowService())->cancel($id, $note, $this->user);
        return $this->returnJson($res['code'], $res['message'], []);
    }


    /**
     * 审核列表
     * @Permission(action='reimbursement.audit.search')
     *
     * @return mixed
     * @throws Exception
     */
    public function auditListAction()
    {
        $params = $this->request->get();

        $lock_key = md5(RedisKey::REIMBURSEMENT_AUDIT_LIST_LOCK_PREFIX . $this->user['id']);
        $res      = $this->atomicLock(function () use ($params) {
            return ListService::getInstance()->getList($params, $this->user, BaseService::LIST_TYPE_AUDIT);
        }, $lock_key, 30);

        if (isset($res['code'])) {
            $code    = $res['code'];
            $message = $res['message'];
            $data    = $res['data'];
        } else {
            $code    = ErrCode::$FREQUENT_VISIT_ERROR;
            $message = $this->t['sys_processing'];
            $data    = [];
        }

        return $this->returnJson($code, $message, $data);
    }

    /**
     * 报销审核-导出
     * @Permission(action='reimbursement.audit.export')
     *
     * @return mixed
     * @throws Exception
     */
    public function auditExportAction()
    {
        $params = trim_array($this->request->get());

        $params['data_list_type'] = BaseService::LIST_TYPE_AUDIT;

        // 加锁处理
        $lock_key = md5(RedisKey::REIMBURSEMENT_AUDIT_EXPORT_LOCK_PREFIX . $this->user['id']);
        $res      = $this->atomicLock(function () use ($params) {
            // 大于指定数量, 添加异步任务 导出
            if (ListService::getInstance()->getDownloadDataTotal($params, $this->user,
                    BaseService::LIST_TYPE_AUDIT) > BaseService::SYNC_EXPORT_MAX_COUNT) {
                $result         = DownloadCenterService::getInstance()->addDownloadCenter($this->user['id'],
                    DownloadCenterEnum::FINANCE_REIMBURSEMENT_DATA, $params);
                $result['data'] = [
                    'export_method' => DownloadCenterEnum::EXPORT_METHOD_ASYNC,
                    'file_url'      => '',
                ];
            } else {
                // 小于等于指定数量, 同步导出
                $result         = ListService::getInstance()->export($params, $this->user, false, BaseService::LIST_TYPE_AUDIT);
                $result['data'] = [
                    'export_method' => DownloadCenterEnum::EXPORT_METHOD_SYNC,
                    'file_url'      => is_string($result['data']) ? $result['data'] : '',
                ];
            }

            return $result;
        }, $lock_key, 30);

        if (isset($res['code'])) {
            $code    = $res['code'];
            $message = $res['message'];
            $data    = $res['data'];
        } else {
            $code    = ErrCode::$FREQUENT_VISIT_ERROR;
            $message = $this->t['sys_processing'];
            $data    = [];
        }

        return $this->returnJson($code, $message, $data);
    }


    /**
     * 审核详情
     * @Permission(action='reimbursement.audit.view')
     *
     * @return Response|ResponseInterface
     * @throws ValidationException
     */
    public function auditDetailAction()
    {
        $data = $this->request->get();
        $id   = $this->request->get('id');
        Validation::validate($data, BaseService::$validate_detail);
        $res = DetailService::getInstance()->getCommonDetail($id, $this->user['id'], BaseService::LIST_TYPE_AUDIT);
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }


    /**
     * 审核
     * @Permission(action='reimbursement.audit.audit')
     */
    public function auditAction()
    {
        $data = $this->request->get();
        $id   = $this->request->get('id', 'int');
        $flag = $this->request->get("flag", 'int');
        $note = $this->request->get('note', 'trim');

        Validation::validate($data, BaseService::$validate_audit);
        Validation::validate($data['data'], BaseService::$validate_audit_expense);

        $lock_key = md5(RedisKey::REIMBURSEMENT_AUDIT_LOCK_PREFIX . "_{$id}_{$flag}");
        $res      = $this->atomicLock(function () use ($id, $note, $data, $flag) {
            $flow_service = new ReimbursementFlowService();
            if (empty($flag)) {
                //通过
                return $flow_service->approve($id, $note, $this->user, $data['data'] ?? []);
            } else {
                return $flow_service->reject($id, $note, $this->user);
            }
        }, $lock_key, 20);

        if (isset($res['code'])) {
            $code    = $res['code'];
            $message = $res['message'];
        } else {
            $code    = ErrCode::$FREQUENT_VISIT_ERROR;
            $message = $this->t['sys_processing'];
        }

        return $this->returnJson($code, $message, []);
    }

    /**
     * 付款
     * @Permission(action='reimbursement.pay.pay')
     */
    public function payAction()
    {
        $data = $this->request->get();

        Validation::validate($data, BaseService::$validate_pay);

        $id   = $data['id'];
        $data = array_only($data, array_keys(BaseService::$validate_pay));
        unset($data['id']);
        $res = UpdateService::getInstance()->pay($id, $data, $this->user);
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }

    /**
     * 付款==这个接口废弃了，不在调用，v8382改了这个逻辑，不再这么核销
     * @Permission(action='reimbursement.audit.edit')
     */
    public function getLoanAmountAction()
    {
        $data = $this->request->get();
        $id   = $this->request->get('id', 'int');

        Validation::validate($data, BaseService::$validate_detail);

        $res = DetailService::getInstance()->getLoanAmount($id);
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }


    /**
     * 数据列表
     * @Permission(action='reimbursement.data.search')
     *
     * @return mixed
     * @throws ValidationException
     */
    public function dataListAction()
    {
        $params = trim_array($this->request->get());

        Validation::validate($params, BaseService::getDataListValidation($params));

        $res = ListService::getInstance()->getList($params, $this->user, BaseService::LIST_TYPE_QUERY);
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }


    /**
     * 数据详情
     * @Permission(action='reimbursement.data.view')
     *
     * @return mixed
     * @throws ValidationException
     */
    public function dataDetailAction()
    {
        $data = $this->request->get();
        $id   = $this->request->get('id');
        Validation::validate($data, BaseService::$validate_detail);

        //uid=null，不用判断权限，能看所有
        $res = DetailService::getInstance()->getCommonDetail($id);
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }

    /**
     * 数据查询-导出Excel
     * @Permission(action='reimbursement.data.export')
     *
     * @return mixed
     * @throws ValidationException
     */
    public function exportAction()
    {
        $params = trim_array($this->request->get());

        Validation::validate($params, BaseService::getDataListValidation($params));

        $params['data_list_type'] = BaseService::LIST_TYPE_QUERY_EXPORT;

        // 加锁处理
        $lock_key = md5(RedisKey::REIMBURSEMENT_DATA_EXPORT_LOCK_PREFIX . $this->user['id']);
        $res      = $this->atomicLock(function () use ($params) {
            // 大于指定数量, 添加异步任务 导出
            if (ListService::getInstance()->getDownloadDataTotal($params, $this->user,
                    BaseService::LIST_TYPE_QUERY_EXPORT) > BaseService::SYNC_EXPORT_MAX_COUNT) {
                $result         = DownloadCenterService::getInstance()->addDownloadCenter($this->user['id'],
                    DownloadCenterEnum::FINANCE_REIMBURSEMENT_DATA, $params);
                $result['data'] = [
                    'export_method' => DownloadCenterEnum::EXPORT_METHOD_ASYNC,
                    'file_url'      => '',
                ];
            } else {
                // 小于等于指定数量, 同步导出
                $result         = ListService::getInstance()->export($params, $this->user, false, BaseService::LIST_TYPE_QUERY_EXPORT);
                $result['data'] = [
                    'export_method' => DownloadCenterEnum::EXPORT_METHOD_SYNC,
                    'file_url'      => is_string($result['data']) ? $result['data'] : '',
                ];
            }

            return $result;
        }, $lock_key, 30);

        if (isset($res['code'])) {
            $code    = $res['code'];
            $message = $res['message'];
            $data    = $res['data'];
        } else {
            $code    = ErrCode::$FREQUENT_VISIT_ERROR;
            $message = $this->t['sys_processing'];
            $data    = [];
        }

        return $this->returnJson($code, $message, $data);
    }

    /**
     * 下载
     * @Permission(action='reimbursement.apply.download')
     *
     * @return mixed
     * @throws GuzzleException
     * @throws ValidationException
     */
    public function downloadAction()
    {
        $data = $this->request->get();
        $id   = $data['id'] ?? 0;
        Validation::validate($data, BaseService::$validate_detail);

        $res = DetailService::getInstance()->download($id, $this->user['id']);
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }


    /**
     * 下载
     * @Permission(action='reimbursement.audit.download')
     *
     * @return mixed
     * @throws ValidationException
     */
    public function auditDownloadAction()
    {
        $data = $this->request->get();
        $id   = $data['id'] ?? 0;
        Validation::validate($data, BaseService::$validate_detail);

        $res = DetailService::getInstance()->download($id);
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }


    /**
     * 下载
     * @Permission(action='reimbursement.data.download')
     *
     * @return mixed
     * @throws ValidationException
     */
    public function dataDownloadAction()
    {
        $data = $this->request->get();
        $id   = $data['id'] ?? 0;
        Validation::validate($data, BaseService::$validate_detail);

        $res = DetailService::getInstance()->download($id);
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }

    /**
     * 申请单重新提交
     * @Permission(action='reimbursement.apply.apply')
     *
     * @api https://yapi.flashexpress.pub/project/133/interface/api/71277
     * @return mixed
     * @throws Exception
     */
    public function recommitAction()
    {
        $data = $this->request->get();
        $data = BaseService::handleParams($data, AddService::$not_must_params);

        $validate_param = array_merge(BaseService::$validate_detail, BaseService::$validate_param);
        $validate_param = BaseService::getExtendValidation($validate_param, ReimbursementEnums::CREATE_ACTION_RESUBMIT, $data);
        Validation::validate($data, $validate_param);

        $data['source_type'] = ReimbursementEnums::SOURCE_TYPE_OA;

        //参数验证通过后校验其他额外关联项信息
        $data = AddService::getInstance()->validationOther($data, ReimbursementEnums::CREATE_ACTION_RESUBMIT);
        if (isset($data['can_apply']) && $data['can_apply'] == ReimbursementEnums::CAN_APPLY_NO) {
            return $this->returnJson(ErrCode::$SUCCESS, $this->t['success'], $data);
        }

        $lock_key = md5('reimbursement_recommit_' . $this->user['id']);
        $res      = $this->atomicLock(function () use ($data) {
            return AddService::getInstance()->recommit($data, $this->user);
        }, $lock_key, 30);

        if (isset($res['code'])) {
            $code    = $res['code'];
            $message = $res['message'];
            $data    = $res['data'];
        } else {
            $code    = ErrCode::$FREQUENT_VISIT_ERROR;
            $message = $this->t['sys_processing'];
            $data    = [];
        }

        return $this->returnJson($code, $message, $data);
    }

    /**
     * 城市列表
     *
     * @Token
     * @return Response|ResponseInterface
     */
    public function country_codeAction()
    {
        $data = BaseService::getLangCountryArr();
        foreach ($data as $v => &$b) {
            $b["text"] = $this->t->_($b['text_key']);
        }
        return $this->returnJson(ErrCode::$SUCCESS, '', $data ?? "");
    }

    /**
     * 搜索字段
     *
     * @Token
     */
    public function getSearchFieldAction()
    {
        $menu_type = $this->request->get('menu_type');
        $status_item = BaseService::getStatusItem($menu_type);

        $data = EnumsService::getInstance()->getCostSearchFields(Enums\BudgetObjectEnums::BUDGET_OBJECT_ORDER_TYPE_1);
        $data['status_item'] = $status_item;

        return $this->returnJson(ErrCode::$SUCCESS, 'success', $data);
    }

    /**
     * 所属网点 工号所在网点+工号管辖的一级部门list level = 1
     *
     * @Token
     */
    public function getMyDeptListAction()
    {
        $data = ListService::getInstance()->getMyDeptList($this->user);
        return $this->returnJson(ErrCode::$SUCCESS, '', $data);
    }

    /**
     * DESC: 获取所有可用的编号
     *
     * @Token
     * @return Response|ResponseInterface
     * @throws ValidationException
     */
    public function getSerialNoListAction()
    {
        $data = $this->request->get();

        Validation::validate($data, BaseService::$validate_serial_no);

        $data = AddService::getInstance()->get_serial_no($data['serial_no']);
        return $this->returnJson(ErrCode::$SUCCESS, 'ok', array_values($data));
    }

    /**
     * DESC:获取外协员工工单详情
     *
     * @Token
     * @return Response|ResponseInterface
     * @throws ValidationException
     */
    public function getSerialNoInfoAction()
    {
        $data['serial_no'] = $this->request->get('serial_no', 'trim');

        Validation::validate($data, BaseService::$validate_serial_no);

        $data = AddService::getInstance()->get_serial_no_info($data['serial_no']);
        return $this->returnJson(ErrCode::$SUCCESS, 'ok', (array)$data);
    }


    /**
     * 报销获得当前人-借款列表（已支付，未归还）
     *
     * @Token
     */
    public function getLoansAction()
    {
        $validate = [
            'pageSize'        => 'IntGe:1',
            'pageNum'         => 'IntGe:1',
            'currency'        => 'Required|IntIn:' . GlobalEnums::VALIDATE_CURRENCY_PARAMS,
            'applyId'         => 'Required|IntGe:1|>>>:params error[applyId]',
            'cost_company_id' => 'Required|IntGe:1',
        ];

        $data = $this->request->get();

        Validation::validate($data, $validate);

        $params = array_only($data, array_keys($validate));

        //取费用所属公司,从参数中去掉,不参与数据筛选
        $cost_company_id = $params['cost_company_id'];
        unset($params['cost_company_id']);

        $params['pay_status'] = Enums::LOAN_PAY_STATUS_PAY;     //已支付
        if (!isset($params['loan_status'])) {
            $params['loan_status'] = [
                Enums\LoanEnums::LOAN_STATUS_NOT_START_RETURN,
                Enums\LoanEnums::LOAN_STATUS_PARTIAL_RETURN,
            ];   //未开始归还和部分归还
        }
        $params['order']       = 'c.lno desc';                       //产品需求12664【ALL|OA|借款】 借款允许员工多次申请由预计还款时间正序修噶为按照借款号倒序
        $params['amount_cmp']  = 'amount > re_amount + back_amount'; // 只展示未归还不为0的
        $params['biz_channel'] = 'reimbursement';

        $res = LoanList::getInstance()->getList($params, $this->user, BaseService::LIST_TYPE_APPLY);
        //标记哪一条可以自动关联
        if (!empty($res['data']['items'])) {
            $res['data']['items'] = ListService::getInstance()->getAutoOneLoan($res['data']['items'], $cost_company_id, $params['currency']);
        }

        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }


    /**
     *
     * 获得当前借款总金额-会判断是当前人的借款单
     * @Token
     */
    public function getLoansAmountAction()
    {
        $validate = [
            'amount'   => 'Required|FloatGe:0',
            'currency' => 'Required|IntGe:1',
            'loan_id'  => 'Required|IntGe:1',
            'uid'      => 'Required|IntGe:1',
        ];

        $data = $this->request->get();
        $data = array_only($data, array_keys($validate));

        Validation::validate($data, $validate);

        $res = LoanList::getInstance()->getAmountFromCurrency($data['amount'], $data['currency'], $data['loan_id'], $data['uid']);
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }

    /**
     *
     * 模糊搜索匹配的所有的pc_code
     *
     * @Token
     */
    public function getPcCodeListAction()
    {
        $validate = [
            'pc_code' => 'Required|StrLenGeLe:5,100',
        ];

        $param = $this->request->get();
        $param = array_only($param, array_keys($validate));

        Validation::validate($param, $validate);

        $data = AddService::getInstance()->get_pc_code_list($param['pc_code']);
        return $this->returnJson(ErrCode::$SUCCESS, 'ok', $data);
    }

    /**
     * 获取用户的可用备用金列表
     * @Permission(action='reimbursement.apply.apply')
     *
     * @return Response|ResponseInterface
     */
    public function getMyApplyMapAction()
    {
        // 申请人工号
        $apply_id = $this->request->get('apply_id');
        $user     = empty($apply_id) ? $this->user : ['id' => $apply_id];

        $res = ReserveFundApplyService::getInstance()->getMyApplyMap($user);
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }

    /**
     * 获取当前用户支援网点列表
     * @Permission(action='reimbursement.apply.apply')
     *
     * @api https://yapi.flashexpress.pub/project/133/interface/api/49993
     * @return Response|ResponseInterface
     * @throws ValidationException
     */
    public function getStoreSupportListAction()
    {
        $params = $this->request->get();
        Validation::validate($params, [
            'apply_id'   => 'Required|IntGe:1|>>>:' . $this->t->_('params_error', ['param' => 'apply_id']),
            'product_id' => 'Required|IntGe:1|>>>:' . $this->t->_('params_error', ['param' => 'product_id']),
        ]);

        $data = AddService::getInstance()->getStoreSupportList($params);
        return $this->returnJson(ErrCode::$SUCCESS, 'ok', $data);
    }


    /**
     * 支付列表
     * @Permission(action='reimbursement.pay.search')
     *
     * @return mixed
     * @throws Exception
     */
    public function payListAction()
    {
        $params = $this->request->get();

        $lock_key = md5(RedisKey::REIMBURSEMENT_PAY_LIST_LOCK_PREFIX . $this->user['id']);
        $res      = $this->atomicLock(function () use ($params) {
            return ListService::getInstance()->getList($params, $this->user, BaseService::LIST_TYPE_PAY);
        }, $lock_key, 20);

        if (isset($res['code'])) {
            $code    = $res['code'];
            $message = $res['message'];
            $data    = $res['data'];
        } else {
            $code    = ErrCode::$FREQUENT_VISIT_ERROR;
            $message = $this->t['sys_processing'];
            $data    = [];
        }

        return $this->returnJson($code, $message, $data);
    }

    /**
     * 支付详情
     * @Permission(action='reimbursement.pay.view')
     *
     * @return Response|ResponseInterface
     * @throws ValidationException
     */
    public function payDetailAction()
    {
        $data = $this->request->get();
        Validation::validate($data, BaseService::$validate_detail);

        $res = DetailService::getInstance()->getCommonDetail($data['id'], $this->user['id'],
            BaseService::LIST_TYPE_PAY);
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }

    /**
     * 支付下载
     * @Permission(action='reimbursement.pay.download')
     *
     * @return mixed
     * @throws ValidationException
     * @throws GuzzleException
     */
    public function payDownloadAction()
    {
        $data = $this->request->get();
        Validation::validate($data, BaseService::$validate_detail);

        $res = DetailService::getInstance()->download($data['id'], $this->user['id'], BaseService::LIST_TYPE_PAY);
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }

    /**
     * 获取税号信息
     *
     * @Token
     * @return Response|ResponseInterface
     * @throws ValidationException
     * @api https://yapi.flashexpress.pub/project/133/interface/api/75992
     */
    public function getInvoiceTaxNoAction()
    {
        $param = $this->request->get();
        Validation::validate($param, BaseService::$validate_tax_no);
        $res = AddService::getInstance()->getInvoiceTaxNo($param['tax_no']);
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }

    /**
     * 获取申请人的出差编号
     *
     * @Permission(menu='reimbursement')
     * @return mixed
     * @throws ValidationException
     *
     * @api https://yapi.flashexpress.pub/project/133/interface/api/88271
     *
     */
    public function getBusinessTripListAction()
    {
        $params = $this->request->get();

        $params['is_stay'] = $params['is_stay'] ?? 0;
        Validation::validate($params, [
            'apply_id' => 'Required|IntGt:0|>>>:' . $this->t->_('params_error', ['param' => 'apply_id']),
            'is_stay' => 'Required|IntGe:0|>>>:' . $this->t->_('params_error', ['param' => 'is_stay']),
        ]);

        $lock_key = md5(RedisKey::REIMBURSEMENT_GET_STAFF_TRIP_LIST_LOCK_PREFIX . $this->user['id']);
        $res = $this->atomicLock(function () use ($params) {
            return AddService::getInstance()->getBusinessTrip($params);
        }, $lock_key, 20);

        if ($res === false) {
            $code = ErrCode::$FREQUENT_VISIT_ERROR;
            $message = $this->t['sys_processing'];
            $data = [];
        } else {
            $code = ErrCode::$SUCCESS;
            $message = $this->t['success'];
            $data = $res;
        }

        return $this->returnJson($code, $message, $data);
    }

    /**
     * 报销新增 - 获取草稿
     * @Permission(action='reimbursement.apply.apply')
     *
     * @api https://yapi.flashexpress.pub/project/133/interface/api/87875
     * @return mixed
     * @throws ValidationException
     */
    public function getDraftAction()
    {
        $data = DetailService::getInstance()->getUserDraft($this->user);
        return $this->returnJson(ErrCode::$SUCCESS, $this->t['success'], $data);
    }

    /**
     * 获取明细行的支援列表
     * @Permission(menu='reimbursement')
     *
     * @api https://yapi.flashexpress.pub/project/133/interface/api/88967
     * @return Response|ResponseInterface
     * @throws ValidationException
     */
    public function getDetailStoreSupportListAction()
    {
        $params = $this->request->get();
        Validation::validate($params, [
            'reimbursement_id' => 'Required|IntGe:1|>>>:' . $this->t->_('params_error', ['param' => 'reimbursement_id']),
            'detail_id'        => 'Required|IntGe:1|>>>:' . $this->t->_('params_error', ['param' => 'detail_id']),
        ]);

        $data = DetailService::getInstance()->getDetailStoreSupportList($params);
        return $this->returnJson(ErrCode::$SUCCESS, 'ok', $data);
    }

    /**
     * 导出明细行的支援列表
     * @Permission(menu='reimbursement')
     *
     * @api https://yapi.flashexpress.pub/project/133/interface/api/88970
     * @return Response|ResponseInterface
     * @throws ValidationException
     */
    public function exportDetailStoreSupportListAction()
    {
        $params = $this->request->get();
        Validation::validate($params, [
            'reimbursement_id' => 'Required|IntGe:1|>>>:' . $this->t->_('params_error', ['param' => 'reimbursement_id']),
            'detail_id'        => 'Required|IntGe:1|>>>:' . $this->t->_('params_error', ['param' => 'detail_id']),
        ]);

        $lock_key = md5('export_detail_store_support_list_' . $this->user['id']);
        $res      = $this->atomicLock(function () use ($params) {
            return DetailService::getInstance()->exportDetailStoreSupportList($params);
        }, $lock_key, 10);

        if ($res !== false) {
            $code    = ErrCode::$SUCCESS;
            $message = $this->t->_('success');
            $data    = $res;
        } else {
            $code    = ErrCode::$FREQUENT_VISIT_ERROR;
            $message = $this->t['sys_processing'];
            $data    = '';
        }
        return $this->returnJson($code, $message, $data);
    }


    /**
     * 获取支援单信息
     * @Permission(menu='reimbursement')
     *
     * @api https://yapi.flashexpress.pub/project/133/interface/api/89132
     * @return Response|ResponseInterface
     * @throws ValidationException
     */
    public function getStoreSupportDetailAction()
    {
        $params = $this->request->get();
        Validation::validate($params, [
            'support_serial_no_item' => 'Required|ArrLenGe:1|>>>:' . $this->t->_('params_error', ['param' => 'support_serial_no_item']),
        ]);

        $data = DetailService::getInstance()->getStoreSupportNoDetail($params['support_serial_no_item']);
        return $this->returnJson(ErrCode::$SUCCESS, 'ok', $data);
    }

    /**
     * 获取出差-共同住宿人的出差编号
     *
     * @Permission(menu='reimbursement')
     * @return mixed
     * @throws ValidationException
     *
     * @api https://yapi.flashexpress.pub/project/133/interface/api/89363
     *
     */
    public function getTravelRoommateNoListAction()
    {
        $params = $this->request->get();

        Validation::validate($params, [
            'apply_id'         => 'Required|IntGt:0|>>>:' . $this->t->_('params_error', ['param' => 'apply_id']),
            'travel_serial_no' => 'Required|StrLenGe:1|>>>:' . $this->t->_('params_error', ['param' => 'travel_serial_no']),
        ]);

        $res = AddService::getInstance()->getApplyRoommateBusinessTrip($params);
        return $this->returnJson(ErrCode::$SUCCESS, $this->t['success'], $res);
    }

    /**
     * 获取明细行的出差共同住宿单列表
     * @Permission(menu='reimbursement')
     *
     * @api https://yapi.flashexpress.pub/project/133/interface/api/89501
     * @return Response|ResponseInterface
     * @throws ValidationException
     */
    public function getDetailTravelRoommateListAction()
    {
        $params = $this->request->get();
        Validation::validate($params, [
            'reimbursement_id' => 'Required|IntGe:1|>>>:' . $this->t->_('params_error', ['param' => 'reimbursement_id']),
            'detail_id'        => 'Required|IntGe:1|>>>:' . $this->t->_('params_error', ['param' => 'detail_id']),
        ]);

        $data = DetailService::getInstance()->getDetailTravelRoommateList($params);
        return $this->returnJson(ErrCode::$SUCCESS, 'ok', $data);
    }

    /**
     * 获取明细行的BY出差单信息
     * @Permission(menu='reimbursement')
     *
     * @api https://yapi.flashexpress.pub/project/133/interface/api/89504
     * @return Response|ResponseInterface
     * @throws ValidationException
     */
    public function getDetailTravelNoInfoAction()
    {
        $params = $this->request->get();
        Validation::validate($params, [
            'reimbursement_id' => 'Required|IntGe:1|>>>:' . $this->t->_('params_error', ['param' => 'reimbursement_id']),
            'detail_id'        => 'Required|IntGe:1|>>>:' . $this->t->_('params_error', ['param' => 'detail_id']),
        ]);

        $data = DetailService::getInstance()->getDetailTravelNoInfo($params, $this->user);
        return $this->returnJson(ErrCode::$SUCCESS, 'ok', $data);
    }

    /**
     * 计算总金额
     * @Permission(action='reimbursement.apply.apply')
     *
     * @api https://yapi.flashexpress.pub/project/133/interface/api/89576
     * @return Response|ResponseInterface
     * @throws ValidationException
     */
    public function calculateTotalAmountAction()
    {
        $params = $this->request->get();

        // 获取国家默认币种
        GlobalEnums::setCurrency();
        $default_currency = GlobalEnums::$sys_default_currency;

        Validation::validate($params, [
            'submit_type'                 => 'Required|IntIn:1,2|>>>:' . $this->t->_('params_error', ['param' => 'submit_type']),
            'expense'                     => 'Required|Arr|ArrLenGe:1|>>>:' . $this->t->_('params_error', ['param' => 'expense']),
            'expense[*].invoice_currency' => 'Required|IntGe:1|>>>:' . $this->t->_('params_error', ['param' => 'invoice_currency']),
            'expense[*].invoice_date'     => "Required|IfIntNe:invoice_currency,{$default_currency}|Date|>>>:" . $this->t->_('params_error', ['param' => 'invoice_date']),
            'expense[*].amount'           => 'Required|FloatGe:0|>>>:' . $this->t->_('params_error', ['param' => 'amount']),
            'expense[*].payable_amount'   => 'Required|FloatGe:0|>>>:' . $this->t->_('params_error', ['param' => 'payable_amount']),
        ]);

        $result = AddService::getInstance()->calculateTotalAmount($params, $default_currency);
        return $this->returnJson(ErrCode::$SUCCESS, $this->t['success'], $result);
    }

    /**
     * 境内机票金额校验
     * @Permission(action='reimbursement.apply.apply')
     *
     * @api https://yapi.flashexpress.pub/project/133/interface/api/89588
     * @return Response|ResponseInterface
     * @throws ValidationException
     */
    public function checkDomesticAirTicketAmountAction()
    {
        $params = $this->request->get();

        // 获取国家默认币种
        $default_currency = GlobalEnums::getSysDefaultCurrency();

        Validation::validate($params, [
            'submit_type'                                  => 'Required|IntIn:1,2|>>>:' . $this->t->_('params_error', ['param' => 'submit_type']),
            'apply_id'                                     => 'Required|StrLenGe:1|>>>:' . $this->t->_('params_error', ['param' => 'apply_id']),
            'travel_serial_no'                             => 'Required|StrLenGe:1|>>>:' . $this->t->_('params_error', ['param' => 'travel_serial_no']),
            'domestic_air_ticket_item'                     => 'Required|Arr|ArrLenGe:1|>>>:' . $this->t->_('params_error', ['param' => 'domestic_air_ticket_item']),
            'domestic_air_ticket_item[*].invoice_currency' => 'Required|IntGe:1|>>>:' . $this->t->_('params_error', ['param' => 'invoice_currency']),
            'domestic_air_ticket_item[*].invoice_date'     => "Required|IfIntNe:invoice_currency,{$default_currency}|Date|>>>:" . $this->t->_('params_error', ['param' => 'invoice_date']),
            'domestic_air_ticket_item[*].amount'           => 'Required|FloatGe:0|>>>:' . $this->t->_('params_error', ['param' => 'amount']),
        ]);

        $result = AddService::getInstance()->checkDomesticAirTicket($params, $default_currency);
        return $this->returnJson(ErrCode::$SUCCESS, $this->t['success'], $result);
    }

    /**
     * 出差住宿费金额校验
     * @Permission(action='reimbursement.apply.apply')
     *
     * @api https://yapi.flashexpress.pub/project/133/interface/api/89591
     * @return Response|ResponseInterface
     * @throws ValidationException
     */
    public function checkTripAccommodationAmountAction()
    {
        $params = $this->request->get();

        // 获取国家默认币种
        $default_currency = GlobalEnums::getSysDefaultCurrency();

        Validation::validate($params, [
            'submit_type'                                => 'Required|IntIn:1,2|>>>:' . $this->t->_('params_error', ['param' => 'submit_type']),
            'apply_id'                                   => 'Required|StrLenGe:1|>>>:' . $this->t->_('params_error', ['param' => 'apply_id']),
            'travel_serial_no'                           => 'Required|StrLenGe:1|>>>:' . $this->t->_('params_error', ['param' => 'travel_serial_no']),
            'accommodation_item'                         => 'Required|Arr|ArrLenGe:1|>>>:' . $this->t->_('params_error', ['param' => 'accommodation_item']),
            'accommodation_item[*].invoice_currency'     => 'Required|IntGe:1|>>>:' . $this->t->_('params_error', ['param' => 'invoice_currency']),
            'accommodation_item[*].invoice_date'         => "Required|IfIntNe:invoice_currency,{$default_currency}|Date|>>>:" . $this->t->_('params_error', ['param' => 'invoice_date']),
            'accommodation_item[*].amount'               => 'Required|FloatGe:0|>>>:' . $this->t->_('params_error', ['param' => 'amount']),
            'accommodation_item[*].travel_roommate_item' => 'Required|Arr|ArrLenGe:0|>>>:' . $this->t->_('params_error', ['param' => 'travel_roommate_item']),
        ]);

        $result = AddService::getInstance()->checkTripAccommodation($params, $default_currency);
        return $this->returnJson(ErrCode::$SUCCESS, $this->t['success'], $result);
    }

    /**
     * 租车费金额校验
     * @Permission(action='reimbursement.apply.apply')
     *
     * @api https://yapi.flashexpress.pub/project/133/interface/api/89594
     * @return Response|ResponseInterface
     * @throws ValidationException
     */
    public function checkCarRentalAmountAction()
    {
        $params = $this->request->get();

        // 获取国家默认币种
        $default_currency = GlobalEnums::getSysDefaultCurrency();

        Validation::validate($params, [
            'submit_type'                         => 'Required|IntIn:1,2|>>>:' . $this->t->_('params_error', ['param' => 'submit_type']),
            'apply_id'                            => 'Required|StrLenGe:1|>>>:' . $this->t->_('params_error', ['param' => 'apply_id']),
            'travel_serial_no'                    => 'Required|StrLenGe:1|>>>:' . $this->t->_('params_error', ['param' => 'travel_serial_no']),
            'car_rental_item'                     => 'Required|Arr|ArrLenGe:1|>>>:' . $this->t->_('params_error', ['param' => 'car_rental_item']),
            'car_rental_item[*].invoice_currency' => 'Required|IntGe:1|>>>:' . $this->t->_('params_error', ['param' => 'invoice_currency']),
            'car_rental_item[*].invoice_date'     => "Required|IfIntNe:invoice_currency,{$default_currency}|Date|>>>:" . $this->t->_('params_error', ['param' => 'invoice_date']),
            'car_rental_item[*].amount'           => 'Required|FloatGe:0|>>>:' . $this->t->_('params_error', ['param' => 'amount']),
        ]);

        $result = AddService::getInstance()->checkCarRental($params, $default_currency);
        return $this->returnJson(ErrCode::$SUCCESS, $this->t['success'], $result);
    }

    /**
     * 删除草稿
     * @Permission(action='reimbursement.apply.apply')
     *
     * @api https://yapi.flashexpress.pub/project/133/interface/api/89597
     * @return Response|ResponseInterface
     * @throws ValidationException
     */
    public function delDraftAction()
    {
        UpdateService::getInstance()->delUserDraft($this->user);
        return $this->returnJson(ErrCode::$SUCCESS, $this->t['success']);
    }

    /**
     * OCR读取车辆里程
     * @Permission(action='reimbursement.apply.apply')
     *
     * @api https://yapi.flashexpress.pub/project/133/interface/api/89813
     * @return Response|ResponseInterface
     * @throws ValidationException
     * @throws BusinessException
     */
    public function getMileageByOCRAction()
    {
        $params = $this->request->get();

        Validation::validate($params, [
            'object_url' => 'Required|Url|>>>:' . $this->t->_('params_error', ['param' => 'object_url']),
        ]);

        $mileage = AddService::getInstance()->getODOByOCR($params['object_url'], $this->user);
        return $this->returnJson(ErrCode::$SUCCESS, $this->t['success'], ['mileage' => $mileage]);
    }

    /**
     * 获取申请人身份证信息
     * @Permission(action='reimbursement.apply.apply')
     *
     * @api https://yapi.flashexpress.pub/project/133/interface/api/89891
     * @return Response|ResponseInterface
     * @throws ValidationException
     */
    public function getApplyIDInfoAction()
    {
        $params = $this->request->get();
        Validation::validate($params, [
            'apply_id' => 'Required|StrLenGe:1|>>>:' . $this->t->_('params_error', ['param' => 'apply_id']),
        ]);

        $data = AddService::getInstance()->getApplyIDInfo($params['apply_id']);
        return $this->returnJson(ErrCode::$SUCCESS, $this->t->_('success'), ['identity_file' => $data]);
    }

    /**
     * 申请人签字提交
     * @Permission(action='reimbursement.apply.apply')
     *
     * @api https://yapi.flashexpress.pub/project/133/interface/api/89894
     * @return Response|ResponseInterface
     * @throws ValidationException
     */
    public function saveApplySignatureInfoAction()
    {
        $params = $this->request->get();

        Validation::validate($params, [
            'no'             => 'Required|StrLenGe:1|>>>:' . $this->t->_('params_error', ['param' => 'no']),
            'identity_file'  => 'Required|Url|>>>:' . $this->t->_('params_error', ['param' => 'identity_file']),
            'signature_file' => 'Required|Url|>>>:' . $this->t->_('params_error', ['param' => 'signature_file']),
        ]);

        $params['signature_channel'] = ReimbursementEnums::OPERATE_CHANNEL_OA;

        $lock_key = md5('reimbursement_save_apply_aignature_info_' . $this->user['id']);
        $res      = $this->atomicLock(function () use ($params) {
            return AddService::getInstance()->submitApplySignatureInfo($params, $this->user);
        }, $lock_key, 30);

        if (isset($res['code'])) {
            $code    = $res['code'];
            $message = $res['message'];
        } else {
            $code    = ErrCode::$FREQUENT_VISIT_ERROR;
            $message = $this->t['sys_processing'];
        }

        return $this->returnJson($code, $message);
    }

    /**
     * 获取申请人签字二维码
     * @Permission(action='reimbursement.apply.apply')
     *
     * @api https://yapi.flashexpress.pub/project/133/interface/api/89918
     * @return Response|ResponseInterface
     * @throws ValidationException
     */
    public function getApplySignatureQRcodeAction()
    {
        $params = $this->request->get();

        Validation::validate($params, [
            'no'       => 'Required|StrLenGe:1|>>>:' . $this->t->_('params_error', ['param' => 'no']),
        ]);

        $lock_key = md5('reimbursement_get_signature_qrcode_' . $this->user['id']);
        $res      = $this->atomicLock(function () use ($params) {
            return AddService::getInstance()->generateSignatureQRcode($params, $this->user);
        }, $lock_key, 3);

        if (!empty($res['qrtoken_base64'])) {
            $code    = ErrCode::$SUCCESS;
            $message = $this->t['success'];
            $data    = $res;
        } else {
            $code    = ErrCode::$FREQUENT_VISIT_ERROR;
            $message = $this->t['sys_processing'];
            $data    = [];
        }

        return $this->returnJson($code, $message, $data);
    }

    /**
     * 申请人签字状态查询
     * @Permission(action='reimbursement.apply.apply')
     *
     * @api https://yapi.flashexpress.pub/project/133/interface/api/89921
     * @return Response|ResponseInterface
     * @throws ValidationException
     */
    public function getApplySignatureStatusAction()
    {
        $params = $this->request->get();

        Validation::validate($params, [
            'no' => 'Required|StrLenGe:1|>>>:' . $this->t->_('params_error', ['param' => 'no']),
        ]);

        $lock_key = md5('reimbursement_get_signature_status_' . $this->user['id']);
        $res      = $this->atomicLock(function () use ($params) {
            return AddService::getInstance()->getSignatureStatus($params, $this->user);
        }, $lock_key, 1);

        if (!empty($res)) {
            $code    = ErrCode::$SUCCESS;
            $message = $this->t['success'];
            $data    = $res;
        } else {
            $code    = ErrCode::$FREQUENT_VISIT_ERROR;
            $message = $this->t['sys_processing'];
            $data    = [];
        }

        return $this->returnJson($code, $message, $data);
    }

    /** 
     * 
     * 获取报销相关规则配置
     *
     * @Token
     * @api https://yapi.flashexpress.pub/project/133/interface/api/89954
     * @return mixed
     */
    public function getConfigAction()
    {
        $res = AddService::getInstance()->getConfig();
        return $this->returnJson(ErrCode::$SUCCESS, $this->t->_('success'), $res);
    }

    /**
     * 手动作废
     * @Permission(action='reimbursement.apply.apply')
     *
     * @api https://yapi.flashexpress.pub/project/133/interface/api/89972
     * @return Response|ResponseInterface
     * @throws ValidationException
     */
    public function manualInvalidAction()
    {
        $params = $this->request->get();

        Validation::validate($params, [
            'no' => 'Required|StrLenGe:1|>>>:' . $this->t->_('params_error', ['param' => 'no']),
        ]);

        $params['operate_channel'] = ReimbursementEnums::OPERATE_CHANNEL_OA;
        $lock_key                  = md5('reimbursement_manual_invalid_order_' . $this->user['id']);
        $res                       = $this->atomicLock(function () use ($params) {
            return AddService::getInstance()->invalidOrder($params, $this->user);
        }, $lock_key, 20);

        if (!empty($res)) {
            $code    = ErrCode::$SUCCESS;
            $message = $this->t['success'];
        } else {
            $code    = ErrCode::$FREQUENT_VISIT_ERROR;
            $message = $this->t['sys_processing'];
        }

        return $this->returnJson($code, $message);
    }

    /**
     * 手动提交
     * @Permission(action='reimbursement.apply.apply')
     *
     * @api https://yapi.flashexpress.pub/project/133/interface/api/89978
     * @return Response|ResponseInterface
     * @throws ValidationException
     */
    public function manualSubmitAction()
    {
        $params = $this->request->get();

        Validation::validate($params, [
            'no' => 'Required|StrLenGe:1|>>>:' . $this->t->_('params_error', ['param' => 'no']),
        ]);

        $lock_key                  = md5('reimbursement_manual_submit_order_' . $this->user['id']);
        $res                       = $this->atomicLock(function () use ($params) {
            return AddService::getInstance()->manualSubmit($params, $this->user);
        }, $lock_key, 20);

        if (isset($res['code'])) {
            $code    = $res['code'];
            $message = $res['message'];
        } else {
            $code    = ErrCode::$FREQUENT_VISIT_ERROR;
            $message = $this->t['sys_processing'];
        }

        return $this->returnJson($code, $message);
    }

    /**
     * 上传员工支援网点单号
     * @Permission(action='reimbursement.apply.apply')
     *
     * @api https://yapi.flashexpress.pub/project/133/interface/api/49993
     * @return Response|ResponseInterface
     * @throws ValidationException
     */
    public function uploadApplySupportOrderAction()
    {
        $params = $this->request->get();
        Validation::validate($params, [
            'apply_id'   => 'Required|IntGe:1|>>>:' . $this->t->_('params_error', ['param' => 'apply_id']),
            'product_id' => 'Required|IntGe:1|>>>:' . $this->t->_('params_error', ['param' => 'product_id']),
        ]);

        $lock_key = md5('reimbursement_upload_apply_support_order_' . $this->user['id']);
        $res      = $this->atomicLock(function () use ($params) {
            return AddService::getInstance()->uploadApplySupportOrder($params, $this->user);
        }, $lock_key, 15);

        if (isset($res['code'])) {
            $code    = $res['code'];
            $message = $res['message'];
            $data    = $res['data'];
        } else {
            $code    = ErrCode::$FREQUENT_VISIT_ERROR;
            $message = $this->t['sys_processing'];
            $data    = [];
        }

        return $this->returnJson($code, $message, $data);
    }

    /**
     * 获取明细行的支援列表V2-带分页版本
     * @Permission(menu='reimbursement')
     *
     * @api https://yapi.flashexpress.pub/project/133/interface/api/91775
     * @return Response|ResponseInterface
     * @throws ValidationException
     */
    public function getDetailStoreSupportListV2Action()
    {
        $params = $this->request->get();
        Validation::validate($params, [
            'reimbursement_id' => 'Required|IntGe:1|>>>:' . $this->t->_('params_error', ['param' => 'reimbursement_id']),
            'detail_id'        => 'Required|IntGe:1|>>>:' . $this->t->_('params_error', ['param' => 'detail_id']),
        ]);

        $data = DetailService::getInstance()->getDetailStoreSupportListV2($params);
        return $this->returnJson($data['code'], $data['message'], $data['data']);
    }

    /**
     * 报销详情-导出报销实质关联的支援单V2(异步)
     * @Permission(menu='reimbursement')
     *
     * @api https://yapi.flashexpress.pub/project/133/interface/api/91802
     * @return mixed
     * @throws ValidationException
     */
    public function exportDetailStoreSupportListV2Action()
    {
        $params = $this->request->get();
        Validation::validate($params, [
            'reimbursement_id' => 'Required|IntGe:1|>>>:' . $this->t->_('params_error', ['param' => 'reimbursement_id']),
            'detail_id'        => 'Required|IntGe:1|>>>:' . $this->t->_('params_error', ['param' => 'detail_id']),
        ]);

        // 加锁处理
        $lock_key = md5(RedisKey::REIMBURSEMENT_EXPORT_DETAIL_STORE_SUPPORT_V2_PREFIX . $this->user['id']);
        $res      = $this->atomicLock(function () use ($params) {
            $result = DownloadCenterService::getInstance()->addDownloadCenter($this->user['id'], DownloadCenterEnum::FINANCE_REIMBURSEMENT_DETAIL_STORE_SUPPORT_V2, $params);
            $result['data'] = [
                'export_method' => DownloadCenterEnum::EXPORT_METHOD_ASYNC,
                'file_url'      => '',
            ];

            return $result;
        }, $lock_key, 5);
        if (isset($res['code'])) {
            $code    = $res['code'];
            $message = $res['message'];
            $data    = $res['data'];
        } else {
            $code    = ErrCode::$FREQUENT_VISIT_ERROR;
            $message = $this->t['sys_processing'];
            $data    = [];
        }

        return $this->returnJson($code, $message, $data);
    }

    /**
     * 获取支援单关联的报销实质列表
     * @Permission(menu='reimbursement')
     *
     * @api https://yapi.flashexpress.pub/project/133/interface/api/91805
     * @return Response|ResponseInterface
     * @throws ValidationException
     */
    public function getSupportRelatedDetailListAction()
    {
        $params = trim_array($this->request->get());
        Validation::validate($params, [
            'support_serial_no' => 'Required|StrLenGe:1|>>>:' . $this->t->_('params_error', ['param' => 'support_serial_no']),
        ]);

        $data = DetailService::getInstance()->getSupportRelatedDetailList($params['support_serial_no']);
        return $this->returnJson(ErrCode::$SUCCESS, $this->t->_('success'), $data);
    }

    /**
     * 导出支援单关联的报销实质
     * @Permission(menu='reimbursement')
     *
     * @api https://yapi.flashexpress.pub/project/133/interface/api/91808
     * @return Response|ResponseInterface
     * @throws ValidationException
     */
    public function exportSupportRelatedDetailListAction()
    {
        $params = trim_array($this->request->get());
        Validation::validate($params, [
            'support_serial_no' => 'Required|StrLenGe:1|>>>:' . $this->t->_('params_error', ['param' => 'support_serial_no']),
        ]);

        $lock_key = md5(RedisKey::REIMBURSEMENT_EXPORT_SUPPORT_RELATED_DETAIL_PREFIX . $this->user['id']);
        $res      = $this->atomicLock(function () use ($params) {
            return DetailService::getInstance()->exportSupportRelatedDetailList($params['support_serial_no']);
        }, $lock_key, 5);
        if (!empty($res)) {
            $code    = ErrCode::$SUCCESS;
            $message = $this->t->_('success');
            $data    = $res;
        } else {
            $code    = ErrCode::$FREQUENT_VISIT_ERROR;
            $message = $this->t['sys_processing'];
            $data    = [];
        }

        return $this->returnJson($code, $message, $data);
    }

}

