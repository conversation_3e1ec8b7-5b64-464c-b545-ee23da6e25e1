<?php

namespace App\Modules\Reimbursement\Services;

use App\Library\ApiClient;
use App\Library\Enums;
use App\Library\Enums\ByWorkflowEnums;
use App\Library\Enums\GlobalEnums;
use App\Library\ErrCode;
use App\Library\Exception\BusinessException;
use App\Library\RedisClient;
use App\Library\Validation\Validation;
use App\Library\Validation\ValidationException;
use App\Models\backyard\SysCityModel;
use App\Models\backyard\SysProvinceModel;
use App\Models\oa\ReimbursementDetailSupportRelModel;
use App\Models\oa\ReimbursementDetailTravelRoommateRelModel;
use App\Models\oa\ReimbursementMsgLogModel;
use App\Models\oa\SysAttachmentModel;
use App\Modules\Budget\Models\BudgetObjectProduct;
use App\Modules\Budget\Services\BudgetService;
use App\Modules\Common\Models\EnvModel;
use App\Modules\Common\Services\CommonService;
use App\Modules\Common\Services\EnumsService;
use App\Modules\Common\Services\StaffLangService;
use App\Modules\Loan\Services\AddService as LoanAddService;
use App\Modules\Organization\Models\SysStoreModel;
use App\Modules\Reimbursement\Models\BankListModel;
use App\Modules\Reimbursement\Models\Detail;
use App\Modules\Reimbursement\Models\HrStaffApplySupportStore;
use App\Modules\Reimbursement\Models\Pccode;
use App\Modules\Reimbursement\Models\Reimbursement;
use App\Modules\User\Models\DepartmentModel;
use App\Modules\User\Models\HrStaffItemsModel;
use App\Modules\User\Models\TripModel;
use App\Modules\User\Services\UserService;
use App\Repository\backyard\BusinessTripRepository;
use App\Repository\backyard\HrJobDepartmentRelationRepository;
use App\Repository\backyard\HrStaffApplySupportStoreRepository;
use App\Repository\backyard\PieceRepository;
use App\Repository\backyard\RegionRepository;
use App\Repository\backyard\SysCityRepository;
use App\Repository\backyard\SysDistrictRepository;
use App\Repository\backyard\SysProvinceRepository;
use App\Repository\HrJobTitleRepository;
use App\Repository\oa\ReimbursementCarRentalQuotaRepository;
use App\Repository\oa\ReimbursementDomesticAccommodationAreaRepository;
use App\Repository\oa\ReimbursementDomesticAccommodationQuotaRepository;
use App\Repository\oa\ReimbursementDomesticAirTicketQuotaRepository;
use App\Repository\oa\ReimbursementFuelQuotaRepository;
use App\Repository\oa\ReimbursementOverseasAccommodationAreaRepository;
use App\Repository\oa\ReimbursementOverseasAccommodationQuotaRepository;
use App\Repository\oa\ReimbursementTripTimeLimitRepository;
use App\Repository\oa\SysExchangeRateRepository;
use App\Repository\oa\ReimbursementBankChargesAreaQuotaRepository;
use App\Repository\oa\ReimbursementBankChargesStoreQuotaRepository;
use App\Repository\StoreRepository;
use App\Util\RedisExpire;
use App\Util\RedisKey;
use App\Modules\Purchase\Services\BaseService as PurchaseService;
use App\Repository\HrStaffRepository;
use App\Library\Enums\StaffInfoEnums;
use App\Library\Enums\ReimbursementEnums;
use App\Library\Enums\KingDeeEnums;

class BaseService extends \App\Library\BaseService
{
    const LIST_TYPE_APPLY = 1;
    const LIST_TYPE_AUDIT = 2;           //审核列表
    const LIST_TYPE_FYR = 3;             //意见征询回复列表
    const LIST_TYPE_QUERY = 4;           //数据查询
    const LIST_TYPE_CONSULTED_REPLY = 5; //征询回复
    const LIST_TYPE_QUERY_EXPORT = 6;    // 查询导出
    const LIST_TYPE_PAY = 7;             // 支付列表

    //数据来源  1 oa  2 by
    const LIST_SOURCE_TYPE_1 = 1;
    const LIST_SOURCE_TYPE_2 = 2;

    // 同步导出最多数量
    const SYNC_EXPORT_MAX_COUNT = 10000;

    const NOTREQUIRED_LONG_TEXT_LEN = 'StrLenGeLe:0,1000';
    const REQUIRED_LONG_TEXT_LEN = 'Required|StrLenGeLe:1,1000';

    public static $validate_detail = [
        'id' => 'Required|IntGe:1'
    ];
    public static $validate_audit  = [
        'id'   => 'Required|IntGe:1',
        'flag' => 'Required|IntIn:0,1',
        'note' => 'IfIntEq:flag,1|' . self::REQUIRED_LONG_TEXT_LEN,
    ];

    public static $validate_audit_expense = [
        'expense[*].voucher_description' => 'StrLenGeLe:0,40',
        'expense'                        => 'Required|Arr|ArrLenGe:1',
        'expense[*]'                     => 'Required|Obj',
    ];


    public static $validate_department = [
        'department_id' => 'Required|IntGe:1'
    ];

    public static $validate_pay = [
        'id'               => 'Required|IntGe:1',
        'pay_status'       => 'Required|IntIn:2,3',
        //'loan_amount' => 'IfIntEq:pay_status,2|Required|FloatGe:0',
        //'other_amount' => 'IfIntEq:pay_status,2|Required|FloatGe:0',
        //'real_amount' => 'IfIntEq:pay_status,2|Required|FloatGe:0',
        'pay_bank_id'      => 'IfIntEq:pay_status,2|Required',
        'pay_bank_name'    => 'IfIntEq:pay_status,2|Required',
        'pay_bank_account' => 'IfIntEq:pay_status,2|Required',
        'pay_at'           => 'IfIntEq:pay_status,2|Required',
        'remark'           => 'IfIntEq:pay_status,3|' . self::REQUIRED_LONG_TEXT_LEN
    ];

    public static $validate_user = [
        'id' => 'Required|IntGe:1',
    ];

    public static $validate_budget = [
        'cost_department' => 'Required|IntGe:1',
        'cost_store_type' => 'Required|IntIn:1,2',
    ];


    public static $validate_limit = [
        'page'     => 'Required|Int',
        'pageSize' => 'Required|Int',
    ];

    public static $validate_serial_no = [
        'serial_no' => 'Required|Str'
    ];

    public static $validate_supplement = [
        'id'                       => 'Required|IntGe:1',
        'required_supplement_file' => 'Required|Arr'
    ];

    /**
     * 申请单校验
     *
     * @var string[]
     */
    public static $validate_param  = [
        'no'                             => 'Required|StrLenGeLe:10,20',
        'created_id'                     => 'Required',
        'created_name'                   => 'Required',
        'created_department_id'          => 'Required',
        'apply_id'                       => 'Required|IntGe:1|>>>:params error[apply_id]',
        'is_submit'                      => 'Required|IntIn:0,1',
        'apply_name'                     => 'Required',
        'apply_department_id'            => 'Required',
        'currency'                       => 'Required|IntIn:' . GlobalEnums::VALIDATE_CURRENCY_PARAMS,
        'bank_name'                      => 'Required',
        'bank_account'                   => 'Required',
        'bank_type'                      => 'Required',
        'cost_company_id'                => 'Required|IntGt:0',
        'cost_company_name'              => 'Required',
        'invoice_header_id'              => 'Required|IntGt:0|>>>:params error[invoice_header_id]',// 发票抬头必选
        'cost_department'                => 'Required|IntGt:0',
        'cost_department_name'           => 'Required',
        'cost_store'                     => 'Required',
        'cost_store_type'                => 'Required|IntIn:1,2',
        'cost_store_name'                => 'Required',
        //'cost_center_code' => 'Required',          //费用所属中心，v8112去掉了
        'country_code'                   => 'StrLenGeLe:0,20',
        'rfano'                          => 'StrLenGeLe:0,20', //备用金no
        'business_type'                  => 'IntIn:1,2',       //业务类型
        'account_type'                   => 'Required|IntIn:' . KingDeeEnums::ACCOUNT_TYPE_FLASH_EXPRESS_COMPANY . ',' . KingDeeEnums::ACCOUNT_TYPE_SUB_COMPANY,// 科目类型：1快递公司科目，2子公司科目


        // 报销实质
        'expense'                        => 'Required|Arr|ArrLenGe:1',
        'expense[*]'                     => 'Required|Obj',
        'expense[*].wht_type'            => 'Required|IntGe:0',
        'expense[*].wht_tax'             => 'Required',
        'expense[*].wht_tax_amount'      => 'Required',
        'expense[*].category_a'          => 'Required|IntEq:0',
        'expense[*].category_b'          => 'Required|IntEq:0',
        'expense[*].budget_id'           => 'Required|IntGt:0',
        'expense[*].product_id'          => 'IntGe:0',
        'expense[*].ledger_account_id'   => 'IntGe:0', //核算科目
        'expense[*].product_name'        => 'StrLenGeLe:0,60',
        'expense[*].start_at'            => 'Required|Date',       //费用发生开始时间
        'expense[*].end_at'              => 'Required|Date',       //费用发生结束时间
        'expense[*].cost_store_n_id'     => 'StrLenGeLe:0,255',    //费用所属网点名字，非总部的时候必填
        'expense[*].cost_store_n_name'   => 'StrLenGeLe:0,255',    //费用所属网点名字，非总部的时候必填
        'expense[*].cost_center_code'    => 'Required',            //费用所属中心
        'expense[*].info'                => 'Required|StrLenGeLe:1,1000',
        'expense[*].tax'                 => 'Required|FloatGe:0',
        'expense[*].tax_not'             => 'Required|FloatGt:0',
        'expense[*].invoice_no'          => 'StrLenGeLe:0,100', //增值税发票号
        'expense[*].travel_id'           => 'IfIntEq:category_a,1|Required|IntGt:0',
        'expense[*].fuel_start'          => 'IfIntEq:category_b,' . Enums::REIMBURSEMENT_EXPENSE_FUEL . '|Required',
        'expense[*].fuel_end'            => 'IfIntEq:category_b,' . Enums::REIMBURSEMENT_EXPENSE_FUEL . '|Required',
        'expense[*].fuel_mileage'        => 'IfIntEq:category_b,' . Enums::REIMBURSEMENT_EXPENSE_FUEL . '|Required',

        //报销详细=饮水费，水电费（category_b=25 26）,费用网点id,费用网点名字
        'expense[*].cost_store_id'       => 'IfIntIn:category_b,' . Enums::REIMBURSEMENT_EXPENSE_WATER_AND_ELE . ',' . Enums::REIMBURSEMENT_EXPENSE_DRINKING_WATER . '|Required',
        'expense[*].cost_store_name'     => 'IfIntIn:category_b,' . Enums::REIMBURSEMENT_EXPENSE_WATER_AND_ELE . ',' . Enums::REIMBURSEMENT_EXPENSE_DRINKING_WATER . '|Required',

        // 新增发票税务号、企业名称
        'expense[*].invoice_tax_no'      => 'StrLenGeLe:0,30',
        'expense[*].enterprise_name'     => 'StrLenGeLe:0,200',

        //报销凭证
        'expense[*].voucher_description' => 'StrLenGeLe:0,40',
        'expense[*].attachments'         => 'Arr',
        'expense[*].payment_method'      => 'Required',
        'expense[*].payment_voucher'     => 'Required|Arr',

        'loan_id' => 'IntGe:0|>>>:params error[loan_id]',         //改成只能关联一个
    ];
    public static $validate_tax_no = [
        'tax_no' => 'Required|Str'
    ];

    public static function handleParams($params, $not_must)
    {
        if (empty($not_must) || !is_array($not_must)) {
            return $params;
        }
        foreach ($not_must as $value) {
            if (isset($params[$value]) && empty($params[$value])) {
                unset($params[$value]);
            }
        }
        return $params;
    }

    /**
     * 获取额外的参数组信息
     *
     * @param array $validate_param 验证参数组
     * @param int $submit_type
     * @param array $params 请求参数组
     * @return mixed
     */
    public static function getExtendValidation(array $validate_param, int $submit_type, array $params)
    {
        // 通用校验规则
        $country_code = get_country_code();

        //V22269 报销实质 - 网点/总部必填拦截
        if (!empty($params['cost_store_type'])) {
            //1 网点 2 总部
            if ($params['cost_store_type'] == 1) {
                $validate_param['expense[*].cost_store_n_id']   = 'Required|StrLenGeLe:1,255';
                $validate_param['expense[*].cost_store_n_name'] = 'Required|StrLenGeLe:1,255';
            } elseif ($params['cost_store_type'] == 2) {
                $validate_param['expense[*].cost_department']      = 'Required|IntGt:0';
                $validate_param['expense[*].cost_department_name'] = 'Required|StrLenGeLe:1,255';
            }
        }

        // 支援费用相关校验
        $support_budget_id = static::getSupportTypeBudgetId();
        if (!empty($support_budget_id)) {
            //存在设置的信息
            $validate_param['expense[*].support_serial_no'] = 'IfIntEq:budget_id,' . $support_budget_id . '|Required|StrLenGe:1';

            if ($country_code == GlobalEnums::TH_COUNTRY_CODE) {
                $validate_param['expense[*].support_job_title_id']                = 'IfIntEq:budget_id,' . $support_budget_id . '|Required|IntGt:0';
                $validate_param['expense[*].support_job_title_name']              = 'IfIntEq:budget_id,' . $support_budget_id . '|Required|StrLenGeLe:1,255';
                $validate_param['expense[*].support_store_id']                    = 'IfIntEq:budget_id,' . $support_budget_id . '|Required|StrLenGeLe:1,10';
                $validate_param['expense[*].support_store_name']                  = 'IfIntEq:budget_id,' . $support_budget_id . '|Required|StrLenGeLe:1,50';
                $validate_param['expense[*].support_employment_begin_date']       = 'IfIntEq:budget_id,' . $support_budget_id . '|Required|Date';
                $validate_param['expense[*].support_employment_end_date']         = 'IfIntEq:budget_id,' . $support_budget_id . '|Required|Date';
                $validate_param['expense[*].support_employment_days']             = 'IfIntEq:budget_id,' . $support_budget_id . '|Required|IntGt:0';
                $validate_param['expense[*].support_is_followed_policy']          = 'IfIntEq:budget_id,' . $support_budget_id . '|Required';
                $validate_param['expense[*].support_not_followed_policy_reasons'] = 'IfIntEq:budget_id,' . $support_budget_id . '|Required|StrLenGeLe:0,1000';
            }
        }

        $travel_budget_id                              = static::getTravelTypeBudgetId();
        $validate_param['expense[*].travel_serial_no'] = 'IfIntEq:budget_id,' . $travel_budget_id . '|Required';
        $validate_param['expense[*].travel_start_at']  = 'IfIntEq:budget_id,' . $travel_budget_id . '|Required';
        $validate_param['expense[*].travel_end_at']    = 'IfIntEq:budget_id,' . $travel_budget_id . '|Required';
        $validate_param['expense[*].travel_start']     = 'IfIntEq:budget_id,' . $travel_budget_id . '|Required';
        $validate_param['expense[*].travel_end']       = 'IfIntEq:budget_id,' . $travel_budget_id . '|Required';

        // 增值税发票号
        $validate_param['expense[*].invoice_no'] = 'StrLenGeLe:0,100|>>>:' . static::$t->_('vat_invoice_params_error');

        // 新增提交, 验证发票日期和发票币种
        if ($country_code == GlobalEnums::TH_COUNTRY_CODE) {
            if ($submit_type == ReimbursementEnums::CREATE_ACTION_ADD_SUBMIT) {
                $default_currency = GlobalEnums::getSysDefaultCurrency();
                $validate_param['expense[*].invoice_currency'] = 'Required|IntGe:1|>>>:' . static::$t->_('params_error', ['param' => 'invoice_currency']);
                $validate_param['expense[*].invoice_date'] = "Required|IfIntNe:invoice_currency,{$default_currency}|Date|>>>:" . static::$t->_('params_error', ['param' => 'invoice_date']);
            }
        }

        // 发票税务号 基础长度校验
        $invoice_tax_no_validation = 'StrLenGeLe:0,30';
        // 是否启用发票税务号校验
        $required_tax_no = EnumsService::getInstance()->getSettingEnvValue('sys_module_reimbursement_validation_tax_no', '0');
        if ($required_tax_no == '1') {
            if ($country_code == GlobalEnums::PH_COUNTRY_CODE) {
                $invoice_tax_no_validation = 'StrLenGeLe:0,17|Regexp:/^[0-9-]*$/';
            } elseif ($country_code == GlobalEnums::ID_COUNTRY_CODE) {
                $invoice_tax_no_validation = 'StrLenGeLe:0,20|Regexp:/^[0-9-.]*$/';
            }
        }

        // 菲律宾的必填校验
        if ($country_code == GlobalEnums::PH_COUNTRY_CODE) {
            //获取校验必填的发票类型(必填项:增值税发票号、发票税务号、企业名称) 逗号拼接的字符串
            $required_tax_no = EnumsService::getInstance()->getSettingEnvValue('sys_module_reimbursement_invoice_type_required');
            //获取报销配置的发票类型枚举值
            $reimbursement_invoice_type_enums = EnumsService::getInstance()->getSettingEnvValueMap('sys_module_reimbursement_invoice_type_enums');
            // 发票类型
            $invoice_type_enums                        = implode(',', array_keys($reimbursement_invoice_type_enums));
            $validate_param['expense[*].invoice_type'] = "Required|IntIn:{$invoice_type_enums}|>>>:" . static::$t->_('financial_invoice_type_params_error');
            // 公司地址
            $validate_param['expense[*].company_addr'] = 'Required|StrLenGeLe:1,100';
            //发票类型属于[校验必填的发票类型(必填项:增值税发票号、发票税务号、企业名称)]
            if (!empty($required_tax_no)) {
                $validate_param['expense[*].invoice_no']      = 'IfIntIn:invoice_type,' . $required_tax_no . '|Required|StrLenGeLe:1,100' . '|>>>:' . static::$t->_('vat_invoice_params_error');
                $invoice_tax_no_validation                    = 'IfIntIn:invoice_type,' . $required_tax_no . '|Required|StrLenGe:1|' . $invoice_tax_no_validation;
                $validate_param['expense[*].enterprise_name'] = 'IfIntIn:invoice_type,' . $required_tax_no . '|Required|StrLenGeLe:1,200';
            }
        } else {
            $validate_param['expense[*].company_addr'] = 'StrLenGeLe:0,100';
        }

        // 印尼的必填校验
        if ($country_code == GlobalEnums::ID_COUNTRY_CODE) {
            $validate_param['expense[*].is_with_vat_invoice'] = 'Required|IntIn:' . implode(',', GlobalEnums::$is_with_vat_invoice_item) . '|>>>:' . static::$t->_('is_with_vat_invoice_params_error');
            $validate_param['expense[*].invoice_no']          = 'IfIntEq:is_with_vat_invoice,' . GlobalEnums::IS_WITH_VAT_INVOICE_YES . '|Required|StrLenGeLe:1,100' . '|>>>:' . static::$t->_('vat_invoice_params_error');
        }
        // 发票税务号经过上边的处理后,赋值给验证器
        $validate_param['expense[*].invoice_tax_no'] = $invoice_tax_no_validation;

        // 马来: 支付方式必填
        if ($country_code == GlobalEnums::MY_COUNTRY_CODE) {
            $payment_method                              = implode(',', array_keys(ReimbursementEnums::$payment_method_item));
            $validate_param['expense[*].payment_method'] = 'Required|IntIn:' . $payment_method . '|>>>:' . static::$t->_('params_error', ['param' => 'payment_method']);
        }

        return $validate_param;
    }

    /**
     * 换算明细行金额
     * @param $expense_item
     * @param $submit_type
     * @return mixed
     * @throws ValidationException
     */
    private function conversionExpenseAmount($expense_item, $submit_type)
    {
        if (get_country_code() != GlobalEnums::TH_COUNTRY_CODE) {
            return $expense_item;
        }

        if ($submit_type != ReimbursementEnums::CREATE_ACTION_ADD_SUBMIT) {
            return $expense_item;
        }

        $basic_currency = GlobalEnums::getSysDefaultCurrency();

        foreach ($expense_item as &$item) {
            $original_amount_field_data = [
                'invoice_currency' => $item['invoice_currency'],
                'invoice_date'     => $item['invoice_date'],
            ];

            $converted_amount_field_data = [
                'basic_currency' => $basic_currency,
                'exchange_rate'  => 1,
            ];

            $exchange_rate = 1;
            foreach (ReimbursementEnums::$expense_amount_fields as $amount_field) {
                $converted_amount = $original_amount = $item[$amount_field] ?? '0.00';

                if (!empty($original_amount) && $item['invoice_currency'] != $basic_currency) {
                    $converted_res    = $this->conversionAmountByExchangeRate($item['invoice_currency'], $item['invoice_date'], $original_amount);
                    $converted_amount = $converted_res['amount'];
                    $exchange_rate    = $converted_res['exchange_rate'];
                }

                $item[$amount_field]                        = $converted_amount;
                $original_amount_field_data[$amount_field]  = $original_amount;
                $converted_amount_field_data[$amount_field] = $converted_amount;
            }

            $item['invoice_currency'] = $basic_currency;

            $original_amount_field_data['exchange_rate'] = $exchange_rate;
            $item['amount_field_reference_data']         = [
                'original_data'  => $original_amount_field_data,
                'converted_data' => $converted_amount_field_data,
            ];
        }

        return $expense_item;
    }

    /**
     * 报销新增/重新提交-参数验证通过后校验其他额外关联项信息验证
     *
     * @param array $params 请求参数组
     * @param int $submit_type
     * @return mixed
     * @throws ValidationException
     */
    public function validationOther(array $params, int $submit_type)
    {
        // 是否使用备用金, 若前端未传: 默认否
        $params['petty_used'] = empty($params['petty_used']) ? ReimbursementEnums::PETTY_USED_NO : ReimbursementEnums::PETTY_USED_YES;
        // 选择使用备用金, 备用金单号须必选, 否则阻塞 并 给出相应提示语
        if ($params['petty_used'] == ReimbursementEnums::PETTY_USED_YES && empty($params['rfano'])) {
            throw new ValidationException(static::$t->_('reimbursement_submit_error_2'), ErrCode::$VALIDATE_ERROR);
        }

        // 报销实质: vat/sst、wht、可抵扣vat/sst税 严格校验
        $vat_config        = EnumsService::getInstance()->getVatRateValueItem();
        $wht_config        = EnumsService::getInstance()->getWhtRateMap();
        $deductible_config = EnumsService::getInstance()->getDeductibleRateValueItem();
        //获取vat/sst税名称
        $vat_sst_name = EnumsService::getInstance()->getVatSStRateName();
        //sap公司
        $sap_company_ids = EnumsService::getInstance()->getSettingEnvValueIds('purchase_sap_company_ids');
        //金蝶公司
        $kingdee_company_ids = EnumsService::getInstance()->getSettingEnvValueIds(KingDeeEnums::KINGDEE_BU_SETTING_CODE);
        //获取税额允差值
        $finance_tax_allowance_val   = EnumsService::getInstance()->getSettingEnvValue('finance_tax_allowance', 0);
        $finance_tax_allowance_lines = [];

        // 明细行的金额字段转换
        $params['expense'] = $this->conversionExpenseAmount($params['expense'], $submit_type);

        $is_add_type = in_array($submit_type, [ReimbursementEnums::CREATE_ACTION_ADD_SUBMIT, ReimbursementEnums::CREATE_ACTION_RESUBMIT]);

        $country_code = get_country_code();

        // 可选的出差单号
        $all_business_trip_list         = [];// 所有单号
        $business_trip_lodging_fee_list = [];//住宿费单号

        // 本次提交的报销科目ID
        $submit_budget_ids = array_filter(array_column($params['expense'], 'budget_id'));

        // 差旅科目ID
        $travel_budget_id = static::getTravelTypeBudgetId();

        // 交通费科目ID
        $transportation_budget_id = static::getTransportationTypeBudgetId();

        // 银行手续费科目id
        $bank_charges_budget_ids = [];

        // 明细类型配置
        $domestic_air_ticket_ids = [];
        $car_rental_ids = [];
        $accommodation_ids = [];
        $fule_ids = [];

        // 获取报销明细是否有住宿费的
        if ($country_code == GlobalEnums::TH_COUNTRY_CODE) {
            $bank_charges_budget_ids = static::getBankChargesBudgetIds();

            $fule_ids = static::getDetailTypeItem(ReimbursementEnums::SPECIAL_EXPENSE_DETAILS_TYPE_FUEL_COST);

            if (in_array($travel_budget_id, $submit_budget_ids)) {
                $domestic_air_ticket_ids = static::getDetailTypeItem(ReimbursementEnums::SPECIAL_EXPENSE_DETAILS_TYPE_DOMESTIC_AIR_TICKETS);
                $car_rental_ids          = static::getDetailTypeItem(ReimbursementEnums::SPECIAL_EXPENSE_DETAILS_TYPE_CAR_RENTAL_FEE);
                $accommodation_ids       = static::getDetailTypeItem(ReimbursementEnums::SPECIAL_EXPENSE_DETAILS_TYPE_ACCOMMODATION_FEES);

                $trip_params = [
                    'apply_id'    => $params['apply_id'],
                    'order_no'    => $params['no'],
                    'submit_type' => $submit_type,
                    'is_stay'     => 0,
                ];
                
                // 所有单号
                $all_business_trip_list = $this->getBusinessTrip($trip_params);
                $all_business_trip_list = array_column($all_business_trip_list, 'travel_serial_no');

                // 住宿费单号
                $trip_params['is_stay'] = 1;
                $business_trip_lodging_fee_list = $this->getBusinessTrip($trip_params);
                $business_trip_lodging_fee_list = array_column($business_trip_lodging_fee_list, 'travel_serial_no');
            }
        }

        // 住宿费的附件必填校验
        $lodging_budget_limit_config = EnumsService::getInstance()->getSettingEnvValueMap('reimbursement_add_about_lodging_budget_limit_config');
        $apply_lodging_is_limit      = !empty($lodging_budget_limit_config) && in_array($params['sys_department_id'],
                $lodging_budget_limit_config['first_level_department_ids']) && in_array($params['apply_job_title_id'],
                $lodging_budget_limit_config['job_title_ids']);

        // 检测支援费用-支援编号是否已被占用
        $support_budget_id = static::getSupportTypeBudgetId();

        $all_submit_support_no_list = [];
        if (in_array($support_budget_id, $submit_budget_ids) && in_array($country_code, [GlobalEnums::TH_COUNTRY_CODE, GlobalEnums::PH_COUNTRY_CODE])) {
            $all_submit_support_no_list = implode(',', array_column($params['expense'], 'support_serial_no'));
            $all_submit_support_no_list = array_filter(array_unique(explode(',', $all_submit_support_no_list)));
        }

        $travel_serial_no_map = [];
        $domestic_air_ticket_list = [];
        $car_rental_list = [];
        $accommodation_list = [];
        // 银行手续费的实质
        $bank_charges_expense = [];
        foreach ($params['expense'] as $key => &$item) {
            if ($item['deductible_vat_tax'] != '' && !in_array($item['deductible_vat_tax'], $deductible_config)) {
                throw new ValidationException(static::$t->_('deductable_rate_error_hint', ['VAT_SST' => $vat_sst_name]), ErrCode::$VALIDATE_ERROR);
            }

            if (!in_array($item['rate'], $vat_config)) {
                throw new ValidationException(static::$t->_('vat_sst_rate_error_hint', ['VAT_SST' => $vat_sst_name]), ErrCode::$VALIDATE_ERROR);
            }

            $_wht_info = $wht_config[$item['wht_type']] ?? [];
            if (empty($_wht_info)) {
                throw new ValidationException(static::$t->_('wht_type_error_hint'), ErrCode::$VALIDATE_ERROR);
            }

            if (empty($_wht_info['rate_list'][$item['wht_tax']])) {
                throw new ValidationException(static::$t->_('wht_rate_error_hint'), ErrCode::$VALIDATE_ERROR);
            }
            //17764需求当费用所属公司属于SAP同步公司或者属于同步金蝶BU时费用所属中心必填
            if (empty($item['cost_center_code']) && (in_array($params['cost_company_id'], $sap_company_ids) || in_array($params['cost_company_id'], $kingdee_company_ids))) {
                throw new ValidationException(static::$t->_('cost_center_code_is_must'), ErrCode::$VALIDATE_ERROR);
            }

            // 差旅费用
            $item['travel_reimbursement_overdue_remark'] = $item['travel_reimbursement_overdue_remark'] ?? '';

            // 住宿费-共同住宿
            $item['travel_is_have_roommate'] = $item['travel_is_have_roommate'] ?? 0;
            if ($item['travel_is_have_roommate'] == ReimbursementEnums::TRAVEL_IS_HAVE_ROOMMATE_YES) {
                if (empty($item['travel_roommate_item'])) {
                    throw new ValidationException(static::$t->_('reimbursement_save_error_011', ['index_no' => $key + 1]), ErrCode::$VALIDATE_ERROR);
                }

                if (count($item['travel_roommate_item']) > 10) {
                    throw new ValidationException(static::$t->_('reimbursement_save_error_012', ['max_total' => 10, 'index_no' => $key + 1]), ErrCode::$VALIDATE_ERROR);
                }
            } else {
                $item['travel_roommate_item'] = [];
            }

            // TH
            if ($country_code == GlobalEnums::TH_COUNTRY_CODE) {
                // wht类型不是/时, 发票税务号, 企业名称, 公司地址必填
                if (!empty($item['wht_type'])) {
                    if (mb_strlen($item['invoice_tax_no']) < 1) {
                        throw new ValidationException(static::$t->_('reimbursement_save_error_005', ['number' => $key + 1]),
                            ErrCode::$VALIDATE_ERROR);
                    }

                    if (mb_strlen($item['enterprise_name']) < 1) {
                        throw new ValidationException(static::$t->_('reimbursement_save_error_006', ['number' => $key + 1]),
                            ErrCode::$VALIDATE_ERROR);
                    }

                    if (mb_strlen($item['company_addr']) < 1) {
                        throw new ValidationException(static::$t->_('reimbursement_save_error_007', ['number' => $key + 1]),
                            ErrCode::$VALIDATE_ERROR);
                    }
                }

                // 网点类型 且 实质是银行手续费的
                if ($params['cost_store_type'] == 1 && in_array($item['budget_id'], $bank_charges_budget_ids)) {
                    $bank_charges_expense[] = $item;
                }

                // 差旅实质 住宿费明细 与 可选的出差单号
                if ($item['budget_id'] == $travel_budget_id) {
                    // 住宿费
                    if (in_array($item['product_id'], $accommodation_ids) && !in_array($item['travel_serial_no'], $business_trip_lodging_fee_list)) {
                        throw new ValidationException(static::$t->_('reimbursement_save_error_021', ['serial_no' => $item['travel_serial_no']]),
                            ErrCode::$VALIDATE_ERROR);
                    }

                    // 所有
                    if (!in_array($item['product_id'], $accommodation_ids) && !in_array($item['travel_serial_no'], $all_business_trip_list)) {
                        throw new ValidationException(static::$t->_('reimbursement_save_error_008',
                            ['travel_serial_no' => $item['travel_serial_no']]), ErrCode::$VALIDATE_ERROR);
                    }

                    // 共同住宿出差单号校验
                    // 住宿费 + 有共同住宿人
                    if (in_array($item['product_id'], $accommodation_ids) && !empty($item['travel_roommate_item'])) {
                        $check_roommate_params = [
                            'apply_id'         => $params['apply_id'],
                            'travel_serial_no' => $item['travel_serial_no'],
                            'submit_type'      => $submit_type,
                            'order_no'         => $params['no'],
                        ];
                        $this->checkTravelRoommateNo($check_roommate_params, $item['travel_roommate_item']);
                    }

                    // 超出报销时间说明校验
                    if ($is_add_type) {
                        $this->checkTravelReimbursementOverdueRemark($item['travel_end_at'], $item['travel_reimbursement_overdue_remark'], $params['first_apply_date'] ?? '');
                    }

                    // 出差单号和科目明细是否有混合提交
                    $travel_serial_no_map[$item['travel_serial_no']][] = $item['product_id'];

                    // 发票币种/日期 初始值
                    if ($submit_type == ReimbursementEnums::CREATE_ACTION_ADD_SUBMIT) {
                        $invoice_currency = $item['invoice_currency'];
                        $invoice_date     = $item['invoice_date'];
                    } else {
                        $invoice_currency = $params['currency'];
                        $invoice_date     = '';
                    }

                    // 收集境内机票明细
                    if (in_array($item['product_id'], $domestic_air_ticket_ids)) {
                        $domestic_air_ticket_list[$item['travel_serial_no']][] = [
                            'invoice_currency'    => $invoice_currency,
                            'invoice_date'        => $invoice_date,
                            'amount'              => $item['amount'],
                            'approval_email_flag' => !empty($item['exceeds_standard_amount_email_file']) ? 1 : 0,
                        ];
                    }

                    // 收集租车费明细
                    if (in_array($item['product_id'], $car_rental_ids)) {
                        $car_rental_list[$item['travel_serial_no']][] = [
                            'invoice_currency'    => $invoice_currency,
                            'invoice_date'        => $invoice_date,
                            'amount'              => $item['amount'],
                            'approval_email_flag' => !empty($item['exceeds_standard_amount_email_file']) ? 1 : 0,
                        ];
                    }

                    // 收集住宿费明细
                    if (in_array($item['product_id'], $accommodation_ids)) {
                        $accommodation_list[$item['travel_serial_no']][] = [
                            'invoice_currency'     => $invoice_currency,
                            'invoice_date'         => $invoice_date,
                            'amount'               => $item['amount'],
                            'approval_email_flag'  => !empty($item['exceeds_standard_amount_email_file']) ? 1 : 0,
                            'travel_roommate_item' => $item['travel_roommate_item'] ?? [],
                        ];
                    }
                }

                // 差旅/支援/交通 下的油费校验
                if (in_array($item['budget_id'], [$travel_budget_id, $support_budget_id, $transportation_budget_id]) && in_array($item['product_id'], $fule_ids)) {
                    if (!is_valid_integer_range($item['fuel_start_mileage'], 0, *********)) {
                        throw new ValidationException(static::$t->_('reimbursement_save_error_040', ['index_no' => $key + 1]), ErrCode::$VALIDATE_ERROR);
                    }

                    if (!is_valid_integer_range($item['fuel_end_mileage'], 0, *********)) {
                        throw new ValidationException(static::$t->_('reimbursement_save_error_041', ['index_no' => $key + 1]), ErrCode::$VALIDATE_ERROR);
                    }

                    if (!$this->checkFuel($item, $params['apply_id'])) {
                        throw new ValidationException(static::$t->_('reimbursement_save_error_022', ['index_no' => $key + 1]), ErrCode::$VALIDATE_ERROR);
                    }
                }
            }

            //17687需要对报销实质的每一行的（|tax_not发票金额（不含VAT含WHT）*rateVAT税率-taxVAT税额| 或 |tax_not发票金额（不含VAT含WHT）*wht_taxWHT税率-wht_tax_amountWHT税额|）与系统配置的税额允差值进行比较
            if (!empty($finance_tax_allowance_val) && $finance_tax_allowance_val > 0) {
                $vat_diff = abs(bcsub(round($item['tax_not'] * $item['rate'] / 100, 2), $item['tax'], 2));
                $wht_diff = abs(bcsub(round($item['tax_not'] * $item['wht_tax'] / 100, 2), $item['wht_tax_amount'], 2));
                //超过允差值需要拦截提交
                if (bccomp($vat_diff, $finance_tax_allowance_val, 2) === 1 || bccomp($wht_diff, $finance_tax_allowance_val, 2) === 1) {
                    $finance_tax_allowance_lines[] = ($key + 1);
                }
            }

            // 住宿费的ops同意邮件附件校验, 必填
            if ($apply_lodging_is_limit && in_array($item['product_id'],
                    $lodging_budget_limit_config['product_ids']) && empty($item['attachments'])) {
                throw new ValidationException(static::$t->_('reimbursement_save_error_001'), ErrCode::$VALIDATE_ERROR);
            }

            // 马来 若支付方式 非现金, 则 支付凭证必填
            if ($country_code == GlobalEnums::MY_COUNTRY_CODE) {
                if ($item['payment_method'] != ReimbursementEnums::PAYMENT_METHOD_CASH && empty($item['payment_voucher'])) {
                    throw new ValidationException(static::$t->_('reimbursement_save_error_004'), ErrCode::$VALIDATE_ERROR);
                }
            } else {
                $params['expense'][$key]['payment_method'] = 0;
            }

            // 支援费用-支援单号校验
            $item['support_serial_no_item'] = [];
            $item['support_serial_no']      = $item['support_serial_no'] ?? '';
            if ($item['budget_id'] == $support_budget_id && in_array($country_code, [GlobalEnums::TH_COUNTRY_CODE, GlobalEnums::PH_COUNTRY_CODE])) {
                $other_params = [
                    'order_no'                   => $params['no'],
                    'submit_type'                => $submit_type,
                    'apply_id'                   => $params['apply_id'],
                    'all_submit_support_no_list' => $all_submit_support_no_list,
                ];

                $expense_serial_no_item = array_filter(array_unique(explode(',', $item['support_serial_no'])));
                $this->checkSupportExpense($item['product_id'], $expense_serial_no_item, $other_params);

                $item['support_serial_no_item'] = $expense_serial_no_item;
            }

            $item['support_is_followed_policy']         = !empty($item['support_is_followed_policy']) ? $item['support_is_followed_policy'] : 0;

            $item['exceeds_standard_amount_email_file'] = !empty($item['exceeds_standard_amount_email_file']) ? $item['exceeds_standard_amount_email_file'] : [];

            $item['fuel_start_mileage']                 = !empty($item['fuel_start_mileage']) ? $item['fuel_start_mileage'] : 0;
            $item['fuel_end_mileage']                   = !empty($item['fuel_end_mileage']) ? $item['fuel_end_mileage'] : 0;
            $item['fuel_vehicle_type']                  = !empty($item['fuel_vehicle_type']) ? $item['fuel_vehicle_type'] : '';
            $item['fuel_vehicle_number']                = !empty($item['fuel_vehicle_number']) ? $item['fuel_vehicle_number'] : '';
            $item['fuel_oil_type']                      = !empty($item['fuel_oil_type']) ? $item['fuel_oil_type'] : 0;
            $item['fuel_start_mileage_file']            = !empty($item['fuel_start_mileage_file']) ? $item['fuel_start_mileage_file'] : [];
            $item['fuel_end_mileage_file']              = !empty($item['fuel_end_mileage_file']) ? $item['fuel_end_mileage_file'] : [];
        }

        // 银行手续费校验
        $this->checkBankChargesExpense($bank_charges_expense);

        if (!empty($finance_tax_allowance_lines)) {
            throw new ValidationException(static::$t->_('reimbursement_tax_allowance_validate_error',
                ['LINES' => implode('、', $finance_tax_allowance_lines), 'VAT_SST' => $vat_sst_name]), ErrCode::$VALIDATE_ERROR);
        }

        // 非泰国, 在不使用备用金时, 对借款单 和 剩余未还总额 进行校验
        if ($country_code != GlobalEnums::TH_COUNTRY_CODE && $params['petty_used'] == ReimbursementEnums::PETTY_USED_NO) {
            if (!empty($params['loan_id'])) {
                //如果选择了借款单,校验借款单是否合规
                LoanAddService::getInstance()->checkLoanReimbursement($params['loan_id'], $params['apply_id'], $params['cost_company_id']);
            } elseif (empty((float)$params['loan_amount']) || empty($data['loan_id'])) {
                //查询员工是否存在未归还完毕的借款单
                if (LoanAddService::getInstance()->getStaffUnReturnByCompany($params['apply_id'], $params['cost_company_id'])) {
                    throw new ValidationException(static::$t->_('reimbursement_submit_apply_not_returned_error_1'), ErrCode::$VALIDATE_ERROR);
                }
            }
        }

        // 差旅费下的出差单 和 费用明细混合提交校验
        $this->checkTravelMixedSubmission($travel_serial_no_map);

        // 境内机票校验
        foreach ($domestic_air_ticket_list as $travel_serial_no => $air_item) {
            $air_item_params = [
                'submit_type'              => $submit_type,
                'apply_id'                 => $params['apply_id'],
                'travel_serial_no'         => $travel_serial_no,
                'domestic_air_ticket_item' => $air_item,
            ];
            $air_res         = $this->checkDomesticAirTicket($air_item_params);
            if ($air_res['is_exceeds_standard_amount'] && in_array(0, array_column($air_item, 'approval_email_flag'))) {
                $validation_message = static::$t->_('reimbursement_save_error_017', ['serial_no' => $travel_serial_no]);

                if ($is_add_type) {
                    return [
                        'can_apply'        => ReimbursementEnums::CAN_APPLY_NO,
                        'message'          => $validation_message,
                        'travel_serial_no' => $travel_serial_no,
                        'product_ids'      => $domestic_air_ticket_ids,
                    ];
                } else {
                    throw new ValidationException($validation_message, ErrCode::$REIMBURSEMENT_ABOVE_QUOTA_VALIDATION);
                }
            }
        }

        // 租车费校验
        foreach ($car_rental_list as $travel_serial_no => $car_item) {
            $car_item_params = [
                'submit_type'      => $submit_type,
                'apply_id'         => $params['apply_id'],
                'travel_serial_no' => $travel_serial_no,
                'car_rental_item'  => $car_item,
            ];
            $car_res         = $this->checkCarRental($car_item_params);
            if ($car_res['is_exceeds_standard_amount'] && in_array(0, array_column($car_item, 'approval_email_flag'))) {
                $validation_message = static::$t->_('reimbursement_save_error_020', ['serial_no' => $travel_serial_no]);

                if ($is_add_type) {
                    return [
                        'can_apply'        => ReimbursementEnums::CAN_APPLY_NO,
                        'message'          => $validation_message,
                        'travel_serial_no' => $travel_serial_no,
                        'product_ids'      => $car_rental_ids,
                    ];
                } else {
                    throw new ValidationException($validation_message, ErrCode::$REIMBURSEMENT_ABOVE_QUOTA_VALIDATION);
                }
            }
        }

        // 出差住宿费校验
        foreach ($accommodation_list as $travel_serial_no => $accommodation_item) {
            $accommodation_item_params = [
                'submit_type'        => $submit_type,
                'apply_id'           => $params['apply_id'],
                'travel_serial_no'   => $travel_serial_no,
                'accommodation_item' => $accommodation_item,
            ];

            $accommodation_res = $this->checkTripAccommodation($accommodation_item_params);
            if ($accommodation_res['is_exceeds_standard_amount'] && in_array(0, array_column($accommodation_item, 'approval_email_flag'))) {
                $validation_message = static::$t->_('reimbursement_save_error_019', ['serial_no' => $travel_serial_no]);

                if ($is_add_type) {
                    return [
                        'can_apply'        => ReimbursementEnums::CAN_APPLY_NO,
                        'message'          => $validation_message,
                        'travel_serial_no' => $travel_serial_no,
                        'product_ids'      => $accommodation_ids,
                    ];
                } else {
                    throw new ValidationException($validation_message, ErrCode::$REIMBURSEMENT_ABOVE_QUOTA_VALIDATION);
                }
            }
        }

        // 主单据标记: 是否有出差共同住宿人
        $params['travel_is_have_roommate'] = 0;
        $expense_is_have_roommate          = array_filter(array_column($params['expense'], 'travel_is_have_roommate'));
        if (in_array(ReimbursementEnums::TRAVEL_IS_HAVE_ROOMMATE_YES, $expense_is_have_roommate)) {
            $params['travel_is_have_roommate'] = ReimbursementEnums::TRAVEL_IS_HAVE_ROOMMATE_YES;
        } elseif (in_array(ReimbursementEnums::TRAVEL_IS_HAVE_ROOMMATE_NO, $expense_is_have_roommate)) {
            $params['travel_is_have_roommate'] = ReimbursementEnums::TRAVEL_IS_HAVE_ROOMMATE_NO;
        }

        return $params;
    }

    /**
     * 数据查询列表参数校验
     *
     * @param array $params
     * @return array
     */
    public static function getDataListValidation(array $params = [])
    {
        $validate_params = [];
        if (!empty($params['pay_date_start'])) {
            $validate_params['pay_date_start'] = 'Date|>>>:params error[pay_date_start YYYY-MM-DD]';
        }

        if (!empty($params['pay_date_end'])) {
            $validate_params['pay_date_end'] = 'Date|>>>:params error[pay_date_end YYYY-MM-DD]';
        }

        if (!empty($params['pay_operate_start_date'])) {
            $validate_params['pay_operate_start_date'] = 'Date|>>>:params error[pay_operate_start_date YYYY-MM-DD]';
        }

        if (!empty($params['pay_operate_end_date'])) {
            $validate_params['pay_operate_end_date'] = 'Date|>>>:params error[pay_operate_end_date YYYY-MM-DD]';
        }

        return $validate_params;
    }

    /**
     * 获取明细项类型配置
     *
     * @param int $details_type
     * @return array
     */
    public static function getDetailTypeItem(int $details_type = 0)
    {
        static $details_type_item = [];
        if (empty($details_type_item)) {
            $details_type_item = EnumsService::getInstance()->getReimbursementEnums()['special_expense_details_type'] ?? [];
        }

        return empty($details_type) ? $details_type_item : ($details_type_item[$details_type] ?? []);
    }

    /**
     * 获取支援费用允许报销的交通方式
     */
    public static function getSupportAllowTransportationMode()
    {
        static $allow_transportation_modes = [];
        if (empty($allow_transportation_modes)) {
            $allow_transportation_modes = EnumsService::getInstance()->getSettingEnvValueIds('reimbursement_support_allow_transportation_mode');
        }

        return $allow_transportation_modes;
    }

    /**
     * 获取网点支援费用类型对应的预算科目ID
     *
     * @return array
     */
    public static function getSupportTypeBudgetId()
    {
        static $budget_id = null;

        if (is_null($budget_id)) {
            // 科目类型配置
            $expense_type_item = EnumsService::getInstance()->getReimbursementEnums()['special_expense_type'] ?? [];
            $budget_id        = $expense_type_item[ReimbursementEnums::SPECIAL_EXPENSE_ITEM_TYPE_SUPPORT_FEE] ?? '';
        }

        return $budget_id;
    }

    /**
     * 获取差旅费用类型对应的预算科目ID
     *
     * @return array
     */
    public static function getTravelTypeBudgetId()
    {
        static $budget_id = null;

        if (is_null($budget_id)) {
            // 科目类型配置
            $expense_type_item = EnumsService::getInstance()->getReimbursementEnums()['special_expense_type'] ?? [];
            $budget_id        = $expense_type_item[ReimbursementEnums::SPECIAL_EXPENSE_ITEM_TYPE_TRAVEL_FEE] ?? GlobalEnums::BUDGET_TRAVEL_ID;
        }

        return $budget_id;
    }

    /**
     * 获取交通费用类型对应的预算科目ID
     *
     * @return array
     */
    public static function getTransportationTypeBudgetId()
    {
        static $budget_id = null;

        if (is_null($budget_id)) {
            // 科目类型配置
            $expense_type_item = EnumsService::getInstance()->getReimbursementEnums()['special_expense_type'] ?? [];
            $budget_id        = $expense_type_item[ReimbursementEnums::SPECIAL_EXPENSE_ITEM_TYPE_TRANSPORTATION_FEE] ?? '';
        }

        return $budget_id;
    }

    /**
     * 获取银行手续费的预算科目ID
     *
     * @return array
     */
    public static function getBankChargesBudgetIds()
    {
        static $ids = [];
        if (empty($ids)) {
            $ids = EnumsService::getInstance()->getSettingEnvValueIds('budget_object_bank_charges_ids');
        }

        return $ids;
    }

    /**
     * 获取合同编号
     *
     * @return string
     */
    public static function getNo($date)
    {
        $key = self::getCounterKey($date);
        if (self::getCounter($key)) {           //有计数器
            $lno = self::incrCounter($key);
        } else {
            $lno = self::setCounter($key);
        }
        return $date . sprintf('%04s', $lno);
    }

    /**
     * 判断计数器是否存在
     *
     * @param string $key
     * @return bool|int
     */
    private static function getCounter($key)
    {
        return RedisClient::getInstance()->getClient()->exists($key);
    }

    /**
     * 计数器不存在的情况下
     *
     * @param string $key
     * @return bool|int
     */
    private static function setCounter($key)
    {
        $lno = 1;
        RedisClient::getInstance()->getClient()->setex($key, RedisExpire::ONE_DAY, $lno);
        return $lno;
    }

    /**
     * 计数器存在的情况下
     *
     * @param string $key
     * @return int
     */
    private static function incrCounter($key)
    {
        return RedisClient::getInstance()->getClient()->incrBy($key, 1);
    }


    private static function getCounterKey($date)
    {
        return RedisKey::REIMBURSEMENT_CREATE_COUNTER . "_" . $date;
    }


    /**
     * 去bi里面取相关数据
     *
     * @param $userId
     * @param int $flag 0发起人，1申请人
     * @return array|string
     * @throws ValidationException
     */
    public function getUserMetaFromBi($userId, $flag = 0)
    {
        $model = (new UserService())->getUserByIdInRbi($userId);
        if (empty($model)) {
            throw new ValidationException(static::$t->_('re_staff_info_id_not_exist'), ErrCode::$VALIDATE_ERROR);
        }

        if ($model->state == StaffInfoEnums::STAFF_STATE_LEAVE) {
            throw new ValidationException(static::$t->_('re_staff_info_id_left'), ErrCode::$VALIDATE_ERROR);
        }
        //非编制内
        if ($model->formal != StaffInfoEnums::FORMAL_IN) {
            throw new ValidationException(static::$t->_('re_staff_info_id_not_formal'), ErrCode::$VALIDATE_ERROR);
        }

        $data               = [];
        $id                 = $model->staff_info_id ?? '';
        $name               = $model->name ?? '';
        $nick_name          = $model->nick_name ?? '';
        $department_id      = $model->sys_department_id;
        $node_department_id = $model->node_department_id;

        $department_id   = empty($node_department_id) ? $department_id : $node_department_id;
        $department_name = '';
        $company_name = '';
        //是否是 lnt
        $isLnt = HrStaffRepository::isLntCompanyByInfo($model->toArray());

        $t = DepartmentModel::findFirst([
            "conditions" => "id = :id:",
            "bind"       => [
                "id" => $department_id,
            ]
        ]);
        if (!empty($t)) {
            $department_name = $t->name;
            $company_name    = $t->company_name;
        }
        $data['create_job_title_id']   = $model->job_title;
        $data['wait_leave_state']      = $model->wait_leave_state;
        $data['state']                 = $model->state;
        $data['create_job_title_name'] = "";
        $t                             = $model->getJobTitle();
        if (!empty($t)) {
            $data['create_job_title_name'] = $t->job_name;
        }

        if (empty($flag)) {
            $data['created_id']              = $id;
            $data['created_name']            = $this->getNameAndNickName($name, $nick_name);
            $data['created_department_id']   = $department_id;
            $data['created_department_name'] = $department_name;
            $data['created_company_name'] = $company_name;
            $data['is_lnt'] = $isLnt;
        } else {
            $data['apply_user']['apply_id']              = $id;
            $data['apply_user']['apply_name']            = $this->getNameAndNickName($name, $nick_name);
            $data['apply_user']['apply_department_id']   = $department_id;
            $data['apply_user']['apply_department_name'] = $department_name;
            $data['apply_user']['apply_company_name'] = $company_name;
            $data['apply_user']['apply_center_code'] = "";
            $data['apply_user']['apply_store_id'] = $model->sys_store_id;
            $data['apply_user']['apply_mobile'] = $model->mobile;
            $data['apply_user']['is_lnt']       = $isLnt;

            $data['apply_user']['apply_job_title_id']   = $data['create_job_title_id'];
            $data['apply_user']['apply_job_title_name'] = $data['create_job_title_name'];

            // 申请人职位性质
            $department_job_info = HrJobDepartmentRelationRepository::getInstance()->getDepartmentJobInfo($model->node_department_id, $model->job_title);
            $data['apply_user']['apply_position_type'] = !empty($department_job_info['position_type']) ? $department_job_info['position_type'] : '';


            // 申请人职级
            $data['apply_user']['apply_job_title_grade'] = $model->job_title_grade_v2;
            //上传审批邮件附件！
            $data['apply_user']['is_need_email_attachment'] = false;

            if ($model->sys_store_id == Enums::HEAD_OFFICE_STORE_FLAG) {
                $data['apply_user']['apply_store_name'] = Enums::PAYMENT_HEADER_STORE_NAME;
            } else {
                $tmp                                            = SysStoreModel::findFirst("id = '" . $model->sys_store_id . "'");
                $department_config                              = EnumsService::getInstance()->getSettingEnvValueMap('appoint_store_by_department_id');
                $data['apply_user']['apply_store_name']         = $tmp ? $tmp->name : '';
                $data['apply_user']['is_need_email_attachment'] = get_country_code() == GlobalEnums::PH_COUNTRY_CODE && $model->sys_department_id == $department_config['network'];//是否需要上传审批邮件附件
            }

            // 是否允许自由选择费用所属部门
            $data['apply_user']['is_free_change'] = false;

            // 可自由选择全量费用所属部门的部门配置
            $change_cost_department_ids = EnumsService::getInstance()->getSettingEnvValueIds('change_cost_department_ids');

            // 可自由选择全量费用所属部门的工号配置
            $change_cost_staff_ids = EnumsService::getInstance()->getSettingEnvValueIds('change_cost_department_by_staff_ids');

            // 申请人所属部门 或 申请人 隶属 上述配置时, 可自由选择费用所属部门 v18029
            if (in_array($department_id, $change_cost_department_ids) || in_array($id, $change_cost_staff_ids)) {
                $data['apply_user']['is_free_change'] = true;
            }

            $data['apply_user']['bank_account'] = $model->bank_no;
            $data['apply_user']['bank_type']    = $this->getBankTypeFromBy($model->bank_type);

            $data['apply_user']['bank_name'] = $name;
            if (in_array(get_country_code(), [GlobalEnums::ID_COUNTRY_CODE, GlobalEnums::VN_COUNTRY_CODE])) {
                $staff_items_arr                 = (new HrStaffRepository())->getStaffItems([$userId],
                    [StaffInfoEnums::HR_STAFF_ITEMS_BANK_NO_NAME]);
                $data['apply_user']['bank_name'] = $staff_items_arr[0]['value'] ?? $name;
            }

            $data['apply_user']['sys_store_id']       = $model->sys_store_id;
            $data['apply_user']['sys_department_id']  = $model->sys_department_id;
            $data['apply_user']['node_department_id'] = $model->node_department_id;
            //staff_items=NATIONALITY=国籍1泰国2中国99其他
            $data['apply_user']['is_china'] = 0;

            $data['apply_user']['cost_department']           = $data['apply_user']['node_department_id'];
            $data['apply_user']['cost_department_name']      = $data['apply_user']['apply_department_name'];
            $data['apply_user']['cost_department_is_update'] = 0;
            $data['apply_user']['cost_store']                = $data['apply_user']['sys_store_id'];
            $data['apply_user']['cost_store_name']           = $data['apply_user']['apply_store_name'];

            // 获取费用部门的部门信息, 9975需求 1. 根据【费用部门】字段，自动带出部门所属的BU级别的信息。2. 如果无BU或者所属Clevel不是COO\CEO，则默认值为空，且必填；需要选择对应COO\CEO下的BU级别的信息。
            $data['apply_user']['cost_company_id']   = '';
            $data['apply_user']['cost_company_name'] = '';
            $dept_info                               = DepartmentModel::findFirst($data['apply_user']['cost_department']);
            if (!empty($dept_info) && !empty($dept_info->company_id)) {
                $coo_company_list = (new PurchaseService())->getCooCostCompany();
                if (!empty($coo_company_list)) {
                    foreach ($coo_company_list as $company) {
                        if ($company['cost_company_id'] == $dept_info->company_id) {
                            $data['apply_user']['cost_company_id']   = $company['cost_company_id'];
                            $data['apply_user']['cost_company_name'] = $company['cost_company_name'];
                            break;
                        }
                    }
                }
            }
            $pcCode                                 = DetailService::getInstance()->getPcCode($model->sys_department_id);
            $data['apply_user']['cost_center_code'] = '';
            if ($pcCode['code'] == ErrCode::$SUCCESS) {
                $data['apply_user']['cost_center_code'] = $pcCode['data']['cost_center_code'];
            }

            $national = HrStaffItemsModel::findFirst([
                'conditions' => 'staff_info_id = ?1 and item = ?2',
                'bind'       => [
                    1 => $userId,
                    2 => 'NATIONALITY'
                ],
            ]);

            if (!empty($national) && $national->value == 2) {
                $data['apply_user']['is_china'] = 1;
            }

            //总部员工，找部门对应code
            if ($model->sys_store_id == -1) {
                $item = Pccode::findFirst(["conditions" => "department_id = :id:", "bind" => ["id" => $department_id]]);
                if (!empty($item)) {
                    $data['apply_user']['apply_center_code'] = $item->pc_code;
                }
            } else {
                $data['apply_user']['apply_center_code'] = $this->getPcCodeByStoreId($model->sys_store_id);
            }
        }

        return $data;
    }

    /**
     * 获取对应网点的pc_code
     *
     * @param $store_id
     * @return string
     */
    public function getPcCodeByStoreId($store_id)
    {
        $item = SysStoreModel::findFirst(["conditions" => "id = :id:", "bind" => ["id" => $store_id]]);
        if (!empty($item)) {
            return $item->sap_pc_code;
        }
        return "";
    }


    public function getLangKeyArr()
    {
        return [
            "title",
            "id",
            "base_info",
            "created_name",
            "created_id",
            "created_company_name",
            "created_department_name",

            "apply_name",
            "apply_id",
            "apply_company_name",
            "apply_department_name",
            "apply_store_name",
            "date",
            "apply_center_code",
            "bank_info",

            "bank_name",
            "currency_text",
            "bank_account",
            "bank_type",
            "detail",
            "detail_id",
            "category_a",
            "category_b",
            "start_at",

            "travel_start",
            "travel_end",
            "fuel_start",
            "fuel_end",
            "fuel_mileage",
            "rate",
            "tax",
            "payable_amount",
            "wht_tax_amount",
            "tax_not",
            "invoices_ids",
            "stat",

            "detail_amount",
            "travel",
            "local",
            "amount",
            "payable_amount_all",
            "loan_amount",
            "other_amount",
            "real_amount",
            "auth_logs",
            "step",
            "finished_at",

            "deal_id",
            "deal_name",
            "deal_res",
            "deal_mark",
            "approve",
            "info",
            "cost_store_name",
            "apply_mobile",
            "pay_info",
            "is_pay",
            "pay_bank_account",

            "pay_bank_name",
            "sign_name",
            "pay_at",
            "remark"
        ];
    }

    /**
     * 费用项目这里是国家
     * 老挝    Laos    ลาว
     * 菲律宾    Philippines    ฟิลิปปินส์
     * 越南    Vietnam    เวียดนาม
     * 柬埔寨    Cambodia    กัมพูชา
     * 马来西亚    Malaysia    มาเลเซีย
     *
     * @return mixed
     */
    public static function getLangCountryArr()
    {
        return [
            ["code" => "lao", "text_key" => "country_code_laos"],
            ["code" => "philippines", "text_key" => "country_code_philippines"],
            ["code" => "vietnam", "text_key" => "country_code_vietnam"],
            ["code" => "cambodia", "text_key" => "country_code_cambodia"],
            ["code" => "malaysia", "text_key" => "country_code_malaysia"],
            ["code" => "Indonesia", "text_key" => "country_code_indonesia"],
        ];
    }

    /**
     * 获得借款付款申请人呢
     *
     * @return array|false|string[]
     */

    public function getReimbursementPayStaffIds()
    {
        $pay_staff_id = EnvModel::getEnvByCode('reimbursement_pay_staff_id');
        $pay_staff_id = explode(',', $pay_staff_id);
        return $pay_staff_id ?? [];
    }


    public function isCanDownload($item, $uid)
    {
        if (empty($item)) {
            return '0';
        }

        if (empty($uid)) {
            return '1';
        }

        if (is_object($item)) {
            $item = $item->toArray();
        }

        //已驳回，已撤销，未付款不能下载
        if ($item['status'] == Enums::CONTRACT_STATUS_CANCEL || $item['status'] == Enums::CONTRACT_STATUS_REJECTED || $item['pay_status'] == Enums::LOAN_PAY_STATUS_NOTPAY) {
            return '0';
        }
        /**
         * 线上问题修复，改is_after_ap_th字段返回值
         * 再一期审批流修改需求后不再维护变更，故而导致无法正常下载pdf
         * 跟万琳沟通后，直接改为不满足以上提交就可以下载
         */
        return '1';
    }

    /**
     * 从by的bank_list表获取银行名称
     * 原来是从枚举Enums::$bank_type中获取银行名称
     * 保持原逻辑,0=>'',未匹配=>'(unknown)'
     *
     * @param $bank_type_id
     * @return string
     * @date 2022/3/30
     */
    public function getBankTypeFromBy($bank_type_id)
    {
        //原枚举中的逻辑 0 => ''
        if ($bank_type_id === 0) {
            return '';
        }
        $bank_type_info = BankListModel::findFirst([
            'conditions' => ' bank_id = :bank_id: ',
            'bind'       => ['bank_id' => $bank_type_id]
        ]);
        $bank_type_info = $bank_type_info ? $bank_type_info->toArray() : [];
        return $bank_type_info['bank_name'] ?? '(unknown)';
    }

    /**
     * 取国家中文名称
     *
     * @return string
     * @date 2022/4/29
     */
    public function getCountryCnName()
    {
        return static::$t->_("working_country." . strtolower(get_country_code()));
    }

    /**
     * 获取费用明细已报销在途的支援单号
     * 在途: 整单待确认/待签字/待提交/待审核/已通过 且 待支付/已支付
     * @param $product_id
     * @param array $support_serial_no_list
     * @param array $other_params
     * @return mixed
     */
    public function getSupportSerialNoList($product_id, array $support_serial_no_list = [], array $other_params = [])
    {
        if (empty($product_id)) {
            return [];
        }

        $order_no    = $other_params['order_no'] ?? '';
        $submit_type = $other_params['submit_type'] ?? ReimbursementEnums::CREATE_ACTION_ADD_SUBMIT;

        $builder = $this->modelsManager->createBuilder();
        $builder->columns('support_rel.support_serial_no');
        $builder->from(['main' => Reimbursement::class]);
        $builder->leftjoin(ReimbursementDetailSupportRelModel::class, 'support_rel.re_id = main.id', 'support_rel');
        $builder->where('support_rel.product_id = :product_id:', ['product_id' => $product_id]);
        $builder->inWhere('main.status', [
            ReimbursementEnums::STATUS_WAIT,
            ReimbursementEnums::STATUS_PASS,
            ReimbursementEnums::STATUS_WAITING_CONFIRMED,
            ReimbursementEnums::STATUS_WAITING_SIGNED,
            ReimbursementEnums::STATUS_WAITING_SUBMITTED,
        ]);
        $builder->inWhere('main.pay_status', [ReimbursementEnums::PAY_STATUS_WAIT, ReimbursementEnums::PAY_STATUS_PAY]);

        if (in_array($submit_type, [ReimbursementEnums::CREATE_ACTION_APPLY_SIGNED, ReimbursementEnums::CREATE_ACTION_MANUAL_SUBMIT])) {
            $builder->where('main.no != :order_no:', ['order_no' => $order_no]);
        }

        if (!empty($support_serial_no_list)) {
            $builder->inWhere('support_rel.support_serial_no', $support_serial_no_list);
        }

        $item = $builder->getQuery()->execute()->toArray();
        return array_unique(array_column($item, 'support_serial_no'));
    }

    /**
     * 获取申请人可报销的出差单
     *
     * @param $params
     * @return array
     * @throws ValidationException
     */
    public function getBusinessTrip($params)
    {
        $apply_id    = $params['apply_id'];
        $is_stay     = $params['is_stay'];
        $order_no    = $params['order_no'] ?? '';
        $submit_type = $params['submit_type'] ?? ReimbursementEnums::CREATE_ACTION_ADD_SUBMIT;

        // 差旅住宿费明细ID
        $accommodation_ids = [];
        if ($is_stay) {
            $accommodation_ids = static::getDetailTypeItem(ReimbursementEnums::SPECIAL_EXPENSE_DETAILS_TYPE_ACCOMMODATION_FEES);
            $accommodation_ids = array_filter($accommodation_ids);
            if (empty($accommodation_ids)) {
                throw new ValidationException(static::$t->_('reimbursement_travel_setting_error_001'), ErrCode::$VALIDATE_ERROR);
            }
        }

        // 获取申请人 报销在途的出差单号(所有/住宿)
        $used_serial_no_list = $this->getBusinessTravelUsed($apply_id, $is_stay, $accommodation_ids, $submit_type, $order_no);

        // 获取可用的差旅编号
        $trip_time_limit              = ReimbursementTripTimeLimitRepository::getInstance()->getOneById(1);
        $allowed_travel_end_date_days = $trip_time_limit->allowed_travel_end_date_days ?? 0;

        $start = date('Y-m-d', strtotime("-{$allowed_travel_end_date_days} days"));

        $builder = $this->modelsManager->createBuilder();
        $builder->from(TripModel::class);
        $builder->where('apply_user = :apply_user: AND status = :status: AND end_time >= :start_time: AND end_time < :end_time: AND business_trip_type != 5',
            [
                'apply_user' => $apply_id,
                'status'     => Enums::TRAFFIC_STATUS_APPROVAL,
                'start_time' => $start,
                'end_time'   => date('Y-m-d'),
            ]
        );

        // 是否获取住宿费
        if ($is_stay) {
            $builder->andWhere('is_stay = :is_stay:', ['is_stay' => ReimbursementEnums::BUSINESS_TRIP_IS_STAY_YES]);
        }

        $items = $builder->getQuery()->execute()->toArray();

        // 获取城市code对应的城市名称
        $city_list = $this->getTravelNoListCityInfo($items);

        // 获取在途的共同住宿出差单
        $serial_no_item           = array_column($items, 'serial_no');
        $is_roommate_trip_no_list = $this->getIsRoommateTripNoList($serial_no_item, $submit_type, $order_no);

        $data = [];
        foreach ($items as $item) {
            // 过滤调报销在途的
            if (in_array($item['serial_no'], $used_serial_no_list)) {
                continue;
            }

            // 出发城市
            $departure_city = $item['departure_city'];
            if (empty($departure_city)) {
                $departure_city_info = $city_list[$item['departure_city_code']] ?? [];
                $departure_city      = ($departure_city_info['province_name'] ?? '') . ' ' . ($departure_city_info['city_name'] ?? '');
            }

            // 目的城市
            $destination_city = $item['destination_city'];
            if (empty($destination_city)) {
                $destination_city_info = $city_list[$item['destination_city_code']] ?? [];
                $destination_city      = ($destination_city_info['province_name'] ?? '') . ' ' . ($destination_city_info['city_name'] ?? '');
            }

            $data[] = [
                'travel_id'                      => $item['id'],
                'travel_serial_no'               => $item['serial_no'],
                'travel_start'                   => trim($departure_city),
                'travel_end'                     => trim($destination_city),
                'travel_start_at'                => $item['start_time'],
                'travel_end_at'                  => $item['end_time'],
                'travel_days_num'                => $item['days_num'],
                'apply_user'                     => $item['apply_user'],
                'is_stay'                        => $item['is_stay'],
                'is_roommate'                    => in_array($item['serial_no'], $is_roommate_trip_no_list),
                'travel_business_trip_type'      => $item['business_trip_type'],
                'travel_business_trip_type_text' => static::$t->_(ReimbursementEnums::$travel_business_trip_type[$item['business_trip_type']] ?? '') ?? '',
            ];
        }

        return $data;
    }

    /**
     * 获取出差单的城市名称信息
     */
    protected function getTravelNoListCityInfo($travel_list, $name_field = '')
    {
        if (empty($travel_list)) {
            return [];
        }

        // 获取城市code对应的城市名称
        $departure_city_code   = array_column($travel_list, 'departure_city_code');
        $destination_city_code = array_column($travel_list, 'destination_city_code');
        $city_codes            = array_unique(array_filter(array_merge($departure_city_code, $destination_city_code)));

        $city_list = [];
        if (!empty($city_codes)) {
            $name_field = !empty($name_field) ? $name_field : (static::$language == 'en' ? 'en_name' : 'name');

            $city_columns = [
                "city.code AS city_code",
                "city.$name_field AS city_name",
                "province.$name_field AS province_name",
                'city.province_code',
            ];

            $city_list = $this->modelsManager->createBuilder()
                ->from(['city' => SysCityModel::class])
                ->leftjoin(SysProvinceModel::class, 'province.code = city.province_code', 'province')
                ->inWhere('city.code', $city_codes)
                ->columns($city_columns)
                ->getQuery()->execute()->toArray();

            $city_list = array_column($city_list, null, 'city_code');
        }

        return $city_list;
    }

    /**
     * 获得已经用过的travel_id
     * @param $userId
     * @param $source_type
     * @return array
     */
    public function getTravelUsed($userId, $source_type)
    {
        $builder = $this->modelsManager->createBuilder();
        $builder->from(['d' => Detail::class]);
        $builder->leftjoin(Reimbursement::class, 'd.re_id=r.id', 'r');
        $builder->andWhere('r.apply_id = :id:', ['id' => $userId]);
        $builder->columns("group_concat(d.re_id) re_ids,d.travel_id");

        // 不可被关联的新逻辑: 审批中、审批通过 且 已支付/待支付
        $builder->andWhere("(r.status = :status_pending: OR (r.status = :status_approval: AND r.pay_status IN ({pay_status:array}))) AND d.level_code = :level_code:",
            [
                'status_pending'  => Enums::WF_STATE_PENDING,
                'status_approval' => Enums::WF_STATE_APPROVED,
                'pay_status'      => [
                    Enums::PAYMENT_PAY_STATUS_PAY,
                    Enums::PAYMENT_PAY_STATUS_PENDING,
                ],
                'level_code'      => GlobalEnums::BUDGET_TRAVEL_LEVEL_CODE,// 差旅费
            ]);

        // OA平台仅排除通过OA平台提交的差旅费明细(不必区分是油费还是其他明细)
        if ($source_type == ReimbursementEnums::SOURCE_TYPE_OA) {
            $builder->andWhere('r.source_type = :source_type:', ["source_type" => (int)$source_type]);
        } elseif ($source_type == ReimbursementEnums::SOURCE_TYPE_BY) {
            // BY 平台仅排除油费明细(不区分提交平台是 OA 还是 BY)
            $builder->andWhere('d.product_id = :product_id:',
                ['product_id' => GlobalEnums::BUDGET_TRAVEL_OIL_PRODUCT_ID]);
        }

        $builder->groupBy('d.travel_id');
        $items = $builder->getQuery()->execute()->toArray();

        if (empty($items)) {
            return [];
        }
        $travel_id_list = [];
        $limit          = GlobalEnums::MY_COUNTRY_CODE == get_country_code() ? 1 : 0;
        foreach ($items as $item) {
            $re_ids_arr = explode(',', $item['re_ids']);
            if (count(array_unique($re_ids_arr)) > $limit) {
                $travel_id_list[] = $item['travel_id'];
            }
        }
        //去重，过滤掉空的
        return array_values(array_unique(array_filter($travel_id_list)));
    }

    /**
     * 获取申请人 在途的报销出差单号
     * @param $user_id
     * @param $is_stay
     * @param $accommodation_ids
     * @param $submit_type
     * @param $order_no
     * @return array
     */
    public function getBusinessTravelUsed($user_id, $is_stay, $accommodation_ids, $submit_type, $order_no)
    {
        $is_filter_order = in_array($submit_type, [ReimbursementEnums::CREATE_ACTION_APPLY_SIGNED, ReimbursementEnums::CREATE_ACTION_MANUAL_SUBMIT]);

        // 差旅实质
        $travel_budget_id = static::getTravelTypeBudgetId();

        // 申请人在途的出差单
        $travel_serial_builder = $this->modelsManager->createBuilder()
            ->from(['d' => Detail::class])
            ->leftjoin(Reimbursement::class, 'd.re_id = r.id', 'r')
            ->columns('d.travel_serial_no')
            ->where('r.apply_id = :apply_id:', ['apply_id' => $user_id])
            ->andWhere('r.status IN ({status:array}) AND r.pay_status IN ({pay_status:array}) AND d.budget_id = :budget_id:',
                [
                    'status'     => [
                        ReimbursementEnums::STATUS_WAITING_CONFIRMED,
                        ReimbursementEnums::STATUS_WAITING_SIGNED,
                        ReimbursementEnums::STATUS_WAITING_SUBMITTED,
                        Enums::WF_STATE_PENDING,
                        Enums::WF_STATE_APPROVED,
                    ],
                    'pay_status' => [
                        Enums::PAYMENT_PAY_STATUS_PAY,
                        Enums::PAYMENT_PAY_STATUS_PENDING,
                    ],
                    'budget_id'  => $travel_budget_id,
                ]
            );

        if ($is_filter_order) {
            $travel_serial_builder->andWhere('r.no != :order_no:', ['order_no' => $order_no]);
        }

        $serial_no_item = $travel_serial_builder->getQuery()->execute()->toArray();
        $serial_no_item = array_column($serial_no_item, 'travel_serial_no');


        // 在途的共同住宿出差单
        $roommate_no_item = [];
        if ($is_stay) {
            $roommate_builder = $this->modelsManager->createBuilder()
                ->from(['rel' => ReimbursementDetailTravelRoommateRelModel::class])
                ->leftjoin(Reimbursement::class, 'rel.re_id = r.id', 'r')
                ->columns('rel.serial_no')
                ->where('r.status IN ({status:array}) AND r.pay_status IN ({pay_status:array}) AND rel.product_id IN ({product_ids:array})', [
                    'status'      => [
                        ReimbursementEnums::STATUS_WAITING_CONFIRMED,
                        ReimbursementEnums::STATUS_WAITING_SIGNED,
                        ReimbursementEnums::STATUS_WAITING_SUBMITTED,
                        Enums::WF_STATE_PENDING,
                        Enums::WF_STATE_APPROVED,
                    ],
                    'pay_status'  => [
                        Enums::PAYMENT_PAY_STATUS_PAY,
                        Enums::PAYMENT_PAY_STATUS_PENDING,
                    ],
                    'product_ids' => array_values($accommodation_ids),
                ]);

            if ($is_filter_order) {
                $roommate_builder->andWhere('r.no != :order_no:', ['order_no' => $order_no]);
            }

            $roommate_no_item = $roommate_builder->getQuery()->execute()->toArray();
            $roommate_no_item = array_column($roommate_no_item, 'serial_no');
        }

        // 去重，过滤掉空的
        return array_unique(array_filter(array_merge($serial_no_item, $roommate_no_item)));
    }

    /**
     * 查验出差单号是否是共同住宿在途的
     */
    public function getIsRoommateTripNoList($trip_no_list, $submit_type, $order_no)
    {
        $trip_no_list = array_filter(array_unique($trip_no_list));
        if (empty($trip_no_list)) {
            return [];
        }

        $accommodation_ids = static::getDetailTypeItem(ReimbursementEnums::SPECIAL_EXPENSE_DETAILS_TYPE_ACCOMMODATION_FEES);
        $accommodation_ids = array_filter($accommodation_ids);
        if (empty($accommodation_ids)) {
            throw new ValidationException(static::$t->_('reimbursement_travel_setting_error_001'), ErrCode::$VALIDATE_ERROR);
        }

        $is_filter_order = in_array($submit_type, [ReimbursementEnums::CREATE_ACTION_APPLY_SIGNED, ReimbursementEnums::CREATE_ACTION_MANUAL_SUBMIT]);

        $roommate_builder = $this->modelsManager->createBuilder()
            ->from(['rel' => ReimbursementDetailTravelRoommateRelModel::class])
            ->leftjoin(Reimbursement::class, 'rel.re_id = r.id', 'r')
            ->columns('rel.serial_no')
            ->where('r.status IN ({status:array}) AND r.pay_status IN ({pay_status:array}) AND rel.product_id IN ({product_ids:array})', [
                'status'      => [
                    ReimbursementEnums::STATUS_WAITING_CONFIRMED,
                    ReimbursementEnums::STATUS_WAITING_SIGNED,
                    ReimbursementEnums::STATUS_WAITING_SUBMITTED,
                    Enums::WF_STATE_PENDING,
                    Enums::WF_STATE_APPROVED,
                ],
                'pay_status'  => [
                    Enums::PAYMENT_PAY_STATUS_PAY,
                    Enums::PAYMENT_PAY_STATUS_PENDING,
                ],
                'product_ids' => array_values($accommodation_ids),
            ])
            ->inWhere('rel.serial_no', array_values($trip_no_list));

        if ($is_filter_order) {
            $roommate_builder->andWhere('r.no != :order_no:', ['order_no' => $order_no]);
        }

        $roommate_no_item = $roommate_builder->getQuery()->execute()->toArray();
        return array_column($roommate_no_item, 'serial_no');
    }

    /**
     * 校验差旅实质提交报销时, 差旅单号和费用明细混合校验
     * @param array $travel_serial_no_map
     * @return array
     * @throws ValidationException
     */
    public function checkTravelMixedSubmission(array $travel_serial_no_map)
    {
        if (empty($travel_serial_no_map)) {
            return true;
        }

        $expense_ids = EnumsService::getInstance()->getSettingEnvValueIds('reimbursement_trip_forbid_same_time_submit_expense_ids');
        $expense_ids = array_unique(array_filter($expense_ids));
        if (count($expense_ids) <= 1) {
            return true;
        }

        $name_field   = get_lang_field_name('name_', static::$language, 'th', 'cn');
        $expense_list = BudgetObjectProduct::find([
            'conditions' => 'id IN ({ids:array})',
            'bind'       => ['ids' => array_values($expense_ids)],
            'columns'    => ['id', "{$name_field} as name"],
        ])->toArray();
        $expense_list = array_column($expense_list, 'name', 'id');

        foreach ($travel_serial_no_map as $key => $val) {
            $product_ids   = array_filter(array_unique($val));
            $intersect_ids = array_intersect($product_ids, $expense_ids);
            if (count($intersect_ids) <= 1) {
                continue;
            }

            $intersect_name_list = [];
            foreach ($intersect_ids as $id) {
                $intersect_name_list[] = $expense_list[$id] ?? '';
            }

            $intersect_name = implode('、', $intersect_name_list);
            throw new ValidationException(static::$t->_('reimbursement_save_error_014',
                ['serial_no' => $key, 'product_name' => $intersect_name]), ErrCode::$VALIDATE_ERROR);
        }

        return true;
    }

    /**
     * 校验差旅实质提交报销时, 超出报销时间说明
     * @param string $travel_end_date
     * @param string $travel_reimbursement_overdue_remark
     * @param string $first_apply_date
     * @return array
     * @throws ValidationException
     */
    public function checkTravelReimbursementOverdueRemark(
        string $travel_end_date,
        string $travel_reimbursement_overdue_remark,
        string $first_apply_date
    ) {
        if (empty($travel_end_date)) {
            throw new ValidationException(static::$t->_('business_setting_save_error_014', ['travel_end_at' => $travel_end_date]),
                ErrCode::$VALIDATE_ERROR);
        }

        if (empty($first_apply_date)) {
            throw new ValidationException(static::$t->_('params_error', ['param' => 'first_apply_date']), ErrCode::$VALIDATE_ERROR);
        }

        static $travel_limit_time_rule = [];
        if (empty($travel_limit_time_rule)) {
            $travel_limit_time_rule = EnumsService::getInstance()->getReimbursementEnums()['trip_time_limit_rule'] ?? [];
        }

        if ($travel_limit_time_rule['is_limit'] == ReimbursementEnums::TRIP_TIME_IS_LIMIT) {
            $remark_length = mb_strlen($travel_reimbursement_overdue_remark);
            if ($remark_length > 1000) {
                throw new ValidationException(static::$t->_('business_setting_save_error_015'), ErrCode::$VALIDATE_ERROR);
            }

            $limit_date = date("Y-m-{$travel_limit_time_rule['next_month_specified_date']}", strtotime($travel_end_date . ' +1 month'));
            if ((strtotime($first_apply_date) > strtotime($limit_date)) && $remark_length < 1) {
                throw new ValidationException(static::$t->_('business_setting_save_error_016'), ErrCode::$VALIDATE_ERROR);
            }
        }

        return true;
    }

    /**
     * 校验提交的共同住宿出差单号是否可用
     * @param array $check_params
     * @param array $travel_roommate_item 共同住宿出差单
     * @return array
     * @throws ValidationException
     */
    public function checkTravelRoommateNo(array $check_params, array $travel_roommate_item)
    {
        $travel_serial_no = $check_params['travel_serial_no'];

        static $travel_roommateno_map = [];

        $can_use_serial_no_list = $this->getApplyRoommateBusinessTrip($check_params, true);

        $error_no_list = [];
        foreach ($travel_roommate_item as $item) {
            if (!in_array($item['serial_no'], $can_use_serial_no_list)) {
                $error_no_list[] = $item['serial_no'];
            }

            $travel_roommateno_map[$item['serial_no']][] = $travel_serial_no;

            // 共同住宿出差申请编号 不可同时 关联不同的出差编号
            if (count(array_unique($travel_roommateno_map[$item['serial_no']])) > 1) {
                throw new ValidationException(static::$t->_('reimbursement_save_error_013', ['serial_no' => $item['serial_no']]),
                    ErrCode::$VALIDATE_ERROR);
            }
        }

        if (!empty($error_no_list)) {
            throw new ValidationException(static::$t->_('reimbursement_save_error_010', ['error_no' => implode(',', $error_no_list)]),
                ErrCode::$VALIDATE_ERROR);
        }

        return true;
    }

    /**
     * 共同住宿出差单号: 获取报销在途的
     * @param $serial_no_list
     * @param $submit_type
     * @param $order_no
     * @return array
     */
    public function getTravelRoommateUsed($serial_no_list, $submit_type, $order_no)
    {
        if (empty($serial_no_list)) {
            return [];
        }

        $lodging_fee_ids = static::getDetailTypeItem(ReimbursementEnums::SPECIAL_EXPENSE_DETAILS_TYPE_ACCOMMODATION_FEES);
        $lodging_fee_ids = array_filter($lodging_fee_ids);
        if (empty($lodging_fee_ids)) {
            $this->logger->notice('报销管理-获取在途的出差单: 住宿费明细ID未配置[reimbursement_special_expense_details_type]');
            return [];
        }

        $is_filter_order = in_array($submit_type, [ReimbursementEnums::CREATE_ACTION_APPLY_SIGNED, ReimbursementEnums::CREATE_ACTION_MANUAL_SUBMIT]);

        $travel_budget_id = static::getTravelTypeBudgetId();

        // 在途的出差单
        $serial_builder = $this->modelsManager->createBuilder()
            ->from(['detail' => Detail::class])
            ->leftjoin(Reimbursement::class, 'detail.re_id = main.id', 'main')
            ->columns('detail.travel_serial_no')
            ->inWhere('detail.travel_serial_no', $serial_no_list)
            ->andWhere('detail.budget_id = :budget_id:', ['budget_id' => $travel_budget_id])
            ->inWhere('detail.product_id', $lodging_fee_ids)
            ->andWhere('main.status IN ({status:array}) AND main.pay_status IN ({pay_status:array})', [
                'status'     => [
                    ReimbursementEnums::STATUS_WAITING_CONFIRMED,
                    ReimbursementEnums::STATUS_WAITING_SIGNED,
                    ReimbursementEnums::STATUS_WAITING_SUBMITTED,
                    Enums::WF_STATE_PENDING,
                    Enums::WF_STATE_APPROVED,
                ],
                'pay_status' => [
                    Enums::PAYMENT_PAY_STATUS_PENDING,
                    Enums::PAYMENT_PAY_STATUS_PAY,
                ],
            ]);

        if ($is_filter_order) {
            $serial_builder->andWhere('main.no != :order_no:', ['order_no' => $order_no]);
        }

        $serial_no_item = $serial_builder->getQuery()->execute()->toArray();
        $serial_no_item = array_column($serial_no_item, 'travel_serial_no');

        // 在途的共同住宿出差单
        $roommate_builder = $this->modelsManager->createBuilder()
            ->from(['roommate_rel' => ReimbursementDetailTravelRoommateRelModel::class])
            ->leftjoin(Reimbursement::class, 'roommate_rel.re_id = main.id', 'main')
            ->columns('roommate_rel.serial_no')
            ->inWhere('roommate_rel.serial_no', $serial_no_list)
            ->inWhere('roommate_rel.product_id', $lodging_fee_ids)
            ->andWhere('main.status IN ({status:array}) AND main.pay_status IN ({pay_status:array})', [
                'status'     => [
                    ReimbursementEnums::STATUS_WAITING_CONFIRMED,
                    ReimbursementEnums::STATUS_WAITING_SIGNED,
                    ReimbursementEnums::STATUS_WAITING_SUBMITTED,
                    Enums::WF_STATE_PENDING,
                    Enums::WF_STATE_APPROVED,
                ],
                'pay_status' => [
                    Enums::PAYMENT_PAY_STATUS_PENDING,
                    Enums::PAYMENT_PAY_STATUS_PAY,
                ],
            ]);

        if ($is_filter_order) {
            $roommate_builder->andWhere('main.no != :order_no:', ['order_no' => $order_no]);
        }

        $roommate_item = $roommate_builder->getQuery()->execute()->toArray();
        $roommate_item = array_column($roommate_item, 'serial_no');

        return array_unique(array_filter(array_merge($serial_no_item, $roommate_item)));
    }

    /**
     * 处理网点支援数据列表
     * @param $list
     * @return array
     */
    protected function handleStoreSupportList($list)
    {
        if (empty($list)) {
            return [];
        }

        $support_store_ids = array_column($list, 'support_store_id');
        $staff_store_ids   = array_column($list, 'staff_store_id');
        $store_ids         = array_unique(array_merge($support_store_ids, $staff_store_ids));

        $sys_store_list = (new StoreRepository())->getStoreListByIds($store_ids, Enums::STORE_STATE_ALL);

        $staff_info_ids = array_column($list, 'staff_info_id');
        $staff_list     = (new HrStaffRepository())->getStaffListByStaffIds($staff_info_ids);

        $job_title_ids  = array_column($list, 'job_title_id');
        $job_title_list = (new HrJobTitleRepository())->getJobTitleByIds($job_title_ids, false);

        foreach ($list as $key => &$value) {
            if ($value['support_store_id'] == Enums::HEAD_OFFICE_STORE_FLAG) {
                $support_store_name = Enums::PAYMENT_HEADER_STORE_NAME;
            } else {
                $support_store_name = $sys_store_list[$value['support_store_id']]['name'] ?? '';
            }

            $value['support_store_name'] = $support_store_name;

            if ($value['staff_store_id'] == Enums::HEAD_OFFICE_STORE_FLAG) {
                $staff_store_name = Enums::PAYMENT_HEADER_STORE_NAME;
            } else {
                $staff_store_name = $sys_store_list[$value['staff_store_id']]['name'] ?? '';
            }

            $value['staff_store_name'] = $staff_store_name;

            $value['staff_info_name'] = $staff_list[$value['staff_info_id']]['name'] ?? '';

            $value['job_title_name'] = $job_title_list[$value['job_title_id']]['job_name'] ?? '';

            $value['status_text'] = static::$t->_('by_state_' . $value['status']);

            $value['is_stay_text'] = static::$t->_('staff_support_store_is_stay_' . $value['is_stay']);

            $value['transportation_mode_text'] = !empty($value['transportation_mode']) ? static::$t->_('transportation_mode_' . $value['transportation_mode']) : '';
        }

        return $list;
    }

    /**
     *
     */

    /**
     * 报销单下关联的支援单号根据明细行分组
     * @param $list
     * @return array
     */
    public function formatDetailSupportRel($list)
    {
        if (empty($list)) {
            return [];
        }

        $result = [];
        foreach ($list as $val) {
            $result[$val['detail_id']][] = $val['support_serial_no'];
        }

        return $result;
    }

    /**
     * 报销单下关联的支援单号根据明细行分组
     * @param $list
     * @param bool $is_detail_map
     * @return array
     */
    public function formatTravelRoommateRel($list, bool $is_detail_map = true)
    {
        if (empty($list)) {
            return [];
        }

        $result = [];
        foreach ($list as $val) {
            if ($is_detail_map) {
                $result[$val['detail_id']][] = [
                    'serial_no'        => $val['serial_no'],
                    'apply_staff_id'   => $val['apply_staff_id'],
                    'apply_staff_name' => $val['apply_staff_name'],
                ];
            } else {
                $result[] = [
                    'serial_no'        => $val['serial_no'],
                    'apply_staff_id'   => $val['apply_staff_id'],
                    'apply_staff_name' => $val['apply_staff_name'],
                    'confirm_status'   => $val['confirm_status'],
                    'confirm_at'       => $val['confirm_at'],
                ];
            }
        }

        return $result;
    }

    /** 
     * 校验银行手续费实质的额度
     * @param array $bank_charges_expense
     * @return bool|void
     * @throws ValidationException
     */
    public function checkBankChargesExpense(array $bank_charges_expense)
    {
        if (empty($bank_charges_expense)) {
            return true;
        }

        // 需校验的网点类型配置
        $limit_store_categories = EnumsService::getInstance()->getSettingEnvValueMap('reimbursement_limit_bank_charges_store_categories');
        $limit_store_categories = array_column($limit_store_categories, 'value');

        // 所有网点
        $store_ids       = array_filter(array_column($bank_charges_expense, 'cost_store_n_id'));
        $store_info_list = (new StoreRepository())->getStoreListByIds($store_ids, Enums::STORE_STATE_ALL);

        $store_amount_item = [];
        foreach ($bank_charges_expense as $value) {
            $store_info = $store_info_list[$value['cost_store_n_id']] ?? [];
            if (empty($store_info) || !in_array($store_info['category'], $limit_store_categories)) {
                continue;
            }

            $store_item = $store_amount_item[$value['cost_store_n_id']] ?? [];
            if (empty($store_item)) {
                $store_item = [
                    'sum_amount' => $value['amount'],
                    'store_name' => $value['cost_store_n_name'],
                    'sorting_no' => strtoupper($store_info['sorting_no']),
                ];
            } else {
                $store_item['sum_amount'] = bcadd($store_item['sum_amount'], $value['amount'], 2);
            }

            $store_amount_item[$value['cost_store_n_id']] = $store_item;
        }

        $this->logger->info(['本次提交的待校验银行手续费的费用网点及发票金额总计' => $store_amount_item]);

        if (empty($store_amount_item)) {
            return true;
        }

        $pending_check_store_ids = array_keys($store_amount_item);

        // 上述网点本月报销在途的发票金额总计
        $exist_store_amount_total_item = $this->getCostStoreBankChargesAmountTotal($pending_check_store_ids);
        $this->logger->info(['本次提交的待校验银行手续费的费用网点在途的发票金额总计' => $exist_store_amount_total_item]);

        // 获取网点额度配置
        $store_config_quota = ReimbursementBankChargesStoreQuotaRepository::getInstance()->getListByStoreIds($pending_check_store_ids);

        // 获取网点分拣区额度配置
        $sorting_area_config_quota = ReimbursementBankChargesAreaQuotaRepository::getInstance()->getAll();

        $this->logger->info(['网点额度配置' => $store_config_quota, '网点分拣区域额度配置' => $sorting_area_config_quota]);

        foreach ($store_amount_item as $key => $value) {
            // 是否配置了网点额度
            $config_quote = $store_config_quota[$key] ?? ($sorting_area_config_quota[$value['sorting_no']] ?? null);

            // 额度未配置, 跳过
            if (is_null($config_quote)) {
                continue;
            }

            $store_total_amount_sum = bcadd($value['sum_amount'], $exist_store_amount_total_item[$key] ?? '0', 2);
            if (bccomp($store_total_amount_sum, $config_quote, 2) > 0) {
                $this->logger->info(['网点发票金额总计' => $store_total_amount_sum, '配置额度' => $config_quote]);
                throw new ValidationException(static::$t->_('reimbursement_save_error_050', ['store_name' => $value['store_name']]), ErrCode::$VALIDATE_ERROR);
            }
        }

        return true;
    }

    /**
     * 获取明细行费用网点银行手续费在途的发票金额总计
     * @param array $cost_store_ids
     * @return array
     */
    public function getCostStoreBankChargesAmountTotal(array $cost_store_ids = [])
    {
        if (empty($cost_store_ids)) {
            return [];
        }

        $bank_charges_budget_ids = static::getBankChargesBudgetIds();

        $builder = $this->modelsManager->createBuilder();
        $builder->columns('detail.cost_store_n_id, detail.amount');
        $builder->from(['main' => Reimbursement::class]);
        $builder->leftjoin(Detail::class, 'detail.re_id = main.id', 'detail');
        $builder->inWhere('detail.budget_id', $bank_charges_budget_ids);
        $builder->inWhere('detail.cost_store_n_id', $cost_store_ids);
        $builder->inWhere('main.status', [
            ReimbursementEnums::STATUS_WAIT,
            ReimbursementEnums::STATUS_PASS,
            ReimbursementEnums::STATUS_WAITING_CONFIRMED,
            ReimbursementEnums::STATUS_WAITING_SIGNED,
            ReimbursementEnums::STATUS_WAITING_SUBMITTED,
        ]);
        $builder->inWhere('main.pay_status', [
            ReimbursementEnums::PAY_STATUS_WAIT,
            ReimbursementEnums::PAY_STATUS_PAY,
        ]);

        $builder->andWhere('main.apply_date >= :month_start_date:', ['month_start_date' => date('Y-m-01')]);
        $item = $builder->getQuery()->execute()->toArray();

        $result = [];
        foreach ($item as $v) {
            $amount                        = bcdiv($v['amount'], 1000, 2);
            $store_amount_total            = $result[$v['cost_store_n_id']] ?? '0';
            $result[$v['cost_store_n_id']] = bcadd($store_amount_total, $amount, 2);
        }

        return $result;
    }

    /**
     * 获取申请人共同住宿的可报销出差单
     *
     * @param $params
     * @param bool $only_serial_no
     * @return array
     */
    public function getApplyRoommateBusinessTrip($params, bool $only_serial_no = false)
    {
        $apply_id         = $params['apply_id'];         // 申请人
        $travel_serial_no = $params['travel_serial_no']; // 申请人出差单号

        $serial_no_model = TripModel::findFirst([
            'conditions' => 'serial_no = :serial_no: AND status = :status: AND is_stay = :is_stay: AND business_trip_type != 5',
            'bind'       => [
                'serial_no' => $travel_serial_no,
                'status'    => Enums::TRAFFIC_STATUS_APPROVAL,
                'is_stay'   => ReimbursementEnums::BUSINESS_TRIP_IS_STAY_YES,
            ],
        ]);
        if (empty($serial_no_model) || $serial_no_model->apply_user != $apply_id) {
            return [];
        }

        if (!in_array($serial_no_model->business_trip_type,
            [ReimbursementEnums::TRAVEL_BUSINESS_TRIP_TYPE_IN, ReimbursementEnums::TRAVEL_BUSINESS_TRIP_TYPE_OUT])) {
            return [];
        }

        // 获取共同住宿人所有可报销的单号
        $builder = $this->modelsManager->createBuilder();
        $builder->from(TripModel::class);
        $builder->columns([
            'serial_no',
            'apply_user AS apply_staff_id',
        ]);
        $builder->where('apply_user != :apply_user: AND status = :status: AND business_trip_type != 5', [
            'apply_user' => $apply_id,
            'status'     => Enums::TRAFFIC_STATUS_APPROVAL,
        ]);

        $builder->andWhere('start_time <= :trip_end_time: AND end_time >= :trip_start_time:', [
            'trip_end_time'   => $serial_no_model->end_time,
            'trip_start_time' => $serial_no_model->start_time,
        ]);

        // 境内 OR  境外
        if ($serial_no_model->business_trip_type == ReimbursementEnums::TRAVEL_BUSINESS_TRIP_TYPE_IN) {
            $builder->andWhere('destination_city_code = :destination_city_code:',
                ['destination_city_code' => $serial_no_model->destination_city_code,]);
        } else {
            $builder->andWhere('destination_country = :destination_country:', ['destination_country' => $serial_no_model->destination_country,]);
        }

        $all_item = $builder->getQuery()->execute()->toArray();

        // 获取报销在途的出差单
        $all_serial_no_item = array_column($all_item, 'serial_no');
        $submit_type        = $params['submit_type'] ?? ReimbursementEnums::CREATE_ACTION_ADD_SUBMIT;
        $order_no           = $params['order_no'] ?? '';
        $used_item          = $this->getTravelRoommateUsed($all_serial_no_item, $submit_type, $order_no);

        if (!empty($used_item)) {
            foreach ($all_item as $key => $val) {
                if (in_array($val['serial_no'], $used_item)) {
                    unset($all_item[$key]);
                }
            }

            $all_item = array_values($all_item);
        }

        if ($only_serial_no) {
            return array_column($all_item, 'serial_no');
        }

        // 出差申请人姓名
        if (!empty($all_item)) {
            $apply_user_item = (new HrStaffRepository())->getStaffListByStaffIds(array_column($all_item, 'apply_staff_id'));
            foreach ($all_item as &$item) {
                $item['apply_staff_name'] = $apply_user_item[$item['apply_staff_id']]['name'] ?? '';
            }
        }

        return $all_item;
    }

    /**
     * 获取报销-我的申请状态枚举
     */
    public function getStatusItem($menu_type)
    {
        $status_item = ReimbursementEnums::$audit_status_item;
        if (get_country_code() == GlobalEnums::TH_COUNTRY_CODE && $menu_type == self::LIST_TYPE_APPLY) {
            $status_item = ReimbursementEnums::$apply_status_item;
        }

        $item = [];
        foreach ($status_item as $status) {
            $item[] = [
                'code'  => (string)$status,
                'label' => static::$t->_('reimbursement_apply_status_' . $status),
            ];
        }

        return $item;
    }

    /**
     * 报销单状态变更维护
     *
     * @param int $current_action
     * @param int $travel_is_have_roommate
     * @param int $current_status
     * @return int|mixed
     */
    public function getOrderNextStatus(int $current_action, int $travel_is_have_roommate = 0, int $current_status = 0)
    {
        if (get_country_code() != GlobalEnums::TH_COUNTRY_CODE) {
            return $current_status;
        }

        $next_status = 0;
        switch ($current_action) {
            // 新增提交->待确认/待签字
            case ReimbursementEnums::CREATE_ACTION_ADD_SUBMIT:
                if ($travel_is_have_roommate == ReimbursementEnums::TRAVEL_IS_HAVE_ROOMMATE_YES) {
                    $next_status = ReimbursementEnums::STATUS_WAITING_CONFIRMED;
                } else {
                    $next_status = ReimbursementEnums::STATUS_WAITING_SIGNED;
                }
                break;

            // 重新提交/签字完成(自动提交)/手动补充提交->待审核
            case ReimbursementEnums::CREATE_ACTION_RESUBMIT:
            case ReimbursementEnums::CREATE_ACTION_APPLY_SIGNED:
            case ReimbursementEnums::CREATE_ACTION_MANUAL_SUBMIT:
                $next_status = ReimbursementEnums::STATUS_WAIT;
                break;

            // 签字完成(自动提交失败)->待提交
            case ReimbursementEnums::CREATE_ACTION_APPLY_SIGNED_AUTO_SUBMIT_FAIL:
                $next_status = ReimbursementEnums::STATUS_WAITING_SUBMITTED;
                break;

            // 拒绝确认/超时自动拒绝确认/发起人作废/申请人拒绝签字
            case ReimbursementEnums::CREATE_ACTION_REJECT_CONFIRM:
            case ReimbursementEnums::CREATE_ACTION_TIMEOUT_AUTO_REJECT:
            case ReimbursementEnums::CREATE_ACTION_CREATED_INVALID:
            case ReimbursementEnums::CREATE_ACTION_APPLY_REJECT_SIGN:
                $next_status = ReimbursementEnums::STATUS_DISCARDED;
                break;

            // 全部已确认
            case ReimbursementEnums::CREATE_ACTION_ALL_CONFIRMED:
                $next_status = ReimbursementEnums::STATUS_WAITING_SIGNED;
                break;

            default:
                break;
        }

        return $next_status;
    }


    /**
     * 报销消息维护
     *
     * @param int $msg_scence
     * @param array $staff_ids
     * @param object $main_model
     * @return bool|void
     * @throws BusinessException
     */
    public function sendMsgNotice(int $msg_scence, array $staff_ids, object $main_model)
    {
        if (get_country_code() != GlobalEnums::TH_COUNTRY_CODE) {
            return true;
        }

        if (empty($staff_ids)) {
            return true;
        }

        // 标题双语
        $th_lang = self::getTranslation('th');
        $en_lang = self::getTranslation('en');

        $by_page_domain_v3 = env('by_url_prefix_new');
        $url_params        = "?order_no={$main_model->no}&src=msg";

        $msg_content_variable = [
            'href_style' => ReimbursementEnums::MSG_CONTENT_HREF_STYLE,
        ];
        switch ($msg_scence) {
            case ReimbursementEnums::MSG_SCENCE_WAITING_CONFIRMED:
                $msg_title_key   = 'reimbursement_roommate_confirm_msg_title';
                $msg_content_key = 'reimbursement_roommate_confirm_msg_text';

                $msg_content_variable['apply_name']            = $main_model->apply_name;
                $msg_content_variable['apply_id']              = $main_model->apply_id;
                $msg_content_variable['order_no']              = $main_model->no;
                $url_path                                      = $by_page_domain_v3 . ReimbursementEnums::MSG_PAGE_PATH_TO_CONFIRM . $url_params;
                $msg_content_variable['roommate_confirm_path'] = $url_path;

                break;
            case ReimbursementEnums::MSG_SCENCE_WAITING_SIGNED:
                $msg_title_key   = 'reimbursement_apply_sign_msg_title';
                $msg_content_key = 'reimbursement_apply_sign_msg_text';

                $msg_content_variable['created_name']    = $main_model->created_name;
                $msg_content_variable['created_id']      = $main_model->created_id;
                $msg_content_variable['order_no']        = $main_model->no;
                $url_path                                = $by_page_domain_v3 . ReimbursementEnums::MSG_PAGE_PATH_TO_SIGN . $url_params;
                $msg_content_variable['apply_sign_path'] = $url_path;

                break;
            case ReimbursementEnums::MSG_SCENCE_REJECT_CONFIRM:
            case ReimbursementEnums::MSG_SCENCE_REJECT_SIGN:
                $msg_title_key   = 'reimbursement_refusal_confirm_msg_title';
                $msg_content_key = 'reimbursement_refusal_confirm_msg_text';

                $msg_content_variable['order_no']         = $main_model->no;
                $url_path                                 = $by_page_domain_v3 . ReimbursementEnums::MSG_PAGE_PATH_VIEW_DETAIL . $url_params;
                $msg_content_variable['view_detail_path'] = $url_path;
                break;
            case ReimbursementEnums::MSG_SCENCE_AUTO_SUBMIT_FAIL:
                $msg_title_key   = 'reimbursement_auto_submit_failed_msg_title';
                $msg_content_key = 'reimbursement_auto_submit_failed_msg_text';

                $msg_content_variable['order_no']         = $main_model->no;
                $url_path                                 = $by_page_domain_v3 . ReimbursementEnums::MSG_PAGE_PATH_VIEW_DETAIL . $url_params;
                $msg_content_variable['view_detail_path'] = $url_path;

                break;
            default:
                throw new BusinessException("消息场景有误, 请确认[{$msg_scence}]", ErrCode::$BUSINESS_ERROR);
        }

        $msg_title = $th_lang->_($msg_title_key) . ' ' . $en_lang->_($msg_title_key);

        // 收信人最新登录BY/KIT的语言
        $msg_content_template = ReimbursementEnums::MSG_CONTENT_TEMPLATE;

        $push_url_path = str_replace('&src=msg', '&src=push', $url_path);
        $push_message_scheme = !empty($push_url_path) ? 'flashbackyard://fe/html?url=' . urlencode($push_url_path) : '';

        $msg_log_data = [];
        $queue_name   = $this->getRedisMsgQueueName();
        foreach ($staff_ids as $staff_id) {
            UserService::getInstance()->appendReddotPrehotCache($staff_id, RedisKey::OA_REDDOT_PREHOT_CACHE_SCENCE_REIMBURSEMENT_APPLY);

            $staff_mobile_lang = StaffLangService::getInstance()->getLatestMobileLang($staff_id, 'en');
            $staff_lang        = self::getTranslation($staff_mobile_lang);

            $message_content = $staff_lang->_($msg_content_key, $msg_content_variable);

            // 站内信
            $kit_params = [
                'staff_users'     => [['id' => $staff_id]],
                'message_title'   => $msg_title,
                'message_content' => addslashes(str_replace('MESSAGE_CONTENT_TEXT', $message_content, $msg_content_template)),
                'category'        => '-1',
            ];

            // push
            $push_params = [
                'staff_info_id'   => $staff_id,
                'message_title'   => $msg_title,
                'message_content' => '',
                'message_scheme'  => $push_message_scheme,
            ];

            $msg_params = [
                'kit_params'  => $kit_params,
                'push_params' => $push_params,
            ];

            $msg_id         = generate_uuid('BX_MSG_');
            $msg_log_data[] = [
                'msg_id'     => $msg_id,
                'order_no'   => $main_model->no,
                'msg_scence' => $msg_scence,
                'staff_id'   => $staff_id,
                'msg_params' => json_encode($msg_params, JSON_UNESCAPED_UNICODE),
                'created_at' => date('Y-m-d H:i:s'),
            ];

            $queue_data = [
                'msg_id'     => $msg_id,
                'created_at' => date('Y-m-d H:i:s'),
                'msg_params' => $msg_params,
            ];


            if (!$this->setRedisQueueData($queue_name, $queue_data)) {
                $this->logger->notice([
                    'reimbursement_send_msg_sotice' => '入队失败,请注意消息补发',
                    'order_no'                      => $main_model->no,
                    'msg_scence'                    => $msg_scence,
                    'staff_id'                      => $staff_id,
                    'queue_data'                    => $queue_data,
                ]);
            } else {
                $this->logger->info(['reimbursement_send_msg_sotice' => $queue_data, 'res' => '入队成功', 'msg_scence' => $msg_scence]);
            }
        }

        $reimbursement_msg_log_model = new ReimbursementMsgLogModel();
        if (!empty($msg_log_data) && $reimbursement_msg_log_model->batch_insert($msg_log_data) === false) {
            throw new BusinessException('消息写入失败, 请排查, data=' . json_encode($msg_log_data, JSON_UNESCAPED_UNICODE), ErrCode::$BUSINESS_ERROR);
        }

        return true;
    }

    /**
     * 报销创建流程 不同环节 相应 事件处理
     *
     * @param $current_action
     * @param $biz_params
     * @param $amount_info
     * @param $user
     * @param array $other_params
     * @return bool
     * @throws BusinessException
     * @throws ValidationException
     */
    public function createEventCallBack($current_action, $biz_params, $amount_info, $user, array $other_params = [])
    {
        if (get_country_code() == GlobalEnums::TH_COUNTRY_CODE) {
            switch ($current_action) {
                // 新增提交, 预算仅做一次校验
                case ReimbursementEnums::CREATE_ACTION_ADD_SUBMIT:
                    $biz_params['is_submit'] = $biz_params['is_submit'] ?? 0;
                    if ($biz_params['is_submit'] == 0) {
                        $this->checkBudgets($biz_params, $amount_info, $user);
                    }
                    break;

                // 重新提交
                case ReimbursementEnums::CREATE_ACTION_RESUBMIT:
                    $this->checkBudgets($biz_params, $amount_info, $user);
                    $this->recommitWorkflowRequest($biz_params['no'], $user, $other_params);
                    break;

                // 签字完成(自动提交)/手动提交
                case ReimbursementEnums::CREATE_ACTION_APPLY_SIGNED:
                case ReimbursementEnums::CREATE_ACTION_MANUAL_SUBMIT:
                    // 预算占用
                    $this->checkBudgets($biz_params, $amount_info, $user);

                    // 自动生成附件
                    $this->autoGenerateOrderAttachment($biz_params, $other_params);

                    // 创建审批流
                    $this->createWorkflowRequest($biz_params['no'], $user);
                    break;
                default:
                    throw new BusinessException("current_action错误[{$current_action}], 请排查", ErrCode::$BUSINESS_ERROR);
            }
        } else {
            switch ($current_action) {
                case ReimbursementEnums::CREATE_ACTION_ADD_SUBMIT:
                    $this->checkBudgets($biz_params, $amount_info, $user);
                    $this->createWorkflowRequest($biz_params['no'], $user);
                    break;
                case ReimbursementEnums::CREATE_ACTION_RESUBMIT:
                    $this->checkBudgets($biz_params, $amount_info, $user);
                    $this->recommitWorkflowRequest($biz_params['no'], $user, $other_params);
                    break;
                default:
                    throw new BusinessException("current_action错误[{$current_action}], 请排查", ErrCode::$BUSINESS_ERROR);
            }
        }

        return true;
    }

    /**
     * 报销预算占用
     *
     * @param $biz_params
     * @param $amount_info
     * @param $user
     * @return bool
     * @throws ValidationException
     */
    public function checkBudgets($biz_params, $amount_info, $user)
    {
        $is_submit  = (int)$biz_params['is_submit'] ?? 0;
        $order_type = BudgetService::ORDER_TYPE_1;
        return (new BudgetService())->checkBudgets($biz_params['no'], $amount_info, $order_type, $biz_params, $is_submit, $user['id']);
    }

    /**
     * 报销创建审批流
     * @param $order_no
     * @param $user
     * @return bool
     * @throws BusinessException
     * @throws ValidationException
     */
    public function createWorkflowRequest($order_no, $user)
    {
        $model = Reimbursement::findFirst([
            'conditions' => 'no = :no:',
            'bind'       => ['no' => $order_no],
        ]);

        if (empty($model)) {
            throw new BusinessException("报销创建审批流异常[新增]-单据不存在[no-{$order_no}]", ErrCode::$BUSINESS_ERROR);
        }

        $flow_res = (new ReimbursementFlowService())->createRequest($model, $user);
        if ($flow_res == false) {
            throw new BusinessException("报销创建审批流失败[新增]-[no-{$order_no}]", ErrCode::$BUSINESS_ERROR);
        }

        return true;
    }

    /**
     * 报销创建审批流
     * @param $order_no
     * @param $user
     * @param $other_params
     * @return bool
     * @throws BusinessException
     * @throws ValidationException
     */
    public function recommitWorkflowRequest($order_no, $user, $other_params)
    {
        $model = Reimbursement::findFirst([
            'conditions' => 'no = :no:',
            'bind'       => ['no' => $order_no],
        ]);

        if (empty($model)) {
            throw new BusinessException("报销创建审批流异常[重新提交]-单据不存在[no-{$order_no}]", ErrCode::$BUSINESS_ERROR);
        }

        // 比较修改前后的数据, 是否改了敏感字段
        $before_main_data   = $other_params['before_main_data'] ?? [];
        $before_detail_data = $other_params['before_detail_data'] ?? [];
        $check_sensitive    = $this->checkSensitiveFields($before_main_data, $model, $before_detail_data);
        $flow_res           = (new ReimbursementFlowService())->recommit($model, $user, $check_sensitive);
        if ($flow_res == false) {
            throw new BusinessException("报销创建审批流失败[重新提交]-[no-{$order_no}]", ErrCode::$BUSINESS_ERROR);
        }

        return true;
    }

    /**
     * 是否修改了敏感字段
     * @param array $before_main_data
     * @param object $model
     * @param array $before_details
     * @return bool true:修改了敏感字段 false:没有修改 null:没有配置或配置为空
     */
    private function checkSensitiveFields(array $before_main_data, object $model, array $before_details)
    {
        //获取敏感字段配置
        $sensitive_fields = EnumsService::getInstance()->getSettingEnvValueMap('reimbursement_recommit_sensitive_fields');
        if (empty($sensitive_fields['main']) && empty($sensitive_fields['detail'])) {
            return null;
        }

        if (!empty($sensitive_fields['main'])) {
            //主表中的字段
            foreach ($sensitive_fields['main'] as $main_field) {
                //如果存在字段再比较
                if (isset($before_main_data[$main_field]) && isset($model->$main_field) && $before_main_data[$main_field] != $model->$main_field) {
                    return true;
                }
            }
        }

        if (!empty($sensitive_fields['detail'])) {
            //查询本次修改后的详情,在同一个事务里,能查到本次尚未提交的详情
            $details = $model->getDetails()->toArray();

            //详情条数不同,肯定不一样
            if (count($details) != count($before_details)) {
                return true;
            }

            //如果条数一样,比较内容
            foreach ($sensitive_fields['detail'] as $detail_field) {
                //取要比较的字段
                $new_field = array_column($details, $detail_field);
                $old_field = array_column($before_details, $detail_field);
                //排序后拼接成字符串
                sort($new_field);
                sort($old_field);
                //用方法名拼接起来
                $new_field = implode('|checkSensitiveFields|', $new_field);
                $old_field = implode('|checkSensitiveFields|', $old_field);
                //判断修改前后是否相同
                if ($new_field != $old_field) {
                    return true;
                }
            }
        }

        return false;
    }

    /**
     * 币种、金额、汇率 转换计算
     *
     * @param $currency
     * @param $date
     * @param $amount
     * @return array
     * @throws ValidationException
     */
    public function conversionAmountByExchangeRate($currency, $date, $amount)
    {
        static $exchange_rate_item = [];
        static $adjustment_factor_val = null;

        $item_key = $currency . '_' . $date;
        if (!isset($exchange_rate_item[$item_key])) {
            $exchange_rate = SysExchangeRateRepository::getInstance()->getExchangeRateByCurrencyAndSellingDate($currency, $date);
            if (is_null($exchange_rate)) {
                throw new ValidationException(static::$t->_('reimbursement_save_error_015'), ErrCode::$VALIDATE_ERROR);
            }

            $exchange_rate_item[$item_key] = $exchange_rate;
        } else {
            $exchange_rate = $exchange_rate_item[$item_key];
        }

        if (is_null($adjustment_factor_val)) {
            $adjustment_factor_val = EnumsService::getInstance()->getSettingEnvValue('reimbursement_exchange_rate_adjustment_factor_val');
        }

        $system_parameter_exchange_rate = bcmul($exchange_rate, $adjustment_factor_val, 3);

        $system_exchange_rate = bcadd($exchange_rate, $system_parameter_exchange_rate, 3);

        $system_exchange_rate_amount = bcmul($amount, $system_exchange_rate, 3);

        return [
            'amount'        => number_format($system_exchange_rate_amount, 2, '.', ''),
            'exchange_rate' => $exchange_rate,
        ];
    }

    /**
     * 境内机票金额校验
     *
     * @param $params
     * @param string $default_currency
     * @return array
     * @throws ValidationException
     */
    public function checkDomesticAirTicket($params, string $default_currency = '')
    {
        $result = [
            'travel_serial_no'           => $params['travel_serial_no'],
            'is_exceeds_standard_amount' => false,
        ];

        if (empty($params)) {
            return $result;
        }

        // 获取申请人职位性质
        static $apply_position_type = '';
        if (empty($apply_position_type)) {
            $apply_position_info = HrJobDepartmentRelationRepository::getInstance()->getStaffPositionInfo($params['apply_id']);
            if (empty($apply_position_info['position_type'])) {
                throw new ValidationException(static::$t->_('reimbursement_save_error_016'), ErrCode::$VALIDATE_ERROR);
            }

            $apply_position_type = $apply_position_info['position_type'];
        }

        // 获取境内机票额度配置
        $config_model = ReimbursementDomesticAirTicketQuotaRepository::getInstance()->getOneById(1);
        if (empty($config_model)) {
            return $result;
        }

        $position_type_quota_amount = '0.00';
        switch ($apply_position_type) {
            case ReimbursementEnums::POSITION_TYPE_1:
                $position_type_quota_amount = $config_model->frontline_operation_amount;
                break;
            case ReimbursementEnums::POSITION_TYPE_2:
                $position_type_quota_amount = $config_model->frontline_functional_amount;
                break;
            case ReimbursementEnums::POSITION_TYPE_3:
                $position_type_quota_amount = $config_model->head_office_amount;
                break;
            default:
                break;
        }

        if (empty($default_currency)) {
            $default_currency = GlobalEnums::getSysDefaultCurrency();
        }

        $total_amount = '0.00';
        foreach ($params['domestic_air_ticket_item'] as $val) {
            // 非新增提交,不涉及币种转换, 直接累计
            if ($params['submit_type'] != ReimbursementEnums::CREATE_ACTION_ADD_SUBMIT) {
                $total_amount = bcadd($total_amount, $val['amount'], 2);
                continue;
            }

            // 新增提交: 金额转换计算
            if ($val['invoice_currency'] != $default_currency) {
                $val['amount'] = $this->conversionAmountByExchangeRate($val['invoice_currency'], $val['invoice_date'], $val['amount'])['amount'];
            }

            $total_amount = bcadd($total_amount, $val['amount'], 2);
        }

        if (bccomp($total_amount, $position_type_quota_amount, 2) > 0) {
            $result['is_exceeds_standard_amount'] = true;
        }

        return $result;
    }

    /**
     * 租车费金额校验
     *
     * @param $params
     * @param string $default_currency
     * @return array
     * @throws ValidationException
     */
    public function checkCarRental($params, string $default_currency = '')
    {
        $result = [
            'travel_serial_no'           => $params['travel_serial_no'],
            'is_exceeds_standard_amount' => false,
        ];

        if (empty($params)) {
            return $result;
        }

        // 获取申请人职位信息
        static $apply_position_info = null;
        if (is_null($apply_position_info)) {
            $apply_position_info = HrJobDepartmentRelationRepository::getInstance()->getStaffPositionInfo($params['apply_id']);
        }

        if (empty($apply_position_info['position_type'])) {
            throw new ValidationException(static::$t->_('reimbursement_save_error_016'), ErrCode::$VALIDATE_ERROR);
        }

        // 出差单
        static $trip_list = [];
        $trip_info = $trip_list[$params['travel_serial_no']] ?? [];
        if (empty($trip_info)) {
            $trip_info = BusinessTripRepository::getInstance()->getInfoBySerialNo($params['travel_serial_no']);
            if (empty($trip_info)) {
                throw new ValidationException(static::$t->_('biz_order_data_is_null', ['biz_id' => $params['travel_serial_no']]),
                    ErrCode::$VALIDATE_ERROR);
            }

            $trip_list[$params['travel_serial_no']] = $trip_info;
        }

        // 获取申请人出差开始日期时的职级
        static $apply_job_level_list = [];

        $apply_job_level_key = $params['apply_id'] . '_' . $trip_info['start_time'];
        $apply_job_level     = $apply_job_level_list[$apply_job_level_key] ?? null;
        if (is_null($apply_job_level)) {
            $job_title_grade = (new HrStaffRepository())->getStaffJobGradeByDate($params['apply_id'], $trip_info['start_time']);
            if (!empty($job_title_grade)) {
                $apply_job_level = $job_title_grade;
            } else {
                $apply_job_level = $apply_position_info['job_title_grade_v2'];
            }

            $apply_job_level_list[$apply_job_level_key] = $apply_job_level;
        }

        // 获取职级 + 职位性质 的租车额度
        $config_model = ReimbursementCarRentalQuotaRepository::getInstance()->getOneByType($apply_position_info['position_type'], $apply_job_level);
        if (empty($config_model)) {
            $result['is_exceeds_standard_amount'] = true;
            $this->logger->notice([
                'logger_key'      => 'reimbursement_car_rental_quota',
                'message'         => '报销-租车费校验, 租车费额度配置为空, 请联系产品',
                'position_type'   => $apply_position_info['position_type'],
                'apply_job_level' => $apply_job_level,
            ]);
            return $result;
        }

        if ($config_model->is_required_email_approval == ReimbursementEnums::IS_REQUIRED_EMAIL_APPROVAL_YES) {
            $result['is_exceeds_standard_amount'] = true;
            return $result;
        }

        // 总额度 = 每天租车额度 * 出差天数
        $total_quota_amount = bcmul($config_model->standards_amount, $trip_info['days_num'], 2);

        if (empty($default_currency)) {
            $default_currency = GlobalEnums::getSysDefaultCurrency();
        }

        $total_amount = '0.00';
        foreach ($params['car_rental_item'] as $val) {
            // 非新增提交,不涉及币种转换, 直接累计
            if ($params['submit_type'] != ReimbursementEnums::CREATE_ACTION_ADD_SUBMIT) {
                $total_amount = bcadd($total_amount, $val['amount'], 2);
                continue;
            }

            // 新增提交: 金额转换计算
            if ($val['invoice_currency'] != $default_currency) {
                $val['amount'] = $this->conversionAmountByExchangeRate($val['invoice_currency'], $val['invoice_date'], $val['amount'])['amount'];
            }

            $total_amount = bcadd($total_amount, $val['amount'], 2);
        }

        if (bccomp($total_amount, $total_quota_amount, 2) > 0) {
            $result['is_exceeds_standard_amount'] = true;
        }

        return $result;
    }

    /**
     * 出差住宿费金额校验
     *
     * @param $params
     * @param string $default_currency
     * @return array
     * @throws ValidationException
     */
    public function checkTripAccommodation($params, string $default_currency = '')
    {
        $result = [
            'travel_serial_no'           => $params['travel_serial_no'],
            'is_exceeds_standard_amount' => false,
        ];

        if (empty($params)) {
            return $result;
        }

        // 获取申请人职位性质
        static $apply_position_type = null;
        if (is_null($apply_position_type)) {
            $apply_position_info = HrJobDepartmentRelationRepository::getInstance()->getStaffPositionInfo($params['apply_id']);
            if (empty($apply_position_info['position_type'])) {
                throw new ValidationException(static::$t->_('reimbursement_save_error_016'), ErrCode::$VALIDATE_ERROR);
            }

            $apply_position_type = $apply_position_info['position_type'];
        }

        // 获取BY出差单和共同住宿出差单出差日期
        $all_serial_no_list = [$params['travel_serial_no'],];
        foreach ($params['accommodation_item'] as $item) {
            $all_serial_no_list = array_merge($all_serial_no_list, array_column($item['travel_roommate_item'], 'serial_no'));
        }
        $all_serial_no_list = array_filter($all_serial_no_list);
        $trip_list = BusinessTripRepository::getInstance()->getListBySerialNoList($all_serial_no_list);
        $trip_list = array_column($trip_list, null, 'serial_no');
        $apply_serial_no_info = $trip_list[$params['travel_serial_no']] ?? [];
        if (empty($apply_serial_no_info)) {
            throw new ValidationException(static::$t->_('biz_order_data_is_null', ['biz_id' => $params['travel_serial_no']]), ErrCode::$VALIDATE_ERROR);
        }

        // 计算最大职级
        $hr_staff_repository = (new HrStaffRepository());

        // 获取申请人和共同住宿人的当前职级
        $satff_grade_list = $hr_staff_repository->getStaffListByStaffIds(array_column($trip_list, 'apply_user'));
        $satff_grade_list = array_column($satff_grade_list, 'job_title_grade_v2', 'staff_info_id');

        // 获取出差人的职级
        static $job_level_list = [];
        foreach ($trip_list as $trip_info) {
            $job_level_key = $trip_info['apply_user'] . '_' . $trip_info['start_time'];
            $job_level = $job_level_list[$job_level_key] ?? null;
            if (is_null($job_level)) {
                $job_title_grade = $hr_staff_repository->getStaffJobGradeByDate($trip_info['apply_user'], $trip_info['start_time']);
                if (!empty($job_title_grade)) {
                    $job_level = $job_title_grade;
                } else {
                    $job_level = $satff_grade_list[$trip_info['apply_user']] ?? null;
                }

                $job_level_list[$job_level_key] = $job_level;
            }
        }

        $max_job_level = max($job_level_list);

        // 境内
        if ($apply_serial_no_info['business_trip_type'] == ReimbursementEnums::TRAVEL_BUSINESS_TRIP_TYPE_IN) {
            // 获取区对应的府
            $city_info = SysCityRepository::getInstance()->getInfoByCode($apply_serial_no_info['destination_city_code'] ?? '');

            // 获取境内区域类型
            $area_type = $this->getDomesticAccommodationAreaType($city_info['province_code'] ?? '', $city_info['code'] ?? '');

            $quota_model = ReimbursementDomesticAccommodationQuotaRepository::getInstance()->getOneByType($apply_position_type, $area_type, $max_job_level);
            if (empty($quota_model)) {
                $result['is_exceeds_standard_amount'] = true;
                $this->logger->notice([
                    'logger_key'    => 'reimbursement_domestic_accommodation_quota',
                    'message'       => '报销-出差住宿费金额校验, 境内住宿额度配置为空, 请联系产品',
                    'position_type' => $apply_position_type,
                    'area_type'     => $area_type,
                    'max_job_level' => $max_job_level,
                ]);

                return $result;
            }

        } else {
            // 境外
            $overseas_area_model = ReimbursementOverseasAccommodationAreaRepository::getInstance()->getOneByCountryId($apply_serial_no_info['destination_country']);
            if (empty($overseas_area_model)) {
                $result['is_exceeds_standard_amount'] = true;
                $this->logger->notice([
                    'logger_key'          => 'reimbursement_overseas_accommodation_area',
                    'message'             => '报销-出差住宿费金额校验, 境外住宿区域配置为空, 请联系产品',
                    'destination_country' => $apply_serial_no_info['destination_country'],
                ]);

                return $result;
            }

            $accommodation_type = $overseas_area_model->accommodation_type;
            $quota_model = ReimbursementOverseasAccommodationQuotaRepository::getInstance()->getOneByType($apply_position_type, $accommodation_type);
            if (empty($quota_model)) {
                $result['is_exceeds_standard_amount'] = true;
                $this->logger->notice([
                    'logger_key'         => 'reimbursement_overseas_accommodation_quota',
                    'message'            => '报销-出差住宿费金额校验, 境外住宿额度配置为空, 请联系产品',
                    'position_type'      => $apply_position_type,
                    'accommodation_type' => $accommodation_type,
                ]);

                return $result;
            }
        }

        $standards_amount = $quota_model->standards_amount ?? '0.00';

        // 总额度
        $total_quota_amount = bcmul(($apply_serial_no_info['days_num'] - 1), $standards_amount, 2);

        if (empty($default_currency)) {
            $default_currency = GlobalEnums::getSysDefaultCurrency();
        }

        $total_amount = '0.00';
        foreach ($params['accommodation_item'] as $val) {
            // 非新增提交,不涉及币种转换, 直接累计
            if ($params['submit_type'] != ReimbursementEnums::CREATE_ACTION_ADD_SUBMIT) {
                $total_amount = bcadd($total_amount, $val['amount'], 2);
                continue;
            }

            // 新增提交: 金额转换计算
            if ($val['invoice_currency'] != $default_currency) {
                $val['amount'] = $this->conversionAmountByExchangeRate($val['invoice_currency'], $val['invoice_date'], $val['amount'])['amount'];
            }

            $total_amount = bcadd($total_amount, $val['amount'], 2);
        }

        if (bccomp($total_amount, $total_quota_amount, 2) > 0) {
            $result['is_exceeds_standard_amount'] = true;
        }

        return $result;
    }

    /**
     * 获取境内住宿区域类型
     * @param $province_code
     * @param $city_code
     * @return int
     */
    protected function getDomesticAccommodationAreaType($province_code, $city_code)
    {
        static $area_type_list = [];

        $area_key  = $province_code . '_' . $city_code;
        $area_type = $area_type_list[$area_key] ?? null;
        if (!is_null($area_type)) {
            return $area_type;
        }

        $area_model = ReimbursementDomesticAccommodationAreaRepository::getInstance()->getOneByAreaCode($province_code, $city_code);
        if (!empty($area_model->area_type)) {
            $area_type_list[$area_key] = $area_model->area_type;
            return $area_model->area_type;
        }

        $area_model = ReimbursementDomesticAccommodationAreaRepository::getInstance()->getOneByAreaCode($province_code, '');
        if (!empty($area_model->area_type)) {
            $area_type_list[$area_key] = $area_model->area_type;
            return $area_model->area_type;
        }

        $area_type_list[$area_key] = ReimbursementEnums::ACCOMMODATION_AREA_TYPE_3;
        return ReimbursementEnums::ACCOMMODATION_AREA_TYPE_3;
    }

    /**
     * 差旅/支援科目下的油费校验
     *
     * @param $expense_info
     * @param $apply_id
     * @return bool
     * @throws ValidationException
     */
    public function checkFuel($expense_info, $apply_id)
    {
        // 油费表单项校验
        $Validation = [
            'fuel_use_date'                          => 'Required|Date|>>>:' . static::$t->_('params_error', ['param' => 'fuel_use_date']),
            'fuel_start'                             => 'Required|StrLenGeLe:1,100|>>>:' . static::$t->_('params_error', ['param' => 'fuel_start']),
            'fuel_end'                               => 'Required|StrLenGeLe:1,100|>>>:' . static::$t->_('params_error', ['param' => 'fuel_end']),
            'fuel_start_mileage_file'                => 'Required|Arr|ArrLen:1|>>>:' . static::$t->_('params_error', ['param' => 'fuel_start_mileage_file']),
            'fuel_start_mileage_file[*].file_name'   => 'Required|StrLenGe:1|>>>:' . static::$t->_('params_error', ['param' => 'file_name']),
            'fuel_start_mileage_file[*].bucket_name' => 'Required|StrLenGe:1|>>>:' . static::$t->_('params_error', ['param' => 'bucket_name']),
            'fuel_start_mileage_file[*].object_key'  => 'Required|StrLenGe:1|>>>:' . static::$t->_('params_error', ['param' => 'object_key']),
            'fuel_end_mileage_file'                  => 'Required|Arr|ArrLen:1|>>>:' . static::$t->_('params_error', ['param' => 'fuel_end_mileage_file']),
            'fuel_end_mileage_file[*].file_name'     => 'Required|StrLenGe:1|>>>:' . static::$t->_('params_error', ['param' => 'file_name']),
            'fuel_end_mileage_file[*].bucket_name'   => 'Required|StrLenGe:1|>>>:' . static::$t->_('params_error', ['param' => 'bucket_name']),
            'fuel_end_mileage_file[*].object_key'    => 'Required|StrLenGe:1|>>>:' . static::$t->_('params_error', ['param' => 'object_key']),
            'fuel_start_mileage'  => 'Required|FloatGeLe:0,*********.99|>>>:' . static::$t->_('params_error', ['param' => 'fuel_start_mileage']),
            'fuel_end_mileage'    => 'Required|FloatGeLe:0,*********.99|>>>:' . static::$t->_('params_error', ['param' => 'fuel_end_mileage']),
            'fuel_mileage'        => 'Required|FloatGeLe:0,*********.99|>>>:' . static::$t->_('params_error', ['param' => 'fuel_mileage']),
            'fuel_vehicle_type'   => 'Required|StrLenGeLe:1,20|>>>:' . static::$t->_('params_error', ['param' => 'fuel_vehicle_type']),
            'fuel_vehicle_number' => 'Required|StrLenGeLe:1,20|>>>:' . static::$t->_('params_error', ['param' => 'fuel_vehicle_number']),
            'fuel_oil_type'       => 'Required|IntIn:1,2,3|>>>:' . static::$t->_('params_error', ['param' => 'fuel_oil_type']),
        ];

        Validation::validate($expense_info, $Validation);

        // 获取申请人职位性质
        static $apply_position_type = '';
        if (empty($apply_position_type)) {
            $apply_position_info = HrJobDepartmentRelationRepository::getInstance()->getStaffPositionInfo($apply_id);
            if (empty($apply_position_info['position_type'])) {
                throw new ValidationException(static::$t->_('reimbursement_save_error_016'), ErrCode::$VALIDATE_ERROR);
            }

            $apply_position_type = $apply_position_info['position_type'];
        }

        $expenses_type = '';
        switch ($expense_info['budget_id']) {
            case static::getTravelTypeBudgetId():
                $expenses_type = ReimbursementEnums::EXPENSES_TYPE_1;
                break;
            case static::getTransportationTypeBudgetId():
                $expenses_type = ReimbursementEnums::EXPENSES_TYPE_2;
                break;
            case static::getSupportTypeBudgetId():
                $expenses_type = ReimbursementEnums::EXPENSES_TYPE_3;
                break;
        }

        $oil_type         = $expense_info['fuel_oil_type'];
        $fuel_quota_model = ReimbursementFuelQuotaRepository::getInstance()->getOneByType($apply_position_type, $expenses_type, $oil_type);

        $standards_amount = !empty($fuel_quota_model) ? $fuel_quota_model->rates : '0.00';
        $quota_amount     = number_format(bcmul($standards_amount, $expense_info['fuel_mileage'], 3), '2', '.', '');

        $default_currency = GlobalEnums::getSysDefaultCurrency();
        if (isset($expense_info['invoice_currency']) && $expense_info['invoice_currency'] != $default_currency) {
            $expense_info['amount'] = $this->conversionAmountByExchangeRate($expense_info['invoice_currency'], $expense_info['invoice_date'], $expense_info['amount'])['amount'];
        }

        if (bccomp($expense_info['amount'], $quota_amount, 2) > 0) {
            return false;
        }

        return true;
    }

    /**
     * 生成PDF文件
     */
    protected function generatePdfFile($form_data, $template_url)
    {
        $template_header = '';
        $template_footer = '';

        $post_data = [
            'pdfName'      => time(),
            'templateUrl'  => $template_url,
            'data'         => $form_data,
            'downLoadData' => [], // v2版本接口 不支持该参数
            "pdfOptions"   => [
                "format"              => 'A4',
                "displayHeaderFooter" => true,
                "printBackground"     => true,
                "headerTemplate"      => $template_header,
                "footerTemplate"      => $template_footer,
            ],
        ];

        $api_path      = '/api/pdf/v2/createPdfByJvppeteer';
        $json_post_data = json_encode($post_data, JSON_UNESCAPED_UNICODE);
        $response_data = CommonService::getInstance()->newPostRequest(env('pdf_rpc_endpoint') . $api_path, $json_post_data);
        if ($response_data['code'] != ErrCode::$SUCCESS) {
            throw new BusinessException("报销生成PDF附件-调用pdf服务失败({$api_path})", ErrCode::$BUSINESS_ERROR);
        }

        $bucket_data = $response_data['data'] ?? [];
        if (empty($bucket_data['bucket_name']) || empty($bucket_data['object_key']) || !isset($bucket_data['content_type'])) {
            throw new BusinessException('报销生成PDF附件-生成pdf文件格式错误', ErrCode::$BUSINESS_ERROR);
        }

        return $bucket_data;
    }

    /**
     * 格式化户口地址
     */
    protected function formatHouseholdAddress($staff_husehold_items)
    {
        $address = '';
        if (empty($staff_husehold_items)) {
            return $address;
        }

        $staff_husehold_items = array_column($staff_husehold_items, 'value', 'item');

        // 获取省市区泰文名称
        $province_code = $staff_husehold_items[ReimbursementEnums::STAFF_ITEM_REGISTER_PROVINCE] ?? '';
        $city_code     = $staff_husehold_items[ReimbursementEnums::STAFF_ITEM_REGISTER_CITY] ?? '';
        $district_code = $staff_husehold_items[ReimbursementEnums::STAFF_ITEM_REGISTER_DISTRICT] ?? '';

        $staff_husehold_items[ReimbursementEnums::STAFF_ITEM_REGISTER_PROVINCE] = SysProvinceRepository::getInstance()->getInfoByCode($province_code)['name'] ?? '';
        $staff_husehold_items[ReimbursementEnums::STAFF_ITEM_REGISTER_CITY]     = SysCityRepository::getInstance()->getInfoByCode($city_code)['name'] ?? '';
        $staff_husehold_items[ReimbursementEnums::STAFF_ITEM_REGISTER_DISTRICT] = SysDistrictRepository::getInstance()->getInfoByCode($district_code)['name'] ?? '';

        // 曼谷 和 非曼谷的地址模板
        $address_tpl_config = EnumsService::getInstance()->getSettingEnvValueMap('reimbursement_husehold_address_template');
        $tpl_config_key     = $province_code == ReimbursementEnums::BANGKOK_PROVINCE_CODE ? strtolower($province_code) : 'other';
        $address_tpl        = $address_tpl_config[$tpl_config_key] ?? '';

        $replace_search_key = [
            ReimbursementEnums::STAFF_ITEM_REGISTER_HOUSE_NUM,
            ReimbursementEnums::STAFF_ITEM_REGISTER_VILLAGE_NUM,
            ReimbursementEnums::STAFF_ITEM_REGISTER_VILLAGE,
            ReimbursementEnums::STAFF_ITEM_REGISTER_ALLEY,
            ReimbursementEnums::STAFF_ITEM_REGISTER_STREET,
            ReimbursementEnums::STAFF_ITEM_REGISTER_DISTRICT,
            ReimbursementEnums::STAFF_ITEM_REGISTER_CITY,
            ReimbursementEnums::STAFF_ITEM_REGISTER_PROVINCE,
            ReimbursementEnums::STAFF_ITEM_REGISTER_POSTCODES,
            '{',
            '}',
        ];

        $replace_values = [];
        foreach ($replace_search_key as $key) {
            $replace_values[] = $staff_husehold_items[$key] ?? '';
        }

        return trim(str_replace($replace_search_key, $replace_values, $address_tpl));
    }

    /**
     * 公司配置预警
     */
    protected function companyInfoConfigWarning($company_id, $company_config)
    {
        $warning_msg = '公司信息配置预警(不影响业务, 请联系产品确认): 公司ID-' . $company_id;
        if (empty($company_id)) {
            $this->logger->notice($warning_msg . ', 公司ID为空');
            return true;
        }

        if (empty($company_config)) {
            $this->logger->notice($warning_msg . ', 公司配置未找到[sys_company_information_config]');
            return true;
        }

        $config_fields = [
            'logo',
            'address_th',
            'address_en',
            'name_th',
            'name_en',
        ];

        $field_data_msg = [];
        foreach ($config_fields as $field) {
            if (empty($company_config[$field])) {
                $field_data_msg[] = $field . ' 的配置项不存在 或 值为空';
            }
        }

        if (!empty($field_data_msg)) {
            $this->logger->notice($warning_msg . ', 配置项异常-' . (implode(' | ', $field_data_msg)) . '[sys_company_information_config]');
        }

        return true;
    }

    /**
     * 生成单据附件(明细行)
     */
    protected function autoGenerateOrderAttachment($order_data, $expense_data)
    {
        // 格式化单据详情数据
        $order_data['expense'] = $expense_data;
        $order_data            = DetailService::getInstance()->handleData($order_data);
        if (isset($order_data['expense_v1'])) {
            $expense_data = $order_data['expense_v1'];
            unset($order_data['expense_v1']);
        } else {
            $expense_data = $order_data['expense'];
            unset($order_data['expense']);
        }

        $format_apply_date = date('d/m/Y', strtotime($order_data['apply_date']));

        // 自动生成的所有附件
        $auto_attachment_files = [];

        // 获取科目类型和明细类型配置
        $travel_budget_id         = static::getTravelTypeBudgetId();
        $transportation_budget_id = static::getTransportationTypeBudgetId();
        $support_budget_id        = static::getSupportTypeBudgetId();

        $fuel_cost_type_budget_ids = [
            $travel_budget_id,
            $transportation_budget_id,
            $support_budget_id,
        ];

        $expense_details_type      = static::getDetailTypeItem();
        $fuel_cost_product_ids     = $expense_details_type[ReimbursementEnums::SPECIAL_EXPENSE_DETAILS_TYPE_FUEL_COST] ?? [];            // 油费
        $overseas_air_product_ids  = $expense_details_type[ReimbursementEnums::SPECIAL_EXPENSE_DETAILS_TYPE_OVERSEAS_AIR_TICKETS] ?? []; // 海外机票
        $domestic_air_product_ids  = $expense_details_type[ReimbursementEnums::SPECIAL_EXPENSE_DETAILS_TYPE_DOMESTIC_AIR_TICKETS] ?? []; // 境内机票
        $train_tickets_product_ids = $expense_details_type[ReimbursementEnums::SPECIAL_EXPENSE_DETAILS_TYPE_TRAIN_TICKETS] ?? [];        // 火车票
        $bus_tickets_product_ids   = $expense_details_type[ReimbursementEnums::SPECIAL_EXPENSE_DETAILS_TYPE_BUS_TICKETS] ?? [];          // 汽车票
        $ferry_tickets_product_ids = $expense_details_type[ReimbursementEnums::SPECIAL_EXPENSE_DETAILS_TYPE_FERRY_TICKETS] ?? [];        // 船票
        $accommodation_product_ids = $expense_details_type[ReimbursementEnums::SPECIAL_EXPENSE_DETAILS_TYPE_ACCOMMODATION_FEES] ?? [];   // 住宿费

        $all_transport_product_ids = array_merge(
            $overseas_air_product_ids,
            $domestic_air_product_ids,
            $train_tickets_product_ids,
            $bus_tickets_product_ids,
            $ferry_tickets_product_ids
        );
        $all_transport_product_ids = array_filter($all_transport_product_ids);

        $air_tickets_product_ids = array_filter(array_merge($overseas_air_product_ids, $domestic_air_product_ids));

        // PDF附件ftl模板配置
        $pdf_ftl_template_config = EnumsService::getInstance()->getSettingEnvValueMap('reimbursement_pdf_ftl_template');

        // 获取公司信息配置
        $company_config_item = EnumsService::getInstance()->getSettingEnvValueMap('sys_company_information_config');

        // 单据的费用所属公司
        $cost_company_config = $company_config_item[$order_data['cost_company_id']] ?? [];
        $this->companyInfoConfigWarning($order_data['cost_company_id'], $cost_company_config);

        // 获取申请人的HR信息(身份证号码/户口地址)
        $hr_staff_repository = new HrStaffRepository();
        $apply_info          = $hr_staff_repository->getStaffById($order_data['apply_id']);
        $apply_identity      = $apply_info['identity'] ?? '';
        $apply_job_level     = $apply_info['job_title_grade_v2'] ?? null;

        $order_data['apply_name'] = $apply_info['name'];

        $apply_husehold_items   = $hr_staff_repository->getStaffItems([$order_data['apply_id']], ReimbursementEnums::$staff_husehold_register_items);
        $apply_husehold_address = $this->formatHouseholdAddress($apply_husehold_items);

        // 申请人职位性质
        $apply_position_type = HrJobDepartmentRelationRepository::getInstance()->getDepartmentJobInfo($apply_info['node_department_id'], $apply_info['job_title'])['position_type'] ?? '';

        $th_lang = static::getTranslation('th');
        $en_lang = static::getTranslation('en');

        $position_type_th = $position_type_en = '';
        if (in_array($apply_position_type, [ReimbursementEnums::POSITION_TYPE_1, ReimbursementEnums::POSITION_TYPE_2])) {
            $position_type_th = $th_lang->_('global_firstline');
            $position_type_en = $en_lang->_('global_firstline');
        } elseif ($apply_position_type == ReimbursementEnums::POSITION_TYPE_3) {
            $position_type_th = $th_lang->_('global_headquarters');
            $position_type_en = $en_lang->_('global_headquarters');
        }

        // 获取差旅单信息
        $travel_serial_no_list = array_column($expense_data, 'travel_serial_no');
        $travel_serial_no_list = BusinessTripRepository::getInstance()->getListBySerialNoList($travel_serial_no_list);
        $travel_serial_no_list = array_column($travel_serial_no_list, null, 'serial_no');
        $travel_city_list      = $this->getTravelNoListCityInfo($travel_serial_no_list, 'name');

        // 境内机票额度配置
        $apply_domestic_air_ticket_quota_amount = '0.00';
        $domestic_air_ticket_quota              = ReimbursementDomesticAirTicketQuotaRepository::getInstance()->getOneById(1);
        switch ($apply_position_type) {
            case ReimbursementEnums::POSITION_TYPE_1:
                $apply_domestic_air_ticket_quota_amount = $domestic_air_ticket_quota->frontline_operation_amount ?? '0.00';
                break;
            case ReimbursementEnums::POSITION_TYPE_2:
                $apply_domestic_air_ticket_quota_amount = $domestic_air_ticket_quota->frontline_functional_amount ?? '0.00';
                break;
            case ReimbursementEnums::POSITION_TYPE_3:
                $apply_domestic_air_ticket_quota_amount = $domestic_air_ticket_quota->head_office_amount ?? '0.00';
                break;
        }

        // 各PDF公共变量
        $company_logo_url = $cost_company_config['logo'] ?? '';
        $company_name_en  = ($cost_company_config['name_en'] ?? '') . '(' . $position_type_en . ')';

        // CLAIM Public transport expense 附件明细行数据
        $transport_expense_pdf_item         = [];
        $transport_expense_total_cost_sum   = '0.00';
        $transport_expense_total_amount_sum = '0.00';

        // CLAIM Accommodation-境内 附件明细行数据
        $accommodation_domestic_expense_pdf_item = [];

        // CLAIM Accommodation-境外 附件明细行数据
        $accommodation_overseas_expense_pdf_item = [];

        // 出差单住宿费发票总金额
        $travel_no_accommodation_amount_item     = [];
        $accommodation_domestic_total_cost_sum   = '0.00';
        $accommodation_domestic_total_amount_sum = '0.00';
        $accommodation_overseas_total_cost_sum   = '0.00';
        $accommodation_overseas_total_amount_sum = '0.00';

        // Cash Receipt附件数据
        $total_amount               = $total_vat_amount = $total_wht_amount = '0.00';
        $order_cash_receipt_expense = [];
        foreach ($expense_data as $key => $item) {
            $total_amount     = bcadd($total_amount, $item['tax_not'], 2);
            $total_vat_amount = bcadd($total_vat_amount, $item['tax'], 2);
            $total_wht_amount = bcadd($total_wht_amount, $item['wht_tax_amount'], 2);

            $order_cash_receipt_expense[] = [
                'serial_number' => $key + 1,
                'expense_name'  => trim($item['budget_text_th'] . ' ' . $item['product_name_th']),
                'quantity'      => 1,
                'tax_not'       => $item['tax_not'],
                'total_tax_not' => $item['tax_not'],
            ];

            if (empty($item['budget_id']) || empty($item['product_id'])) {
                continue;
            }

            // 1. CLAIM Private car expense 油费 (差旅/交通/支援科目下的)
            if (in_array($item['budget_id'], $fuel_cost_type_budget_ids) && in_array($item['product_id'], $fuel_cost_product_ids)) {
                $expenses_type             = '';
                $travel_reason_application = '';
                switch ($item['budget_id']) {
                    case $travel_budget_id:
                        $expenses_type             = ReimbursementEnums::EXPENSES_TYPE_1;
                        $travel_reason_application = $travel_serial_no_list[$item['travel_serial_no']]['reason_application'] ?? '';
                        break;
                    case $transportation_budget_id:
                        $expenses_type             = ReimbursementEnums::EXPENSES_TYPE_2;
                        $travel_reason_application = static::$t->_('travel_reason_application_transportation_fuel');
                        break;
                    case $support_budget_id:
                        $expenses_type             = ReimbursementEnums::EXPENSES_TYPE_3;
                        $travel_reason_application = static::$t->_('travel_reason_application_support_fuel');
                        break;
                }

                $fuel_quota = ReimbursementFuelQuotaRepository::getInstance()->getOneByType($apply_position_type, $expenses_type, $item['fuel_oil_type']);
                $unit_price = $fuel_quota->rates ?? '0.00';

                $fuel_mileage = is_numeric($item['fuel_mileage']) ? $item['fuel_mileage'] : '0';
                $total_cost   = number_format(bcmul($unit_price, $fuel_mileage, 3), 2, '.', '');

                $private_car_expense_pdf_data = [
                    'company_logo_url'          => $company_logo_url,
                    'company_name_en'           => $company_name_en,
                    'apply_id'                  => $order_data['apply_id'],
                    'apply_name'                => $order_data['apply_name'],
                    'fuel_vehicle_type'         => $item['fuel_vehicle_type'],   // 车辆类型
                    'fuel_vehicle_number'       => $item['fuel_vehicle_number'], // 车辆号码
                    'fuel_use_date'             => $item['fuel_use_date'],       // 用车日期
                    'fuel_start'                => $item['fuel_start'],          // 出发地
                    'fuel_end'                  => $item['fuel_end'],            // 目的地
                    'travel_reason_application' => $travel_reason_application,   // 出差事由
                    'fuel_start_mileage'        => $item['fuel_start_mileage'],  // 开始里程
                    'fuel_end_mileage'          => $item['fuel_end_mileage'],    // 结束里程
                    'fuel_mileage'              => $item['fuel_mileage'],        // 总里程
                    'unit_price'                => $unit_price,                  // 单价
                    'total_cost'                => $total_cost,                  // 总费用
                    'total_amount'              => $item['amount'],              // 发票总金额
                    'total_cost_sum'            => $total_cost,                  // 总费用合计
                    'total_amount_sum'          => $item['amount'],              // 发票总金额合计
                ];

                $private_car_expense_ftl  = $pdf_ftl_template_config['private_car_expense'] ?? '';
                $private_car_expense_file = $this->generatePdfFile($private_car_expense_pdf_data, $private_car_expense_ftl);
                $auto_attachment_files[]  = [
                    'oss_bucket_type' => Enums::OSS_BUCKET_TYPE_REIMBURSEMENT,
                    'oss_bucket_key'  => $item['id'],
                    'bucket_name'     => $private_car_expense_file['bucket_name'],
                    'object_key'      => $private_car_expense_file['object_key'],
                    'file_name'       => ReimbursementEnums::PDF_FILE_NAME_PRIVATE_CAR_EXPENSE,
                ];

                continue;
            }

            // 非差旅费的跳过
            if ($item['budget_id'] != $travel_budget_id) {
                continue;
            }

            $travel_serial_no   = $item['travel_serial_no'];
            $travel_serial_info = $travel_serial_no_list[$travel_serial_no] ?? [];

            $fuel_start         = $fuel_end = $travel_end_date = $travel_start_date = $travel_reason_application = '';
            $accommodation_days = 0;
            if (!empty($travel_serial_info)) {
                $travel_reason_application = $travel_serial_info['reason_application'];
                $travel_start_date         = $travel_serial_info['start_time'];
                $travel_end_date           = $travel_serial_info['end_time'];

                $accommodation_days = $travel_serial_info['days_num'] - 1;
                $accommodation_days = $accommodation_days > 0 ? $accommodation_days : 0;

                switch ($travel_serial_info['business_trip_type']) {
                    case ReimbursementEnums::TRAVEL_BUSINESS_TRIP_TYPE_IN:
                        $departure_city_info   = $travel_city_list[$travel_serial_info['departure_city_code']] ?? [];
                        $destination_city_info = $travel_city_list[$travel_serial_info['destination_city_code']] ?? [];

                        $fuel_start = trim(($departure_city_info['province_name'] ?? '') . ' ' . ($departure_city_info['city_name'] ?? ''));
                        $fuel_end   = trim(($destination_city_info['province_name'] ?? '') . ' ' . ($destination_city_info['city_name'] ?? ''));
                        break;
                    case ReimbursementEnums::TRAVEL_BUSINESS_TRIP_TYPE_OUT:
                        $fuel_start = $travel_serial_info['departure_city'];
                        $fuel_end   = $travel_serial_info['destination_city'];
                        break;
                }
            }

            // 2. CLAIM Public transport expense 差旅下的公共交通: 海外机票、国内机票、火车票、汽车票、船票
            if (in_array($item['product_id'], $all_transport_product_ids)) {
                $expense_type = 0;
                if (in_array($item['product_id'], $air_tickets_product_ids)) {
                    $expense_type = 1; // Airplane Fee
                } elseif (in_array($item['product_id'], $train_tickets_product_ids)) {
                    $expense_type = 2; // Train Fee
                } elseif (in_array($item['product_id'], $bus_tickets_product_ids)) {
                    $expense_type = 3; // Bus Fee
                } elseif (in_array($item['product_id'], $ferry_tickets_product_ids)) {
                    $expense_type = 4; // Boat Fee
                }

                $total_cost = '';
                if (in_array($item['product_id'], $domestic_air_product_ids)) {
                    $total_cost = $apply_domestic_air_ticket_quota_amount;
                }

                $transport_expense_pdf_item[] = [
                    'id'                        => $item['id'],
                    'fuel_start'                => $fuel_start,                // 出发地
                    'fuel_end'                  => $fuel_end,                  // 目的地
                    'travel_reason_application' => $travel_reason_application, // 出差事由
                    'expense_type'              => $expense_type,              // 费用类型
                    'total_cost'                => $total_cost,                // 总费用
                    'total_amount'              => $item['amount'],            // 发票总金额
                ];

                $transport_expense_total_cost_sum   = bcadd($transport_expense_total_cost_sum, $total_cost, 2);
                $transport_expense_total_amount_sum = bcadd($transport_expense_total_amount_sum, $item['amount'], 2);

                continue;
            }

            // 3. 差旅下的住宿费
            if (in_array($item['product_id'], $accommodation_product_ids)) {
                $serial_no_total_amount                                 = $travel_no_accommodation_amount_item[$travel_serial_no] ?? '0.00';
                $serial_no_total_amount                                 = bcadd($serial_no_total_amount, $item['amount'], 2);
                $travel_no_accommodation_amount_item[$travel_serial_no] = $serial_no_total_amount;

                $accommodation_expense_common_info = [
                    'id'                        => $item['id'],
                    'travel_reason_application' => $travel_reason_application, // 出差事由
                    'travel_start_date'         => $travel_start_date,         // 入住日期
                    'travel_end_date'           => $travel_end_date,           // 退住日期
                    'accommodation_days'        => $accommodation_days,        // 住宿天数
                    'total_amount'              => $serial_no_total_amount,    // 发票总金额
                ];

                // 3.1 CLAIM Accommodation-境内
                if ($travel_serial_info['business_trip_type'] == ReimbursementEnums::TRAVEL_BUSINESS_TRIP_TYPE_IN) {
                    // 发票总金额合计
                    $accommodation_domestic_total_amount_sum = bcadd($accommodation_domestic_total_amount_sum, $item['amount'], 2);

                    if (isset($accommodation_domestic_expense_pdf_item[$travel_serial_no])) {
                        $accommodation_domestic_expense_pdf_item[$travel_serial_no]['total_amount'] = $serial_no_total_amount;
                        continue;
                    }

                    // 住宿标准
                    $job_title_grade = $hr_staff_repository->getStaffJobGradeByDate($order_data['apply_id'], $travel_serial_info['start_time']);
                    if (!empty($job_title_grade)) {
                        $apply_job_level = $job_title_grade;
                    }

                    // 获取境内区域类型
                    $destination_province_code = $destination_city_info['province_code'] ?? '';
                    $destination_city_code     = $destination_city_info['city_code'] ?? '';
                    $area_type                 = $this->getDomesticAccommodationAreaType($destination_province_code, $destination_city_code);
                    $quota_model = ReimbursementDomesticAccommodationQuotaRepository::getInstance()->getOneByType($apply_position_type, $area_type, $apply_job_level);
                    $standards_amount = $quota_model->standards_amount ?? '0.00';
                    $total_cost = bcmul($standards_amount, (string)$accommodation_days, 3);
                    $total_cost = number_format($total_cost, 2, '.', '');

                    $accommodation_domestic_total_cost_sum = bcadd($accommodation_domestic_total_cost_sum, $total_cost, 2); // 总金额合计

                    $accommodation_domestic_expense_pdf_item[$travel_serial_no] = array_merge($accommodation_expense_common_info,
                        [
                            'fuel_start'       => $fuel_start,       // 出发地
                            'fuel_end'         => $fuel_end,         // 目的地
                            'standards_amount' => $standards_amount, // 住宿标准
                            'total_cost'       => $total_cost,       // 总金额
                        ]
                    );

                } elseif ($travel_serial_info['business_trip_type'] == ReimbursementEnums::TRAVEL_BUSINESS_TRIP_TYPE_OUT) {
                    // 3.2 CLAIM Accommodation-境外

                    // 发票总金额合计
                    $accommodation_overseas_total_amount_sum = bcadd($accommodation_overseas_total_amount_sum, $item['amount'], 2);

                    if (isset($accommodation_overseas_expense_pdf_item[$travel_serial_no])) {
                        $accommodation_overseas_expense_pdf_item[$travel_serial_no]['total_amount'] = $serial_no_total_amount;
                        continue;
                    }

                    // 住宿标准
                    $overseas_area_model = ReimbursementOverseasAccommodationAreaRepository::getInstance()->getOneByCountryId($travel_serial_info['destination_country']);
                    $accommodation_type = $overseas_area_model->accommodation_type ?? 0;
                    $quota_model = ReimbursementOverseasAccommodationQuotaRepository::getInstance()->getOneByType($apply_position_type, $accommodation_type);
                    $standards_amount = $quota_model->standards_amount ?? '0.00';

                    $total_cost = bcmul($standards_amount, (string)$accommodation_days, 3);
                    $total_cost = number_format($total_cost, 2, '.', '');

                    $accommodation_overseas_total_cost_sum = bcadd($accommodation_overseas_total_cost_sum, $total_cost, 2);// 总金额合计

                    $accommodation_overseas_expense_pdf_item[$travel_serial_no] = array_merge($accommodation_expense_common_info,
                        [
                            'fuel_start'       => $travel_serial_info['departure_city'],   // 出发地
                            'fuel_end'         => $travel_serial_info['destination_city'], // 目的地
                            'standards_amount' => $standards_amount,                       // 住宿标准
                            'total_cost'       => $total_cost,                             // 总金额
                        ]
                    );
                }

                continue;
            }

        }

        // Cash Receipt 整单现金收据
        $total_payable_amount    = bcsub(bcadd($total_amount, $total_vat_amount, 2), $total_wht_amount, 2);
        $order_cash_receipt_data = [
            'company_logo_url'         => $company_logo_url,
            'company_name_th'          => ($cost_company_config['name_th'] ?? '') . '(' . $position_type_th . ')',
            'company_name_en'          => $company_name_en,
            'company_address_th'       => $cost_company_config['address_th'] ?? '',
            'company_address_en'       => $cost_company_config['address_en'] ?? '',
            'issue_date'               => $format_apply_date,
            'apply_id'                 => $order_data['apply_id'],
            'apply_name'               => $order_data['apply_name'],
            'apply_id_number'          => $apply_identity,         // 申请人身份证号
            'apply_husehold_address'   => $apply_husehold_address, // 申请人户口地址
            'apply_company_name_th'    => $cost_company_config['name_th'] ?? '',
            'apply_company_address_th' => $cost_company_config['address_th'] ?? '',
            'apply_date'               => $format_apply_date,
            'apply_identity_url'       => $order_data['apply_identity_url'],             // 申请人身份证照片
            'apply_signature_url'      => $order_data['apply_signature_url'],            // 申请人签章图片
            'total_amount'             => $total_amount,                                 // 合计总金额
            'total_vat_amount'         => $total_vat_amount,                             // 合计vat税额
            'total_wht_amount'         => $total_wht_amount,                             // 合计wht税额
            'total_payable_amount'     => $total_payable_amount,                         // 应付金额合计
            'expense'                  => $order_cash_receipt_expense,                   // 明细行
        ];

        $receipt_voucher_ftl = $pdf_ftl_template_config['receipt_voucher'] ?? '';
        $receipt_voucher_file = $this->generatePdfFile($order_cash_receipt_data, $receipt_voucher_ftl);

        // 明细行与收据附件
        foreach ($expense_data as $item) {
            $auto_attachment_files[] = [
                'oss_bucket_type' => Enums::OSS_BUCKET_TYPE_REIMBURSEMENT,
                'oss_bucket_key'  => $item['id'],
                'bucket_name'     => $receipt_voucher_file['bucket_name'],
                'object_key'      => $receipt_voucher_file['object_key'],
                'file_name'       => ReimbursementEnums::PDF_FILE_NAME_RECEIPT_VOUCHER,
            ];
        }

        // CLAIM Public transport expense
        if (!empty($transport_expense_pdf_item)) {
            $transport_expense_pdf_data = [
                'company_logo_url'       => $company_logo_url,
                'company_name_en'        => $company_name_en,
                'apply_id'               => $order_data['apply_id'],
                'apply_name'             => $order_data['apply_name'],
                'apply_job_title_name'   => $order_data['apply_job_title_name'],
                'apply_dept_branch_name' => $order_data['apply_department_name'] . '/' . $order_data['apply_store_name'],
                'total_cost_sum'         => $transport_expense_total_cost_sum,
                'total_amount_sum'       => $transport_expense_total_amount_sum,
                'expense_item'           => $transport_expense_pdf_item,
            ];

            $transport_expense_ftl = $pdf_ftl_template_config['transport_expense'] ?? '';
            $transport_expense_file = $this->generatePdfFile($transport_expense_pdf_data, $transport_expense_ftl);

            foreach ($transport_expense_pdf_item as $item) {
                $auto_attachment_files[] = [
                    'oss_bucket_type' => Enums::OSS_BUCKET_TYPE_REIMBURSEMENT,
                    'oss_bucket_key'  => $item['id'],
                    'bucket_name'     => $transport_expense_file['bucket_name'],
                    'object_key'      => $transport_expense_file['object_key'],
                    'file_name'       => ReimbursementEnums::PDF_FILE_NAME_PUBLIC_TRANSPORT_EXPENSE,
                ];
            }
        }

        // CLAIM Accommodation-境内
        if (!empty($accommodation_domestic_expense_pdf_item)) {
            $accommodation_domestic_pdf_data = [
                'company_logo_url'       => $company_logo_url,
                'company_name_en'        => $company_name_en,
                'apply_id'               => $order_data['apply_id'],
                'apply_name'             => $order_data['apply_name'],
                'apply_job_title_name'   => $order_data['apply_job_title_name'],
                'apply_dept_branch_name' => $order_data['apply_department_name'] . '/' . $order_data['apply_store_name'],
                'total_cost_sum'         => $accommodation_domestic_total_cost_sum,
                'total_amount_sum'       => $accommodation_domestic_total_amount_sum,
                'expense_item'           => array_values($accommodation_domestic_expense_pdf_item),
            ];

            $accommodayion_domestic_ftl  = $pdf_ftl_template_config['accommodayion_domestic'] ?? '';
            $accommodayion_domestic_file = $this->generatePdfFile($accommodation_domestic_pdf_data, $accommodayion_domestic_ftl);
            foreach ($accommodation_domestic_expense_pdf_item as $item) {
                $auto_attachment_files[] = [
                    'oss_bucket_type' => Enums::OSS_BUCKET_TYPE_REIMBURSEMENT,
                    'oss_bucket_key'  => $item['id'],
                    'bucket_name'     => $accommodayion_domestic_file['bucket_name'],
                    'object_key'      => $accommodayion_domestic_file['object_key'],
                    'file_name'       => ReimbursementEnums::PDF_FILE_NAME_ACCOMMODAYION_DOMESTIC,
                ];
            }
        }

        // CLAIM Accommodation-境外
        if (!empty($accommodation_overseas_expense_pdf_item)) {
            $accommodation_overseas_pdf_data = [
                'company_logo_url'       => $company_logo_url,
                'company_name_en'        => $company_name_en,
                'apply_id'               => $order_data['apply_id'],
                'apply_name'             => $order_data['apply_name'],
                'apply_job_title_name'   => $order_data['apply_job_title_name'],
                'apply_dept_branch_name' => $order_data['apply_department_name'] . '/' . $order_data['apply_store_name'],
                'total_cost_sum'         => $accommodation_overseas_total_cost_sum,
                'total_amount_sum'       => $accommodation_overseas_total_amount_sum,
                'expense_item'           => array_values($accommodation_overseas_expense_pdf_item),
            ];

            $accommodayion_overseas_ftl  = $pdf_ftl_template_config['accommodayion_overseas'] ?? '';
            $accommodation_overseas_file = $this->generatePdfFile($accommodation_overseas_pdf_data, $accommodayion_overseas_ftl);
            foreach ($accommodation_overseas_expense_pdf_item as $item) {
                $auto_attachment_files[] = [
                    'oss_bucket_type' => Enums::OSS_BUCKET_TYPE_REIMBURSEMENT,
                    'oss_bucket_key'  => $item['id'],
                    'bucket_name'     => $accommodation_overseas_file['bucket_name'],
                    'object_key'      => $accommodation_overseas_file['object_key'],
                    'file_name'       => ReimbursementEnums::PDF_FILE_NAME_ACCOMMODAYION_OVERSEAS,
                ];
            }
        }

        $sys_attachment = new SysAttachmentModel();
        if (!empty($auto_attachment_files) && $sys_attachment->batch_insert($auto_attachment_files) === false) {
            throw new BusinessException('自动生成附件批量写入附件表失败, order_no=' . $order_data['no'], ErrCode::$BUSINESS_ERROR);
        }

        return true;
    }

    /**
     * 获取报销Redis消息队列名称
     */
    public function getRedisMsgQueueName()
    {
        return get_country_code() . '_' . ReimbursementEnums::MSG_REDIS_QUEUE_NAME;
    }

    /**
     * 获取申请人可选的支援单范围
     */
    protected function getApplyRelatedSupportList(string $apply_id, array $support_serial_no_list)
    {
        // 取数范围
        $and_where      = '';
        $and_where_bind = [];

        // 20250811 邮件需求, 去掉管辖范围的约束
//        $time_1 = get_curr_micro_time();

        // 管理大区下的网点
//        $manage_store_ids = RegionRepository::getInstance()->getStoreListByManagerId($apply_id);
//        $manage_store_ids = array_filter(array_column($manage_store_ids, 'store_id'));
//        if (!empty($manage_store_ids)) {
//            $manage_store_ids = array_values($manage_store_ids);
//            $and_where        = 'staff_store_id IN ({staff_store_ids:array}) OR store_id IN ({store_ids:array})';
//            $and_where_bind   = [
//                'staff_store_ids' => $manage_store_ids,
//                'store_ids'       => $manage_store_ids,
//            ];
//        }

        // 管理片区下的网点
//        if (empty($and_where)) {
//            $manage_store_ids = PieceRepository::getInstance()->getStoreListByManagerId($apply_id);
//            $manage_store_ids = array_filter(array_column($manage_store_ids, 'store_id'));
//            if (!empty($manage_store_ids)) {
//                $and_where      = 'store_id IN ({store_ids:array})';
//                $and_where_bind = [
//                    'store_ids' => array_values($manage_store_ids),
//                ];
//            }
//        }

        // 管理的网点
//        if (empty($and_where)) {
//            $manage_store_ids = (new StoreRepository())->getListByManagerId($apply_id);
//            $manage_store_ids = array_filter(array_column($manage_store_ids, 'store_id'));
//            if (!empty($manage_store_ids)) {
//                $and_where      = 'store_id IN ({store_ids:array})';
//                $and_where_bind = [
//                    'store_ids' => array_values($manage_store_ids),
//                ];
//            }
//        }

//        $this->logger->info([
//            'getStoreSupportListExecTime' => [
//                'get_manage_store_ids'   => get_exec_time($time_1),
//                'manage_store_ids_count' => count($manage_store_ids),
//            ],
//        ]);

//        if (empty($and_where)) {
//            $and_where      = 'staff_info_id = :apply_id:';
//            $and_where_bind = ['apply_id' => $apply_id];
//        }

        $time_2 = get_curr_micro_time();

        // 符合条件的支援单
        $builder = $this->modelsManager->createBuilder();
        $builder->from(HrStaffApplySupportStore::class);

        // 支援清单中申请状态=已通过 && 生效状态=2已生效、3已失效
        $builder->where('status = :status: AND support_status in ({support_status:array}) AND employment_end_date < :today_date:', [
            'status'         => ByWorkflowEnums::BY_OPERATE_PASS,
            'support_status' => [
                ReimbursementEnums::STORE_SUPPORT_STATUS_2,
                ReimbursementEnums::STORE_SUPPORT_STATUS_3,
            ],
            'today_date'     => date('Y-m-d'),
        ]);

        // 申请人 和 管辖范围的取数条件
//        $builder->andWhere($and_where, $and_where_bind);
        $builder->inWhere('serial_no', $support_serial_no_list);

        $columns = [
            'serial_no',
            'is_stay',
            'transportation_mode',
            'job_title_id',
            'staff_store_id',
            'employment_begin_date',
            'employment_end_date',
            'employment_days',
            'store_id',
            'store_name',
        ];
        $builder->columns($columns);
        $items = $builder->getQuery()->execute()->toArray();

        $this->logger->info([
            'getStoreSupportListExecTime' => [
                'get_items'   => get_exec_time($time_2),
                'items_count' => count($items),
            ],
        ]);

        return $items;
    }

    /**
     * 校验支援费用
     *
     * @param string $product_id 实质的明细id
     * @param array $expense_serial_no_list 实质的支援单列表
     * @param array $other_params 其他补充参数
     * @return bool
     * @throws ValidationException
     */
    protected function checkSupportExpense(string $product_id, array $expense_serial_no_list = [], array $other_params = [])
    {
        $apply_id = $other_params['apply_id'];
        $all_submit_support_no_list = $other_params['all_submit_support_no_list'];

        $country_code = get_country_code();

        // 所有可选的
        static $normal_support_no_list = [];
        if (empty($normal_support_no_list)) {
            switch ($country_code) {
                case GlobalEnums::TH_COUNTRY_CODE:
                    $normal_support_no_list = HrStaffApplySupportStoreRepository::getInstance()->getNormalSupportNoList($all_submit_support_no_list);
                    break;
                case GlobalEnums::PH_COUNTRY_CODE:
                    $normal_support_no_list = $this->getApplyRelatedSupportList($apply_id, $all_submit_support_no_list);
                    $normal_support_no_list = array_column($normal_support_no_list, null, 'serial_no');
                    break;
            }
        }

        $this->logger->info(['reimbursement_normal_support_no_list' => $normal_support_no_list]);

        // 费用明细类型配置
        $accommodation_fees_ids = static::getDetailTypeItem(ReimbursementEnums::SPECIAL_EXPENSE_DETAILS_TYPE_ACCOMMODATION_FEES);
        $transportation_ids     = static::getDetailTypeItem(ReimbursementEnums::SPECIAL_EXPENSE_DETAILS_TYPE_TRANSPORTATION);

        // 支援费用允许报销的交通方式
        $allow_transportation_modes = static::getSupportAllowTransportationMode();

        // 在途的
        $exist_support_no_list = $this->getSupportSerialNoList($product_id, $expense_serial_no_list, $other_params);
        $this->logger->info(['reimbursement_exist_support_no_list' => $exist_support_no_list]);

        foreach ($expense_serial_no_list as $_support_serial_no) {
            if ($country_code == GlobalEnums::TH_COUNTRY_CODE) {
                if (!in_array($_support_serial_no, $normal_support_no_list) || in_array($_support_serial_no, $exist_support_no_list)) {
                    throw new ValidationException(static::$t->_('reimbursement_save_error_009', ['support_serial_no' => $_support_serial_no]), ErrCode::$VALIDATE_ERROR);
                }
            } elseif ($country_code == GlobalEnums::PH_COUNTRY_CODE) {
                if (in_array($_support_serial_no, $exist_support_no_list)) {
                    throw new ValidationException(static::$t->_('reimbursement_save_error_051', ['support_serial_no' => $_support_serial_no]), ErrCode::$VALIDATE_ERROR);
                }

                $can_use_info = $normal_support_no_list[$_support_serial_no] ?? [];

                if (empty($can_use_info)) {
                    throw new ValidationException(static::$t->_('reimbursement_save_error_051', ['support_serial_no' => $_support_serial_no]), ErrCode::$VALIDATE_ERROR);
                }

                if (in_array($product_id, $accommodation_fees_ids) && $can_use_info['is_stay'] != 1) {
                    throw new ValidationException(static::$t->_('reimbursement_save_error_051', ['support_serial_no' => $_support_serial_no]), ErrCode::$VALIDATE_ERROR);
                }

                if (in_array($product_id, $transportation_ids) && !empty($can_use_info['transportation_mode']) && !in_array($can_use_info['transportation_mode'], $allow_transportation_modes)) {
                    throw new ValidationException(static::$t->_('reimbursement_save_error_051', ['support_serial_no' => $_support_serial_no]), ErrCode::$VALIDATE_ERROR);
                }
            }
        }

        return true;
    }

}
