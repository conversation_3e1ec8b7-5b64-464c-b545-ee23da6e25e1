<?php

namespace App\Modules\Reimbursement\Services;

use App\Library\ApiClient;
use App\Library\Enums;
use App\Library\Enums\GlobalEnums;
use App\Library\Enums\OrdinaryPaymentEnums;
use App\Library\Enums\ReimbursementEnums;
use App\Library\Enums\SysConfigEnums;
use App\Library\Enums\KingDeeEnums;
use App\Library\ErrCode;
use App\Library\Exception\BusinessException;
use App\Library\Validation\ValidationException;
use App\Models\oa\SettingInvoiceTaxNoModel;
use App\Modules\Budget\Services\LedgerAccountService;
use App\Modules\Common\Models\EnvModel;
use App\Modules\Common\Services\EnumsService;
use App\Modules\Hc\Services\SysService;
use App\Modules\Loan\Models\Loan;
use App\Modules\Organization\Models\SysDepartmentModel;
use App\Modules\Organization\Models\SysStoreModel;
use App\Modules\PaperDocument\Services\ConfirmationService;
use App\Modules\Pay\Services\PayService;
use App\Modules\Purchase\Services\ApplyService;
use App\Modules\Reimbursement\Models\Detail;
use App\Modules\Reimbursement\Models\Reimbursement;
use App\Modules\Reimbursement\Models\ReimbursementRelLoan;
use App\Modules\ReserveFund\Models\ReserveFundApply;
use App\Modules\ReserveFund\Models\ReserveFundReimburse;
use App\Modules\Setting\Services\DataPermissionModuleConfigService;
use App\Modules\User\Services\StaffService;
use App\Modules\Workflow\Models\WorkflowRequestModel;
use App\Modules\Workflow\Models\WorkflowUpdateLogModel;
use App\Modules\Workflow\Services\AbstractFlowService;
use App\Modules\Workflow\Services\WorkflowServiceV2;
use App\Repository\HrStaffRepository;
use App\Repository\oa\SettingInvoiceTaxNoRepository;
use App\Repository\oa\WorkflowRequestNodeAuditorRepository;
use App\Repository\StoreRepository;
use FlashExpress\bi\App\Models\SysDepartment;
use Phalcon\Mvc\Model\Resultset;
use Phalcon\Mvc\Phalcon\Mvc\Model;

class ReimbursementFlowService extends AbstractFlowService
{

    /**
     * 报销审核
     * @param $id
     * @param $note
     * @param $user
     * @param $update_data array 更新的数据
     * @return array
     */
    public function approve($id, $note, $user, $update_data = [])
    {
        $code = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $work_req = $this->getRequest($id);
        $db = $this->getDI()->get('db_oa');
        try {
            $db->begin();
            if (empty($work_req->id)) {
                throw new BusinessException('获取工作流批次失败', ErrCode::$CONTRACT_GET_WORK_REQUEST_ERROR);
            }
            $item = Reimbursement::getFirst([
                'id = :id:',
                'bind' => ['id' => $id]
            ]);

            if (empty($item)) {
                throw new ValidationException(static::$t->_('biz_order_data_is_null', ['biz_id' => $id]), ErrCode::$VALIDATE_ERROR);
            }

            if ($item->status != Enums::WF_STATE_PENDING) {
                throw new ValidationException(static::$t->_('workflow_action_status_check_error'), ErrCode::$VALIDATE_ERROR);
            }

            //如果更新数据不为空
            if (!empty($update_data)) {
                $can_edit_field = $this->getCanEditFieldByReq($work_req, $user['id']);
                if (!empty($can_edit_field)) {
                    $this->dealEditField($item, $can_edit_field, $update_data, $work_req, $user);
                }
            }
            $ws = new WorkflowServiceV2();
            $result = $ws->doApprove($work_req, $user, $this->getWorkflowParams($item, $user), $note);
            if (!empty($result->approved_at)) {
                $update_data = [
                    'status' => Enums::WF_STATE_APPROVED,
                    'updated_at' => date('Y-m-d H:i:s'),
                    'approved_at' => $result->approved_at,
                ];
                $countryCode = get_country_code();
                if (in_array($countryCode, [GlobalEnums::TH_COUNTRY_CODE, GlobalEnums::PH_COUNTRY_CODE, GlobalEnums::MY_COUNTRY_CODE])) {
                    $confirmInfo = $item->getPaperDocumentData();
                    $update_data['confirm_status'] = $confirmInfo['confirm_status'];
                }
                $bool = $item->i_update($update_data);
                if ($bool === false) {
                    throw new BusinessException('更新Reimbursement表失败', ErrCode::$CONTRACT_UPDATE_ERROR);
                }

                $countryCode = get_country_code();
                if (in_array($countryCode, [GlobalEnums::TH_COUNTRY_CODE, GlobalEnums::PH_COUNTRY_CODE, GlobalEnums::MY_COUNTRY_CODE])) {
                    //进入到确认流程
                    ConfirmationService::getInstance()->saveOne($item);
                    if (isset($confirmInfo['confirm_status']) && $confirmInfo['confirm_status'] == Enums\PaperDocumentEnums::CONFIRM_STATE_COMPLETE) {
                        $this->syncDataToPayment($item, $work_req);
                    }
                } else {
                    $this->syncDataToPayment($item, $work_req);
                }
                //同步发票税号信息
                AddService::getInstance()->saveInvoiceTaxNo($item);
            }

            //如果当前不是，且到了泰国AP
            if ($item->is_after_ap_th != 1 && $ws->isAfterApTH($result)) {
                $item->is_after_ap_th = 1;
                $item->save();
            }

            $db->commit();
        } catch (ValidationException $e) {
            $code = $e->getCode();
            $message = $e->getMessage();
        } catch (BusinessException $e) {
            $code = $e->getCode();
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        }
        if (!empty($message)) {
            $db->rollback();
        }
        if (!empty($real_message)) {
            $this->logger->warning('reimbursement-approve-failed:' . $real_message);
        }

        return [
            'code' => $code,
            'message' => $message,
        ];
    }

    /**
     * @param $id
     * @param $note
     * @param $user
     * @return array
     */
    public function reject($id, $note, $user)
    {
        $code = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $work_req = $this->getRequest($id);
        $db = $this->getDI()->get('db_oa');
        try {

            $db->begin();
            if (empty($work_req->id)) {
                throw new BusinessException('获取工作流批次失败', ErrCode::$CONTRACT_GET_WORK_REQUEST_ERROR);
            }
            $item = Reimbursement::getFirst([
                'id = :id:',
                'bind' => ['id' => $id]
            ]);

            if (empty($item)) {
                throw new ValidationException(static::$t->_('biz_order_data_is_null', ['biz_id' => $id]), ErrCode::$VALIDATE_ERROR);
            }

            if ($item->status != Enums::WF_STATE_PENDING) {
                throw new ValidationException(static::$t->_('workflow_action_status_check_error'), ErrCode::$VALIDATE_ERROR);
            }

            $result = (new WorkflowServiceV2())->doReject($work_req, $user, $this->getWorkflowParams($item, $user), $note);
            if ($result === false) {
                throw new BusinessException('审批流拒绝失败', ErrCode::$CONTRACT_WORK_FLOW_REJECT_ERROR);
            }
            $bool = $item->i_update([
                'status' => Enums::CONTRACT_STATUS_REJECTED,
                'updated_at' => date('Y-m-d H:i:s'),
                'remark' => $note,
                'pay_status' => Enums::LOAN_PAY_STATUS_NOTPAY
            ]);
            if ($bool === false) {
                throw new BusinessException('更新Reimbursement表失败', ErrCode::$CONTRACT_UPDATE_ERROR);
            }

            // todo 驳回 释放含税金额占用
            AddService::getInstance()->freeBudget($id, $user);

            // 释放关联借款
            $this->freeLoans($item);

            // 释放备用金关联
            $this->freeReserves($item);

            $db->commit();

            //报销单 如果是 by申请的 油费报销 驳回之后 需要 删除用车记录关联
            if ($item->source_type == 2) {
                $ac = new ApiClient('by', '', 'reject_fuel');
                $api_params = [
                    [
                        'staff_id' => $item->created_id,
                        'no' => $item->no,
                    ],

                ];
                $ac->setParams($api_params);
                $ac->execute();
            }

        } catch (ValidationException $e) {
            $code = $e->getCode();
            $message = $e->getMessage();
        } catch (BusinessException $e) {
            $code = $e->getCode();
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        } catch (\Exception $e) {
            $code = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        }

        if (!empty($message)) {
            $db->rollback();
        }

        if (!empty($real_message)) {
            $logger = $this->getDI()->get('logger');
            $logger->warning('reimbursement-reject-failed:' . $real_message);
        }

        return [
            'code' => $code,
            'message' => $message,
        ];
    }

    /**
     * @param $id
     * @param $note
     * @param $user
     * @return array
     */
    public function cancel($id, $note, $user)
    {
        $code = ErrCode::$SUCCESS;
        $message = $real_message = '';

        $work_req = $this->getRequest($id);
        $db = $this->getDI()->get('db_oa');
        $db->begin();

        try {
            $item = Reimbursement::getFirst([
                'id = :id:',
                'bind' => ['id' => $id]
            ]);

            if (empty($item)) {
                throw new ValidationException(static::$t->_('biz_order_data_is_null', ['biz_id' => $id]), ErrCode::$VALIDATE_ERROR);
            }

            if ($item->status != Enums::WF_STATE_PENDING) {
                throw new ValidationException(static::$t->_('workflow_action_status_check_error'), ErrCode::$VALIDATE_ERROR);
            }

            $result = (new WorkflowServiceV2())->doCancel($work_req, $user, $this->getWorkflowParams($item, $user), $note);
            if ($result === false) {
                throw new BusinessException('审批流拒绝失败', ErrCode::$CONTRACT_WORK_FLOW_REJECT_ERROR);
            }
            $bool = $item->i_update([
                'status' => Enums::CONTRACT_STATUS_CANCEL,
                'updated_at' => date('Y-m-d H:i:s'),
                'remark' => $note,
                'pay_status' => Enums::LOAN_PAY_STATUS_NOTPAY
            ]);
            if ($bool === false) {
                throw new BusinessException(static::$t->_('contract_withdrawal_failed'), ErrCode::$CONTRACT_CANCEL_ERROR);
            }

            // 释放占用的金额
            AddService::getInstance()->freeBudget($id, $user);

            // 释放关联借款
            $this->freeLoans($item);

            // 释放备用金关联
            $this->freeReserves($item);

            $db->commit();

            //报销单 如果是 by申请的 油费报销 驳回之后 需要 删除用车记录关联
            if ($item->source_type == 2) {
                $ac = new ApiClient('by', '', 'reject_fuel');
                $api_params = [
                    [
                        'staff_id' => $item->created_id,
                        'no' => $item->no,
                    ],
                ];
                $ac->setParams($api_params);
                $ac->execute();
            }

        } catch (ValidationException $e) {
            $db->rollback();

            //校验错误，可对外抛出
            $code = $e->getCode();
            $message = $e->getMessage();
        } catch (BusinessException $e) {
            $db->rollback();

            //业务错误（数据错误等），不可对外抛出
            $code = $e->getCode();
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        } catch (\Exception $e) {
            $db->rollback();

            //系统错误，不可对外抛出
            $code = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        }

        if (!empty($real_message)) {
            $this->logger->error('reimbursement-cancel-failed:' . $real_message);
        }

        return [
            'code' => $code,
            'message' => $message,
        ];
    }

    /**
     * 找最新的request
     *
     * @param $id
     * @return \Phalcon\Mvc\Model
     */
    public function getRequest($id)
    {
        return WorkflowRequestModel::findFirst(
            [
                'biz_type = :type: and biz_value= :lid:',
                'bind' => ['type' => Enums::WF_REIMBURSEMENT_TYPE, 'lid' => $id],
                'order' => 'id desc'
            ]
        );
    }

    /**
     * @param $item
     * @param $user
     * @param array $recommit_params 重新提交需要的参数
     * @return Resultset|Model
     * @throws BusinessException
     * @throws ValidationException
     */
    public function createRequest($item, $user, $recommit_params = [])
    {
        $work_flow_service_v2 = new WorkflowServiceV2();
        $data['id'] = $item->id;
        $data['name'] = $item->no . '审批申请';
        $data['biz_type'] = Enums::WF_REIMBURSEMENT_TYPE;
        //报销用的是申请人id，申请人名字
        $user['id'] = $item->apply_id;
        $user['name'] = $item->apply_name;

        $info = $this->getWorkflowParams($item, $user);

        //创建审批申请
        $store_id = 0;
        if (isset($info['store_id'])) {
            $store_id = $info['store_id'];
        }
        //v18028泰国、18960马来、菲律宾启用新审批流和配置, 其他国家保持原逻辑
        $country_code = get_country_code();
        if (in_array($country_code, [GlobalEnums::TH_COUNTRY_CODE, GlobalEnums::PH_COUNTRY_CODE, GlobalEnums::MY_COUNTRY_CODE])) {
            $data['flow_id'] = $this->getFlowIdNew($item, $info);
        } else {
            $data['flow_id'] = $this->getFlowId($item, $store_id, $info);
        }

        //如果审批流前后没变, 且没有修改敏感字段, 要从上次提交的驳回节点开始审批
        $continue_node_id = 0;
        if (!empty($recommit_params)) {
            if ($data['flow_id'] == $recommit_params['origin_request']->flow_id && $recommit_params['check_sensitive'] === false && $recommit_params['origin_request']->state == Enums::WF_STATE_REJECTED) {
                //找到上一次驳回的节点
                $continue_node_id = WorkflowRequestNodeAuditorRepository::getInstance()->getRejectNodeId($recommit_params['origin_request']->id);
            }
        }
        return $work_flow_service_v2->createRequest($data, $user, $info, $continue_node_id);
    }

    /**
     * 获取审批流需要数据
     *
     * @param $item
     * @param $user
     * @return array
     */
    public function getWorkflowParams($item, $user)
    {
        // 泰国express 判断报销实质(预算科目)外包外协
        $departmentCompanyId = (new EnumsService())->getSysDepartmentCompanyIds();
        //是否包含外包外协 0 无 1.外包 2.外协  以首次出现为准,基于产品逻辑,泰国network外包外协不会混合提交,这里只取第一个,用于泰国network外包外协的审批流
        $out_worker = ReimbursementEnums::OUT_WORKER_NOT;
        $is_fine = ReimbursementEnums::IS_NO_FINE;
        $country_code = get_country_code();
        if (in_array($country_code, [GlobalEnums::TH_COUNTRY_CODE, GlobalEnums::PH_COUNTRY_CODE]) && $item->cost_company_id == $departmentCompanyId['FlashExpress']) {
            $details = $item->getDetails();
            foreach ($details as $k => $v) {
                if ($v->budget_id == Enums::BUDGET_OBJECT_OUT_WORKER_ID) {
                    $out_worker = ReimbursementEnums::OUT_WORKER_EPIBOLY;
                    break;
                }
                if ($v->budget_id == Enums::BUDGET_OBJECT_EXTERNAL_COURIER_ID) {
                    $out_worker = ReimbursementEnums::OUT_WORKER_OUTSOURCE;
                    break;
                }
                if ($v->is_fine == ReimbursementEnums::IS_FINE) {
                    $is_fine = ReimbursementEnums::IS_FINE;
                    break;
                }


            }
        }
        // 金额根据币种汇率转换为系统默认币种的额度
        $default_currency_amount = (new EnumsService())->amountExchangeRateCalculation($item->amount, $item->exchange_rate, 0);
        return [
            'amount'             => $default_currency_amount,
            'currency'           => $item->currency,
            'submitter_id'       => $item->apply_id,
            'department_id'      => $item->sys_department_id,
            'node_department_id' => $item->node_department_id,
            'company_id'         => $item->cost_company_id, //v10533改为费用所属公司判断
            'create_company_id'  => $item->cost_company_id, //为了取财务分组审批人,需要定义create_company_id=费用所属公司
            'store_id'           => $item->apply_store_id,
            'create_staff_id'    => $item->created_id,
            //'is_island' => $item->is_island,
            'type'               => $item->type,
            'KEY'                => $this->isFlashHrOrCpoAndNotPhilippinesPeople($item->node_department_id ? $item->node_department_id : $item->sys_department_id),
            'cost_store_type'    => $item->cost_store_type,
            'out_worker'         => $out_worker,
            'is_fine'            => $is_fine,
            'is_cmo'             => $country_code == GlobalEnums::TH_COUNTRY_CODE ? (new HrStaffRepository())->isCMO($item->apply_id) : 0,//V21791-当申请人直线上级等于配置的CMO工号时该节点需要审批
        ];
    }


    /**
     * 是否是cpo部门且不是 Philippines People 部门
     *
     * @param $department_id
     *
     */
    public function isFlashHrOrCpoAndNotPhilippinesPeople($department_id)
    {
        $sysDepartment = SysDepartmentModel::findFirst([
            'conditions' => " ancestry_v3 like '999/222/70001%' and  id = :department_id: ",
            'bind' => [
                "department_id" => $department_id
            ]
        ]);
        if ($sysDepartment) {
            return 1;
        }

        $sysDepartment = SysDepartmentModel::findFirst([
            "conditions" => " ancestry_v3 like '999/444/%' and ancestry_v3 not LIKE '999/444/210%' and id = :department_id: ",
            "bind" => [
                "department_id" => $department_id
            ]
        ]);

        return $sysDepartment ? 1 : 0;
    }

    /**
     * v18028、v18960获取审批流id
     *
     * @param object $model
     * @param array $info
     * @return int 审批流id
     * @throws ValidationException
     */
    public function getFlowIdNew(object $model, array $info)
    {
        //1. 获取所有配置
        $flow_config = EnumsService::getInstance()->getSettingEnvValueMap('reimbursement_create_flow_new_config');
        $department_config = EnumsService::getInstance()->getSettingEnvValueMap('appoint_store_by_department_id');
        $company_config = EnumsService::getInstance()->getSettingEnvValueMap('sys_department_company_ids');
        //2. 找到公司
        $company_key = array_search($info['company_id'], $company_config);
        //FlashHomeOperation和FlashHomeHolding统一转成FlashHome
        if (in_array($company_key, ['FlashHomeOperation', 'FlashHomeHolding'])) {
            $company_key = 'FlashHome';
        }
        if (isset($flow_config[$company_key])) {
            $company_config = $flow_config[$company_key];
        } elseif (isset($flow_config['other'])) {
            $company_config = $flow_config['other'];
        } else {
            $this->logger->warning('费用所属公司未配置审批流 config=' . json_encode($flow_config, JSON_UNESCAPED_UNICODE));
            throw new ValidationException(static::$t->_('flow_config_not_exist_company'), ErrCode::$VALIDATE_ERROR);
        }
        //3. 找到部门
        //用申请人一级部门找
        $country_code = get_country_code();
        $department_key = false;
        $apply_staff_info = (new HrStaffRepository())->getStaffById($model->apply_id);
        if (!empty($apply_staff_info)) {
            if ($apply_staff_info['sys_store_id'] != Enums::HEAD_OFFICE_STORE_FLAG) {
                $department_key = array_search($apply_staff_info['sys_department_id'], $department_config);
            } elseif ($country_code == GlobalEnums::PH_COUNTRY_CODE && isset($department_config['network']) && $apply_staff_info['sys_department_id'] == $department_config['network']) {
                //v18960菲律宾-network部门分总部和网点-总部
                $department_key = 'network_head_office';
            } elseif ($company_key == 'FlashExpress' && $country_code == GlobalEnums::TH_COUNTRY_CODE && isset($department_config['pmd']) && $apply_staff_info['sys_department_id'] == $department_config['pmd']) {
                //v21791泰国-总部-pmd部门
                $department_key = 'pmd';
            }
        }
        if ($department_key !== false && isset($company_config[$department_key])) {
            $flow_department_config = $company_config[$department_key];
        } elseif (isset($company_config['headquarters'])) {
            $flow_department_config = $company_config['headquarters'];
        } else {
            $this->logger->warning('部门未配置审批流 config=' . json_encode($flow_config, JSON_UNESCAPED_UNICODE));
            throw new ValidationException(static::$t->_('flow_config_not_exist_department'), ErrCode::$VALIDATE_ERROR);
        }
        //4. 找到报销实质
        //找到最优先的报销实质
        $flow_config_keys = array_keys($flow_department_config);
        //获取所有报销实质
        $details = $model->getDetails()->toArray();
        $budget_ids = array_column($details, 'budget_id');
        //给报销实质分组
        $product_group = AddService::getInstance()->getBudgetTypeNew($budget_ids);
        $find_budget = 'other';//默认找不到用other
        foreach ($flow_config_keys as $v) {
            if (key_exists($v, $product_group)) {
                $find_budget = $v;
                break;
            }
        }
        //找到审批流
        if (empty($flow_department_config[$find_budget])) {
            $this->logger->warning('报销实质未配置审批流 budget_ids=' . $budget_ids . ' config=' . json_encode($flow_config, JSON_UNESCAPED_UNICODE));
            throw new ValidationException(static::$t->_('flow_config_not_exist_budget'), ErrCode::$VALIDATE_ERROR);
        }
        //返回审批流id
        return $flow_department_config[$find_budget];
    }

    /**
     * 从WorkflowServiceV2迁移过来
     * v13571增加了泰国flashExpress公司的审批流判断分支
     *
     * @inheritDoc
     * @throws BusinessException
     * @throws ValidationException
     */
    public function getFlowId($model = null, $store_id = 0, $info = [])
    {
        $company_ids = EnumsService::getInstance()->getSysDepartmentCompanyIds();
        $country_code = get_country_code();
        //子公司审批流配置
        $company_flow_data = EnumsService::getInstance()->getSettingEnvValueMap('reimbursement_bu_flow_config');
        $company_key = array_search($info['company_id'], $company_ids);
        //先判断子公司
        $data = $company_flow_data[$company_key] ?? ($company_flow_data['other'] ?? 0);
        //如果子公司没有找到,再继续以下逻辑(根据当前配置,老挝,越南,印尼都配置了other,泰国,菲律宾,马来没配置other,所以不符合子公司条件的时候会进入以下逻辑)
        if (in_array($country_code, [GlobalEnums::TH_COUNTRY_CODE, GlobalEnums::PH_COUNTRY_CODE, GlobalEnums::MY_COUNTRY_CODE]) && empty($data)) {
            if ($info['company_id'] == $company_ids['FlashExpress']) {
                //v13571增加了泰国flashExpress公司的审批流判断分支 v14700 增加ph flashExpress公司的审批流判断分支
                $data = $this->getBelongCompanyFlowId($model, $store_id, $info, ReimbursementEnums::REIMBURSEMENT_FLASH_EXPRESS);
            } elseif ($country_code == GlobalEnums::TH_COUNTRY_CODE && in_array($info['company_id'], [$company_ids['FlashHomeHolding'], $company_ids['FlashHomeOperation']])) {
                $data = $this->getBelongCompanyFlowId($model, $store_id, $info, ReimbursementEnums::REIMBURSEMENT_FLASH_HOME);
            } elseif ($store_id == Enums::HEAD_OFFICE_STORE_FLAG) {
                $data = Enums::REIMBURSEMENT_HEADER_WF_ID;//报销总部
            } else {
                //创建的时候判断了
                if ($info['type'] == Enums::REIMBURSEMENT_TYPE_TAX) {
                    $data = Enums::REIMBURSEMENT_GGP_WF_ID;
                } else {
                    $store = (new WorkflowServiceV2())->getStoreById($store_id);
                    switch ($store['category']) {
                        //network
                        case 1:
                        case 2:
                        case 10:
                        case 13:
                        case 14:
                            $data = Enums::REIMBURSEMENT_NETWORK_WF_ID; //报销网点（Network）
                            break;
                        //门店shop
                        case 4:
                        case 5:
                        case 7:
                            $data = Enums::REIMBURSEMENT_SHOP_WF_ID;//报销网点（门店shop）
                            break;
                        //hub
                        case 8:
                        case 9:
                        case 12:
                            $data = Enums::REIMBURSEMENT_HUB_WF_ID; //报销网点（hub）
                            break;
                        default:
                            $data = Enums::REIMBURSEMENT_HEADER_WF_ID;//报销总部

                            $this->logger->waring('报销没找到匹配的网点类型走默认兜底审批流(Express总部)' . $data);

                            break;
                    }
                    $sysDepartment = StaffService::getInstance()->getParentDepartment($info['department_id'], 1);
                    if (GlobalEnums::TH_COUNTRY_CODE == $country_code && $data == Enums::REIMBURSEMENT_NETWORK_WF_ID) {
                        if ($info['cost_store_type'] == 1 && trim($sysDepartment['id']) == Enums::SYS_DEPARTMENT_ONE_ID) {
                            $data = Enums::REIMBURSEMENT_INTEL_STORE_WF_ID;//报销申请网点
                        }
                    }

                }
            }
        }
        return $data;
    }


    /**
     * 重新提交的审批流维护流程
     *
     * @param $item
     * @param $user
     * @param $check_sensitive // 是否修改了敏感字段, 如果没配置不传(true是 false否 null没配置不需要续接审批流)
     * @return mixed
     * @throws BusinessException
     * @throws ValidationException
     */
    public function recommit($item, $user, $check_sensitive = null)
    {
        // 废弃原审批流
        $req = $this->getRequest($item->id);
        if (empty($req)) {
            throw new BusinessException("没有找到原审批流, biz_id={$item->id}", ErrCode::$BUSINESS_ERROR);
        }

        //老的改成被遗弃
        $req->is_abandon = Enums\GlobalEnums::WORKFLOW_ABANDON_STATE_YES;
        if ($req->save() === false) {
            throw new BusinessException('原审批流废弃状态更新失败, 原因可能是=' . get_data_object_error_msg($req) . '; 数据=' . json_encode($req->toArray(), JSON_UNESCAPED_UNICODE), ErrCode::$BUSINESS_ERROR);
        }
        if ($check_sensitive !== null) {
            $recommit_params = [
                'origin_request' => $req,
                'check_sensitive' => $check_sensitive
            ];
        }
        // 创建新审批流
        return $this->createRequest($item, $user, $recommit_params ?? []);
    }


    /**
     * 释放报销 - 关联借款单
     *
     * @param $item
     * @return bool
     * @throws BusinessException
     */
    public function freeLoans($item)
    {
        $loanObj = ReimbursementRelLoan::findFirst([
            'conditions' => 're_id = :re_id: and is_deleted = :is_deleted:',
            'bind' => [
                're_id' => $item->id,
                'is_deleted' => GlobalEnums::IS_NO_DELETED
            ],
        ]);

        if (!empty($loanObj)) {
            $this->logger->info("reimbursement_free_loans 报销与借款的关系数据(报销关联的借款单re_id={$item->id}): " . json_encode($loanObj->toArray(), JSON_UNESCAPED_UNICODE));

            $loan = Loan::findFirst([
                'conditions' => 'id = :id:',
                'bind' => ['id' => $loanObj->loan_id]
            ]);

            if (empty($loan)) {
                throw new BusinessException("reimbursement_free_loans 借款数据不存在: loan_id = {$loanObj->loan_id}", ErrCode::$BUSINESS_ERROR);
            }

            $this->logger->info('reimbursement_free_loans 借款数据(before): ' . json_encode($loan->toArray(), JSON_UNESCAPED_UNICODE));

            $loan->is_used_by_re = 0;//取消报销单关联
            $loan->re_amount = $loan->re_amount - $item->loan_amount;//返还报销金额（➖报销的冲减借款金额）
            $loan->updated_at = date('Y-m-d H:i:s');
            //15873需求需要变更借款单状态
            $paid_return_amount = bcadd($loan->re_amount, $loan->back_amount);//已归还金额
            if ($paid_return_amount == 0) {
                //未开始归还：已归还金额=0
                $loan->loan_status = Enums\LoanEnums::LOAN_STATUS_NOT_START_RETURN;
            } elseif ($paid_return_amount < $loan->amount) {
                //部分归还：已归还金额<借款金额
                $loan->loan_status = Enums\LoanEnums::LOAN_STATUS_PARTIAL_RETURN;
            } elseif ($paid_return_amount == $loan->amount) {
                //已还清：已归还总金额=借款金额
                $loan->loan_status = Enums\LoanEnums::LOAN_STATUS_PAID_OFF;
            } elseif ($paid_return_amount > $loan->amount) {
                //超额归还：已归还总金额＞借款金额
                $loan->loan_status = Enums\LoanEnums::LOAN_STATUS_OVER_RETURN;
            }
            $this->logger->info('reimbursement_free_loans 借款数据(after): ' . json_encode($loan->toArray(), JSON_UNESCAPED_UNICODE));

            //保存关联的借款信息
            if ($loan->save() === false) {
                throw new BusinessException('reimbursement_free_loans, loan表更新失败, 原因可能是: ' . get_data_object_error_msg($loan), ErrCode::$BUSINESS_ERROR);
            }

            // 清空冲减借款金额
            $bool = $item->i_update([
                'loan_amount' => 0,
                'updated_at' => date('Y-m-d H:i:s')
            ]);
            if ($bool === false) {
                throw new BusinessException('reimbursement_free_loans 更新Reimbursement表失败, 原因可能是: ' . get_data_object_error_msg($item), ErrCode::$BUSINESS_ERROR);
            }

            // 更新报销与借款关系表
            $loanObj->is_deleted = GlobalEnums::IS_DELETED;
            $loanObj->updated_at = date('Y-m-d H:i:s');

            $this->logger->info("reimbursement_free_loans 报销与借款关系表更新后数据(re_id={$item->id}): " . json_encode($loanObj->toArray(), JSON_UNESCAPED_UNICODE));

            if ($loanObj->save() === false) {
                throw new BusinessException('reimbursement_free_loans 报销与借款关系表更新失败, 原因可能是: ' . get_data_object_error_msg($loanObj), ErrCode::$BUSINESS_ERROR);
            }
        }

        return true;
    }


    /**
     * 释放报销 - 关联备用金
     *
     * @param $item
     * @return bool
     * @throws BusinessException
     */
    public function freeReserves($item)
    {
        //$detailsArr = !empty($item->Details) ? $item->Details->toArray() : [];
        //if (!empty($detailsArr)) {
        //    foreach ($detailsArr as $item) {
        //        $fundModel = ReserveFundReimburse::findFirst(
        //            [
        //                'conditions' => 'rei_detail_id = :id: and rei_id = :rei_id:',
        //                'bind' => ['id' => $item['id'],'rei_id' => $item['re_id']],
        //            ]
        //        );
        //        if (!empty($fundModel)) {
        //            $fundModel->delete();
        //        }
        //    }
        //}
        $fundModel = ReserveFundReimburse::findFirst(
            [
                'conditions' => 'rei_id = :rei_id:',
                'bind' => ['rei_id' => $item->id],
            ]
        );

        if (!empty($fundModel)) {
            $this->logger->info('报销关联备用金的待删数据' . json_encode($fundModel->toArray(), JSON_UNESCAPED_UNICODE));

            // 更新备用金申请单关联
            $fundApplyModel = ReserveFundApply::findFirst([
                'conditions' => ' rfano = :rfano: ',
                'bind' => ['rfano' => $fundModel->rfano]
            ]);
            if (!empty($fundApplyModel)) {
                $fundApplyModel->is_cite = 0; //取消关联
                $fundApplyModel->updated_at = date('Y-m-d H:i:s');
                if ($fundApplyModel->save() === false) {
                    throw new BusinessException('报销释放关联的备用金-备用金更新失败, ' . $fundApplyModel->getErrorMessagesString(), ErrCode::$BUSINESS_ERROR);
                }
            }

            // 删除关联
            if ($fundModel->delete() === false) {
                throw new BusinessException('报销释放关联的备用金-关联关系删除失败, ' . $fundModel->getErrorMessagesString(), ErrCode::$BUSINESS_ERROR);
            }
        }

        return true;
    }

    /**
     * 获得修改的日志
     *
     * @param $req
     * @return array
     */
    public function getEditLog($req)
    {
        if (empty($req)) {
            return [];
        }

        $list = WorkflowUpdateLogModel::find(
            [
                'conditions' => 'request_id = :id:',
                'bind' => ['id' => $req->id],
                'order' => 'id DESC'
            ]
        )->toArray();


        foreach ($list as $k => $item) {
            $logArr = [];
            $content = json_decode($item['content'], 1);
            foreach ($content['main'] as $kk => $vv) {
                if ($vv['is_display'] == 0) {
                    continue;
                }


                $log = [];
                $log['field_name'] = static::$t->_($vv['field_name']);
                if ($vv['need_translate']) {
                    $log['before'] = static::$t->_($vv['before']);
                    $log['after'] = static::$t->_($vv['after']);
                } else {
                    $log['before'] = $vv['before'];
                    $log['after'] = $vv['after'];
                }
                $logArr[] = $log;
            }

            $ledger_account_ids = [];
            foreach ($content['meta'] as $kk => $vv) {
                if ($vv['field_name'] == 'purchase_apply_ledger_account') {
                    if (!empty($vv['before']) && !in_array($vv['before'], $ledger_account_ids)) {
                        $ledger_account_ids[] = $vv['before'];
                    }
                    if (!empty($vv['after']) && !in_array($vv['after'], $ledger_account_ids)) {
                        $ledger_account_ids[] = $vv['after'];
                    }
                }
            }

            $ledgerIdToName = [];
            if (!empty($ledger_account_ids)) {
                $res = LedgerAccountService::getInstance()->getList($ledger_account_ids);
                if ($res['code'] == ErrCode::$SUCCESS) {
                    $ledgerIdToName = array_column($res['data'], 'name', 'id');
                }
            }

            foreach ($content['meta'] as $kk => $vv) {
                if ($vv['is_display'] == 0) {
                    continue;
                }
                $log = [];
                $log['field_name'] = $vv['no'] . "-" . static::$t->_($vv['field_name']);
                if ($vv['need_translate']) {
                    $log['before'] = static::$t->_($vv['before']);
                    $log['after'] = static::$t->_($vv['after']);
                } else {
                    //税率
                    if ($vv['field_name'] == 'purchase_product_field_wht_ratio') {
                        $log['before'] = "%" . $vv['before'];
                        $log['after'] = "%" . $vv['after'];
                    } //核算科目
                    elseif ($vv['field_name'] == 'purchase_apply_ledger_account') {
                        $log['before'] = $ledgerIdToName[$vv['before']] ?? '';
                        $log['after'] = $ledgerIdToName[$vv['after']] ?? '';
                    } else {
                        $log['before'] = $vv['before'];
                        $log['after'] = $vv['after'];
                    }
                }
                $logArr[] = $log;
            }

            $list[$k]['log'] = $logArr;
            unset($list[$k]['content']);
        }
        return $list;
    }


    /**
     * 处理更新数据
     * @param object $item 报销单据信息对象
     * @param array $edit_field 可编辑字段组
     * @param array $update_data 更新的数据
     * @param object $work_req 审批流信息对象
     * @param array $user 审批人信息组
     * @throws ValidationException
     */
    public function dealEditField($item, $edit_field, $update_data, $work_req, $user)
    {
        $mainData = [];
        $logData = [];
        $logData['main'] = [];
        $logData['meta'] = [];
        $update_flag = false;
        $company_ids = EnumsService::getInstance()->getSysDepartmentCompanyIds();

        $amount_field = ['wht_amount', 'real_amount', 'deductible_tax_amount'];
        //值需要翻译的字段
        $tran_field = ['category_a', 'category_b', 'is_tax', 'wht_type'];

        //需要展示修改的字段
        $display_field = ['ledger_account_id'];

        //展示字段的翻译
        $lang_field = [
            'purchase_apply_ledger_account',
        ];

        $langArr = array_combine($display_field, $lang_field);

        // $whtKeyArr = EnumsService::getInstance()->getWhtRateCategoryMap();

        //sap公司
        $sap_company_ids = EnumsService::getInstance()->getSettingEnvValueIds('purchase_sap_company_ids');
        //金蝶公司
        $kingdee_company_ids = EnumsService::getInstance()->getSettingEnvValueIds(KingDeeEnums::KINGDEE_BU_SETTING_CODE);
        //费用所属公司是SAP公司组里的则是1快递公司科目，否则判断是金蝶公司组里的，则认为是子公司科目，否则都认为是快递公司科目
        $account_type = in_array($item->cost_company_id, $sap_company_ids) ? KingDeeEnums::ACCOUNT_TYPE_FLASH_EXPRESS_COMPANY : (in_array($item->cost_company_id, $kingdee_company_ids) ? KingDeeEnums::ACCOUNT_TYPE_SUB_COMPANY : KingDeeEnums::ACCOUNT_TYPE_FLASH_EXPRESS_COMPANY);
        if ($item->account_type != $account_type) {
            //若审核中发生了变化，才需要更新
            array_push($edit_field['main'], 'account_type');
            $update_data['main']['account_type'] = $account_type;
        }
        //9542,新增可修改主表信息extra_message,voucher_abstract , 子表信息deductible_vat_tax
        //deductible_vat_tax变化,可抵扣税额重新计算deductible_tax_amount
        if (!empty($edit_field['main'])) {
            foreach ($edit_field['main'] as $key) {
                //如果没有定义该参数，则不修改
                if (!isset($update_data['main'][$key])) {
                    continue;
                }

                $tmp = $update_data['main'][$key];

                $is_amount = 0;
                if (in_array($key, $amount_field)) {
                    $tmp = bcmul(round($tmp, 2), 1000);
                    $is_amount = 1;
                }

                //不相等，才记录
                if ($tmp != $item->$key) {
                    $log = [];
                    $log['need_translate'] = in_array($key, $tran_field) ? 1 : 0;
                    $log['is_display'] = in_array($key, $display_field) ? 1 : 0;
                    if ($log['need_translate']) {
                        if ($key == 'is_tax') {
                            $log['before'] = 'purchase_order_is_tax_' . $item->$key;
                            $log['after'] = 'purchase_order_is_tax_' . $tmp;
                        }
                    } else {
                        if ($is_amount) {
                            $log['before'] = bcdiv($item->$key, 1000, 2);
                            $log['after'] = bcdiv($tmp, 1000, 2);
                        } else {
                            $log['before'] = $item->$key;
                            $log['after'] = $tmp;
                        }
                    }
                    $log['field_name'] = $langArr[$key] ?? $key;
                    $logData['main'][] = $log;

                    $update_flag = true;
                    $mainData[$key] = $tmp;
                }
            }
        }

        $update_data_meta = $update_data['expense'];
        if (!empty($edit_field['meta']) && !empty($update_data_meta)) {
            $model = new Detail();
            $deductible_config = EnumsService::getInstance()->getDeductibleRateValueItem();
            foreach ($update_data_meta as $k => $metaArr) {
                $metaItem = $model::findFirst([
                    'conditions' => 'id = :id: and re_id = :re_id:',
                    'bind' => ['id' => $metaArr['id'], 're_id' => $item->id],
                ]);
                if (empty($metaItem)) {
                    continue;
                }

                $metaData = [];
                foreach ($edit_field['meta'] as $key) {
                    if (Enums\GlobalEnums::TH_COUNTRY_CODE == get_country_code()) {
                        //验证必填项
                        if ($key == 'voucher_description') { //凭证描述
                            //当公司=Flash Express,此字段必填
                            if ($item->cost_company_id == $company_ids['FlashExpress'] && (!isset($metaArr['voucher_description']) || (string)$metaArr['voucher_description'] == '')) {
                                throw new ValidationException(static::$t->_('voucher_description_is_must'), ErrCode::$VALIDATE_ERROR);
                            }
                            //如果填了,验证
                            if (isset($metaArr['voucher_description'])) {
                                if (preg_match(ApplyService::CN_TH_CHAR_REGULAR, $metaArr['voucher_description'])) {  //判断字符串中是否有中文/泰文
                                    throw new ValidationException(static::$t->_('voucher_description_content_error'), ErrCode::$VALIDATE_ERROR);
                                }
                                if (stripos($metaArr['voucher_description'], '&') !== false) {
                                    throw new ValidationException(static::$t->_('voucher_description_content_error'), ErrCode::$VALIDATE_ERROR);
                                }
                                if (mb_strlen($metaArr['voucher_description']) > 40) {
                                    throw new ValidationException(static::$t->_('voucher_description_overlength'), ErrCode::$VALIDATE_ERROR);
                                }
                            }
                        }
                    }
                    //17764核算科目/费用所属中心，费用所属公司属于SAP同步公司或费用所属公司属于金蝶同步BU时必填
                    if (($key == 'ledger_account_id' || $key == 'cost_center_code') && (in_array($item->cost_company_id, $sap_company_ids) || in_array($item->cost_company_id, $kingdee_company_ids))) {
                        if (!isset($metaArr['ledger_account_id']) || empty($metaArr['ledger_account_id'])) {
                            throw new ValidationException(static::$t->_('ledger_account_id_is_must'), ErrCode::$VALIDATE_ERROR);
                        }

                        if (!isset($metaArr['cost_center_code']) || empty($metaArr['cost_center_code'])) {
                            throw new ValidationException(static::$t->_('cost_center_code_is_must'), ErrCode::$VALIDATE_ERROR);
                        }
                    }
                    if ($key == 'cost_store_n_id') { //网点/总部
                        if ($item->cost_store_type == 1) {
                            if (!isset($metaArr['cost_store_n_id']) || empty($metaArr['cost_store_n_id'])) {
                                throw new ValidationException(static::$t->_('cost_store_n_id_is_must'), ErrCode::$VALIDATE_ERROR);
                            }
                        } else {
                            if (isset($metaArr['cost_store_n_id'])) {
                                unset($metaArr['cost_store_n_id']);
                            }
                        }
                    }
                    //可抵扣税率修改,重新计算可抵扣税额
                    if ($key == 'deductible_vat_tax') {
                        $deductible_vat_tax = isset($metaArr['deductible_vat_tax']) ? $metaArr['deductible_vat_tax'] : $metaItem->deductible_vat_tax;
                        if (!in_array($deductible_vat_tax, $deductible_config)) {
                            throw new ValidationException(static::$t->_('deductable_rate_error_hint', ['VAT_SST' => EnumsService::getInstance()->getVatSStRateName()]), ErrCode::$VALIDATE_ERROR);
                        }
                    }
                    //可抵扣税额修改
                    if ($key == 'deductible_tax_amount') {
                        if (isset($metaArr['deductible_tax_amount']) && (!is_numeric($metaArr['deductible_tax_amount']) || $metaArr['deductible_tax_amount'] < 0)) {
                            throw new ValidationException(static::$t->_('deductible_tax_amount_error'), ErrCode::$VALIDATE_ERROR);
                        }
                    }
                    //如果没定义
                    if (!isset($metaArr[$key])) {
                        continue;
                    }

                    $tmp = $metaArr[$key];
                    $is_amount = 0;
                    if (in_array($key, $amount_field)) {
                        $tmp = bcmul(round($tmp, 2), 1000);
                        $is_amount = 1;
                    }
                    //不相等，才记录
                    if ($tmp != $metaItem->$key) {
                        $log = [];
                        $log['id'] = $metaItem->id;
                        $log['no'] = $k + 1;
                        $log['need_translate'] = in_array($key, $tran_field) ? 1 : 0;
                        $log['is_display'] = in_array($key, $display_field) ? 1 : 0;
                        $log['field_name'] = $langArr[$key] ?? $key;

                        //如果需要翻译，category_a,category_b，product_id为空才能修改，否则过滤
                        if ($log['need_translate']) {
                            if (!empty($metaItem->product_id)) {
                                continue;
                            }
                            if ($key == 'category_a' || $key == 'category_b') {
                                $log['before'] = 'purchase_product_category_name_' . $metaItem->$key;
                                $log['after'] = 'purchase_product_category_name_' . $tmp;
                            }

                            if ($key == 'wht_type') {
                                $log['before'] = $whtKeyArr[$metaItem->$key] ?? "";
                                $log['after'] = $whtKeyArr[$tmp] ?? "";
                            }
                        } else {
                            if ($is_amount) {
                                $log['before'] = bcdiv($metaItem->$key, 1000, 2);
                                $log['after'] = bcdiv($tmp, 1000, 2);
                            } else {
                                $log['before'] = $metaItem->$key;
                                $log['after'] = $tmp;
                            }
                        }
                        $logData['meta'][] = $log;
                        $metaData[$key] = $tmp;
                    }
                }
                if (!empty($metaData)) {
                    //修改网点/总部，影响费用所属中心
                    if (isset($metaData['cost_store_n_id'])) {
                        $metaData['cost_store_n_name'] = $metaArr['cost_store_n_name'];
                        $metaData['cost_center_code'] = $metaArr['cost_center_code'];
                    }
                    //修改可抵扣税率, 可抵扣税率发生变化则修改可抵扣金额
                    //如果可修改可抵扣金额, 直接修改可抵扣金额
                    if (isset($metaData['deductible_vat_tax'])) {
                        if (!isset($metaData['deductible_tax_amount'])) {
                            $metaData['deductible_tax_amount'] = bcmul(round($metaArr['deductible_tax_amount'], 2), 1000);
                        }
                    }
                    $update_flag = true;
                    $metaItem->update($metaData);
                }
            }
        }
        if (!empty($mainData)) {
            // 财务审核时更新补充发票时间
            if (isset($mainData['is_supplement_invoice']) && !empty($mainData['is_supplement_invoice'])) {
                $mainData['supplement_file_change_date'] = date('Y-m-d H:i:s');
            }
            $item->update($mainData);
        }

        if ($update_flag) {
            $log = new WorkflowUpdateLogModel();
            $log->save(
                [
                    'request_id' => $work_req->id,
                    'flow_id' => $work_req->flow_id,
                    'flow_node_id' => $work_req->current_flow_node_id,
                    'staff_id' => $user['id'],
                    'staff_name' => $this->getNameAndNickName($user['name'], $user['nick_name']),
                    'staff_department' => $user['department'],
                    'staff_job_title' => $user['job_title'],
                    'content' => json_encode($logData, JSON_UNESCAPED_UNICODE),
                    'created_at' => date('Y-m-d H:i:s'),
                ]
            );
        }
    }

    /**
     * 根据配置获取审批流(泰国-FlashExpress-总部和网点)
     *
     * @param object $model 报销单据信息
     * @param string $store_id 申请人所属网点id
     * @param array $info 审批流所需条件信息组（getWorkflowParams）
     * @param string $flow_config_type 报销审批流配置类型key
     * @date 2022/7/11
     * @return mixed
     * @throws ValidationException
     */
    public function getBelongCompanyFlowId(object $model, string $store_id, array $info, string $flow_config_type)
    {
        //获取所有报销实质
        $details = $model->getDetails();
        $budget_product_relation = [];
        foreach ($details as $k => $v) {
            $budget_product_relation[$v->budget_id][] = $v->product_id;
        }
        //读取配置
        $budget_type_data = AddService::getInstance()->getBudgetType($budget_product_relation);//配置数据
        $budget_types_keys = array_keys($budget_type_data);
        $flow_config = json_decode((new EnvModel())->getEnvByCode('reimbursement_create_flow_config'), true);
        $flow_config = $flow_config[$flow_config_type];
        //结合优先级判断类型,在优先级中找不到的归为其他
        $subject_numbers = array_column($flow_config, 'subject_number');
        $new_budget_types = [];
        foreach ($budget_types_keys as $one_type) {
            if (in_array($one_type, $subject_numbers)) {
                $new_budget_types[] = $one_type;
            } else {
                $new_budget_types[] = 'other';
            }
        }
        $budget_types = array_values(array_unique($new_budget_types));
        //判断优先级
        $flow_config_arr = [];
        foreach ($flow_config as $value_) {
            if (in_array($value_['subject_number'], $budget_types)) {
                $flow_config_arr[] = $value_;
            }
        }
        $weight = array_column($flow_config_arr, 'weight');
        $min_array = $flow_config_arr[array_search(min($weight), $weight)];
        //福利团建费 验证hrbp节点 v16843马来hrbp-director改成固定工号了,所以不校验
        $country_code = get_country_code();

        if ($min_array['subject_number'] == 'welfare_and_morale' && $country_code != GlobalEnums::MY_COUNTRY_CODE) {
            $hrbp_id = SysService::getInstance()->getBpHeadByStaff($info['submitter_id']);
            if (empty($hrbp_id)) {
                throw new ValidationException(static::$t->_('ordinary_payment_not_hrbp'), ErrCode::$VALIDATE_ERROR);
            }
        }
        $find_key = '';

        //查找flow_id的下标key
        if ($country_code == GlobalEnums::MY_COUNTRY_CODE) {
            $find_key = 'headquarters';
            //如果是网点, 再去判断一级部门id属于哪个网点, 如果是总部, 直接找总部审批流
            if ($model->apply_store_id != Enums::HEAD_OFFICE_STORE_FLAG) {
                //马来用部门id找
                //取网点类型对应的一级部门id
                $department_arr = EnumsService::getInstance()->getSettingEnvValueMap('appoint_store_by_department_id');
                foreach ($department_arr as $key => $value) {
                    //判断是什么类型网点
                    if ($info['department_id'] == $value) {
                        $find_key = $key;
                        break;
                    }
                }
            }

        } else {
            //其他国家用网点的category找
            if ($store_id == Enums::HEAD_OFFICE_STORE_FLAG || $flow_config_type == ReimbursementEnums::REIMBURSEMENT_FLASH_HOME) {
                //总部
                $find_key = 'headquarters';
            } else {
                $data = (new StoreRepository())->getStoreDetail($store_id);
                if (!empty($data)) {
                    $flow_key = OrdinaryPaymentEnums::FLOW_ID_ARRAY_KEY;
                    if ($country_code == GlobalEnums::PH_COUNTRY_CODE) {
                        $flow_key = OrdinaryPaymentEnums::FLOW_ID_PH_ARRAY_KEY;
                    }
                    foreach ($flow_key as $key => $category) {
                        if (in_array($data['category'], $category)) {
                            $find_key = $key;
                            break;
                        }
                    }
                }
            }
        }
        if (empty($find_key)) {
            $find_key = 'headquarters';//终极兜底 如果四项Hub、Network、Shop、Network Bulky  都没找到 即为总部
        }
        $flow_id = $min_array['store_flow_map'][$find_key];
        if (empty($flow_id)) {
            //当前部门兜底
            $other_key = array_search('other', array_column($flow_config, 'subject_number'));
            $flow_id = $flow_config[$other_key]['store_flow_map'][$find_key];
        }
        return $flow_id;
    }

    /**
     * @param $item
     * @param \Phalcon\Mvc\Model $work_req
     * @return void
     * @throws BusinessException
     */
    public function syncDataToPayment($item, \Phalcon\Mvc\Model $work_req): void
    {
        // 同步数据到支付模块
        if (EnumsService::getInstance()->getPayModuleStatus(SysConfigEnums::SYS_MODULE_REIMBURSEMENT,
            $item->cost_company_id)) {
            PayService::getInstance()->saveOne($item);
        } else {
            // 不需要同步到支付模块的单据, 给支付人员发送待支付信息邮件 v18029
            $pay_staff_ids = (new BaseService())->getReimbursementPayStaffIds();

            // 过滤费用所属公司所在管辖范围的管辖人
            $pay_staff_ids = DataPermissionModuleConfigService::getInstance()->filterStaffIdsByDepartmentId($item->cost_company_id,
                $pay_staff_ids, SysConfigEnums::SYS_MODULE_REIMBURSEMENT);

            // 获取单据费用所属公司 管辖范围内的 支付人清单
            if ($pay_staff_ids) {
                $this->sendEmailToAuditors($work_req, $pay_staff_ids, 1);
                $this->delUnReadNumsKeyByStaffIds($pay_staff_ids);
            }
        }
    }
}
