<?php

namespace App\Modules\BankFlow\Services;

use App\Library\Enums;
use App\Library\Enums\BankFlowEnums;
use App\Library\Enums\GlobalEnums;
use App\Library\Enums\ImportCenterEnums;
use App\Library\ErrCode;
use App\Library\Exception\BusinessException;
use App\Library\OssHelper;
use App\Library\Validation\ValidationException;
use App\Models\oa\OrdinaryPaymentModel;
use App\Modules\BankFlow\Models\BankAccountModel;
use App\Modules\BankFlow\Models\BankFlowAttachmentModel;
use App\Modules\BankFlow\Models\BankFlowEnumModel;
use App\Modules\BankFlow\Models\BankFlowExpenseModel;
use App\Modules\BankFlow\Models\BankFlowMetaModel;
use App\Modules\BankFlow\Models\BankFlowModel;
use App\Modules\BankFlow\Models\BankFlowOaModel;
use App\Modules\Common\Services\EnumsService;
use App\Modules\Common\Services\ImportCenterService;
use App\Modules\Loan\Models\Loan;
use App\Modules\Reimbursement\Models\Reimbursement;
use App\Modules\Cheque\Services\ChequeService;
use GuzzleHttp\Exception\GuzzleException;

class ListService extends BaseService
{
    // 交易日期最大时间段 默认 90 天
    public static $trade_date_max_between_days = 90;

    // 导出列表超出指定条数, 则临时设置内存
    private static $list_apply_mem_limit_count = 10000;

    private static $instance;

    private function __construct()
    {
    }

    private function __clone()
    {
    }

    public static function getInstance()
    {
        if (!isset(self::$instance) || !(self::$instance instanceof self)) {
            self::$instance = new self;
        }
        return self::$instance;
    }

    /**
     * 银行流水列表
     *
     * @param array $params
     * @param int $flow_type
     * @param bool $is_download 用于流水管理-导出
     * @param bool $export 用于流水上传-导出流水
     * @return mixed
     */
    public function getFlowList(array $params, int $flow_type, bool $is_download = false, $export = false)
    {
        $page_size = empty($params['pageSize']) ? 20 : $params['pageSize'];
        $page_num = empty($params['pageNum']) ? 1 : $params['pageNum'];
        $offset = $page_size * ($page_num - 1);

        $code = ErrCode::$SUCCESS;
        $message = $real_message = '';

        $data = [
            'items' => [],
            'pagination' => [
                'pageNum' => $page_num,
                'pageSize' => $page_size,
                'total_count' => 0,
            ]
        ];

        try {
            // 交易日期校验
            if ($params['trade_start_date'] > $params['trade_end_date']) {
                throw new ValidationException(static::$t->_('params_error', ['param' => 'trade_start_date']));
            } else if ((strtotime($params['trade_end_date']) - strtotime($params['trade_start_date'])) > self::$trade_date_max_between_days * 86400) {
                $error_msg = self::$t['bank_flow.list_query_trade_date_error'];
                $error_msg = str_replace('{max_days}', self::$trade_date_max_between_days, $error_msg);
                throw new ValidationException($error_msg);
            }
            $count = $this->getFlowCount($params, $flow_type);

            // 超过 1w条, 临时设置内存空间 , 目前只有脚本调用时$export为true,脚本不用设置内存
            if ($count > self::$list_apply_mem_limit_count && !$export) {
                ini_set('memory_limit', '1024M');
            }

            $items = [];
            if ($count > 0) {
                //获取条件
                $conditions_array = $this->getFlowConditions($params, $flow_type);

                //getFlowCount方法中已经判断了返回false的情况, $count>0时$conditions_array一定是数组
                $conditions = $conditions_array[0];
                $bind = $conditions_array[1];

                //字段
                $columns = [
                    'id AS flow_id',
                    'bank_id',
                    'bank_name',
                    'bank_account',
                    'date',
                    'time',
                    'ticket_no',
                    'get_amount',
                    'pay_amount',
                    'bank_left_amount',
                    'currency',
                    'trade_desc',
                    'bank_flow_expense_id',
                    'updated_at',
                    'updated_staff_id',
                    'updated_staff_name',
                    'edit_status',
                    'confirm_status',
                    'extra'
                ];

                $columns = implode(',', $columns);

                // 原实现 [未能使用最优索引, 查询慢] start //
                //查询信息汇总
                $item_cond = [
                    'conditions' => $conditions,
                    'bind' => $bind,
                    'order' => 'id ASC',
                    'columns' => $columns,
                ];

                //分页
                $item_cond['offset'] = $offset;
                $item_cond['limit'] = $page_size;

                //执行查询
//                $items = BankFlowModel::find($item_cond)->toArray();
                // 原实现 end //

                //*/ 强制使用指定索引优化 v1.0 start //
                [$conditions, $bind] = $this->convertQueryConditions($conditions, $bind);

                $sql   = "SELECT {$columns} FROM bank_flow FORCE INDEX (`idx_type_account_date`) WHERE {$conditions}";
                $sql   .= " ORDER BY id ASC";
                $sql   .= " LIMIT {$offset}, {$page_size}";
                $items = $this->db_oa->query($sql, $bind)->fetchAll(\Phalcon\Db::FETCH_ASSOC);
                // v1.0 end /*/

                if ($export) {
                    // 流水上传-导出流水
                    $items = $this->exportFlowListExcel($items);//导出流水
                } else {
                    // 流水上传-列表, 流水管理-付款流水-导出, 流水管理-收款流水-导出
                    $items = $this->handleFlowListData($items, $flow_type, $is_download);
                }
            }

            $data['items'] = $items ?? [];
            $data['pagination']['total_count'] = $count;
        } catch (ValidationException $e) {
            $code = ErrCode::$VALIDATE_ERROR;
            $message = $e->getMessage();

        } catch (BusinessException $e) {
            // 业务错误（数据错误等），不可对外抛出
            $code = $e->getCode();
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage();

        } catch (\Exception $e) {
            $code = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        }

        if (!empty($real_message)) {
            $this->logger->warning('银行流水 - 列表异常: ' . $real_message);
        }

        return [
            'code' => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data' => $data
        ];
    }

    /**
     * 解析列表查询条件
     */
    private function convertQueryConditions($conditions, $bind)
    {
        $conditions = trim_array(explode('and', $conditions));
        foreach ($conditions as &$factor) {
            // in 条件
            if (preg_match('/\{(\w+):array\}/', $factor, $matches)) {
                $cond_name = $matches[0];
                $var_name  = $matches[1];

                if (isset($bind[$var_name])) {
                    $cond_name_values = is_array($bind[$var_name]) ? implode(',', $bind[$var_name]) : $bind[$var_name];
                    $factor           = str_replace($cond_name, $cond_name_values, $factor);
                    unset($bind[$var_name]);
                }
            } else {
                $factor = rtrim($factor, ':');
            }
        }

        $conditions = implode(' and ', $conditions);
        return [$conditions, $bind];
    }

    /**
     * 处理流水列表数据
     *
     * @param array $items
     * @param int $flow_type
     * @param bool $is_download
     * @return mixed
     */
    protected function handleFlowListData(array $items, int $flow_type, bool $is_download = false)
    {
        if (empty($items)) {
            return [];
        }

        GlobalEnums::init();
        $currency_item = GlobalEnums::$currency_symbol_map;

        $translation = self::$t;
        $result = [];
        //查询扩展信息
        $bank_acct_setting = BankAccountModel::find([
            'columns' => ['id', 'account', 'company_name'],
        ])->toArray();
        $bank_acct_setting = !empty($bank_acct_setting) ? array_column($bank_acct_setting, NULL, 'id') : [];
        $flow_ids = array_column($items, 'flow_id');
        $meta_item = BankFlowMetaModel::find([
            'conditions' => 'bank_flow_id IN ({bank_flow_ids:array}) and item IN ({items:array})',
            'bind' => [
                'bank_flow_ids' => $flow_ids,
                'items' => [BankFlowEnums::BANK_FLOW_META_BANK_FLOW_FROM_BANK, BankFlowEnums::BANK_FLOW_META_BANK_FLOW_TO_BANK, BankFlowEnums::BANK_FLOW_META_BANK_FLOW_REMARK]
            ],
            'columns' => ['bank_flow_id', 'item', 'val']
        ])->toArray();
        $meta_item_kv = [];
        foreach ($meta_item as $meta_k => $meta_v) {
            $meta_item_kv[$meta_v['bank_flow_id']][$meta_v['item']] = $meta_v['val'];
        }

        $expense_type_prefix = BankFlowEnums::get_expense_type_prefix();

        //流水管理-导出
        if ($is_download) {
            $country_code = get_country_code();
            foreach ($items as $k => $item) {
                // 金额字段, 千分位展示
                $item['get_amount'] = number_format($item['get_amount'], 2);
                $item['pay_amount'] = number_format($item['pay_amount'], 2);
                $item['bank_left_amount'] = number_format($item['bank_left_amount'], 2);

                // 币种
                $item['currency'] = $currency_item[$item['currency']] ?? '';

                // 费用类型
                $item['bank_flow_expense_name'] = $translation[$expense_type_prefix . $item['bank_flow_expense_id']] ?? '';

                // 流水备注
                $bank_flow_remark = $meta_item_kv[$item['flow_id']][BankFlowEnums::BANK_FLOW_META_BANK_FLOW_REMARK] ?? '';
                // 转出银行 & 转出银行所属公司
                $from_bank_acct_id = $meta_item_kv[$item['flow_id']][BankFlowEnums::BANK_FLOW_META_BANK_FLOW_FROM_BANK] ?? '';
                $from_bank_acct = $bank_acct_setting[$from_bank_acct_id]['account'] ?? '';
                $from_bank_company = $bank_acct_setting[$from_bank_acct_id]['company_name'] ?? '';
                // 转入银行 & 转入银行所属公司
                $to_bank_acct_id = $meta_item_kv[$item['flow_id']][BankFlowEnums::BANK_FLOW_META_BANK_FLOW_TO_BANK] ?? '';
                $to_bank_acct = $bank_acct_setting[$to_bank_acct_id]['account'] ?? '';
                $to_bank_company = $bank_acct_setting[$to_bank_acct_id]['company_name'] ?? '';

                $download_data = [
                    $item['flow_id'],
                    $item['bank_name'],
                    $item['bank_account'],
                    $item['date'],
                    $item['time'],
                    $item['ticket_no'],
                    $item['get_amount'],
                    $item['pay_amount'],
                    $item['bank_left_amount'],
                    $item['currency'],
                    $item['trade_desc'],
                    $bank_flow_remark, // 流水备注
                    $item['bank_flow_expense_name'],
                    $from_bank_acct, // 转出银行
                    $to_bank_acct, // 转入银行
                    '',
                    '',
                    $from_bank_company,//转出银行所属公司
                    $to_bank_company,//转入银行所属公司
                ];
                //拼接额外信息
                if ($country_code == GlobalEnums::PH_COUNTRY_CODE) {
                    if (isset($item['extra'])) {
                        $extra_data = $this->dealExtra($item['bank_id'], $item['extra']);
                        $download_data = array_merge($download_data, $extra_data);
                    }
                } elseif ($country_code == GlobalEnums::MY_COUNTRY_CODE && isset($item['extra'])) {
                    $download_data = array_merge($download_data, array_values(json_decode($item['extra'], true)));
                }
                $result[] = $download_data;

            }

        } else {
            // 流水上传-列表 & 流水管理-列表
            // 全部流水, 需要获取附件数
            $count_item = [];
            if ($flow_type == parent::BANK_FLOW_TYPE_ALL) {
                $flow_ids = array_column($items, 'flow_id');
                $count_item = BankFlowAttachmentModel::find([
                    'conditions' => 'bank_flow_id IN ({flow_ids:array}) AND is_deleted = :is_deleted:',
                    'bind' => ['flow_ids' => $flow_ids, 'is_deleted' => 0],
                    'columns' => ['COUNT(*) AS attachment_count', 'bank_flow_id'],
                    'group' => 'bank_flow_id'
                ])->toArray();
                $count_item = array_column($count_item, 'attachment_count', 'bank_flow_id');
            }


            $bank_expense_arr = $this->expense_template_relation();
            foreach ($items as $k => $item) {
                // 金额字段, 千分位展示
                $item['get_amount'] = number_format($item['get_amount'], 2);
                $item['pay_amount'] = number_format($item['pay_amount'], 2);
                $item['bank_left_amount'] = number_format($item['bank_left_amount'], 2);

                // 币种
                $item['currency'] = $currency_item[$item['currency']] ?? '';

                // 费用类型
                $item['bank_flow_expense_name'] = $translation[$expense_type_prefix . $item['bank_flow_expense_id']] ?? '';
                $item['detail_template'] = $bank_expense_arr[$item['bank_flow_expense_id']] ?? '';

                // 全部流水时, 取附件数
                $item['attachment_count'] = '0';
                if (!empty($count_item)) {
                    $item['attachment_count'] = $count_item[$item['flow_id']] ?? '0';
                }

                // 非导出: 编辑状态
                $item['edit_status'] = $translation['bank_flow.edit_status.' . $item['edit_status']] ?? '';
                // 流水备注
                $item['bank_flow_remark'] = $meta_item_kv[$item['flow_id']][BankFlowEnums::BANK_FLOW_META_BANK_FLOW_REMARK] ?? '';

                $result[] = $item;
            }
        }
        unset($items);
        return $result;
    }


    /**
     * 获得导出模板的头，及字段
     *
     * @return array
     */
    public function getImportTplAndField()
    {
        $field = [
            'oa_type',        //所属模块
            'no',             //费用单号
        ];

        $header = [
            self::$t->_('bank_flow_field_oa_type'),
            self::$t->_('bank_flow_field_no'),
        ];
        return ['header' => $header, 'field' => $field];
    }


    /**
     * 获得导入模板
     *
     * @return array
     */
    public function getImportTpl()
    {
        $header = $this->getImportTplAndField()['header'];
        $code = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $data = [];
        try {
            $res = $this->exportExcel($header, []);
            $data = $res['data'];
        } catch (\Exception $e) {
            $this->logger->error('getImportTpl error===' . $e->getMessage());
            $code = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
        }

        return [
            'code' => $code,
            'message' => $message,
            'data' => $data
        ];
    }

    /**
     * 获取导入后的数据
     *
     * @param $data
     * @param $user
     * @return array
     */
    public function import($data, $user)
    {
        $code = ErrCode::$SUCCESS;
        $message = '';
        $res = [];

        $field = $this->getImportTplAndField()['field'];

        try {
            $noToLine = [];
            foreach ($data as $k => $v) {
                $tmp = array_combine($field, $v);
                $line = $k + 1;
                if (empty($tmp)) {
                    throw new ValidationException(self::$t->_("bank_flow_line_data_error", ['line' => $line]));
                }
                $typeId = BankFlowEnums::$name_to_oa_type_id[$tmp['oa_type']] ?? 0;
                if (empty($typeId)) {
                    throw new ValidationException(self::$t->_("bank_flow_line_data_not_found_oa_type", ['line' => $line]));
                }

                if (isset($noToLine[$tmp['no']])) {
                    throw new ValidationException(self::$t->_("bank_flow_line_data_repeat"), ['line' => $line, 'where' => $noToLine[$tmp['no']]]);
                }

                $lineData = $this->checkLineData($typeId, $tmp['no'], $tmp['no']);
                $lineData['updated_id'] = $user['id'];
                $lineData['updated_at'] = date("Y-m-d H:i:s");
                $noToLine[$tmp['no']] = $line;
                $res[] = $lineData;
            }
        } catch (ValidationException $e) {
            $code = ErrCode::$VALIDATE_ERROR;
            $message = $e->getMessage();
            $res = [];
        } catch (\Exception $e) {
            $code = ErrCode::$SYSTEM_ERROR;
            $message = $e->getMessage();
            $this->logger->notice('pay_flow import error=' . $message);
            $message = static::$t->_('retry_later');
            $res = [];
        }
        return ['code' => $code, 'message' => $message, 'data' => $res];
    }


    /**
     * 校验每行数据
     *
     * @param $typeId
     * @param $no
     * @param $line
     * @param bool $is_submit 如果是提交，多返回一个model
     * @return array
     * @throws ValidationException
     */
    public function checkLineData($typeId, $no, $line, $is_submit = false)
    {

        $model = $this->getModelByTypeId($typeId, $line);
        $model = $model->getModelByNo($no);
        if (empty($model)) {
            throw new ValidationException(self::$t->_("bank_flow_line_data_not_found_oa_data", ['line' => $line]));
        }

        $formatData = $model->getFormatData();
        if ($formatData['status'] != Enums::CONTRACT_STATUS_APPROVAL || $formatData['pay_status'] != Enums::LOAN_PAY_STATUS_PENDING) {
            throw new ValidationException(self::$t->_("bank_flow_line_data_not_found_oa_data", ['line' => $line]));
        }
        $formatData['currency_text'] = self::$t->_(GlobalEnums::$currency_item[$formatData['currency']]);
        if ($is_submit) {
            $formatData['model'] = $model;
        }

        return $formatData;
    }


    /**
     * 根据模块id返回模块model
     *
     * @param $typeId
     * @param $line integer 是否有行号
     * @return Loan
     * @throws ValidationException
     */
    public function getModelByTypeId($typeId, $line = 0)
    {
        switch ($typeId) {
            case 1:
                $model = new Loan();
                break;
            case 2:
                $model = new Reimbursement();
                break;
            default:
                if (empty($line)) {
                    throw new ValidationException(self::$t->_("bank_flow_data_not_found_oa_type"));
                } else {
                    throw new ValidationException(self::$t->_("bank_flow_line_data_not_found_oa_data", ['line' => $line]));
                }
                break;
        }
        return $model;
    }

    /**
     * 银行流水关联系统单号
     *
     * @param $params
     * @param $user
     * @return array
     */
    public function link($params, $user)
    {
        $code = ErrCode::$SUCCESS;
        $message = '';
        $res = [];
        $res['res'] = false;
        $res['diff_amount'] = 0;


        $db = $this->getDI()->get('db_oa');

        try {
            $db->begin();
            $flow = BankFlowModel::findFirst(
                [
                    'conditions' => 'id = :id:',
                    'bind' => ['id' => $params['bank_flow_id']]
                ]
            );

            //为空，或者不是付款，或者已确认
            if (empty($flow) || $flow->type != 2 || $flow->confirm_status == 2) {
                throw new ValidationException(self::$t->_("bank_flow_not_found"));
            }

            $pay_amount = $flow->pay_amount;
            $currency = $flow->currency;
            $t_amount = 0;

            $now = date("Y-m-d H:i:s");

            $data = [];

            $noToModel = [];
            $noToLine = [];

            $bankOaBatchKey = ['bank_flow_id', 'oa_type', 'oa_value', 'no', 'amount', 'currency', 'real_amount', 'created_id', 'created_at', 'updated_id', 'updated_at'];

            GlobalEnums::init();
            foreach ($params['data'] as $k => $v) {
                $line = $k + 1;
                if (isset($noToLine[$v['no']])) {
                    throw new ValidationException(self::$t->_("bank_flow_line_data_repeat"), ['line' => $line, 'where' => $noToLine[$v['no']]]);
                }
                $item = $this->checkLineData($v['oa_type'], $v['no'], $line, true);
                $item['updated_id'] = $user['id'];
                $item['updated_at'] = $now;
                $item['created_id'] = $user['id'];
                $item['created_at'] = $now;
                $item['bank_flow_id'] = $params['bank_flow_id'];
                $item['real_amount'] = GlobalEnums::getAmountByDefaultRate($item['amount'], $item['currency'], $currency);
                $t_amount = bcadd($t_amount, $item['real_amount'], 2);
                $noToModel[$v['no']] = $item['model'];
                $noToLine[$v['no']] = $line;
                unset($item['model']);
                $data[] = array_only($item, $bankOaBatchKey);
            }

            //有差额，并且是校验，就直接返回。
            $res['diff_amount'] = bcsub($pay_amount, $t_amount, 2);
            //如果不是0.00，并且是校验
            if ($res['diff_amount'] != '0.00' && $params['is_check']) {
                $res['diff_amount'] = abs($res['diff_amount']);
                throw new ValidationException('have diff amount', ErrCode::$BANK_FLOW_HAVE_DIFF_AMOUNT);
            }


            $flow_oa = new BankFlowOaModel();
            $flow_oa->batch_insert($data);

            $bankFlowData = [];

            $bankFlowData['create_id'] = $user['id'];
            $bankFlowData['create_name'] = $this->getNameAndNickName($user['name'], $user['nick_name']);
            $bankFlowData['create_department_name'] = $user['department'];
            $bankFlowData['create_job_title_name'] = $user['job_title'];
            $bankFlowData['bank_id'] = $flow->bank_id;
            $bankFlowData['bank_name'] = $flow->bank_name;
            $bankFlowData['bank_account'] = $flow->bank_account;
            $bankFlowData['date'] = $flow->date;

            foreach ($data as $item) {
                $model = $noToModel[$item['no']];
                $model->link($bankFlowData);
            }

            $flow->confirm_status = 2;
            $flow->save();
            $db->commit();
            $res['res'] = true;
        } catch (ValidationException $e) {
            $db->rollback();
            //如果是有差额，返回code=1给前端，让前端帮忙提示窗口
            if ($code == ErrCode::$BANK_FLOW_HAVE_DIFF_AMOUNT) {
                $code = ErrCode::$SUCCESS;
            } else {
                $code = ErrCode::$VALIDATE_ERROR;
                $message = $e->getMessage();
            }
        } catch (\Exception $e) {
            $code = ErrCode::$SYSTEM_ERROR;
            $this->logger->warning('bank_flow link error==' . $e->getMessage() . '---' . $e->getLine() . '---' . $e->getTraceAsString());
            $db->rollback();
            $message = static::$t->_('retry_later');
        }

        return ['code' => $code, 'message' => $message, 'data' => $res];
    }


    /**
     * 银行流水关联系统单号列表
     *
     * @param $params
     * @param $user
     * @return array
     */
    public function getOAList($bank_flow_id)
    {
        $code = ErrCode::$SUCCESS;
        $message = '';
        $data = [];

        try {
            $flow = BankFlowModel::findFirst(
                [
                    'conditions' => 'id = :bank_flow_id:',
                    'bind' => ['bank_flow_id' => $bank_flow_id]
                ]
            );
            if (empty($flow)) {
                throw new ValidationException(self::$t->_("bank_flow_not_found"));
            }

            $data = BankFlowOaModel::find(
                [
                    'conditions' => 'bank_flow_id = :bank_flow_id:',
                    'bind' => ['bank_flow_id' => $bank_flow_id]
                ]
            )->toArray();


            foreach ($data as &$item) {
                $item['currency_text'] = self::$t->_(GlobalEnums::$currency_item[$item['currency']]);
                $item['is_cancel'] = (boolean)$item['is_cancel'];
            }


        } catch (ValidationException $e) {
            $code = ErrCode::$VALIDATE_ERROR;
            $message = $e->getMessage();
        } catch (\Exception $e) {
            $code = ErrCode::$SYSTEM_ERROR;
            $this->logger->warning(
                'bank_flow getOAList error==' . $e->getMessage() . '---' . $e->getLine() . '---' . $e->getTraceAsString()
            );
            $message = static::$t->_('retry_later');
        }

        return ['code' => $code, 'message' => $message, 'data' => $data];
    }


    /**
     * 银行流水关联系统单号列表
     *
     * @param $id
     * @param $bank_flow_id
     * @param $user
     * @return array
     */
    public function cancelOA($id, $bank_flow_id, $user)
    {
        $code = ErrCode::$SUCCESS;
        $message = '';
        $data = false;
        $db = $this->getDI()->get('db_oa');
        try {

            $db->begin();

            //必须是没撤销的
            $item = BankFlowOaModel::findFirst(
                [
                    'conditions' => 'id = :id: and bank_flow_id = :bank_flow_id: and is_cancel=0',
                    'bind' => ['id' => $id, 'bank_flow_id' => $bank_flow_id]
                ]
            );
            if (empty($item)) {
                throw new ValidationException(self::$t->_("bank_flow_oa_not_found"));
            }


            $model = $this->getModelByTypeId($item->oa_type);
            $model = $model->getModelByNo($item->no);
            if (empty($model)) {
                throw new ValidationException(self::$t->_("bank_flow_data_not_found_oa_type"));
            }

            $model->cancel();

            $item->is_cancel = 1;
            $item->updated_id = $user['id'];
            $item->updated_at = date("Y-m-d H:i:s");
            $item->save();

            $data = true;

            $db->commit();

        } catch (ValidationException $e) {
            $db->rollback();
            $code = ErrCode::$VALIDATE_ERROR;
            $message = $e->getMessage();
        } catch (\Exception $e) {
            $db->rollback();
            $code = ErrCode::$SYSTEM_ERROR;
            $this->logger->warning(
                'bank_flow getOAList error==' . $e->getMessage() . '---' . $e->getLine() . '---' . $e->getTraceAsString()
            );
            $message = static::$t->_('retry_later');
        }

        return ['code' => $code, 'message' => $message, 'data' => $data];
    }

    /**
     * 付款流水 - 导出流水
     *
     * @param array $params
     * @param int $flow_type
     * @param array $data 数据
     * @return mixed
     */
    public function exportFlow(array $params, int $flow_type, $data = [])
    {
        $code = ErrCode::$SUCCESS;
        $message = '';
        $oss_file_url = '';

        try {
            $header = [];
            $country_code = get_country_code();
            if ($country_code == GlobalEnums::TH_COUNTRY_CODE) {
                $header = $this->getExpenseFieldAndHeader()['header'];
            } elseif ($country_code == GlobalEnums::PH_COUNTRY_CODE) {
                $header = $this->getExpenseFieldAndHeaderPh()['header'];
            } elseif ($country_code == GlobalEnums::MY_COUNTRY_CODE) {
                $header = $this->getExpenseFieldAndHeaderMy($params['bank_id'])['header'];
            }
            if (empty($data)) {
                $flow_list = $this->getFlowList($params, $flow_type, true);
                $data = $flow_list['data']['items'] ?? [];
            }
            $file_name = 'BankFlowExport_' . date('Ymd') . '.xlsx';
            $oss_res = $this->exportExcel($header, $data, $file_name);

            if (!empty($oss_res['code']) && $oss_res['code'] == ErrCode::$SUCCESS) {
                $oss_file_url = $oss_res['data'];
            } else {
                $this->logger->warning('银行流水 - 导出 OSS 上传异常: ' . json_encode($oss_res, JSON_UNESCAPED_UNICODE));
            }

        } catch (\Exception $e) {
            $code = ErrCode::$SYSTEM_ERROR;
            $this->logger->error('银行流水 - 导出异常: ' . $e->getMessage());
            $message = static::$t->_('retry_later');
        }


        return [
            'code' => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data' => [
                'file_url' => $oss_file_url
            ]
        ];
    }

    /**
     * 获得费用类型的导入导出头
     *
     * @return array
     */
    public function getExpenseFieldAndHeader()
    {
        $header = [
            self::$t['bank_flow.export_flow.001'],
            self::$t['bank_flow.export_flow.002'],
            self::$t['bank_flow.export_flow.003'],
            self::$t['bank_flow.export_flow.004'],
            self::$t['bank_flow.export_flow.005'],
            self::$t['bank_flow.export_flow.006'],
            self::$t['bank_flow.export_flow.007'],
            self::$t['bank_flow.export_flow.008'],
            self::$t['bank_flow.export_flow.009'],
            self::$t['bank_flow.export_flow.010'],
            self::$t['bank_flow.export_flow.011'],
            self::$t['bank_flow.export_flow.bank_flow_remark'],// 流水备注
            self::$t['bank_flow.export_flow.012'],
            self::$t['bank_flow.export_flow.013'],
            self::$t['bank_flow.export_flow.014'],
            self::$t['bank_flow.export_flow.015'],
            self::$t['bank_flow.export_flow.016'],
            self::$t['bank_flow.export_flow.transfer_out_bank_company'],// 转出银行所属公司
            self::$t['bank_flow.export_flow.transfer_into_bank_company'],// 转入银行所属公司
        ];

        //费用名字，只用id和费用类型字段
        $field = [
            'id',           // id
            'bank_name',    // 银行名字
            'bank_account', // 银行账号,
            'date',  // 交易日期
            'time',         // 交易时间
            'ticket_no',    // 支票号码
            'get_amount',   // 收款金额
            'pay_amount',   // 付款金额
            'bank_left_amount', // 账户余额
            'currency',     // 币种
            'trade_desc',   // 描述
            'bank_flow_remark',// 流水备注
            'bank_flow_expense_name',   // 费用类型名字
            'transfer_out_bank',   // 转出银行
            'transfer_to_bank',   // 转入银行
            'oa_biz_module',   // 模块
            'oa_biz_order_number',   // 单号
            'transfer_out_bank_company', // 转出银行所属公司
            'transfer_into_bank_company', // 转入银行所属公司
        ];
        return ['header' => $header, 'field' => $field];
    }

    /**
     * 获得费用类型的导入导出头-菲律宾
     * @return array
     */
    public function getExpenseFieldAndHeaderPh()
    {
        $header = [
            self::$t['bank_flow.export_flow.001'],
            self::$t['bank_flow.export_flow.002'],
            self::$t['bank_flow.export_flow.003'],
            self::$t['bank_flow.export_flow.004'],
            self::$t['bank_flow.export_flow.005'],
            self::$t['bank_flow.export_flow.006'],
            self::$t['bank_flow.export_flow.007'],
            self::$t['bank_flow.export_flow.008'],
            self::$t['bank_flow.export_flow.009'],
            self::$t['bank_flow.export_flow.010'],
            self::$t['bank_flow.export_flow.011'],
            self::$t['bank_flow.export_flow.bank_flow_remark'],// 流水备注
            self::$t['bank_flow.export_flow.012'],
            self::$t['bank_flow.export_flow.013'],
            self::$t['bank_flow.export_flow.014'],
            self::$t['bank_flow.export_flow.015'],
            self::$t['bank_flow.export_flow.016'],
            self::$t['bank_flow.export_flow.transfer_out_bank_company'],// 转出银行所属公司
            self::$t['bank_flow.export_flow.transfer_into_bank_company'],// 转入银行所属公司
            'Branch',//BDO银行额外信息
            'Narrative',//security银行额外信息
            'Reference Number', 'Remarks', 'Remarks 1', 'Remarks 2', 'Branch', 'Sender Name', 'Sender Address', 'Sender Bank',
            'Credited Amount', 'Original Amount', 'Net Amount', 'Remittance Ref 1', 'Remittance Ref 2', 'Remittance Ref 3',
            'Remittance Ref 4', 'Remittance Ref 5', 'Reversed Transaction', 'Reversal Transaction Ref No', 'Biller Name',
            'Payment Channel', 'Bills Payment Ref 1', 'Bills Payment Ref 2', 'Bills Payment Ref 3', 'Bills Payment Ref 4',
            'Bills Payment Ref 5', 'Bills Payment Ref 6', 'Bills Payment Ref 7', 'Bills Payment Ref 8', 'Bills Payment Ref 9',
            'Bills Payment Ref 10', //Union 银行额外信息
        ];
        //费用名字，只用id和费用类型字段
        $field = [
            'id',           // id
            'bank_name',    // 银行名字
            'bank_account', // 银行账号,
            'date',  // 交易日期
            'time',         // 交易时间
            'ticket_no',    // 支票号码
            'get_amount',   // 收款金额
            'pay_amount',   // 付款金额
            'bank_left_amount', // 账户余额
            'currency',     // 币种
            'trade_desc',   // 描述
            'bank_flow_remark',// 流水备注
            'bank_flow_expense_name',   // 费用类型名字
            'transfer_out_bank',   // 转出银行
            'transfer_to_bank',   // 转入银行
            'oa_biz_module',   // 模块
            'oa_biz_order_number',   // 单号
            'transfer_out_bank_company', // 转出银行所属公司
            'transfer_into_bank_company', // 转入银行所属公司
            // 额外信息
            'branch', 'narrative',
            'reference_number', 'remarks', 'remarks_1', 'remarks_2',
            'branch', 'sender_name', 'sender_address', 'sender_bank',
            'credited_amount', 'original_amount', 'net_amount',
            'remittance_ref_1', 'remittance_ref_2', 'remittance_ref_3',
            'remittance_ref_4', 'remittance_ref_5', 'reversed_transaction',
            'reversal_transaction_ref_no', 'biller_name', 'payment_channel',
            'bills_payment_ref_1', 'bills_payment_ref_2', 'bills_payment_ref_3',
            'bills_payment_ref_4', 'bills_payment_ref_5', 'bills_payment_ref_6',
            'bills_payment_ref_7', 'bills_payment_ref_8', 'bills_payment_ref_9',
            'bills_payment_ref_10',
        ];
        return ['header' => $header, 'field' => $field];
    }

    /**
     * 付款流水-导出标头字段-马来
     *
     * @param int $bank_id 银行ID
     * @return array
     */
    public function getExpenseFieldAndHeaderMy($bank_id)
    {
        $header = [
            self::$t['bank_flow.export_flow.001'],
            self::$t['bank_flow.export_flow.002'],
            self::$t['bank_flow.export_flow.003'],
            self::$t['bank_flow.export_flow.004'],
            self::$t['bank_flow.export_flow.005'],
            self::$t['bank_flow.export_flow.006'],
            self::$t['bank_flow.export_flow.007'],
            self::$t['bank_flow.export_flow.008'],
            self::$t['bank_flow.export_flow.009'],
            self::$t['bank_flow.export_flow.010'],
            self::$t['bank_flow.export_flow.011'],
            self::$t['bank_flow.export_flow.bank_flow_remark'],// 流水备注
            self::$t['bank_flow.export_flow.012'],
            self::$t['bank_flow.export_flow.013'],
            self::$t['bank_flow.export_flow.014'],
            self::$t['bank_flow.export_flow.015'],
            self::$t['bank_flow.export_flow.016'],
            self::$t['bank_flow.export_flow.transfer_out_bank_company'],// 转出银行所属公司
            self::$t['bank_flow.export_flow.transfer_into_bank_company'],// 转入银行所属公司
        ];
        $extra = BankFlowEnums::$bank_flow_file_header[GlobalEnums::MY_COUNTRY_CODE][$bank_id] ?? [];
        $header = array_merge($header, $extra);
        //费用名字，只用id和费用类型字段
        $field = [
            'id',           // id
            'bank_name',    // 银行名字
            'bank_account', // 银行账号,
            'date',  // 交易日期
            'time',         // 交易时间
            'ticket_no',    // 支票号码
            'get_amount',   // 收款金额
            'pay_amount',   // 付款金额
            'bank_left_amount', // 账户余额
            'currency',     // 币种
            'trade_desc',   // 描述
            'bank_flow_remark',// 流水备注
            'bank_flow_expense_name',   // 费用类型名字
            'transfer_out_bank',   // 转出银行
            'transfer_to_bank',   // 转入银行
            'oa_biz_module',   // 模块
            'oa_biz_order_number',   // 单号
            'transfer_out_bank_company', // 转出银行所属公司
            'transfer_into_bank_company', // 转入银行所属公司
            // 额外信息
        ];
        $field = array_merge($field, $extra);
        return ['header' => $header, 'field' => $field];
    }

    /**
     * 流水异步导入任务添加
     *
     * @param object $file_obj
     * @param array $params
     * @param array $user_info
     * @param int $flow_type
     * @return mixed
     * @throws BusinessException
     * @throws GuzzleException
     * @throws ValidationException
     */
    protected function addFlowExpenseImportTask(object $file_obj, array $params, array $user_info, int $flow_type)
    {
        // 文件生成OSS链接
        $file_path = sys_get_temp_dir() . '/' . date('YmdHis') . '_' . mt_rand(10, 10000) . ' ' . $file_obj->getName();
        $file_obj->moveTo($file_path);
        $oss_result = OssHelper::uploadFile($file_path);
        $this->logger->info(['excel_file_data_oss_result' => $oss_result]);
        if (empty($oss_result['object_url'])) {
            throw new ValidationException(static::$t->_('file_upload_error'), ErrCode::$VALIDATE_ERROR);
        }

        if ($flow_type == BankFlowEnums::BANK_FLOW_TYPE_GET_INCOME) {
            $task_type = ImportCenterEnums::TYPE_BANK_FLOW_GET_FLOW_UPLOAD_EXPENSE;
        } else {
            $task_type = ImportCenterEnums::TYPE_BANK_FLOW_PAY_FLOW_UPLOAD_EXPENSE;
        }

        ImportCenterService::getInstance()->addImportCenter($user_info, $oss_result['object_url'], $task_type, $params);

        return [
            'code' => ErrCode::$SUCCESS,
            'message' => '',
            'data' => [
                'import_method' => ImportCenterEnums::IMPORT_METHOD_ASYNC,
            ]
        ];
    }

    /**
     * 批量更新费用类型
     *
     * @param $params
     * @param $flow_type
     * @param $user
     * @param string $call_channel
     * @param null $file_obj
     * @return array
     * @throws GuzzleException
     */
    public function importFlowExpense($params, $flow_type, $user, $call_channel = 'api', $file_obj = null)
    {
        if ($call_channel == ImportCenterEnums::CALL_METHOD_METHOD_TASK) {
            $import_method = ImportCenterEnums::IMPORT_METHOD_ASYNC;
        } else {
            $import_method = ImportCenterEnums::IMPORT_METHOD_SYNC;
        }

        $code = ErrCode::$SUCCESS;
        $message = '';

        // Excel 数据
        $data = $params['excel_data'];
        $file_type = $params['file_type'];
        unset($params['excel_data']);

        // 如果文件类型是关联系统单号的, 则只允许上传 指定 条数, 非关联系统单号, 限制5000条
        $data_count = count($data);

        try {
            // 非关联单号的: 每次最多 10w 条, 超过 5000 条, 走异步
            if ($call_channel == ImportCenterEnums::CALL_METHOD_METHOD_API && $file_type == BankFlowEnums::FLOW_UPLOAD_FILE_TYPE_UNASSOCIATED_ORDER && $data_count > BankFlowEnums::EXPENSE_UPDATE_UNASSOCIATED_ALLOWED_MAX_COUNT) {
                throw new ValidationException(static::$t->_('bank_flow_expense_batch_update_max_count_error_02', ['total' => BankFlowEnums::EXPENSE_UPDATE_UNASSOCIATED_ALLOWED_MAX_COUNT]), ErrCode::$VALIDATE_ERROR);
            }

            // 关联单号的, 每次最多 5w 条, 超过 500 条, 走异步
            if ($call_channel == ImportCenterEnums::CALL_METHOD_METHOD_API && $file_type == BankFlowEnums::FLOW_UPLOAD_FILE_TYPE_ASSOCIATED_ORDER && $data_count > BankFlowEnums::EXPENSE_UPDATE_ASSOCIATE_OA_ORDER_ALLOWED_MAX_COUNT) {
                throw new ValidationException(static::$t->_('bank_flow_expense_batch_update_max_count_error', ['total' => BankFlowEnums::EXPENSE_UPDATE_ASSOCIATE_OA_ORDER_ALLOWED_MAX_COUNT]), ErrCode::$VALIDATE_ERROR);
            }

            // 超过制定条数, 创建异步任务
            if ($call_channel == ImportCenterEnums::CALL_METHOD_METHOD_API && (($file_type == BankFlowEnums::FLOW_UPLOAD_FILE_TYPE_UNASSOCIATED_ORDER && $data_count > BankFlowEnums::EXPENSE_UPDATE_UNASSOCIATED_ALLOWED_SYNC_MAX_COUNT) || ($file_type == BankFlowEnums::FLOW_UPLOAD_FILE_TYPE_ASSOCIATED_ORDER && $data_count > BankFlowEnums::EXPENSE_UPDATE_ASSOCIATE_OA_ORDER_ALLOWED_SYNC_MAX_COUNT))
            ) {
                $import_method = ImportCenterEnums::IMPORT_METHOD_ASYNC;
                return $this->addFlowExpenseImportTask($file_obj, $params, $user, $flow_type);
            }

            $db = $this->getDI()->get('db_oa');
            $db->begin();

            $data_scenes = $flow_type == BankFlowEnums::BANK_FLOW_TYPE_GET_INCOME ? 'get_edit' : 'pay_edit';

            $expenseArr = InitFlowService::getInstance()->getExpenseTypeList($data_scenes, true);
            $expenseNameToValue = array_column($expenseArr, 'value', 'label');

            // 系统支持的银行账号列表
            $bank_accounts = BankAccountModel::find([
                'conditions' => 'is_deleted = :is_deleted:',
                'bind' => ['is_deleted' => 0],
                'columns' => ['id', 'account']
            ])->toArray();
            $bank_accounts = array_column($bank_accounts, 'id', 'account');

            $expenseIdToId = [];

            // 银行流水的费用类型扩展数据: bank_flow_meta
            $flow_expense_meta = [];

            // OA 模块关联的银行流水列表: 用于从OA业务侧主表批量取数
            $oa_module_linked_flows = [];

            // 流水ID与系统单号列表
            $bank_flow_id_and_oa_biz_orders = [];

            $field = $this->getExpenseFieldAndHeader()['field'];
            $current_time = date('Y-m-d H:i:s');
            //这没有校验每个id是否存在。如果不存在，只是不更新。用flow_type限制对应费用类型跟flow_type对应关系
            foreach ($data as $k => $v) {
                // 只识别A-S列(和field保持一致)
                $v = array_slice($v, 0, count($field));
                $v = array_map('trim', $v);
                $tmp = array_combine($field, $v);
                $line = $k + 2;
                if (empty($tmp) || empty($tmp['id']) || !is_numeric($tmp['id'])) {
                    throw new ValidationException(self::$t->_('bank_flow_line_data_error', ['line' => $line]));
                }

                $tmp['id'] = intval($tmp['id']);
                $tmp['bank_flow_expense_id'] = 0;
                if (!empty($tmp['bank_flow_expense_name'])) {
                    //如果费用类型名字不为空，判断费用类型是否存在e
                    if (!isset($expenseNameToValue[$tmp['bank_flow_expense_name']])) {
                        throw new ValidationException(self::$t->_("bank_flow_line_data_not_found_expense_id", ['line' => $line]));
                    } else {
                        $tmp['bank_flow_expense_id'] = $expenseNameToValue[$tmp['bank_flow_expense_name']];
                    }

                    // 若费用类型 = 内部转账，则校验转入银行账号 和 转出银行账号
                    // 1. 必填
                    // 2. 转入银行账号 和 转出银行账号不能一样
                    // 3. 上述两类银行账号需在系统中存在
                    // 只有泰国需要校验, 菲律宾不用, 如果其他国家要加校验, 一定要注意BankFlowEnums::EXPENSE_TYPE_INTERNAL_TRANSFER_ID这个枚举的值目前是泰国的"转入转出类型"ID
                    if (get_country_code() == GlobalEnums::TH_COUNTRY_CODE && $tmp['bank_flow_expense_id'] == BankFlowEnums::EXPENSE_TYPE_INTERNAL_TRANSFER_ID) {
                        if (empty($tmp['transfer_out_bank']) || empty($tmp['transfer_to_bank'])) {
                            throw new ValidationException(self::$t->_('bank_flow_expense_update_bank_account_error_003', ['line' => $line]));
                        }

                        if ($tmp['transfer_out_bank'] == $tmp['transfer_to_bank']) {
                            throw new ValidationException(self::$t->_('bank_flow_expense_update_bank_account_error_001', ['line' => $line]));
                        }

                        if (!array_key_exists($tmp['transfer_out_bank'], $bank_accounts) || !array_key_exists($tmp['transfer_to_bank'], $bank_accounts)) {
                            throw new ValidationException(self::$t->_('bank_flow_expense_update_bank_account_error_002', ['line' => $line]));
                        }

                        // 同一笔流水的内部转账费用类型扩展数据
                        // 转出银行
                        $flow_expense_meta[] = [
                            'bank_flow_id' => $tmp['id'],
                            'item' => 'from_bank_acct_id',
                            'val' => $bank_accounts[$tmp['transfer_out_bank']],
                            'created_id' => $user['id'],
                            'created_at' => $current_time
                        ];

                        // 转入银行
                        $flow_expense_meta[] = [
                            'bank_flow_id' => $tmp['id'],
                            'item' => 'to_bank_acct_id',
                            'val' => $bank_accounts[$tmp['transfer_to_bank']],
                            'created_id' => $user['id'],
                            'created_at' => $current_time
                        ];

                        // 流水备注
                        $flow_expense_meta[] = [
                            'bank_flow_id' => $tmp['id'],
                            'item' => BankFlowEnums::BANK_FLOW_META_BANK_FLOW_REMARK,
                            'val' => $tmp['bank_flow_remark'] ?: ' ',
                            'created_id' => $user['id'],
                            'created_at' => $current_time
                        ];
                    } else {
                        // 流水备注
                        $flow_expense_meta[] = [
                            'bank_flow_id' => $tmp['id'],
                            'item' => BankFlowEnums::BANK_FLOW_META_BANK_FLOW_REMARK,
                            'val' => $tmp['bank_flow_remark'] ?: ' ',
                            'created_id' => $user['id'],
                            'created_at' => $current_time
                        ];
                    }

                    if ($tmp['oa_biz_module'] == BankFlowEnums::BANK_FLOW_OA_TYPE_CHEQUE_ACCOUNT_ZP && strstr($tmp['oa_biz_order_number'], '_')) {
                        throw new ValidationException(self::$t->_('bank_flow_dataupload_not_glide_line'), ErrCode::$VALIDATE_ERROR);

                    } elseif (in_array($tmp['oa_biz_module'], [BankFlowEnums::BANK_FLOW_OA_TYPE_CHEQUE_ACCOUNT_ZPZFFK, BankFlowEnums::BANK_FLOW_OA_TYPE_CHEQUE_ACCOUNT_ZPGM])) {

                        if (!strstr($tmp['oa_biz_order_number'], '_')) {
                            throw new ValidationException(self::$t->_('bank_flow_dataupload_mast_glide_line'), ErrCode::$VALIDATE_ERROR);
                        }
                        if (count(explode('_', $tmp['oa_biz_order_number'])) > 2) {
                            throw new ValidationException(self::$t->_('bank_flow_dataupload_multiple_glide_line'), ErrCode::$VALIDATE_ERROR);
                        }
                    }

                    // 流水备注
                    if (mb_strlen($tmp['bank_flow_remark']) > 1000) {
                        throw new ValidationException(self::$t->_('bank_flow_expense_update_bank_flow_remark_error', ['line' => $line]));
                    }
                }

                if (empty($expenseIdToId[$tmp['bank_flow_expense_id']])) {
                    $expenseIdToId[$tmp['bank_flow_expense_id']] = [];
                }

                $expenseIdToId[$tmp['bank_flow_expense_id']][] = $tmp['id'];

                // 流水关联的OA系统单号
                if ($file_type == BankFlowEnums::FLOW_UPLOAD_FILE_TYPE_ASSOCIATED_ORDER) {
                    if (empty($tmp['oa_biz_module']) || empty($tmp['oa_biz_order_number'])) {
                        throw new ValidationException(self::$t->_('bank_flow_expense_update_biz_info_error_006', ['line' => $line]));
                    }

                    $oa_module_linked_flows[$tmp['oa_biz_module']][] = [
                        'flow_id' => $tmp['id'],
                        'oa_biz_module' => $tmp['oa_biz_module'],
                        'oa_biz_order_number' => (string)$tmp['oa_biz_order_number'],
                        'excel_line_no' => $line
                    ];

                    // 文件类型为关联系统单号, 每一笔流水只允许关联一笔系统单
                    $bank_flow_id_and_oa_biz_orders[] = [
                        'flow_id' => $tmp['id'],
                        'oa_biz_order_number' => $tmp['oa_biz_order_number']
                    ];
                }
            }

            // 如果是关联系统单号类型的, 则校验系统单号相关的逻辑
            if ($file_type == BankFlowEnums::FLOW_UPLOAD_FILE_TYPE_ASSOCIATED_ORDER) {
                $this->logger->info('银行流水 - 付款 - 费用类型批量上传 - 待关联的系统单号:' . json_encode($oa_module_linked_flows, JSON_UNESCAPED_UNICODE));
                $this->logger->info('银行流水 - 付款 - 费用类型批量上传 - 流水与单号映射关系:' . json_encode($bank_flow_id_and_oa_biz_orders, JSON_UNESCAPED_UNICODE));

                // 1. 获取流水基础校验: 付款流水
                $bank_flow_ids = array_values(array_unique(array_column($bank_flow_id_and_oa_biz_orders, 'flow_id')));
                $oa_biz_orders_flow_id_map = array_column($bank_flow_id_and_oa_biz_orders, 'flow_id', 'oa_biz_order_number');

                // 分批获取, 异步数据量较大, 每批次 5000
                $bank_flows = [];

                $bank_flow_ids_chunk = array_chunk($bank_flow_ids, 3000);
                foreach ($bank_flow_ids_chunk as $flow_ids) {
                    $_bank_flows = BankFlowModel::find([
                        'conditions' => 'id IN ({ids:array})',
                        'bind' => ['ids' => $flow_ids],
                        'columns' => [
                            'id',
                            'type',
                            'date',
                            'time',
                            'pay_amount',
                            'currency',
                            'confirm_status',
                            'bank_id',
                            'bank_name',
                            'bank_account',
                            'bank_account_id',
                            'ticket_no',
                            'created_at'
                        ]
                    ])->toArray();

                    $bank_flows = array_merge($bank_flows, $_bank_flows);

                    // task 调用, 歇一会
                    if ($call_channel == ImportCenterEnums::CALL_METHOD_METHOD_TASK) {
                        sleep(2);
                    }
                }

                unset($bank_flow_ids_chunk);
                if (empty($bank_flows)) {
                    throw new ValidationException(self::$t->_('bank_flow_not_found'));
                }

                foreach ($bank_flows as $flow) {
                    // 非付款流水, 不可关联系统单号
                    if ($flow['type'] != BankFlowEnums::BANK_FLOW_TYPE_PAY_OUTLAY) {
                        throw new ValidationException(self::$t->_('bank_flow_not_found'));
                    }
                }

                // 2. 业务侧模块校验及数据处理
                $bank_flows = array_column($bank_flows, null, 'id');

                // 待写入的流水与系统单号关系数据
                $bank_flow_oa_item_arr = [];

                // 待更新的业务侧数据
                $oa_biz_update_data = [];
                $oa_biz_update_data['create_id'] = $user['id'];
                $oa_biz_update_data['create_name'] = $this->getNameAndNickName($user['name'], $user['nick_name']);
                $oa_biz_update_data['create_department_name'] = $user['department'];
                $oa_biz_update_data['create_job_title_name'] = $user['job_title'];

                $pay_flow_service = new \App\Modules\Pay\Services\PayFlowService();
                //是否需要包含已支付数据
                $has_pay = EnumsService::getInstance()->getSettingEnvValue('bank_flow_link_oa_prepaid') == 1;
                $country = get_country_code();

                $system_pay_module_status = EnumsService::getInstance()->getSystemPayModuleStatus();
                foreach ($oa_module_linked_flows as $module_key => $module_info) {
                    $type_id = BankFlowEnums::$name_to_oa_type_id[$module_key] ?? '';
                    if (empty($type_id)) {
                        throw new ValidationException(self::$t->_("bank_flow_line_data_not_found_oa_type"));
                    }
                    if (!in_array($country, [GlobalEnums::MY_COUNTRY_CODE, GlobalEnums::PH_COUNTRY_CODE]) && in_array($type_id, [BankFlowEnums::BANK_FLOW_OA_TYPE_CHEQUE_APPLY, BankFlowEnums::BANK_FLOW_OA_TYPE_CHEQUE_ACCOUNT, BankFlowEnums::BANK_FLOW_OA_TYPE_CHEQUE_ACCOUNT_OTHER])) {
                        throw new ValidationException(self::$t->_('bank_flow_my_or_ph_not_data'));
                    }

                    if ($type_id == BankFlowEnums::BANK_FLOW_OA_TYPE_CHEQUE_ACCOUNT_OTHER) {
                        $group_arr = ChequeService::getInstance()->chequeBusinessGroup($module_info);
                    } else {
                        $group_arr = [['module_info' => $module_info, 'type_id' => $type_id]];
                    }

                    //分组支票支付包含普通付款和采购
                    foreach ($group_arr as $group) {
                        $type_id = $group['type_id'];
                        // 实例化相关业务模块并获取相关业务数据
                        $nos = array_column($group['module_info'], 'oa_biz_order_number');

                        if ($type_id == BankFlowEnums::BANK_FLOW_OA_TYPE_CHEQUE_APPLY && count(array_unique($nos)) != count($nos)) {
                            //对支票工本费  不能有相同的单号和id
                            throw new ValidationException(self::$t->_('cheque_apply_detail_not_glide_err'), ErrCode::$VALIDATE_ERROR);
                        }

                        //校验数据
                        if (in_array($type_id, [BankFlowEnums::BANK_FLOW_OA_TYPE_CHEQUE_ACCOUNT, BankFlowEnums::BANK_FLOW_OA_TYPE_CHEQUE_ACCOUNT_OTHER])) {
                            $all_no = [];
                            foreach ($nos as $no_arr) {
                                $cheque_arr = explode('_', $no_arr);
                                $all_no[] = $cheque_arr[0];
                            }
                            if (count(array_unique($all_no)) != count($nos)) {
                                throw new ValidationException(self::$t->_('cheque_code_not_the_same'), ErrCode::$VALIDATE_ERROR);
                            }
                        }

                        // 支付模块批量修改状态
                        // 支付模块第三节点审批人赋值
                        if ($system_pay_module_status) {
                            $pay_flow_service->autoUpdatePayStatus($user, $type_id, $nos);
                        }

                        $model = PayFlowService::getInstance()->getModelByTypeId($type_id);
                        $models = $model->getModelByNos($nos, $has_pay, false);

                        //返回为空的提示
                        if (is_object($models) && empty($models->toArray())) {
                            throw new ValidationException(self::$t->_('bank_flow_data_not_found'), ErrCode::$VALIDATE_ERROR);
                        }
                        //校验不通过的提示
                        if (is_array($models) && !empty($models['msg'])) {
                            throw new ValidationException(self::$t->_($models['msg']), ErrCode::$VALIDATE_ERROR);
                        }

                        foreach ($models as $model) {
                            $bank_flow_oa_item = [];
                            if ($type_id == BankFlowEnums::BANK_FLOW_OA_TYPE_CHEQUE_ACCOUNT_ORDINARY_PAYMENT) {
                                $biz_order_info = (new OrdinaryPaymentModel())->getFormatData($model);
                            } else {
                                $biz_order_info = $model->getFormatData();
                            }
                            // 取系统单号的币种/金额 与 流水的币种金额校验
                            $flow_id = $oa_biz_orders_flow_id_map[$biz_order_info['no']] ?? 0;
                            if (empty($flow_id)) {
                                throw new ValidationException(self::$t->_('bank_flow_not_found'), ErrCode::$VALIDATE_ERROR);
                            }

                            $flow_info = $bank_flows[$flow_id] ?? [];
                            if (empty($flow_info)) {
                                throw new ValidationException(self::$t->_('bank_flow_not_found'), ErrCode::$VALIDATE_ERROR);
                            }
                            // 业务侧币种 需和 流水币种一致
                            if ($biz_order_info['currency'] != $flow_info['currency']) {
                                throw new ValidationException(self::$t->_('bank_flow_expense_update_biz_info_error_004'), ErrCode::$VALIDATE_ERROR);
                            }

                            // 业务侧金额 需和 流水金额一致
                            $this->logger->info("银行流水 - 付款 - 费用类型批量上传 - payment_amount: {$flow_info['pay_amount']}, biz_amount:{$biz_order_info['amount']}");
                            $this->logger->info("银行流水 - 付款 - 费用类型批量上传 - flow_info: " . json_encode($flow_info, JSON_UNESCAPED_UNICODE) . " biz_order_info:" . json_encode($biz_order_info, JSON_UNESCAPED_UNICODE));

                            if ($biz_order_info['amount'] != $flow_info['pay_amount']) {
                                throw new ValidationException(self::$t->_('bank_flow_expense_update_biz_info_error_005'), ErrCode::$VALIDATE_ERROR);
                            }

                            $bank_flow_oa_item[] = [
                                'bank_flow_id' => $flow_id,
                                'oa_type' => $biz_order_info['oa_type'],
                                'oa_value' => $biz_order_info['oa_value'],
                                'no' => $biz_order_info['no'],
                                'amount' => $biz_order_info['amount'],
                                'currency' => $biz_order_info['currency'],
                                'real_amount' => $biz_order_info['amount'],
                                'diff_amount' => 0,
                                'diff_info' => "Batch Auto Be Linked [{$module_key}]",
                                'created_id' => $user['id'],
                                'updated_id' => $user['id'],
                                'bank_account_id' => $flow_info['bank_account_id'],
                                'date' => $flow_info['date'],
                                'created_at' => $current_time,
                                'updated_at' => $current_time,
                            ];

                            $bank_flow_oa_item_arr[] = $bank_flow_oa_item[0];
                            if (in_array($country, [GlobalEnums::MY_COUNTRY_CODE, GlobalEnums::PH_COUNTRY_CODE])) {
                                //$flow_info 每次会重新赋值
                                $oa_biz_update_data['flow_info'] = $flow_info;
                                $oa_biz_update_data['order_info'] = $biz_order_info;
                                $oa_biz_update_data['bank_flow_oa'] = $bank_flow_oa_item;
                                $oa_biz_update_data['is_link'] = 0;//区分是行还是头上传
                                if ($type_id == BankFlowEnums::BANK_FLOW_OA_TYPE_CHEQUE_APPLY && $biz_order_info['amount'] <= 0) {
                                    throw new ValidationException(self::$t->_('pay_data_list_upload_cheque_not_zero'));
                                }
                            }

                            // 更新业务侧数据
                            $oa_biz_update_data['bank_id'] = $flow_info['bank_id'];
                            $oa_biz_update_data['bank_name'] = $flow_info['bank_name'];
                            $oa_biz_update_data['bank_account'] = $flow_info['bank_account'];
                            $oa_biz_update_data['date'] = $flow_info['date'];
                            $oa_biz_update_data['ticket_no'] = $flow_info['ticket_no'] ?? '';

                            $this->logger->info('银行流水 - 付款 - 费用类型批量上传 - 待更新的业务侧数据:' . json_encode($oa_biz_update_data, JSON_UNESCAPED_UNICODE));
                            $this->logger->info('银行流水 - 付款 - 费用类型批量上传 - 待更新的业务侧id:' . $biz_order_info['oa_value']);
                            if ($type_id == BankFlowEnums::BANK_FLOW_OA_TYPE_CHEQUE_ACCOUNT_ORDINARY_PAYMENT) {
                                (new OrdinaryPaymentModel())->batch_link([$biz_order_info['oa_value']], $oa_biz_update_data);
                            } else {
                                $model->batch_link([$biz_order_info['oa_value']], $oa_biz_update_data);
                            }
                        }
                    }

                    // task调用, 歇一会
                    if ($call_channel == ImportCenterEnums::CALL_METHOD_METHOD_TASK) {
                        sleep(5);
                    }
                }

                $this->logger->info('待写入的流水关联单号数据: ' . json_encode($bank_flow_oa_item_arr, JSON_UNESCAPED_UNICODE));

                /**
                 * 2022-08-04测试测出关联系统单号，excel中仅有一条数据且存在错误的单号
                 * $bank_flow_oa_item 是[]，走批量插入会报系统错误，跟产品沟通后，做如下逻辑补充：
                 * 1.确实没有需要要插入的银行流水数据
                 * 2.如果文件中，有多笔流水需要更新，提交时，有一笔有问题，则整个文件报错，且不更新数据库
                 */
                if (empty($bank_flow_oa_item_arr) || count($bank_flow_ids) != count($bank_flow_oa_item_arr)) {
                    throw new ValidationException(static::$t->_('bank_flow_expense_update_biz_info_error_008'));
                }

                //去除支票支付租房付款的数据
                foreach ($bank_flow_oa_item_arr as &$bank_flow_oa_item_arr) {
                    if ($bank_flow_oa_item_arr['oa_type'] != BankFlowEnums::BANK_FLOW_OA_TYPE_CHEQUE_ACCOUNT) {
                        $bank_flow_oa_arr[] = $bank_flow_oa_item_arr;
                    }
                }

                //如果不是使用支票租房付款的时候
                if (!empty($bank_flow_oa_arr)) {
                    $bank_flow_oa_model = new BankFlowOaModel();

                    // 分批写入
                    $bank_flow_oa_arr_chunk = array_chunk($bank_flow_oa_arr, 3000);
                    foreach ($bank_flow_oa_arr_chunk as $flow_oa_chunk) {
                        $bank_flow_oa_model_batch_add_res = $bank_flow_oa_model->batch_insert($flow_oa_chunk);
                        $this->logger->info('写入结果: ' . $bank_flow_oa_model_batch_add_res);
                        if ($bank_flow_oa_model_batch_add_res === false) {
                            throw new BusinessException('银行流水 - 付款 - 费用类型批量上传 - oa关联系统单号批量写入失败', ErrCode::$BANK_FLOW_BATCH_LINKED_OA_ORDER_INSERT_ERROR);
                        }
                    }
                    unset($bank_flow_oa_arr_chunk);
                }

                // 3. 更新银行流水确认状态
                // 分批更新
                $bank_flow_ids_chunk = array_chunk($bank_flow_ids, 3000);
                foreach ($bank_flow_ids_chunk as $flow_ids_chunk) {
                    $_bank_flow_ids = implode(',', $flow_ids_chunk);
                    $update_flow_sql = "
                    -- 批量更新银行流水确认状态
                    UPDATE
                        bank_flow
                    SET
                        confirm_status = :confirm_status, updated_staff_id = :updated_staff_id, updated_staff_name = :updated_staff_name, updated_at = :updated_at
                    WHERE
                        id IN ({$_bank_flow_ids})
                    ";
                    $update_flow_sql_params = [
                        'confirm_status' => BankFlowEnums::BANK_FLOW_CONFIRMED_STATUS,
                        'updated_staff_id' => $user['id'],
                        'updated_staff_name' => $user['name'],
                        'updated_at' => $current_time,
                    ];

                    $this->logger->info('银行流水 - 付款 - 费用类型批量上传 - 批量更新银行流水确认状态SQL: ' . $update_flow_sql);
                    $this->logger->info('银行流水 - 付款 - 费用类型批量上传 - 批量更新银行流水确认状态SQL 参数: ' . json_encode($update_flow_sql_params, JSON_UNESCAPED_UNICODE));

                    $flow_update_res = $db->execute($update_flow_sql, $update_flow_sql_params);
                    $this->logger->info('批量更新银行流水确认状态结果: ' . $flow_update_res);
                    if ($flow_update_res === false) {
                        throw new BusinessException('银行流水-批量更新费用类型==更新流水的确认状态失败 = ' . $update_flow_sql . json_encode($update_flow_sql_params, JSON_UNESCAPED_UNICODE));
                    }
                }
            }

            // 产品沟通: 可以编辑附加枚举的费用类型都不允许批量更新, is_batch=0 ,不会通过上传导入,所以每次清空扩展表就行
            // 流水费用拓展数据处理(此处是批量, 流水编辑是单条)
            if (!empty($flow_expense_meta)) {
                // 1. 批量删除
                // 分批删除
                $waiting_deleted_flow_ids = array_values(array_filter(array_unique(array_column($flow_expense_meta, 'bank_flow_id'))));
                $waiting_deleted_flow_ids_chunk = array_chunk($waiting_deleted_flow_ids, 3000);
                foreach ($waiting_deleted_flow_ids_chunk as $del_bank_flow_ids) {
                    $exist_meta_item_model = BankFlowMetaModel::find([
                        'conditions' => 'bank_flow_id IN ({bank_flow_ids:array})',
                        'bind' => ['bank_flow_ids' => $del_bank_flow_ids]
                    ]);

                    if ($exist_meta_item_model->delete() === false) {
                        throw new BusinessException('银行流水费用类型批量上传, meta已有数据删除失败: ' . get_data_object_error_msg($exist_meta_item_model), ErrCode::$BANK_FLOW_EXPENSE_TYPE_SAVE_EXIST_EXPAND_DEL_ERROR);
                    }
                }

                // 2. 批量插入
                // 分批写入
                $bank_flow_meta_model = new BankFlowMetaModel();
                $flow_expense_meta_chunk = array_chunk($flow_expense_meta, 3000);
                foreach ($flow_expense_meta_chunk as $meta_chunk) {
                    $meta_mode_batch_add_res = $bank_flow_meta_model->batch_insert($meta_chunk);
                    if ($meta_mode_batch_add_res === false) {
                        throw new BusinessException('银行流水费用类型批量上传, meta批量写入失败, 待写入数据为: ' . json_encode($flow_expense_meta, JSON_UNESCAPED_UNICODE), ErrCode::$BANK_FLOW_EXPENSE_TYPE_SAVE_EXIST_EXPAND_DEL_ERROR);
                    }
                }
            }

            // 更新流水主表数据
            $updated_at = date('Y-m-d H:i:s');
            foreach ($expenseIdToId as $expenseId => $expense_flow_ids) {
                // 分批更新
                $expense_flow_ids_chunk = array_chunk($expense_flow_ids, 3000);
                foreach ($expense_flow_ids_chunk as $ids) {
                    $sql = 'update bank_flow set
                     bank_flow_expense_id=' . intval($expenseId) . ',
                     updated_staff_id="' . $user['id'] . '",
                     updated_staff_name="' . $user['name'] . '",
                     updated_at="' . $updated_at . '",
                     edit_status=2
                     where id in (' . implode(",", $ids) . ') and `type`=' . intval($flow_type);
                    $bool = $db->execute($sql);
                    if ($bool === false) {
                        throw new BusinessException('银行流水-批量更新费用类型==' . $sql);
                    }

                    if ($call_channel == ImportCenterEnums::CALL_METHOD_METHOD_TASK) {
                        sleep(5);
                    }
                }
            }

            $db->commit();

        } catch (ValidationException $e) {
            $code = ErrCode::$VALIDATE_ERROR;
            $message = $e->getMessage();
        } catch (BusinessException $e) {
            $code = ErrCode::$BUSINESS_ERROR;
            $message = static::$t->_('retry_later');
            $this->logger->warning('importFlowExpense error (BusinessException) : ' . $e->getMessage());
        } catch (\Exception $e) {
            $code = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $this->logger->error('importFlowExpense error (Exception) : ' . $e->getMessage());
        }

        if (!empty($message) && isset($db)) {
            $db->rollback();
        }

        $return_data = [
            'import_method' => $import_method,
        ];

        if ($call_channel == ImportCenterEnums::CALL_METHOD_METHOD_TASK) {
            $return_data['success_count'] = $code == ErrCode::$SUCCESS ? $data_count : 0;
            $return_data['error_count'] = $code == ErrCode::$SUCCESS ? 0 : $data_count;
        }

        return [
            'code' => $code,
            'message' => $message,
            'data' => $return_data
        ];
    }

    /**
     * 费用类型模版映射关系
     * */
    public function expense_template_relation()
    {
        $bank_expense_arr = BankFlowExpenseModel::find([
            "columns" => "id,template_type",
            'conditions' => 'is_deleted = 0'
        ])->toArray();

        $bank_expense_arr = array_column($bank_expense_arr, 'template_type', 'id');

        return $bank_expense_arr ?? [];
    }


    /**
     * 流水导出数据处理
     * */
    protected function handleFlowListDataExport($items)
    {

        $translation = self::$t;
        $bank_flow_id = array_column($items, 'flow_id');

        //查询
        // 获取扩展的字段
        $meta_item = BankFlowMetaModel::find([
            'conditions' => 'bank_flow_id IN ({bank_flow_ids:array})',

            'bind' => ['bank_flow_ids' => $bank_flow_id],
            'columns' => ['bank_flow_id', 'item', 'val']
        ])->toArray();
        $meta_item_arr = [];
        foreach ($meta_item as $k1 => $v1) {
            $meta_item_arr[$v1['bank_flow_id']][] = $v1;
        }

        $bank_flow_expense = BankFlowExpenseModel::find([
            'columns' => ['id', 'relation_item']
        ])->toArray();

        $bank_flow_expense = array_column($bank_flow_expense, 'relation_item', 'id');


        // 获取自定义枚举名称
        $enums_setting = BankFlowEnumModel::find([
            'columns' => ['item', 'value_lang_key', 'value'],
        ])->toArray();
        $bank_acct_setting = BankAccountModel::find([
            'columns' => ['id', 'account', 'company_name'],
        ])->toArray();
        $bank_acct_setting = !empty($bank_acct_setting) ? array_column($bank_acct_setting, null, 'id') : [];

        $expense_type_prefix = BankFlowEnums::get_expense_type_prefix();

        foreach ($items as $k => $item) {
            $meta_item = [];
            // 金额字段, 千分位展示
            $item['get_amount'] = number_format($item['get_amount'], 2);
            $item['pay_amount'] = number_format($item['pay_amount'], 2);
            $item['bank_left_amount'] = number_format($item['bank_left_amount'], 2);
            // 币种
            $item['currency'] = $currency_item[$item['currency']] ?? '';
            // 费用类型
            $item['bank_flow_expense_name'] = $translation[$expense_type_prefix . $item['bank_flow_expense_id']] ?? '';
            $meta_item = $meta_item_arr[$item['flow_id']] ?? '';
            $meta_item = !empty($meta_item) ? array_column($meta_item, 'val', 'item') : [];

            if (empty($meta_item)) {
                if (isset($item['bank_flow_expense_id']) && !empty($bank_flow_expense[$item['bank_flow_expense_id']])) {
                    $relation_item = explode(',', $bank_flow_expense[$item['bank_flow_expense_id']]);
                    foreach ($relation_item as $item_1) {
                        $meta_item[$item_1] = '';
                    }
                }
            }
            if (!empty($meta_item)) {
                // 获取银行账号
                foreach ($meta_item as $meta_key => $meta_val) {
                    // 银行账号枚举
                    if (in_array($meta_key, ['from_bank_acct_id', 'to_bank_acct_id'])) {
                        $meta_item[$meta_key . '_label'] = $bank_acct_setting[$meta_val]['account'] ?? ' ';
                        continue;
                    }
                    // 普通枚举
                    $meta_item[$meta_key . '_label'] = '';

                    foreach ($enums_setting as $enum) {
                        if ($meta_key == $enum['item']) {
                            if ($meta_val == $enum['value']) {
                                $meta_item[$meta_key . '_label'] = self::$t[$enum['value_lang_key']] ?? '';
                            }
                        }
                    }
                }
            }
            // 流水备注
            $item['bank_flow_remark'] = $meta_item[BankFlowEnums::BANK_FLOW_META_BANK_FLOW_REMARK] ?? '';
            // 转出银行 & 转出银行所属公司
            $from_bank_acct_id = $meta_item[BankFlowEnums::BANK_FLOW_META_BANK_FLOW_FROM_BANK] ?? 0;
            $item['from_bank_company'] = $bank_acct_setting[$from_bank_acct_id]['company_name'] ?? '';
            // 转入银行 & 转入银行所属公司
            $to_bank_acct_id = $meta_item[BankFlowEnums::BANK_FLOW_META_BANK_FLOW_TO_BANK] ?? 0;
            $item['to_bank_company'] = $bank_acct_setting[$to_bank_acct_id]['company_name'] ?? '';

            $result[] = array_merge($item, $meta_item);
        }

        return $result ?? [];
    }

    /**
     * 流水导出表头
     * */
    public function handleFlowListHeader($item)
    {
        $new_data = [];
        $file_name = "flow_list_" . date("Ymd") . '.xlsx';
        $header = [
            'ID',
            static::$t->_('csr_field_flow_bank_name'),// '银行',
            static::$t->_('csr_field_flow_bank_account'),  // '银行账号',
            static::$t->_('csr_field_flow_date'), // '交易日期',
            static::$t->_('csr_field_flow_ticket_no'),// '支票号码',
            static::$t->_('csr_field_flow_get_amount'),// '收款',
            static::$t->_('csr_field_flow_pay_amount'),// '付款',
            static::$t->_('csr_field_flow_bank_left_amount'),// '余额',
            static::$t->_('csr_field_flow_trade_desc'),// '描述',
            static::$t->_('csr_field_flow_bank_flow_expense_name'),// '费用类型',
            static::$t->_('csr_field_flow_from_bank'),// '转出银行',
            static::$t->_('bank_flow.export_flow.transfer_out_bank_company'),// 转出银行所属公司,
            static::$t->_('csr_field_flow_to_bank'),// 转入银行,
            static::$t->_('bank_flow.export_flow.transfer_into_bank_company'),// 转入银行所属公司,
            static::$t->_('csr_field_flow_refund_type'),// '类型',
            static::$t->_('csr_filed_flow_store_name'),// '网点名称',
            static::$t->_('csr_field_flow_refund_type_mark'),// '备注',
            static::$t->_('bank_flow.export_flow.bank_flow_remark'),// '流水备注',

        ];
        foreach ($item as $key => $value) {
            $new_data[] = [
                $value['flow_id'],
                $value['bank_name'],
                $value['bank_account'],
                $value['date'],
                $value['ticket_no'],
                $value['get_amount'],
                $value['pay_amount'],
                $value['bank_left_amount'],
                $value['trade_desc'],
                $value['bank_flow_expense_name'],
                $value['from_bank_acct_id_label'] ?? '',
                $value['from_bank_company'] ?? '',
                $value['to_bank_acct_id_label'] ?? '',
                $value['to_bank_company'] ?? '',
                $value['refund_type_label'] ?? '',
                $value['store_name'] ?? '',
                $value['refund_type_mark'] ?? '',
                $value['bank_flow_remark'] ?? '',
            ];
        }

        return $this->exportExcel($header, $new_data, $file_name);
    }

    /**
     * 导出流水
     * */
    public function exportFlowListExcel($items)
    {
        $data = [];
        if (!empty($items)) {
            $data = $this->handleFlowListDataExport($items);
        }
        return $data;
    }

    public function dealExtra($bank_id, $data)
    {
        $data = json_decode($data, true);
        $return_data = [];
        if ($bank_id == BankFlowEnums::BANK_CODE_BDO) {
            $return_data = [
                $data['branch'] ?? '',
            ];
        }
        if ($bank_id == BankFlowEnums::BANK_CODE_SECURITY) {
            $return_data = [
                '',
                $data['narrative'] ?? '',
            ];
        }
        if ($bank_id == BankFlowEnums::BANK_CODE_UNION) {
            $return_data = [
                '',
                '',
                $data['reference_number'] ?? '',
                $data['remarks'] ?? '',
                $data['remarks_1'] ?? '',
                $data['remarks_2'] ?? '',
                $data['branch'] ?? '',
                $data['sender_name'] ?? '',
                $data['sender_address'] ?? '',
                $data['sender_bank'] ?? '',
                $data['credited_amount'] ?? '',
                $data['original_amount'] ?? '',
                $data['net_amount'] ?? '',
                $data['remittance_ref_1'] ?? '',
                $data['remittance_ref_2'] ?? '',
                $data['remittance_ref_3'] ?? '',
                $data['remittance_ref_4'] ?? '',
                $data['remittance_ref_5'] ?? '',
                $data['reversed_transaction'] ?? '',
                $data['reversal_transaction_ref_no'] ?? '',
                $data['biller_name'] ?? '',
                $data['payment_channel'] ?? '',
                $data['bills_payment_ref_1'] ?? '',
                $data['bills_payment_ref_2'] ?? '',
                $data['bills_payment_ref_3'] ?? '',
                $data['bills_payment_ref_4'] ?? '',
                $data['bills_payment_ref_5'] ?? '',
                $data['bills_payment_ref_6'] ?? '',
                $data['bills_payment_ref_7'] ?? '',
                $data['bills_payment_ref_8'] ?? '',
                $data['bills_payment_ref_9'] ?? '',
                $data['bills_payment_ref_10'] ?? '',
            ];
        }

        return $return_data;
    }


    /**
     * 获取条件和参数绑定
     *
     * @param $params
     * @param $flow_type
     * @date 2023/8/11
     * @return array|bool  array:条件和参数绑定 | bool:false表示银行账号的条件没查到数据,不用在查流水表了
     */
    public function getFlowConditions($params, $flow_type)
    {
        $trade_start_date = $params['trade_start_date'] ?? '';
        $trade_end_date = $params['trade_end_date'] ?? '';
        $ticket_no = $params['ticket_no'] ?? '';
        $bank_flow_expense_id = $params['bank_flow_expense_id'] ?? '';
        $trade_desc = $params['trade_desc'] ?? '';
        $bank_id = $params['bank_id'] ?? 0;
        $company_name = $params['company_name'] ?? '';
        $bank_account_id = $params['bank_account_id'] ?? 0;

        $updated_at_start_date = $params['updated_at_start_date'] ?? '';
        $updated_at_end_date = $params['updated_at_end_date'] ?? '';

        $conditions = [];
        $bind = [];
        // 不同流水类型的取值范围
        switch ($flow_type) {
            case parent::BANK_FLOW_TYPE_GET:
                $conditions[] = 'type = :flow_type:';
                $bind['flow_type'] = BankFlowEnums::BANK_FLOW_TYPE_VALUE_GET;
                //如果流水数据中的收款金额和付款金额均为0，则在查询时不显示该行流水数据
                $conditions[]   = "(get_amount > 0 OR pay_amount > 0)";
                break;
            case parent::BANK_FLOW_TYPE_PAY:
                $conditions[] = 'type = :flow_type:';
                $bind['flow_type'] = BankFlowEnums::BANK_FLOW_TYPE_VALUE_PAY;
                //如果流水数据中的收款金额和付款金额均为0，则在查询时不显示该行流水数据
                $conditions[]   = "(get_amount > 0 OR pay_amount > 0)";
                break;
            default:
                $flow_type_item = [BankFlowEnums::BANK_FLOW_TYPE_VALUE_GET, BankFlowEnums::BANK_FLOW_TYPE_VALUE_PAY];
                $flow_type_item = implode(',', $flow_type_item);
                $conditions[]   = "type in ({$flow_type_item})";
                //如果流水数据中的收款金额和付款金额均为0，则在查询时不显示该行流水数据
                $conditions[]   = "(get_amount > 0 OR pay_amount > 0)";
                break;
        }

        //关联账号id
        $is_have_acct_condition = false;
        if (!empty($bank_id) || !empty($company_name) || !empty($bank_account_id)) {
            $bank_conditions = [];
            $bank_bind = [];
            //银行
            if (!empty($bank_id)) {
                $bank_conditions[] = 'bank_id = :bank_id:';
                $bank_bind['bank_id'] = $bank_id;
            }
            //账号
            if (!empty($bank_account_id)) {
                $bank_conditions[] = 'id = :bank_account_id:';
                $bank_bind['bank_account_id'] = $bank_account_id;
            }
            //公司名称
            if (!empty($company_name)) {
                $bank_conditions[] = 'company_name = :company_name:';
                $bank_bind['company_name'] = $company_name;
            }

            $bank_conditions_str = implode(' and ', $bank_conditions);
            $bank_accounts = BankAccountModel::find([
                'conditions' => $bank_conditions_str,
                'bind' => $bank_bind,
                'columns' => ['id']
            ])->toArray();
            $bank_account_id_arr = array_column($bank_accounts, 'id');
            if (empty($bank_account_id_arr)) {
                //没查到, 返回false
                return false;
            }

            $is_have_acct_condition = true;

            $bank_account_ids = implode(',', $bank_account_id_arr);
            $conditions[]     = "bank_account_id in ({$bank_account_ids})";
        }

        // 索引使用优化: idx_type_account_date
        // 若无账号ID条件, 则默认取全量
        if ($is_have_acct_condition === false) {
            $bank_account_ids = BankAccountModel::find([
                'columns' => ['id'],
            ])->toArray();

            $bank_account_ids = !empty($bank_account_ids) ? array_column($bank_account_ids, 'id') : [0];
            $bank_account_ids = implode(',', $bank_account_ids);
            $conditions[]     = "bank_account_id in ({$bank_account_ids})";
        }

        //流水日期-开始时间
        if (!empty($trade_start_date)) {
            $conditions[] = 'date >= :trade_start_date:';
            $bind['trade_start_date'] = $trade_start_date;
        }

        //流水日期-结束时间
        if (!empty($trade_end_date)) {
            $conditions[] = 'date <= :trade_end_date:';
            $bind['trade_end_date'] = $trade_end_date;
        }

        //支票号码
        if (!empty($ticket_no)) {
            $conditions[] = 'ticket_no = :ticket_no:';
            $bind['ticket_no'] = $ticket_no;
        }

        //费用类型
        if ($bank_flow_expense_id != '') {
            $conditions[] = 'bank_flow_expense_id = :bank_flow_expense_id:';
            $bind['bank_flow_expense_id'] = $bank_flow_expense_id;
        }

        //描述
        if (!empty($trade_desc)) {
            $conditions[] = 'trade_desc LIKE :trade_desc:';
            $bind['trade_desc'] = '%' . $trade_desc . '%';
        }

        //更新日期-开始时间
        if (!empty($updated_at_start_date)) {
            $conditions[] = 'updated_at >= :updated_at_start_date:';
            $bind['updated_at_start_date'] = $updated_at_start_date . ' 00:00:00';
        }

        //更新日期-结束时间
        if (!empty($updated_at_end_date)) {
            $conditions[] = 'updated_at <= :updated_at_end_date:';
            $bind['updated_at_end_date'] = $updated_at_end_date . ' 23:59:59';
        }

        $conditions_str = implode(' and ', $conditions);
        return [$conditions_str, $bind];
    }

    /**
     * 获取流水查询的总数量
     *
     * @param $params
     * @param $flow_type
     * @return int
     * @date 2023/8/11
     */
    public function getFlowCount($params, $flow_type)
    {
        $conditions_array = $this->getFlowConditions($params, $flow_type);
        if ($conditions_array === false) {
            return 0;
        }
        $conditions = $conditions_array[0];
        $bind = $conditions_array[1];
        return BankFlowModel::count([
            'conditions' => $conditions,
            'bind' => $bind,
        ]);
    }
}
