<?php

namespace App\Repository\oa;

use App\Library\Enums\ContractEnums;
use App\Models\oa\ContractElectronicModel;
use App\Repository\BaseRepository;

/**
 * Class ContractElectronicRepository
 * @package App\Repository
 */
class ContractElectronicRepository extends BaseRepository
{
    private static $instance;

    private function __construct()
    {
    }

    private function __clone()
    {
    }

    public static function getInstance()
    {
        if (!isset(self::$instance) || !(self::$instance instanceof self)) {
            self::$instance = new self;
        }

        return self::$instance;
    }

    /**
     * 获取电子合同
     *
     * @param string $no
     * @return mixed
     */
    public function getContractElectronicByNo(string $no)
    {
        return ContractElectronicModel::findFirst([
            'conditions' => 'no = :no:',
            'bind' => ['no' => $no]
        ]);
    }

    /**
     * 通过合同编号获取最近完成签约的电子合同
     *
     * @param string $contract_no
     * @return mixed
     */
    public function getLatestRelatedByContractNo(string $contract_no)
    {
        if (empty($contract_no)) {
            return [];
        }

        $model = ContractElectronicModel::findFirst([
            'conditions' => 'contract_no = :contract_no: AND sign_status IN ({sign_status:array}) AND template_type != :template_type:',
            'bind' => [
                'contract_no' => $contract_no,
                'sign_status' => [ContractEnums::CONTRACT_SIGN_STATUS_3, ContractEnums::CONTRACT_SIGN_STATUS_4],
                'template_type' => ContractEnums::ELECTRONIC_CONTRACT_TYPE_11
            ],
            'order' => 'sign_completed_at DESC'
        ]);

        return $model ? $model->toArray() : [];
    }

    /**
     * 通过关联的合同ID获取电子合同
     *
     * @param int $relate_id
     * @return mixed
     */
    public function getOneByRelateId(int $relate_id)
    {
        if (empty($relate_id)) {
            return [];
        }

        $model = ContractElectronicModel::findFirst([
            'conditions' => 'relate_id = :relate_id:',
            'bind' => [
                'relate_id' => $relate_id,
            ],
        ]);

        return $model ? $model->toArray() : [];
    }

    /**
     * 获取电子合同 By id
     *
     * @param $id
     * @return mixed
     */
    public function getContractElectronicById($id)
    {
        if (empty($id)) {
            return false;
        }

        return ContractElectronicModel::findFirst([
            'conditions' => 'id = :id:',
            'bind'       => ['id' => $id],
        ]);
    }

}
