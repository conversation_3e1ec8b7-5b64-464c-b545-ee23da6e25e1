<?php

namespace App\Repository\oa;

use App\Modules\Vendor\Models\Vendor;
use App\Repository\BaseRepository;

/**
 * 供应商
 * Class VendorRepository
 * @package App\Repository\oa
 */
class VendorRepository extends BaseRepository
{
    private static $instance;

    private function __construct()
    {
    }

    private function __clone()
    {
    }

    /**
     * @return VendorRepository
     */
    public static function getInstance()
    {
        if (!isset(self::$instance) || !(self::$instance instanceof self)) {
            self::$instance = new self;
        }

        return self::$instance;
    }

    public function getVendorByVendorId($vendor_id){
        $vendor = Vendor::findFirst([
            'vendor_id = :vendor_id:',
            'bind' => ['vendor_id' => $vendor_id]
        ]);
        return $vendor ? $vendor->toArray() : [];
    }

    /**
     * 获取指定供应商id的供应商数据
     * @param array $vendor_ids 供应商id组
     * @return array
     */
    public function getVendorListByIds($vendor_ids = [])
    {
        $vendor_list = [];
        if (!empty($vendor_ids)) {
            $vendor_ids = array_values(array_filter(array_unique($vendor_ids)));
            $builder = $this->modelsManager->createBuilder();
            $builder->from(Vendor::class);
            $builder->inWhere('vendor_id', $vendor_ids);
            $builder->columns('*');
            $vendor_list = $builder->getQuery()->execute()->toArray();
            $vendor_list = array_column($vendor_list, null, 'vendor_id');
        }
        return $vendor_list;
    }
}
